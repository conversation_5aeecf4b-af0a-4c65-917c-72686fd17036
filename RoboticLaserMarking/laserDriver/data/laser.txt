类名：LaserControl
函数接口：
函数名	bool ConnectLaser(LaserCallbackDelegate callBack)
说明	初始化并连接激光控制卡，并注册回调函数LaserCallbackDelegate，当加工过程中出现异常时会激活回调函数
返回值	成功返回True，失败返回False

函数名	bool CloseLaser()
说明	关闭激光控制卡连接并释放资源
返回值	成功返回True，失败返回False

函数名	bool IsLaserConnected ()
说明	获取激光控制卡连接状态
返回值	已连接返回True，未连接返回False

函数名	bool IsLaserWorking ()
说明	获取激光控制卡工作状态
返回值	正在工作返回True，待机状态返回False

函数名	bool LaserHandON ()
说明	手动开激光
返回值	成功返回True，失败返回False

函数名	bool LaserHandOFF ()
说明	手动关激光
返回值	成功返回True，失败返回False

函数名	bool RedGuide(bool _enable)
说明	打开或关闭红光，当_enable=True打开红光，反之关闭红光
返回值	成功返回True，失败返回False
 
函数名	bool BeginMark (bool _pre_view)
说明	开始雕刻，当_pre_view=True为预览模式，反之为雕刻模式
返回值	成功返回True，失败返回False

函数名	bool EndMark ()
说明	终止当前正在进行雕刻
返回值	成功返回True，失败返回False

函数名	UInt64 GetLaserMarkTime()
说明	获取激光上一次雕刻所花费的时间
返回值	成功返回时间(单位为us)，失败返回0

函数名	bool ExternalStart_StopSignal(bool _enable, ref bool _start, ref bool _stop)
说明	设置和获取外部启停信号，当_enable=True时开启外部启停，反之关闭外部启停控制。_start返回外部当前启动信号状态, _stop返回外部当前停止信号状态
返回值	成功返回True，失败返回False

函数名	bool LoadProcessParam (string _path)
说明	加载激光加工参数，_path加工参数文件的绝对地址
返回值	成功返回True，失败返回False
 
函数名	bool LoadMarkData (string _path)
说明	加载激光加工轨迹，_path加工轨迹文件的绝对地址
返回值	成功返回True，失败返回False

函数名	bool SetWorkDir (string _dir)
说明	设置激光工作目录，_dir工作目录下需要包含两个文件：名称为ProcessParam.ini的激光加工工艺参数文件以及名称为LaserMarkData.lmd的激光轨迹加工文件
返回值	成功返回True，失败返回False

函数名	bool ClearData ()
说明	清除激光标刻数据
返回值	成功返回True，失败返回False

函数名	Int64 DataSize ()
说明	获取当前内存中激光标刻数据大小
返回值	返回数据大小
 
函数名	bool ResetLaserState ()
说明	重置激光状态
返回值	成功返回True，失败返回False

函数名	void GetLastErrorMsg(ref bool _b, StringBuilder _msg)
说明	获取最新的激光错误信息，当_b=True时代表运行中发生错误，_msg内部错误的具体内容
返回值	无
 
