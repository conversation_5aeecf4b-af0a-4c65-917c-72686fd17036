namespace BdScanfLineSensorWinFormsDemo
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.btnConnect = new System.Windows.Forms.Button();
            this.btnBeginMark = new System.Windows.Forms.Button();
            this.btnEndMark = new System.Windows.Forms.Button();
            this.btnDisConnect = new System.Windows.Forms.Button();
            this.btnLaserOn = new System.Windows.Forms.Button();
            this.btnLaserOff = new System.Windows.Forms.Button();
            this.cbRedGuide = new System.Windows.Forms.CheckBox();
            this.lbMarkTime = new System.Windows.Forms.Label();
            this.txtMarkTime = new System.Windows.Forms.TextBox();
            this.btnLoadProcessParam = new System.Windows.Forms.Button();
            this.btnLoadMarkData = new System.Windows.Forms.Button();
            this.txtProcessParamFilePath = new System.Windows.Forms.TextBox();
            this.txtMarkDataFilePath = new System.Windows.Forms.TextBox();
            this.btnSetWorkDir = new System.Windows.Forms.Button();
            this.txtWorkDir = new System.Windows.Forms.TextBox();
            this.btnResetLaserState = new System.Windows.Forms.Button();
            this.btnClearData = new System.Windows.Forms.Button();
            this.txtLogger = new System.Windows.Forms.TextBox();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.cbExternalSignal = new System.Windows.Forms.CheckBox();
            this.label2 = new System.Windows.Forms.Label();
            this.rbIsConnect = new System.Windows.Forms.RadioButton();
            this.rbIsWork = new System.Windows.Forms.RadioButton();
            this.btnWriteIO = new System.Windows.Forms.Button();
            this.cbReadIO = new System.Windows.Forms.CheckBox();
            this.txtIOValue = new System.Windows.Forms.TextBox();
            this.rbStartSignal = new System.Windows.Forms.RadioButton();
            this.rbStopSignal = new System.Windows.Forms.RadioButton();
            this.btnReLoadProcessParam = new System.Windows.Forms.Button();
            this.btnReLoadMarkData = new System.Windows.Forms.Button();
            this.btnReSetWorkDir = new System.Windows.Forms.Button();
            this.SuspendLayout();
            // 
            // btnConnect
            // 
            this.btnConnect.Location = new System.Drawing.Point(14, 12);
            this.btnConnect.Name = "btnConnect";
            this.btnConnect.Size = new System.Drawing.Size(105, 23);
            this.btnConnect.TabIndex = 0;
            this.btnConnect.Text = "Connect";
            this.btnConnect.UseVisualStyleBackColor = true;
            this.btnConnect.Click += new System.EventHandler(this.btnConnect_Click);
            // 
            // btnBeginMark
            // 
            this.btnBeginMark.Location = new System.Drawing.Point(125, 12);
            this.btnBeginMark.Name = "btnBeginMark";
            this.btnBeginMark.Size = new System.Drawing.Size(87, 23);
            this.btnBeginMark.TabIndex = 0;
            this.btnBeginMark.Text = "BeginMark";
            this.btnBeginMark.UseVisualStyleBackColor = true;
            this.btnBeginMark.Click += new System.EventHandler(this.btnBeginMark_Click);
            // 
            // btnEndMark
            // 
            this.btnEndMark.Location = new System.Drawing.Point(125, 49);
            this.btnEndMark.Name = "btnEndMark";
            this.btnEndMark.Size = new System.Drawing.Size(87, 23);
            this.btnEndMark.TabIndex = 0;
            this.btnEndMark.Text = "EndMark";
            this.btnEndMark.UseVisualStyleBackColor = true;
            this.btnEndMark.Click += new System.EventHandler(this.btnEndMark_Click);
            // 
            // btnDisConnect
            // 
            this.btnDisConnect.Location = new System.Drawing.Point(14, 49);
            this.btnDisConnect.Name = "btnDisConnect";
            this.btnDisConnect.Size = new System.Drawing.Size(105, 23);
            this.btnDisConnect.TabIndex = 0;
            this.btnDisConnect.Text = "DisConnect";
            this.btnDisConnect.UseVisualStyleBackColor = true;
            this.btnDisConnect.Click += new System.EventHandler(this.btnDisConnect_Click);
            // 
            // btnLaserOn
            // 
            this.btnLaserOn.Location = new System.Drawing.Point(330, 12);
            this.btnLaserOn.Name = "btnLaserOn";
            this.btnLaserOn.Size = new System.Drawing.Size(82, 23);
            this.btnLaserOn.TabIndex = 0;
            this.btnLaserOn.Text = "LaserOn";
            this.btnLaserOn.UseVisualStyleBackColor = true;
            this.btnLaserOn.Click += new System.EventHandler(this.btnLaserOn_Click);
            // 
            // btnLaserOff
            // 
            this.btnLaserOff.Location = new System.Drawing.Point(330, 49);
            this.btnLaserOff.Name = "btnLaserOff";
            this.btnLaserOff.Size = new System.Drawing.Size(82, 23);
            this.btnLaserOff.TabIndex = 0;
            this.btnLaserOff.Text = "LaserOff";
            this.btnLaserOff.UseVisualStyleBackColor = true;
            this.btnLaserOff.Click += new System.EventHandler(this.btnLaserOff_Click);
            // 
            // cbRedGuide
            // 
            this.cbRedGuide.AutoSize = true;
            this.cbRedGuide.Location = new System.Drawing.Point(226, 12);
            this.cbRedGuide.Name = "cbRedGuide";
            this.cbRedGuide.Size = new System.Drawing.Size(93, 19);
            this.cbRedGuide.TabIndex = 4;
            this.cbRedGuide.Text = "RedGuide";
            this.cbRedGuide.UseVisualStyleBackColor = true;
            this.cbRedGuide.CheckedChanged += new System.EventHandler(this.cbRedGuide_CheckedChanged);
            // 
            // lbMarkTime
            // 
            this.lbMarkTime.AutoSize = true;
            this.lbMarkTime.Location = new System.Drawing.Point(384, 236);
            this.lbMarkTime.Name = "lbMarkTime";
            this.lbMarkTime.Size = new System.Drawing.Size(79, 15);
            this.lbMarkTime.TabIndex = 5;
            this.lbMarkTime.Text = "MarkTime:";
            // 
            // txtMarkTime
            // 
            this.txtMarkTime.Location = new System.Drawing.Point(470, 231);
            this.txtMarkTime.Name = "txtMarkTime";
            this.txtMarkTime.ReadOnly = true;
            this.txtMarkTime.Size = new System.Drawing.Size(141, 25);
            this.txtMarkTime.TabIndex = 6;
            // 
            // btnLoadProcessParam
            // 
            this.btnLoadProcessParam.Location = new System.Drawing.Point(14, 86);
            this.btnLoadProcessParam.Name = "btnLoadProcessParam";
            this.btnLoadProcessParam.Size = new System.Drawing.Size(150, 23);
            this.btnLoadProcessParam.TabIndex = 0;
            this.btnLoadProcessParam.Text = "LoadProcessParam";
            this.btnLoadProcessParam.UseVisualStyleBackColor = true;
            this.btnLoadProcessParam.Click += new System.EventHandler(this.btnLoadProcessParam_Click);
            // 
            // btnLoadMarkData
            // 
            this.btnLoadMarkData.Location = new System.Drawing.Point(14, 123);
            this.btnLoadMarkData.Name = "btnLoadMarkData";
            this.btnLoadMarkData.Size = new System.Drawing.Size(150, 23);
            this.btnLoadMarkData.TabIndex = 0;
            this.btnLoadMarkData.Text = "LoadMarkData";
            this.btnLoadMarkData.UseVisualStyleBackColor = true;
            this.btnLoadMarkData.Click += new System.EventHandler(this.btnLoadMarkData_Click);
            // 
            // txtProcessParamFilePath
            // 
            this.txtProcessParamFilePath.Location = new System.Drawing.Point(170, 86);
            this.txtProcessParamFilePath.Name = "txtProcessParamFilePath";
            this.txtProcessParamFilePath.ReadOnly = true;
            this.txtProcessParamFilePath.Size = new System.Drawing.Size(364, 25);
            this.txtProcessParamFilePath.TabIndex = 6;
            // 
            // txtMarkDataFilePath
            // 
            this.txtMarkDataFilePath.Location = new System.Drawing.Point(170, 124);
            this.txtMarkDataFilePath.Name = "txtMarkDataFilePath";
            this.txtMarkDataFilePath.ReadOnly = true;
            this.txtMarkDataFilePath.Size = new System.Drawing.Size(364, 25);
            this.txtMarkDataFilePath.TabIndex = 6;
            // 
            // btnSetWorkDir
            // 
            this.btnSetWorkDir.Location = new System.Drawing.Point(14, 160);
            this.btnSetWorkDir.Name = "btnSetWorkDir";
            this.btnSetWorkDir.Size = new System.Drawing.Size(150, 23);
            this.btnSetWorkDir.TabIndex = 0;
            this.btnSetWorkDir.Text = "SetWorkDir";
            this.btnSetWorkDir.UseVisualStyleBackColor = true;
            this.btnSetWorkDir.Click += new System.EventHandler(this.btnSetWorkDir_Click);
            // 
            // txtWorkDir
            // 
            this.txtWorkDir.Location = new System.Drawing.Point(170, 160);
            this.txtWorkDir.Name = "txtWorkDir";
            this.txtWorkDir.ReadOnly = true;
            this.txtWorkDir.Size = new System.Drawing.Size(364, 25);
            this.txtWorkDir.TabIndex = 6;
            // 
            // btnResetLaserState
            // 
            this.btnResetLaserState.Location = new System.Drawing.Point(223, 49);
            this.btnResetLaserState.Name = "btnResetLaserState";
            this.btnResetLaserState.Size = new System.Drawing.Size(96, 23);
            this.btnResetLaserState.TabIndex = 0;
            this.btnResetLaserState.Text = "ResetLaserState";
            this.btnResetLaserState.UseVisualStyleBackColor = true;
            this.btnResetLaserState.Click += new System.EventHandler(this.btnResetLaserState_Click);
            // 
            // btnClearData
            // 
            this.btnClearData.Location = new System.Drawing.Point(14, 196);
            this.btnClearData.Name = "btnClearData";
            this.btnClearData.Size = new System.Drawing.Size(150, 23);
            this.btnClearData.TabIndex = 0;
            this.btnClearData.Text = "ClearData";
            this.btnClearData.UseVisualStyleBackColor = true;
            this.btnClearData.Click += new System.EventHandler(this.btnClearData_Click);
            // 
            // txtLogger
            // 
            this.txtLogger.Location = new System.Drawing.Point(617, 11);
            this.txtLogger.Multiline = true;
            this.txtLogger.Name = "txtLogger";
            this.txtLogger.ReadOnly = true;
            this.txtLogger.Size = new System.Drawing.Size(595, 252);
            this.txtLogger.TabIndex = 6;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // cbExternalSignal
            // 
            this.cbExternalSignal.AutoSize = true;
            this.cbExternalSignal.Location = new System.Drawing.Point(180, 198);
            this.cbExternalSignal.Name = "cbExternalSignal";
            this.cbExternalSignal.Size = new System.Drawing.Size(141, 19);
            this.cbExternalSignal.TabIndex = 7;
            this.cbExternalSignal.Text = "ExternalSignal";
            this.cbExternalSignal.UseVisualStyleBackColor = true;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(467, 205);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(0, 15);
            this.label2.TabIndex = 8;
            // 
            // rbIsConnect
            // 
            this.rbIsConnect.AutoSize = true;
            this.rbIsConnect.Location = new System.Drawing.Point(418, 14);
            this.rbIsConnect.Name = "rbIsConnect";
            this.rbIsConnect.Size = new System.Drawing.Size(116, 19);
            this.rbIsConnect.TabIndex = 10;
            this.rbIsConnect.TabStop = true;
            this.rbIsConnect.Text = "IsConnected";
            this.rbIsConnect.UseVisualStyleBackColor = true;
            // 
            // rbIsWork
            // 
            this.rbIsWork.AutoSize = true;
            this.rbIsWork.Location = new System.Drawing.Point(418, 50);
            this.rbIsWork.Name = "rbIsWork";
            this.rbIsWork.Size = new System.Drawing.Size(76, 19);
            this.rbIsWork.TabIndex = 10;
            this.rbIsWork.TabStop = true;
            this.rbIsWork.Text = "IsWork";
            this.rbIsWork.UseVisualStyleBackColor = true;
            // 
            // btnWriteIO
            // 
            this.btnWriteIO.Location = new System.Drawing.Point(14, 232);
            this.btnWriteIO.Name = "btnWriteIO";
            this.btnWriteIO.Size = new System.Drawing.Size(150, 23);
            this.btnWriteIO.TabIndex = 0;
            this.btnWriteIO.Text = "WriteIO";
            this.btnWriteIO.UseVisualStyleBackColor = true;
            this.btnWriteIO.Click += new System.EventHandler(this.btnWriteIO_Click);
            // 
            // cbReadIO
            // 
            this.cbReadIO.AutoSize = true;
            this.cbReadIO.Location = new System.Drawing.Point(301, 237);
            this.cbReadIO.Name = "cbReadIO";
            this.cbReadIO.Size = new System.Drawing.Size(77, 19);
            this.cbReadIO.TabIndex = 7;
            this.cbReadIO.Text = "ReadIO";
            this.cbReadIO.UseVisualStyleBackColor = true;
            // 
            // txtIOValue
            // 
            this.txtIOValue.Location = new System.Drawing.Point(170, 230);
            this.txtIOValue.Name = "txtIOValue";
            this.txtIOValue.Size = new System.Drawing.Size(111, 25);
            this.txtIOValue.TabIndex = 11;
            // 
            // rbStartSignal
            // 
            this.rbStartSignal.AutoSize = true;
            this.rbStartSignal.Location = new System.Drawing.Point(327, 198);
            this.rbStartSignal.Name = "rbStartSignal";
            this.rbStartSignal.Size = new System.Drawing.Size(116, 19);
            this.rbStartSignal.TabIndex = 10;
            this.rbStartSignal.TabStop = true;
            this.rbStartSignal.Text = "StartSignal";
            this.rbStartSignal.UseVisualStyleBackColor = true;
            // 
            // rbStopSignal
            // 
            this.rbStopSignal.AutoSize = true;
            this.rbStopSignal.Location = new System.Drawing.Point(449, 198);
            this.rbStopSignal.Name = "rbStopSignal";
            this.rbStopSignal.Size = new System.Drawing.Size(108, 19);
            this.rbStopSignal.TabIndex = 10;
            this.rbStopSignal.TabStop = true;
            this.rbStopSignal.Text = "StopSignal";
            this.rbStopSignal.UseVisualStyleBackColor = true;
            // 
            // btnReLoadProcessParam
            // 
            this.btnReLoadProcessParam.Location = new System.Drawing.Point(540, 85);
            this.btnReLoadProcessParam.Name = "btnReLoadProcessParam";
            this.btnReLoadProcessParam.Size = new System.Drawing.Size(71, 23);
            this.btnReLoadProcessParam.TabIndex = 0;
            this.btnReLoadProcessParam.Text = "Reload";
            this.btnReLoadProcessParam.UseVisualStyleBackColor = true;
            this.btnReLoadProcessParam.Click += new System.EventHandler(this.btnReLoadProcessParam_Click);
            // 
            // btnReLoadMarkData
            // 
            this.btnReLoadMarkData.Location = new System.Drawing.Point(540, 124);
            this.btnReLoadMarkData.Name = "btnReLoadMarkData";
            this.btnReLoadMarkData.Size = new System.Drawing.Size(71, 23);
            this.btnReLoadMarkData.TabIndex = 0;
            this.btnReLoadMarkData.Text = "Reload";
            this.btnReLoadMarkData.UseVisualStyleBackColor = true;
            this.btnReLoadMarkData.Click += new System.EventHandler(this.btnReLoadMarkData_Click);
            // 
            // btnReSetWorkDir
            // 
            this.btnReSetWorkDir.Location = new System.Drawing.Point(540, 162);
            this.btnReSetWorkDir.Name = "btnReSetWorkDir";
            this.btnReSetWorkDir.Size = new System.Drawing.Size(71, 23);
            this.btnReSetWorkDir.TabIndex = 0;
            this.btnReSetWorkDir.Text = "Reload";
            this.btnReSetWorkDir.UseVisualStyleBackColor = true;
            this.btnReSetWorkDir.Click += new System.EventHandler(this.btnReSetWorkDir_Click);
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1224, 275);
            this.Controls.Add(this.txtIOValue);
            this.Controls.Add(this.rbIsWork);
            this.Controls.Add(this.rbStopSignal);
            this.Controls.Add(this.rbStartSignal);
            this.Controls.Add(this.rbIsConnect);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.cbReadIO);
            this.Controls.Add(this.cbExternalSignal);
            this.Controls.Add(this.txtMarkTime);
            this.Controls.Add(this.txtWorkDir);
            this.Controls.Add(this.txtMarkDataFilePath);
            this.Controls.Add(this.txtProcessParamFilePath);
            this.Controls.Add(this.txtLogger);
            this.Controls.Add(this.lbMarkTime);
            this.Controls.Add(this.cbRedGuide);
            this.Controls.Add(this.btnSetWorkDir);
            this.Controls.Add(this.btnLoadMarkData);
            this.Controls.Add(this.btnReSetWorkDir);
            this.Controls.Add(this.btnReLoadMarkData);
            this.Controls.Add(this.btnReLoadProcessParam);
            this.Controls.Add(this.btnLoadProcessParam);
            this.Controls.Add(this.btnLaserOff);
            this.Controls.Add(this.btnWriteIO);
            this.Controls.Add(this.btnLaserOn);
            this.Controls.Add(this.btnResetLaserState);
            this.Controls.Add(this.btnClearData);
            this.Controls.Add(this.btnEndMark);
            this.Controls.Add(this.btnBeginMark);
            this.Controls.Add(this.btnDisConnect);
            this.Controls.Add(this.btnConnect);
            this.Name = "Form1";
            this.Text = "Demo";
            this.Load += new System.EventHandler(this.Form1_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.Button btnConnect;
        private System.Windows.Forms.Button btnBeginMark;
        private System.Windows.Forms.Button btnEndMark;
        private System.Windows.Forms.Button btnDisConnect;
        private System.Windows.Forms.Button btnLaserOn;
        private System.Windows.Forms.Button btnLaserOff;
        private System.Windows.Forms.CheckBox cbRedGuide;
        private System.Windows.Forms.Label lbMarkTime;
        private System.Windows.Forms.TextBox txtMarkTime;
        private System.Windows.Forms.Button btnLoadProcessParam;
        private System.Windows.Forms.Button btnLoadMarkData;
        private System.Windows.Forms.TextBox txtProcessParamFilePath;
        private System.Windows.Forms.TextBox txtMarkDataFilePath;
        private System.Windows.Forms.Button btnSetWorkDir;
        private System.Windows.Forms.TextBox txtWorkDir;
        private System.Windows.Forms.Button btnResetLaserState;
        private System.Windows.Forms.Button btnClearData;
        private System.Windows.Forms.TextBox txtLogger;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.CheckBox cbExternalSignal;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.RadioButton rbIsConnect;
        private System.Windows.Forms.RadioButton rbIsWork;
        private System.Windows.Forms.Button btnWriteIO;
        private System.Windows.Forms.CheckBox cbReadIO;
        private System.Windows.Forms.TextBox txtIOValue;
        private System.Windows.Forms.RadioButton rbStartSignal;
        private System.Windows.Forms.RadioButton rbStopSignal;
        private System.Windows.Forms.Button btnReLoadProcessParam;
        private System.Windows.Forms.Button btnReLoadMarkData;
        private System.Windows.Forms.Button btnReSetWorkDir;
    }
}

