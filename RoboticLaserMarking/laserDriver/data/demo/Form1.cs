using BDLaser;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace BdScanfLineSensorWinFormsDemo
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }
        private void AppendLog(string msg)
        {
            //byte[] asciiBytes = Encoding.UTF8.GetBytes(msg);
            //System.Text.Encoding.Convert(System.Text.Encoding.ASCII, System.Text.Encoding.Unicode, Encoding.Default.GetBytes(msg));
            string log = string.Format("[{0:yy/MM/dd hh:mm:ss}]:{1}\r\n", DateTime.Now, msg);
            this.txtLogger.AppendText(log);
        }
        private LaserControlHelper m_laserControlHelper;

        private void Form1_Load(object sender, EventArgs e)
        {
            m_laserControlHelper = new LaserControlHelper();
            string path = System.Environment.CurrentDirectory;//获取系统路径

            this.txtLogger.ScrollBars = ScrollBars.Vertical;
            timer1.Interval = 500;
            timer1.Start();
            FreshForm();
        }

        private void FreshForm()
        {
            this.btnConnect.Enabled = true;
            this.btnDisConnect.Enabled = false;
            this.btnBeginMark.Enabled = false;
            this.btnEndMark.Enabled = false;
            this.btnLaserOn.Enabled = false;
            this.btnLaserOff.Enabled = false;
            this.cbRedGuide.Enabled = false;
            this.btnResetLaserState.Enabled = false;
            this.btnLoadMarkData.Enabled = false;
            this.btnLoadProcessParam.Enabled = false;
            this.btnSetWorkDir.Enabled = false;
            this.btnClearData.Enabled = false;
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            bool ret = ControlCard.OpenCard(0);
            if (!ret)
            {
                this.AppendLog("OpenCard Failed");
            }

            ret = m_laserControlHelper.Connect();
            if (ret)
            {
                this.btnConnect.Enabled = false;
                this.btnDisConnect.Enabled = true;
                this.btnBeginMark.Enabled = true;
                this.btnEndMark.Enabled = true;
                this.btnLaserOn.Enabled = true;
                this.btnLaserOff.Enabled = true;
                this.cbRedGuide.Enabled = true;
                this.btnResetLaserState.Enabled = true;
                this.btnLoadMarkData.Enabled = true;
                this.btnLoadProcessParam.Enabled = true;
                this.btnSetWorkDir.Enabled = true;
                this.btnClearData.Enabled = true;
            }
            else
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("ConnectSensor Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnDisConnect_Click(object sender, EventArgs e)
        {
            if (ControlCard.IsCardOpened())
            {
                bool bret = ControlCard.CloseCard();
                if (!bret)
                {
                    this.AppendLog("CloseCard Failed");
                }
            }
            bool ret = m_laserControlHelper.DisConnect();
            if (ret)
            {
                this.btnConnect.Enabled = true;
                this.btnDisConnect.Enabled = false;
                this.btnBeginMark.Enabled = false;
                this.btnEndMark.Enabled = false;
                this.btnLaserOn.Enabled = false;
                this.btnLaserOff.Enabled = false;
                this.cbRedGuide.Enabled = false;
                this.btnResetLaserState.Enabled = false;
                this.btnLoadMarkData.Enabled = false;
                this.btnLoadProcessParam.Enabled = false;
                this.btnSetWorkDir.Enabled = false;
                this.btnClearData.Enabled = false;
            }
            else
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("DisConnectSensor Result:[{0}]{1};", error, errorMsg));
            }

        }

        private void btnBeginMark_Click(object sender, EventArgs e)
        {
            bool preview = this.cbRedGuide.Checked;
            bool ret = m_laserControlHelper.BeginMark(preview);
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("BeginMark Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnEndMark_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.EndMark();
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("EndMark Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void cbRedGuide_CheckedChanged(object sender, EventArgs e)
        {
            bool enable = this.cbRedGuide.Checked;
            bool ret = m_laserControlHelper.RedGuide(enable);
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("RedGuide Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnResetLaserState_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.ResetLaserState();
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("ResetLaserState Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnLaserOn_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.LaserOn();
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("LaserON Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnLaserOff_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.LaserOff();
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("LaserOFF Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnLoadProcessParam_Click(object sender, EventArgs e)
        {
            string filepath = string.Empty; //文件名

            OpenFileDialog dlg = new OpenFileDialog();
            dlg.DefaultExt = "ini";
            dlg.Filter = "Ini Files|*.ini";
            if (dlg.ShowDialog() == DialogResult.OK)
                filepath = dlg.FileName;
            if (filepath == null) { AppendLog("File Is Wrong"); return; }
            if (File.Exists(filepath))
            {
                this.txtProcessParamFilePath.Text = filepath;
                bool ret = m_laserControlHelper.LoadProcessParam(filepath);
                if (!ret)
                {
                    bool error = false;
                    string errorMsg = string.Empty;
                    m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                    this.AppendLog(string.Format("LoadProcessParam Result:[{0}]{1};", error, errorMsg));
                }
            }
        }

        private void btnReLoadProcessParam_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.LoadProcessParam(this.txtProcessParamFilePath.Text);
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("LoadProcessParam Result:[{0}]{1};", error, errorMsg));
            }
        }

        private void btnLoadMarkData_Click(object sender, EventArgs e)
        {
            string filepath = string.Empty; //文件名

            OpenFileDialog dlg = new OpenFileDialog();
            dlg.DefaultExt = "lmd";
            dlg.Filter = "Lmd Files|*.lmd";
            if (dlg.ShowDialog() == DialogResult.OK)
                filepath = dlg.FileName;
            if (filepath == null) { AppendLog("File Is Wrong"); return; }
            if (File.Exists(filepath))
            {
                this.txtMarkDataFilePath.Text = filepath;
                bool ret = m_laserControlHelper.LoadMarkData(filepath);
                if (!ret)
                {
                    bool error = false;
                    string errorMsg = string.Empty;
                    m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                    this.AppendLog(string.Format("LoadMarkData Result:[{0}]{1};", error, errorMsg));
                }
                else
                {
                    this.AppendLog(string.Format("LoadMarkData Success:{0}", m_laserControlHelper.DataSize()));
                }
            }
        }

        private void btnReLoadMarkData_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.LoadMarkData(this.txtMarkDataFilePath.Text);
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("LoadMarkData Result:[{0}]{1};", error, errorMsg));
            }
            else
            {
                this.AppendLog(string.Format("LoadMarkData Success:{0}", m_laserControlHelper.DataSize()));
            }
        }

        private void btnSetWorkDir_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog())
            {
                folderBrowserDialog.Description = "选择文件夹";
                DialogResult dialogResult = folderBrowserDialog.ShowDialog();

                if (dialogResult == DialogResult.OK)
                {
                    string selectedFolderPath = folderBrowserDialog.SelectedPath;
                    this.txtWorkDir.Text = selectedFolderPath;
                    bool ret = m_laserControlHelper.SetWorkDir(selectedFolderPath);
                    if (!ret)
                    {
                        bool error = false;
                        string errorMsg = string.Empty;
                        m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                        this.AppendLog(string.Format("SetWorkDir Result:[{0}]{1};", error, errorMsg));
                    }
                    else
                    {
                        this.AppendLog(string.Format("SetWorkDir Success:{0}", m_laserControlHelper.DataSize()));
                    }
                }
            }
        }

        private void btnReSetWorkDir_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.SetWorkDir(this.txtWorkDir.Text);
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("ReSetWorkDir Result:[{0}]{1};", error, errorMsg));
            }
            else
            {
                this.AppendLog(string.Format("ReSetWorkDir Success:{0}", m_laserControlHelper.DataSize()));
            }
        }

        private void btnClearData_Click(object sender, EventArgs e)
        {
            bool ret = m_laserControlHelper.ClearData();
            if (!ret)
            {
                bool error = false;
                string errorMsg = string.Empty;
                m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                this.AppendLog(string.Format("ClearData Result:[{0}]{1};", error, errorMsg));
            }
            else
            {
                this.AppendLog(string.Format("ClearData Success:{0}", m_laserControlHelper.DataSize()));
            }
        }

        private void btnWriteIO_Click(object sender, EventArgs e)
        {
            if (ControlCard.IsCardOpened())
            {
                UInt16 ouputValue = UInt16.Parse(this.txtIOValue.Text);
                if (ouputValue < 0 || ouputValue > 15)
                {
                    this.AppendLog("WriteOutputIO Failed:Value Must Be Greate Than 0 Or Less Than 16.");
                    return;
                }
                bool ret = ControlCard.WriteOutputIO(ouputValue);
                if (!ret)
                {
                    this.AppendLog("WriteOutputIO Failed");
                }
            }
            else
            {
                this.AppendLog("Card Is Not Opened");
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            bool isconnected = m_laserControlHelper.IsConnected();
            this.rbIsConnect.Checked = isconnected;
            if (isconnected)
            {
                this.rbIsWork.Checked = m_laserControlHelper.IsWorking();
                double l_time = m_laserControlHelper.GetLaserMarkTime() / 1000.0;
                this.txtMarkTime.Text = string.Format("{0:F3} ms", l_time);

                bool enable_external = this.cbExternalSignal.Checked;
                bool _start = false;
                bool _stop = false;
                bool ret = m_laserControlHelper.ExternalStart_StopSignal(enable_external, ref _start, ref _stop);
                if (!ret)
                {
                    bool error = false;
                    string errorMsg = string.Empty;
                    m_laserControlHelper.GetErrorMsg(ref error, ref errorMsg);
                    this.AppendLog(string.Format("ExternalStart_StopSignal Result:[{0}]{1};", error, errorMsg));
                }
                else
                {
                    this.rbStartSignal.Checked = _start;
                    this.rbStopSignal.Checked = _stop;
                }
            }
            if (this.cbReadIO.Checked)
            {
                if (ControlCard.IsCardOpened())
                {
                    UInt16 inputValue = 0;
                    ControlCard.ReadInputIO(ref inputValue);
                    this.AppendLog(string.Format("ReadInputIO:{0}", inputValue));

                    UInt16 outputValue = 0;
                    ControlCard.ReadOutputIO(ref outputValue);
                    this.AppendLog(string.Format("ReadOutputIO:{0}", outputValue));

                    for (UInt16 i = 0; i < 4; i++)
                    {
                        bool value = false;
                        ControlCard.ReadOutputIOBit(i, ref value);
                        this.AppendLog(string.Format("ReadOutputIOBit:{0}={1}", i, value));
                    }

                    for (UInt16 i = 0; i < 4; i++)
                    {
                        bool value = false;
                        ControlCard.ReadInputIOBit(i, ref value);
                        this.AppendLog(string.Format("ReadInputIOBit:{0}={1}", i, value));
                    }

                }
            }
        }
    }
}
