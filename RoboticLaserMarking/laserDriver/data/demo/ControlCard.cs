using System;
using System.Runtime.InteropServices;
using System.Text;

namespace BDLaser
{
    public sealed class ControlCard
    {
        /// <summary>
        /// 连接IO卡
        /// </summary>
        /// <param name="_card_no">卡号：0</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool OpenCard(UInt16 _card_no);
        /// <summary>
        /// 关闭IO卡
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool CloseCard();
        /// <summary>
        /// 是否打开IO卡
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsCardOpened();
        /// <summary>
        /// 输出IO
        /// </summary>
        /// <param name="_value">所有的输出IO状态值，16进制，每个位代表对应输出IO的状态</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool WriteOutputIO(UInt16 _value);
        /// <summary>
        /// 读取IO输出状态
        /// </summary>
        /// <param name="_value">所有的输出IO状态值，16进制，每个位代表对应输出IO的状态</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ReadOutputIO(ref UInt16 _value);
        /// <summary>
        /// 按位输出IO
        /// </summary>
        /// <param name="_io_index">IO序号，10进制</param>
        /// <param name="_value">对应输出的IO值</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool WriteOutputIOBit(UInt16 _io_index, bool _value);
        /// <summary>
        /// 按位读取IO输出状态
        /// </summary>
        /// <param name="_io_index">IO序号，10进制</param>
        /// <param name="_value">读取对应输出的IO值</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ReadOutputIOBit(UInt16 _io_index, ref bool _value);
        /// <summary>
        /// 读取IO输入状态
        /// </summary>
        /// <param name="_value">所有的输入IO状态值，16进制，每个位代表对应输入IO的状态</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ReadInputIO(ref UInt16 _value);
        /// <summary>
        /// 按位读取IO输入状态
        /// </summary>
        /// <param name="_io_index">IO序号，10进制</param>
        /// <param name="_value">读取对应输入的IO值</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ReadInputIOBit(UInt16 _io_index, ref bool _value);
        /// <summary>
        /// 按位等待IO输入状态
        /// </summary>
        /// <param name="_io_index">IO序号，10进制</param>
        /// <param name="_cond">目标状态值</param>
        /// <param name="_timeouts">超时设置，值小于等于0时表示无限循环等待直到满足目标状态</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool WaitForIOBit(UInt16 _io_index, bool _cond, double _timeouts);
    }
}
