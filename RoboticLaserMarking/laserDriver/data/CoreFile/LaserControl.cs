using System;
using System.Runtime.InteropServices;
using System.Text;

namespace BDLaser
{
    public delegate void LaserCallbackDelegate([MarshalAs(UnmanagedType.LPStr)] string msg);

    public sealed class LaserControl
    {
        /// <summary>
        /// 连接
        /// </summary>
        /// <param name="callBack"></param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ConnectLaser(LaserCallbackDelegate callBack);
        /// <summary>
        /// 断开连接
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool CloseLaser();
        /// <summary>
        /// 是否连接
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsLaserConnected();
        /// <summary>
        /// 工作状态
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool IsLaserWorking();
        /// <summary>
        /// 单独开激光(慎用)
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool LaserHandON();
        /// <summary>
        /// 单独关激光(慎用)
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool LaserHandOFF();
        /// <summary>
        /// 红光开关
        /// </summary>
        /// <param name="_enable">true:开启,false:关闭</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
         public static extern bool RedGuide(bool _enable);
        /// <summary>
        /// 开始标刻
        /// </summary>
        /// <param name="_pre_view">true:预览,false:标刻</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
         public static extern bool BeginMark(bool _pre_view);
        /// <summary>
        /// 停止标刻
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool EndMark();
        /// <summary>
        /// 获取激光标刻时间
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern UInt64 GetLaserMarkTime();
        /// <summary>
        /// 设置外部IO启动停止信号
        /// </summary>
        /// <param name="_enable">是否启动</param>
        /// <param name="_start">外部启动信号</param>
        /// <param name="_stop">外部停止信号</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ExternalStart_StopSignal(bool _enable, ref bool _start, ref bool _stop);
        /// <summary>
        /// 加载加工参数,后缀名为ini
        /// </summary>
        /// <param name="_path">工艺参数路径</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool LoadProcessParam([MarshalAs(UnmanagedType.LPStr)] string _path);
        /// <summary>
        /// 加载标刻数据,后缀名为lmd
        /// </summary>
        /// <param name="_path">LMD格式文件路径</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool LoadMarkData([MarshalAs(UnmanagedType.LPStr)] string _path);
        /// <summary>
        /// 设置工作目录,目录中要求“加工参数名为:ProcessParam.ini,轨迹名称为:LaserMarkData.lmd”,否则返回加载失败
        /// </summary>
        /// <param name="_dir">工作目录</param>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SetWorkDir([MarshalAs(UnmanagedType.LPStr)] string _dir);
        /// <summary>
        /// 清空数据
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ClearData();
        /// <summary>
        /// 内存数据大小
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern Int64 DataSize();
        /// <summary>
        /// 重置激光状态
        /// </summary>
        /// <returns></returns>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ResetLaserState();
        /// <summary>
        /// 获取错误信息
        /// </summary>
        /// <param name="_b">是否有错误</param>
        /// <param name="_msg">错误具体消息</param>
        [DllImport("LaserControlFrameSDK.dll", CharSet = CharSet.Auto, CallingConvention = CallingConvention.Cdecl)]
        public static extern void GetLastErrorMsg(ref bool _b, StringBuilder _msg);
    }
}
