#include "LaserControlAPI.h"
#include <iostream>

namespace LaserDriver {

// 全局回调函数指针
static LaserCallbackFunc g_callbackFunc = nullptr;

/**
 * @brief 内部回调处理函数
 * @param msg 回调消息
 */
void LaserControl::InternalCallback(const char* msg) {
    if (g_callbackFunc) {
        g_callbackFunc(msg);
    }
}

/**
 * @brief LaserControl构造函数
 */
LaserControl::LaserControl() : m_connected(false), m_callback(nullptr) {
    // 初始化全局回调函数指针
    g_callbackFunc = nullptr;
}

/**
 * @brief LaserControl析构函数
 */
LaserControl::~LaserControl() {
    if (m_connected) {
        Disconnect();
    }
}

/**
 * @brief 设置回调函数
 * @param callback 回调函数
 */
void LaserControl::SetCallback(LaserCallbackFunc callback) {
    m_callback = callback;
    g_callbackFunc = callback;
}

/**
 * @brief 连接激光控制器
 * @return 连接成功返回true，失败返回false
 */
bool LaserControl::Connect() {
    // 确保在Connect之前有回调函数设置
    if (!m_callback) {
        // 如果用户没有设置回调函数，设置一个空的默认回调防止崩溃
        SetCallback([](const char* msg) {
            std::cerr << "警告: 未设置用户回调函数，收到消息: " << msg << std::endl;
        });
    }

    // 确保全局回调函数指针已设置
    g_callbackFunc = m_callback;
    
    // 创建MarkEventCallBack对象
    MarkEventCallBack eventCallback = [](const char* msg) {
        InternalCallback(msg);
    };
    
    bool result = ConnectLaser(eventCallback);
    m_connected = result;
    return result;
}

/**
 * @brief 断开激光控制器连接
 * @return 断开成功返回true，失败返回false
 */
bool LaserControl::Disconnect() {
    if (!m_connected) {
        return false;
    }

    bool result = CloseLaser();
    if (result) {
        m_connected = false;
    }
    return result;
}

/**
 * @brief 检查激光控制器是否已连接
 * @return 已连接返回true，未连接返回false
 */
bool LaserControl::IsConnected() const {
    return IsLaserConnected();
}

/**
 * @brief 开始打标
 * @param previewMode 预览模式
 * @return 成功返回true，失败返回false
 */
bool LaserControl::BeginMark(bool previewMode) {
    if (!m_connected) {
        return false;
    }
    return ::BeginMark(previewMode);
}

/**
 * @brief 结束打标
 * @return 成功返回true，失败返回false
 */
bool LaserControl::EndMark() {
    if (!m_connected) {
        return false;
    }
    return ::EndMark();
}

/**
 * @brief 打开激光
 * @return 成功返回true，失败返回false
 */
bool LaserControl::LaserOn() {
    if (!m_connected) {
        return false;
    }
    return LaserHandON();
}

/**
 * @brief 关闭激光
 * @return 成功返回true，失败返回false
 */
bool LaserControl::LaserOff() {
    if (!m_connected) {
        return false;
    }
    return LaserHandOFF();
}

/**
 * @brief 红光指示
 * @param enable 启用红光指示
 * @return 成功返回true，失败返回false
 */
bool LaserControl::RedGuide(bool enable) {
    if (!m_connected) {
        return false;
    }
    return ::RedGuide(enable);
}

/**
 * @brief 重置激光状态
 * @return 成功返回true，失败返回false
 */
bool LaserControl::ResetLaserState() {
    if (!m_connected) {
        return false;
    }
    return ::ResetLaserState();
}

/**
 * @brief 检查激光是否正在工作
 * @return 正在工作返回true，否则返回false
 */
bool LaserControl::IsWorking() const {
    if (!m_connected) {
        return false;
    }
    return ::IsLaserWorking();
}

/**
 * @brief 加载工艺参数
 * @param filePath 参数文件路径
 * @return 成功返回true，失败返回false
 */
bool LaserControl::LoadProcessParam(const std::string& filePath) {
    if (!m_connected) {
        return false;
    }
    return ::LoadProcessParam(filePath.c_str());
}

/**
 * @brief 加载打标数据
 * @param filePath 数据文件路径
 * @return 成功返回true，失败返回false
 */
bool LaserControl::LoadMarkData(const std::string& filePath) {
    if (!m_connected) {
        return false;
    }
    return ::LoadMarkData(filePath.c_str());
}

/**
 * @brief 设置工作目录
 * @param dirPath 目录路径
 * @return 成功返回true，失败返回false
 */
bool LaserControl::SetWorkDir(const std::string& dirPath) {
    if (!m_connected) {
        return false;
    }
    return ::SetWorkDir(dirPath.c_str());
}

/**
 * @brief 清除数据
 * @return 成功返回true，失败返回false
 */
bool LaserControl::ClearData() {
    if (!m_connected) {
        return false;
    }
    return ::ClearData();
}

/**
 * @brief 获取数据大小
 * @return 数据大小
 */
int64_t LaserControl::DataSize() const {
    if (!m_connected) {
        return 0;
    }
    return ::DataSize();
}

/**
 * @brief 获取激光打标时间
 * @return 打标时间
 */
uint64_t LaserControl::GetLaserMarkTime() const {
    if (!m_connected) {
        return 0;
    }
    return ::GetLaserMarkTime();
}

/**
 * @brief 外部启停信号
 * @param enable 启用外部信号
 * @param start 启动信号状态
 * @param stop 停止信号状态
 * @return 成功返回true，失败返回false
 */
bool LaserControl::ExternalStartStopSignal(bool enable, bool& start, bool& stop) {
    if (!m_connected) {
        return false;
    }
    return ExternalStart_StopSignal(enable, start, stop);
}

/**
 * @brief 获取错误信息
 * @param hasError 是否有错误
 * @param errorMsg 错误信息
 */
void LaserControl::GetErrorMessage(bool& hasError, std::string& errorMsg) const {
    if (!m_connected) {
        hasError = true;
        errorMsg = "未连接到激光控制卡";
        return;
    }
    
    char msgBuffer[4096] = {0};
    ::GetLastErrorMsg(hasError, msgBuffer);
    errorMsg = msgBuffer;
}

/**
 * @brief ControlCard构造函数
 */
ControlCard::ControlCard() : m_cardOpened(false) {
}

/**
 * @brief ControlCard析构函数
 */
ControlCard::~ControlCard() {
    if (m_cardOpened) {
        CloseCard();
    }
}

/**
 * @brief 打开控制卡
 * @param cardNo 控制卡编号
 * @return 成功返回true，失败返回false
 */
bool ControlCard::OpenCard(uint16_t cardNo) {
    bool result = ::OpenCard(cardNo);
    m_cardOpened = result;
    return result;
}

/**
 * @brief 关闭控制卡
 * @return 成功返回true，失败返回false
 */
bool ControlCard::CloseCard() {
    if (!m_cardOpened) {
        return false;
    }
    bool result = ::CloseCard();
    if (result) {
        m_cardOpened = false;
    }
    return result;
}

/**
 * @brief 检查控制卡是否已打开
 * @return 已打开返回true，未打开返回false
 */
bool ControlCard::IsCardOpened() const {
    return ::IsCardOpened();
}

/**
 * @brief 写输出IO
 * @param value IO值
 * @return 成功返回true，失败返回false
 */
bool ControlCard::WriteOutputIO(uint16_t value) {
    if (!m_cardOpened) {
        return false;
    }
    return ::WriteOutputIO(value);
}

/**
 * @brief 读输出IO
 * @param value IO值引用
 * @return 成功返回true，失败返回false
 */
bool ControlCard::ReadOutputIO(uint16_t& value) {
    if (!m_cardOpened) {
        return false;
    }
    return ::ReadOutputIO(value);
}

/**
 * @brief 写输出IO位
 * @param ioIndex IO索引
 * @param value IO位值
 * @return 成功返回true，失败返回false
 */
bool ControlCard::WriteOutputIOBit(uint16_t ioIndex, bool value) {
    if (!m_cardOpened) {
        return false;
    }
    return ::WriteOutputIOBit(ioIndex, value);
}

/**
 * @brief 读输出IO位
 * @param ioIndex IO索引
 * @param value IO位值引用
 * @return 成功返回true，失败返回false
 */
bool ControlCard::ReadOutputIOBit(uint16_t ioIndex, bool& value) {
    if (!m_cardOpened) {
        return false;
    }
    return ::ReadOutputIOBit(ioIndex, value);
}

/**
 * @brief 读输入IO
 * @param value IO值引用
 * @return 成功返回true，失败返回false
 */
bool ControlCard::ReadInputIO(uint16_t& value) {
    if (!m_cardOpened) {
        return false;
    }
    return ::ReadInputIO(value);
}

/**
 * @brief 读输入IO位
 * @param ioIndex IO索引
 * @param value IO位值引用
 * @return 成功返回true，失败返回false
 */
bool ControlCard::ReadInputIOBit(uint16_t ioIndex, bool& value) {
    if (!m_cardOpened) {
        return false;
    }
    return ::ReadInputIOBit(ioIndex, value);
}

/**
 * @brief 等待IO位状态
 * @param ioIndex IO索引
 * @param cond 等待条件
 * @param timeouts 超时时间
 * @return 成功返回true，失败返回false
 */
bool ControlCard::WaitForIOBit(uint16_t ioIndex, bool cond, double timeouts) {
    if (!m_cardOpened) {
        return false;
    }
    return ::WaitForIOBit(ioIndex, cond, timeouts);
}

} // namespace LaserDriver