#pragma once

#include <Windows.h>
#include <string>
#include <functional>
#include "LaserControlFrameSDK.h"

namespace LaserDriver {

// 回调函数类型定义
typedef std::function<void(const char* msg)> LaserCallbackFunc;

class LaserControl {
public:
    // 构造和析构
    LaserControl();
    ~LaserControl();

    // 连接与断开
    bool Connect();
    bool Disconnect();
    bool IsConnected() const;
    
    // 激光控制
    bool BeginMark(bool previewMode);
    bool EndMark();
    bool LaserOn();
    bool LaserOff();
    bool RedGuide(bool enable);
    bool ResetLaserState();
    bool IsWorking() const;
    
    // 文件操作
    bool LoadProcessParam(const std::string& filePath);
    bool LoadMarkData(const std::string& filePath);
    bool SetWorkDir(const std::string& dirPath);
    bool ClearData();
    int64_t DataSize() const;
    
    // 状态与信息
    uint64_t GetLaserMarkTime() const;
    bool ExternalStartStopSignal(bool enable, bool& start, bool& stop);
    void GetErrorMessage(bool& hasError, std::string& errorMsg) const;
    
    // 设置回调
    void SetCallback(LaserCallbackFunc callback);

private:
    // 回调处理
    static void InternalCallback(const char* msg);
    
    // 成员变量
    LaserCallbackFunc m_callback;
    bool m_connected;
};

// IO控制卡类
class ControlCard {
public:
    ControlCard();
    ~ControlCard();

    // 连接控制
    bool OpenCard(uint16_t cardNo);
    bool CloseCard();
    bool IsCardOpened() const;
    
    // IO操作
    bool WriteOutputIO(uint16_t value);
    bool ReadOutputIO(uint16_t& value);
    bool WriteOutputIOBit(uint16_t ioIndex, bool value);
    bool ReadOutputIOBit(uint16_t ioIndex, bool& value);
    bool ReadInputIO(uint16_t& value);
    bool ReadInputIOBit(uint16_t ioIndex, bool& value);
    bool WaitForIOBit(uint16_t ioIndex, bool cond, double timeouts);

private:
    bool m_cardOpened;
};

}  // namespace LaserDriver