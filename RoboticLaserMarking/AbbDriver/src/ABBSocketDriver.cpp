#include "ABBSocketDriver.h"
#include <algorithm>
#include <vector>
#include <string>
#include <iostream>
#include <cstdio>
#include <cstdlib>

namespace RobotDriver {

// 构造函数
ABBSocketDriver::ABBSocketDriver() 
    : m_serverSocket(INVALID_SOCKET), 
      m_clientSocket(INVALID_SOCKET),
      m_isRunning(false),
      m_isConnected(false),
      m_port(8080) {
}

// 析构函数
ABBSocketDriver::~ABBSocketDriver() {
    Stop();
}

// 清除指定端口占用的辅助函数
bool ABBSocketDriver::ClearPortUsage(int port) {
    std::string command = "netstat -ano | findstr :" + std::to_string(port);
    
    // 执行netstat命令查找占用端口的进程
    FILE* pipe = _popen(command.c_str(), "r");
    if (!pipe) {
        std::cerr << "无法执行netstat命令" << std::endl;
        return false;
    }
    
    char buffer[256];
    std::vector<std::string> pids;
    
    // 读取命令输出，提取PID
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        std::string line(buffer);
        // 查找监听状态的连接
        if (line.find("LISTENING") != std::string::npos) {
            // 提取PID（最后一列）
            size_t lastSpace = line.find_last_of(" \t");
            if (lastSpace != std::string::npos) {
                std::string pid = line.substr(lastSpace + 1);
                // 移除换行符
                pid.erase(std::remove(pid.begin(), pid.end(), '\n'), pid.end());
                pid.erase(std::remove(pid.begin(), pid.end(), '\r'), pid.end());
                if (!pid.empty() && pid != "0") {
                    pids.push_back(pid);
                }
            }
        }
    }
    _pclose(pipe);
    
    // 终止占用端口的进程
    for (const auto& pid : pids) {
        std::string killCommand = "taskkill /F /PID " + pid;
        std::cout << "正在终止占用端口" << port << "的进程 PID: " << pid << std::endl;
        
        int result = system(killCommand.c_str());
        if (result == 0) {
            std::cout << "成功终止进程 PID: " << pid << std::endl;
        } else {
            std::cout << "终止进程 PID: " << pid << " 失败，可能需要管理员权限" << std::endl;
        }
    }
    
    // 等待一段时间让系统释放端口
    if (!pids.empty()) {
        std::cout << "等待端口释放..." << std::endl;
        Sleep(2000); // 等待2秒
    }
    
    return true;
}

// 初始化Socket服务器
bool ABBSocketDriver::Initialize(int port) {
    if (m_isRunning) {
        return false;
    }

    m_port = port;
    
    // 在Windows平台上清除端口占用
    std::cout << "正在检查并清除端口 " << port << " 的占用..." << std::endl;
    ClearPortUsage(port);

    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        std::cerr << "WSAStartup失败, 错误码: " << WSAGetLastError() << std::endl;
        return false;
    }

    // 创建服务器socket
    m_serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_serverSocket == INVALID_SOCKET) {
        std::cerr << "Socket创建失败, 错误码: " << WSAGetLastError() << std::endl;
        WSACleanup();
        return false;
    }

    // 绑定地址和端口
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(static_cast<u_short>(m_port));

    if (bind(m_serverSocket, (SOCKADDR*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        std::cerr << "绑定失败, 错误码: " << WSAGetLastError() << std::endl;
        closesocket(m_serverSocket);
        WSACleanup();
        return false;
    }

    // 设置socket为监听状态
    if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        std::cerr << "监听失败, 错误码: " << WSAGetLastError() << std::endl;
        closesocket(m_serverSocket);
        WSACleanup();
        return false;
    }

    return true;
}

// 启动服务器
bool ABBSocketDriver::Start() {
    if (m_isRunning) {
        return false;
    }

    m_isRunning = true;
    m_serverThread = std::thread(&ABBSocketDriver::ServerListenThread, this);
    return true;
}

// 停止服务器
void ABBSocketDriver::Stop() {
    if (!m_isRunning) {
        return;
    }

    m_isRunning = false;

    // 关闭客户端socket
    if (m_clientSocket != INVALID_SOCKET) {
        closesocket(m_clientSocket);
        m_clientSocket = INVALID_SOCKET;
        m_isConnected = false;
    }

    // 关闭服务器socket
    if (m_serverSocket != INVALID_SOCKET) {
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }

    // 等待线程结束
    if (m_serverThread.joinable()) {
        m_serverThread.join();
    }
    
    if (m_clientThread.joinable()) {
        m_clientThread.join();
    }

    WSACleanup();
}

// 服务器监听线程
void ABBSocketDriver::ServerListenThread() {
    while (m_isRunning) {
        std::cout << "等待ABB机器人连接..." << std::endl;
        
        // 接受连接
        sockaddr_in clientAddr;
        int addrLen = sizeof(clientAddr);
        m_clientSocket = accept(m_serverSocket, (SOCKADDR*)&clientAddr, &addrLen);
        
        if (m_clientSocket == INVALID_SOCKET) {
            if (m_isRunning) {
                std::cerr << "接受连接失败, 错误码: " << WSAGetLastError() << std::endl;
            }
            continue;
        }

        // 获取客户端IP地址
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
        std::cout << "ABB机器人已连接: " << clientIP << std::endl;
        
        m_isConnected = true;
        
        // 启动客户端处理线程
        if (m_clientThread.joinable()) {
            m_clientThread.join();
        }
        m_clientThread = std::thread(&ABBSocketDriver::ClientHandlerThread, this);
    }
}

// 客户端处理线程
void ABBSocketDriver::ClientHandlerThread() {
    const int bufferSize = 1024;
    char buffer[bufferSize];
    
    while (m_isRunning && m_isConnected) {
        // 清空缓冲区
        memset(buffer, 0, bufferSize);
        
        // 接收数据
        int bytesReceived = recv(m_clientSocket, buffer, bufferSize - 1, 0);
        
        if (bytesReceived > 0) {
            // 处理收到的消息
            std::string rawMessage(buffer, bytesReceived);
            ABBMessage message = ParseMessage(rawMessage);
            
            // 添加到消息队列
            {
                // std::lock_guard<std::mutex> lock(m_queueMutex);
                m_messageQueue.push(message);
            }
            
            // 调用回调函数
            {
                // std::lock_guard<std::mutex> lock(m_callbackMutex);
                if (m_messageCallback) {
                    m_messageCallback(message);
                }
            }
        }
        else if (bytesReceived == 0) {
            // 连接关闭
            std::cout << "ABB机器人断开连接" << std::endl;
            m_isConnected = false;
            break;
        }
        else {
            // 发生错误
            std::cerr << "接收数据失败, 错误码: " << WSAGetLastError() << std::endl;
            m_isConnected = false;
            break;
        }
        Sleep(100);
    }
    
    // 关闭客户端socket
    if (m_clientSocket != INVALID_SOCKET) {
        closesocket(m_clientSocket);
        m_clientSocket = INVALID_SOCKET;
    }
}

// 解析RAPID格式消息
ABBMessage ABBSocketDriver::ParseMessage(const std::string& rawMessage) {
    ABBMessage message;
    message.rawMessage = rawMessage;
    message.type = ABBMessageType::UNKNOWN;
    
    // 查找结束符 '#'
    size_t endPos = rawMessage.find('#');
    if (endPos == std::string::npos) {
        // 消息格式错误
        return message;
    }
    
    std::string msgContent = rawMessage.substr(0, endPos);
    
    // 按逗号分割字符串
    std::vector<std::string> tokens;
    size_t start = 0;
    size_t end = msgContent.find(',');
    
    while (end != std::string::npos) {
        tokens.push_back(msgContent.substr(start, end - start));
        start = end + 1;
        end = msgContent.find(',', start);
    }
    
    // 添加最后一个token
    if (start < msgContent.length()) {
        tokens.push_back(msgContent.substr(start));
    }
    
    // 解析指令码
    if (tokens.empty()) {
        return message;
    }
    
    try {
        int instructionCode = std::stoi(tokens[0]);
        message.type = static_cast<ABBMessageType>(instructionCode);
    } catch (const std::exception&) {
        return message;
    }
    
    // 解析参数
    for (size_t i = 1; i < tokens.size(); i++) {
        try {
            if (!tokens[i].empty()) {
                double param = std::stod(tokens[i]);
                message.params.push_back(param);
            }
        } catch (const std::exception&) {
            // 参数解析失败，跳过
            continue;
        }
    }
    
    return message;
}

// 构建RAPID格式命令
std::string ABBSocketDriver::BuildCommand(ABBCommandType cmdType, const std::vector<double>& params) {
    char buff[32];
    std::string msg;
    
    // 添加指令码，格式化为两位数字加逗号
    sprintf(buff, "%02d,", static_cast<int>(cmdType));
    msg += buff;
    
    // 添加参数，每个参数后跟一个逗号
    for (const auto& param : params) {
        sprintf(buff, "%.1f,", param);
        msg += buff;
    }
    
    // 添加结束符
    msg += "#";
    
    return msg;
}

// 发送命令
bool ABBSocketDriver::SendCommand(ABBCommandType cmdType, const std::vector<double>& params) {

    if (!m_isConnected) {
        return false;
    }
    std::string command = BuildCommand(cmdType, params);
    std::cout << "RAPID command: " << command << std::endl;
    return SendMessage(command);
}

// 发送消息
bool ABBSocketDriver::SendMessage(const std::string& message) {
    if (!m_isConnected) {
        return false;
    }
    
    Sleep(100);
    int bytesSent = send(m_clientSocket, message.c_str(), static_cast<int>(message.length()), 0);
    if (bytesSent == SOCKET_ERROR) {
        std::cerr << "发送消息失败, 错误码: " << WSAGetLastError() << std::endl;
        return false;
    }
    
    return true;
}

// 设置消息回调
void ABBSocketDriver::SetMessageCallback(ABBMessageCallback callback) {
    // std::lock_guard<std::mutex> lock(m_callbackMutex);
    m_messageCallback = callback;
}

// 检查是否运行中
bool ABBSocketDriver::IsRunning() const {
    return m_isRunning;
}

// 检查是否已连接
bool ABBSocketDriver::IsConnected() const {
    return m_isConnected;
}

// 获取最新消息
bool ABBSocketDriver::GetLatestMessage(ABBMessage& message) {
    // std::lock_guard<std::mutex> lock(m_queueMutex);
    if (m_messageQueue.empty()) {
        return false;
    }
    
    message = m_messageQueue.front();
    m_messageQueue.pop();
    return true;
}

// 发送鞋码信息
bool ABBSocketDriver::SendShoeCode(int shoeCode, bool isLeftFoot) {
    if (!m_isConnected) {
        return false;
    }
    
    std::vector<double> params;
    params.push_back(static_cast<double>(shoeCode));
    params.push_back(isLeftFoot ? 1.0 : 0.0);  // 1表示左脚，0表示右脚
    
    return SendCommand(ABBCommandType::SEND_SHOE_CODE, params);
}

// 发送鞋子信息结构体
bool ABBSocketDriver::SendShoeInfo(const ShoeInfo& shoeInfo) {
    return SendShoeCode(shoeInfo.shoeCode, shoeInfo.isLeftFoot);
}

// 获取状态
RobotStatus ABBSocketDriver::GetStatus() const {
    return m_currentStatus;
}

// 启动并初始化
bool ABBSocketDriver::Start(int port) {
    // 先初始化，再启动
    if (Initialize(port)) {
        return Start();
    }
    return false;
}

} // namespace RobotDriver