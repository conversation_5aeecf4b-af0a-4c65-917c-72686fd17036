#pragma once

// 防止Windows.h包含winsock.h导致冲突
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

// 确保socket头文件正确包含顺序
#include <WinSock2.h>
#include <ws2tcpip.h>
#include <Windows.h>

// 标准库
#include <string>
#include <functional>
#include <thread>
#include <mutex>
#include <queue>
#include <atomic>
#include <iostream>
#include <sstream>
#include <vector>

// 链接socket库
#pragma comment(lib, "ws2_32.lib") 