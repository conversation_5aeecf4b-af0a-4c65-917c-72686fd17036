#pragma once

// 使用预编译头文件
#include "AbbDriverPCH.h"

namespace RobotDriver {

// 机器人状态结构体
struct RobotStatus {
    std::string position;
    std::string statusMessage;
    bool isMoving;
    bool isError;
    
    RobotStatus() : isMoving(false), isError(false) {}
};

// 定义机器人消息类型（RAPID格式指令码）
enum class ABBMessageType {
    UNKNOWN = -1,
    PICKUP_COMPLETED = 10,        // 机器人取料完成
    REACHED_TARGET_POSITION = 20, // 机器人到达目标位置
    OPERATION_COMPLETED = 30,     // 机器人操作完成
    ABB_ERROR = 99                // 错误信息
};

// 定义机器人指令类型（RAPID格式指令码）
enum class ABBCommandType {
    LASER_READY = 1,        // 激光准备好命令
    MOVE_NEXT = 2,          // 移动到下一位置命令
    STOP = 3,               // 停止命令
    RESET = 4,              // 重置命令
    SEND_SHOE_CODE = 5      // 发送鞋码信息命令
};

// 机器人消息结构
struct ABBMessage {
    ABBMessageType type;
    std::vector<double> params;  // 参数数组
    std::string rawMessage;      // 原始消息
    
    // 兼容性方法，将第一个参数作为content
    std::string GetContent() const {
        if (params.empty()) return "";
        return std::to_string(params[0]);
    }
};

// 鞋子信息结构体
struct ShoeInfo {
    int shoeCode;            // 鞋码
    bool isLeftFoot;         // 是否左脚
};

// 机器人回调函数定义
using ABBMessageCallback = std::function<void(const ABBMessage& message)>;

class ABBSocketDriver {
public:
    ABBSocketDriver();
    ~ABBSocketDriver();

    // 服务器控制
    bool Initialize(int port = 8080);
    bool Start();
    void Stop();
    bool IsRunning() const;
    
    // Windows平台端口清理
    bool ClearPortUsage(int port);
    
    // 设置回调
    void SetMessageCallback(ABBMessageCallback callback);
    
    // 发送命令（RAPID格式）
    bool SendCommand(ABBCommandType cmdType, const std::vector<double>& params = {});
    
    // 发送自定义消息
    bool SendMessage(const std::string& message);

    // 发送鞋码信息
    bool SendShoeCode(int shoeCode, bool isLeftFoot = true);
    
    // 发送鞋子信息结构体
    bool SendShoeInfo(const ShoeInfo& shoeInfo);
    
    // 获取连接状态
    bool IsConnected() const;
    
    // 获取最新消息
    bool GetLatestMessage(ABBMessage& message);
    
    // 添加新方法
    RobotStatus GetStatus() const;
    int GetPort() const { return m_port; }
    bool Start(int port);

private:
    // 服务器监听和客户端处理函数
    void ServerListenThread();
    void ClientHandlerThread();
    
    // RAPID格式消息解析函数
    ABBMessage ParseMessage(const std::string& rawMessage);
    
    // 构造RAPID格式命令字符串
    std::string BuildCommand(ABBCommandType cmdType, const std::vector<double>& params);

private:
    // Socket相关
    SOCKET m_serverSocket;
    SOCKET m_clientSocket;
    std::atomic<bool> m_isRunning;
    std::atomic<bool> m_isConnected;
    int m_port;
    
    // 线程相关
    std::thread m_serverThread;
    std::thread m_clientThread;
    
    // 消息队列
    std::queue<ABBMessage> m_messageQueue;
    std::mutex m_queueMutex;
    
    // 回调函数
    ABBMessageCallback m_messageCallback;
    std::mutex m_callbackMutex;
    
    // 状态信息
    RobotStatus m_currentStatus;
};

} // namespace RobotDriver