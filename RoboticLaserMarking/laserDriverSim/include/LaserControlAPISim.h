#pragma once

#include <Windows.h>
#include <string>
#include <functional>

namespace LaserDriver {

// 回调函数类型定义
typedef std::function<void(const char* msg)> LaserCallbackFunc;

class LaserControl {
public:
    // 构造和析构
    LaserControl();
    ~LaserControl();

    // 连接与断开
    bool Connect();
    bool Disconnect();
    bool IsConnected() const;
    
    // 激光控制
    bool BeginMark(bool previewMode);
    bool EndMark();
    bool LaserOn();
    bool LaserOff();
    bool RedGuide(bool enable);
    bool ResetLaserState();
    bool IsWorking() const;
    
    // 文件操作
    bool LoadProcessParam(const std::string& filePath);
    bool LoadMarkData(const std::string& filePath);
    bool SetWorkDir(const std::string& dirPath);
    bool ClearData();
    int64_t DataSize() const;
    
    // 状态与信息
    uint64_t GetLaserMarkTime() const;
    bool ExternalStartStopSignal(bool enable, bool& start, bool& stop);
    void GetErrorMessage(bool& hasError, std::string& errorMsg) const;
    
    // 设置回调
    void SetCallback(LaserCallbackFunc callback);

private:
    // 模拟设备状态
    bool m_connected;             // 连接状态
    bool m_isWorking;             // 是否正在工作
    bool m_laserOn;               // 激光开关状态
    bool m_redGuideOn;            // 红光开关状态
    int64_t m_dataSize;           // 数据大小
    uint64_t m_markTime;          // 打标时间
    std::string m_workDir;        // 工作目录
    std::string m_errorMsg;       // 错误信息
    bool m_hasError;              // 是否有错误
    LaserCallbackFunc m_callback; // 回调函数
};

// IO控制卡类
class ControlCard {
public:
    ControlCard();
    ~ControlCard();

    // 连接控制
    bool OpenCard(uint16_t cardNo);
    bool CloseCard();
    bool IsCardOpened() const;
    
    // IO操作
    bool WriteOutputIO(uint16_t value);
    bool ReadOutputIO(uint16_t& value);
    bool WriteOutputIOBit(uint16_t ioIndex, bool value);
    bool ReadOutputIOBit(uint16_t ioIndex, bool& value);
    bool ReadInputIO(uint16_t& value);
    bool ReadInputIOBit(uint16_t ioIndex, bool& value);
    bool WaitForIOBit(uint16_t ioIndex, bool cond, double timeouts);

private:
    // 模拟设备状态
    bool m_cardOpened;        // 卡是否打开
    uint16_t m_outputIO;      // 输出IO状态
    uint16_t m_inputIO;       // 输入IO状态
};

}  // namespace LaserDriver