#include "LaserControlAPI.h"
#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <filesystem>

// Laser callback function
void LaserCallback(const char* msg) {
    std::cout << "Laser control callback: " << msg << std::endl;
}

// Get laser error information
void GetLaserError(LaserDriver::LaserControl& laser) {
    bool hasError = false;
    std::string errorMsg;
    laser.GetErrorMessage(hasError, errorMsg);
    if (hasError) {
        std::cout << "Laser error: " << errorMsg << std::endl;
    }
}

int main() {
    using namespace LaserDriver;
    using namespace std::chrono_literals;

    std::cout << "Laser control test program starting..." << std::endl;


    // Create control card instance
    ControlCard controlCard;
    bool cardOpened = false;
    bool laserConnected = false;

    try {
        // Open control card
        std::cout << "Opening control card..." << std::endl;
        if (!controlCard.OpenCard(0)) {
            std::cout << "Failed to open control card" << std::endl;
        }
        cardOpened = true;
        std::cout << "Control card opened successfully" << std::endl;

        // Create laser control instance
        LaserControl laserControl;

        // Set callback function
        laserControl.SetCallback(LaserCallback);

        // Connect to laser controller
        std::cout << "Connecting to laser controller..." << std::endl;
        if (!laserControl.Connect()) {
            std::cout << "Failed to connect to laser controller" << std::endl;
            GetLaserError(laserControl);
            throw std::runtime_error("Failed to connect to laser controller");
        }
        laserConnected = true;
        std::cout << "Laser controller connected successfully" << std::endl;

        // Check connection status
        if (laserControl.IsConnected()) {
            std::cout << "Laser controller is connected" << std::endl;
        } else {
            std::cout << "Laser controller is not connected" << std::endl;
            throw std::runtime_error("Laser controller connection status abnormal");
        }

        std::this_thread::sleep_for(100s);

        // Disconnect
        std::cout << "Disconnecting..." << std::endl;
        if (!laserControl.Disconnect()) {
            std::cout << "Failed to disconnect from laser controller" << std::endl;
            GetLaserError(laserControl);
        } else {
            std::cout << "Laser controller disconnected successfully" << std::endl;
            laserConnected = false;
        }

        // Close control card
        std::cout << "Closing control card..." << std::endl;
        if (!controlCard.CloseCard()) {
            std::cout << "Failed to close control card" << std::endl;
        } else {
            std::cout << "Control card closed successfully" << std::endl;
            cardOpened = false;
        }
    }
    catch (const std::exception& e) {
        std::cout << "Exception occurred during testing: " << e.what() << std::endl;
    }

    // Ensure resources are released
    try {
        if (laserConnected) {
            std::cout << "Exception handling: Disconnecting laser..." << std::endl;
            LaserControl tempLaser;
            tempLaser.Disconnect();
        }
        
        if (cardOpened) {
            std::cout << "Exception handling: Closing control card..." << std::endl;
            controlCard.CloseCard();
        }
    }
    catch (...) {
        std::cout << "Exception occurred during resource cleanup" << std::endl;
    }

    std::cout << "Program ended" << std::endl;
    return 0;
}