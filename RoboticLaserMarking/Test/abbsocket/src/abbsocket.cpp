#include "ABBSocketDriver.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <vector>
#include <string>
#include <atomic>

// 测试状态枚举
enum class TestState {
    WAITING_FOR_CONNECTION,
    PICKUP_COMPLETED,
    WAITING_FOR_LASER_READY,
    LASER_READY_RECEIVED,
    WAITING_FOR_MARKING_COMPLETE,
    OPERATION_COMPLETED,
    CYCLE_COMPLETE
};

// 全局变量
std::atomic<TestState> currentState{TestState::WAITING_FOR_CONNECTION};
std::atomic<bool> testRunning{true};
std::atomic<int> cycleCount{0};
std::atomic<bool> messageReceived{false};
RobotDriver::ABBMessage lastMessage;

// ABB机器人消息回调函数
void OnABBMessage(const RobotDriver::ABBMessage& message) {
    std::cout << "\n[" << std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count() 
        << "s] 收到ABB消息: ";

    switch (message.type) {
        case RobotDriver::ABBMessageType::PICKUP_COMPLETED:
            std::cout << "取料完成 (10)";
            currentState = TestState::WAITING_FOR_LASER_READY;
            break;
        case RobotDriver::ABBMessageType::REACHED_TARGET_POSITION:
            std::cout << "到达目标位置 (20)";
            currentState = TestState::LASER_READY_RECEIVED;
            break;
        case RobotDriver::ABBMessageType::OPERATION_COMPLETED:
            std::cout << "操作完成 (30)";
            currentState = TestState::OPERATION_COMPLETED;
            break;
        case RobotDriver::ABBMessageType::ABB_ERROR:
            std::cout << "错误 (99)";
            break;
        default:
            std::cout << "未知类型 (" << static_cast<int>(message.type) << ")";
            break;
    }

    std::cout << std::endl;
    if (!message.params.empty()) {
        std::cout << "参数: [";
        for (size_t i = 0; i < message.params.size(); ++i) {
            std::cout << message.params[i];
            if (i < message.params.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;
    }
    std::cout << "原始消息: " << message.rawMessage << std::endl;
    
    lastMessage = message;
    messageReceived = true;
}

// 等待特定状态
bool waitForState(TestState expectedState, int timeoutSeconds = 10) {
    auto startTime = std::chrono::steady_clock::now();
    while (currentState != expectedState && testRunning) {
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now() - startTime).count();
        if (elapsed >= timeoutSeconds) {
            std::cout << "等待状态超时: 期望=" << static_cast<int>(expectedState) 
                      << ", 当前=" << static_cast<int>(currentState.load()) << std::endl;
            return false;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    return testRunning;
}

// 模拟激光标记过程
void simulateLaserMarking() {
    std::cout << "\n=== 模拟激光标记过程 ===" << std::endl;
    for (int i = 1; i <= 5; ++i) {
        std::cout << "激光标记进度: " << (i * 20) << "%" << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(1));
        if (!testRunning) break;
    }
    std::cout << "激光标记完成!" << std::endl;
}

// 执行单个测试循环
bool runSingleCycle(RobotDriver::ABBSocketDriver& abbDriver, int cycle) {
    std::cout << "\n\n========== 开始第 " << cycle << " 个测试循环 ==========" << std::endl;
    
    // 步骤1: 模拟机器人发送取料完成消息
    std::cout << "\n步骤1: 等待机器人发送取料完成消息..." << std::endl;
    std::cout << "(在RAPID脚本中: SendRapidMessage 10)" << std::endl;
    if (!waitForState(TestState::WAITING_FOR_LASER_READY, 1500)) {
        std::cout << "等待机器人到达目标位置超时!" << std::endl;
        // 继续测试，不返回失败
    }
    
    // 等待取料完成消息或超时后继续
    std::this_thread::sleep_for(std::chrono::seconds(2));
    
    // 步骤2: 发送激光准备命令
    std::cout << "\n步骤2: 发送激光准备命令..." << std::endl;
    std::cout << "(模拟UI发送: LASER_READY with 鞋码参数)" << std::endl;
    
    // 发送带鞋码参数的激光准备命令
    std::vector<double> shoeParams = {static_cast<double>(40 + cycle % 5), 1.0}; // 鞋码40-44循环, 左脚
    bool sendResult = abbDriver.SendCommand(RobotDriver::ABBCommandType::LASER_READY, shoeParams);
    if (!sendResult) {
        std::cout << "发送激光准备命令失败!" << std::endl;
        return false;
    }

    // 步骤3: 等待机器人到达目标位置
    std::cout << "\n步骤3: 等待机器人到达目标位置..." << std::endl;
    std::cout << "(在RAPID脚本中: WaitForLaserReady -> SendRapidMessage 20)" << std::endl;
    
    if (!waitForState(TestState::LASER_READY_RECEIVED, 1500)) {
        std::cout << "等待机器人到达目标位置超时!" << std::endl;
        // 继续测试，不返回失败
    }
    
    // 步骤4: 模拟激光标记过程
    std::cout << "\n步骤4: 开始激光标记..." << std::endl;
    currentState = TestState::WAITING_FOR_MARKING_COMPLETE;
    simulateLaserMarking();
    
    // 步骤5: 发送移动到下一位置命令（模拟标记完成）
    std::cout << "\n步骤5: 发送移动到下一位置命令..." << std::endl;
    std::cout << "(模拟UI通知标记完成: MOVE_NEXT)" << std::endl;
    
    sendResult = abbDriver.SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT,{101});
    if (!sendResult) {
        std::cout << "发送移动命令失败!" << std::endl;
        return false;
    }


    // 步骤3: 等待机器人到达目标位置
    std::cout << "\n步骤3: 等待机器人到达目标位置..." << std::endl;
    std::cout << "(在RAPID脚本中: WaitForLaserReady -> SendRapidMessage 20)" << std::endl;

    if (!waitForState(TestState::LASER_READY_RECEIVED, 1500)) {
        std::cout << "等待机器人到达目标位置超时!" << std::endl;
        // 继续测试，不返回失败
    }

    // 步骤4: 模拟激光标记过程
    std::cout << "\n步骤4: 开始激光标记..." << std::endl;
    currentState = TestState::WAITING_FOR_MARKING_COMPLETE;
    simulateLaserMarking();


    // 步骤5: 发送移动到下一位置命令（模拟标记完成）
    std::cout << "\n步骤5: 发送移动到下一位置命令..." << std::endl;
    std::cout << "(模拟UI通知标记完成: MOVE_NEXT)" << std::endl;

    sendResult = abbDriver.SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT,{0});
    if (!sendResult) {
        std::cout << "发送移动命令失败!" << std::endl;
        return false;
    }




    // 步骤6: 等待操作完成
    std::cout << "\n步骤6: 等待机器人操作完成..." << std::endl;
    std::cout << "(在RAPID脚本中: WaitUntilMarking -> SendRapidMessage 30)" << std::endl;
    
    if (!waitForState(TestState::OPERATION_COMPLETED, 150)) {
        std::cout << "等待操作完成超时!" << std::endl;
        // 继续测试，不返回失败
    }
    
    std::cout << "\n========== 第 " << cycle << " 个测试循环完成 ==========" << std::endl;
    currentState = TestState::CYCLE_COMPLETE;
    
    return true;
}

int main() {
    std::cout << "=== ABB机器人循环流程自动化测试程序 ===" << std::endl;
    std::cout << "模拟RAPID脚本工作流程:" << std::endl;
    std::cout << "1. PickupShoe -> SendRapidMessage 10 (PICKUP_COMPLETED)" << std::endl;
    std::cout << "2. WaitForLaserReady -> SendRapidMessage 20 (REACHED_TARGET_POSITION)" << std::endl;
    std::cout << "3. WaitUntilMarking -> SendRapidMessage 30 (OPERATION_COMPLETED)" << std::endl;
    std::cout << "4. 循环重复" << std::endl;
    
    // 创建ABB Socket驱动
    RobotDriver::ABBSocketDriver abbDriver;
    
    // 初始化和启动服务器
    if (!abbDriver.Initialize(8080)) {
        std::cerr << "初始化ABB Socket服务器失败" << std::endl;
        return -1;
    }
    
    abbDriver.SetMessageCallback(OnABBMessage);
    
    if (!abbDriver.Start()) {
        std::cerr << "启动ABB Socket服务器失败" << std::endl;
        return -1;
    }
    
    std::cout << "\nABB Socket服务器已启动在端口8080" << std::endl;
    std::cout << "等待ABB机器人连接..." << std::endl;
    
    // 等待连接或用户输入
    std::cout << "\n选择测试模式:" << std::endl;
    std::cout << "1. 等待真实机器人连接" << std::endl;
    std::cout << "2. 模拟模式（不等待连接）" << std::endl;
    std::cout << "请输入选择 (1 或 2): ";
    
    int mode;
    std::cin >> mode;
    
    if (mode == 1) {
        // 等待真实连接
        while (!abbDriver.IsConnected() && testRunning) {
            std::cout << "等待机器人连接..." << std::endl;
            std::this_thread::sleep_for(std::chrono::seconds(2));
        }
        std::cout << "机器人已连接!" << std::endl;
    } else {
        std::cout << "进入模拟模式" << std::endl;
    }
    
    // 询问测试循环次数
    std::cout << "\n请输入测试循环次数 (输入0表示无限循环): ";
    int maxCycles;
    std::cin >> maxCycles;
    
    std::cout << "\n开始循环测试..." << std::endl;
    std::cout << "按 Ctrl+C 停止测试" << std::endl;
    
    // 主测试循环
    int currentCycle = 1;
    while (testRunning && (maxCycles == 0 || currentCycle <= maxCycles)) {
        bool cycleSuccess = runSingleCycle(abbDriver, currentCycle);
        
        if (cycleSuccess) {
            cycleCount++;
            std::cout << "\n✓ 循环 " << currentCycle << " 成功完成" << std::endl;
        } else {
            std::cout << "\n✗ 循环 " << currentCycle << " 失败" << std::endl;
        }
        
        currentCycle++;
        
        // 循环间隔
        if (testRunning && (maxCycles == 0 || currentCycle <= maxCycles)) {
            std::cout << "\n等待3秒后开始下一个循环..." << std::endl;
            for (int i = 3; i > 0 && testRunning; --i) {
                std::cout << i << "... ";
                std::cout.flush();
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            std::cout << std::endl;
        }
    }
    
    // 测试总结
    std::cout << "\n\n=== 测试总结 ===" << std::endl;
    std::cout << "完成的循环数: " << cycleCount.load() << std::endl;
    std::cout << "连接状态: " << (abbDriver.IsConnected() ? "已连接" : "未连接") << std::endl;
    
    // 停止服务器
    std::cout << "\n正在停止服务器..." << std::endl;
    abbDriver.Stop();
    std::cout << "ABB Socket服务器已停止" << std::endl;
    
    std::cout << "\n按任意键退出..." << std::endl;
    std::cin.ignore();
    std::cin.get();
    
    return 0;
}