/**
 * @file test_rfid_driver.cpp
 * @brief 测试RFID驱动程序 - 读取施耐德PLC MW720开始的双字数据
 * <AUTHOR> by AI Assistant
 * @date 2025
 */

#include <iostream>
#include <vector>
#include <iomanip>
#include <thread>
#include <chrono>
#include "RFIDModbusDriver.h"

/**
 * @brief 将两个16位寄存器组合成一个32位双字
 * @param high_word 高位字
 * @param low_word 低位字
 * @return 32位双字值
 */
uint32_t combineToDoubleWord(uint16_t high_word, uint16_t low_word) {
    return (static_cast<uint32_t>(high_word) << 16) | low_word;
}

/**
 * @brief 将两个16位寄存器组合成一个32位双字(小端字节序)
 * @param low_word 低位字(在前)
 * @param high_word 高位字(在后)
 * @return 32位双字值
 */
uint32_t combineToDoubleWordLittleEndian(uint16_t low_word, uint16_t high_word) {
    return (static_cast<uint32_t>(high_word) << 16) | low_word;
}

/**
 * @brief 将两个16位寄存器组合成一个32位双字(字节交换)
 * @param word1 第一个字
 * @param word2 第二个字
 * @return 32位双字值
 */
uint32_t combineToDoubleWordSwapped(uint16_t word1, uint16_t word2) {
    return (static_cast<uint32_t>(word2) << 16) | word1;
}

/**
 * @brief 将32位双字分解为两个16位寄存器
 * @param double_word 32位双字值
 * @param high_word 输出高位字
 * @param low_word 输出低位字
 */
void splitDoubleWord(uint32_t double_word, uint16_t& high_word, uint16_t& low_word) {
    high_word = static_cast<uint16_t>((double_word >> 16) & 0xFFFF);
    low_word = static_cast<uint16_t>(double_word & 0xFFFF);
}

/**
 * @brief 字节序模式枚举
 */
enum ByteOrder {
    LITTLE_ENDIAN = 0, // 小端字节序
    WORD_SWAP = 1      // 字交换模式
};

/**
 * @brief 读取施耐德PLC MW720开始的双字数据
 * @param driver RFID Modbus驱动实例
 * @param start_address 起始地址 (MW720对应Modbus地址720)
 * @param count 要读取的双字数量
 * @return 读取的双字数据向量
 */
std::vector<uint32_t> readDoubleWords(RFIDModbusDriver& driver, int start_address, int count) {
    std::vector<uint32_t> double_words;
    
    // 每个双字需要2个16位寄存器
    int register_count = count * 2;
    std::vector<uint16_t> registers(register_count);
    
    // 读取保持寄存器
    int result = driver.readHoldingRegisters(start_address, register_count, registers.data());
    
    if (result == -1) {
        std::cerr << "读取寄存器失败!" << std::endl;
        return double_words;
    }
    
    std::cout << "成功读取 " << register_count << " 个寄存器" << std::endl;
    
    // 将寄存器数据组合成双字 - 尝试多种字节序
    for (int i = 0; i < count; i++) {
        uint16_t word1 = registers[i * 2];     // 第一个字
        uint16_t word2 = registers[i * 2 + 1]; // 第二个字
        
        // 方式1: 小端字节序 (word1为低位字)
        uint32_t double_word_le = combineToDoubleWordLittleEndian(word1, word2);
        
        // 方式2: 字交换 (word2为高位字)
        uint32_t double_word_swap = combineToDoubleWordSwapped(word1, word2);
        
        // 默认使用小端字节序
        double_words.push_back(double_word_le);
        
        std::cout << "双字 " << i << ": MW" << (start_address + i * 2) << std::endl;
        std::cout << "  原始寄存器: [0x" << std::hex << std::setfill('0') << std::setw(4) << word1
                  << ", 0x" << std::setfill('0') << std::setw(4) << word2 << "]" << std::dec << std::endl;
        std::cout << "  小端字节序: 0x" << std::hex << std::setfill('0') << std::setw(8) << double_word_le
                  << " (" << std::dec << double_word_le << ")" << std::endl;
        std::cout << "  字交换模式: 0x" << std::hex << std::setfill('0') << std::setw(8) << double_word_swap
                  << " (" << std::dec << double_word_swap << ")" << std::endl;
        
        // 检查哪种方式接近真实值153644211
        uint32_t target_value = 153644211;
        std::cout << "  目标值: " << std::dec << target_value << " (0x" << std::hex << target_value << ")" << std::endl;
        
        uint32_t diff_le = (double_word_le > target_value) ? (double_word_le - target_value) : (target_value - double_word_le);
        uint32_t diff_swap = (double_word_swap > target_value) ? (double_word_swap - target_value) : (target_value - double_word_swap);
        
        std::cout << "  与目标值差异:" << std::dec << std::endl;
        std::cout << "    小端: " << diff_le << std::endl;
        std::cout << "    交换: " << diff_swap << std::endl;
        
        if (diff_le < diff_swap) {
            std::cout << "  ** 建议使用小端字节序 **" << std::endl;
        } else {
            std::cout << "  ** 建议使用字交换模式 **" << std::endl;
        }
        std::cout << std::endl;
    }
    
    return double_words;
}

/**
 * @brief 根据指定字节序读取施耐德PLC双字数据
 * @param driver RFID Modbus驱动实例
 * @param start_address 起始地址
 * @param count 要读取的双字数量
 * @param byte_order 字节序模式
 * @return 读取的双字数据向量
 */
std::vector<uint32_t> readDoubleWordsWithByteOrder(RFIDModbusDriver& driver, int start_address, int count, ByteOrder byte_order) {
    std::vector<uint32_t> double_words;
    
    // 每个双字需要2个16位寄存器
    int register_count = count * 2;
    std::vector<uint16_t> registers(register_count);
    
    // 读取保持寄存器
    int result = driver.readHoldingRegisters(start_address, register_count, registers.data());
    
    if (result == -1) {
        std::cerr << "读取寄存器失败!" << std::endl;
        return double_words;
    }
    
    std::cout << "成功读取 " << register_count << " 个寄存器" << std::endl;
    
    const char* byte_order_names[] = {"小端字节序", "字交换模式"};
    std::cout << "使用字节序: " << byte_order_names[byte_order] << std::endl;
    
    // 根据指定字节序组合双字
    for (int i = 0; i < count; i++) {
        uint16_t word1 = registers[i * 2];     // 第一个字
        uint16_t word2 = registers[i * 2 + 1]; // 第二个字
        uint32_t double_word;
        
        switch (byte_order) {
            case LITTLE_ENDIAN:
                double_word = combineToDoubleWordLittleEndian(word1, word2);
                break;
            case WORD_SWAP:
                double_word = combineToDoubleWordSwapped(word1, word2);
                break;
            default:
                double_word = combineToDoubleWordLittleEndian(word1, word2);
                break;
        }
        
        double_words.push_back(double_word);
        
        std::cout << "双字 " << i << ": MW" << (start_address + i * 2) 
                  << " = 0x" << std::hex << std::setfill('0') << std::setw(8) << double_word
                  << " (" << std::dec << double_word << ")" << std::endl;
        std::cout << "  原始寄存器: [0x" << std::hex << std::setfill('0') << std::setw(4) << word1
                  << ", 0x" << std::setfill('0') << std::setw(4) << word2 << "]" << std::dec << std::endl;
    }
    
    return double_words;
}

/**
 * @brief 写入双字数据到施耐德PLC
 * @param driver RFID Modbus驱动实例
 * @param start_address 起始地址
 * @param double_words 要写入的双字数据
 * @return 成功返回0，失败返回-1
 */
int writeDoubleWords(RFIDModbusDriver& driver, int start_address, const std::vector<uint32_t>& double_words) {
    if (double_words.empty()) {
        std::cout << "没有数据需要写入" << std::endl;
        return 0;
    }
    
    // 准备寄存器数据
    std::vector<uint16_t> registers;
    
    for (uint32_t double_word : double_words) {
        uint16_t high_word, low_word;
        splitDoubleWord(double_word, high_word, low_word);
        registers.push_back(high_word);  // 高位字在前
        registers.push_back(low_word);   // 低位字在后
    }
    
    // 写入寄存器
    int result = driver.writeRegisters(start_address, registers.size(), registers.data());
    
    if (result == -1) {
        std::cerr << "写入寄存器失败!" << std::endl;
        return -1;
    }
    
    std::cout << "成功写入 " << double_words.size() << " 个双字到地址 MW" << start_address << std::endl;
    return 0;
}

/**
 * @brief 连续监控PLC数据变化
 * @param driver RFID Modbus驱动实例
 * @param start_address 起始地址
 * @param count 监控的双字数量
 * @param interval_ms 监控间隔(毫秒)
 * @param duration_sec 监控持续时间(秒)
 */
void monitorPLCData(RFIDModbusDriver& driver, int start_address, int count, int interval_ms, int duration_sec) {
    std::cout << "\n开始监控PLC数据变化..." << std::endl;
    std::cout << "监控地址: MW" << start_address << " - MW" << (start_address + count * 2 - 1) << std::endl;
    std::cout << "监控间隔: " << interval_ms << "ms, 持续时间: " << duration_sec << "s" << std::endl;
    std::cout << "按Ctrl+C停止监控\n" << std::endl;
    
    auto start_time = std::chrono::steady_clock::now();
    std::vector<uint32_t> last_values(count, 0xFFFFFFFF); // 初始化为无效值
    
    while (true) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time).count();
        
        if (elapsed >= duration_sec) {
            std::cout << "监控时间到达，停止监控" << std::endl;
            break;
        }
        
        // 读取当前数据
        std::vector<uint32_t> current_values = readDoubleWords(driver, start_address, count);
        
        if (!current_values.empty()) {
            // 检查数据是否有变化
            bool has_change = false;
            for (size_t i = 0; i < current_values.size() && i < last_values.size(); i++) {
                if (current_values[i] != last_values[i]) {
                    has_change = true;
                    break;
                }
            }
            
            if (has_change || last_values[0] == 0xFFFFFFFF) {
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                std::cout << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S") << "] ";
                
                if (last_values[0] != 0xFFFFFFFF) {
                    std::cout << "数据变化检测到!" << std::endl;
                } else {
                    std::cout << "初始数据读取:" << std::endl;
                }
                
                for (size_t i = 0; i < current_values.size(); i++) {
                    std::cout << "  MW" << (start_address + i * 2) << ": 0x" 
                              << std::hex << std::setfill('0') << std::setw(8) << current_values[i]
                              << " (" << std::dec << current_values[i] << ")";
                    
                    if (i < last_values.size() && last_values[i] != 0xFFFFFFFF && current_values[i] != last_values[i]) {
                        std::cout << " [变化: 0x" << std::hex << last_values[i] << " -> 0x" << current_values[i] << std::dec << "]";
                    }
                    std::cout << std::endl;
                }
                std::cout << std::endl;
            }
            
            last_values = current_values;
        }
        
        // 等待指定间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms));
    }
}

/**
 * @brief 连续监控PLC数据变化(支持字节序选择)
 * @param driver RFID Modbus驱动实例
 * @param start_address 起始地址
 * @param count 监控的双字数量
 * @param interval_ms 监控间隔(毫秒)
 * @param duration_sec 监控持续时间(秒)
 * @param byte_order 字节序模式
 */
void monitorPLCDataWithByteOrder(RFIDModbusDriver& driver, int start_address, int count, int interval_ms, int duration_sec, ByteOrder byte_order) {
    const char* byte_order_names[] = {"小端字节序", "字交换模式"};
    
    std::cout << "\n开始监控PLC数据变化..." << std::endl;
    std::cout << "监控地址: MW" << start_address << " - MW" << (start_address + count * 2 - 1) << std::endl;
    std::cout << "字节序模式: " << byte_order_names[byte_order] << std::endl;
    std::cout << "监控间隔: " << interval_ms << "ms, 持续时间: " << duration_sec << "s" << std::endl;
    std::cout << "按Ctrl+C停止监控\n" << std::endl;
    
    auto start_time = std::chrono::steady_clock::now();
    std::vector<uint32_t> last_values(count, 0xFFFFFFFF); // 初始化为无效值
    
    while (true) {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(current_time - start_time).count();
        
        if (elapsed >= duration_sec) {
            std::cout << "监控时间到达，停止监控" << std::endl;
            break;
        }
        
        // 读取当前数据
        std::vector<uint32_t> current_values = readDoubleWordsWithByteOrder(driver, start_address, count, byte_order);
        
        if (!current_values.empty()) {
            // 检查数据是否有变化
            bool has_change = false;
            for (size_t i = 0; i < current_values.size() && i < last_values.size(); i++) {
                if (current_values[i] != last_values[i]) {
                    has_change = true;
                    break;
                }
            }
            
            if (has_change || last_values[0] == 0xFFFFFFFF) {
                auto now = std::chrono::system_clock::now();
                auto time_t = std::chrono::system_clock::to_time_t(now);
                std::cout << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S") << "] ";
                
                if (last_values[0] != 0xFFFFFFFF) {
                    std::cout << "数据变化检测到!" << std::endl;
                } else {
                    std::cout << "初始数据读取:" << std::endl;
                }
                
                for (size_t i = 0; i < current_values.size(); i++) {
                    std::cout << "  MW" << (start_address + i * 2) << ": 0x" 
                              << std::hex << std::setfill('0') << std::setw(8) << current_values[i]
                              << " (" << std::dec << current_values[i] << ")";
                    
                    if (i < last_values.size() && last_values[i] != 0xFFFFFFFF && current_values[i] != last_values[i]) {
                        std::cout << " [变化: 0x" << std::hex << last_values[i] << " -> 0x" << current_values[i] << std::dec << "]";
                    }
                    std::cout << std::endl;
                }
                std::cout << std::endl;
            }
            
            last_values = current_values;
        }
        
        // 等待指定间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(interval_ms));
    }
}

/**
 * @brief 主函数
 */
int main() {
    std::cout << "=== 施耐德PLC MW720双字数据读取测试程序 ===" << std::endl;
    
    // 创建RFID Modbus驱动实例
    RFIDModbusDriver driver;
    
    // PLC连接参数
    std::string plc_ip = "***************";  // 请根据实际PLC IP地址修改
    int plc_port = 502;                     // Modbus TCP标准端口
    
    std::cout << "\n正在连接到施耐德PLC..." << std::endl;
    std::cout << "IP地址: " << plc_ip << std::endl;
    std::cout << "端口: " << plc_port << std::endl;
    
    // 连接到PLC
    if (driver.connectTCP(plc_ip, plc_port) != 0) {
        std::cerr << "连接PLC失败!" << std::endl;
        std::cerr << "请检查:" << std::endl;
        std::cerr << "1. PLC IP地址是否正确: " << plc_ip << std::endl;
        std::cerr << "2. 网络连接是否正常" << std::endl;
        std::cerr << "3. PLC Modbus TCP服务是否启用" << std::endl;
        return -1;
    }
    
    std::cout << "成功连接到PLC!" << std::endl;
    
    // 设置超时时间
    driver.setTimeout(1, 0);  // 1秒超时
    
    try {
        // 测试1: 分析字节序并读取MW720开始的4个双字
        std::cout << "\n=== 测试1: 字节序分析 ===" << std::endl;
        int start_address = 16;  // MW720对应Modbus地址720
        int double_word_count = 4;
        
        std::cout << "正在分析不同字节序模式..." << std::endl;
        std::vector<uint32_t> read_data = readDoubleWords(driver, start_address, double_word_count);
        
        // 让用户选择正确的字节序
        std::cout << "\n=== 测试2: 使用正确字节序重新读取 ===" << std::endl;
        std::cout << "根据上面的分析，请选择正确的字节序模式:" << std::endl;
        std::cout << "0 - 小端字节序 (Little Endian)" << std::endl;
        std::cout << "1 - 字交换模式 (Word Swap)" << std::endl;
        std::cout << "请输入选择 (0-1): ";
        
        int choice;
        std::cin >> choice;
        
        if (choice == 0 || choice == 1) {
            ByteOrder selected_order = static_cast<ByteOrder>(choice);
            std::cout << "\n使用选定的字节序重新读取数据:" << std::endl;
            std::vector<uint32_t> correct_data = readDoubleWordsWithByteOrder(driver, start_address, double_word_count, selected_order);
            
            if (!correct_data.empty()) {
                std::cout << "\n最终读取到的双字数据:" << std::endl;
                for (size_t i = 0; i < correct_data.size(); i++) {
                    std::cout << "双字 " << i << " (MW" << (start_address + i * 2) << "): "
                              << "0x" << std::hex << std::setfill('0') << std::setw(8) << correct_data[i]
                              << " (" << std::dec << correct_data[i] << ")" << std::endl;
                }
                
                // 检查第一个双字是否接近目标值
                if (correct_data.size() > 0) {
                    uint32_t target_value = 153644211;
                    uint32_t actual_value = correct_data[0];
                    uint32_t diff = (actual_value > target_value) ? (actual_value - target_value) : (target_value - actual_value);
                    
                    std::cout << "\n验证结果:" << std::endl;
                    std::cout << "目标值: " << target_value << std::endl;
                    std::cout << "读取值: " << actual_value << std::endl;
                    std::cout << "差异: " << diff << std::endl;
                    
                    if (diff < 1000) {
                        std::cout << "✓ 数据读取正确!" << std::endl;
                    } else {
                        std::cout << "✗ 数据可能仍有问题，请检查PLC配置或地址映射" << std::endl;
                    }
                }
            }
        } else {
            std::cout << "无效选择，使用默认小端字节序" << std::endl;
            read_data = readDoubleWordsWithByteOrder(driver, start_address, double_word_count, LITTLE_ENDIAN);
        }
        
      
        
        // 测试3: 连续监控数据变化(可选)
        char monitor_choice;
        std::cout << "\n是否要启动数据监控? (y/n): ";
        std::cin >> monitor_choice;
        
        if (monitor_choice == 'y' || monitor_choice == 'Y') {
            std::cout << "请选择监控时使用的字节序模式:" << std::endl;
            std::cout << "0 - 小端字节序 (Little Endian)" << std::endl;
            std::cout << "1 - 字交换模式 (Word Swap)" << std::endl;
            std::cout << "请输入选择 (0-1): ";
            
            int monitor_byte_order;
            std::cin >> monitor_byte_order;
            
            if (monitor_byte_order == 0 || monitor_byte_order == 1) {
                ByteOrder selected_monitor_order = static_cast<ByteOrder>(monitor_byte_order);
                monitorPLCDataWithByteOrder(driver, start_address, 2, 1000, 30, selected_monitor_order);  // 监控2个双字，1秒间隔，持续30秒
            } else {
                std::cout << "无效选择，使用小端字节序监控" << std::endl;
                monitorPLCDataWithByteOrder(driver, start_address, 2, 1000, 30, LITTLE_ENDIAN);
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "程序执行出错: " << e.what() << std::endl;
    }
    
    // 断开连接
    std::cout << "\n断开PLC连接..." << std::endl;
    driver.disconnect();
    
    std::cout << "程序结束" << std::endl;
    return 0;
}