/**
 * @file rfid_modbus_server.cpp
 * @brief RFID ModbusTCP服务端程序
 * <AUTHOR> by AI Assistant
 * @date 2024
 */

#include <iostream>
#include <vector>
#include <thread>
#include <chrono>
#include <modbus.h>
#include <modbus-tcp.h>
#include <cstring>
#include <csignal>

// 全局变量用于控制程序运行
static bool g_running = true;

// 信号处理函数
void signal_handler(int signal) {
    if (signal == SIGINT || signal == SIGTERM) {
        std::cout << "\n接收到终止信号，正在关闭服务..." << std::endl;
        g_running = false;
    }
}

// 模拟RFID数据生成
class RFIDDataSimulator {
private:
    std::vector<uint16_t> holding_registers;
    const int REGISTER_COUNT = 1000;  // 模拟1000个寄存器
    const int START_ADDRESS = 720;    // 从MW720开始

    // 生成符合RFID编码规则的随机值
    uint32_t generateRFIDValue() {
        // // 模具楦头号 (1位)
        // int moldHeadNumber = rand() % 10;  // 0-9
        //
        // // 模具编号 (4位)
        // int moldNumber = rand() % 10000;   // 0000-9999
        //
        // // 尺码 (2位)
        // int shoeSize = 16 + (rand() % 32);  // 16-47
        //
        // // 尺码序号 (1位)
        // int sizeSequence = rand() % 10;     // 0-9
        //
        // // 左右脚 (1位)
        // int footSide = (rand() % 2) + 1;    // 1或2
        //
        // // 组合成9位数字
        // uint32_t rfidValue = moldHeadNumber * 100000000 +
        //                    moldNumber * 10000 +
        //                    shoeSize * 100 +
        //                    sizeSequence * 10 +
        //                    footSide;
        // std::cout << rfidValue << std::endl;
        // return rfidValue;
        return 153644211;
    }

public:
    RFIDDataSimulator() {
        // 初始化寄存器
        holding_registers.resize(REGISTER_COUNT, 0);
        
        // 设置初始值
        uint32_t target_value = generateRFIDValue();
        holding_registers[START_ADDRESS] = static_cast<uint16_t>(target_value & 0xFFFF);         // 低位字
        holding_registers[START_ADDRESS + 1] = static_cast<uint16_t>((target_value >> 16) & 0xFFFF); // 高位字
    }

    // 获取寄存器值
    uint16_t getRegister(int address) const {
        if (address >= 0 && address < REGISTER_COUNT) {
            return holding_registers[address];
        }
        return 0;
    }

    // 设置寄存器值
    void setRegister(int address, uint16_t value) {
        if (address >= 0 && address < REGISTER_COUNT) {
            holding_registers[address] = value;
        }
    }

    // 更新modbus映射中的寄存器
    void updateModbusMapping(modbus_mapping_t* mb_mapping) {
        for (int i = 0; i < REGISTER_COUNT; i++) {
            mb_mapping->tab_registers[i] = holding_registers[i];
        }
    }

    // 模拟数据变化
    void simulateDataChange() {
        // 生成新的RFID值
        uint32_t new_value = generateRFIDValue();
        holding_registers[START_ADDRESS] = static_cast<uint16_t>(new_value & 0xFFFF);
        holding_registers[START_ADDRESS + 1] = static_cast<uint16_t>((new_value >> 16) & 0xFFFF);
    }
};

int main() {
    std::cout << "=== RFID ModbusTCP服务端程序 ===" << std::endl;
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 创建Modbus上下文
    modbus_t* ctx = modbus_new_tcp("0.0.0.0", 502);
    if (ctx == nullptr) {
        std::cerr << "创建Modbus上下文失败!" << std::endl;
        return -1;
    }
    
    // 设置调试模式
    modbus_set_debug(ctx, TRUE);
    
    // 设置从站ID
    modbus_set_slave(ctx, 1);
    
    // 创建服务器
    int server_socket = modbus_tcp_listen(ctx, 1);
    if (server_socket == -1) {
        std::cerr << "创建服务器失败!" << std::endl;
        modbus_free(ctx);
        return -1;
    }
    
    std::cout << "服务器已启动，监听端口502..." << std::endl;
    
    // 创建数据模拟器
    RFIDDataSimulator simulator;
    
    // 创建modbus映射
    modbus_mapping_t* mb_mapping = modbus_mapping_new(0, 0, 1000, 0); // 只使用保持寄存器
    if (mb_mapping == nullptr) {
        std::cerr << "创建Modbus映射失败!" << std::endl;
        modbus_free(ctx);
        return -1;
    }
    
    // 主循环
    while (g_running) {
        // 等待客户端连接
        int client_socket = modbus_tcp_accept(ctx, &server_socket);
        if (client_socket == -1) {
            continue;
        }
        
        std::cout << "客户端已连接" << std::endl;
        
        // 处理客户端请求
        while (g_running) {
            uint8_t query[MODBUS_TCP_MAX_ADU_LENGTH];
            int rc = modbus_receive(ctx, query);
            
            if (rc > 0) {
                // 更新映射的寄存器值
                simulator.updateModbusMapping(mb_mapping);
                
                // 处理请求
                int response_length = modbus_reply(ctx, query, rc, mb_mapping);
                
                if (response_length == -1) {
                    std::cerr << "发送响应失败: " << modbus_strerror(errno) << std::endl;
                }
            } else if (rc == -1) {
                // 连接断开
                break;
            }
            
            // 模拟数据变化
            simulator.simulateDataChange();
            
            // 短暂延时
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 关闭客户端连接
        modbus_close(ctx);
    }
    
    // 清理资源
    modbus_mapping_free(mb_mapping);
    modbus_close(ctx);
    modbus_free(ctx);
    
    std::cout << "服务器已关闭" << std::endl;
    return 0;
} 