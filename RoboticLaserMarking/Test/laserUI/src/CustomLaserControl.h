#ifndef CUSTOMLASERCONTROL_H
#define CUSTOMLASERCONTROL_H

#include <QObject>
#include <QString>
#include <QDebug>
#include <memory>
#include <string>
#include <functional>
#include "LaserControlAPI.h"

// 前向声明
class CustomLaserControl;

// 全局回调函数声明
extern "C" void __cdecl GlobalLaserCallback(const char* msg);

// 全局实例指针声明
extern CustomLaserControl* g_laserInstance;

class CustomLaserControl : public QObject {
    Q_OBJECT

public:
    explicit CustomLaserControl(QObject* parent = nullptr);
    ~CustomLaserControl();

    // 连接与断开
    bool connect(std::function<void(const char*)> callback);
    bool disconnect();
    bool isConnected() const;
    bool isDllLoaded() const;
    std::string getDllPath() const;
    std::string getLastError() const;

    // 工作目录设置
    bool setWorkDir(const std::string& dirPath);

    // 激光控制
    bool beginMark(bool previewMode);
    bool endMark();
    bool laserOn();
    bool laserOff();
    bool redGuide(bool enable);
    bool resetLaserState();
    bool isWorking() const;

    // 文件操作
    bool loadProcessParam(const std::string& filePath);
    bool loadMarkData(const std::string& filePath);
    bool clearData();
    int64_t dataSize() const;
    uint64_t getLaserMarkTime() const;

    // 错误处理
    void getErrorMessage(bool& hasError, std::string& errorMsg) const;

    // 异步连接
    Q_SIGNAL void connectFinished(bool success, const QString& errorMsg);
    void connectAsync(std::function<void(const char*)> callback);

    // 回调处理
    void handleCallbackMsg(const char* msg);

private:
    static CustomLaserControl* s_instance;
    bool m_connected;
    bool m_dllLoaded;
    std::string m_lastError;
    std::function<void(const char*)> m_callbackFunc;
    
    // 使用新的LaserControl和ControlCard类
    std::unique_ptr<LaserDriver::LaserControl> m_laserControl;
    std::unique_ptr<LaserDriver::ControlCard> m_controlCard;
};

#endif // CUSTOMLASERCONTROL_H 