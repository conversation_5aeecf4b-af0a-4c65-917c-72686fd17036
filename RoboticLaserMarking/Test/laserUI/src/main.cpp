#include "mainwindow.h"
#include <QApplication>
#include <QMessageBox>
#include <QFileInfo>
#include <QDir>
#include <iostream>
#include <Windows.h>


#pragma execution_character_set("utf-8")

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 显示当前工作目录
    QString currentDir = QDir::currentPath();
    std::cout << "当前工作目录: " << currentDir.toStdString() << std::endl;
    

    
    try {
        MainWindow window;
        window.show();
        return app.exec();
    } catch (const std::exception &e) {
        QMessageBox::critical(nullptr, "错误", QString("程序出现异常: %1").arg(e.what()));
        return -1;
    }
} 