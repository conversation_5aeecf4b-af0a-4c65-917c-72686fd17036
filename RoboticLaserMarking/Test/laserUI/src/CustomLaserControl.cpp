#include "CustomLaserControl.h"
#include <QThread>
#include <QDebug>

// 初始化全局实例指针
CustomLaserControl* g_laserInstance = nullptr;

// 实现全局回调函数 - 简单C风格实现
extern "C" void __cdecl GlobalLaserCallback(const char* msg) {
    // 简单的控制台输出，用于调试
    if (msg) {
        qDebug() << "激光回调:" << msg;
    } else {
        qDebug() << "激光回调: null";
    }
    
    // 调用全局实例的回调处理方法
    if (g_laserInstance) {
        g_laserInstance->handleCallbackMsg(msg);
    }
}

// 初始化静态成员变量
CustomLaserControl* CustomLaserControl::s_instance = nullptr;

CustomLaserControl::CustomLaserControl(QObject* parent)
    : QObject(parent), m_connected(false), m_dllLoaded(false), m_callbackFunc(nullptr) {
    // 设置静态实例和全局指针
    s_instance = this;
    g_laserInstance = this;
    
    // 创建LaserControl实例
    m_laserControl = std::make_unique<LaserDriver::LaserControl>();
    m_controlCard = std::make_unique<LaserDriver::ControlCard>();
    
    // 不在构造函数中设置回调，而是在connect方法中设置
    
    m_dllLoaded = true; // 假设LaserControlAPI已经静态链接
}

CustomLaserControl::~CustomLaserControl() {
    disconnect();
    // 清除静态实例和全局指针
    if (s_instance == this) {
        s_instance = nullptr;
    }
    if (g_laserInstance == this) {
        g_laserInstance = nullptr;
    }
}

void CustomLaserControl::connectAsync(std::function<void(const char*)> callback) {
    QThread* thread = QThread::create([this, callback]() {
        bool result = this->connect(callback);
        emit connectFinished(result, QString::fromStdString(this->getLastError()));
    });
    thread->start();
    QObject::connect(thread, &QThread::finished, thread, &QObject::deleteLater);
}

bool CustomLaserControl::connect(std::function<void(const char*)> callback) {
    qDebug() << "准备连接...";
    
    try {
        // 检查m_laserControl是否有效
        if (!m_laserControl) {
            m_lastError = "激光控制对象未初始化";
            qDebug() << "激光控制对象未初始化";
            return false;
        }
        
        // 保存回调函数
        m_callbackFunc = callback;
        
        // 设置回调函数 - 这一步与C#版本保持一致，在连接前设置回调
        m_laserControl->SetCallback([this](const char* msg) {
            if (m_callbackFunc) {
                m_callbackFunc(msg);
            } else {
                qDebug() << "激光回调(无用户处理函数):" << msg;
            }
        });
        
        // 先尝试关闭之前可能打开的控制卡
        if (m_controlCard) {
            m_controlCard->CloseCard();
        } else {
            m_lastError = "控制卡对象未初始化";
            qDebug() << "控制卡对象未初始化";
            return false;
        }
        
        // 打开控制卡 - 与C#版本保持一致，先打开控制卡
        if (!m_controlCard->OpenCard(0)) {
            m_lastError = "打开控制卡失败";
            qDebug() << "打开控制卡失败";
        }
        
        // 连接激光器 - 确保回调已设置
        qDebug() << "设置回调函数并连接...";
        m_connected = m_laserControl->Connect();
        
        if (!m_connected) {
            // 获取错误信息
            bool hasError = false;
            std::string errorMsg;
            m_laserControl->GetErrorMessage(hasError, errorMsg);
            if (hasError) {
                m_lastError = "连接失败，SDK返回错误: " + errorMsg;
            } else {
                m_lastError = "连接失败，SDK返回错误";
            }
            qDebug() << "连接失败:" << QString::fromStdString(m_lastError);
            
            // 连接失败时关闭控制卡
            m_controlCard->CloseCard();
            return false;
        } else {
            qDebug() << "连接成功!";
            return true;
        }
    } catch (const std::exception& e) {
        m_lastError = std::string("连接异常: ") + e.what();
        qDebug() << "连接异常:" << e.what();
        
        // 异常时关闭控制卡
        try {
            if (m_controlCard) {
                m_controlCard->CloseCard();
            }
        } catch (...) {
            // 忽略关闭时的异常
        }
        
        return false;
    } catch (...) {
        m_lastError = "连接过程中发生未知异常";
        qDebug() << "连接过程中发生未知异常";
        
        // 异常时关闭控制卡
        try {
            if (m_controlCard) {
                m_controlCard->CloseCard();
            }
        } catch (...) {
            // 忽略关闭时的异常
        }
        
        return false;
    }
}

bool CustomLaserControl::disconnect() {
    if (!m_connected) return true;

    try {
        // 检查控制卡和激光控制对象是否有效
        if (!m_controlCard || !m_laserControl) {
            m_lastError = "控制对象未初始化";
            return false;
        }
        
        // 先关闭控制卡
        m_controlCard->CloseCard();
        
        // 断开激光连接
        bool result = m_laserControl->Disconnect();
        if (result) {
            m_connected = false;
        } else {
            bool hasError = false;
            std::string errorMsg;
            m_laserControl->GetErrorMessage(hasError, errorMsg);
            m_lastError = "断开连接失败: " + errorMsg;
        }
        return result;
    } catch (const std::exception& e) {
        m_lastError = std::string("断开连接异常: ") + e.what();
        return false;
    } catch (...) {
        m_lastError = "断开连接过程中发生未知异常";
        return false;
    }
}

bool CustomLaserControl::isConnected() const {
    if (!m_connected || !m_laserControl) return false;
    return m_laserControl->IsConnected();
}

bool CustomLaserControl::isDllLoaded() const {
    return m_dllLoaded;
}

std::string CustomLaserControl::getDllPath() const {
    return "LaserControlAPI (静态链接)";
}

std::string CustomLaserControl::getLastError() const {
    return m_lastError;
}

bool CustomLaserControl::setWorkDir(const std::string& dirPath) {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法设置工作目录";
        return false;
    }
    return m_laserControl->SetWorkDir(dirPath);
}

bool CustomLaserControl::beginMark(bool previewMode) {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法开始打标";
        return false;
    }
    return m_laserControl->BeginMark(previewMode);
}

bool CustomLaserControl::endMark() {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法结束打标";
        return false;
    }
    return m_laserControl->EndMark();
}

bool CustomLaserControl::laserOn() {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法开启激光";
        return false;
    }
    return m_laserControl->LaserOn();
}

bool CustomLaserControl::laserOff() {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法关闭激光";
        return false;
    }
    return m_laserControl->LaserOff();
}

bool CustomLaserControl::redGuide(bool enable) {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法控制红光指示";
        return false;
    }
    return m_laserControl->RedGuide(enable);
}

bool CustomLaserControl::resetLaserState() {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法重置激光状态";
        return false;
    }
    return m_laserControl->ResetLaserState();
}

bool CustomLaserControl::isWorking() const {
    if (!m_connected || !m_laserControl) return false;
    return m_laserControl->IsWorking();
}

bool CustomLaserControl::loadProcessParam(const std::string& filePath) {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法加载处理参数";
        return false;
    }
    return m_laserControl->LoadProcessParam(filePath);
}

bool CustomLaserControl::loadMarkData(const std::string& filePath) {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法加载打标数据";
        return false;
    }
    return m_laserControl->LoadMarkData(filePath);
}

bool CustomLaserControl::clearData() {
    if (!m_connected || !m_laserControl) {
        m_lastError = "未连接或控制对象无效，无法清除数据";
        return false;
    }
    return m_laserControl->ClearData();
}

int64_t CustomLaserControl::dataSize() const {
    if (!m_connected || !m_laserControl) return 0;
    return m_laserControl->DataSize();
}

uint64_t CustomLaserControl::getLaserMarkTime() const {
    if (!m_connected || !m_laserControl) return 0;
    return m_laserControl->GetLaserMarkTime();
}

void CustomLaserControl::getErrorMessage(bool& hasError, std::string& errorMsg) const {
    if (!m_connected || !m_laserControl) {
        hasError = true;
        errorMsg = "未连接到激光控制器或控制器对象无效";
        return;
    }
    
    m_laserControl->GetErrorMessage(hasError, errorMsg);
}

void CustomLaserControl::handleCallbackMsg(const char* msg) {
    if (m_callbackFunc && msg) {
        m_callbackFunc(msg);
    }
} 