#pragma once

#include <Windows.h>
#include <string>
#include <vector>
#include <memory>
#include <QString>
#include <QDir>

#pragma execution_character_set("utf-8")

class DllLoader {
public:
    DllLoader() : m_hModule(nullptr) {}
    ~DllLoader() {
        unload();
    }

    // 根据提供的搜索路径列表加载DLL
    bool loadFromPaths(const std::vector<std::string>& searchPaths, std::string& errorMsg) {
        unload();  // 先卸载已存在的DLL

        errorMsg = "";
        for (const auto& path : searchPaths) {
            m_hModule = LoadLibraryA(path.c_str());
            if (m_hModule) {
                m_loadedPath = path;
                return true;
            }
        }

        // 获取最后错误信息
        DWORD error = GetLastError();
        char buffer[256];
        FormatMessageA(FORMAT_MESSAGE_FROM_SYSTEM, nullptr, error, 0, buffer, sizeof(buffer), nullptr);
        errorMsg = "无法加载DLL，错误: " + std::to_string(error) + " - " + buffer;
        return false;
    }

    // 在当前目录和系统路径中加载DLL
    bool load(const std::string& dllName, std::string& errorMsg) {
        unload();  // 先卸载已存在的DLL

        // 获取当前目录
        char currentDir[MAX_PATH];
        GetCurrentDirectoryA(MAX_PATH, currentDir);

        // 构建搜索路径列表
        std::vector<std::string> searchPaths = {
            std::string(currentDir) + "\\" + dllName,                            // 当前目录
            std::string(currentDir) + "\\..\\lib\\" + dllName,                   // 上级lib目录
            std::string(currentDir) + "\\..\\laserDriver\\lib\\" + dllName,      // 上级laserDriver/lib目录
            "D:\\fuxios\\RoboticLaserMarking\\laserDriver\\lib\\" + dllName,     // 绝对路径
            dllName                                                               // 系统路径
        };

        return loadFromPaths(searchPaths, errorMsg);
    }

    // 卸载DLL
    void unload() {
        if (m_hModule) {
            FreeLibrary(m_hModule);
            m_hModule = nullptr;
        }
        m_loadedPath = "";
    }

    // 获取函数指针
    template<typename T>
    T getProcAddress(const char* procName) {
        if (!m_hModule) return nullptr;
        return reinterpret_cast<T>(GetProcAddress(m_hModule, procName));
    }

    // 检查是否已加载
    bool isLoaded() const {
        return m_hModule != nullptr;
    }

    // 获取已加载的DLL路径
    std::string getLoadedPath() const {
        return m_loadedPath;
    }

    // 获取系统错误信息
    static std::string getLastErrorMessage() {
        DWORD error = GetLastError();
        if (error == 0) return "";

        char buffer[256];
        FormatMessageA(FORMAT_MESSAGE_FROM_SYSTEM, nullptr, error, 0, buffer, sizeof(buffer), nullptr);
        return "错误码 " + std::to_string(error) + ": " + buffer;
    }

private:
    HMODULE m_hModule;
    std::string m_loadedPath;
}; 