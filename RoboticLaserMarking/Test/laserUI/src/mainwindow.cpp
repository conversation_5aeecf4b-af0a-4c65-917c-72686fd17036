#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QDateTime>
#include <QDir>
#include <QFileInfo>
#include <QDebug>

#pragma execution_character_set("utf-8")
// 静态成员初始化
MainWindow* MainWindow::s_instance = nullptr;

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
    , m_connected(false)
{
    ui->setupUi(this);
    s_instance = this;

    // 设置默认工作目录
    m_workDir = "D:/LaserWorkspace";
    ui->txtWorkDir->setText(m_workDir);

    // 设置默认预览模式
    ui->chkPreviewMode->setChecked(true);

    // 记录日志
    ui->txtLog->append("程序启动，当前工作目录: " + QDir::currentPath());
    ui->txtLog->append("激光控制API版本：测试版");

    // 创建激光控制对象
    try {
        ui->txtLog->append("正在创建激光控制对象...");
        
        // 创建自定义控制对象
        m_laserControl = std::make_shared<CustomLaserControl>();
        
        // 检查DLL是否加载成功
        if (m_laserControl->isDllLoaded()) {
            ui->txtLog->append("激光控制DLL加载成功: " + QString::fromStdString(m_laserControl->getDllPath()));
            ui->txtLog->append("激光控制对象创建成功");
        } else {
            ui->txtLog->append("激光控制DLL加载失败: " + QString::fromStdString(m_laserControl->getLastError()));
            QMessageBox::warning(this, "DLL加载警告", "无法加载激光控制DLL: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    } catch (const std::exception& e) {
        ui->txtLog->append("激光控制API初始化异常: " + QString(e.what()));
        QMessageBox::critical(this, "初始化错误", "激光控制API初始化失败: " + QString(e.what()));
    } catch (...) {
        ui->txtLog->append("激光控制API初始化过程中发生未知异常");
        QMessageBox::critical(this, "未知异常", "激光控制API初始化过程中发生未知异常");
    }

    // 连接信号和槽
    connect(ui->btnConnect, &QPushButton::clicked, this, &MainWindow::onConnectClicked);
    connect(ui->btnSetWorkDir, &QPushButton::clicked, this, &MainWindow::onSetWorkDirClicked);
    connect(ui->btnBeginMark, &QPushButton::clicked, this, &MainWindow::onBeginMarkClicked);
    connect(ui->btnEndMark, &QPushButton::clicked, this, &MainWindow::onEndMarkClicked);
    connect(ui->btnLaserOnOff, &QPushButton::clicked, this, &MainWindow::onLaserOnOffClicked);
    connect(ui->btnRedGuide, &QPushButton::clicked, this, &MainWindow::onRedGuideClicked);
    connect(ui->btnResetState, &QPushButton::clicked, this, &MainWindow::onResetStateClicked);
    connect(ui->btnLoadMarkData, &QPushButton::clicked, this, &MainWindow::onLoadMarkDataClicked);
    connect(ui->btnLoadProcessParam, &QPushButton::clicked, this, &MainWindow::onLoadProcessParamClicked);
    connect(ui->btnClearData, &QPushButton::clicked, this, &MainWindow::onClearDataClicked);

    // 设置状态更新定时器
    connect(&m_statusTimer, &QTimer::timeout, this, &MainWindow::updateStatus);
    m_statusTimer.setInterval(500); // 每500毫秒更新一次状态

    // 初始禁用控件
    setControlsEnabled(false);
}

MainWindow::~MainWindow()
{
    if (m_connected && m_laserControl) {
        m_laserControl->disconnect();
    }
    
    delete ui;
    s_instance = nullptr;
}

void MainWindow::LaserCallbackHandler(const char* msg)
{
    if (!msg) {
        // 空消息，忽略
        return;
    }

    if (s_instance) {
        try {
            // 使用QString::fromUtf8代替fromLocal8Bit，避免字符编码问题
            QString message = QString::fromUtf8(msg);
            
            // 将消息添加到日志窗口，使用QMetaObject::invokeMethod确保线程安全
            QString timestamp = QDateTime::currentDateTime().toString("[yyyy-MM-dd hh:mm:ss.zzz] ");
            QString fullMessage = timestamp + message;
            
            // 确保在UI线程上更新UI
            QMetaObject::invokeMethod(s_instance, "appendLog", Qt::QueuedConnection,
                Q_ARG(QString, fullMessage));
        }
        catch (const std::exception& e) {
            qDebug() << "回调处理异常:" << e.what();
        }
        catch (...) {
            qDebug() << "回调处理未知异常";
        }
    }
}

void MainWindow::onConnectClicked()
{
    if (!m_laserControl) {
        ui->txtLog->append("错误: 激光控制对象未初始化");
        QMessageBox::critical(this, "初始化错误", "激光控制对象未正确初始化");
        return;
    }

    if (!m_connected) {
        ui->txtLog->append("尝试连接到激光控制器...");
        // 禁用按钮防止重复点击
        ui->btnConnect->setEnabled(false);
        // 连接信号
        disconnect(m_laserControl.get(), nullptr, this, nullptr);
        connect(m_laserControl.get(), &CustomLaserControl::connectFinished, this, [this](bool result, const QString& errorMsg) {
            ui->btnConnect->setEnabled(true);
            if (result) {
                m_connected = true;
                ui->btnConnect->setText("断开连接");
                ui->lblConnectionStatus->setText("已连接");
                m_statusTimer.start();
                setControlsEnabled(true);
                ui->txtLog->append("成功连接到激光控制器");
            } else {
                ui->txtLog->append("连接失败: " + errorMsg);
                QMessageBox::critical(this, "连接错误", "无法连接到激光控制器: " + errorMsg);
            }
        });
        // 异步连接
        m_laserControl->connectAsync(LaserCallbackHandler);
    } else {
        ui->txtLog->append("正在断开连接...");
        try {
            bool result = m_laserControl->disconnect();
            if (result) {
                m_connected = false;
                ui->btnConnect->setText("连接");
                ui->lblConnectionStatus->setText("未连接");
                m_statusTimer.stop();
                setControlsEnabled(false);
                ui->txtLog->append("已断开与激光控制器的连接");
            } else {
                QString errorMsg = QString::fromStdString(m_laserControl->getLastError());
                ui->txtLog->append("断开连接失败: " + errorMsg);
            }
        } catch (const std::exception& e) {
            ui->txtLog->append("断开连接过程中发生异常: " + QString(e.what()));
        } catch (...) {
            ui->txtLog->append("断开连接过程中发生未知异常");
        }
    }
}

void MainWindow::onSetWorkDirClicked()
{
    if (!m_laserControl) return;
    
    QString dir = QFileDialog::getExistingDirectory(this, "选择工作目录", m_workDir);
    
    if (!dir.isEmpty()) {
        m_workDir = dir;
        ui->txtWorkDir->setText(dir);
        
        // 设置工作目录
        if (m_connected) {
            if (m_laserControl->setWorkDir(dir.toStdString())) {
                ui->txtLog->append("设置工作目录成功: " + dir);
            } else {
                showErrorMessage("设置工作目录失败: " + QString::fromStdString(m_laserControl->getLastError()));
            }
        }
    }
}

void MainWindow::onBeginMarkClicked()
{
    if (!m_laserControl) return;
    
    bool preview = ui->chkPreviewMode->isChecked();
    
    if (m_laserControl->beginMark(preview)) {
        ui->txtLog->append(QString("开始%1").arg(preview ? "预览模式" : "打标"));
        ui->btnBeginMark->setEnabled(false);
        ui->btnEndMark->setEnabled(true);
    } else {
        showErrorMessage("开始打标失败: " + QString::fromStdString(m_laserControl->getLastError()));
    }
}

void MainWindow::onEndMarkClicked()
{
    if (!m_laserControl) return;
    
    if (m_laserControl->endMark()) {
        ui->txtLog->append("结束打标");
        ui->btnBeginMark->setEnabled(true);
        ui->btnEndMark->setEnabled(false);
        
        // 更新打标时间
        uint64_t markTime = m_laserControl->getLaserMarkTime();
        ui->lblMarkTime->setText(QString::number(markTime / 1000.0) + " ms");
    } else {
        showErrorMessage("结束打标失败: " + QString::fromStdString(m_laserControl->getLastError()));
    }
}

void MainWindow::onLaserOnOffClicked()
{
    if (!m_laserControl) return;
    
    static bool laserOn = false;
    
    if (!laserOn) {
        // 打开激光
        if (m_laserControl->laserOn()) {
            laserOn = true;
            ui->btnLaserOnOff->setText("激光关闭");
            ui->txtLog->append("激光已开启");
        } else {
            showErrorMessage("激光开启失败: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    } else {
        // 关闭激光
        if (m_laserControl->laserOff()) {
            laserOn = false;
            ui->btnLaserOnOff->setText("激光开启");
            ui->txtLog->append("激光已关闭");
        } else {
            showErrorMessage("激光关闭失败: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    }
}

void MainWindow::onRedGuideClicked()
{
    if (!m_laserControl) return;
    
    static bool redGuideOn = false;
    
    if (!redGuideOn) {
        // 打开红光指示
        if (m_laserControl->redGuide(true)) {
            redGuideOn = true;
            ui->btnRedGuide->setText("关闭红光");
            ui->txtLog->append("红光指示已开启");
        } else {
            showErrorMessage("无法开启红光指示: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    } else {
        // 关闭红光指示
        if (m_laserControl->redGuide(false)) {
            redGuideOn = false;
            ui->btnRedGuide->setText("红光指示");
            ui->txtLog->append("红光指示已关闭");
        } else {
            showErrorMessage("无法关闭红光指示: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    }
}

void MainWindow::onResetStateClicked()
{
    if (!m_laserControl) return;
    
    if (m_laserControl->resetLaserState()) {
        ui->txtLog->append("激光状态已重置");
    } else {
        showErrorMessage("重置激光状态失败: " + QString::fromStdString(m_laserControl->getLastError()));
    }
}

void MainWindow::onLoadMarkDataClicked()
{
    if (!m_laserControl) return;
    
    QString filePath = QFileDialog::getOpenFileName(this, "选择打标数据文件", m_workDir, "打标数据文件 (*.ezd);;所有文件 (*.*)");
    
    if (!filePath.isEmpty()) {
        if (m_laserControl->loadMarkData(filePath.toStdString())) {
            ui->txtLog->append("成功加载打标数据: " + filePath);
            
            // 更新数据大小
            int64_t dataSize = m_laserControl->dataSize();
            ui->lblDataSize->setText(QString::number(dataSize) + " 字节");
        } else {
            showErrorMessage("加载打标数据失败: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    }
}

void MainWindow::onLoadProcessParamClicked()
{
    if (!m_laserControl) return;
    
    QString filePath = QFileDialog::getOpenFileName(this, "选择处理参数文件", m_workDir, "处理参数文件 (*.para);;所有文件 (*.*)");
    
    if (!filePath.isEmpty()) {
        if (m_laserControl->loadProcessParam(filePath.toStdString())) {
            ui->txtLog->append("成功加载处理参数: " + filePath);
        } else {
            showErrorMessage("加载处理参数失败: " + QString::fromStdString(m_laserControl->getLastError()));
        }
    }
}

void MainWindow::onClearDataClicked()
{
    if (!m_laserControl) return;
    
    if (m_laserControl->clearData()) {
        ui->txtLog->append("数据已清除");
        
        // 更新数据大小
        ui->lblDataSize->setText("0 字节");
    } else {
        showErrorMessage("清除数据失败: " + QString::fromStdString(m_laserControl->getLastError()));
    }
}

void MainWindow::updateStatus()
{
    if (!m_laserControl) return;
    
    if (m_connected) {
        // 更新连接状态
        bool isConnected = m_laserControl->isConnected();
        ui->lblIsConnected->setText(isConnected ? "已连接" : "未连接");
        
        // 更新工作状态
        bool isWorking = m_laserControl->isWorking();
        ui->lblIsWorking->setText(isWorking ? "工作中" : "空闲");
        
        // 更新数据大小
        int64_t dataSize = m_laserControl->dataSize();
        ui->lblDataSize->setText(QString::number(dataSize) + " 字节");
        
        // 获取错误信息
        bool hasError = false;
        std::string errorMsg;
        m_laserControl->getErrorMessage(hasError, errorMsg);
        
        if (hasError) {
            ui->lblErrorStatus->setText(QString::fromStdString(errorMsg));
        } else {
            ui->lblErrorStatus->setText("正常");
        }
    }
}

void MainWindow::appendLog(const QString &message)
{
    // 这个方法在UI线程执行，安全地更新UI
    ui->txtLog->append(message);
}

void MainWindow::showErrorMessage(const QString &message)
{
    ui->txtLog->append("错误: " + message);
    QMessageBox::critical(this, "错误", message);
}

void MainWindow::setControlsEnabled(bool enabled)
{
    // 设置控件启用状态
    ui->btnBeginMark->setEnabled(enabled);
    ui->btnEndMark->setEnabled(false);  // 初始状态下禁用结束按钮
    ui->btnLaserOnOff->setEnabled(enabled);
    ui->btnRedGuide->setEnabled(enabled);
    ui->btnResetState->setEnabled(enabled);
    ui->btnLoadMarkData->setEnabled(enabled);
    ui->btnLoadProcessParam->setEnabled(enabled);
    ui->btnClearData->setEnabled(enabled);
    ui->btnSetWorkDir->setEnabled(enabled);
    ui->chkPreviewMode->setEnabled(enabled);
} 