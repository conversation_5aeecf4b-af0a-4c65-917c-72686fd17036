#pragma once

#include <QMainWindow>
#include <QTimer>
#include <QString>
#include <QTextEdit>
#include <QLineEdit>
#include <QPushButton>
#include <QCheckBox>
#include <QLabel>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <memory>
#include "CustomLaserControl.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 连接/断开激光控制器
    void onConnectClicked();
    // 设置工作目录
    void onSetWorkDirClicked();
    // 开始打标
    void onBeginMarkClicked();
    // 结束打标
    void onEndMarkClicked();
    // 开启/关闭激光
    void onLaserOnOffClicked();
    // 红光指示
    void onRedGuideClicked();
    // 重置激光状态
    void onResetStateClicked();
    // 加载打标数据
    void onLoadMarkDataClicked();
    // 加载处理参数
    void onLoadProcessParamClicked();
    // 清除数据
    void onClearDataClicked();
    // 更新状态信息
    void updateStatus();
    // 显示错误信息
    void showErrorMessage(const QString &message);
    // 安全添加日志消息（从其他线程调用）
    void appendLog(const QString &message);

private:
    // 设置UI控件状态
    void setControlsEnabled(bool enabled);
    // 激光回调处理
    static void LaserCallbackHandler(const char* msg);
    static MainWindow* s_instance;

    Ui::MainWindow *ui;
    std::shared_ptr<CustomLaserControl> m_laserControl;
    QTimer m_statusTimer;
    QString m_workDir;
    bool m_connected;
}; 