#pragma once

#include <QString>
#include <QVector>
#include <QFile>
#include <QTextStream>
#include <QStringList>
#include <QDir>
#include <QDebug>
#include <QStandardItemModel>
#include <QDateTime>

// 鞋子模板结构体
struct ShoeTemplate {
    QString rfidId;              // RFID标识
    int shoeCode;                // 鞋码(16-47)
    bool isLeftFoot;             // 是否左脚
    QString templateName;        // 模板名称
    QString laserWorkDir;        // 激光工作目录
    QString markDataDir;         // 打标数据目录
    QString processParamDir;     // 工艺参数目录
    int markCount;               // 打粗次数统计
    QDateTime lastMarkTime;      // 上次打粗时间
};

class TemplateManager : public QObject
{
    Q_OBJECT

public:
    explicit TemplateManager(QObject *parent = nullptr);
    ~TemplateManager();

    // 模板操作
    bool addTemplate(const ShoeTemplate& templ);
    bool updateTemplate(const ShoeTemplate& templ);
    bool deleteTemplate(const QString& rfidId);
    
    // 通过RFID查找模板
    bool findTemplateByRFID(const QString& rfidId, ShoeTemplate& templ);
    
    // 通过鞋码和左右脚查找
    bool findTemplateByShoeInfo(int shoeCode, bool isLeftFoot, ShoeTemplate& templ);
    
    // 获取所有模板
    QVector<ShoeTemplate> getAllTemplates() const;
    
    // CSV文件操作
    bool loadFromCSV(const QString& filePath);
    int saveToCSV(const QString& filePath);
    
    // 导入导出
    bool importTemplates(const QString& filePath);
    bool exportTemplates(const QString& filePath);
    
    // 更新统计信息
    bool updateMarkCount(const QString& rfidId);
    
    // 获取模板表格模型
    void updateTemplateModel(QStandardItemModel* model);
    
    // 检查模板是否存在
    bool templateExists(const QString& rfidId);
    
    // 获取模板数量
    int getTemplateCount() const;
    
    // 设置CSV文件路径
    void setCSVFilePath(const QString& filePath);
    
    // 创建默认模板
    void createDefaultTemplate();
    
signals:
    void templateAdded(const ShoeTemplate& templ);
    void templateUpdated(const ShoeTemplate& templ);
    void templateDeleted(const QString& rfidId);
    void templatesLoaded();
    void errorOccurred(const QString& error);

private:
    QVector<ShoeTemplate> m_templates;
    QString m_csvFilePath;
    
    // CSV列索引定义
    enum CSVColumns {
        RFID_ID = 0,
        SHOE_CODE,
        IS_LEFT_FOOT,
        TEMPLATE_NAME,
        MARK_DATA_FILE,
        PROCESS_PARAM_FILE,
        MARK_COUNT,
        LAST_MARK_TIME
    };
    
    // CSV文件读写辅助函数
    QStringList templateToStringList(const ShoeTemplate& templ);
    ShoeTemplate stringListToTemplate(const QStringList& fields);
};