#pragma once

#include <QPushButton>
#include <QWidget>
#include <QLabel>
#include <QString>
#include <QList>
#include <functional>
#include <memory>
#include "UIUtils.h"
/**
 * @brief 设备控制工具类
 * 
 * 提供通用的设备连接、断开、状态管理功能
 * 减少各个设备控制代码中的重复
 */
class DeviceControlUtils
{
public:
    // 设备状态枚举
    enum class DeviceState {
        Disconnected,
        Connecting,
        Connected,
        Error
    };

    // 设备操作结果
    struct OperationResult {
        bool success;
        QString message;
        QString errorDetails;
    };

    // 设备操作回调函数类型
    using DeviceOperation = std::function<OperationResult()>;
    using StatusUpdateCallback = std::function<void(DeviceState state, const QString& message)>;

    /**
     * @brief 设备控制按钮组
     */
    struct DeviceButtonGroup {
        QPushButton* connectButton;
        QPushButton* disconnectButton;
        QLabel* statusLabel;
        QString deviceName;
        
        DeviceButtonGroup(const QString& name, QWidget* parent = nullptr);
        void updateState(DeviceState state, const QString& message = "");
    };

    /**
     * @brief 设备控制器类
     * 
     * 管理单个设备的连接、断开操作和状态显示
     */
    class DeviceController
    {
    public:
        DeviceController(const QString& deviceName, QWidget* parent = nullptr);
        ~DeviceController() = default;

        // 设置操作回调
        void setConnectOperation(DeviceOperation operation);
        void setDisconnectOperation(DeviceOperation operation);
        void setStatusUpdateCallback(StatusUpdateCallback callback);

        // 获取按钮组
        DeviceButtonGroup* getButtonGroup() { return &m_buttonGroup; }

        // 执行操作
        void executeConnect();
        void executeDisconnect();

        // 手动更新状态
        void updateState(DeviceState state, const QString& message = "");

        // 获取当前状态
        DeviceState getCurrentState() const { return m_currentState; }

    private:
        DeviceButtonGroup m_buttonGroup;
        DeviceOperation m_connectOperation;
        DeviceOperation m_disconnectOperation;
        StatusUpdateCallback m_statusCallback;
        DeviceState m_currentState;

        void setupConnections();
        void handleOperationResult(const OperationResult& result, bool isConnect);
    };

    /**
     * @brief 多设备管理器
     * 
     * 管理多个设备的统一操作
     */
    class MultiDeviceManager
    {
    public:
        MultiDeviceManager() = default;
        ~MultiDeviceManager() = default;

        // 添加设备控制器
        void addDevice(std::shared_ptr<DeviceController> controller);

        // 批量操作
        void connectAllDevices();
        void disconnectAllDevices();

        // 获取所有设备状态
        QList<DeviceState> getAllStates() const;
        bool areAllDevicesConnected() const;
        bool areAllDevicesDisconnected() const;

        // 设置全局状态回调
        void setGlobalStatusCallback(std::function<void(const QString&)> callback);

    private:
        QList<std::shared_ptr<DeviceController>> m_devices;
        std::function<void(const QString&)> m_globalStatusCallback;
    };

    // 静态工具方法

    /**
     * @brief 创建标准的设备控制按钮组
     */
    static DeviceButtonGroup createDeviceButtons(const QString& deviceName, 
                                               const QString& connectText = "连接",
                                               const QString& disconnectText = "断开",
                                               QWidget* parent = nullptr);

    /**
     * @brief 通用的激光设备错误处理
     */
    template<typename LaserControl>
    static OperationResult handleLaserOperation(LaserControl* laserControl, 
                                              std::function<bool()> operation,
                                              const QString& operationName);

    /**
     * @brief 通用的设备连接状态检查
     */
    template<typename Device>
    static bool checkDeviceConnection(Device* device, const QString& deviceName, QWidget* parent = nullptr);

    /**
     * @brief 设备状态到字符串的转换
     */
    static QString stateToString(DeviceState state);

    /**
     * @brief 获取设备状态对应的颜色
     */
    static QString stateToColor(DeviceState state);

private:
    DeviceControlUtils() = default; // 工具类，不允许实例化
};

// 模板方法实现
template<typename LaserControl>
DeviceControlUtils::OperationResult DeviceControlUtils::handleLaserOperation(
    LaserControl* laserControl, 
    std::function<bool()> operation,
    const QString& operationName)
{
    OperationResult result;
    
    if (!laserControl) {
        result.success = false;
        result.message = operationName + "失败";
        result.errorDetails = "激光控制器未初始化";
        return result;
    }

    if (!laserControl->IsConnected()) {
        result.success = false;
        result.message = operationName + "失败";
        result.errorDetails = "激光设备未连接";
        return result;
    }

    result.success = operation();
    if (result.success) {
        result.message = operationName + "成功";
    } else {
        result.message = operationName + "失败";
        
        // 获取详细错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);
        
        if (hasError) {
            result.errorDetails = QString::fromStdString(errorMsg);
        } else {
            result.errorDetails = "未知错误";
        }
    }
    
    return result;
}

template<typename Device>
bool DeviceControlUtils::checkDeviceConnection(Device* device, const QString& deviceName, QWidget* parent)
{
    if (!device) {
        UIUtils::showWarningMessage(parent, "设备检查", deviceName + "控制器未初始化");
        return false;
    }

    if (!device->IsConnected()) {
        UIUtils::showWarningMessage(parent, "设备检查", deviceName + "未连接，请先连接设备");
        return false;
    }

    return true;
}
