#pragma once

#include <QDialog>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QGridLayout>
#include <QClipboard>
#include <QApplication>
#include <QMessageBox>
#include <QTextEdit>
#include "LicenseManager.h"

class RegisterDialog : public QDialog
{
    Q_OBJECT

public:
    explicit RegisterDialog(LicenseManager* licenseManager, QWidget *parent = nullptr);
    ~RegisterDialog();

private slots:
    // 复制MAC地址
    void onCopyMACAddresses();
    
    // 关闭对话框
    void onClose();
    
    // 继续试用
    void onContinueTrial();
    
    // 刷新状态
    void onRefreshStatus();

private:
    // 更新UI状态
    void updateUI();
    
    // 创建UI元素
    void setupUI();

    LicenseManager* m_licenseManager;
    
    // UI控件
    QLabel* lblTitle;
    QLabel* lblMACAddresses;
    QTextEdit* txtMACAddresses;
    QLabel* lblStatus;
    QLabel* lblTrialDays;
    
    QPushButton* btnCopyMACAddresses;
    QPushButton* btnRefreshStatus;
    QPushButton* btnClose;
    QPushButton* btnContinueTrial;
    
    QGridLayout* mainLayout;
};