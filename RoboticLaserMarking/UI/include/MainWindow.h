#pragma once

#include <Executor.h>
#include <QMainWindow>
#include <QTimer>
#include <QLabel>
#include <QThread>
#include <QMutex>
#include <QComboBox>
#include <QPushButton>
#include <QCheckBox>
#include <QTableView>
#include <QGridLayout>
#include <QLineEdit>
#include <QGroupBox>
#include <QStackedWidget>
#include <QListWidget>
#include <QStatusBar>
#include <QProgressBar>
#include <QFile>
#include <QTextEdit>
#include <QDateTime>
#include <QHeaderView>
#include <QStandardItemModel>
#include <QMessageBox>
#include <QFileDialog>
#include <QChart>
#include <QChartView>
#include <QLineSeries>
#include <memory>

#include "ABBSocketDriver.h"
#include "LaserControlAPI.h"
// #include "LaserControlAPISim.h"
#include "RFIDModbusDriver.h"
#include "TemplateManager.h"
#include "DeviceStatusWidget.h"
#include "LogWidget.h"
#include "TemplateEditDialog.h"
#include "SystemSettingsDialog.h"
#include "LicenseManager.h"

QT_CHARTS_USE_NAMESPACE

// Forward declarations for new statistics components
class ProductionStatisticsManager;
class EnhancedStatisticsWidget;
class DetailedReportWidget;
class StatisticsExporter;



class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();


    // RFID队列访问接口
    QString getNextRFIDFromQueue();
    bool isRFIDQueueEmpty() ;
    int getRFIDQueueSize() ;

private slots:
    // 设备连接控制
    void onInitializeAllDevices();

    void updateLicenseStatus();

    void onConnectABB();
    void onDisconnectABB();
    void onConnectLaser();
    void onDisconnectLaser();
    void onConnectRFID();
    void onDisconnectRFID();
    
    // 激光控制
    void onLaserOn();
    void onLaserOff();
    void onRedGuideToggle(bool enable);
    void onLoadMarkData();
    void onLoadProcessParam();
    void onSetWorkDir();
    void onSingleMark();
    void onClearLaserData();
    void onResetLaserStatus();
    
    // 模板管理
    void onAddTemplate();
    void onEditTemplate();
    void onDeleteTemplate();
    void onImportTemplates();
    void onExportTemplates();
    void saveTemplates();
    
    // 系统控制
    void onStartSystem();
    void onStopSystem();
    void onPauseSystem();
    void onResetSystem();
    void onExitProgram();
    
    // 系统设置
    void onSystemSettings();
    
    // 状态更新
    void updateDeviceStatus();
    void updateStatistics();
    
    // 激光工作状态检查
    void checkLaserWorkingStatus();
    
    // 刷新UI样式
    void refreshUIStyle();
    
    // ABB机器人消息处理
    void onABBMessageReceived(const RobotDriver::ABBMessage& message);
    

    
    // 状态更新处理
    void onLogMessage(const QString& message, const QString& category = "系统");
    
    // 页面切换
    void onNavItemChanged(int index);
    
    // 注册管理
    void onRegisterSoftware();
    void onShowLicenseInfo();
    void onOpenShoePathEditor();
    bool checkLicense();
    

private:
    // 初始化UI
    void setupUI();
    void createLogoPanel();  
    void createActions();
    void createMenus();
    void createStatusBar();

    bool eventFilter(QObject *obj, QEvent *event);

    void createDevicePanel();
    void createNavigation();
    void createMainArea();
    void createSystemControlPanel();
    
    // 流程处理函数
    void handleRFIDRequest();
    void startLaserMarking();
    void finishLaserMarking();
    void updateMarkingProgress(int progress);
    
    // RFID队列管理函数
    void startRFIDTimer();  // 启动RFID定时器
    void stopRFIDTimer();   // 停止RFID定时器
    void processRFIDQueue(); // 处理RFID队列
    bool isValidRFIDCode(const QString& rfidCode); // 验证RFID编码规则
    
    // 新增方法用于多面打粗
    bool processMarkingFace();
    int countTotalFaces(const QString& workDir);
    
    // 统计信息相关函数
    void updateStatisticsChart();
    void initializeStatisticsManager();
    void initializeDetailedReportWidget();
    void onStatisticsRecordStarted(const QString& recordId);
    void onStatisticsRecordFinished(const QString& recordId);
    
    // 系统设置相关函数
    SystemSettings getSystemSettings();
    void saveSystemSettings(const SystemSettings& settings);
    void applySystemSettings(const SystemSettings& settings);
    
    // 异步设备连接
    void connectDevicesAsync();
    
    
    // 多面打粗相关变量
    int m_currentFace;  // 当前打粗面编号(101, 102, ...)
    int m_totalFaces;   // 总面数
    ShoeTemplate m_currentTemplate;  // 当前处理的模板
    int m_laserNotWorkingCount;  // 激光未工作计数器
    bool m_isLaserProcessing;  // 激光是否正在处理中的标志
    
    // RFID队列管理相关变量
    QStringList rfidQueue;  // RFID队列
    QString lastRFIDCode;   // 上次读取的RFID编码
    QMutex rfidQueueMutex;  // RFID队列互斥锁
    // UI组件
    QWidget* centralWidget;
    QGridLayout* mainLayout;
    QStatusBar* statusBar;
    QWidget* logoPanel; 
    
    // 导航栏
    QListWidget* navigationList;
    QStackedWidget* mainAreaStack;
    
    // 设备面板
    QGroupBox* devicePanel;
    QPushButton* btnConnectABB;
    QPushButton* btnDisconnectABB;
    QPushButton* btnConnectLaser;
    QPushButton* btnDisconnectLaser;
    QPushButton* btnConnectRFID;
    QPushButton* btnDisconnectRFID;

    
    // 系统控制面板
    QGroupBox* systemControlPanel;
    QPushButton* btnStart;
    QPushButton* btnStop;
    QPushButton* btnPause;
    QPushButton* btnReset;
    QPushButton* btnExit;
    
    // 激光控制面板
    QGroupBox* laserControlPanel;
    QPushButton* btnLaserOn;
    QPushButton* btnLaserOff;
    QCheckBox* btnRedGuide;
    QPushButton* btnLoadMarkData;
    QPushButton* btnLoadProcessParam;
    QPushButton* btnSetWorkDir;
    QPushButton* btnSingleMark;
    QPushButton* btnClearLaserData;
    QPushButton* btnResetLaserStatus;
    QLabel* lblCurrentMarkDataDir;
    QLabel* lblCurrentProcessParamDir;
    QLabel* lblCurrentWorkDir;
    
    // 模板管理
    QTableView* templateTableView;
    QStandardItemModel* templateModel;
    QPushButton* btnAddTemplate;
    QPushButton* btnEditTemplate;
    QPushButton* btnDeleteTemplate;
    QPushButton* btnImportTemplates;
    QPushButton* btnExportTemplates;
    QPushButton* btnOpenShoePathEditor;
    
    // 统计视图
    QChartView* statisticsChartView;
    QChart* statisticsChart;

    // 增强统计组件
    EnhancedStatisticsWidget* enhancedStatisticsWidget;
    
    // 日志显示
    LogWidget* logWidget;
    
    // 设备状态显示
    DeviceStatusWidget* deviceStatusWidget;
    
    // 设备驱动
    std::shared_ptr<LaserDriver::LaserControl> laserControl;
    std::shared_ptr<RobotDriver::ABBSocketDriver> abbDriver;
    std::shared_ptr<RFIDModbusDriver> rfidDriver;

    // 模板管理
    std::shared_ptr<TemplateManager> templateManager;

    // 许可证管理
    std::shared_ptr<LicenseManager> licenseManager;

    // 统计管理
    std::shared_ptr<ProductionStatisticsManager> statisticsManager;
    std::shared_ptr<StatisticsExporter> statisticsExporter;
    DetailedReportWidget* detailedReportWidget;
    
    // 定时器
    QTimer* statusUpdateTimer;
    QTimer* statisticsUpdateTimer;
    QTimer* rfidTimer;  // RFID定时读取定时器
    QTimer* errorRetryTimer;  // ABB错误重试定时器
    QTimer* laserWorkingCheckTimer;  // 激光工作状态检查定时器
 
    // 工作流程状态枚举
    enum class WorkflowState {
        IDLE,                    // 空闲状态
        WAITING_PICKUP,          // 等待取料完成
        WAITING_TARGET_POSITION, // 等待到达目标位置
        WAITING_OPERATION_COMPLETE // 等待操作完成
    };
    
    // 系统状态
    bool systemRunning;
    bool systemPaused;
    WorkflowState currentWorkflowState; // 当前工作流程状态
    
    // ABB错误处理相关状态
    bool abbErrorOccurred;  // ABB错误发生标志
    WorkflowState savedWorkflowState;  // 保存的工作流程状态
    int savedCurrentFace;  // 保存的当前面编号
    QString savedRFIDCode;  // 保存的RFID编码
    
    // 统计数据
    int totalProcessed;
    int currentSessionProcessed;
    QDateTime sessionStartTime;
    QString currentProductionRecordId;  // 当前生产记录ID
    
    // 日志文件
    std::shared_ptr<QFile> logFile;

    // 设备线程
    std::shared_ptr<QThread> abbThread;
    std::shared_ptr<QThread> rfidThread;
    
    // 注册状态标签
    QLabel* lblLicenseStatus;
    QString rfidDecimal;
    std::shared_ptr<Fuxi::Common::Executor> _executor;
    std::shared_ptr<Fuxi::Common::Executor> _executor1;
    RobotDriver::ABBMessage m_abbMessage;
};