#pragma once

#include <QWidget>
#include <QTextEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QComboBox>
#include <QLabel>
#include <QDateTime>
#include <QFile>
#include <QTextStream>

class LogWidget : public QWidget
{
    Q_OBJECT

public:
    explicit LogWidget(QWidget *parent = nullptr);
    ~LogWidget();

    // 添加日志消息
    void addLogMessage(const QString& message, const QString& category = "系统");
    
    // 清除日志
    void clearLog();
    
    // 保存日志到文件
    bool saveLogToFile(const QString& filePath);
    
    // 设置自动保存
    void setAutoSave(bool enable, const QString& filePath = "");

public slots:
    // 过滤日志
    void filterByCategory(const QString& category);
    
    // 保存日志按钮响应
    void onSaveLog();
    
    // 清除日志按钮响应
    void onClearLog();

private:
    // 格式化日志消息
    QString formatLogMessage(const QString& message, const QString& category);
    
    // 添加日志到文件
    void appendToFile(const QString& formattedMessage);

    QTextEdit* logTextEdit;
    QPushButton* btnClear;
    QPushButton* btnSave;
    QComboBox* categoryFilter;
    QLabel* lblFilter;
    
    QVBoxLayout* mainLayout;
    QHBoxLayout* controlLayout;
    
    // 日志存储
    QStringList logMessages;
    QStringList logCategories;
    
    // 自动保存设置
    bool autoSave;
    QString autoSaveFilePath;
}; 