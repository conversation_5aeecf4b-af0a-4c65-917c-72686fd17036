#pragma once

#include <QString>

namespace AppStyle {


const QString MainStyleSheet = R"(

    QWidget {
        background-color: #f5f7fa;
        color: #2c3e50;
        font-family: "Microsoft YaHei", "微软雅黑", "SimHei", "宋体", sans-serif;
        font-size: 11px;
    }
    

    QMainWindow {
        background-color: #ffffff;
        border: 1px solid #e1e4e8;
    }
    

    QPushButton {
        background-color: #2980b9;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 12px;
        font-weight: bold;
        font-size: 16px;
    }
    
    QPushButton:hover {
        background-color: #3498db;
    }
    
    QPushButton:pressed {
        background-color: #16527a;
        padding: 6px 10px 2px 14px;
        color: #e6e6e6;
        transform: scale(0.98);
    }

    QPushButton:focus {
        background-color: #2573a7;
        outline: none;
    }
    
    QPushButton:checked {
        background-color: #16a085;
    }
    
    QPushButton:disabled {
        background-color: #bdc3c7;
        color: #7f8c8d;
    }
    

    QPushButton#btnStart {
        background-color: #27ae60;
        color: white;
        border: none;
        padding: 5px 13px;
        font-size: 16px;
        min-height: 45px;

    }
    
    QPushButton#btnStart:hover {
        background-color: #2ecc71;
    }
    
    QPushButton#btnStart:pressed {
        background-color: #186d3d;
        padding: 7px 11px 3px 15px;
        color: #e6e6e6;
        transform: scale(0.96);
    }

    QPushButton#btnStart:disabled {
        background-color: #7f8c8d;
    }
    
    QPushButton#btnStop {
        background-color: #c0392b;
        color: white;
        border: none;
        padding: 5px 13px;
        font-size: 16px;
        min-height: 45px;
    }
    
    QPushButton#btnStop:hover {
        background-color: #e74c3c;
    }
    
    QPushButton#btnStop:pressed {
        background-color: #7e261c;
        padding: 7px 11px 3px 15px;
        color: #e6e6e6;
        transform: scale(0.96);
    }

    QPushButton#btnStop:disabled {
        background-color: #7f8c8d;
    }
    

    QLabel {
        color: #34495e;
        font-size: 11px;
    }
    
    QLabel[cssClass="title"] {
        color: #2980b9;
        font-size: 14px;
        font-weight: bold;
    }
    
    QLabel[cssClass="subtitle"] {
        color: #3498db;
        font-size: 12px;
        font-weight: bold;
    }
    
    QLabel[cssClass="status"] {
        color: #7f8c8d;
        font-size: 10px;
    }
    

    QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
        background-color: white;
        border: 1px solid #bdc3c7;
        border-radius: 3px;
        padding: 2px;
        color: #2c3e50;
        font-size: 11px;
    }
    
    QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
        border: 1px solid #3498db;
    }
    

    QTableView {
        background-color: white;
        border: 1px solid #e1e4e8;
        gridline-color: #ecf0f1;
        selection-background-color: #3498db;
        selection-color: white;
        font-size: 11px;
    }
    
    QTableView::item {
        padding: 3px;
    }
    
    QTableView::item:selected {
        background-color: #3498db;
    }
    
    QHeaderView::section {
        background-color: #ecf0f1;
        color: #2c3e50;
        padding: 4px;
        border: 1px solid #d3d7cf;
        font-weight: bold;
        font-size: 11px;
    }
    

    QListWidget {
        background-color: white;
        border: 1px solid #e1e4e8;
        border-radius: 3px;
        font-size: 11px;
    }
    
    QListWidget::item {
        padding: 6px;
        border-bottom: 1px solid #ecf0f1;
    }
    
    QListWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    

    QGroupBox {
        border: 1px solid #e1e4e8;
        border-radius: 5px;
        margin-top: 1.2ex;
        padding-top: 8px;
        font-weight: bold;
        color: #2980b9;
        background-color: white;
        font-size: 16px;
    }
    
    QGroupBox::title {
        font-size: 16px;
        subcontrol-origin: margin;
        subcontrol-position: top center;
        padding: 0 4px;
        background-color: white;
    }
    

    QTabWidget::pane {
        border: 1px solid #e1e4e8;
        border-radius: 3px;
        background-color: white;
    }
    
    QTabBar::tab {
        background-color: #ecf0f1;
        color: #34495e;
        border: 1px solid #d3d7cf;
        border-bottom: none;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        padding: 4px 8px;
        font-size: 11px;
    }
    
    QTabBar::tab:selected {
        background-color: white;
        color: #2980b9;
        border-bottom: 2px solid #2980b9;
    }
    

    QProgressBar {
        border: 1px solid #bdc3c7;
        border-radius: 3px;
        background-color: #ecf0f1;
        text-align: center;
        color: #34495e;
        font-size: 10px;
    }
    
    QProgressBar::chunk {
        background-color: #3498db;
        width: 10px;
    }
    

    QMenuBar {
        background-color: white;
        color: #2c3e50;
        font-size: 11px;
    }
    
    QMenuBar::item {
        background: transparent;
        padding: 4px 8px;
    }
    
    QMenuBar::item:selected {
        background-color: #3498db;
        color: white;
    }
    
    QMenu {
        background-color: white;
        border: 1px solid #e1e4e8;
        font-size: 11px;
    }
    
    QMenu::item {
        padding: 4px 20px 4px 20px;
    }
    
    QMenu::item:selected {
        background-color: #3498db;
        color: white;
    }
    

    QStatusBar {
        background-color: #ecf0f1;
        color: #34495e;
        font-size: 10px;
    }
    

    QScrollBar:vertical {
        border: none;
        background: #ecf0f1;
        width: 10px;
        margin: 0px;
    }
    
    QScrollBar::handle:vertical {
        background: #bdc3c7;
        min-height: 20px;
        border-radius: 5px;
    }
    
    QScrollBar::handle:vertical:hover {
        background: #95a5a6;
    }
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        border: none;
        background: none;
        height: 0px;
    }
    
    QScrollBar:horizontal {
        border: none;
        background: #ecf0f1;
        height: 10px;
        margin: 0px;
    }
    
    QScrollBar::handle:horizontal {
        background: #bdc3c7;
        min-width: 20px;
        border-radius: 5px;
    }
    
    QScrollBar::handle:horizontal:hover {
        background: #95a5a6;
    }
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        border: none;
        background: none;
        width: 0px;
    }
    

    QTextEdit {
        background-color: white;
        border: 1px solid #bdc3c7;
        border-radius: 3px;
        color: #2c3e50;
        font-size: 11px;
    }
    

    QRadioButton, QCheckBox {
        color: #2c3e50;
        spacing: 4px;
        font-size: 11px;
    }
    
    QRadioButton::indicator, QCheckBox::indicator {
        width: 13px;
        height: 13px;
    }
    
    QRadioButton::indicator::checked, QCheckBox::indicator:checked {
        image: url(:/images/check.png);
    }


    QPushButton#btnPause {
        background-color: #f39c12;
        color: white;
        border: none;
        padding: 5px 13px;
        font-size: 16px;
        min-height: 45px;
    }
    
    QPushButton#btnPause:hover {
        background-color: #f1c40f;
    }
    
    QPushButton#btnPause:pressed {
        background-color: #9c640c;
        padding: 7px 11px 3px 15px;
        color: #e6e6e6;
        transform: scale(0.96);
    }

    QPushButton#btnPause:disabled {
        background-color: #7f8c8d;
    }


    QPushButton#btnContinue {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 5px 13px;
        font-size: 16px;
        min-height: 45px;
    }
    
    QPushButton#btnContinue:hover {
        background-color: #2e86c1;
    }
    
    QPushButton#btnContinue:pressed {
        background-color: #154360;
        padding: 7px 11px 3px 15px;
        color: #e6e6e6;
        transform: scale(0.96);
    }

    QPushButton#btnContinue:disabled {
        background-color: #7f8c8d;
    }


    QPushButton#btnReset {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 5px 13px;
        font-size: 16px;
        min-height: 45px;
    }
    
    QPushButton#btnReset:hover {
        background-color: #5dade2;
    }
    
    QPushButton#btnReset:pressed {
        background-color: #21618c;
        padding: 7px 11px 3px 15px;
        color: #e6e6e6;
        transform: scale(0.96);
    }

    QPushButton#btnReset:disabled {
        background-color: #7f8c8d;
    }


    QPushButton#btnExit {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 5px 13px;
        font-size: 16px;
        min-height: 45px;
    }
    
    QPushButton#btnExit:hover {
        background-color: #2e86c1;
    }
    
    QPushButton#btnExit:pressed {
        background-color: #154360;
        padding: 7px 11px 3x 15px;
        color: #e6e6e6;
        transform: scale(0.96);
    }

    QPushButton#btnExit:disabled {
        background-color: #7f8c8d;
    }

    QPushButton[cssClass="functionButton"] {
        border: none;
        border-radius: 5px;
        font-weight: bold;
        min-height: 30px;
    }
    
    QPushButton[cssClass="functionButton"]:hover {
        background-color: #3498db;
        color: white;
    }
    
    QPushButton[cssClass="functionButton"]:pressed {
        background-color: #1c2833;
        color: #e6e6e6;
        padding: 7px 10px 3px 14px;
        transform: scale(0.96);
    }
)";


const QString StatusIndicatorActive = "background-color: #27ae60; border-radius: 12px; min-width: 24px; min-height: 24px;";
const QString StatusIndicatorInactive = "background-color: #c0392b; border-radius: 12px; min-width: 24px; min-height: 24px;";
const QString StatusIndicatorWarning = "background-color: #d68910; border-radius: 12px; min-width: 24px; min-height: 24px;";

const QString LogCategorySystem = "#3498db";
const QString LogCategoryError = "#e74c3c";
const QString LogCategoryWarning = "#f39c12";
const QString LogCategoryABB = "#27ae60";
const QString LogCategoryLaser = "#8e44ad";
const QString LogCategoryRFID = "#d35400";

} // namespace AppStyle