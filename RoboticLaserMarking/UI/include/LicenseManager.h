#pragma once

#include <QString>
#include <QDateTime>
#include <QSettings>
#include <QCryptographicHash>
#include <QSysInfo>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QDebug>
#include <QNetworkInterface>
#include <QStringList>

class LicenseManager
{
public:
    LicenseManager();
    ~LicenseManager();

    // 检查当前MAC是否在白名单中
    bool isAuthorized() const;
    
    // 检查当前是否已注册（兼容性保持）
    bool isRegistered() const;
    
    // 获取许可证到期日期（兼容性保持）
    QDateTime getExpirationDate() const;
    
    // 获取错误信息
    QString getLastError() const;
    
    // 获取当前机器的MAC地址列表
    QStringList getMACAddresses() const;
    
    // 获取机器码（用于显示）
    QString getMachineCode() const;
    
    // 获取试用天数
    int getTrialDaysLeft() const;
    
    // 试用模式是否可用
    bool isTrialAvailable() const;

private:
    // 生成机器码 (基于MAC地址)
    QString generateMachineCode() const;
    
    // 获取所有网络接口MAC地址
    QStringList getAllMACAddresses() const;
    
    // 获取硬编码的MAC白名单
    QStringList getHardcodedMACWhitelist() const;
    
    // 检查MAC地址是否在白名单中
    bool isMACInWhitelist(const QString& macAddress) const;
    
    // 加密函数（保留用于其他用途）
    QString encrypt(const QString& text, const QString& key) const;
    
    // 解密函数（保留用于其他用途）
    QString decrypt(const QString& text, const QString& key) const;
    
    // 成员变量
    QString m_lastError;
    
    // 常量
    const QString SALT = "FuxiRoboticLaser2025";
    const int TRIAL_DAYS = 30;
};