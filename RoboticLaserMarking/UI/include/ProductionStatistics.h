#pragma once

#include <QString>
#include <QDateTime>
#include <QVector>
#include <QMap>
#include <QJsonObject>
#include <QJsonDocument>
#include <QJsonArray>

// Production status enumeration
enum class ProductionStatus {
    SUCCESS = 0,        // Successful completion
    FAILED = 1,         // Failed
    INTERRUPTED = 2,    // Interrupted
    TIMEOUT = 3         // Timeout
};

// Production record structure
struct ProductionRecord {
    QString recordId;           // Unique record ID
    QDateTime startTime;        // Start time
    QDateTime endTime;          // End time
    QString rfidCode;           // RFID code
    QString templateName;       // Template name
    int shoeCode;               // Shoe size
    bool isLeftFoot;            // Is left foot
    int totalFaces;             // Total faces to process
    int completedFaces;         // Completed faces
    ProductionStatus status;    // Production status
    qint64 processingTimeMs;    // Total processing time in milliseconds
    qint64 laserTimeMs;         // Actual laser working time in milliseconds
    QString operatorId;         // Operator ID (if applicable)
    QString errorMessage;       // Error message (if any)
    QMap<QString, QVariant> customData; // Custom data fields
    
    // Default constructor
    ProductionRecord() 
        : shoeCode(0)
        , isLeftFoot(false)
        , totalFaces(0)
        , completedFaces(0)
        , status(ProductionStatus::SUCCESS)
        , processingTimeMs(0)
        , laserTimeMs(0) {}
    
    // Calculate efficiency (faces per minute)
    double getEfficiency() const {
        if (processingTimeMs <= 0) return 0.0;
        return (completedFaces * 60000.0) / processingTimeMs;
    }
    
    // Get success rate
    double getSuccessRate() const {
        if (totalFaces <= 0) return 0.0;
        return (double)completedFaces / totalFaces;
    }
    
    // Check if record is complete
    bool isComplete() const {
        return completedFaces >= totalFaces && status == ProductionStatus::SUCCESS;
    }
    
    // Convert to JSON
    QJsonObject toJson() const;
    
    // Create from JSON
    static ProductionRecord fromJson(const QJsonObject& json);
};

// Statistics summary structure
struct ProductionSummary {
    QDateTime periodStart;      // Period start time
    QDateTime periodEnd;        // Period end time
    int totalRecords;           // Total records
    int successfulRecords;      // Successful records
    int failedRecords;          // Failed records
    int totalFaces;             // Total faces processed
    qint64 totalProcessingTime; // Total processing time
    qint64 totalLaserTime;      // Total laser time
    double averageEfficiency;   // Average efficiency
    double successRate;         // Success rate
    QMap<QString, int> templateUsage; // Template usage statistics
    QMap<int, int> shoeSizeDistribution; // Shoe size distribution
    
    // Default constructor
    ProductionSummary()
        : totalRecords(0)
        , successfulRecords(0)
        , failedRecords(0)
        , totalFaces(0)
        , totalProcessingTime(0)
        , totalLaserTime(0)
        , averageEfficiency(0.0)
        , successRate(0.0) {}
    
    // Calculate utilization rate
    double getUtilizationRate() const {
        if (totalProcessingTime <= 0) return 0.0;
        return (double)totalLaserTime / totalProcessingTime;
    }
    
    // Get average processing time per face
    double getAverageTimePerFace() const {
        if (totalFaces <= 0) return 0.0;
        return (double)totalProcessingTime / totalFaces;
    }
};

// Time period enumeration for statistics
enum class StatisticsPeriod {
    HOUR = 0,
    DAY = 1,
    WEEK = 2,
    MONTH = 3,
    YEAR = 4,
    CUSTOM = 5
};

// Chart type enumeration
enum class ChartType {
    PIE_CHART = 0,          // Pie chart
    LINE_CHART = 1,         // Line chart
    BAR_CHART = 2,          // Bar chart
    AREA_CHART = 3,         // Area chart
    SCATTER_CHART = 4       // Scatter chart
};

// Statistics filter structure
struct StatisticsFilter {
    QDateTime startTime;        // Start time filter
    QDateTime endTime;          // End time filter
    QStringList templateNames;  // Template name filter
    QList<int> shoeCodes;       // Shoe code filter
    QList<ProductionStatus> statusFilter; // Status filter
    bool leftFootOnly;          // Left foot only
    bool rightFootOnly;         // Right foot only
    
    // Default constructor
    StatisticsFilter()
        : leftFootOnly(false)
        , rightFootOnly(false) {}
    
    // Check if filter is empty
    bool isEmpty() const {
        return !startTime.isValid() && !endTime.isValid() && 
               templateNames.isEmpty() && shoeCodes.isEmpty() && 
               statusFilter.isEmpty() && !leftFootOnly && !rightFootOnly;
    }
};

// Trend data point structure
struct TrendDataPoint {
    QDateTime timestamp;
    double value;
    QString label;
    
    TrendDataPoint() : value(0.0) {}
    TrendDataPoint(const QDateTime& time, double val, const QString& lbl = "")
        : timestamp(time), value(val), label(lbl) {}
};

// Trend data series
using TrendDataSeries = QVector<TrendDataPoint>;

// Statistics data for charts
struct ChartData {
    ChartType type;
    QString title;
    QString xAxisLabel;
    QString yAxisLabel;
    QMap<QString, TrendDataSeries> series;
    QMap<QString, QVariant> metadata;
    
    ChartData() : type(ChartType::PIE_CHART) {}
};

// Helper function declarations
QString productionStatusToString(ProductionStatus status);
ProductionStatus stringToProductionStatus(const QString& statusStr);
QString statisticsPeriodToString(StatisticsPeriod period);
QString chartTypeToString(ChartType type);
QString generateRecordId();
qint64 calculateTimeDifference(const QDateTime& start, const QDateTime& end);
QString formatDuration(qint64 milliseconds);
QDateTime getPeriodStartTime(const QDateTime& referenceTime, StatisticsPeriod period);
QDateTime getPeriodEndTime(const QDateTime& referenceTime, StatisticsPeriod period);
qint64 getPeriodSeconds(StatisticsPeriod period);
