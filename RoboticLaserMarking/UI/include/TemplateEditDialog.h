#pragma once

#include <QDialog>
#include <QLineEdit>
#include <QSpinBox>
#include <QCheckBox>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QFileDialog>
#include <QRadioButton>
#include <QButtonGroup>
#include <QComboBox>
#include <QMessageBox>
#include <QHBoxLayout>
#include <QDateTime>
#include <memory>
#include "TemplateManager.h"
#include "RFIDModbusDriver.h"
#include "ABBSocketDriver.h"

class MainWindow; // 前向声明

class TemplateEditDialog : public QDialog
{
    Q_OBJECT

public:
    explicit TemplateEditDialog(std::shared_ptr<TemplateManager> manager, std::shared_ptr<RFIDModbusDriver> rfidDriver, std::shared_ptr<RobotDriver::ABBSocketDriver> abbDriver, MainWindow* mainWindow, QWidget *parent = nullptr);
    explicit TemplateEditDialog(std::shared_ptr<TemplateManager> manager, std::shared_ptr<RFIDModbusDriver> rfidDriver, std::shared_ptr<RobotDriver::ABBSocketDriver> abbDriver, const ShoeTemplate& templ, MainWindow* mainWindow, QWidget *parent = nullptr);
    ~TemplateEditDialog();

    // 获取模板数据
    ShoeTemplate getTemplateData() const;
    
    // 设置现有的模板数据
    void setTemplateData(const ShoeTemplate& templ);
    
    // 验证输入
    bool validateInput();

private slots:
    // 浏览目录
    void onBrowseLaserWorkDir();
    
    // 确认按钮
    void onAccept();
    
    // 取消按钮
    void onCancel();
    
    // 左右脚选择变化
    void onFootSideChanged();
    
    // 鞋码变化
    void onShoeCodeChanged(int value);
    
    // RFID变化
    void onRFIDChanged(const QString& text);
    
    // 读取RFID按钮点击
    void onReadRFID();
    
    // 创建默认工作目录
    void onCreateDefaultWorkDir();
    
    // 机器人手动控制槽函数
    void onMoveToFace();
    void onMoveToPreviousFace();
    void onMoveToNextFace();
    void onCompleteOperation();
    void onLaserReady();  // 激光准备好槽函数

private:
    // 初始化UI
    void setupUI();
    
    // 更新UI状态
    void updateUIState();
    
    // 加载预设模板名称
    void loadTemplatePresets();
    
    // 解析RFID编码
    /**
     * @brief 解析RFID编码并自动填充相关字段
     * @param rfidCode RFID编码字符串
     * @return 解析是否成功
     */
    bool parseRFIDCode(const QString& rfidCode);

    // 输入控件
    QLineEdit* edtRFID;
    QSpinBox* spnShoeCode;
    QRadioButton* radLeft;
    QRadioButton* radRight;
    QButtonGroup* footSideGroup;
    QLineEdit* edtTemplateName;
    QLineEdit* edtLaserWorkDir;
    
    // 按钮
    QPushButton* btnBrowseLaserWorkDir;
    QPushButton* btnReadRFID;
    QPushButton* btnCreateDefaultWorkDir;
    QPushButton* btnOK;
    QPushButton* btnCancel;
    
    // 机器人手动控制按钮
    QPushButton* btnMoveToFace;
    QPushButton* btnMoveToPreviousFace;
    QPushButton* btnMoveToNextFace;
    QPushButton* btnCompleteOperation;
    QPushButton* btnLaserReady;  // 激光准备好按钮
    QSpinBox* spnTargetFace;
    
    // 预设下拉菜单
    QLineEdit* cmbTemplatePresets;
    QLabel* lblTemplatePresets;
    
    // 布局
    QGridLayout* mainLayout;
    
    // 管理器和驱动
    std::shared_ptr<TemplateManager> templateManager;
    std::shared_ptr<RFIDModbusDriver> rfidDriver;
    std::shared_ptr<RobotDriver::ABBSocketDriver> abbDriver;
    MainWindow* mainWindow;  // MainWindow指针，用于访问RFID队列
    
    // 是否是编辑模式
    bool isEditMode;
    
    // 原始模板数据(编辑模式)
    ShoeTemplate originalTemplate;
    
    // 原始RFID
    QString originalRFID;
};