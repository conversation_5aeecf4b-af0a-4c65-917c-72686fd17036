#pragma once

#include <QDialog>
#include <QLineEdit>
#include <QSpinBox>
#include <QCheckBox>
#include <QPushButton>
#include <QLabel>
#include <QGridLayout>
#include <QGroupBox>
#include <QTabWidget>
#include <QFileDialog>
#include <QDir>
#include <QMessageBox>
#include <QComboBox>
#include <QHBoxLayout>
#include <QFrame>


// 系统设置结构体定义
struct SystemSettings {
    // ABB机器人设置
    int abbPort;
    
    // RFID设置
    QString rfidIP;
    int rfidPort;
    
    // 打粗设置
    QString laserWorkDir;
    
    // 系统设置
    bool autoConnect;
    QString logLevel;
    
    // 鞋子路径编辑器设置
    QString shoePathEditorPath;
};

class SystemSettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SystemSettingsDialog(QWidget *parent = nullptr);
    ~SystemSettingsDialog();

    // 获取设备设置
    QString getABBServerPort() const;
    QString getRFIDIPAddress() const;
    int getRFIDPort() const;
    
    // 获取文件路径设置
    QString getTemplateCSVPath() const;
    QString getLogDirectory() const;
    QString getLaserWorkDirectory() const;
    QString getShoePathEditorPath() const;
    
    // 获取系统设置
    bool getAutoConnectDevices() const;
    bool getAutoSaveLog() const;
    int getStatusUpdateInterval() const;
    
    // 应用设置
    void applySettings();
    
    // 加载配置
    bool loadConfig(const QString& filePath);
    
    // 保存配置
    bool saveConfig(const QString& filePath);
    
    // 设置设备配置
    void setABBServerPort(const QString& port);
    void setRFIDIPAddress(const QString& ipAddress);
    void setRFIDPort(int port);
    
    // 设置文件路径
    void setTemplateCSVPath(const QString& path);
    void setLogDirectory(const QString& dir);
    void setLaserWorkDirectory(const QString& dir);
    void setShoePathEditorPath(const QString& path);
    
    // 设置系统选项
    void setAutoConnectDevices(bool enable);
    void setAutoSaveLog(bool enable);
    void setStatusUpdateInterval(int intervalMs);
    
    // 设置当前使用的配置文件路径
    void setConfigFilePath(const QString& filePath);
    
    // 添加获取/设置完整设置的方法
    SystemSettings getSettings() const;
    void setSettings(const SystemSettings& settings);

private slots:
    // 浏览文件夹
    void onBrowseTemplateCSV();
    void onBrowseLogDirectory();
    void onBrowseLaserWorkDirectory();
    void onBrowseShoePathEditor();
    
    // 保存配置按钮
    void onSaveConfig();
    
    // 加载配置按钮
    void onLoadConfig();
    
    // 确定按钮
    void onAccept();
    
    // 取消按钮
    void onCancel();
    
    // 恢复默认设置
    void onRestoreDefaults();

private:
    // 初始化UI
    void setupUI();
    
    // 创建设备设置选项卡
    QWidget* createDeviceTab();
    
    // 创建文件路径选项卡
    QWidget* createPathsTab();
    
    // 创建系统选项卡
    QWidget* createSystemTab();
    
    // 加载默认设置
    void loadDefaultSettings();

    // 选项卡控件
    QTabWidget* tabWidget;
    
    // 设备设置控件
    QSpinBox* spnABBServerPort;
    QLineEdit* edtRFIDIPAddress;
    QSpinBox* spnRFIDPort;
    
    // 文件路径控件
    QLineEdit* edtTemplateCSVPath;
    QLineEdit* edtLogDirectory;
    QLineEdit* edtLaserWorkDirectory;
    QLineEdit* edtShoePathEditorPath;
    
    // 浏览按钮
    QPushButton* btnBrowseTemplateCSV;
    QPushButton* btnBrowseLogDirectory;
    QPushButton* btnBrowseLaserWorkDirectory;
    QPushButton* btnBrowseShoePathEditor;
    
    // 系统设置控件
    QCheckBox* chkAutoConnectDevices;
    QCheckBox* chkAutoSaveLog;
    QSpinBox* spnStatusUpdateInterval;
    QComboBox* cmbTheme;
    
    // 按钮
    QPushButton* btnSaveConfig;
    QPushButton* btnLoadConfig;
    QPushButton* btnRestoreDefaults;
    QPushButton* btnOK;
    QPushButton* btnCancel;
    
    // 布局
    QGridLayout* mainLayout;
    
    // 当前配置文件路径
    QString configFilePath;
};