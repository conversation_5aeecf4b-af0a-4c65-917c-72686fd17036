#pragma once

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>
#include <QVector>
#include <memory>
#include <QSet>
#include <QVariant>

#include "ProductionStatistics.h"

class ProductionStatisticsManager;

/**
 * Worker thread for database operations to prevent UI blocking
 */
class DatabaseQueryWorker : public QObject
{
    Q_OBJECT

public:
    enum class QueryType {
        GET_RECORDS,
        GET_SUMMARY,
        GET_RECORD_BY_ID,
        START_RECORD,
        UPDATE_RECORD,
        FINISH_RECORD,
        ADD_CUSTOM_DATA,
        SAVE_RECORD_TO_DB
    };

    struct QueryRequest {
        QueryType type;
        StatisticsFilter filter;
        QString recordId;
        int requestId;

        // For write operations
        ProductionRecord record;
        QString rfidCode;
        QString templateName;
        int shoeCode;
        bool isLeftFoot;
        int totalFaces;
        int completedFaces;
        ProductionStatus status;
        QString errorMessage;
        QString customDataKey;
        QVariant customDataValue;
    };

    explicit DatabaseQueryWorker(std::shared_ptr<ProductionStatisticsManager> statsManager, QObject *parent = nullptr);
    ~DatabaseQueryWorker();

    // Request database operations (read)
    int requestRecords(const StatisticsFilter& filter);
    int requestSummary(const StatisticsFilter& filter);
    int requestRecord(const QString& recordId);

    // Request database operations (write)
    int requestStartRecord(const QString& rfidCode, const QString& templateName,
                          int shoeCode, bool isLeftFoot, int totalFaces);
    int requestUpdateRecord(const QString& recordId, int completedFaces,
                           ProductionStatus status = ProductionStatus::SUCCESS,
                           const QString& errorMessage = "");
    int requestFinishRecord(const QString& recordId,
                           ProductionStatus status = ProductionStatus::SUCCESS,
                           const QString& errorMessage = "");
    int requestAddCustomData(const QString& recordId, const QString& key, const QVariant& value);
    int requestSaveRecord(const ProductionRecord& record);

    // Cancel operations
    void cancelRequest(int requestId);
    void cancelAllRequests();

public slots:
    void processQueries();
    void shutdown();

signals:
    // Read operation results
    void recordsReady(int requestId, const QVector<ProductionRecord>& records);
    void summaryReady(int requestId, const ProductionSummary& summary);
    void recordReady(int requestId, const ProductionRecord& record);

    // Write operation results
    void recordStarted(int requestId, const QString& recordId);
    void recordUpdated(int requestId, bool success);
    void recordFinished(int requestId, bool success);
    void customDataAdded(int requestId, bool success);
    void recordSaved(int requestId, bool success);

    // Error handling
    void queryError(int requestId, const QString& error);
    void queryTimeout(int requestId);

    // Progress
    void queryStarted(int requestId);
    void queryProgress(int requestId, int percentage);

private slots:
    void handleQuery(const QueryRequest& request);

private:
    // Core functionality (read operations)
    void processRecordsQuery(const QueryRequest& request);
    void processSummaryQuery(const QueryRequest& request);
    void processRecordQuery(const QueryRequest& request);

    // Core functionality (write operations)
    void processStartRecordQuery(const QueryRequest& request);
    void processUpdateRecordQuery(const QueryRequest& request);
    void processFinishRecordQuery(const QueryRequest& request);
    void processAddCustomDataQuery(const QueryRequest& request);
    void processSaveRecordQuery(const QueryRequest& request);

    // Helper methods
    bool isRequestCancelled(int requestId) const;
    void emitError(int requestId, const QString& error);
    
    // Data members
    std::shared_ptr<ProductionStatisticsManager> m_statsManager;
    
    // Thread synchronization
    mutable QMutex m_requestMutex;
    QWaitCondition m_requestCondition;
    QVector<QueryRequest> m_pendingRequests;
    QSet<int> m_cancelledRequests;
    
    // State management
    bool m_shutdown;
    int m_nextRequestId;
    
    // Performance settings
    static const int MAX_QUERY_TIME_MS = 15000;  // 15 seconds
    static const int BATCH_SIZE = 100;           // Process records in batches
};

/**
 * Thread manager for database operations
 */
class DatabaseQueryThread : public QThread
{
    Q_OBJECT

public:
    explicit DatabaseQueryThread(std::shared_ptr<ProductionStatisticsManager> statsManager, QObject *parent = nullptr);
    ~DatabaseQueryThread();

    // Get the worker instance
    DatabaseQueryWorker* worker() const { return m_worker; }

    // Thread control
    void startWorker();
    void stopWorker();

signals:
    void workerReady();
    void workerError(const QString& error);

protected:
    void run() override;

private:
    std::shared_ptr<ProductionStatisticsManager> m_statsManager;
    DatabaseQueryWorker* m_worker;
    bool m_running;
};
