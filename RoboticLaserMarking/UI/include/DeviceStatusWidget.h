#pragma once

#include <QWidget>
#include <QGridLayout>
#include <QLabel>
#include <QProgressBar>
#include <QGroupBox>
#include <QFrame>
#include <QTimer>

class DeviceStatusWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DeviceStatusWidget(QWidget *parent = nullptr);
    ~DeviceStatusWidget();

    // 设置设备连接状态
    void setABBConnected(bool connected);
    void setLaserConnected(bool connected);
    void setRFIDConnected(bool connected);
    
    // 设置激光状态
    void setLaserError(bool hasError, const QString& errorMsg);
    
    // 设置激光工作状态
    void setLaserWorkingStatus(bool isWorking, const QString& workingState = "");
    
    // 设置ABB状态
    void setABBPosition(const QString& position);
    void setABBStatus(const QString& status);
    
    // 设置RFID状态
    void setRFIDStatus(bool isReading, const QString& lastTag);
    
    // 设置当前作业信息
    void setCurrentRFID(const QString& rfidId);
    void setCurrentTemplate(const QString& templateName);
    void setCurrentShoeInfo(int shoeCode, bool isLeft);
    

    
    // 设置面进度信息
    void setFaceProgress(int currentFace, int totalFaces, int completedFaces);
    
    // 设置作业时间
    void setMarkTime(uint64_t timeMs);
    
    // 设置作业计数
    void setMarkCount(int count);
    
    // 设置系统状态
    void setSystemState(const QString& state);
    void setSystemRunning(bool running);
    void setSystemPaused(bool paused);
    void setRunTime(const QString& timeText);
    


public slots:
    // 更新界面
    void updateUI();

private:
    // 创建状态指示器
    QFrame* createStatusIndicator();
    
    // 更新状态指示器
    void updateStatusIndicator(QFrame* indicator, bool active);

    // 布局
    QGridLayout* mainLayout;
    
    // 状态指示器
    QFrame* abbStatusIndicator;
    QFrame* laserStatusIndicator;
    QFrame* rfidStatusIndicator;
    QFrame* systemStatusIndicator;
    
    // 标签
    QLabel* lblABBStatus;
    QLabel* lblLaserStatus;
    QLabel* lblRFIDStatus;
    QLabel* lblSystemState;
    QLabel* lblCurrentRFID;
    QLabel* lblCurrentTemplate;
    QLabel* lblCurrentShoeInfo;
    QLabel* lblCurrentFace;
    QLabel* lblFaceProgress;
    QLabel* lblMarkTime;
    QLabel* lblMarkCount;
    QLabel* lblLaserWorkingStatus;
    

    
    // 分组框
    QGroupBox* deviceGroup;
    QGroupBox* jobGroup;
    QGroupBox* statisticsGroup;
    
    // 移除重复的状态数据成员变量，改为纯显示组件
    // 所有状态数据由MainWindow统一管理，通过接口方法直接更新显示
    
    // 定时器
    QTimer* blinkTimer;
    bool blinkState;
};