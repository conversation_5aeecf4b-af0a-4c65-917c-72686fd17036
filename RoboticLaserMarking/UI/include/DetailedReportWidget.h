#pragma once

#include "ProductionStatistics.h"
#include "ProductionStatisticsManager.h"
#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QTableWidget>
#include <QComboBox>
#include <QDateTimeEdit>
#include <QPushButton>
#include <QLabel>
#include <QTextEdit>
#include <QSplitter>
#include <QCheckBox>
#include <QSpinBox>
#include <QSlider>
#include <QProgressDialog>
#include <QTimer>
#include <memory>

// Forward declarations
class ProductionStatisticsManager;
class DatabaseQueryThread;
class DatabaseQueryWorker;
struct ProductionRecord;
struct ProductionSummary;
struct StatisticsFilter;

/**
 * @brief 详细报表组件 - 可嵌入式的报表查询和显示组件
 * 
 * 这个类提供了完整的生产记录查询、过滤和显示功能，
 * 可以作为独立的Widget嵌入到任何界面中
 */
class DetailedReportWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DetailedReportWidget(std::shared_ptr<ProductionStatisticsManager> statsManager,
                                QWidget *parent = nullptr);
    ~DetailedReportWidget();
    
    // Public interface
    void queryData();
    void populateTableAsync(const QVector<ProductionRecord>& records, int startIndex);
    void refreshData(); // 刷新数据的便捷方法

public slots:
    void onQueryClicked();
    void onFilterChanged();
    void onRefreshClicked();

    // Database query result handlers
    void onRecordsReady(int requestId, const QVector<ProductionRecord>& records);
    void onQueryError(int requestId, const QString& error);
    void onQueryTimeout(int requestId);
    void onQueryStarted(int requestId);

    // Database thread handlers
    void onWorkerReady();
    void onWorkerError(const QString& error);

signals:
    // 信号用于通知外部组件
    void dataLoaded(int recordCount);
    void queryStarted();
    void queryFinished();
    void errorOccurred(const QString& error);

private:
    void setupUI();
    void createFilterPanel();
    void createDataPanel();
    void createControlPanel();

    // Data processing functions
    QVector<ProductionRecord> getFilteredRecords() const;

    // Database query management
    void initializeQueryThread();
    
    // Helper functions
    StatisticsFilter getCurrentFilter() const;
    QString formatDuration(qint64 milliseconds) const;
    QString formatDateTime(const QDateTime& dateTime) const;

private:
    // Core components
    std::shared_ptr<ProductionStatisticsManager> m_statsManager;
    
    // UI components
    QVBoxLayout* m_mainLayout;
    QSplitter* m_mainSplitter;

    // Filter panel
    QGroupBox* m_filterPanel;
    QDateTimeEdit* m_startTimeEdit;
    QDateTimeEdit* m_endTimeEdit;
    QComboBox* m_templateCombo;
    QComboBox* m_statusCombo;
    QCheckBox* m_showErrorsOnlyCheck;

    // Data panel
    QWidget* m_dataPanel;
    QTableWidget* m_dataTable;
    QLabel* m_recordCountLabel;

    // Control panel
    QWidget* m_controlPanel;
    QPushButton* m_queryButton;
    QPushButton* m_refreshButton;

    // Current state
    StatisticsFilter m_currentFilter;
    QVector<ProductionRecord> m_currentRecords;

    // Control flags
    bool m_isInitializing;
    bool m_isQuerying;

    // Database query thread
    DatabaseQueryThread* m_queryThread;
    DatabaseQueryWorker* m_queryWorker;
    int m_currentRecordsRequestId;
    bool m_waitingForRecords;
};
