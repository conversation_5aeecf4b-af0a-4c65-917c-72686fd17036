#pragma once

#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QFont>
#include <QGroupBox>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QGridLayout>
#include <QComboBox>
#include <QDateTimeEdit>
#include <QCheckBox>
#include <QTableWidget>
#include <QMessageBox>
#include <QString>
#include <QStringList>
#include <functional>

/**
 * @brief UI工具类 - 提供通用的UI创建和样式设置功能
 * 
 * 这个类包含了常用的UI组件创建方法，统一样式设置，
 * 减少各个UI类中的重复代码
 */
class UIUtils
{
public:
    // 按钮样式枚举
    enum class ButtonStyle {
        Primary,    // 主要按钮 - 蓝色
        Success,    // 成功按钮 - 绿色
        Warning,    // 警告按钮 - 橙色
        Danger,     // 危险按钮 - 红色
        Info,       // 信息按钮 - 青色
        Secondary,  // 次要按钮 - 灰色
        Function    // 功能按钮 - 使用CSS类
    };

    // 字体大小枚举
    enum class FontSize {
        Small = 12,
        Medium = 14,
        Large = 16,
        XLarge = 18
    };

    // 按钮创建方法
    static QPushButton* createButton(const QString& text, 
                                   ButtonStyle style = ButtonStyle::Primary,
                                   FontSize fontSize = FontSize::Medium,
                                   QWidget* parent = nullptr);

    static QPushButton* createStyledButton(const QString& text,
                                         const QString& backgroundColor,
                                         const QString& textColor = "white",
                                         FontSize fontSize = FontSize::Medium,
                                         QWidget* parent = nullptr);

    // 标签创建方法
    static QLabel* createLabel(const QString& text,
                             FontSize fontSize = FontSize::Medium,
                             bool bold = false,
                             QWidget* parent = nullptr);

    static QLabel* createStyledLabel(const QString& text,
                                   const QString& color = "#2c3e50",
                                   FontSize fontSize = FontSize::Medium,
                                   bool bold = false,
                                   QWidget* parent = nullptr);

    // 组框创建方法
    static QGroupBox* createGroupBox(const QString& title,
                                   FontSize fontSize = FontSize::Medium,
                                   bool bold = true,
                                   QWidget* parent = nullptr);

    // 布局创建方法
    static QHBoxLayout* createHBoxLayout(int spacing = 5, int margin = 5);
    static QVBoxLayout* createVBoxLayout(int spacing = 5, int margin = 5);
    static QGridLayout* createGridLayout(int spacing = 5, int margin = 5);

    // 复选框样式枚举
    enum class CheckBoxStyle {
        Default,    // 默认样式
        Custom,     // 自定义样式（绿色主题）
        Toggle,     // 切换开关样式
        Radio       // 单选按钮样式
    };

    // 输入控件创建方法
    static QComboBox* createComboBox(const QStringList& items = QStringList(),
                                   FontSize fontSize = FontSize::Medium,
                                   QWidget* parent = nullptr);

    static QDateTimeEdit* createDateTimeEdit(const QDateTime& dateTime = QDateTime::currentDateTime(),
                                            FontSize fontSize = FontSize::Medium,
                                            QWidget* parent = nullptr);

    static QCheckBox* createCheckBox(const QString& text,
                                   FontSize fontSize = FontSize::Medium,
                                   QWidget* parent = nullptr);

    static QCheckBox* createStyledCheckBox(const QString& text,
                                         CheckBoxStyle style = CheckBoxStyle::Custom,
                                         FontSize fontSize = FontSize::Medium,
                                         QWidget* parent = nullptr);

    // 表格创建方法
    static QTableWidget* createTableWidget(const QStringList& headers,
                                         FontSize fontSize = FontSize::Medium,
                                         QWidget* parent = nullptr);

    // 字体工具方法
    static QFont createFont(FontSize fontSize = FontSize::Medium, bool bold = false);
    static QFont createFont(int size, bool bold = false);

    // 样式字符串生成方法
    static QString getButtonStyleSheet(ButtonStyle style);
    static QString getButtonStyleSheet(const QString& backgroundColor,
                                     const QString& textColor = "white");
    static QString getCheckBoxStyleSheet(CheckBoxStyle style);

    // 消息框工具方法
    static void showInfoMessage(QWidget* parent, const QString& title, const QString& message);
    static void showWarningMessage(QWidget* parent, const QString& title, const QString& message);
    static void showErrorMessage(QWidget* parent, const QString& title, const QString& message);
    static bool showConfirmDialog(QWidget* parent, const QString& title, const QString& message);

    // 信号连接工具方法 - 简化版本，直接使用QObject::connect即可
    // 这些模板函数主要用于保持代码风格一致性，实际使用中可以直接用connect

    // 批量设置控件启用状态
    static void setWidgetsEnabled(const QList<QWidget*>& widgets, bool enabled);

    // 批量设置按钮样式
    static void setButtonsStyle(const QList<QPushButton*>& buttons, ButtonStyle style);

    // 常用颜色常量
    static const QString COLOR_PRIMARY;
    static const QString COLOR_SUCCESS;
    static const QString COLOR_WARNING;
    static const QString COLOR_DANGER;
    static const QString COLOR_INFO;
    static const QString COLOR_SECONDARY;
    static const QString COLOR_LIGHT;
    static const QString COLOR_DARK;

private:
    UIUtils() = default; // 工具类，不允许实例化
};

/**
 * @brief 错误处理工具类
 */
class ErrorHandler
{
public:
    // 错误处理函数类型
    using ErrorCallback = std::function<void(const QString&)>;

    // 设置全局错误处理回调
    static void setGlobalErrorHandler(ErrorCallback callback);

    // 处理错误并显示消息
    static void handleError(QWidget* parent, const QString& title, const QString& message);

    // 处理激光控制错误（通用模式）
    template<typename LaserControl>
    static bool handleLaserError(LaserControl* laserControl, QWidget* parent,
                               const QString& operation, ErrorCallback logCallback = nullptr) {
        if (!laserControl) {
            UIUtils::showWarningMessage(parent, "设备错误", operation + "失败：激光控制器未初始化");
            if (logCallback) logCallback(operation + "失败：激光控制器未初始化");
            return false;
        }

        if (!laserControl->IsConnected()) {
            UIUtils::showWarningMessage(parent, "设备错误", operation + "失败：激光设备未连接");
            if (logCallback) logCallback(operation + "失败：激光设备未连接");
            return false;
        }

        return true;
    }

private:
    static ErrorCallback s_globalErrorHandler;
};

/**
 * @brief 日志工具类
 */
class LogUtils
{
public:
    // 日志级别
    enum class LogLevel {
        Debug,
        Info,
        Warning,
        Error
    };

    // 日志回调函数类型
    using LogCallback = std::function<void(const QString&, const QString&)>;

    // 设置日志回调
    static void setLogCallback(LogCallback callback);

    // 记录日志
    static void log(const QString& message, const QString& category = "系统", LogLevel level = LogLevel::Info);
    static void logDebug(const QString& message, const QString& category = "调试");
    static void logInfo(const QString& message, const QString& category = "信息");
    static void logWarning(const QString& message, const QString& category = "警告");
    static void logError(const QString& message, const QString& category = "错误");

private:
    static LogCallback s_logCallback;
};

/**
 * @brief 数据库查询工具类
 */
class DatabaseUtils
{
public:
    // 查询状态枚举
    enum class QueryState {
        Idle,
        Running,
        Success,
        Error,
        Timeout
    };

    // 查询结果回调函数类型
    template<typename T>
    using QueryCallback = std::function<void(QueryState state, const QVector<T>& results, const QString& error)>;

    // 通用查询状态管理
    static void setQueryState(QPushButton* queryButton, QPushButton* refreshButton,
                            QLabel* statusLabel, QueryState state, const QString& message = "");

    // 通用元类型注册
    template<typename T>
    static void registerMetaType(const QString& typeName) {
        qRegisterMetaType<T>(typeName.toStdString().c_str());
    }

    // 通用信号连接（用于数据库查询）
    template<typename Worker, typename ResultType>
    static void connectQuerySignals(Worker* worker, QObject* receiver,
                                  QueryCallback<ResultType> callback) {
        // 这个函数的实现需要根据具体的Worker类型来定制
        // 暂时留空，在具体使用时再实现
    }
};
