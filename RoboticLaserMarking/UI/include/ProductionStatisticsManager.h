#pragma once

#include "ProductionStatistics.h"
#include <QObject>
#include <QTimer>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QMutex>
#include <QSqlDatabase>
#include <QSqlQuery>
#include <QSqlError>
#include <memory>

// Forward declarations
class DatabaseQueryThread;
class DatabaseQueryWorker;

class ProductionStatisticsManager : public QObject
{
    Q_OBJECT

public:
    explicit ProductionStatisticsManager(QObject *parent = nullptr);
    ~ProductionStatisticsManager();

    // Initialize the manager
    bool initialize(const QString& dataDirectory = "");
    
    // Record management (synchronous - for backward compatibility)
    QString startProductionRecord(const QString& rfidCode,
                                const QString& templateName,
                                int shoeCode,
                                bool isLeftFoot,
                                int totalFaces);

    bool updateProductionRecord(const QString& recordId,
                              int completedFaces,
                              ProductionStatus status = ProductionStatus::SUCCESS,
                              const QString& errorMessage = "");

    bool finishProductionRecord(const QString& recordId,
                              ProductionStatus status = ProductionStatus::SUCCESS,
                              const QString& errorMessage = "");

    bool addCustomData(const QString& recordId,
                      const QString& key,
                      const QVariant& value);

    // Async record management (recommended for UI operations)
    void startProductionRecordAsync(const QString& rfidCode,
                                   const QString& templateName,
                                   int shoeCode,
                                   bool isLeftFoot,
                                   int totalFaces);

    void updateProductionRecordAsync(const QString& recordId,
                                    int completedFaces,
                                    ProductionStatus status = ProductionStatus::SUCCESS,
                                    const QString& errorMessage = "");

    void finishProductionRecordAsync(const QString& recordId,
                                    ProductionStatus status = ProductionStatus::SUCCESS,
                                    const QString& errorMessage = "");

    void addCustomDataAsync(const QString& recordId,
                           const QString& key,
                           const QVariant& value);
    
    // Query functions
    QVector<ProductionRecord> getRecords(const StatisticsFilter& filter = StatisticsFilter()) const;
    ProductionRecord getRecord(const QString& recordId) const;
    ProductionSummary getSummary(const StatisticsFilter& filter = StatisticsFilter()) const;
    ProductionSummary getPeriodSummary(StatisticsPeriod period, 
                                     const QDateTime& referenceTime = QDateTime::currentDateTime()) const;
    
    // Statistics functions
    ChartData getProductionTrend(StatisticsPeriod period, 
                               int periodCount = 7,
                               const QDateTime& endTime = QDateTime::currentDateTime()) const;
    
    ChartData getEfficiencyAnalysis(const StatisticsFilter& filter = StatisticsFilter()) const;
    ChartData getTemplateUsageChart(const StatisticsFilter& filter = StatisticsFilter()) const;
    ChartData getStatusDistribution(const StatisticsFilter& filter = StatisticsFilter()) const;
    ChartData getShoeSizeDistribution(const StatisticsFilter& filter = StatisticsFilter()) const;
    
    // Data export functions
    bool exportToCSV(const QString& filePath, 
                    const StatisticsFilter& filter = StatisticsFilter()) const;
    
    bool exportSummaryToCSV(const QString& filePath, 
                          StatisticsPeriod period,
                          int periodCount = 30,
                          const QDateTime& endTime = QDateTime::currentDateTime()) const;
    
    // Data management
    bool clearOldRecords(int daysToKeep = 90);
    bool backupData(const QString& backupPath) const;
    bool restoreData(const QString& backupPath);
    
    // Real-time monitoring
    void startRealTimeMonitoring(int intervalMs = 1000);
    void stopRealTimeMonitoring();
    
    // Configuration
    void setAutoSaveInterval(int minutes = 5);
    void setMaxRecordsInMemory(int maxRecords = 10000);
    
    // Statistics
    int getTotalRecordsCount() const;
    int getActiveRecordsCount() const;
    QDateTime getOldestRecordTime() const;
    QDateTime getNewestRecordTime() const;

signals:
    // Emitted when a new record is started
    void recordStarted(const QString& recordId, const ProductionRecord& record);
    
    // Emitted when a record is updated
    void recordUpdated(const QString& recordId, const ProductionRecord& record);
    
    // Emitted when a record is finished
    void recordFinished(const QString& recordId, const ProductionRecord& record);
    
    // Emitted when statistics are updated
    void statisticsUpdated();
    
    // Emitted when an error occurs
    void errorOccurred(const QString& error);
    
    // Real-time monitoring signals
    void realTimeDataUpdated(const ProductionSummary& currentSummary);

private slots:
    void onAutoSaveTimer();
    void onRealTimeMonitoringTimer();

private:
    // Database operations
    bool initializeDatabase();
    bool createTables();
    bool saveRecordToDatabase(const ProductionRecord& record);
    bool updateRecordInDatabase(const ProductionRecord& record);
    QVector<ProductionRecord> loadRecordsFromDatabase(const StatisticsFilter& filter) const;
    
    // File operations
    bool saveRecordsToFile() const;
    bool loadRecordsFromFile();
    QString getDataFilePath() const;
    QString getDatabasePath() const;
    
    // Helper functions
    bool applyFilter(const ProductionRecord& record, const StatisticsFilter& filter) const;
    ProductionSummary calculateSummary(const QVector<ProductionRecord>& records) const;
    TrendDataSeries calculateTrendData(const QVector<ProductionRecord>& records,
                                     StatisticsPeriod period) const;

    // Memory management
    void trimCachedRecords();

    // Data validation
    bool validateRecord(const ProductionRecord& record) const;
    bool validateFilter(const StatisticsFilter& filter) const;

    // Async database operations
    void initializeDatabaseQueryThread();

private:
    // Data storage
    QMap<QString, ProductionRecord> m_activeRecords;  // Currently active records
    QVector<ProductionRecord> m_cachedRecords;        // Cached records for quick access
    
    // Database
    QSqlDatabase m_database;
    QString m_dataDirectory;
    QString m_databasePath;
    
    // Configuration
    int m_autoSaveIntervalMinutes;
    int m_maxRecordsInMemory;
    int m_realTimeMonitoringInterval;
    
    // Timers
    QTimer* m_autoSaveTimer;
    QTimer* m_realTimeMonitoringTimer;
    
    // Thread safety
    mutable QMutex m_dataMutex;

    // Statistics cache
    mutable QMap<QString, ProductionSummary> m_summaryCache;
    mutable QDateTime m_lastCacheUpdate;

    // Async database operations
    DatabaseQueryThread* m_dbQueryThread;
    DatabaseQueryWorker* m_dbQueryWorker;

    // Status
    bool m_initialized;
    bool m_realTimeMonitoringActive;
};
