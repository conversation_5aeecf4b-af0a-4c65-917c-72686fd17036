#pragma once

#include "ProductionStatistics.h"
#include "ProductionStatisticsManager.h"
#include <QObject>
#include <QString>
#include <QDateTime>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <memory>

// Export format enumeration
enum class ExportFormat {
    CSV = 0,
    JSON = 1,
    HTML = 2,
    XML = 3,
    EXCEL = 4
};

// Export options structure
struct ExportOptions {
    ExportFormat format;
    QString filePath;
    StatisticsFilter filter;
    QStringList selectedFields;
    bool includeHeader;
    bool includeMetadata;
    bool includeCharts;
    QString encoding;
    QString delimiter;
    QString dateTimeFormat;
    
    // Default constructor
    ExportOptions()
        : format(ExportFormat::CSV)
        , includeHeader(true)
        , includeMetadata(true)
        , includeCharts(false)
        , encoding("UTF-8")
        , delimiter(",")
        , dateTimeFormat("yyyy-MM-dd hh:mm:ss") {}
};

class StatisticsExporter : public QObject
{
    Q_OBJECT

public:
    explicit StatisticsExporter(std::shared_ptr<ProductionStatisticsManager> statsManager,
                               QObject *parent = nullptr);
    ~StatisticsExporter();

    // Export functions
    bool exportRecords(const ExportOptions& options);
    bool exportSummary(const ExportOptions& options, const ProductionSummary& summary);
    bool exportComparison(const ExportOptions& options, 
                         StatisticsPeriod period, 
                         int periodCount = 7);
    bool exportTemplate(const QString& templatePath, const ExportOptions& options);
    
    // Batch export functions
    bool exportMultipleFormats(const QString& baseFilePath, 
                              const StatisticsFilter& filter,
                              const QList<ExportFormat>& formats);
    
    bool exportScheduledReport(const QString& outputDirectory,
                              StatisticsPeriod period = StatisticsPeriod::DAY);
    
    // Template functions
    bool createExportTemplate(const QString& templatePath, const ExportOptions& options);
    bool loadExportTemplate(const QString& templatePath, ExportOptions& options);
    
    // Validation functions
    bool validateExportOptions(const ExportOptions& options) const;
    QStringList getAvailableFields() const;
    QStringList getSupportedEncodings() const;
    
    // Utility functions
    QString getDefaultFileName(ExportFormat format, const StatisticsFilter& filter) const;
    QString getFormatExtension(ExportFormat format) const;
    QString getFormatDescription(ExportFormat format) const;

signals:
    void exportStarted(const QString& filePath);
    void exportProgress(int percentage, const QString& message);
    void exportCompleted(const QString& filePath, bool success);
    void exportError(const QString& error);

private:
    // Format-specific export functions
    bool exportToCSV(const ExportOptions& options, const QVector<ProductionRecord>& records);
    bool exportToJSON(const ExportOptions& options, const QVector<ProductionRecord>& records);
    bool exportToHTML(const ExportOptions& options, const QVector<ProductionRecord>& records);
    bool exportToXML(const ExportOptions& options, const QVector<ProductionRecord>& records);
    bool exportToExcel(const ExportOptions& options, const QVector<ProductionRecord>& records);
    
    // Summary export functions
    bool exportSummaryToCSV(const ExportOptions& options, const ProductionSummary& summary);
    bool exportSummaryToJSON(const ExportOptions& options, const ProductionSummary& summary);
    bool exportSummaryToHTML(const ExportOptions& options, const ProductionSummary& summary);
    
    // Helper functions
    QString formatFieldValue(const ProductionRecord& record, const QString& fieldName, const ExportOptions& options) const;
    QString escapeCSVValue(const QString& value, const QString& delimiter) const;
    QString formatDateTime(const QDateTime& dateTime, const QString& format) const;
    QString formatDuration(qint64 milliseconds) const;
    QJsonObject recordToJson(const ProductionRecord& record) const;
    QJsonObject summaryToJson(const ProductionSummary& summary) const;
    
    // HTML generation functions
    QString generateHTMLHeader(const QString& title) const;
    QString generateHTMLFooter() const;
    QString generateHTMLTable(const QVector<ProductionRecord>& records, const ExportOptions& options) const;
    QString generateHTMLSummary(const ProductionSummary& summary) const;
    
    // XML generation functions
    QString generateXMLHeader() const;
    QString generateXMLRecord(const ProductionRecord& record) const;
    QString generateXMLSummary(const ProductionSummary& summary) const;
    
    // File operations
    bool writeToFile(const QString& filePath, const QString& content, const QString& encoding) const;
    bool createDirectory(const QString& dirPath) const;
    QString generateUniqueFileName(const QString& basePath) const;
    
    // Progress tracking
    void updateProgress(int percentage, const QString& message);
    void resetProgress();

private:
    std::shared_ptr<ProductionStatisticsManager> m_statsManager;
    
    // Export state
    bool m_exportInProgress;
    int m_currentProgress;
    QString m_currentOperation;
    
    // Default field mappings
    QMap<QString, QString> m_fieldDisplayNames;
    QStringList m_defaultFields;
    QStringList m_availableFields;
    
    // Format configurations
    QMap<ExportFormat, QString> m_formatExtensions;
    QMap<ExportFormat, QString> m_formatDescriptions;
};
