#include "RegisterDialog.h"
#include <QClipboard>
#include <QApplication>
#include <QMessageBox>
#include <QDesktopServices>
#include <QUrl>
#include <QVBoxLayout>
#include <QFrame>

#pragma execution_character_set("utf-8")

RegisterDialog::RegisterDialog(LicenseManager* licenseManager, QWidget *parent)
    : QDialog(parent), m_licenseManager(licenseManager)
{
    setWindowTitle("软件授权状态");
    setupUI();
    updateUI();
    
    // 设置窗口属性
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setFixedSize(500, 400);
}

RegisterDialog::~RegisterDialog()
{
}

void RegisterDialog::setupUI()
{
    mainLayout = new QGridLayout(this);
    
    // 创建标题标签
    lblTitle = new QLabel("激光打粗控制系统授权状态", this);
    lblTitle->setStyleSheet("font-size: 14px; font-weight: bold; color: #2980b9;");
    
    // 创建MAC地址部分
    lblMACAddresses = new QLabel("本机MAC地址:", this);
    lblMACAddresses->setStyleSheet("font-weight: bold;");
    
    txtMACAddresses = new QTextEdit(this);
    txtMACAddresses->setMaximumHeight(120);
    txtMACAddresses->setReadOnly(true);
    txtMACAddresses->setStyleSheet("font-family: Consolas, monospace; font-size: 11px; background-color: #f8f9fa; border: 1px solid #ddd;");
    
    // 显示当前机器的MAC地址
    QStringList macAddresses = m_licenseManager->getMACAddresses();
    QString macText = "";
    for (int i = 0; i < macAddresses.size(); ++i) {
        macText += QString("%1. %2\n").arg(i + 1).arg(macAddresses[i]);
    }
    txtMACAddresses->setPlainText(macText);
    
    btnCopyMACAddresses = new QPushButton("复制MAC地址", this);
    
    // 状态标签
    lblStatus = new QLabel(this);
    
    // 试用天数标签
    int trialDays = m_licenseManager->getTrialDaysLeft();
    lblTrialDays = new QLabel(QString("试用期剩余: %1 天").arg(trialDays), this);
    
    if (trialDays <= 7) {
        lblTrialDays->setStyleSheet("color: #e74c3c; font-weight: bold;");
    } else {
        lblTrialDays->setStyleSheet("color: #27ae60;");
    }
    
    // 按钮
    btnRefreshStatus = new QPushButton("刷新状态", this);
    btnContinueTrial = new QPushButton("继续试用", this);
    btnClose = new QPushButton("关闭", this);
    
    // 创建信息框
    QFrame* infoFrame = new QFrame(this);
    infoFrame->setFrameShape(QFrame::StyledPanel);
    infoFrame->setStyleSheet("background-color: #f0f7fb; border: 1px solid #d0e3f0; padding: 8px; border-radius: 4px;");
    
    QVBoxLayout* infoLayout = new QVBoxLayout(infoFrame);
    QLabel* infoLabel = new QLabel("授权说明:", infoFrame);
    infoLabel->setStyleSheet("font-weight: bold; color: #2980b9;");
    
    QLabel* infoText = new QLabel(
        "本软件采用MAC地址白名单授权机制。\n"
        "请将上方的MAC地址发送给管理员，"
        "管理员将其添加到白名单后，软件即可正常使用。",
        infoFrame);
    
    infoLayout->addWidget(infoLabel);
    infoLayout->addWidget(infoText);
    
    // 创建联系按钮
    QPushButton* btnContact = new QPushButton("联系管理员", this);
    btnContact->setStyleSheet("background-color: #3498db; color: white;");
    connect(btnContact, &QPushButton::clicked, [this]() {
        QString macList = "";
        QStringList macAddresses = m_licenseManager->getMACAddresses();
        for (const QString& mac : macAddresses) {
            macList += mac + "\n";
        }
        QDesktopServices::openUrl(QUrl("mailto:<EMAIL>?subject=激光打粗控制系统授权申请&body=请将以下MAC地址添加到白名单:\n" + macList));
    });
    
    // 添加控件到布局
    int row = 0;
    mainLayout->addWidget(lblTitle, row++, 0, 1, 3, Qt::AlignCenter);
    
    mainLayout->addWidget(lblMACAddresses, row++, 0, 1, 3);
    mainLayout->addWidget(txtMACAddresses, row++, 0, 1, 3);
    mainLayout->addWidget(btnCopyMACAddresses, row++, 0, 1, 3, Qt::AlignCenter);
    
    mainLayout->addWidget(lblStatus, row++, 0, 1, 3, Qt::AlignCenter);
    mainLayout->addWidget(lblTrialDays, row++, 0, 1, 3, Qt::AlignCenter);
    
    mainLayout->addWidget(infoFrame, row++, 0, 1, 3);
    
    // 底部按钮
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(btnRefreshStatus);
    buttonLayout->addWidget(btnContinueTrial);
    buttonLayout->addWidget(btnContact);
    buttonLayout->addWidget(btnClose);
    
    mainLayout->addLayout(buttonLayout, row++, 0, 1, 3);
    
    // 连接信号
    connect(btnCopyMACAddresses, &QPushButton::clicked, this, &RegisterDialog::onCopyMACAddresses);
    connect(btnRefreshStatus, &QPushButton::clicked, this, &RegisterDialog::onRefreshStatus);
    connect(btnContinueTrial, &QPushButton::clicked, this, &RegisterDialog::onContinueTrial);
    connect(btnClose, &QPushButton::clicked, this, &RegisterDialog::onClose);
}

void RegisterDialog::updateUI()
{
    // 检查授权状态
    if (m_licenseManager->isAuthorized()) {
        lblStatus->setText("✓ 软件已授权，可以正常使用");
        lblStatus->setStyleSheet("color: #27ae60; font-weight: bold; font-size: 12px;");
        btnContinueTrial->setVisible(false);
        lblTrialDays->setVisible(false);
    } else {
        lblStatus->setText("✗ 软件未授权，当前MAC地址不在白名单中");
        lblStatus->setStyleSheet("color: #e74c3c; font-weight: bold; font-size: 12px;");
        
        // 检查试用期
        if (m_licenseManager->isTrialAvailable()) {
            btnContinueTrial->setVisible(true);
            lblTrialDays->setVisible(true);
            
            int trialDays = m_licenseManager->getTrialDaysLeft();
            lblTrialDays->setText(QString("试用期剩余: %1 天").arg(trialDays));
            
            if (trialDays <= 7) {
                lblTrialDays->setStyleSheet("color: #e74c3c; font-weight: bold;");
            } else {
                lblTrialDays->setStyleSheet("color: #27ae60;");
            }
        } else {
            btnContinueTrial->setVisible(false);
            lblTrialDays->setText("试用期已结束");
            lblTrialDays->setStyleSheet("color: #e74c3c; font-weight: bold;");
            lblTrialDays->setVisible(true);
        }
    }
}

void RegisterDialog::onCopyMACAddresses()
{
    QStringList macAddresses = m_licenseManager->getMACAddresses();
    QString macText = macAddresses.join("\n");
    
    QClipboard* clipboard = QApplication::clipboard();
    clipboard->setText(macText);
    
    QMessageBox::information(this, "复制成功", "MAC地址已复制到剪贴板");
}

void RegisterDialog::onRefreshStatus()
{
    // 由于MAC白名单已硬编码，不需要重新加载文件
    // 直接更新UI即可
    updateUI();
    
    QMessageBox::information(this, "刷新完成", "授权状态已刷新");
}

void RegisterDialog::onContinueTrial()
{
    if (m_licenseManager->isTrialAvailable()) {
        accept(); // 关闭对话框，继续试用
    } else {
        QMessageBox::warning(this, "试用期结束", "试用期已结束，请联系管理员获取授权");
    }
}

void RegisterDialog::onClose()
{
    reject();
}