#include "DeviceControlUtils.h"
#include "UIUtils.h"
#include <QHBoxLayout>

// DeviceButtonGroup 实现
DeviceControlUtils::DeviceButtonGroup::DeviceButtonGroup(const QString& name, QWidget* parent)
    : deviceName(name)
{
    connectButton = UIUtils::createButton("连接" + name, UIUtils::ButtonStyle::Success, UIUtils::FontSize::Medium, parent);
    disconnectButton = UIUtils::createButton("断开" + name, UIUtils::ButtonStyle::Danger, UIUtils::FontSize::Medium, parent);
    statusLabel = UIUtils::createStyledLabel(name + ": 未连接", "#666", UIUtils::FontSize::Medium, false, parent);
    
    connectButton->setProperty("cssClass", "functionButton");
    disconnectButton->setProperty("cssClass", "functionButton");
    
    // 初始状态
    updateState(DeviceState::Disconnected);
}

void DeviceControlUtils::DeviceButtonGroup::updateState(DeviceState state, const QString& message)
{
    QString statusText = message.isEmpty() ? (deviceName + ": " + DeviceControlUtils::stateToString(state)) : message;
    QString color = DeviceControlUtils::stateToColor(state);
    
    statusLabel->setText(statusText);
    statusLabel->setStyleSheet(QString("QLabel { color: %1; }").arg(color));
    
    switch (state) {
        case DeviceState::Disconnected:
            connectButton->setEnabled(true);
            disconnectButton->setEnabled(false);
            break;
            
        case DeviceState::Connecting:
            connectButton->setEnabled(false);
            disconnectButton->setEnabled(false);
            break;
            
        case DeviceState::Connected:
            connectButton->setEnabled(false);
            disconnectButton->setEnabled(true);
            break;
            
        case DeviceState::Error:
            connectButton->setEnabled(true);
            disconnectButton->setEnabled(false);
            break;
    }
}

// DeviceController 实现
DeviceControlUtils::DeviceController::DeviceController(const QString& deviceName, QWidget* parent)
    : m_buttonGroup(deviceName, parent)
    , m_currentState(DeviceState::Disconnected)
{
    setupConnections();
}

void DeviceControlUtils::DeviceController::setConnectOperation(DeviceOperation operation)
{
    m_connectOperation = operation;
}

void DeviceControlUtils::DeviceController::setDisconnectOperation(DeviceOperation operation)
{
    m_disconnectOperation = operation;
}

void DeviceControlUtils::DeviceController::setStatusUpdateCallback(StatusUpdateCallback callback)
{
    m_statusCallback = callback;
}

void DeviceControlUtils::DeviceController::executeConnect()
{
    if (!m_connectOperation) {
        LogUtils::logWarning("连接操作未设置: " + m_buttonGroup.deviceName, "设备控制");
        return;
    }
    
    updateState(DeviceState::Connecting, m_buttonGroup.deviceName + ": 正在连接...");
    
    OperationResult result = m_connectOperation();
    handleOperationResult(result, true);
}

void DeviceControlUtils::DeviceController::executeDisconnect()
{
    if (!m_disconnectOperation) {
        LogUtils::logWarning("断开操作未设置: " + m_buttonGroup.deviceName, "设备控制");
        return;
    }
    
    OperationResult result = m_disconnectOperation();
    handleOperationResult(result, false);
}

void DeviceControlUtils::DeviceController::updateState(DeviceState state, const QString& message)
{
    m_currentState = state;
    m_buttonGroup.updateState(state, message);
    
    if (m_statusCallback) {
        m_statusCallback(state, message);
    }
}

void DeviceControlUtils::DeviceController::setupConnections()
{
    QObject::connect(m_buttonGroup.connectButton, &QPushButton::clicked,
                    [this]() { executeConnect(); });

    QObject::connect(m_buttonGroup.disconnectButton, &QPushButton::clicked,
                    [this]() { executeDisconnect(); });
}

void DeviceControlUtils::DeviceController::handleOperationResult(const OperationResult& result, bool isConnect)
{
    if (result.success) {
        DeviceState newState = isConnect ? DeviceState::Connected : DeviceState::Disconnected;
        updateState(newState, m_buttonGroup.deviceName + ": " + result.message);
        LogUtils::logInfo(result.message, "设备控制");
    } else {
        updateState(DeviceState::Error, m_buttonGroup.deviceName + ": " + result.message);
        LogUtils::logError(result.message + " - " + result.errorDetails, "设备控制");
    }
}

// MultiDeviceManager 实现
void DeviceControlUtils::MultiDeviceManager::addDevice(std::shared_ptr<DeviceController> controller)
{
    m_devices.append(controller);
}

void DeviceControlUtils::MultiDeviceManager::connectAllDevices()
{
    if (m_globalStatusCallback) {
        m_globalStatusCallback("正在连接所有设备...");
    }
    
    for (auto& device : m_devices) {
        if (device->getCurrentState() == DeviceState::Disconnected) {
            device->executeConnect();
        }
    }
    
    if (m_globalStatusCallback) {
        m_globalStatusCallback("设备连接操作完成");
    }
}

void DeviceControlUtils::MultiDeviceManager::disconnectAllDevices()
{
    if (m_globalStatusCallback) {
        m_globalStatusCallback("正在断开所有设备...");
    }
    
    for (auto& device : m_devices) {
        if (device->getCurrentState() == DeviceState::Connected) {
            device->executeDisconnect();
        }
    }
    
    if (m_globalStatusCallback) {
        m_globalStatusCallback("设备断开操作完成");
    }
}

QList<DeviceControlUtils::DeviceState> DeviceControlUtils::MultiDeviceManager::getAllStates() const
{
    QList<DeviceState> states;
    for (const auto& device : m_devices) {
        states.append(device->getCurrentState());
    }
    return states;
}

bool DeviceControlUtils::MultiDeviceManager::areAllDevicesConnected() const
{
    for (const auto& device : m_devices) {
        if (device->getCurrentState() != DeviceState::Connected) {
            return false;
        }
    }
    return !m_devices.isEmpty();
}

bool DeviceControlUtils::MultiDeviceManager::areAllDevicesDisconnected() const
{
    for (const auto& device : m_devices) {
        if (device->getCurrentState() != DeviceState::Disconnected) {
            return false;
        }
    }
    return true;
}

void DeviceControlUtils::MultiDeviceManager::setGlobalStatusCallback(std::function<void(const QString&)> callback)
{
    m_globalStatusCallback = callback;
}

// 静态工具方法实现
DeviceControlUtils::DeviceButtonGroup DeviceControlUtils::createDeviceButtons(
    const QString& deviceName, const QString& connectText, const QString& disconnectText, QWidget* parent)
{
    DeviceButtonGroup group(deviceName, parent);
    group.connectButton->setText(connectText);
    group.disconnectButton->setText(disconnectText);
    return group;
}

QString DeviceControlUtils::stateToString(DeviceState state)
{
    switch (state) {
        case DeviceState::Disconnected:
            return "未连接";
        case DeviceState::Connecting:
            return "连接中";
        case DeviceState::Connected:
            return "已连接";
        case DeviceState::Error:
            return "错误";
        default:
            return "未知状态";
    }
}

QString DeviceControlUtils::stateToColor(DeviceState state)
{
    switch (state) {
        case DeviceState::Disconnected:
            return "#666";
        case DeviceState::Connecting:
            return "#FF9800";
        case DeviceState::Connected:
            return "#4CAF50";
        case DeviceState::Error:
            return "#F44336";
        default:
            return "#666";
    }
}
