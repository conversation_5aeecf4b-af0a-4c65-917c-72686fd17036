#include "UIUtils.h"
#include <QApplication>
#include <QHeaderView>
#include <QAbstractItemView>
#pragma execution_character_set("utf-8")
// 颜色常量定义
const QString UIUtils::COLOR_PRIMARY = "#2196F3";
const QString UIUtils::COLOR_SUCCESS = "#4CAF50";
const QString UIUtils::COLOR_WARNING = "#FF9800";
const QString UIUtils::COLOR_DANGER = "#F44336";
const QString UIUtils::COLOR_INFO = "#00BCD4";
const QString UIUtils::COLOR_SECONDARY = "#9E9E9E";
const QString UIUtils::COLOR_LIGHT = "#F5F5F5";
const QString UIUtils::COLOR_DARK = "#212121";

// 按钮创建方法
QPushButton* UIUtils::createButton(const QString& text, ButtonStyle style, FontSize fontSize, QWidget* parent)
{
    QPushButton* button = new QPushButton(text, parent);
    button->setFont(createFont(fontSize, style == ButtonStyle::Primary));
    button->setStyleSheet(getButtonStyleSheet(style));
    button->setMinimumHeight(35);
    return button;
}

QPushButton* UIUtils::createStyledButton(const QString& text, const QString& backgroundColor, 
                                       const QString& textColor, FontSize fontSize, QWidget* parent)
{
    QPushButton* button = new QPushButton(text, parent);
    button->setFont(createFont(fontSize, true));
    button->setStyleSheet(getButtonStyleSheet(backgroundColor, textColor));
    button->setMinimumHeight(35);
    return button;
}

// 标签创建方法
QLabel* UIUtils::createLabel(const QString& text, FontSize fontSize, bool bold, QWidget* parent)
{
    QLabel* label = new QLabel(text, parent);
    label->setFont(createFont(fontSize, bold));
    return label;
}

QLabel* UIUtils::createStyledLabel(const QString& text, const QString& color, 
                                 FontSize fontSize, bool bold, QWidget* parent)
{
    QLabel* label = new QLabel(text, parent);
    label->setFont(createFont(fontSize, bold));
    label->setStyleSheet(QString("QLabel { color: %1; }").arg(color));
    return label;
}

// 组框创建方法
QGroupBox* UIUtils::createGroupBox(const QString& title, FontSize fontSize, bool bold, QWidget* parent)
{
    QGroupBox* groupBox = new QGroupBox(title, parent);
    groupBox->setFont(createFont(fontSize, bold));
    return groupBox;
}

// 布局创建方法
QHBoxLayout* UIUtils::createHBoxLayout(int spacing, int margin)
{
    QHBoxLayout* layout = new QHBoxLayout();
    layout->setSpacing(spacing);
    layout->setContentsMargins(margin, margin, margin, margin);
    return layout;
}

QVBoxLayout* UIUtils::createVBoxLayout(int spacing, int margin)
{
    QVBoxLayout* layout = new QVBoxLayout();
    layout->setSpacing(spacing);
    layout->setContentsMargins(margin, margin, margin, margin);
    return layout;
}

QGridLayout* UIUtils::createGridLayout(int spacing, int margin)
{
    QGridLayout* layout = new QGridLayout();
    layout->setSpacing(spacing);
    layout->setContentsMargins(margin, margin, margin, margin);
    return layout;
}

// 输入控件创建方法
QComboBox* UIUtils::createComboBox(const QStringList& items, FontSize fontSize, QWidget* parent)
{
    QComboBox* comboBox = new QComboBox(parent);
    comboBox->setFont(createFont(fontSize));
    comboBox->setMinimumHeight(30);
    comboBox->addItems(items);
    return comboBox;
}

QDateTimeEdit* UIUtils::createDateTimeEdit(const QDateTime& dateTime, FontSize fontSize, QWidget* parent)
{
    QDateTimeEdit* dateTimeEdit = new QDateTimeEdit(dateTime, parent);
    dateTimeEdit->setFont(createFont(fontSize));
    dateTimeEdit->setMinimumHeight(30);
    dateTimeEdit->setCalendarPopup(true);
    dateTimeEdit->setDisplayFormat("yyyy-MM-dd hh:mm:ss");
    return dateTimeEdit;
}

QCheckBox* UIUtils::createCheckBox(const QString& text, FontSize fontSize, QWidget* parent)
{
    QCheckBox* checkBox = new QCheckBox(text, parent);
    checkBox->setFont(createFont(fontSize));
    return checkBox;
}

QCheckBox* UIUtils::createStyledCheckBox(const QString& text, CheckBoxStyle style, FontSize fontSize, QWidget* parent)
{
    QCheckBox* checkBox = new QCheckBox(text, parent);
    checkBox->setFont(createFont(fontSize));
    checkBox->setStyleSheet(getCheckBoxStyleSheet(style));
    return checkBox;
}

// 表格创建方法
QTableWidget* UIUtils::createTableWidget(const QStringList& headers, FontSize fontSize, QWidget* parent)
{
    QTableWidget* table = new QTableWidget(parent);
    table->setFont(createFont(fontSize));
    table->setColumnCount(headers.size());
    table->setHorizontalHeaderLabels(headers);
    
    // 设置表格属性
    table->setAlternatingRowColors(true);
    table->setSelectionBehavior(QAbstractItemView::SelectRows);
    table->setSortingEnabled(true);
    table->horizontalHeader()->setStretchLastSection(true);
    table->horizontalHeader()->setFont(createFont(fontSize, true));
    table->verticalHeader()->setDefaultSectionSize(30);
    
    return table;
}

// 字体工具方法
QFont UIUtils::createFont(FontSize fontSize, bool bold)
{
    return createFont(static_cast<int>(fontSize), bold);
}

QFont UIUtils::createFont(int size, bool bold)
{
    QFont font("Microsoft YaHei", size);
    if (bold) {
        font.setBold(true);
    }
    return font;
}

// 样式字符串生成方法
QString UIUtils::getButtonStyleSheet(ButtonStyle style)
{
    QString baseStyle = "QPushButton { color: white; border: none; border-radius: 5px; padding: 8px 16px; font-weight: bold; }";
    QString hoverStyle = "QPushButton:hover { opacity: 0.8; }";
    QString pressedStyle = "QPushButton:pressed { opacity: 0.6; }";
    
    QString backgroundColor;
    switch (style) {
        case ButtonStyle::Primary:
            backgroundColor = COLOR_PRIMARY;
            break;
        case ButtonStyle::Success:
            backgroundColor = COLOR_SUCCESS;
            break;
        case ButtonStyle::Warning:
            backgroundColor = COLOR_WARNING;
            break;
        case ButtonStyle::Danger:
            backgroundColor = COLOR_DANGER;
            break;
        case ButtonStyle::Info:
            backgroundColor = COLOR_INFO;
            break;
        case ButtonStyle::Secondary:
            backgroundColor = COLOR_SECONDARY;
            break;
        case ButtonStyle::Function:
            return ""; // 使用CSS类样式
    }
    
    return QString("QPushButton { background-color: %1; %2 } %3 %4")
           .arg(backgroundColor)
           .arg(baseStyle.mid(baseStyle.indexOf("color:")))
           .arg(hoverStyle)
           .arg(pressedStyle);
}

QString UIUtils::getButtonStyleSheet(const QString& backgroundColor, const QString& textColor)
{
    return QString("QPushButton { background-color: %1; color: %2; border: none; border-radius: 5px; padding: 8px 16px; font-weight: bold; } "
                  "QPushButton:hover { opacity: 0.8; } "
                  "QPushButton:pressed { opacity: 0.6; }")
           .arg(backgroundColor)
           .arg(textColor);
}

QString UIUtils::getCheckBoxStyleSheet(CheckBoxStyle style)
{
    switch (style) {
        case CheckBoxStyle::Default:
            return "";

        case CheckBoxStyle::Custom:
            return R"(
                QCheckBox {
                    spacing: 8px;
                    font-weight: bold;
                }
                QCheckBox::indicator {
                    width: 18px;
                    height: 18px;
                    border: 2px solid #4CAF50;
                    border-radius: 3px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #66BB6A;
                }
                QCheckBox::indicator:pressed {
                    background-color: #388E3C;
                    border: 2px solid #388E3C;
                }
            )";

        case CheckBoxStyle::Toggle:
            return R"(
                QCheckBox {
                    spacing: 8px;
                    font-weight: bold;
                }
                QCheckBox::indicator {
                    width: 40px;
                    height: 20px;
                    border: 2px solid #ccc;
                    border-radius: 12px;
                    background-color: #fff;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #66BB6A;
                }
            )";

        case CheckBoxStyle::Radio:
            return R"(
                QCheckBox {
                    spacing: 8px;
                    font-weight: bold;
                }
                QCheckBox::indicator {
                    width: 16px;
                    height: 16px;
                    border: 2px solid #4CAF50;
                    border-radius: 10px;
                    background-color: white;
                }
                QCheckBox::indicator:checked {
                    background-color: #4CAF50;
                    border: 2px solid #4CAF50;
                }
                QCheckBox::indicator:hover {
                    border: 2px solid #66BB6A;
                }
            )";

        default:
            return "";
    }
}

// 消息框工具方法
void UIUtils::showInfoMessage(QWidget* parent, const QString& title, const QString& message)
{
    QMessageBox::information(parent, title, message);
}

void UIUtils::showWarningMessage(QWidget* parent, const QString& title, const QString& message)
{
    QMessageBox::warning(parent, title, message);
}

void UIUtils::showErrorMessage(QWidget* parent, const QString& title, const QString& message)
{
    QMessageBox::critical(parent, title, message);
}

bool UIUtils::showConfirmDialog(QWidget* parent, const QString& title, const QString& message)
{
    return QMessageBox::question(parent, title, message, 
                                QMessageBox::Yes | QMessageBox::No, 
                                QMessageBox::No) == QMessageBox::Yes;
}

// 批量操作方法
void UIUtils::setWidgetsEnabled(const QList<QWidget*>& widgets, bool enabled)
{
    for (QWidget* widget : widgets) {
        if (widget) {
            widget->setEnabled(enabled);
        }
    }
}

void UIUtils::setButtonsStyle(const QList<QPushButton*>& buttons, ButtonStyle style)
{
    QString styleSheet = getButtonStyleSheet(style);
    for (QPushButton* button : buttons) {
        if (button) {
            button->setStyleSheet(styleSheet);
        }
    }
}

// ErrorHandler 实现
ErrorHandler::ErrorCallback ErrorHandler::s_globalErrorHandler = nullptr;

void ErrorHandler::setGlobalErrorHandler(ErrorCallback callback)
{
    s_globalErrorHandler = callback;
}

void ErrorHandler::handleError(QWidget* parent, const QString& title, const QString& message)
{
    if (s_globalErrorHandler) {
        s_globalErrorHandler(message);
    }
    UIUtils::showErrorMessage(parent, title, message);
}

// LogUtils 实现
LogUtils::LogCallback LogUtils::s_logCallback = nullptr;

void LogUtils::setLogCallback(LogCallback callback)
{
    s_logCallback = callback;
}

void LogUtils::log(const QString& message, const QString& category, LogLevel level)
{
    if (s_logCallback) {
        s_logCallback(message, category);
    }
}

void LogUtils::logDebug(const QString& message, const QString& category)
{
    log(message, category, LogLevel::Debug);
}

void LogUtils::logInfo(const QString& message, const QString& category)
{
    log(message, category, LogLevel::Info);
}

void LogUtils::logWarning(const QString& message, const QString& category)
{
    log(message, category, LogLevel::Warning);
}

void LogUtils::logError(const QString& message, const QString& category)
{
    log(message, category, LogLevel::Error);
}

// DatabaseUtils 实现
void DatabaseUtils::setQueryState(QPushButton* queryButton, QPushButton* refreshButton,
                                QLabel* statusLabel, QueryState state, const QString& message)
{
    if (!queryButton || !statusLabel) return;

    switch (state) {
        case QueryState::Idle:
            queryButton->setEnabled(true);
            queryButton->setText("查询");
            if (refreshButton) refreshButton->setEnabled(true);
            statusLabel->setText(message.isEmpty() ? "就绪" : message);
            break;

        case QueryState::Running:
            queryButton->setEnabled(false);
            queryButton->setText("查询中...");
            if (refreshButton) refreshButton->setEnabled(false);
            statusLabel->setText(message.isEmpty() ? "正在查询..." : message);
            break;

        case QueryState::Success:
            queryButton->setEnabled(true);
            queryButton->setText("查询");
            if (refreshButton) refreshButton->setEnabled(true);
            statusLabel->setText(message.isEmpty() ? "查询完成" : message);
            break;

        case QueryState::Error:
            queryButton->setEnabled(true);
            queryButton->setText("查询");
            if (refreshButton) refreshButton->setEnabled(true);
            statusLabel->setText(message.isEmpty() ? "查询失败" : message);
            break;

        case QueryState::Timeout:
            queryButton->setEnabled(true);
            queryButton->setText("查询");
            if (refreshButton) refreshButton->setEnabled(true);
            statusLabel->setText(message.isEmpty() ? "查询超时" : message);
            break;
    }
}
