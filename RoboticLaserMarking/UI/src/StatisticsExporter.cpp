#include "StatisticsExporter.h"
#include <QFile>
#include <QDir>
#include <QTextStream>
#include <QFileInfo>
#include <QStandardPaths>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QXmlStreamWriter>

StatisticsExporter::StatisticsExporter(std::shared_ptr<ProductionStatisticsManager> statsManager,
                                     QObject *parent)
    : QObject(parent)
    , m_statsManager(statsManager)
    , m_exportInProgress(false)
    , m_currentProgress(0)
{
    // Initialize field mappings
    m_fieldDisplayNames["recordId"] = "Record ID";
    m_fieldDisplayNames["startTime"] = "Start Time";
    m_fieldDisplayNames["endTime"] = "End Time";
    m_fieldDisplayNames["rfidCode"] = "RFID Code";
    m_fieldDisplayNames["templateName"] = "Template Name";
    m_fieldDisplayNames["shoeCode"] = "Shoe Code";
    m_fieldDisplayNames["isLeftFoot"] = "Is Left Foot";
    m_fieldDisplayNames["totalFaces"] = "Total Faces";
    m_fieldDisplayNames["completedFaces"] = "Completed Faces";
    m_fieldDisplayNames["status"] = "Status";
    m_fieldDisplayNames["processingTimeMs"] = "Processing Time (ms)";
    m_fieldDisplayNames["laserTimeMs"] = "Laser Time (ms)";
    m_fieldDisplayNames["efficiency"] = "Efficiency (faces/min)";
    m_fieldDisplayNames["successRate"] = "Success Rate";
    m_fieldDisplayNames["errorMessage"] = "Error Message";
    
    // Initialize available fields
    m_availableFields = m_fieldDisplayNames.keys();
    
    // Initialize default fields
    m_defaultFields << "startTime" << "templateName" << "shoeCode" << "isLeftFoot" 
                   << "totalFaces" << "completedFaces" << "status" << "processingTimeMs" 
                   << "efficiency" << "successRate";
    
    // Initialize format configurations
    m_formatExtensions[ExportFormat::CSV] = ".csv";
    m_formatExtensions[ExportFormat::JSON] = ".json";
    m_formatExtensions[ExportFormat::HTML] = ".html";
    m_formatExtensions[ExportFormat::XML] = ".xml";
    m_formatExtensions[ExportFormat::EXCEL] = ".xlsx";
    
    m_formatDescriptions[ExportFormat::CSV] = "Comma Separated Values";
    m_formatDescriptions[ExportFormat::JSON] = "JavaScript Object Notation";
    m_formatDescriptions[ExportFormat::HTML] = "HyperText Markup Language";
    m_formatDescriptions[ExportFormat::XML] = "eXtensible Markup Language";
    m_formatDescriptions[ExportFormat::EXCEL] = "Microsoft Excel";
}

StatisticsExporter::~StatisticsExporter()
{
}

bool StatisticsExporter::exportRecords(const ExportOptions& options)
{
    if (!validateExportOptions(options)) {
        emit exportError("Invalid export options");
        return false;
    }
    
    if (!m_statsManager) {
        emit exportError("Statistics manager not available");
        return false;
    }
    
    emit exportStarted(options.filePath);
    m_exportInProgress = true;
    resetProgress();
    
    // Get filtered records
    updateProgress(10, "Retrieving records...");
    QVector<ProductionRecord> records = m_statsManager->getRecords(options.filter);
    
    if (records.isEmpty()) {
        emit exportError("No records found matching the filter criteria");
        m_exportInProgress = false;
        return false;
    }
    
    updateProgress(30, "Processing records...");
    
    bool success = false;
    switch (options.format) {
        case ExportFormat::CSV:
            success = exportToCSV(options, records);
            break;
        case ExportFormat::JSON:
            success = exportToJSON(options, records);
            break;
        case ExportFormat::HTML:
            success = exportToHTML(options, records);
            break;
        case ExportFormat::XML:
            success = exportToXML(options, records);
            break;
        case ExportFormat::EXCEL:
            success = exportToExcel(options, records);
            break;
        default:
            emit exportError("Unsupported export format");
            m_exportInProgress = false;
            return false;
    }
    
    updateProgress(100, "Export completed");
    emit exportCompleted(options.filePath, success);
    m_exportInProgress = false;
    
    return success;
}

bool StatisticsExporter::exportSummary(const ExportOptions& options, const ProductionSummary& summary)
{
    if (!validateExportOptions(options)) {
        emit exportError("Invalid export options");
        return false;
    }
    
    emit exportStarted(options.filePath);
    m_exportInProgress = true;
    resetProgress();
    
    updateProgress(20, "Generating summary...");
    
    bool success = false;
    switch (options.format) {
        case ExportFormat::CSV:
            success = exportSummaryToCSV(options, summary);
            break;
        case ExportFormat::JSON:
            success = exportSummaryToJSON(options, summary);
            break;
        case ExportFormat::HTML:
            success = exportSummaryToHTML(options, summary);
            break;
        default:
            emit exportError("Format not supported for summary export");
            m_exportInProgress = false;
            return false;
    }
    
    updateProgress(100, "Summary export completed");
    emit exportCompleted(options.filePath, success);
    m_exportInProgress = false;
    
    return success;
}

bool StatisticsExporter::exportToCSV(const ExportOptions& options, const QVector<ProductionRecord>& records)
{
    QFile file(options.filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError("Cannot open file for writing: " + options.filePath);
        return false;
    }
    
    QTextStream out(&file);
    out.setCodec(options.encoding.toUtf8());
    
    updateProgress(40, "Writing CSV header...");
    
    // Write header if requested
    if (options.includeHeader) {
        QStringList headers;
        QStringList fieldsToExport = options.selectedFields.isEmpty() ? m_defaultFields : options.selectedFields;
        
        for (const QString& field : fieldsToExport) {
            headers << m_fieldDisplayNames.value(field, field);
        }
        out << headers.join(options.delimiter) << "\n";
    }
    
    updateProgress(50, "Writing CSV data...");
    
    // Write data rows
    QStringList fieldsToExport = options.selectedFields.isEmpty() ? m_defaultFields : options.selectedFields;
    int recordCount = records.size();
    
    for (int i = 0; i < recordCount; ++i) {
        const ProductionRecord& record = records[i];
        QStringList values;
        
        for (const QString& field : fieldsToExport) {
            QString value = formatFieldValue(record, field, options);
            values << escapeCSVValue(value, options.delimiter);
        }
        
        out << values.join(options.delimiter) << "\n";
        
        // Update progress
        if (i % 100 == 0) {
            int progress = 50 + (i * 40 / recordCount);
            updateProgress(progress, QString("Writing record %1 of %2").arg(i + 1).arg(recordCount));
        }
    }
    
    // Write metadata if requested
    if (options.includeMetadata) {
        out << "\n";
        out << "# Export Metadata\n";
        out << "# Export Time," << QDateTime::currentDateTime().toString(options.dateTimeFormat) << "\n";
        out << "# Total Records," << recordCount << "\n";
        out << "# Filter Start Time," << options.filter.startTime.toString(options.dateTimeFormat) << "\n";
        out << "# Filter End Time," << options.filter.endTime.toString(options.dateTimeFormat) << "\n";
    }
    
    file.close();
    updateProgress(90, "CSV export completed");
    return true;
}

bool StatisticsExporter::exportToJSON(const ExportOptions& options, const QVector<ProductionRecord>& records)
{
    updateProgress(40, "Generating JSON data...");
    
    QJsonObject rootObject;
    QJsonArray recordsArray;
    
    // Convert records to JSON
    for (const ProductionRecord& record : records) {
        QJsonObject recordObj = recordToJson(record);
        
        // Filter fields if specified
        if (!options.selectedFields.isEmpty()) {
            QJsonObject filteredObj;
            for (const QString& field : options.selectedFields) {
                if (recordObj.contains(field)) {
                    filteredObj[field] = recordObj[field];
                }
            }
            recordsArray.append(filteredObj);
        } else {
            recordsArray.append(recordObj);
        }
    }
    
    rootObject["records"] = recordsArray;
    
    // Add metadata if requested
    if (options.includeMetadata) {
        QJsonObject metadata;
        metadata["exportTime"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        metadata["totalRecords"] = records.size();
        metadata["filterStartTime"] = options.filter.startTime.toString(Qt::ISODate);
        metadata["filterEndTime"] = options.filter.endTime.toString(Qt::ISODate);
        metadata["exportFormat"] = "JSON";
        metadata["version"] = "1.0";
        
        rootObject["metadata"] = metadata;
    }
    
    updateProgress(70, "Writing JSON file...");
    
    // Write to file
    QJsonDocument doc(rootObject);
    QString jsonString = doc.toJson(QJsonDocument::Indented);
    
    bool success = writeToFile(options.filePath, jsonString, options.encoding);
    
    updateProgress(90, "JSON export completed");
    return success;
}

bool StatisticsExporter::exportToHTML(const ExportOptions& options, const QVector<ProductionRecord>& records)
{
    updateProgress(40, "Generating HTML content...");
    
    QString html = generateHTMLHeader("Production Statistics Report");
    
    // Add summary section if metadata is included
    if (options.includeMetadata) {
        html += "<div class='summary'>\n";
        html += "<h2>Export Summary</h2>\n";
        html += QString("<p>Export Time: %1</p>\n").arg(QDateTime::currentDateTime().toString(options.dateTimeFormat));
        html += QString("<p>Total Records: %1</p>\n").arg(records.size());
        html += QString("<p>Filter Period: %1 to %2</p>\n")
                .arg(options.filter.startTime.toString(options.dateTimeFormat))
                .arg(options.filter.endTime.toString(options.dateTimeFormat));
        html += "</div>\n";
    }
    
    updateProgress(60, "Generating HTML table...");
    
    // Add records table
    html += generateHTMLTable(records, options);
    
    html += generateHTMLFooter();
    
    updateProgress(80, "Writing HTML file...");
    
    bool success = writeToFile(options.filePath, html, options.encoding);
    
    updateProgress(90, "HTML export completed");
    return success;
}

QString StatisticsExporter::formatFieldValue(const ProductionRecord& record, const QString& fieldName, const ExportOptions& options) const
{
    if (fieldName == "recordId") {
        return record.recordId;
    } else if (fieldName == "startTime") {
        return formatDateTime(record.startTime, options.dateTimeFormat);
    } else if (fieldName == "endTime") {
        return formatDateTime(record.endTime, options.dateTimeFormat);
    } else if (fieldName == "rfidCode") {
        return record.rfidCode;
    } else if (fieldName == "templateName") {
        return record.templateName;
    } else if (fieldName == "shoeCode") {
        return QString::number(record.shoeCode);
    } else if (fieldName == "isLeftFoot") {
        return record.isLeftFoot ? "Left" : "Right";
    } else if (fieldName == "totalFaces") {
        return QString::number(record.totalFaces);
    } else if (fieldName == "completedFaces") {
        return QString::number(record.completedFaces);
    } else if (fieldName == "status") {
        return productionStatusToString(record.status);
    } else if (fieldName == "processingTimeMs") {
        return QString::number(record.processingTimeMs);
    } else if (fieldName == "laserTimeMs") {
        return QString::number(record.laserTimeMs);
    } else if (fieldName == "efficiency") {
        return QString::number(record.getEfficiency(), 'f', 2);
    } else if (fieldName == "successRate") {
        return QString::number(record.getSuccessRate() * 100, 'f', 1);
    } else if (fieldName == "errorMessage") {
        return record.errorMessage;
    }

    return "";
}

QString StatisticsExporter::escapeCSVValue(const QString& value, const QString& delimiter) const
{
    QString escaped = value;

    // If value contains delimiter, newline, or quote, wrap in quotes and escape quotes
    if (escaped.contains(delimiter) || escaped.contains('\n') || escaped.contains('\r') || escaped.contains('"')) {
        escaped.replace("\"", "\"\""); // Escape quotes by doubling them
        escaped = "\"" + escaped + "\"";
    }

    return escaped;
}

QString StatisticsExporter::formatDateTime(const QDateTime& dateTime, const QString& format) const
{
    if (!dateTime.isValid()) {
        return "";
    }
    return dateTime.toString(format);
}

QJsonObject StatisticsExporter::recordToJson(const ProductionRecord& record) const
{
    QJsonObject obj;

    obj["recordId"] = record.recordId;
    obj["startTime"] = record.startTime.toString(Qt::ISODate);
    obj["endTime"] = record.endTime.toString(Qt::ISODate);
    obj["rfidCode"] = record.rfidCode;
    obj["templateName"] = record.templateName;
    obj["shoeCode"] = record.shoeCode;
    obj["isLeftFoot"] = record.isLeftFoot;
    obj["totalFaces"] = record.totalFaces;
    obj["completedFaces"] = record.completedFaces;
    obj["status"] = static_cast<int>(record.status);
    obj["statusText"] = productionStatusToString(record.status);
    obj["processingTimeMs"] = static_cast<qint64>(record.processingTimeMs);
    obj["laserTimeMs"] = static_cast<qint64>(record.laserTimeMs);
    obj["efficiency"] = record.getEfficiency();
    obj["successRate"] = record.getSuccessRate();
    obj["errorMessage"] = record.errorMessage;

    return obj;
}

QString StatisticsExporter::generateHTMLHeader(const QString& title) const
{
    QString html = "<!DOCTYPE html>\n";
    html += "<html>\n<head>\n";
    html += "<meta charset=\"UTF-8\">\n";
    html += QString("<title>%1</title>\n").arg(title);
    html += "<style>\n";
    html += "body { font-family: Arial, sans-serif; margin: 20px; }\n";
    html += "h1, h2 { color: #2c3e50; }\n";
    html += "table { border-collapse: collapse; width: 100%; margin: 20px 0; }\n";
    html += "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
    html += "th { background-color: #f2f2f2; font-weight: bold; }\n";
    html += "tr:nth-child(even) { background-color: #f9f9f9; }\n";
    html += ".summary { background-color: #e8f4f8; padding: 15px; border-radius: 5px; margin: 20px 0; }\n";
    html += ".footer { margin-top: 30px; font-size: 12px; color: #666; }\n";
    html += "</style>\n";
    html += "</head>\n<body>\n";
    html += QString("<h1>%1</h1>\n").arg(title);

    return html;
}

QString StatisticsExporter::generateHTMLFooter() const
{
    QString html = "<div class='footer'>\n";
    html += QString("<p>Report generated on %1</p>\n").arg(QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    html += "<p>Generated by Production Statistics System</p>\n";
    html += "</div>\n";
    html += "</body>\n</html>";

    return html;
}

QString StatisticsExporter::generateHTMLTable(const QVector<ProductionRecord>& records, const ExportOptions& options) const
{
    QString html = "<table>\n";

    // Generate header
    if (options.includeHeader) {
        html += "<thead>\n<tr>\n";
        QStringList fieldsToExport = options.selectedFields.isEmpty() ? m_defaultFields : options.selectedFields;

        for (const QString& field : fieldsToExport) {
            html += QString("<th>%1</th>\n").arg(m_fieldDisplayNames.value(field, field));
        }
        html += "</tr>\n</thead>\n";
    }

    // Generate body
    html += "<tbody>\n";
    QStringList fieldsToExport = options.selectedFields.isEmpty() ? m_defaultFields : options.selectedFields;

    for (const ProductionRecord& record : records) {
        html += "<tr>\n";
        for (const QString& field : fieldsToExport) {
            QString value = formatFieldValue(record, field, options);
            html += QString("<td>%1</td>\n").arg(value.toHtmlEscaped());
        }
        html += "</tr>\n";
    }

    html += "</tbody>\n</table>\n";
    return html;
}

bool StatisticsExporter::writeToFile(const QString& filePath, const QString& content, const QString& encoding) const
{
    // Create directory if it doesn't exist
    QFileInfo fileInfo(filePath);
    if (!createDirectory(fileInfo.absolutePath())) {
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream out(&file);
    out.setCodec(encoding.toUtf8());
    out << content;

    file.close();
    return true;
}

bool StatisticsExporter::createDirectory(const QString& dirPath) const
{
    QDir dir;
    return dir.mkpath(dirPath);
}

bool StatisticsExporter::validateExportOptions(const ExportOptions& options) const
{
    if (options.filePath.isEmpty()) {
        return false;
    }

    if (options.delimiter.isEmpty() && options.format == ExportFormat::CSV) {
        return false;
    }

    return true;
}

QStringList StatisticsExporter::getAvailableFields() const
{
    return m_availableFields;
}

QString StatisticsExporter::getDefaultFileName(ExportFormat format, const StatisticsFilter& filter) const
{
    QString baseName = "production_statistics";

    if (filter.startTime.isValid()) {
        baseName += "_" + filter.startTime.toString("yyyyMMdd");
    }
    if (filter.endTime.isValid()) {
        baseName += "_to_" + filter.endTime.toString("yyyyMMdd");
    }

    return baseName + getFormatExtension(format);
}

QString StatisticsExporter::getFormatExtension(ExportFormat format) const
{
    return m_formatExtensions.value(format, ".txt");
}

QString StatisticsExporter::getFormatDescription(ExportFormat format) const
{
    return m_formatDescriptions.value(format, "Unknown Format");
}

void StatisticsExporter::updateProgress(int percentage, const QString& message)
{
    m_currentProgress = percentage;
    m_currentOperation = message;
    emit exportProgress(percentage, message);
}

void StatisticsExporter::resetProgress()
{
    m_currentProgress = 0;
    m_currentOperation.clear();
}

// Private method implementations
bool StatisticsExporter::exportToXML(const ExportOptions& options, const QVector<ProductionRecord>& records)
{
    QFile file(options.filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError("Cannot open file for writing: " + options.filePath);
        return false;
    }

    QXmlStreamWriter writer(&file);
    writer.setAutoFormatting(true);
    writer.writeStartDocument();

    writer.writeStartElement("ProductionRecords");
    writer.writeAttribute("exportDate", QDateTime::currentDateTime().toString(Qt::ISODate));
    writer.writeAttribute("recordCount", QString::number(records.size()));

    updateProgress(30, "Writing XML records...");

    for (int i = 0; i < records.size(); ++i) {
        const ProductionRecord& record = records[i];

        writer.writeStartElement("Record");
        writer.writeAttribute("id", record.recordId);

        writer.writeTextElement("StartTime", record.startTime.toString(Qt::ISODate));
        writer.writeTextElement("EndTime", record.endTime.toString(Qt::ISODate));
        writer.writeTextElement("TemplateName", record.templateName);
        writer.writeTextElement("RFIDCode", record.rfidCode);
        writer.writeTextElement("ShoeCode", QString::number(record.shoeCode));
        writer.writeTextElement("IsLeftFoot", record.isLeftFoot ? "true" : "false");
        writer.writeTextElement("TotalFaces", QString::number(record.totalFaces));
        writer.writeTextElement("CompletedFaces", QString::number(record.completedFaces));
        writer.writeTextElement("Status", productionStatusToString(record.status));
        writer.writeTextElement("ProcessingTime", QString::number(record.processingTimeMs));
        writer.writeTextElement("LaserTime", QString::number(record.laserTimeMs));
        writer.writeTextElement("Efficiency", QString::number(record.getEfficiency(), 'f', 2));
        writer.writeTextElement("SuccessRate", QString::number(record.getSuccessRate(), 'f', 2));

        if (!record.errorMessage.isEmpty()) {
            writer.writeTextElement("ErrorMessage", record.errorMessage);
        }

        writer.writeEndElement(); // Record

        if (i % 100 == 0) {
            updateProgress(30 + (i * 60 / records.size()), "Writing XML records...");
        }
    }

    writer.writeEndElement(); // ProductionRecords
    writer.writeEndDocument();

    updateProgress(100, "XML export completed");
    return true;
}

bool StatisticsExporter::exportToExcel(const ExportOptions& options, const QVector<ProductionRecord>& records)
{
    // For now, export as CSV since Excel support requires additional libraries
    ExportOptions csvOptions = options;
    csvOptions.format = ExportFormat::CSV;
    QString csvPath = options.filePath;
    csvPath.replace(".xlsx", ".csv").replace(".xls", ".csv");
    csvOptions.filePath = csvPath;

    emit exportError("Excel export not yet implemented. Exporting as CSV instead: " + csvOptions.filePath);
    return exportToCSV(csvOptions, records);
}

bool StatisticsExporter::exportSummaryToCSV(const ExportOptions& options, const ProductionSummary& summary)
{
    QFile file(options.filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError("Cannot open file for writing: " + options.filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    updateProgress(20, "Writing CSV summary...");

    // Write header
    out << "Metric,Value,Unit\n";

    // Write summary data
    out << "Export Date," << QDateTime::currentDateTime().toString(Qt::ISODate) << ",\n";
    out << "Total Records," << summary.totalRecords << ",count\n";
    out << "Successful Records," << summary.successfulRecords << ",count\n";
    out << "Failed Records," << summary.failedRecords << ",count\n";
    out << "Success Rate," << QString::number(summary.successRate * 100, 'f', 2) << ",%\n";
    out << "Total Faces," << summary.totalFaces << ",count\n";
    out << "Average Efficiency," << QString::number(summary.averageEfficiency, 'f', 2) << ",faces/min\n";
    out << "Total Processing Time," << summary.totalProcessingTime << ",ms\n";
    out << "Total Laser Time," << summary.totalLaserTime << ",ms\n";
    out << "Utilization Rate," << QString::number(summary.getUtilizationRate() * 100, 'f', 2) << ",%\n";
    out << "Average Time Per Face," << QString::number(summary.getAverageTimePerFace(), 'f', 2) << ",ms\n";

    updateProgress(100, "CSV summary export completed");
    return true;
}

bool StatisticsExporter::exportSummaryToJSON(const ExportOptions& options, const ProductionSummary& summary)
{
    QFile file(options.filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError("Cannot open file for writing: " + options.filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    updateProgress(20, "Writing JSON summary...");

    // Create JSON object
    QJsonObject jsonObj;
    jsonObj["exportDate"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    jsonObj["totalRecords"] = summary.totalRecords;
    jsonObj["successfulRecords"] = summary.successfulRecords;
    jsonObj["failedRecords"] = summary.failedRecords;
    jsonObj["successRate"] = summary.successRate;
    jsonObj["totalFaces"] = summary.totalFaces;
    jsonObj["averageEfficiency"] = summary.averageEfficiency;
    jsonObj["totalProcessingTime"] = static_cast<qint64>(summary.totalProcessingTime);
    jsonObj["totalLaserTime"] = static_cast<qint64>(summary.totalLaserTime);
    jsonObj["utilizationRate"] = summary.getUtilizationRate();
    jsonObj["averageTimePerFace"] = summary.getAverageTimePerFace();

    QJsonDocument doc(jsonObj);
    out << doc.toJson();

    updateProgress(100, "JSON summary export completed");
    return true;
}

bool StatisticsExporter::exportSummaryToHTML(const ExportOptions& options, const ProductionSummary& summary)
{
    QFile file(options.filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit exportError("Cannot open file for writing: " + options.filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    updateProgress(20, "Writing HTML summary...");

    // Write HTML document
    out << "<!DOCTYPE html>\n";
    out << "<html>\n<head>\n";
    out << "<title>Production Summary Report</title>\n";
    out << "<style>\n";
    out << "body { font-family: Arial, sans-serif; margin: 20px; }\n";
    out << "table { border-collapse: collapse; width: 100%; }\n";
    out << "th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n";
    out << "th { background-color: #f2f2f2; }\n";
    out << "tr:nth-child(even) { background-color: #f9f9f9; }\n";
    out << ".metric-value { font-weight: bold; color: #2c5aa0; }\n";
    out << "</style>\n";
    out << "</head>\n<body>\n";

    out << "<h1>Production Summary Report</h1>\n";
    out << "<p><strong>Generated:</strong> " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "</p>\n";

    out << "<h2>Key Metrics</h2>\n";
    out << "<table>\n";
    out << "<tr><th>Metric</th><th>Value</th></tr>\n";
    out << "<tr><td>Total Records</td><td class='metric-value'>" << summary.totalRecords << "</td></tr>\n";
    out << "<tr><td>Successful Records</td><td class='metric-value'>" << summary.successfulRecords << "</td></tr>\n";
    out << "<tr><td>Failed Records</td><td class='metric-value'>" << summary.failedRecords << "</td></tr>\n";
    out << "<tr><td>Success Rate</td><td class='metric-value'>" << QString::number(summary.successRate * 100, 'f', 2) << "%</td></tr>\n";
    out << "<tr><td>Total Faces</td><td class='metric-value'>" << summary.totalFaces << "</td></tr>\n";
    out << "<tr><td>Average Efficiency</td><td class='metric-value'>" << QString::number(summary.averageEfficiency, 'f', 2) << " faces/min</td></tr>\n";
    out << "<tr><td>Total Processing Time</td><td class='metric-value'>" << formatDuration(summary.totalProcessingTime) << "</td></tr>\n";
    out << "<tr><td>Total Laser Time</td><td class='metric-value'>" << formatDuration(summary.totalLaserTime) << "</td></tr>\n";
    out << "<tr><td>Utilization Rate</td><td class='metric-value'>" << QString::number(summary.getUtilizationRate() * 100, 'f', 2) << "%</td></tr>\n";
    out << "<tr><td>Average Time Per Face</td><td class='metric-value'>" << QString::number(summary.getAverageTimePerFace(), 'f', 2) << " ms</td></tr>\n";
    out << "</table>\n";

    out << "</body>\n</html>\n";

    updateProgress(100, "HTML summary export completed");
    return true;
}

QString StatisticsExporter::formatDuration(qint64 milliseconds) const
{
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    qint64 hours = minutes / 60;

    seconds %= 60;
    minutes %= 60;

    if (hours > 0) {
        return QString("%1h %2m %3s").arg(hours).arg(minutes).arg(seconds);
    } else if (minutes > 0) {
        return QString("%1m %2s").arg(minutes).arg(seconds);
    } else {
        return QString("%1s").arg(seconds);
    }
}
