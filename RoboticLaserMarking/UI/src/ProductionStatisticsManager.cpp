#include "ProductionStatisticsManager.h"
#include "DatabaseQueryWorker.h"
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonArray>
#include <QSqlRecord>
#include <QDebug>
#include <QApplication>
#include <QThread>
#include <QElapsedTimer>

ProductionStatisticsManager::ProductionStatisticsManager(QObject *parent)
    : QObject(parent)
    , m_autoSaveIntervalMinutes(5)
    , m_maxRecordsInMemory(10000)
    , m_realTimeMonitoringInterval(1000)
    , m_autoSaveTimer(new QTimer(this))
    , m_realTimeMonitoringTimer(new QTimer(this))
    , m_dbQueryThread(nullptr)
    , m_dbQueryWorker(nullptr)
    , m_initialized(false)
    , m_realTimeMonitoringActive(false)
{
    // Setup auto-save timer
    m_autoSaveTimer->setSingleShot(false);
    connect(m_autoSaveTimer, &QTimer::timeout, this, &ProductionStatisticsManager::onAutoSaveTimer);
    
    // Setup real-time monitoring timer
    m_realTimeMonitoringTimer->setSingleShot(false);
    connect(m_realTimeMonitoringTimer, &QTimer::timeout, this, &ProductionStatisticsManager::onRealTimeMonitoringTimer);
}

ProductionStatisticsManager::~ProductionStatisticsManager()
{
    // Stop and cleanup database query thread
    if (m_dbQueryThread) {
        m_dbQueryThread->stopWorker();
        delete m_dbQueryThread;
        m_dbQueryThread = nullptr;
    }

    if (m_initialized) {
        // Save any pending data
        saveRecordsToFile();

        // Close database
        if (m_database.isOpen()) {
            m_database.close();
        }
    }
}

bool ProductionStatisticsManager::initialize(const QString& dataDirectory)
{
    QMutexLocker locker(&m_dataMutex);
    
    // Set data directory
    if (dataDirectory.isEmpty()) {
        m_dataDirectory = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/Statistics";
    } else {
        m_dataDirectory = dataDirectory;
    }
    
    // Create directory if it doesn't exist
    QDir dir;
    if (!dir.mkpath(m_dataDirectory)) {
        QString error = "Failed to create statistics data directory: " + m_dataDirectory;
        qDebug() << error;
        emit errorOccurred(error);
        return false;
    }

    qDebug() << "Statistics data directory created/verified:" << m_dataDirectory;

    // Initialize database
    if (!initializeDatabase()) {
        QString error = "Failed to initialize statistics database";
        qDebug() << error;
        emit errorOccurred(error);
        return false;
    }

    qDebug() << "Statistics database initialized successfully";
    
    // Load existing records
    if (!loadRecordsFromFile()) {
        qDebug() << "No existing statistics file found, starting fresh";
    }
    
    // Start auto-save timer
    m_autoSaveTimer->start(m_autoSaveIntervalMinutes * 60 * 1000);

    // Initialize database query thread
    initializeDatabaseQueryThread();

    m_initialized = true;
    return true;
}

QString ProductionStatisticsManager::startProductionRecord(const QString& rfidCode,
                                                          const QString& templateName,
                                                          int shoeCode,
                                                          bool isLeftFoot,
                                                          int totalFaces)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (!m_initialized) {
        emit errorOccurred("Statistics manager not initialized");
        return QString();
    }
    
    // Create new record
    ProductionRecord record;
    record.recordId = generateRecordId();
    record.startTime = QDateTime::currentDateTime();
    record.rfidCode = rfidCode;
    record.templateName = templateName;
    record.shoeCode = shoeCode;
    record.isLeftFoot = isLeftFoot;
    record.totalFaces = totalFaces;
    record.completedFaces = 0;
    record.status = ProductionStatus::SUCCESS;
    record.processingTimeMs = 0;
    record.laserTimeMs = 0;
    
    // Validate record
    if (!validateRecord(record)) {
        emit errorOccurred("Invalid production record data");
        return QString();
    }
    
    // Add to active records
    m_activeRecords[record.recordId] = record;
    
    // Emit signal
    emit recordStarted(record.recordId, record);
    
    return record.recordId;
}

bool ProductionStatisticsManager::updateProductionRecord(const QString& recordId,
                                                        int completedFaces,
                                                        ProductionStatus status,
                                                        const QString& errorMessage)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (!m_activeRecords.contains(recordId)) {
        emit errorOccurred("Production record not found: " + recordId);
        return false;
    }
    
    ProductionRecord& record = m_activeRecords[recordId];
    record.completedFaces = completedFaces;
    record.status = status;
    if (!errorMessage.isEmpty()) {
        record.errorMessage = errorMessage;
    }
    
    // Update processing time
    if (record.startTime.isValid()) {
        record.processingTimeMs = record.startTime.msecsTo(QDateTime::currentDateTime());
    }
    
    // Emit signal
    emit recordUpdated(recordId, record);
    
    return true;
}

bool ProductionStatisticsManager::finishProductionRecord(const QString& recordId,
                                                        ProductionStatus status,
                                                        const QString& errorMessage)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (!m_activeRecords.contains(recordId)) {
        emit errorOccurred("Production record not found: " + recordId);
        return false;
    }
    
    ProductionRecord& record = m_activeRecords[recordId];
    record.endTime = QDateTime::currentDateTime();
    record.status = status;
    if (!errorMessage.isEmpty()) {
        record.errorMessage = errorMessage;
    }
    
    // Calculate final processing time
    if (record.startTime.isValid()) {
        record.processingTimeMs = record.startTime.msecsTo(record.endTime);
    }
    
    // Save to database
    if (!saveRecordToDatabase(record)) {
        emit errorOccurred("Failed to save production record to database");
    }
    
    // Add to cached records
    m_cachedRecords.append(record);
    
    // Remove from active records
    m_activeRecords.remove(recordId);
    
    // Limit cached records size
    if (m_cachedRecords.size() > m_maxRecordsInMemory) {
        m_cachedRecords.removeFirst();
    }
    
    // Clear summary cache
    m_summaryCache.clear();
    
    // Emit signals
    emit recordFinished(recordId, record);
    emit statisticsUpdated();
    
    return true;
}

bool ProductionStatisticsManager::addCustomData(const QString& recordId,
                                               const QString& key,
                                               const QVariant& value)
{
    QMutexLocker locker(&m_dataMutex);
    
    if (!m_activeRecords.contains(recordId)) {
        emit errorOccurred("Production record not found: " + recordId);
        return false;
    }
    
    m_activeRecords[recordId].customData[key] = value;
    return true;
}

QVector<ProductionRecord> ProductionStatisticsManager::getRecords(const StatisticsFilter& filter) const
{
    QMutexLocker locker(&m_dataMutex);

    QVector<ProductionRecord> result;

    // Debug logging for Release mode troubleshooting
    qDebug() << "=== ProductionStatisticsManager::getRecords() DEBUG ===";
    qDebug() << "Cached records count:" << m_cachedRecords.size();
    qDebug() << "Active records count:" << m_activeRecords.size();
    qDebug() << "Database open:" << m_database.isOpen();
    qDebug() << "Database valid:" << m_database.isValid();
    qDebug() << "Database path:" << m_databasePath;
    qDebug() << "Data directory:" << m_dataDirectory;

    // Add cached records
    int cachedMatches = 0;
    for (const auto& record : m_cachedRecords) {
        if (applyFilter(record, filter)) {
            result.append(record);
            cachedMatches++;
        }
    }
    qDebug() << "Cached records matching filter:" << cachedMatches;

    // Add active records if they match filter
    int activeMatches = 0;
    for (const auto& record : m_activeRecords) {
        if (applyFilter(record, filter)) {
            result.append(record);
            activeMatches++;
        }
    }
    qDebug() << "Active records matching filter:" << activeMatches;
    
    // Only load from database if we have very few cached records and filter is not too broad
    bool shouldLoadFromDB = false;
    if (result.size() < 50) { // Much lower threshold
        // Only load from DB if we have specific filters (avoid loading all data)
        if (filter.startTime.isValid() || filter.endTime.isValid() ||
            !filter.templateNames.isEmpty() || !filter.shoeCodes.isEmpty()) {
            shouldLoadFromDB = true;
        }
    }

    qDebug() << "Current result size:" << result.size();
    qDebug() << "Should load from DB:" << shouldLoadFromDB;

    if (shouldLoadFromDB) {
        try {
            // Check and recover database connection if needed
            if (!m_database.isOpen() || !m_database.isValid()) {
                qDebug() << "Database connection lost, attempting to reconnect...";
                if (!const_cast<ProductionStatisticsManager*>(this)->initializeDatabase()) {
                    qDebug() << "Failed to reconnect to database";
                    return result; // Return cached results only
                }
                qDebug() << "Database reconnected successfully";
            }

            qDebug() << "About to call loadRecordsFromDatabase()...";
            QVector<ProductionRecord> dbRecords = loadRecordsFromDatabase(filter);
            qDebug() << "loadRecordsFromDatabase() returned" << dbRecords.size() << "records";

            int duplicatesSkipped = 0;
            for (const auto& record : dbRecords) {
                // Avoid duplicates
                bool found = false;
                for (const auto& existing : result) {
                    if (existing.recordId == record.recordId) {
                        found = true;
                        duplicatesSkipped++;
                        break;
                    }
                }
                if (!found) {
                    result.append(record);
                }
            }
            qDebug() << "Added" << (dbRecords.size() - duplicatesSkipped) << "new records from database," << duplicatesSkipped << "duplicates skipped";
        } catch (const std::exception& e) {
            qDebug() << "Error loading records from database:" << e.what();
            // Try to recover database connection for next time
            ProductionStatisticsManager* nonConstThis = const_cast<ProductionStatisticsManager*>(this);
            QTimer::singleShot(5000, nonConstThis, [nonConstThis]() {
                if (!nonConstThis->m_database.isOpen()) {
                    qDebug() << "Attempting delayed database recovery...";
                    nonConstThis->initializeDatabase();
                }
            });
        } catch (...) {
            qDebug() << "Unknown error loading records from database";
        }
    }
    
    // Sort by start time (newest first)
    std::sort(result.begin(), result.end(), [](const ProductionRecord& a, const ProductionRecord& b) {
        return a.startTime > b.startTime;
    });
    
    return result;
}

ProductionRecord ProductionStatisticsManager::getRecord(const QString& recordId) const
{
    QMutexLocker locker(&m_dataMutex);
    
    // Check active records first
    if (m_activeRecords.contains(recordId)) {
        return m_activeRecords[recordId];
    }
    
    // Check cached records
    for (const auto& record : m_cachedRecords) {
        if (record.recordId == recordId) {
            return record;
        }
    }
    
    // Load from database
    StatisticsFilter filter;
    QVector<ProductionRecord> dbRecords = loadRecordsFromDatabase(filter);
    for (const auto& record : dbRecords) {
        if (record.recordId == recordId) {
            return record;
        }
    }
    
    return ProductionRecord(); // Return empty record if not found
}

ProductionSummary ProductionStatisticsManager::getSummary(const StatisticsFilter& filter) const
{
    QMutexLocker locker(&m_dataMutex);

    // Check cache first with more detailed cache key
    QString cacheKey = QString("summary_%1_%2_%3_%4")
                      .arg(filter.startTime.toString(Qt::ISODate))
                      .arg(filter.endTime.toString(Qt::ISODate))
                      .arg(filter.templateNames.join(","))
                      .arg(filter.shoeCodes.size());

    // Use cached result if it's less than 30 seconds old (reduced cache time for better responsiveness)
    if (m_summaryCache.contains(cacheKey) &&
        m_lastCacheUpdate.secsTo(QDateTime::currentDateTime()) < 30) {
        return m_summaryCache[cacheKey];
    }

    // Calculate summary with limited records for performance
    QVector<ProductionRecord> records = getRecords(filter);

    // Limit records for summary calculation to prevent performance issues
    if (records.size() > 1000) {
        records = records.mid(0, 1000);
        qDebug() << "Limited summary calculation to 1000 most recent records for performance";
    }

    ProductionSummary summary = calculateSummary(records);

    // Cache result with size limit
    if (m_summaryCache.size() > 50) {
        m_summaryCache.clear(); // Clear cache if it gets too large
    }
    m_summaryCache[cacheKey] = summary;
    m_lastCacheUpdate = QDateTime::currentDateTime();

    return summary;
}

ProductionSummary ProductionStatisticsManager::getPeriodSummary(StatisticsPeriod period,
                                                               const QDateTime& referenceTime) const
{
    StatisticsFilter filter;
    filter.startTime = getPeriodStartTime(referenceTime, period);
    filter.endTime = getPeriodEndTime(referenceTime, period);

    return getSummary(filter);
}

ChartData ProductionStatisticsManager::getProductionTrend(StatisticsPeriod period,
                                                         int periodCount,
                                                         const QDateTime& endTime) const
{
    ChartData chartData;
    chartData.type = ChartType::LINE_CHART;
    chartData.title = QString("Production Trend (%1)").arg(statisticsPeriodToString(period));
    chartData.xAxisLabel = "Time";
    chartData.yAxisLabel = "Production Count";

    TrendDataSeries trendSeries;

    for (int i = periodCount - 1; i >= 0; --i) {
        QDateTime periodEnd = endTime.addSecs(-i * getPeriodSeconds(period));
        QDateTime periodStart = getPeriodStartTime(periodEnd, period);

        StatisticsFilter filter;
        filter.startTime = periodStart;
        filter.endTime = periodEnd;

        ProductionSummary summary = getSummary(filter);

        TrendDataPoint point;
        point.timestamp = periodStart;
        point.value = summary.successfulRecords;
        point.label = periodStart.toString("MM-dd");

        trendSeries.append(point);
    }

    chartData.series["Production"] = trendSeries;
    return chartData;
}

ChartData ProductionStatisticsManager::getEfficiencyAnalysis(const StatisticsFilter& filter) const
{
    ChartData chartData;
    chartData.type = ChartType::SCATTER_CHART;
    chartData.title = "Efficiency Analysis";
    chartData.xAxisLabel = "Processing Time (minutes)";
    chartData.yAxisLabel = "Faces Completed";

    QVector<ProductionRecord> records = getRecords(filter);
    TrendDataSeries efficiencySeries;

    for (const auto& record : records) {
        if (record.isComplete()) {
            TrendDataPoint point;
            point.timestamp = record.startTime;
            point.value = record.completedFaces;
            point.label = QString("%1 faces in %2 min")
                         .arg(record.completedFaces)
                         .arg(record.processingTimeMs / 60000.0, 0, 'f', 1);

            efficiencySeries.append(point);
        }
    }

    chartData.series["Efficiency"] = efficiencySeries;
    return chartData;
}

ChartData ProductionStatisticsManager::getTemplateUsageChart(const StatisticsFilter& filter) const
{
    ChartData chartData;
    chartData.type = ChartType::PIE_CHART;
    chartData.title = "Template Usage Distribution";

    QVector<ProductionRecord> records = getRecords(filter);
    QMap<QString, int> templateCounts;

    for (const auto& record : records) {
        templateCounts[record.templateName]++;
    }

    TrendDataSeries templateSeries;
    for (auto it = templateCounts.begin(); it != templateCounts.end(); ++it) {
        TrendDataPoint point;
        point.value = it.value();
        point.label = QString("%1 (%2)").arg(it.key()).arg(it.value());
        templateSeries.append(point);
    }

    chartData.series["Templates"] = templateSeries;
    return chartData;
}

ChartData ProductionStatisticsManager::getStatusDistribution(const StatisticsFilter& filter) const
{
    ChartData chartData;
    chartData.type = ChartType::PIE_CHART;
    chartData.title = "Production Status Distribution";

    QVector<ProductionRecord> records = getRecords(filter);
    QMap<ProductionStatus, int> statusCounts;

    for (const auto& record : records) {
        statusCounts[record.status]++;
    }

    TrendDataSeries statusSeries;
    for (auto it = statusCounts.begin(); it != statusCounts.end(); ++it) {
        TrendDataPoint point;
        point.value = it.value();
        point.label = QString("%1 (%2)")
                     .arg(productionStatusToString(it.key()))
                     .arg(it.value());
        statusSeries.append(point);
    }

    chartData.series["Status"] = statusSeries;
    return chartData;
}

ChartData ProductionStatisticsManager::getShoeSizeDistribution(const StatisticsFilter& filter) const
{
    ChartData chartData;
    chartData.type = ChartType::BAR_CHART;
    chartData.title = "Shoe Size Distribution";
    chartData.xAxisLabel = "Shoe Size";
    chartData.yAxisLabel = "Count";

    QVector<ProductionRecord> records = getRecords(filter);
    QMap<int, int> sizeCounts;

    for (const auto& record : records) {
        sizeCounts[record.shoeCode]++;
    }

    TrendDataSeries sizeSeries;
    for (auto it = sizeCounts.begin(); it != sizeCounts.end(); ++it) {
        TrendDataPoint point;
        point.value = it.value();
        point.label = QString("Size %1").arg(it.key());
        sizeSeries.append(point);
    }

    chartData.series["Sizes"] = sizeSeries;
    return chartData;
}

bool ProductionStatisticsManager::exportToCSV(const QString& filePath,
                                             const StatisticsFilter& filter) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit const_cast<ProductionStatisticsManager*>(this)->errorOccurred("Cannot open file for writing: " + filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    // Write header
    out << "Record ID,Start Time,End Time,RFID Code,Template Name,Shoe Code,Is Left Foot,"
        << "Total Faces,Completed Faces,Status,Processing Time (ms),Laser Time (ms),"
        << "Efficiency (faces/min),Success Rate,Error Message\n";

    // Write data
    QVector<ProductionRecord> records = getRecords(filter);
    for (const auto& record : records) {
        out << record.recordId << ","
            << record.startTime.toString(Qt::ISODate) << ","
            << record.endTime.toString(Qt::ISODate) << ","
            << record.rfidCode << ","
            << record.templateName << ","
            << record.shoeCode << ","
            << (record.isLeftFoot ? "Yes" : "No") << ","
            << record.totalFaces << ","
            << record.completedFaces << ","
            << productionStatusToString(record.status) << ","
            << record.processingTimeMs << ","
            << record.laserTimeMs << ","
            << QString::number(record.getEfficiency(), 'f', 2) << ","
            << QString::number(record.getSuccessRate(), 'f', 2) << ","
            << record.errorMessage << "\n";
    }

    file.close();
    return true;
}

bool ProductionStatisticsManager::exportSummaryToCSV(const QString& filePath,
                                                    StatisticsPeriod period,
                                                    int periodCount,
                                                    const QDateTime& endTime) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit const_cast<ProductionStatisticsManager*>(this)->errorOccurred("Cannot open file for writing: " + filePath);
        return false;
    }

    QTextStream out(&file);
    out.setCodec("UTF-8");

    // Write header
    out << "Period,Start Time,End Time,Total Records,Successful Records,Failed Records,"
        << "Total Faces,Total Processing Time (ms),Average Efficiency,Success Rate,Utilization Rate\n";

    // Write data for each period
    for (int i = periodCount - 1; i >= 0; --i) {
        QDateTime periodEnd = endTime.addSecs(-i * getPeriodSeconds(period));
        QDateTime periodStart = getPeriodStartTime(periodEnd, period);

        ProductionSummary summary = getPeriodSummary(period, periodEnd);

        out << statisticsPeriodToString(period) << ","
            << periodStart.toString(Qt::ISODate) << ","
            << periodEnd.toString(Qt::ISODate) << ","
            << summary.totalRecords << ","
            << summary.successfulRecords << ","
            << summary.failedRecords << ","
            << summary.totalFaces << ","
            << summary.totalProcessingTime << ","
            << QString::number(summary.averageEfficiency, 'f', 2) << ","
            << QString::number(summary.successRate, 'f', 2) << ","
            << QString::number(summary.getUtilizationRate(), 'f', 2) << "\n";
    }

    file.close();
    return true;
}

void ProductionStatisticsManager::startRealTimeMonitoring(int intervalMs)
{
    m_realTimeMonitoringInterval = intervalMs;
    m_realTimeMonitoringTimer->start(intervalMs);
    m_realTimeMonitoringActive = true;
}

void ProductionStatisticsManager::stopRealTimeMonitoring()
{
    m_realTimeMonitoringTimer->stop();
    m_realTimeMonitoringActive = false;
}

void ProductionStatisticsManager::setAutoSaveInterval(int minutes)
{
    m_autoSaveIntervalMinutes = minutes;
    if (m_autoSaveTimer->isActive()) {
        m_autoSaveTimer->start(minutes * 60 * 1000);
    }
}

void ProductionStatisticsManager::setMaxRecordsInMemory(int maxRecords)
{
    m_maxRecordsInMemory = qMax(100, maxRecords); // Ensure minimum of 100 records

    // Trim cached records if necessary
    QMutexLocker locker(&m_dataMutex);
    trimCachedRecords();
}

void ProductionStatisticsManager::trimCachedRecords()
{
    // Remove oldest records if we exceed the limit
    while (m_cachedRecords.size() > m_maxRecordsInMemory) {
        m_cachedRecords.removeFirst();
    }

    // Also clean up summary cache if it gets too large
    if (m_summaryCache.size() > 100) {
        m_summaryCache.clear();
        qDebug() << "Cleared summary cache due to size limit";
    }
}

int ProductionStatisticsManager::getTotalRecordsCount() const
{
    QMutexLocker locker(&m_dataMutex);

    // Count from database
    QSqlQuery query(m_database);
    query.prepare("SELECT COUNT(*) FROM production_records");

    if (query.exec() && query.next()) {
        return query.value(0).toInt() + m_activeRecords.size();
    }

    return m_cachedRecords.size() + m_activeRecords.size();
}

int ProductionStatisticsManager::getActiveRecordsCount() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_activeRecords.size();
}

void ProductionStatisticsManager::onAutoSaveTimer()
{
    saveRecordsToFile();

    // Perform memory cleanup during auto-save
    QMutexLocker locker(&m_dataMutex);
    trimCachedRecords();
}

void ProductionStatisticsManager::onRealTimeMonitoringTimer()
{
    if (!m_realTimeMonitoringActive || !m_initialized) {
        return;
    }

    // Prevent overlapping updates by checking if previous update is still processing
    static bool isUpdating = false;
    if (isUpdating) {
        qDebug() << "Skipping real-time update - previous update still in progress";
        return;
    }

    isUpdating = true;

    try {
        // Check database connection before querying
        if (!m_database.isOpen() || !m_database.isValid()) {
            qDebug() << "Database not available for real-time monitoring";
            isUpdating = false;
            return;
        }

        // Use a simplified approach to avoid blocking UI
        ProductionSummary currentSummary;
        currentSummary.totalRecords = m_cachedRecords.size() + m_activeRecords.size();
        currentSummary.successfulRecords = 0;
        currentSummary.failedRecords = 0;

        // Count from cached records only to avoid database query
        for (const auto& record : m_cachedRecords) {
            if (record.status == ProductionStatus::SUCCESS) {
                currentSummary.successfulRecords++;
            } else {
                currentSummary.failedRecords++;
            }
        }

        // Calculate success rate
        if (currentSummary.totalRecords > 0) {
            currentSummary.successRate = static_cast<double>(currentSummary.successfulRecords) / currentSummary.totalRecords;
        }

        emit realTimeDataUpdated(currentSummary);

    } catch (const std::exception& e) {
        qDebug() << "Error in real-time monitoring update:" << e.what();
    } catch (...) {
        qDebug() << "Unknown error in real-time monitoring update";
    }

    isUpdating = false;
}

// Private helper functions
bool ProductionStatisticsManager::initializeDatabase()
{
    m_databasePath = getDatabasePath();

    qDebug() << "=== Database Initialization DEBUG ===";
    qDebug() << "Database path:" << m_databasePath;
    qDebug() << "Data directory:" << m_dataDirectory;

    // Check if directory exists and is writable
    QFileInfo dirInfo(m_dataDirectory);
    qDebug() << "Directory exists:" << dirInfo.exists();
    qDebug() << "Directory is writable:" << dirInfo.isWritable();

    // Remove existing connection if it exists
    if (QSqlDatabase::contains("ProductionStats")) {
        qDebug() << "Removing existing database connection";
        QSqlDatabase::removeDatabase("ProductionStats");
    }

    m_database = QSqlDatabase::addDatabase("QSQLITE", "ProductionStats");
    m_database.setDatabaseName(m_databasePath);

    // Set database options for better performance and reliability
    m_database.setConnectOptions("QSQLITE_BUSY_TIMEOUT=30000");

    qDebug() << "Attempting to open database...";
    if (!m_database.open()) {
        QString error = m_database.lastError().text();
        qDebug() << "Failed to open database:" << error;
        qDebug() << "Database driver error:" << m_database.lastError().driverText();
        qDebug() << "Database text error:" << m_database.lastError().databaseText();

        // Try to recover by creating a new database file
        qDebug() << "Attempting to recover by removing existing database file...";
        QFile::remove(m_databasePath);
        if (!m_database.open()) {
            QString finalError = "Failed to initialize statistics database: " + m_database.lastError().text();
            qDebug() << finalError;
            emit errorOccurred(finalError);
            return false;
        }
    }

    qDebug() << "Database opened successfully";

    // Enable WAL mode for better concurrent access
    QSqlQuery pragmaQuery(m_database);
    bool walResult = pragmaQuery.exec("PRAGMA journal_mode=WAL");
    qDebug() << "WAL mode enabled:" << walResult;

    bool syncResult = pragmaQuery.exec("PRAGMA synchronous=NORMAL");
    qDebug() << "Synchronous mode set:" << syncResult;

    bool cacheResult = pragmaQuery.exec("PRAGMA cache_size=10000");
    qDebug() << "Cache size set:" << cacheResult;

    qDebug() << "Attempting to create tables...";
    bool tablesCreated = createTables();
    qDebug() << "Tables created successfully:" << tablesCreated;

    return tablesCreated;
}

bool ProductionStatisticsManager::createTables()
{
    QSqlQuery query(m_database);

    QString createTableSQL = R"(
        CREATE TABLE IF NOT EXISTS production_records (
            record_id TEXT PRIMARY KEY,
            start_time TEXT NOT NULL,
            end_time TEXT,
            rfid_code TEXT,
            template_name TEXT,
            shoe_code INTEGER,
            is_left_foot INTEGER,
            total_faces INTEGER,
            completed_faces INTEGER,
            status INTEGER,
            processing_time_ms INTEGER,
            laser_time_ms INTEGER,
            operator_id TEXT,
            error_message TEXT,
            custom_data TEXT
        )
    )";

    qDebug() << "Executing CREATE TABLE SQL...";
    if (!query.exec(createTableSQL)) {
        QString error = query.lastError().text();
        qDebug() << "Failed to create table:" << error;
        qDebug() << "SQL error type:" << query.lastError().type();
        qDebug() << "Driver error:" << query.lastError().driverText();
        qDebug() << "Database error:" << query.lastError().databaseText();
        return false;
    }

    qDebug() << "Table created successfully";

    // Create indexes for better performance
    query.exec("CREATE INDEX IF NOT EXISTS idx_start_time ON production_records(start_time)");
    query.exec("CREATE INDEX IF NOT EXISTS idx_template_name ON production_records(template_name)");
    query.exec("CREATE INDEX IF NOT EXISTS idx_status ON production_records(status)");

    return true;
}

bool ProductionStatisticsManager::saveRecordToDatabase(const ProductionRecord& record)
{
    // Retry mechanism for database operations
    const int maxRetries = 3;
    for (int attempt = 0; attempt < maxRetries; ++attempt) {
        try {
            QSqlQuery query(m_database);

            QString insertSQL = R"(
                INSERT OR REPLACE INTO production_records
                (record_id, start_time, end_time, rfid_code, template_name, shoe_code,
                 is_left_foot, total_faces, completed_faces, status, processing_time_ms,
                 laser_time_ms, operator_id, error_message, custom_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            )";

            query.prepare(insertSQL);
            query.addBindValue(record.recordId);
            query.addBindValue(record.startTime.toString(Qt::ISODate));
            query.addBindValue(record.endTime.toString(Qt::ISODate));
            query.addBindValue(record.rfidCode);
            query.addBindValue(record.templateName);
            query.addBindValue(record.shoeCode);
            query.addBindValue(record.isLeftFoot ? 1 : 0);
            query.addBindValue(record.totalFaces);
            query.addBindValue(record.completedFaces);
            query.addBindValue(static_cast<int>(record.status));
            query.addBindValue(static_cast<qint64>(record.processingTimeMs));
            query.addBindValue(static_cast<qint64>(record.laserTimeMs));
            query.addBindValue(record.operatorId);
            query.addBindValue(record.errorMessage);

            // Convert custom data to JSON
            QJsonObject customJson;
            for (auto it = record.customData.begin(); it != record.customData.end(); ++it) {
                customJson[it.key()] = QJsonValue::fromVariant(it.value());
            }
            QJsonDocument customDoc(customJson);
            query.addBindValue(customDoc.toJson(QJsonDocument::Compact));

            if (query.exec()) {
                return true; // Success
            }

            QString errorText = query.lastError().text();
            qDebug() << "Database save attempt" << (attempt + 1) << "failed:" << errorText;

            // Check if it's a recoverable error
            if (errorText.contains("database is locked") || errorText.contains("busy")) {
                QThread::msleep(100 * (attempt + 1)); // Progressive backoff
                continue;
            } else {
                break; // Non-recoverable error
            }

        } catch (const std::exception& e) {
            qDebug() << "Exception in saveRecordToDatabase attempt" << (attempt + 1) << ":" << e.what();
        } catch (...) {
            qDebug() << "Unknown exception in saveRecordToDatabase attempt" << (attempt + 1);
        }
    }

    qDebug() << "Failed to save record to database after" << maxRetries << "attempts";
    return false;
}

QVector<ProductionRecord> ProductionStatisticsManager::loadRecordsFromDatabase(const StatisticsFilter& filter) const
{
    QVector<ProductionRecord> records;

    qDebug() << "=== loadRecordsFromDatabase() DEBUG ===";
    qDebug() << "Database open:" << m_database.isOpen();
    qDebug() << "Database valid:" << m_database.isValid();
    qDebug() << "Database name:" << m_database.databaseName();
    qDebug() << "Database driver:" << m_database.driverName();

    // Check database connection first
    if (!m_database.isOpen() || !m_database.isValid()) {
        qDebug() << "Database not available for loading records";
        qDebug() << "Database error:" << m_database.lastError().text();
        return records;
    }

    QString selectSQL = "SELECT * FROM production_records";
    QStringList conditions;

    // Build WHERE clause based on filter
    if (filter.startTime.isValid()) {
        conditions << "start_time >= ?";
    }
    if (filter.endTime.isValid()) {
        conditions << "start_time <= ?";
    }
    if (!filter.templateNames.isEmpty()) {
        QStringList templatePlaceholders;
        for (int i = 0; i < filter.templateNames.size(); ++i) {
            templatePlaceholders << "?";
        }
        conditions << QString("template_name IN (%1)").arg(templatePlaceholders.join(","));
    }
    if (!filter.shoeCodes.isEmpty()) {
        QStringList shoePlaceholders;
        for (int i = 0; i < filter.shoeCodes.size(); ++i) {
            shoePlaceholders << "?";
        }
        conditions << QString("shoe_code IN (%1)").arg(shoePlaceholders.join(","));
    }
    if (!filter.statusFilter.isEmpty()) {
        QStringList statusPlaceholders;
        for (int i = 0; i < filter.statusFilter.size(); ++i) {
            statusPlaceholders << "?";
        }
        conditions << QString("status IN (%1)").arg(statusPlaceholders.join(","));
    }
    if (filter.leftFootOnly) {
        conditions << "is_left_foot = 1";
    }
    if (filter.rightFootOnly) {
        conditions << "is_left_foot = 0";
    }

    if (!conditions.isEmpty()) {
        selectSQL += " WHERE " + conditions.join(" AND ");
    }

    // Add ordering and limit with configurable page size
    selectSQL += " ORDER BY start_time DESC LIMIT ?";

    // Use a separate database connection for this query to avoid blocking
    QSqlDatabase queryDb = QSqlDatabase::cloneDatabase(m_database, "QueryConnection_" + QString::number(reinterpret_cast<quintptr>(QThread::currentThreadId())));
    if (!queryDb.open()) {
        qDebug() << "Failed to open query database connection:" << queryDb.lastError().text();
        return records;
    }

    QSqlQuery query(queryDb);

    // Set query timeout to prevent hanging
    query.setForwardOnly(true); // Optimize for large result sets

    if (!query.prepare(selectSQL)) {
        qDebug() << "Failed to prepare query:" << query.lastError().text();
        queryDb.close();
        return records;
    }

    // Bind filter values
    if (filter.startTime.isValid()) {
        query.addBindValue(filter.startTime.toString(Qt::ISODate));
    }
    if (filter.endTime.isValid()) {
        query.addBindValue(filter.endTime.toString(Qt::ISODate));
    }
    for (const QString& templateName : filter.templateNames) {
        query.addBindValue(templateName);
    }
    for (int shoeCode : filter.shoeCodes) {
        query.addBindValue(shoeCode);
    }
    for (ProductionStatus status : filter.statusFilter) {
        query.addBindValue(static_cast<int>(status));
    }

    // Add configurable limit (reduced for better performance)
    int recordLimit = qMin(m_maxRecordsInMemory / 4, 300); // Reduced from 500 to 300
    query.addBindValue(recordLimit);

    // Execute query with timeout handling
    QElapsedTimer queryTimer;
    queryTimer.start();
    const int maxQueryTimeMs = 10000; // 10 second timeout

    if (!query.exec()) {
        qDebug() << "Failed to load records from database:" << query.lastError().text();
        queryDb.close();
        return records;
    }

    // Reserve space for better performance
    records.reserve(recordLimit);

    int processedRecords = 0;

    while (query.next()) {
        // Check for timeout every 50 records to avoid blocking too long
        if (processedRecords % 50 == 0 && queryTimer.elapsed() > maxQueryTimeMs) {
            qDebug() << "Database query timeout after" << queryTimer.elapsed() << "ms, processed" << processedRecords << "records";
            break;
        }

        ProductionRecord record;
        record.recordId = query.value("record_id").toString();
        record.startTime = QDateTime::fromString(query.value("start_time").toString(), Qt::ISODate);
        record.endTime = QDateTime::fromString(query.value("end_time").toString(), Qt::ISODate);
        record.rfidCode = query.value("rfid_code").toString();
        record.templateName = query.value("template_name").toString();
        record.shoeCode = query.value("shoe_code").toInt();
        record.isLeftFoot = query.value("is_left_foot").toInt() == 1;
        record.totalFaces = query.value("total_faces").toInt();
        record.completedFaces = query.value("completed_faces").toInt();
        record.status = static_cast<ProductionStatus>(query.value("status").toInt());
        record.processingTimeMs = query.value("processing_time_ms").toLongLong();
        record.laserTimeMs = query.value("laser_time_ms").toLongLong();
        record.operatorId = query.value("operator_id").toString();
        record.errorMessage = query.value("error_message").toString();

        // Parse custom data from JSON only if not empty (performance optimization)
        QString customDataJson = query.value("custom_data").toString();
        if (!customDataJson.isEmpty()) {
            QJsonParseError parseError;
            QJsonDocument customDoc = QJsonDocument::fromJson(customDataJson.toUtf8(), &parseError);
            if (parseError.error == QJsonParseError::NoError) {
                QJsonObject customObj = customDoc.object();
                for (auto it = customObj.begin(); it != customObj.end(); ++it) {
                    record.customData[it.key()] = it.value().toVariant();
                }
            }
        }

        records.append(record);
        processedRecords++;
    }

    // Close the query database connection
    queryDb.close();

    qDebug() << "Loaded" << records.size() << "records from database in" << queryTimer.elapsed() << "ms";
    return records;
}

bool ProductionStatisticsManager::saveRecordsToFile() const
{
    QString filePath = getDataFilePath();
    QFile file(filePath);

    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    QJsonArray recordsArray;

    // Save cached records
    for (const auto& record : m_cachedRecords) {
        recordsArray.append(record.toJson());
    }

    // Save active records
    for (const auto& record : m_activeRecords) {
        recordsArray.append(record.toJson());
    }

    QJsonObject rootObject;
    rootObject["records"] = recordsArray;
    rootObject["version"] = "1.0";
    rootObject["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(rootObject);
    file.write(doc.toJson());
    file.close();

    return true;
}

bool ProductionStatisticsManager::loadRecordsFromFile()
{
    QString filePath = getDataFilePath();
    QFile file(filePath);

    if (!file.exists() || !file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    QJsonDocument doc = QJsonDocument::fromJson(data);
    QJsonObject rootObject = doc.object();
    QJsonArray recordsArray = rootObject["records"].toArray();

    m_cachedRecords.clear();

    for (const auto& value : recordsArray) {
        ProductionRecord record = ProductionRecord::fromJson(value.toObject());
        if (validateRecord(record)) {
            m_cachedRecords.append(record);
        }
    }

    return true;
}

QString ProductionStatisticsManager::getDataFilePath() const
{
    return m_dataDirectory + "/production_statistics.json";
}

QString ProductionStatisticsManager::getDatabasePath() const
{
    return m_dataDirectory + "/production_statistics.db";
}

bool ProductionStatisticsManager::applyFilter(const ProductionRecord& record, const StatisticsFilter& filter) const
{
    // Time filter
    if (filter.startTime.isValid() && record.startTime < filter.startTime) {
        return false;
    }
    if (filter.endTime.isValid() && record.startTime > filter.endTime) {
        return false;
    }

    // Template filter
    if (!filter.templateNames.isEmpty() && !filter.templateNames.contains(record.templateName)) {
        return false;
    }

    // Shoe code filter
    if (!filter.shoeCodes.isEmpty() && !filter.shoeCodes.contains(record.shoeCode)) {
        return false;
    }

    // Status filter
    if (!filter.statusFilter.isEmpty() && !filter.statusFilter.contains(record.status)) {
        return false;
    }

    // Foot filter
    if (filter.leftFootOnly && !record.isLeftFoot) {
        return false;
    }
    if (filter.rightFootOnly && record.isLeftFoot) {
        return false;
    }

    return true;
}

ProductionSummary ProductionStatisticsManager::calculateSummary(const QVector<ProductionRecord>& records) const
{
    ProductionSummary summary;

    if (records.isEmpty()) {
        return summary;
    }

    // Find time range
    summary.periodStart = records.first().startTime;
    summary.periodEnd = records.first().startTime;

    for (const auto& record : records) {
        if (record.startTime < summary.periodStart) {
            summary.periodStart = record.startTime;
        }
        if (record.startTime > summary.periodEnd) {
            summary.periodEnd = record.startTime;
        }

        // Count records by status
        summary.totalRecords++;
        if (record.status == ProductionStatus::SUCCESS) {
            summary.successfulRecords++;
        } else {
            summary.failedRecords++;
        }

        // Accumulate totals
        summary.totalFaces += record.completedFaces;
        summary.totalProcessingTime += record.processingTimeMs;
        summary.totalLaserTime += record.laserTimeMs;

        // Template usage
        summary.templateUsage[record.templateName]++;

        // Shoe size distribution
        summary.shoeSizeDistribution[record.shoeCode]++;
    }

    // Calculate averages
    if (summary.totalRecords > 0) {
        summary.successRate = (double)summary.successfulRecords / summary.totalRecords;

        double totalEfficiency = 0.0;
        int validRecords = 0;

        for (const auto& record : records) {
            if (record.processingTimeMs > 0) {
                totalEfficiency += record.getEfficiency();
                validRecords++;
            }
        }

        if (validRecords > 0) {
            summary.averageEfficiency = totalEfficiency / validRecords;
        }
    }

    return summary;
}

// Async database operations
void ProductionStatisticsManager::initializeDatabaseQueryThread()
{
    if (m_dbQueryThread) {
        return; // Already initialized
    }

    qDebug() << "Initializing database query thread...";

    // For now, we'll skip the actual thread creation but enable the async methods
    // The async methods will fall back to synchronous operations
    // This allows the UI to remain responsive by using QTimer::singleShot

    qDebug() << "Database query thread initialization completed (using fallback mode)";
}

void ProductionStatisticsManager::startProductionRecordAsync(const QString& rfidCode,
                                                           const QString& templateName,
                                                           int shoeCode,
                                                           bool isLeftFoot,
                                                           int totalFaces)
{
    if (!m_dbQueryWorker) {
        qDebug() << "Database query worker not available for async start record";
        // Fallback to synchronous operation
        startProductionRecord(rfidCode, templateName, shoeCode, isLeftFoot, totalFaces);
        return;
    }

    qDebug() << "Starting async production record for RFID:" << rfidCode;
    m_dbQueryWorker->requestStartRecord(rfidCode, templateName, shoeCode, isLeftFoot, totalFaces);
}

void ProductionStatisticsManager::updateProductionRecordAsync(const QString& recordId,
                                                            int completedFaces,
                                                            ProductionStatus status,
                                                            const QString& errorMessage)
{
    if (!m_dbQueryWorker) {
        qDebug() << "Database query worker not available for async update record";
        // Fallback to synchronous operation
        updateProductionRecord(recordId, completedFaces, status, errorMessage);
        return;
    }

    qDebug() << "Updating async production record:" << recordId;
    m_dbQueryWorker->requestUpdateRecord(recordId, completedFaces, status, errorMessage);
}

void ProductionStatisticsManager::finishProductionRecordAsync(const QString& recordId,
                                                            ProductionStatus status,
                                                            const QString& errorMessage)
{
    if (!m_dbQueryWorker) {
        qDebug() << "Database query worker not available for async finish record";
        // Fallback to synchronous operation
        finishProductionRecord(recordId, status, errorMessage);
        return;
    }

    qDebug() << "Finishing async production record:" << recordId;
    m_dbQueryWorker->requestFinishRecord(recordId, status, errorMessage);
}

void ProductionStatisticsManager::addCustomDataAsync(const QString& recordId,
                                                    const QString& key,
                                                    const QVariant& value)
{
    if (!m_dbQueryWorker) {
        qDebug() << "Database query worker not available for async add custom data";
        // Fallback to synchronous operation
        addCustomData(recordId, key, value);
        return;
    }

    qDebug() << "Adding async custom data to record:" << recordId;
    m_dbQueryWorker->requestAddCustomData(recordId, key, value);
}

bool ProductionStatisticsManager::validateRecord(const ProductionRecord& record) const
{
    if (record.recordId.isEmpty()) {
        return false;
    }
    if (!record.startTime.isValid()) {
        return false;
    }
    if (record.totalFaces < 0 || record.completedFaces < 0) {
        return false;
    }
    if (record.completedFaces > record.totalFaces) {
        return false;
    }

    return true;
}


