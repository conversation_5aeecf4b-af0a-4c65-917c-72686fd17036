#include "SystemSettingsDialog.h"
#include <QSettings>
#include <QStandardPaths>

#pragma execution_character_set("utf-8")

SystemSettingsDialog::SystemSettingsDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("系统设置");
    setupUI();
    
    // 默认配置文件路径
    configFilePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/config.ini";
    
    // 连接信号与槽
    connect(btnBrowseTemplateCSV, &QPushButton::clicked, this, &SystemSettingsDialog::onBrowseTemplateCSV);
    connect(btnBrowseLogDirectory, &QPushButton::clicked, this, &SystemSettingsDialog::onBrowseLogDirectory);
    connect(btnBrowseLaserWorkDirectory, &QPushButton::clicked, this, &SystemSettingsDialog::onBrowseLaserWorkDirectory);
    connect(btnBrowseShoePathEditor, &QPushButton::clicked, this, &SystemSettingsDialog::onBrowseShoePathEditor);
    connect(btnSaveConfig, &QPushButton::clicked, this, &SystemSettingsDialog::onSaveConfig);
    connect(btnLoadConfig, &QPushButton::clicked, this, &SystemSettingsDialog::onLoadConfig);
    connect(btnRestoreDefaults, &QPushButton::clicked, this, &SystemSettingsDialog::onRestoreDefaults);
    connect(btnOK, &QPushButton::clicked, this, &SystemSettingsDialog::onAccept);
    connect(btnCancel, &QPushButton::clicked, this, &SystemSettingsDialog::onCancel);
    
    // 加载默认设置
    loadDefaultSettings();
}

SystemSettingsDialog::~SystemSettingsDialog()
{
}

void SystemSettingsDialog::setupUI()
{
    // 创建布局
    mainLayout = new QGridLayout(this);
    
    // 创建选项卡控件
    tabWidget = new QTabWidget(this);
    tabWidget->addTab(createDeviceTab(), "设备设置");
    tabWidget->addTab(createPathsTab(), "文件路径");
    tabWidget->addTab(createSystemTab(), "系统选项");
    
    // 创建按钮
    btnSaveConfig = new QPushButton("保存配置", this);
    btnLoadConfig = new QPushButton("加载配置", this);
    btnRestoreDefaults = new QPushButton("恢复默认", this);
    btnOK = new QPushButton("确定", this);
    btnCancel = new QPushButton("取消", this);
    
    // 添加控件到布局
    mainLayout->addWidget(tabWidget, 0, 0, 1, 5);
    
    mainLayout->addWidget(btnSaveConfig, 1, 0);
    mainLayout->addWidget(btnLoadConfig, 1, 1);
    mainLayout->addWidget(btnRestoreDefaults, 1, 2);
    mainLayout->addWidget(btnOK, 1, 3);
    mainLayout->addWidget(btnCancel, 1, 4);
    
    // 设置窗口属性
    setMinimumWidth(600);
    setMinimumHeight(400);
}

QWidget* SystemSettingsDialog::createDeviceTab()
{
    QWidget* tab = new QWidget(this);
    QGridLayout* layout = new QGridLayout(tab);
    
    // ABB机器人设置
    QGroupBox* abbGroup = new QGroupBox("ABB机器人设置", tab);
    QGridLayout* abbLayout = new QGridLayout(abbGroup);
    
    abbLayout->addWidget(new QLabel("服务器端口:", abbGroup), 0, 0);
    spnABBServerPort = new QSpinBox(abbGroup);
    spnABBServerPort->setRange(1000, 65535);
    spnABBServerPort->setValue(8080);
    abbLayout->addWidget(spnABBServerPort, 0, 1);
    
    // RFID设置
    QGroupBox* rfidGroup = new QGroupBox("RFID设备设置", tab);
    QGridLayout* rfidLayout = new QGridLayout(rfidGroup);
    
    rfidLayout->addWidget(new QLabel("IP地址:", rfidGroup), 0, 0);
    edtRFIDIPAddress = new QLineEdit(rfidGroup);
    edtRFIDIPAddress->setText("*************");
    rfidLayout->addWidget(edtRFIDIPAddress, 0, 1);
    
    rfidLayout->addWidget(new QLabel("端口:", rfidGroup), 1, 0);
    spnRFIDPort = new QSpinBox(rfidGroup);
    spnRFIDPort->setRange(1, 65535);
    spnRFIDPort->setValue(502);
    rfidLayout->addWidget(spnRFIDPort, 1, 1);
    
    // 添加到布局
    layout->addWidget(abbGroup, 0, 0);
    layout->addWidget(rfidGroup, 1, 0);
    layout->setRowStretch(2, 1);
    
    return tab;
}

QWidget* SystemSettingsDialog::createPathsTab()
{
    QWidget* tab = new QWidget(this);
    QGridLayout* layout = new QGridLayout(tab);
    
    // 文件路径设置
    layout->addWidget(new QLabel("模板CSV文件:", tab), 0, 0);
    edtTemplateCSVPath = new QLineEdit(tab);
    layout->addWidget(edtTemplateCSVPath, 0, 1);
    btnBrowseTemplateCSV = new QPushButton("浏览...", tab);
    layout->addWidget(btnBrowseTemplateCSV, 0, 2);
    
    layout->addWidget(new QLabel("日志目录:", tab), 1, 0);
    edtLogDirectory = new QLineEdit(tab);
    layout->addWidget(edtLogDirectory, 1, 1);
    btnBrowseLogDirectory = new QPushButton("浏览...", tab);
    layout->addWidget(btnBrowseLogDirectory, 1, 2);
    
    layout->addWidget(new QLabel("激光工作目录:", tab), 2, 0);
    edtLaserWorkDirectory = new QLineEdit(tab);
    layout->addWidget(edtLaserWorkDirectory, 2, 1);
    btnBrowseLaserWorkDirectory = new QPushButton("浏览...", tab);
    layout->addWidget(btnBrowseLaserWorkDirectory, 2, 2);
    
    layout->addWidget(new QLabel("鞋子路径编辑器:", tab), 3, 0);
    edtShoePathEditorPath = new QLineEdit(tab);
    layout->addWidget(edtShoePathEditorPath, 3, 1);
    btnBrowseShoePathEditor = new QPushButton("浏览...", tab);
    layout->addWidget(btnBrowseShoePathEditor, 3, 2);
    
    layout->setColumnStretch(1, 1);
    layout->setRowStretch(4, 1);
    
    return tab;
}

QWidget* SystemSettingsDialog::createSystemTab()
{
    QWidget* tab = new QWidget(this);
    QGridLayout* layout = new QGridLayout(tab);
    
    // 系统选项 - 自动连接设备（使用开关样式）
    QWidget* autoConnectWidget = new QWidget(tab);
    QHBoxLayout* autoConnectLayout = new QHBoxLayout(autoConnectWidget);
    autoConnectLayout->setContentsMargins(0, 0, 0, 0);
    
    QLabel* autoConnectLabel = new QLabel("启动时自动连接设备:", autoConnectWidget);
    chkAutoConnectDevices = new QCheckBox(tab);
    
    // 直观的复选框样式（小尺寸）
    chkAutoConnectDevices->setStyleSheet(
        "QCheckBox::indicator {"
        "    width: 16px;"
        "    height: 16px;"
        "    border: 2px solid #999;"
        "    border-radius: 3px;"
        "    background-color: white;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #4CAF50;"
        "    border: 2px solid #4CAF50;"
        "    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgNkw0LjUgOC41TDEwIDMiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);"
        "}"
        "QCheckBox::indicator:hover {"
        "    border: 2px solid #666;"
        "}"
        "QCheckBox::indicator:checked:hover {"
        "    background-color: #45a049;"
        "    border: 2px solid #45a049;"
        "}"
    );
    
    autoConnectLayout->addWidget(autoConnectLabel);
    autoConnectLayout->addStretch();
    autoConnectLayout->addWidget(chkAutoConnectDevices);
    layout->addWidget(autoConnectWidget, 0, 0, 1, 2);
    
    // 自动保存日志（使用开关样式）
    QWidget* autoSaveWidget = new QWidget(tab);
    QHBoxLayout* autoSaveLayout = new QHBoxLayout(autoSaveWidget);
    autoSaveLayout->setContentsMargins(0, 0, 0, 0);
    
    QLabel* autoSaveLabel = new QLabel("自动保存日志:", autoSaveWidget);
    chkAutoSaveLog = new QCheckBox(tab);
    
    // 直观的复选框样式（小尺寸）
    chkAutoSaveLog->setStyleSheet(
        "QCheckBox::indicator {"
        "    width: 16px;"
        "    height: 16px;"
        "    border: 2px solid #999;"
        "    border-radius: 3px;"
        "    background-color: white;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #4CAF50;"
        "    border: 2px solid #4CAF50;"
        "    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgNkw0LjUgOC41TDEwIDMiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);"
        "}"
        "QCheckBox::indicator:hover {"
        "    border: 2px solid #666;"
        "}"
        "QCheckBox::indicator:checked:hover {"
        "    background-color: #45a049;"
        "    border: 2px solid #45a049;"
        "}"
    );
    
    autoSaveLayout->addWidget(autoSaveLabel);
    autoSaveLayout->addStretch();
    autoSaveLayout->addWidget(chkAutoSaveLog);
    layout->addWidget(autoSaveWidget, 1, 0, 1, 2);
    
    layout->addWidget(new QLabel("状态更新间隔(毫秒):", tab), 2, 0);
    spnStatusUpdateInterval = new QSpinBox(tab);
    spnStatusUpdateInterval->setRange(100, 10000);
    spnStatusUpdateInterval->setValue(1000);
    spnStatusUpdateInterval->setSingleStep(100);
    layout->addWidget(spnStatusUpdateInterval, 2, 1);
    
    layout->addWidget(new QLabel("界面主题:", tab), 3, 0);
    cmbTheme = new QComboBox(tab);
    cmbTheme->addItem("深蓝科技");
    cmbTheme->addItem("淡蓝科技");
    cmbTheme->addItem("深色主题");
    cmbTheme->addItem("浅色主题");
    layout->addWidget(cmbTheme, 3, 1);
    
    layout->setColumnStretch(1, 1);
    layout->setRowStretch(4, 1);
    
    return tab;
}

void SystemSettingsDialog::loadDefaultSettings()
{
    // 设备设置
    spnABBServerPort->setValue(8080);
    edtRFIDIPAddress->setText("*************");
    spnRFIDPort->setValue(502);
    
    // 文件路径
    edtTemplateCSVPath->setText(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/templates.csv");
    edtLogDirectory->setText(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs");
    edtLaserWorkDirectory->setText(QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LaserWork");
    edtShoePathEditorPath->setText("");  // 不设置默认值，让用户选择
    
    // 系统设置
    chkAutoConnectDevices->setChecked(true);
    chkAutoSaveLog->setChecked(true);
    spnStatusUpdateInterval->setValue(1000);
    cmbTheme->setCurrentIndex(0); // 设置默认主题
}

QString SystemSettingsDialog::getABBServerPort() const
{
    return QString::number(spnABBServerPort->value());
}

QString SystemSettingsDialog::getRFIDIPAddress() const
{
    return edtRFIDIPAddress->text();
}

int SystemSettingsDialog::getRFIDPort() const
{
    return spnRFIDPort->value();
}

QString SystemSettingsDialog::getTemplateCSVPath() const
{
    return edtTemplateCSVPath->text();
}

QString SystemSettingsDialog::getLogDirectory() const
{
    return edtLogDirectory->text();
}

QString SystemSettingsDialog::getLaserWorkDirectory() const
{
    return edtLaserWorkDirectory->text();
}

bool SystemSettingsDialog::getAutoConnectDevices() const
{
    return chkAutoConnectDevices->isChecked();
}

bool SystemSettingsDialog::getAutoSaveLog() const
{
    return chkAutoSaveLog->isChecked();
}

int SystemSettingsDialog::getStatusUpdateInterval() const
{
    return spnStatusUpdateInterval->value();
}

void SystemSettingsDialog::setABBServerPort(const QString& port)
{
    spnABBServerPort->setValue(port.toInt());
}

void SystemSettingsDialog::setRFIDIPAddress(const QString& ipAddress)
{
    edtRFIDIPAddress->setText(ipAddress);
}

void SystemSettingsDialog::setRFIDPort(int port)
{
    spnRFIDPort->setValue(port);
}

void SystemSettingsDialog::setTemplateCSVPath(const QString& path)
{
    edtTemplateCSVPath->setText(path);
}

void SystemSettingsDialog::setLogDirectory(const QString& dir)
{
    edtLogDirectory->setText(dir);
}

void SystemSettingsDialog::setLaserWorkDirectory(const QString& dir)
{
    edtLaserWorkDirectory->setText(dir);
}

void SystemSettingsDialog::setAutoConnectDevices(bool enable)
{
    chkAutoConnectDevices->setChecked(enable);
}

void SystemSettingsDialog::setAutoSaveLog(bool enable)
{
    chkAutoSaveLog->setChecked(enable);
}

void SystemSettingsDialog::setStatusUpdateInterval(int intervalMs)
{
    spnStatusUpdateInterval->setValue(intervalMs);
}

void SystemSettingsDialog::setConfigFilePath(const QString& filePath)
{
    configFilePath = filePath;
}

void SystemSettingsDialog::applySettings()
{
    // 创建必要的目录
    QDir dir;
    dir.mkpath(getLogDirectory());
    dir.mkpath(getLaserWorkDirectory());
}

SystemSettings SystemSettingsDialog::getSettings() const
{
    SystemSettings settings;
    
    // 设备设置
    settings.abbPort = spnABBServerPort->value();
    settings.rfidIP = edtRFIDIPAddress->text();
    settings.rfidPort = spnRFIDPort->value();
    
    // 文件路径
    settings.laserWorkDir = edtLaserWorkDirectory->text();
    
    // 系统设置
    settings.autoConnect = chkAutoConnectDevices->isChecked();
    settings.logLevel = cmbTheme->currentText();
    
    // 鞋子路径编辑器设置
    settings.shoePathEditorPath = edtShoePathEditorPath->text();
    
    return settings;
}

void SystemSettingsDialog::setSettings(const SystemSettings& settings)
{
    // ABB机器人设置
    spnABBServerPort->setValue(settings.abbPort);
    
    // RFID设置
    edtRFIDIPAddress->setText(settings.rfidIP);
    spnRFIDPort->setValue(settings.rfidPort);
    
    // 打粗设置
    edtLaserWorkDirectory->setText(settings.laserWorkDir);
    
    // 系统设置
    chkAutoConnectDevices->setChecked(settings.autoConnect);
    
    // 设置主题(这里使用logLevel字段存储主题)
    int themeIndex = cmbTheme->findText(settings.logLevel);
    if (themeIndex >= 0) {
        cmbTheme->setCurrentIndex(themeIndex);
    }
    
    // 鞋子路径编辑器设置
    edtShoePathEditorPath->setText(settings.shoePathEditorPath);
}

void SystemSettingsDialog::onBrowseTemplateCSV()
{
    QString fileName = QFileDialog::getSaveFileName(this, "选择模板CSV文件",
                                                  edtTemplateCSVPath->text(),
                                                  "CSV文件 (*.csv);;所有文件 (*.*)");
    
    if (!fileName.isEmpty()) {
        edtTemplateCSVPath->setText(fileName);
    }
}

void SystemSettingsDialog::onBrowseLogDirectory()
{
    QString dir = QFileDialog::getExistingDirectory(this, "选择日志目录",
                                                  edtLogDirectory->text().isEmpty() ? QDir::homePath() : edtLogDirectory->text(),
                                                  QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
    
    if (!dir.isEmpty()) {
        edtLogDirectory->setText(dir);
    }
}

void SystemSettingsDialog::onBrowseLaserWorkDirectory()
{
    QString dir = QFileDialog::getExistingDirectory(this, "选择激光工作目录",
                                                  edtLaserWorkDirectory->text().isEmpty() ? QDir::homePath() : edtLaserWorkDirectory->text(),
                                                  QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
    if (!dir.isEmpty()) {
        edtLaserWorkDirectory->setText(dir);
    }
}

void SystemSettingsDialog::onBrowseShoePathEditor()
{
    QString filePath = QFileDialog::getOpenFileName(this, "选择鞋子路径编辑器",
                                                 edtShoePathEditorPath->text().isEmpty() ? QDir::homePath() : QFileInfo(edtShoePathEditorPath->text()).path(),
                                                 "可执行文件 (*.exe)");
    if (!filePath.isEmpty()) {
        edtShoePathEditorPath->setText(filePath);
    }
}

void SystemSettingsDialog::onSaveConfig()
{
    QString filePath = QFileDialog::getSaveFileName(this, "保存配置文件", 
                                                   configFilePath, 
                                                   "INI文件 (*.ini);;所有文件 (*.*)");
    
    if (!filePath.isEmpty()) {
        if (saveConfig(filePath)) {
            QMessageBox::information(this, "保存成功", "配置已保存到: " + filePath);
            configFilePath = filePath;
        } else {
            QMessageBox::warning(this, "保存失败", "无法保存配置到: " + filePath);
        }
    }
}

void SystemSettingsDialog::onLoadConfig()
{
    QString filePath = QFileDialog::getOpenFileName(this, "加载配置文件", 
                                                   configFilePath, 
                                                   "INI文件 (*.ini);;所有文件 (*.*)");
    
    if (!filePath.isEmpty()) {
        if (loadConfig(filePath)) {
            QMessageBox::information(this, "加载成功", "配置已从以下位置加载: " + filePath);
            configFilePath = filePath;
        } else {
            QMessageBox::warning(this, "加载失败", "无法从以下位置加载配置: " + filePath);
        }
    }
}

void SystemSettingsDialog::onRestoreDefaults()
{
    if (QMessageBox::question(this, "恢复默认设置", "确定要恢复所有设置为默认值吗？",
                            QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
        loadDefaultSettings();
    }
}

void SystemSettingsDialog::onAccept()
{
    // 确认所有路径有效
    QDir dir;
    if (!QFile::exists(edtTemplateCSVPath->text()) && !QFile::exists(QFileInfo(edtTemplateCSVPath->text()).path())) {
        QMessageBox::warning(this, "无效路径", "模板CSV文件路径无效");
        return;
    }
    
    if (!dir.exists(edtLogDirectory->text()) && !dir.mkpath(edtLogDirectory->text())) {
        QMessageBox::warning(this, "无效路径", "日志目录无效且无法创建");
        return;
    }
    
    if (!dir.exists(edtLaserWorkDirectory->text()) && !dir.mkpath(edtLaserWorkDirectory->text())) {
        QMessageBox::warning(this, "无效路径", "激光工作目录无效且无法创建");
        return;
    }
    
    // 检查鞋子路径编辑器是否存在
    if (!edtShoePathEditorPath->text().isEmpty() && !QFile::exists(edtShoePathEditorPath->text())) {
        if (QMessageBox::question(this, "无效路径", 
                                "鞋子路径编辑器路径无效，是否继续?", 
                                QMessageBox::Yes | QMessageBox::No) != QMessageBox::Yes) {
            return;
        }
    }
    
    // 保存当前配置到默认配置文件
    saveConfig(configFilePath);
    
    // 接受对话框
    accept();
}

void SystemSettingsDialog::onCancel()
{
    reject();
}

bool SystemSettingsDialog::loadConfig(const QString& filePath)
{
    QSettings settings(filePath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");
    
    if (!settings.contains("Device/ABBPort")) {
        QMessageBox::warning(this, "加载失败", "配置文件格式不正确或不存在");
        return false;
    }
    
    // 设备设置
    spnABBServerPort->setValue(settings.value("Device/ABBPort", 8080).toInt());
    edtRFIDIPAddress->setText(settings.value("Device/RFIDIP", "*************").toString());
    spnRFIDPort->setValue(settings.value("Device/RFIDPort", 502).toInt());
    
    // 文件路径
    edtTemplateCSVPath->setText(settings.value("Paths/TemplateCSV", "").toString());
    edtLogDirectory->setText(settings.value("Paths/LogDir", "").toString());
    edtLaserWorkDirectory->setText(settings.value("Paths/LaserWorkDir", "").toString());
    edtShoePathEditorPath->setText(settings.value("Paths/ShoePathEditorPath", "").toString());
    
    // 系统设置
    chkAutoConnectDevices->setChecked(settings.value("System/AutoConnect", true).toBool());
    chkAutoSaveLog->setChecked(settings.value("System/AutoSaveLog", true).toBool());
    spnStatusUpdateInterval->setValue(settings.value("System/StatusUpdateInterval", 1000).toInt());
    
    return true;
}

bool SystemSettingsDialog::saveConfig(const QString& filePath)
{
    QSettings settings(filePath, QSettings::IniFormat);
    settings.setIniCodec("UTF-8");
    
    // 设备设置
    settings.setValue("Device/ABBPort", spnABBServerPort->value());
    settings.setValue("Device/RFIDIP", edtRFIDIPAddress->text());
    settings.setValue("Device/RFIDPort", spnRFIDPort->value());
    
    // 文件路径
    settings.setValue("Paths/TemplateCSV", edtTemplateCSVPath->text());
    settings.setValue("Paths/LogDir", edtLogDirectory->text());
    settings.setValue("Paths/LaserWorkDir", edtLaserWorkDirectory->text());
    settings.setValue("Paths/ShoePathEditorPath", edtShoePathEditorPath->text());
    
    // 系统设置
    settings.setValue("System/AutoConnect", chkAutoConnectDevices->isChecked());
    settings.setValue("System/AutoSaveLog", chkAutoSaveLog->isChecked());
    settings.setValue("System/StatusUpdateInterval", spnStatusUpdateInterval->value());
    
    settings.sync();
    return true;
}

QString SystemSettingsDialog::getShoePathEditorPath() const
{
    return edtShoePathEditorPath->text();
}

void SystemSettingsDialog::setShoePathEditorPath(const QString& path)
{
    edtShoePathEditorPath->setText(path);
}