#include "TemplateManager.h"
#include <QDateTime>
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>

#pragma execution_character_set("utf-8")

TemplateManager::TemplateManager(QObject *parent)
    : QObject(parent)
{
    // 设置默认模板文件路径
    m_csvFilePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/templates.csv";
    
    // 如果模板文件不存在，创建一个空的模板文件
    if (!QFile::exists(m_csvFilePath)) {
        QDir().mkpath(QFileInfo(m_csvFilePath).path());
        QFile file(m_csvFilePath);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out.setCodec("UTF-8");
            out.setAutoDetectUnicode(true);
            out.setGenerateByteOrderMark(false); // 不生成BOM标记
            out << "RFID_ID,鞋码,是否左脚,模板名称,激光工作目录,打粗次数,最后打粗时间\n";
            file.close();
        }
    }
}

TemplateManager::~TemplateManager()
{
    saveToCSV(m_csvFilePath);
}

bool TemplateManager::addTemplate(const ShoeTemplate& templ)
{
    // 检查是否已存在相同RFID的模板
    for (int i = 0; i < m_templates.size(); ++i) {
        if (m_templates[i].rfidId == templ.rfidId) {
            emit errorOccurred("已存在相同RFID ID的模板: " + templ.rfidId);
            return false;
        }
    }
    
    // 添加新模板
    m_templates.append(templ);
    emit templateAdded(templ);
    
    // 保存到CSV
    saveToCSV(m_csvFilePath);
    
    return true;
}

bool TemplateManager::updateTemplate(const ShoeTemplate& templ)
{
    for (int i = 0; i < m_templates.size(); ++i) {
        if (m_templates[i].rfidId == templ.rfidId) {
            m_templates[i] = templ;
            emit templateUpdated(templ);
            
            // 保存到CSV
            saveToCSV(m_csvFilePath);
            
            return true;
        }
    }
    
    emit errorOccurred("未找到要更新的模板: " + templ.rfidId);
    return false;
}

bool TemplateManager::deleteTemplate(const QString& rfidId)
{
    for (int i = 0; i < m_templates.size(); ++i) {
        if (m_templates[i].rfidId == rfidId) {
            m_templates.removeAt(i);
            emit templateDeleted(rfidId);
            
            // 保存到CSV
            saveToCSV(m_csvFilePath);
            
            return true;
        }
    }
    
    emit errorOccurred("未找到要删除的模板: " + rfidId);
    return false;
}

bool TemplateManager::findTemplateByRFID(const QString& rfidId, ShoeTemplate& templ)
{
    for (const ShoeTemplate& t : m_templates) {
        if (t.rfidId == rfidId) {
            templ = t;
            return true;
        }
    }
    
    return false;
}

bool TemplateManager::findTemplateByShoeInfo(int shoeCode, bool isLeftFoot, ShoeTemplate& templ)
{
    for (const ShoeTemplate& t : m_templates) {
        if (t.shoeCode == shoeCode && t.isLeftFoot == isLeftFoot) {
            templ = t;
            return true;
        }
    }
    
    return false;
}

QVector<ShoeTemplate> TemplateManager::getAllTemplates() const
{
    return m_templates;
}

bool TemplateManager::loadFromCSV(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        emit errorOccurred("无法打开模板文件: " + filePath);
        return false;
    }
    
    QTextStream in(&file);
    in.setCodec("UTF-8");
    in.setAutoDetectUnicode(true);
    
    // 清除现有模板
    m_templates.clear();
    
    // 读取标题行
    QString headerLine = in.readLine();
    if (headerLine.isEmpty()) {
        file.close();
        emit errorOccurred("模板文件格式错误: 缺少标题行");
        return false;
    }
    
    // 读取数据行
    while (!in.atEnd()) {
        QString line = in.readLine();
        if (line.isEmpty()) {
            continue;
        }
        
        QStringList fields = line.split(",");
        ShoeTemplate templ = stringListToTemplate(fields);
        
        // 检查RFID ID是否有效
        if (templ.rfidId.isEmpty()) {
            continue;
        }
        
        m_templates.append(templ);
    }
    
    file.close();
    emit templatesLoaded();
    
    return !m_templates.isEmpty(); // 返回true表示成功加载了至少一个模板
}

int TemplateManager::saveToCSV(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        emit errorOccurred("无法打开文件进行写入: " + filePath);
        return 0;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    out.setAutoDetectUnicode(true);
    out.setGenerateByteOrderMark(false); // 不生成BOM标记
    
    // 写入标题行
    out << "RFID_ID,鞋码,是否左脚,模板名称,激光工作目录,打粗次数,最后打粗时间\n";
    
    // 写入数据行
    int count = 0;
    for (const ShoeTemplate& templ : m_templates) {
        QStringList fields = templateToStringList(templ);
        out << fields.join(",") << "\n";
        count++;
    }
    
    file.close();
    m_csvFilePath = filePath;
    
    return count;
}

bool TemplateManager::importTemplates(const QString& filePath)
{
    // 临时保存原始模板
    QVector<ShoeTemplate> originalTemplates = m_templates;
    
    // 尝试加载新文件
    if (!loadFromCSV(filePath)) {
        // 恢复原始模板
        m_templates = originalTemplates;
        return false;
    }
    
    // 合并现有模板与新导入的模板
    m_templates.append(originalTemplates);
    
    // 去除重复项
    QVector<ShoeTemplate> uniqueTemplates;
    QSet<QString> rfidSet;
    
    for (const ShoeTemplate& templ : m_templates) {
        if (!rfidSet.contains(templ.rfidId)) {
            rfidSet.insert(templ.rfidId);
            uniqueTemplates.append(templ);
        }
    }
    
    m_templates = uniqueTemplates;
    
    // 保存合并后的模板
    saveToCSV(m_csvFilePath);
    
    emit templatesLoaded();
    return true;
}

bool TemplateManager::exportTemplates(const QString& filePath)
{
    return saveToCSV(filePath);
}

bool TemplateManager::updateMarkCount(const QString& rfidId)
{
    for (int i = 0; i < m_templates.size(); ++i) {
        if (m_templates[i].rfidId == rfidId) {
            m_templates[i].markCount++;
            m_templates[i].lastMarkTime = QDateTime::currentDateTime();
            
            emit templateUpdated(m_templates[i]);
            saveToCSV(m_csvFilePath);
            
            return true;
        }
    }
    
    return false;
}

void TemplateManager::updateTemplateModel(QStandardItemModel* model)
{
    if (!model) {
        return;
    }
    
    // 清空模型
    model->clear();
    
    // 设置表头
    QStringList headers;
    headers << "RFID ID" << "鞋码" << "左/右脚" << "模板名称" << "激光工作目录" << "打粗次数" << "最后打粗时间";
    model->setHorizontalHeaderLabels(headers);
    
    // 添加数据行
    for (const ShoeTemplate& templ : m_templates) {
        QList<QStandardItem*> items;
        
        // 添加各列数据
        items.append(new QStandardItem(templ.rfidId));
        items.append(new QStandardItem(QString::number(templ.shoeCode)));
        items.append(new QStandardItem(templ.isLeftFoot ? "左脚" : "右脚"));
        items.append(new QStandardItem(templ.templateName));
        items.append(new QStandardItem(templ.laserWorkDir));
        items.append(new QStandardItem(QString::number(templ.markCount)));
        
        QString lastTimeStr = templ.lastMarkTime.isValid() ? 
                             templ.lastMarkTime.toString("yyyy-MM-dd HH:mm:ss") : "无";
        items.append(new QStandardItem(lastTimeStr));
        
        // 添加到模型
        model->appendRow(items);
    }
}

bool TemplateManager::templateExists(const QString& rfidId)
{
    for (const ShoeTemplate& templ : m_templates) {
        if (templ.rfidId == rfidId) {
            return true;
        }
    }
    
    return false;
}

int TemplateManager::getTemplateCount() const
{
    return m_templates.size();
}

void TemplateManager::setCSVFilePath(const QString& filePath)
{
    m_csvFilePath = filePath;
}

QStringList TemplateManager::templateToStringList(const ShoeTemplate& templ)
{
    QStringList fields;
    fields << templ.rfidId
           << QString::number(templ.shoeCode)
           << (templ.isLeftFoot ? "1" : "0")
           << templ.templateName
           << templ.laserWorkDir
           << QString::number(templ.markCount);
    
    // 添加最后打粗时间
    if (templ.lastMarkTime.isValid()) {
        fields << templ.lastMarkTime.toString("yyyy-MM-dd hh:mm:ss");
    } else {
        fields << "";
    }
    
    return fields;
}

ShoeTemplate TemplateManager::stringListToTemplate(const QStringList& fields)
{
    ShoeTemplate templ;
    
    if (fields.size() >= 5) {
        templ.rfidId = fields[RFID_ID];
        templ.shoeCode = fields[SHOE_CODE].toInt();
        templ.isLeftFoot = (fields[IS_LEFT_FOOT] == "1" || fields[IS_LEFT_FOOT].toLower() == "true");
        templ.templateName = fields[TEMPLATE_NAME];
        templ.laserWorkDir = fields[MARK_DATA_FILE]; // 复用原来的字段位置
        
        // 可选字段
        if (fields.size() > MARK_COUNT) {
            templ.markCount = fields[MARK_COUNT].toInt();
        }
        
        if (fields.size() > LAST_MARK_TIME && !fields[LAST_MARK_TIME].isEmpty()) {
            templ.lastMarkTime = QDateTime::fromString(fields[LAST_MARK_TIME], "yyyy-MM-dd hh:mm:ss");
        }
    }
    
    return templ;
}

// 创建默认模板
void TemplateManager::createDefaultTemplate() {
    // 检查是否已有模板
    if (!m_templates.isEmpty()) {
        return;
    }
    
    // 创建默认模板
    ShoeTemplate defaultTemplate;
    defaultTemplate.rfidId = "DEFAULT_RFID_001";
    defaultTemplate.shoeCode = 40; // 默认40码
    defaultTemplate.isLeftFoot = true;
    defaultTemplate.templateName = "默认模板";
    
    // 设置默认激光工作目录
    QString defaultWorkDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/LaserWork";
    QDir().mkpath(defaultWorkDir); // 确保目录存在
    defaultTemplate.laserWorkDir = defaultWorkDir;
    
    defaultTemplate.markCount = 0;
    defaultTemplate.lastMarkTime = QDateTime::currentDateTime();
    
    // 添加到模板列表
    m_templates.append(defaultTemplate);
    emit templateAdded(defaultTemplate);
    
    // 保存到CSV
    saveToCSV(m_csvFilePath);
} 