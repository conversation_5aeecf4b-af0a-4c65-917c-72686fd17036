#include "TemplateEditDialog.h"
#include "MainWindow.h"
#include <QDir>
#include <QHBoxLayout>
#include <QDateTime>
#include <QDesktopServices>
#include <QUrl>
#include <QApplication>

#pragma execution_character_set("utf-8")

TemplateEditDialog::TemplateEditDialog(std::shared_ptr<TemplateManager> manager, std::shared_ptr<RFIDModbusDriver> rfidDriver, std::shared_ptr<RobotDriver::ABBSocketDriver> abbDriver, MainWindow* mainWindow, QWidget *parent)
    : QDialog(parent), templateManager(manager), rfidDriver(rfidDriver), abbDriver(abbDriver), mainWindow(mainWindow), isEditMode(false)
{
    setWindowTitle("添加模板");
    setupUI();
    
    // 加载模板预设
    loadTemplatePresets();
}

TemplateEditDialog::TemplateEditDialog(std::shared_ptr<TemplateManager> manager, std::shared_ptr<RFIDModbusDriver> rfidDriver, std::shared_ptr<RobotDriver::ABBSocketDriver> abbDriver, const ShoeTemplate& templ, MainWindow* mainWindow, QWidget *parent)
    : QDialog(parent), templateManager(manager), rfidDriver(rfidDriver), abbDriver(abbDriver), mainWindow(mainWindow), isEditMode(true), originalTemplate(templ)
{
    setWindowTitle("编辑模板");
    setupUI();
    
    // 加载现有模板数据
    edtTemplateName->setText(originalTemplate.templateName);
    edtRFID->setText(originalTemplate.rfidId);
    originalRFID = originalTemplate.rfidId;
    spnShoeCode->setValue(originalTemplate.shoeCode);
    if (originalTemplate.isLeftFoot) {
        radLeft->setChecked(true);
    } else {
        radRight->setChecked(true);
    }
    edtLaserWorkDir->setText(originalTemplate.laserWorkDir);
    
    // 加载模板预设
    loadTemplatePresets();
}

TemplateEditDialog::~TemplateEditDialog()
{
    // Qt的父子对象系统会自动删除子对象
}

void TemplateEditDialog::setupUI()
{
    // 设置窗口属性
    setMinimumWidth(550);
    setMinimumHeight(320);
    
    // 创建主布局
    mainLayout = new QGridLayout();
    setLayout(mainLayout);  // 确保设置为对话框的布局
    
    // 创建控件
    QLabel* lblRFID = new QLabel("RFID ID:", this);
    QLabel* lblShoeCode = new QLabel("鞋码:", this);
    QLabel* lblFootSide = new QLabel("左/右脚:", this);
    QLabel* lblTemplateName = new QLabel("模板名称:", this);
    QLabel* lblLaserWorkDir = new QLabel("激光工作目录:", this);
    lblTemplatePresets = new QLabel("鞋子类型:", this);
    QString deviceStatusStyle = "font-size: 14px; font-weight: bold; color: #2c3e50; padding: 2px;";
    lblRFID->setStyleSheet(deviceStatusStyle);
    lblShoeCode->setStyleSheet(deviceStatusStyle);
    lblFootSide->setStyleSheet(deviceStatusStyle);
    lblTemplateName->setStyleSheet(deviceStatusStyle);
    lblLaserWorkDir->setStyleSheet(deviceStatusStyle);
    lblTemplatePresets->setStyleSheet(deviceStatusStyle);


    
    edtRFID = new QLineEdit(this);
    spnShoeCode = new QSpinBox(this);
    spnShoeCode->setRange(16, 47); // 鞋码范围16-47
    spnShoeCode->setValue(40);     // 默认40码
    
    radLeft = new QRadioButton("左脚", this);
    radRight = new QRadioButton("右脚", this);
    
    // 设置QRadioButton的自定义样式，使其更容易观察
    QString radioButtonStyle = 
        "QRadioButton {"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    color: #333333;"
        "    spacing: 8px;"
        "    padding: 8px;"
        "    border: 2px solid #cccccc;"
        "    border-radius: 8px;"
        "    background-color: #f8f8f8;"
        "    margin: 2px;"
        "}"
        "QRadioButton:hover {"
        "    background-color: #e8f4fd;"
        "    border-color: #4a90e2;"
        "    color: #2c5aa0;"
        "}"
        "QRadioButton:checked {"
        "    background-color: #d4edda;"
        "    border-color: #28a745;"
        "    color: #155724;"
        "    font-weight: bold;"
        "}"
        "QRadioButton::indicator {"
        "    width: 20px;"
        "    height: 20px;"
        "    border-radius: 10px;"
        "    border: 2px solid #cccccc;"
        "    background-color: white;"
        "}"
        "QRadioButton::indicator:hover {"
        "    border-color: #4a90e2;"
        "    background-color: #e8f4fd;"
        "}"
        "QRadioButton::indicator:checked {"
        "    border-color: #28a745;"
        "    background-color: #28a745;"
        "    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iNiIgY3k9IjYiIHI9IjMiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=);"
        "}"
        "QRadioButton::indicator:checked:hover {"
        "    background-color: #218838;"
        "    border-color: #1e7e34;"
        "}";
    
    radLeft->setStyleSheet(radioButtonStyle);
    radRight->setStyleSheet(radioButtonStyle);
    
    // 设置最小尺寸以确保样式效果
    radLeft->setMinimumSize(80, 40);
    radRight->setMinimumSize(80, 40);
    
    // 创建单选按钮组
    footSideGroup = new QButtonGroup(this);
    footSideGroup->addButton(radLeft, 0);
    footSideGroup->addButton(radRight, 1);
    radLeft->setChecked(true);
    
    // 创建其他控件
    edtTemplateName = new QLineEdit(this);
    edtLaserWorkDir = new QLineEdit(this);
    cmbTemplatePresets = new QLineEdit(this);
    cmbTemplatePresets->setText("军鞋");
    
    // 创建按钮
    btnBrowseLaserWorkDir = new QPushButton("浏览...", this);
    btnCreateDefaultWorkDir = new QPushButton("创建默认目录", this);
    btnReadRFID = new QPushButton("读取RFID", this);
    btnOK = new QPushButton("确定", this);
    btnCancel = new QPushButton("取消", this);
    
    // 创建机器人控制按钮
    btnMoveToFace = new QPushButton("移动到指定面", this);
    btnMoveToPreviousFace = new QPushButton("上一面", this);
    btnMoveToNextFace = new QPushButton("下一面", this);
    btnCompleteOperation = new QPushButton("完成", this);
    btnLaserReady = new QPushButton("激光准备好", this);
    spnTargetFace = new QSpinBox(this);
    spnTargetFace->setRange(1, 10);
    spnTargetFace->setValue(1);
    spnTargetFace->setPrefix("面 ");


    btnMoveToPreviousFace->setHidden(true);
    btnMoveToNextFace->setHidden(true);
    btnLaserReady->setHidden(true);
    
    // 添加控件到布局
    int row = 0;
    mainLayout->addWidget(lblRFID, row, 0);
    mainLayout->addWidget(edtRFID, row, 1);
    mainLayout->addWidget(btnReadRFID, row, 2);
    
    row++;
    mainLayout->addWidget(lblShoeCode, row, 0);
    mainLayout->addWidget(spnShoeCode, row, 1, 1, 2);
    
    row++;
    mainLayout->addWidget(lblFootSide, row, 0);
    QHBoxLayout* footLayout = new QHBoxLayout();
    footLayout->addWidget(radLeft);
    footLayout->addWidget(radRight);
    footLayout->addStretch(1);
    mainLayout->addLayout(footLayout, row, 1, 1, 2);
    
    row++;
    mainLayout->addWidget(lblTemplatePresets, row, 0);
    mainLayout->addWidget(cmbTemplatePresets, row, 1, 1, 2);
    
    row++;
    mainLayout->addWidget(lblTemplateName, row, 0);
    mainLayout->addWidget(edtTemplateName, row, 1, 1, 2);
    
    row++;
    mainLayout->addWidget(lblLaserWorkDir, row, 0);
    mainLayout->addWidget(edtLaserWorkDir, row, 1);
    
    // 创建按钮布局
    QHBoxLayout* dirBtnLayout = new QHBoxLayout();
    dirBtnLayout->addWidget(btnBrowseLaserWorkDir);
    dirBtnLayout->addWidget(btnCreateDefaultWorkDir);
    dirBtnLayout->setContentsMargins(0, 0, 0, 0);
    dirBtnLayout->setSpacing(5);
    QWidget* dirBtnContainer = new QWidget(this);
    dirBtnContainer->setLayout(dirBtnLayout);
    mainLayout->addWidget(dirBtnContainer, row, 2);
    
    row++;
    // 添加机器人控制区域
    QLabel* lblRobotControl = new QLabel("机器人手动控制:", this);
    lblRobotControl->setStyleSheet("font-size: 14px;font-weight: bold; color: #2c5aa0;");
    mainLayout->addWidget(lblRobotControl, row, 0, 1, 3);
    
    row++;
    QHBoxLayout* robotControlLayout = new QHBoxLayout();
    robotControlLayout->addWidget(spnTargetFace);
    robotControlLayout->addWidget(btnMoveToFace);
    robotControlLayout->addWidget(btnMoveToPreviousFace);
    robotControlLayout->addWidget(btnMoveToNextFace);
    robotControlLayout->addWidget(btnCompleteOperation);
    robotControlLayout->addWidget(btnLaserReady);
    robotControlLayout->setContentsMargins(0, 0, 0, 0);
    robotControlLayout->setSpacing(5);
    QWidget* robotControlContainer = new QWidget(this);
    robotControlContainer->setLayout(robotControlLayout);
    mainLayout->addWidget(robotControlContainer, row, 0, 1, 3);
    
    row++;
    QHBoxLayout* btnLayout = new QHBoxLayout();
    btnLayout->addStretch();
    btnLayout->addWidget(btnOK);
    btnLayout->addWidget(btnCancel);
    btnLayout->setContentsMargins(0, 0, 0, 0);
    btnLayout->setSpacing(10);
    QWidget* btnContainer = new QWidget(this);
    btnContainer->setLayout(btnLayout);
    mainLayout->addWidget(btnContainer, row, 0, 1, 3);
    
    // 设置布局属性
    mainLayout->setColumnStretch(1, 1);
    mainLayout->setVerticalSpacing(10);
    mainLayout->setHorizontalSpacing(10);
    mainLayout->setContentsMargins(10, 10, 10, 10);
    
    // 连接信号和槽
    connect(btnOK, &QPushButton::clicked, this, &TemplateEditDialog::onAccept);
    connect(btnCancel, &QPushButton::clicked, this, &TemplateEditDialog::onCancel);
    connect(btnBrowseLaserWorkDir, &QPushButton::clicked, this, &TemplateEditDialog::onBrowseLaserWorkDir);
    connect(edtRFID, &QLineEdit::textChanged, this, &TemplateEditDialog::onRFIDChanged);
    connect(btnReadRFID, &QPushButton::clicked, this, &TemplateEditDialog::onReadRFID);
    connect(btnCreateDefaultWorkDir, &QPushButton::clicked, this, &TemplateEditDialog::onCreateDefaultWorkDir);
    connect(spnShoeCode, QOverload<int>::of(&QSpinBox::valueChanged), this, &TemplateEditDialog::onShoeCodeChanged);
    connect(footSideGroup, QOverload<int>::of(&QButtonGroup::buttonClicked), [this](int) {
        this->onFootSideChanged();
    });
    
    // 连接机器人控制按钮信号
    connect(btnMoveToFace, &QPushButton::clicked, this, &TemplateEditDialog::onMoveToFace);
    connect(btnMoveToPreviousFace, &QPushButton::clicked, this, &TemplateEditDialog::onMoveToPreviousFace);
    connect(btnMoveToNextFace, &QPushButton::clicked, this, &TemplateEditDialog::onMoveToNextFace);
    connect(btnCompleteOperation, &QPushButton::clicked, this, &TemplateEditDialog::onCompleteOperation);
    connect(btnLaserReady, &QPushButton::clicked, this, &TemplateEditDialog::onLaserReady);
}

void TemplateEditDialog::loadTemplatePresets()
{
    if (!cmbTemplatePresets)
        return;
    
    // 连接文本变更事件
    connect(cmbTemplatePresets, &QLineEdit::textChanged,
            [this](const QString& text) {
        if (!text.isEmpty()) {
            // 当鞋子类型输入框内容变化时，更新模板名称
            this->onShoeCodeChanged(spnShoeCode->value());
        }
    });
}

ShoeTemplate TemplateEditDialog::getTemplateData() const
{
    ShoeTemplate templ;
    
    templ.rfidId = edtRFID ? edtRFID->text() : QString();
    templ.shoeCode = spnShoeCode ? spnShoeCode->value() : 40;
    templ.isLeftFoot = radLeft ? radLeft->isChecked() : true;
    templ.templateName = edtTemplateName ? edtTemplateName->text() : QString();
    templ.laserWorkDir = edtLaserWorkDir ? edtLaserWorkDir->text() : QString();
    templ.markCount = isEditMode ? originalTemplate.markCount : 0;
    templ.lastMarkTime = isEditMode ? originalTemplate.lastMarkTime : QDateTime();
    
    return templ;
}

void TemplateEditDialog::setTemplateData(const ShoeTemplate& templ)
{
    if (!edtRFID || !spnShoeCode || !radLeft || !radRight || 
        !edtTemplateName || !edtLaserWorkDir)
        return;
        
    edtRFID->setText(templ.rfidId);
    spnShoeCode->setValue(templ.shoeCode);
    
    if (templ.isLeftFoot) {
        radLeft->setChecked(true);
    } else {
        radRight->setChecked(true);
    }
    
    edtTemplateName->setText(templ.templateName);
    edtLaserWorkDir->setText(templ.laserWorkDir);
    
    // 在编辑模式下，禁用RFID编辑
    if (isEditMode) {
        edtRFID->setEnabled(false);
    }
}

bool TemplateEditDialog::validateInput()
{
    if (!edtRFID || !edtTemplateName || !edtLaserWorkDir)
        return false;
        
    // 检查RFID
    if (edtRFID->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入RFID编码");
        edtRFID->setFocus();
        return false;
    }
    
    // 检查模板名称
    if (edtTemplateName->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请输入模板名称");
        edtTemplateName->setFocus();
        return false;
    }
    
    // 检查激光工作目录
    if (edtLaserWorkDir->text().isEmpty()) {
        QMessageBox::warning(this, "输入错误", "请选择激光工作目录");
        edtLaserWorkDir->setFocus();
        return false;
    }
    
    return true;
}

void TemplateEditDialog::onBrowseLaserWorkDir()
{
    // 获取当前工作目录路径作为默认起始目录
    QString startDir = QDir::currentPath();
    
    // 如果激光工作目录编辑框中已有路径，检查该路径是否存在
    if (edtLaserWorkDir && !edtLaserWorkDir->text().isEmpty()) {
        QString currentPath = edtLaserWorkDir->text();
        QDir checkDir(currentPath);
        
        if (checkDir.exists()) {
            // 如果目录存在，使用该目录作为起始目录
            startDir = currentPath;
        } else {
            // 如果目录不存在，尝试使用其父目录
            QFileInfo fileInfo(currentPath);
            QString parentPath = fileInfo.absolutePath();
            QDir parentDir(parentPath);
            if (parentDir.exists()) {
                startDir = parentPath;
            }
        }
    }
    
    QString dirName = QFileDialog::getExistingDirectory(this, "选择激光工作目录", 
                                                     startDir,
                                                     QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
    
    if (!dirName.isEmpty() && edtLaserWorkDir) {
        edtLaserWorkDir->setText(dirName);
    }
}

void TemplateEditDialog::onAccept()
{
    if (validateInput()) {
        QDialog::accept();
    }
}

void TemplateEditDialog::onCancel()
{
    QDialog::reject();
}

void TemplateEditDialog::onFootSideChanged()
{
    if (!cmbTemplatePresets || !spnShoeCode || !edtTemplateName || 
        !radLeft || !radRight)
        return;
        
    // 如果鞋子类型不为空，更新模板名称
    if (!cmbTemplatePresets->text().isEmpty()) {
        QString shoeType = cmbTemplatePresets->text();
        int shoeCode = spnShoeCode->value();
        bool isLeft = radLeft->isChecked();
        
        QString templateName = QString("%1_%2_%3")
                              .arg(shoeType)
                              .arg(shoeCode)
                              .arg(isLeft ? "左" : "右");
        
        edtTemplateName->setText(templateName);
    }
}

void TemplateEditDialog::onShoeCodeChanged(int value)
{
    if (!cmbTemplatePresets || !edtTemplateName || !radLeft)
        return;
        
    // 如果鞋子类型不为空，更新模板名称
    if (!cmbTemplatePresets->text().isEmpty()) {
        QString shoeType = cmbTemplatePresets->text();
        bool isLeft = radLeft->isChecked();
        
        QString templateName = QString("%1_%2_%3")
                              .arg(shoeType)
                              .arg(value)
                              .arg(isLeft ? "左" : "右");
        
        edtTemplateName->setText(templateName);
    }
}

void TemplateEditDialog::onRFIDChanged(const QString& text)
{
    // 当RFID变化时，可以根据需要更新其他字段
    Q_UNUSED(text);
}

void TemplateEditDialog::updateUIState()
{
    // 根据当前状态更新UI元素
}

void TemplateEditDialog::onReadRFID()
{
    // 检查MainWindow指针和RFID字段是否有效
    if (!mainWindow || !edtRFID) {
        QMessageBox::warning(this, "系统错误", "无法访问主窗口或RFID输入框！");
        return;
    }

    try {
        // 从MainWindow的RFID队列中读取数据
        QString rfidDecimal;
        {
            // 检查队列是否为空
            if (mainWindow->isRFIDQueueEmpty()) {
                QMessageBox::information(this, "队列为空", "RFID队列中暂无数据，请等待RFID读取器检测到标签！");
                return;
            }
            
            // 取出队列头部的RFID编码
            rfidDecimal = mainWindow->getNextRFIDFromQueue();
        }


        // 检查是否读取到有效数据
        if (rfidDecimal.isEmpty() || rfidDecimal == "0") {
            QMessageBox::information(this, "无效RFID", "队列中的RFID数据无效！");
            return;
        }

        // 设置RFID字段
        edtRFID->setText(rfidDecimal);

        // 直接解析RFID编码
        if (parseRFIDCode(rfidDecimal)) {
            QMessageBox::information(this, "读取成功", 
                QString("从RFID队列读取成功并已解析！\n十进制: %1").arg(rfidDecimal));
                
        
        } else {
            QMessageBox::warning(this, "解析失败", 
                QString("RFID读取成功但编码格式不符合规则！\n十进制: %1\n请检查RFID格式是否正确。")
                .arg(rfidDecimal));
        }

    } catch (const std::exception& e) {
        QMessageBox::critical(this, "读取错误", QString("读取RFID队列时发生错误：%1").arg(e.what()));
    } catch (...) {
        QMessageBox::critical(this, "读取错误", "读取RFID队列时发生未知错误！");
    }
}

bool TemplateEditDialog::parseRFIDCode(const QString& rfidCode)
{
    if (!spnShoeCode || !radLeft || !radRight || !edtTemplateName)
        return false;
        
    /**
     * RFID编码规则解析：
     * 位置1：模具楦头号
     * 位置2-5：模具编号
     * 位置6-7：尺码
     * 位置8：尺码序号
     * 位置9：1为左脚，2为右脚
     */
    
    if (rfidCode.length() != 9) {
        return false;
    }
    
    bool ok;
    
    // 解析模具楦头号（第1位）
    int moldHeadNumber = rfidCode.mid(0, 1).toInt(&ok);
    if (!ok) return false;
    
    // 解析模具编号（第2-5位）
    int moldNumber = rfidCode.mid(1, 4).toInt(&ok);
    if (!ok) return false;
    
    // 解析尺码（第6-7位）
    int shoeSize = rfidCode.mid(5, 2).toInt(&ok);
    if (!ok || shoeSize < 16 || shoeSize > 47) return false;
    
    // 解析尺码序号（第8位）
    int sizeSequence = rfidCode.mid(7, 1).toInt(&ok);
    if (!ok) return false;
    
    // 解析左右脚（第9位）
    int footSide = rfidCode.mid(8, 1).toInt(&ok);
    if (!ok || (footSide != 1 && footSide != 2)) return false;
    
    // 自动填充解析出的信息
    spnShoeCode->setValue(shoeSize);
    
    if (footSide == 1) {
        radLeft->setChecked(true);
    } else {
        radRight->setChecked(true);
    }
    
    // 生成模板名称
    QString templateName = QString("模具%1_%2_尺码%3_%4_%5")
                          .arg(moldHeadNumber)
                          .arg(moldNumber, 4, 10, QChar('0'))
                          .arg(shoeSize)
                          .arg(sizeSequence)
                          .arg(footSide == 1 ? "左" : "右");
    
    edtTemplateName->setText(templateName);
    
    return true;
}

void TemplateEditDialog::onCreateDefaultWorkDir()
{
    if (!edtRFID || !edtLaserWorkDir) {
        QMessageBox::critical(this, "错误", "程序内部错误，无法创建目录!");
        return;
    }
    
    // 获取应用程序目录
    QString appDir = QApplication::applicationDirPath();
    
    // 构建data目录路径
    QDir dataDir(appDir);
    if (!dataDir.cd("data")) {
        // 如果data目录不存在，创建它
        if (!dataDir.mkdir("data")) {
            QMessageBox::critical(this, "错误", "无法创建data目录!");
            return;
        }
        dataDir.cd("data");
    }
    
    // 创建工作目录名称（使用当前日期和时间）
    QString workDirName;
    
    // 如果已有RFID，使用RFID作为目录名的一部分
    if (!edtRFID->text().isEmpty()) {
        workDirName = QString("Work_%1_%2").arg(edtRFID->text())
                                           .arg(QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss"));
    } else {
        // 否则只使用日期时间
        workDirName = QString("Work_%1").arg(QDateTime::currentDateTime().toString("yyyyMMdd_HHmmss"));
    }
    
    // 创建工作目录
    QDir workDir = dataDir;
    if (!workDir.mkdir(workDirName)) {
        QMessageBox::critical(this, "错误", "无法创建工作目录!");
        return;
    }
    
    // 获取完整路径
    QString fullPath = workDir.absoluteFilePath(workDirName);
    
    // 更新工作目录编辑框
    edtLaserWorkDir->setText(fullPath);
    
    // 用文件浏览器打开该目录
    QDesktopServices::openUrl(QUrl::fromLocalFile(fullPath));

}

// 机器人手动控制槽函数实现
void TemplateEditDialog::onMoveToFace()
{
    if (!abbDriver || !abbDriver->IsConnected()) {
        QMessageBox::warning(this, "机器人控制", "ABB机器人未连接，无法执行移动命令！");
        return;
    }

    onLaserReady();

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    int targetFace = spnTargetFace->value();
    int faceCode = 100 + targetFace; // 转换为面编码 (101, 102, 103...)
    
    // 发送移动到指定面的命令
    std::vector<double> params = {static_cast<double>(faceCode)};
    if (abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, params)) {
        QMessageBox::information(this, "机器人控制", 
            QString("已发送移动到面 %1 的命令").arg(targetFace));
    } else {
        QMessageBox::warning(this, "机器人控制", "发送移动命令失败！");
    }
}

void TemplateEditDialog::onMoveToPreviousFace()
{
    if (!abbDriver || !abbDriver->IsConnected()) {
        QMessageBox::warning(this, "机器人控制", "ABB机器人未连接，无法执行移动命令！");
        return;
    }
    
    int currentFace = spnTargetFace->value()+100;
    if (currentFace <= 1) {
        QMessageBox::information(this, "机器人控制", "已经是第一面，无法移动到上一面！");
        return;
    }
    
    int previousFace = currentFace - 1;
    spnTargetFace->setValue(previousFace);
    
    // 发送移动到上一面的命令
    std::vector<double> params = {static_cast<double>(previousFace)};
    if (abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, params)) {
        QMessageBox::information(this, "机器人控制", 
            QString("已发送移动到面 %1 的命令").arg(previousFace));
    } else {
        QMessageBox::warning(this, "机器人控制", "发送移动命令失败！");
    }
}

void TemplateEditDialog::onMoveToNextFace()
{
    if (!abbDriver || !abbDriver->IsConnected()) {
        QMessageBox::warning(this, "机器人控制", "ABB机器人未连接，无法执行移动命令！");
        return;
    }
    
    int currentFace = spnTargetFace->value();
    if (currentFace >= 10) {
        QMessageBox::information(this, "机器人控制", "已经是最后一面，无法移动到下一面！");
        return;
    }
    
    int nextFace = currentFace + 1;
    spnTargetFace->setValue(nextFace);
    
    // 发送移动到下一面的命令
    std::vector<double> params = {static_cast<double>(nextFace)};
    if (abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, params)) {
        QMessageBox::information(this, "机器人控制", 
            QString("已发送移动到下一面的命令").arg(nextFace));
    } else {
        QMessageBox::warning(this, "机器人控制", "发送移动命令失败！");
    }
}

void TemplateEditDialog::onCompleteOperation()
{
    if (!abbDriver || !abbDriver->IsConnected()) {
        QMessageBox::warning(this, "机器人控制", "ABB机器人未连接，无法执行完成命令！");
        return;
    }

    onLaserReady();

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    // 发送完成操作命令 (参数为0表示放到输送线)
    std::vector<double> params = {0.0};
    if (abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, params)) {
        QMessageBox::information(this, "机器人控制", "已发送完成操作命令，机器人将放置工件到输送线");
    } else {
        QMessageBox::warning(this, "机器人控制", "发送完成命令失败！");
    }
}

/**
 * @brief 激光准备好按钮点击处理函数
 * 向ABB机器人发送激光准备就绪命令，包含鞋码和左右脚信息
 */
void TemplateEditDialog::onLaserReady()
{
    if (!abbDriver || !abbDriver->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "ABB机器人未连接，无法发送激光准备命令！");
        return;
    }
    
    if (!spnShoeCode || !radLeft) {
        QMessageBox::warning(this, "激光控制", "界面控件未初始化，无法获取参数！");
        return;
    }
    
    // 准备参数：鞋码和左右脚标识
    std::vector<double> params = {static_cast<double>(spnShoeCode->value()), 
                                radLeft->isChecked() ? 1.0 : 0.0};
    
    // 发送激光准备就绪命令
    if (abbDriver->SendCommand(RobotDriver::ABBCommandType::LASER_READY, params)) {
        QString footSide = radLeft->isChecked() ? "左脚" : "右脚";
        QMessageBox::information(this, "激光控制", 
            QString("已发送激光准备就绪命令！\n鞋码: %1\n脚型: %2")
            .arg(spnShoeCode->value())
            .arg(footSide));
    } else {
        QMessageBox::warning(this, "激光控制", "发送激光准备命令失败！");
    }
}