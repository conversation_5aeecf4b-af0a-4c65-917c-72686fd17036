#include <QApplication>
#include <QFile>
#include <QFontDatabase>
#include <QSplashScreen>
#include <QPixmap>
#include <QThread>
#include <QMessageBox>
#include <QStyleFactory>
#include <QTextCodec>
#include <QTranslator>
#include <QDebug>
#include <QPainter>


#include "MainWindow.h"
#include "AppStyle.h"
#include "LicenseManager.h"
#include "RegisterDialog.h"
#include <windows.h>


#pragma execution_character_set("utf-8")

int main(int argc, char *argv[])
{

    // 设置高DPI缩放（必须在创建QApplication之前设置）
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    // 创建应用程序
    QApplication app(argc, argv);
    app.setApplicationName("皮鞋激光打粗控制系统V2.0");
    app.setOrganizationName("激光");

    
    // 强制本地化为简体中文
    QLocale::setDefault(QLocale(QLocale::Chinese, QLocale::China));
    
    // 设置全局字体，使用系统上一定存在的字体
    QFontDatabase fontDB;
    QStringList fontFamilies = fontDB.families();
    
    // 尝试查找中文字体
    QFont font;
    QStringList preferredFonts = {"Microsoft YaHei", "微软雅黑", "SimSun", "宋体", "SimHei", "黑体"};
    bool fontFound = false;
    
    for (const QString& fontName : preferredFonts) {
        if (fontFamilies.contains(fontName, Qt::CaseInsensitive)) {
            font = QFont(fontName, 9);
            fontFound = true;
            qDebug() << "使用字体:" << fontName;
            break;
        }
    }
    
    // 如果没找到首选字体，使用系统默认
    if (!fontFound) {
        font = QFont("Sans Serif", 9);
        qDebug() << "使用默认字体";
    }
    
    app.setFont(font);
    
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    app.setStyleSheet(AppStyle::MainStyleSheet);
    
    // 显示启动屏幕
    QPixmap splashPixmap(":/images/splash.png");
    if (splashPixmap.isNull()) {
        // 如果找不到启动图像，创建一个默认的启动屏幕
        splashPixmap = QPixmap(600, 400);
        splashPixmap.fill(QColor("#ffffff")); // 使用白色背景
        
        // 添加文字到启动画面
        QPainter painter(&splashPixmap);
        painter.setPen(QColor("#2980b9"));
        painter.setFont(QFont("Microsoft YaHei", 24, QFont::Bold));
        painter.drawText(QRect(0, 150, 600, 50), Qt::AlignCenter, "3515皮鞋激光打粗控制系统V2.0");
        painter.setFont(QFont("Microsoft YaHei", 12));
        // painter.drawText(QRect(0, 200, 600, 30), Qt::AlignCenter, "激光");
        painter.end();
    }
    
    // 创建启动画面
    QSplashScreen splash(splashPixmap);
    splash.show();
    app.processEvents();
    
    // 显示启动消息
    splash.showMessage("加载应用程序...", Qt::AlignBottom | Qt::AlignCenter, QColor("#2980b9"));
    
    // 强制应用样式表
    app.setStyle(QStyleFactory::create("Fusion"));
    app.setStyleSheet(AppStyle::MainStyleSheet);

    Sleep(3000);
    
    // 强制刷新样式
    QList<QWidget*> topLevelWidgets = QApplication::topLevelWidgets();
    for (QWidget* widget : topLevelWidgets) {
        widget->style()->unpolish(widget);
        widget->style()->polish(widget);
        widget->update();
    }
    
    // 检查注册状态
    LicenseManager licenseManager;
    bool continueExecution = true;
    
    if (!licenseManager.isRegistered()) {
        // 检查是否在试用期内
        if (licenseManager.isTrialAvailable()) {
            splash.showMessage("软件处于试用期，还剩 " + QString::number(licenseManager.getTrialDaysLeft()) + " 天...",
                            Qt::AlignBottom | Qt::AlignCenter, QColor("#e67e22"));
            // 给用户一个试用提示
            QThread::msleep(1000);
        } else {
            // 试用期已过，必须注册
            splash.showMessage("软件未注册，需要激活...", Qt::AlignBottom | Qt::AlignCenter, QColor("#e74c3c"));
            QThread::msleep(1000);
            
            // 关闭启动画面显示注册对话框
            splash.close();
            
            RegisterDialog registerDialog(&licenseManager);
            if (registerDialog.exec() != QDialog::Accepted) {
                QMessageBox::critical(nullptr, "注册失败", "软件未注册，无法继续使用。");
                continueExecution = false;
            }
        }
    }
    
    // 如果注册检查通过，继续启动
    if (continueExecution) {
        // 创建并显示主窗口
        MainWindow mainWindow;
        
        // 确保所有子控件都应用样式表
        QList<QWidget*> allWidgets = mainWindow.findChildren<QWidget*>();
        for (QWidget* widget : allWidgets) {
            widget->setStyle(QApplication::style());
            widget->update();
        }
        
        // 短暂延迟以显示启动屏幕
        QThread::msleep(1000);
        
        // 显示主窗口并关闭启动屏幕
        mainWindow.showMaximized();
        splash.finish(&mainWindow);
        
        return app.exec();
    }
    
    return 0;
} 