#include "DetailedReportWidget.h"
#include "DatabaseQueryWorker.h"
#include "UIUtils.h"
#include <QMessageBox>
#include <QHeaderView>
#include <QApplication>
#include <QDebug>
#include <QElapsedTimer>
#include <QMetaType>
#pragma execution_character_set("utf-8")

DetailedReportWidget::DetailedReportWidget(std::shared_ptr<ProductionStatisticsManager> statsManager,
                                         QWidget *parent)
    : QWidget(parent)
    , m_statsManager(statsManager)
    , m_mainLayout(nullptr)
    , m_mainSplitter(nullptr)
    , m_filterPanel(nullptr)
    , m_startTimeEdit(nullptr)
    , m_endTimeEdit(nullptr)
    , m_templateCombo(nullptr)
    , m_statusCombo(nullptr)
    , m_showErrorsOnlyCheck(nullptr)
    , m_dataPanel(nullptr)
    , m_dataTable(nullptr)
    , m_recordCountLabel(nullptr)
    , m_controlPanel(nullptr)
    , m_queryButton(nullptr)
    , m_refreshButton(nullptr)
    , m_isInitializing(true)
    , m_isQuerying(false)
    , m_queryThread(nullptr)
    , m_queryWorker(nullptr)
    , m_currentRecordsRequestId(-1)
    , m_waitingForRecords(false)
{
    // Register meta types for queued connections
    qRegisterMetaType<QVector<ProductionRecord>>("QVector<ProductionRecord>");
    qRegisterMetaType<ProductionRecord>("ProductionRecord");
    qRegisterMetaType<ProductionSummary>("ProductionSummary");
    
    setupUI();
    initializeQueryThread();
    
    // Initialize with default filter
    m_currentFilter = getCurrentFilter();
    
    m_isInitializing = false;
    qDebug() << "DetailedReportWidget initialization completed";
}

DetailedReportWidget::~DetailedReportWidget()
{
    if (m_queryThread) {
        m_queryThread->stopWorker();
        m_queryThread->wait(3000);
        delete m_queryThread;
        m_queryThread = nullptr;
    }
}

void DetailedReportWidget::setupUI()
{
    // 设置组件的基本属性
    setMinimumSize(400, 300); // 减小最小尺寸
    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // 临时添加背景色用于调试
    setStyleSheet("DetailedReportWidget { background-color: #e8f4fd; border: 2px solid #2196F3; }");

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(5, 5, 5, 5);
    m_mainLayout->setSpacing(5);

    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Vertical, this);

    // Create filter panel
    createFilterPanel();
    m_mainSplitter->addWidget(m_filterPanel);

    // Create data panel
    createDataPanel();
    m_mainSplitter->addWidget(m_dataPanel);

    // Create control panel
    createControlPanel();
    m_mainSplitter->addWidget(m_controlPanel);

    // Set splitter proportions
    m_mainSplitter->setStretchFactor(0, 0); // Filter panel - fixed size
    m_mainSplitter->setStretchFactor(1, 1); // Data panel - expandable
    m_mainSplitter->setStretchFactor(2, 0); // Control panel - fixed size
    m_mainSplitter->setSizes({120, 500, 60});

    m_mainLayout->addWidget(m_mainSplitter);

    // 确保组件可见
    show();
}

void DetailedReportWidget::createFilterPanel()
{
    m_filterPanel = UIUtils::createGroupBox("查询条件", UIUtils::FontSize::Medium, true, this);
    QGridLayout* filterLayout = UIUtils::createGridLayout();
    m_filterPanel->setLayout(filterLayout);

    // Time range
    QLabel* startLabel = UIUtils::createLabel("开始时间:", UIUtils::FontSize::Medium, false, m_filterPanel);

    // Set start time to today 00:00:00
    QDateTime startDateTime = QDateTime::currentDateTime();
    startDateTime.setTime(QTime(0, 0, 0));
    m_startTimeEdit = UIUtils::createDateTimeEdit(startDateTime, UIUtils::FontSize::Medium, m_filterPanel);

    QLabel* endLabel = UIUtils::createLabel("结束时间:", UIUtils::FontSize::Medium, false, m_filterPanel);

    // Set end time to today 23:59:59
    QDateTime endDateTime = QDateTime::currentDateTime();
    endDateTime.setTime(QTime(23, 59, 59));
    m_endTimeEdit = UIUtils::createDateTimeEdit(endDateTime, UIUtils::FontSize::Medium, m_filterPanel);

    // Template filter
    QLabel* templateLabel = UIUtils::createLabel("模板:", UIUtils::FontSize::Medium, false, m_filterPanel);
    QStringList templateItems = {"所有模板"};
    m_templateCombo = UIUtils::createComboBox(templateItems, UIUtils::FontSize::Medium, m_filterPanel);
    m_templateCombo->setItemData(0, "");

    // Status filter
    QLabel* statusLabel = UIUtils::createLabel("状态:", UIUtils::FontSize::Medium, false, m_filterPanel);
    QStringList statusItems = {"所有状态", "仅成功", "仅失败"};
    m_statusCombo = UIUtils::createComboBox(statusItems, UIUtils::FontSize::Medium, m_filterPanel);
    m_statusCombo->setItemData(0, -1);
    m_statusCombo->setItemData(1, static_cast<int>(ProductionStatus::SUCCESS));
    m_statusCombo->setItemData(2, static_cast<int>(ProductionStatus::FAILED));

    // Show errors only checkbox
    m_showErrorsOnlyCheck = UIUtils::createCheckBox("仅显示错误记录", UIUtils::FontSize::Medium, m_filterPanel);

    // Layout
    filterLayout->addWidget(startLabel, 0, 0);
    filterLayout->addWidget(m_startTimeEdit, 0, 1);
    filterLayout->addWidget(endLabel, 0, 2);
    filterLayout->addWidget(m_endTimeEdit, 0, 3);
    filterLayout->addWidget(templateLabel, 1, 0);
    filterLayout->addWidget(m_templateCombo, 1, 1);
    filterLayout->addWidget(statusLabel, 1, 2);
    filterLayout->addWidget(m_statusCombo, 1, 3);
    filterLayout->addWidget(m_showErrorsOnlyCheck, 2, 0, 1, 4);

    // Connect signals
    connect(m_startTimeEdit, &QDateTimeEdit::dateTimeChanged, this, &DetailedReportWidget::onFilterChanged);
    connect(m_endTimeEdit, &QDateTimeEdit::dateTimeChanged, this, &DetailedReportWidget::onFilterChanged);
    connect(m_templateCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &DetailedReportWidget::onFilterChanged);
    connect(m_statusCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &DetailedReportWidget::onFilterChanged);
    connect(m_showErrorsOnlyCheck, &QCheckBox::toggled, this, &DetailedReportWidget::onFilterChanged);
}

void DetailedReportWidget::createDataPanel()
{
    m_dataPanel = new QWidget();
    QVBoxLayout* dataLayout = UIUtils::createVBoxLayout();
    m_dataPanel->setLayout(dataLayout);

    // Record count label
    m_recordCountLabel = UIUtils::createLabel("记录数: 0", UIUtils::FontSize::Medium, true, m_dataPanel);
    dataLayout->addWidget(m_recordCountLabel);

    // Data table
    QStringList headers = {"开始时间", "结束时间", "模板", "RFID", "鞋码", "左右脚",
                          "完成面数", "状态", "处理时间", "效率", "成功率", "错误信息"};
    m_dataTable = UIUtils::createTableWidget(headers, UIUtils::FontSize::Small, m_dataPanel);

    dataLayout->addWidget(m_dataTable);
}

void DetailedReportWidget::createControlPanel()
{
    m_controlPanel = new QWidget();
    QHBoxLayout* controlLayout = UIUtils::createHBoxLayout();
    m_controlPanel->setLayout(controlLayout);

    // Create control buttons using UIUtils
    m_queryButton = UIUtils::createButton("查询", UIUtils::ButtonStyle::Primary, UIUtils::FontSize::Medium, m_controlPanel);
    m_refreshButton = UIUtils::createButton("刷新", UIUtils::ButtonStyle::Warning, UIUtils::FontSize::Medium, m_controlPanel);

    controlLayout->addWidget(m_queryButton);
    controlLayout->addWidget(m_refreshButton);
    controlLayout->addStretch();

    // Connect signals
    connect(m_queryButton, &QPushButton::clicked, this, &DetailedReportWidget::onQueryClicked);
    connect(m_refreshButton, &QPushButton::clicked, this, &DetailedReportWidget::onRefreshClicked);
}

void DetailedReportWidget::initializeQueryThread()
{
    if (!m_statsManager) {
        qDebug() << "Statistics manager not available for query thread";
        return;
    }

    try {
        m_queryThread = new DatabaseQueryThread(m_statsManager, this);

        // Connect thread signals
        connect(m_queryThread, &DatabaseQueryThread::workerReady,
                this, &DetailedReportWidget::onWorkerReady, Qt::QueuedConnection);
        connect(m_queryThread, &DatabaseQueryThread::workerError,
                this, &DetailedReportWidget::onWorkerError, Qt::QueuedConnection);

        qDebug() << "Database query thread created successfully";
    } catch (const std::exception& e) {
        qDebug() << "Failed to create query thread:" << e.what();
        m_queryThread = nullptr;
    } catch (...) {
        qDebug() << "Failed to create query thread: unknown error";
        m_queryThread = nullptr;
    }
}

void DetailedReportWidget::queryData()
{
    qDebug() << "=== DetailedReportWidget::queryData() called ===";

    if (!m_statsManager) {
        qDebug() << "Statistics manager not available";
        emit errorOccurred("统计管理器不可用");
        return;
    }

    if (m_isQuerying) {
        qDebug() << "Query already in progress, skipping...";
        return;
    }

    qDebug() << "Starting new query...";
    m_isQuerying = true;
    m_queryButton->setEnabled(false);
    m_queryButton->setText("查询中...");
    m_refreshButton->setEnabled(false);

    emit queryStarted();

    // Update current filter with correct time range
    m_currentFilter = getCurrentFilter();

    qDebug() << "Query filter - Start:" << m_currentFilter.startTime
             << "End:" << m_currentFilter.endTime
             << "Templates:" << m_currentFilter.templateNames
             << "Status filters:" << m_currentFilter.statusFilter.size();

    // Clear existing data
    m_dataTable->setRowCount(0);
    m_recordCountLabel->setText("正在查询...");

    // Try to use database query worker if available
    if (m_queryThread) {
        try {
            // Start worker if not already running
            m_queryThread->startWorker();

            // Wait a short time for worker to be ready (with timeout)
            QElapsedTimer timer;
            timer.start();
            const int maxWaitMs = 1000; // 1 second timeout

            while (timer.elapsed() < maxWaitMs) {
                DatabaseQueryWorker* currentWorker = m_queryThread->worker();
                if (currentWorker) {
                    // Worker is ready
                    if (currentWorker != m_queryWorker) {
                        // Disconnect old signals if worker changed
                        if (m_queryWorker) {
                            disconnect(m_queryWorker, nullptr, this, nullptr);
                        }

                        m_queryWorker = currentWorker;

                        // Connect signals to worker
                        connect(m_queryWorker, &DatabaseQueryWorker::recordsReady,
                                this, &DetailedReportWidget::onRecordsReady, Qt::QueuedConnection);
                        connect(m_queryWorker, &DatabaseQueryWorker::queryError,
                                this, &DetailedReportWidget::onQueryError, Qt::QueuedConnection);
                        connect(m_queryWorker, &DatabaseQueryWorker::queryTimeout,
                                this, &DetailedReportWidget::onQueryTimeout, Qt::QueuedConnection);
                        connect(m_queryWorker, &DatabaseQueryWorker::queryStarted,
                                this, &DetailedReportWidget::onQueryStarted, Qt::QueuedConnection);

                        qDebug() << "Database query worker connected successfully";
                    }
                    break;
                }

                // Process events to allow thread to start
                QApplication::processEvents();
                QThread::msleep(10);
            }

            if (!m_queryWorker) {
                qDebug() << "Worker not available after timeout, will use fallback";
            }
        } catch (const std::exception& e) {
            qDebug() << "Failed to start query thread:" << e.what();
            m_queryWorker = nullptr;
        }
    }

    if (m_queryWorker) {
        // Use async query
        qDebug() << "Using async query with worker:" << m_queryWorker;
        m_waitingForRecords = true;
        m_currentRecordsRequestId = m_queryWorker->requestRecords(m_currentFilter);
        qDebug() << "Started async query with request ID:" << m_currentRecordsRequestId;
    } else {
        // Fallback to direct query
        qDebug() << "Using fallback direct query";

        // Add detailed debugging for Release mode
        qDebug() << "=== DETAILED DEBUG INFO ===";
        qDebug() << "Stats manager available:" << (m_statsManager ? "YES" : "NO");
        if (m_statsManager) {
            qDebug() << "Filter start time:" << m_currentFilter.startTime.toString();
            qDebug() << "Filter end time:" << m_currentFilter.endTime.toString();
            qDebug() << "Filter templates:" << m_currentFilter.templateNames;
        }

        QTimer::singleShot(50, this, [this]() {
            qDebug() << "Executing direct query...";
            try {
                // Add more detailed debugging
                qDebug() << "About to call getRecords()...";
                QVector<ProductionRecord> records = m_statsManager->getRecords(m_currentFilter);
                qDebug() << "Direct query returned" << records.size() << "records";

                // Log first few records for debugging
                for (int i = 0; i < qMin(3, records.size()); ++i) {
                    const auto& record = records[i];
                    qDebug() << "Record" << i << ":" << record.recordId
                             << "Start:" << record.startTime.toString()
                             << "Template:" << record.templateName;
                }

                onRecordsReady(-1, records);
            } catch (const std::exception& e) {
                qDebug() << "Direct query failed with std::exception:" << e.what();
                onQueryError(-1, QString("Query failed: %1").arg(e.what()));
            } catch (...) {
                qDebug() << "Direct query failed with unknown exception";
                onQueryError(-1, "Query failed with unknown error");
            }
        });
    }
}

// Slot implementations
void DetailedReportWidget::onQueryClicked()
{
    queryData();
}

void DetailedReportWidget::onRefreshClicked()
{
    refreshData();
}

void DetailedReportWidget::onFilterChanged()
{
    if (m_isInitializing) {
        return;
    }
    // Auto query when filter changes
    queryData();
}

void DetailedReportWidget::refreshData()
{
    queryData();
}

void DetailedReportWidget::onRecordsReady(int requestId, const QVector<ProductionRecord>& records)
{
    if (requestId != m_currentRecordsRequestId && requestId != -1) {
        LogUtils::logDebug(QString("Received records for outdated request %1, current: %2")
                          .arg(requestId).arg(m_currentRecordsRequestId), "查询");
        return;
    }

    LogUtils::logInfo(QString("Received %1 records for request %2").arg(records.size()).arg(requestId), "查询");

    m_waitingForRecords = false;
    m_currentRecords = records;

    // Filter records if "Show Errors Only" is checked
    QVector<ProductionRecord> displayRecords = m_currentRecords;
    if (m_showErrorsOnlyCheck && m_showErrorsOnlyCheck->isChecked()) {
        displayRecords.clear();
        for (const auto& record : m_currentRecords) {
            if (record.status != ProductionStatus::SUCCESS || !record.errorMessage.isEmpty()) {
                displayRecords.append(record);
            }
        }
    }

    // Update UI state using DatabaseUtils
    DatabaseUtils::setQueryState(m_queryButton, m_refreshButton, m_recordCountLabel,
                                DatabaseUtils::QueryState::Success,
                                QString("记录数: %1").arg(displayRecords.size()));

    // Populate table
    populateTableAsync(displayRecords, 0);

    m_isQuerying = false;

    // Emit signals
    emit dataLoaded(displayRecords.size());
    emit queryFinished();
}

void DetailedReportWidget::onQueryError(int requestId, const QString& error)
{
    LogUtils::logError(QString("Query error for request %1: %2").arg(requestId).arg(error), "查询");

    // Update UI state using DatabaseUtils
    DatabaseUtils::setQueryState(m_queryButton, m_refreshButton, m_recordCountLabel,
                                DatabaseUtils::QueryState::Error, "查询失败");

    m_isQuerying = false;

    // Emit error signal
    emit errorOccurred(error);
    emit queryFinished();
}

void DetailedReportWidget::onQueryTimeout(int requestId)
{
    qDebug() << "Query timeout for request" << requestId;

    m_isQuerying = false;
    m_queryButton->setEnabled(true);
    m_queryButton->setText("查询");
    m_refreshButton->setEnabled(true);

    m_recordCountLabel->setText("查询超时");

    // Emit error signal
    emit errorOccurred("数据库查询超时，请尝试缩小查询范围或稍后重试");
    emit queryFinished();
}

void DetailedReportWidget::onQueryStarted(int requestId)
{
    qDebug() << "Query started for request" << requestId;
    m_recordCountLabel->setText("正在查询数据库...");
}

void DetailedReportWidget::onWorkerReady()
{
    qDebug() << "Database worker is ready";
    // Worker is now available, we can proceed with queries
}

void DetailedReportWidget::onWorkerError(const QString& error)
{
    qDebug() << "Database worker error:" << error;
    // Worker failed to initialize, fall back to direct queries
    m_queryWorker = nullptr;
}

void DetailedReportWidget::populateTableAsync(const QVector<ProductionRecord>& records, int startIndex)
{
    if (!m_dataTable) {
        qDebug() << "Data table not available";
        return;
    }

    // Set table row count
    m_dataTable->setRowCount(records.size());

    // Populate table with records
    for (int i = 0; i < records.size(); ++i) {
        const ProductionRecord& record = records[i];

        // Start time
        QTableWidgetItem* startTimeItem = new QTableWidgetItem(formatDateTime(record.startTime));
        startTimeItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 0, startTimeItem);

        // End time
        QTableWidgetItem* endTimeItem = new QTableWidgetItem(formatDateTime(record.endTime));
        endTimeItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 1, endTimeItem);

        // Template
        QTableWidgetItem* templateItem = new QTableWidgetItem(record.templateName);
        templateItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 2, templateItem);

        // RFID
        QTableWidgetItem* rfidItem = new QTableWidgetItem(record.rfidCode);
        rfidItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 3, rfidItem);

        // Shoe size
        QTableWidgetItem* sizeItem = new QTableWidgetItem(QString::number(record.shoeCode));
        sizeItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 4, sizeItem);

        // Left/Right foot
        QString footType = (record.isLeftFoot) ? "左脚" : "右脚";
        QTableWidgetItem* footItem = new QTableWidgetItem(footType);
        footItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 5, footItem);

        // Completed faces
        QTableWidgetItem* facesItem = new QTableWidgetItem(QString::number(record.completedFaces));
        facesItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 6, facesItem);

        // Status
        QString statusText;
        switch (record.status) {
            case ProductionStatus::SUCCESS:
                statusText = "成功";
                break;
            case ProductionStatus::FAILED:
                statusText = "失败";
                break;
            default:
                statusText = "未知";
                break;
        }
        QTableWidgetItem* statusItem = new QTableWidgetItem(statusText);
        statusItem->setFont(QFont("Microsoft YaHei", 12));

        // Color code status
        if (record.status == ProductionStatus::SUCCESS) {
            statusItem->setBackground(QBrush(QColor(200, 255, 200))); // Light green
        } else {
            statusItem->setBackground(QBrush(QColor(255, 200, 200))); // Light red
        }
        m_dataTable->setItem(i, 7, statusItem);

        // Processing time
        qint64 processingTime = record.startTime.msecsTo(record.endTime);
        QTableWidgetItem* timeItem = new QTableWidgetItem(formatDuration(processingTime));
        timeItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 8, timeItem);

        // Efficiency (faces per second)
        double efficiency = 0.0;
        if (processingTime > 0) {
            efficiency = (double)record.completedFaces / (processingTime / 1000.0);
        }
        QTableWidgetItem* efficiencyItem = new QTableWidgetItem(QString::number(efficiency, 'f', 2) + " faces/s");
        efficiencyItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 9, efficiencyItem);

        // Success rate (for this record, it's either 100% or 0%)
        QString successRate = (record.status == ProductionStatus::SUCCESS) ? "100%" : "0%";
        QTableWidgetItem* successItem = new QTableWidgetItem(successRate);
        successItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 10, successItem);

        // Error message
        QTableWidgetItem* errorItem = new QTableWidgetItem(record.errorMessage);
        errorItem->setFont(QFont("Microsoft YaHei", 12));
        m_dataTable->setItem(i, 11, errorItem);
    }

    // Resize columns to content
    m_dataTable->resizeColumnsToContents();

    qDebug() << "Populated table with" << records.size() << "records";
}

StatisticsFilter DetailedReportWidget::getCurrentFilter() const
{
    StatisticsFilter filter;

    if (m_startTimeEdit && m_endTimeEdit) {
        filter.startTime = m_startTimeEdit->dateTime();
        filter.endTime = m_endTimeEdit->dateTime();
    } else {
        // Default to today's range if controls not initialized
        QDateTime now = QDateTime::currentDateTime();
        filter.startTime = QDateTime(now.date(), QTime(0, 0, 0));
        filter.endTime = QDateTime(now.date(), QTime(23, 59, 59));
    }

    if (m_templateCombo && m_templateCombo->currentData().isValid()) {
        QString templateName = m_templateCombo->currentData().toString();
        if (!templateName.isEmpty()) {
            filter.templateNames.append(templateName);
        }
    }

    if (m_statusCombo && m_statusCombo->currentData().isValid()) {
        QVariant statusData = m_statusCombo->currentData();
        if (statusData.toInt() != -1) {
            ProductionStatus status = static_cast<ProductionStatus>(statusData.toInt());
            filter.statusFilter.append(status);
        }
    }

    return filter;
}

QVector<ProductionRecord> DetailedReportWidget::getFilteredRecords() const
{
    if (!m_statsManager) {
        return QVector<ProductionRecord>();
    }

    StatisticsFilter filter = getCurrentFilter();
    return m_statsManager->getRecords(filter);
}

QString DetailedReportWidget::formatDuration(qint64 milliseconds) const
{
    if (milliseconds <= 0) {
        return "0秒";
    }

    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    qint64 hours = minutes / 60;

    seconds %= 60;
    minutes %= 60;

    if (hours > 0) {
        return QString("%1小时%2分%3秒").arg(hours).arg(minutes).arg(seconds);
    } else if (minutes > 0) {
        return QString("%1分%2秒").arg(minutes).arg(seconds);
    } else {
        return QString("%1秒").arg(seconds);
    }
}

QString DetailedReportWidget::formatDateTime(const QDateTime& dateTime) const
{
    if (!dateTime.isValid()) {
        return "-";
    }
    return dateTime.toString("yyyy-MM-dd hh:mm:ss");
}
