#include "LicenseManager.h"
#include <QDateTime>
#include <QCryptographicHash>
#include <QSysInfo>
#include <QDebug>
#include <QSettings>
#include <QNetworkInterface>

#pragma execution_character_set("utf-8")

LicenseManager::LicenseManager()
{
    // 构造函数简化，不再需要加载文件
}

LicenseManager::~LicenseManager()
{
}

bool LicenseManager::isAuthorized() const
{
    // 获取当前机器的所有MAC地址
    QStringList currentMACs = getAllMACAddresses();
    
    // 检查是否有任一MAC地址在白名单中
    for (const QString& mac : currentMACs) {
        if (isMACInWhitelist(mac)) {
            return true;
        }
    }
    
    return false;
}

bool LicenseManager::isRegistered() const
{
    // 为了兼容性，调用isAuthorized
    return isAuthorized();
}

QString LicenseManager::getLastError() const
{
    return m_lastError;
}

QStringList LicenseManager::getMACAddresses() const
{
    return getAllMACAddresses();
}

QString LicenseManager::getMachineCode() const
{
    return generateMachineCode();
}

int LicenseManager::getTrialDaysLeft() const
{
    // 检查试用期
    QSettings settings;
    QDateTime firstRun = settings.value("License/FirstRun", QDateTime::currentDateTime()).toDateTime();
    
    // 如果是第一次运行，记录时间
    if (!settings.contains("License/FirstRun")) {
        settings.setValue("License/FirstRun", firstRun);
    }
    
    int daysUsed = firstRun.daysTo(QDateTime::currentDateTime());
    int daysLeft = TRIAL_DAYS - daysUsed;
    
    return qMax(0, daysLeft);
}

bool LicenseManager::isTrialAvailable() const
{
    return getTrialDaysLeft() > 0;
}

QString LicenseManager::generateMachineCode() const
{
    // 获取第一个有效的MAC地址
    QStringList macAddresses = getAllMACAddresses();
    if (macAddresses.isEmpty()) {
        return "UNKNOWN-MACHINE";
    }
    
    QString primaryMAC = macAddresses.first();
    
    // 生成唯一ID
    QByteArray hash = QCryptographicHash::hash(primaryMAC.toUtf8(), QCryptographicHash::Sha256);
    
    // 将哈希转换为可读字符串（取前16个字符）
    QString machineCode = hash.toHex().left(16).toUpper();
    
    // 格式化为 XXXX-XXXX-XXXX-XXXX
    machineCode = machineCode.mid(0, 4) + "-" + 
                 machineCode.mid(4, 4) + "-" + 
                 machineCode.mid(8, 4) + "-" + 
                 machineCode.mid(12, 4);
    
    return machineCode;
}

QStringList LicenseManager::getAllMACAddresses() const
{
    QStringList macAddresses;
    QList<QNetworkInterface> interfaces = QNetworkInterface::allInterfaces();
    
    for (const QNetworkInterface& interface : interfaces) {
        // 跳过回环接口
        if (interface.flags().testFlag(QNetworkInterface::IsLoopBack)) {
            continue;
        }
        
        // 获取MAC地址
        QString macAddress = interface.hardwareAddress();
        if (!macAddress.isEmpty() && macAddress != "00:00:00:00:00:00") {
            // 统一格式：大写，用冒号分隔
            macAddress = macAddress.toUpper();
            macAddresses.append(macAddress);
        }
    }
    
    // 去重
    macAddresses.removeDuplicates();
    
    return macAddresses;
}

QStringList LicenseManager::getHardcodedMACWhitelist() const
{
    // 硬编码的MAC地址白名单
    // 请根据实际需要修改这些MAC地址
    QStringList whitelist;
    
    // 示例MAC地址 - 请替换为实际的授权MAC地址
    whitelist << "9C:67:D6:46:D2:06";
    whitelist << "AA:BB:CC:DD:EE:FF";
    whitelist << "12:34:56:78:9A:BC";
    whitelist << "DE:AD:BE:EF:CA:FE";
    whitelist << "00:50:56:C0:00:01";
    whitelist << "50:2B:73:0C:03:95";
    whitelist << "34:1A:4C:05:5A:33";

    // 可以继续添加更多MAC地址
    // whitelist << "XX:XX:XX:XX:XX:XX";
    
    return whitelist;
}

bool LicenseManager::isMACInWhitelist(const QString& macAddress) const
{
    QString normalizedMAC = macAddress.toUpper().replace('-', ':');
    QStringList whitelist = getHardcodedMACWhitelist();
    
    return whitelist.contains(normalizedMAC);
}

QString LicenseManager::encrypt(const QString& text, const QString& key) const
{
    // 简单的XOR加密（保留用于其他用途）
    QByteArray data = text.toUtf8();
    QByteArray keyData = key.toUtf8();
    
    for (int i = 0; i < data.size(); ++i) {
        data[i] = data[i] ^ keyData[i % keyData.size()];
    }
    
    return data.toBase64();
}

QString LicenseManager::decrypt(const QString& text, const QString& key) const
{
    // 简单的XOR解密（保留用于其他用途）
    QByteArray data = QByteArray::fromBase64(text.toUtf8());
    QByteArray keyData = key.toUtf8();
    
    for (int i = 0; i < data.size(); ++i) {
        data[i] = data[i] ^ keyData[i % keyData.size()];
    }
    
    return QString::fromUtf8(data);
}

QDateTime LicenseManager::getExpirationDate() const
{
    // 如果已授权（MAC在白名单中），返回一个很远的未来日期表示永久授权
    if (isAuthorized()) {
        return QDateTime(QDate(9999, 12, 31), QTime(23, 59, 59));
    }
    
    // 如果未授权但在试用期内，返回试用期结束日期
    if (isTrialAvailable()) {
        QSettings settings;
        QDateTime firstRun = settings.value("License/FirstRun", QDateTime::currentDateTime()).toDateTime();
        return firstRun.addDays(TRIAL_DAYS);
    }
    
    // 试用期已过，返回当前时间（表示已过期）
    return QDateTime::currentDateTime();
}