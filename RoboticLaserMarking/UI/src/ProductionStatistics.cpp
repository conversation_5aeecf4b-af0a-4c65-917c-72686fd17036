#include "ProductionStatistics.h"
#include <QUuid>
#include <QDebug>

QJsonObject ProductionRecord::toJson() const {
    QJsonObject json;
    
    json["recordId"] = recordId;
    json["startTime"] = startTime.toString(Qt::ISODate);
    json["endTime"] = endTime.toString(Qt::ISODate);
    json["rfidCode"] = rfidCode;
    json["templateName"] = templateName;
    json["shoeCode"] = shoeCode;
    json["isLeftFoot"] = isLeftFoot;
    json["totalFaces"] = totalFaces;
    json["completedFaces"] = completedFaces;
    json["status"] = static_cast<int>(status);
    json["processingTimeMs"] = static_cast<qint64>(processingTimeMs);
    json["laserTimeMs"] = static_cast<qint64>(laserTimeMs);
    json["operatorId"] = operatorId;
    json["errorMessage"] = errorMessage;
    
    // Convert custom data to JSON
    QJsonObject customJson;
    for (auto it = customData.begin(); it != customData.end(); ++it) {
        customJson[it.key()] = QJsonValue::fromVariant(it.value());
    }
    json["customData"] = customJson;
    
    return json;
}

ProductionRecord ProductionRecord::fromJson(const QJsonObject& json) {
    ProductionRecord record;
    
    record.recordId = json["recordId"].toString();
    record.startTime = QDateTime::fromString(json["startTime"].toString(), Qt::ISODate);
    record.endTime = QDateTime::fromString(json["endTime"].toString(), Qt::ISODate);
    record.rfidCode = json["rfidCode"].toString();
    record.templateName = json["templateName"].toString();
    record.shoeCode = json["shoeCode"].toInt();
    record.isLeftFoot = json["isLeftFoot"].toBool();
    record.totalFaces = json["totalFaces"].toInt();
    record.completedFaces = json["completedFaces"].toInt();
    record.status = static_cast<ProductionStatus>(json["status"].toInt());
    record.processingTimeMs = json["processingTimeMs"].toVariant().toLongLong();
    record.laserTimeMs = json["laserTimeMs"].toVariant().toLongLong();
    record.operatorId = json["operatorId"].toString();
    record.errorMessage = json["errorMessage"].toString();
    
    // Convert custom data from JSON
    QJsonObject customJson = json["customData"].toObject();
    for (auto it = customJson.begin(); it != customJson.end(); ++it) {
        record.customData[it.key()] = it.value().toVariant();
    }
    
    return record;
}

// Helper functions for ProductionRecord
QString productionStatusToString(ProductionStatus status) {
    switch (status) {
        case ProductionStatus::SUCCESS:
            return "Success";
        case ProductionStatus::FAILED:
            return "Failed";
        case ProductionStatus::INTERRUPTED:
            return "Interrupted";
        case ProductionStatus::TIMEOUT:
            return "Timeout";
        default:
            return "Unknown";
    }
}

ProductionStatus stringToProductionStatus(const QString& statusStr) {
    if (statusStr == "Success") return ProductionStatus::SUCCESS;
    if (statusStr == "Failed") return ProductionStatus::FAILED;
    if (statusStr == "Interrupted") return ProductionStatus::INTERRUPTED;
    if (statusStr == "Timeout") return ProductionStatus::TIMEOUT;
    return ProductionStatus::SUCCESS;
}

QString statisticsPeriodToString(StatisticsPeriod period) {
    switch (period) {
        case StatisticsPeriod::HOUR:
            return "Hour";
        case StatisticsPeriod::DAY:
            return "Day";
        case StatisticsPeriod::WEEK:
            return "Week";
        case StatisticsPeriod::MONTH:
            return "Month";
        case StatisticsPeriod::YEAR:
            return "Year";
        case StatisticsPeriod::CUSTOM:
            return "Custom";
        default:
            return "Day";
    }
}

QString chartTypeToString(ChartType type) {
    switch (type) {
        case ChartType::PIE_CHART:
            return "Pie Chart";
        case ChartType::LINE_CHART:
            return "Line Chart";
        case ChartType::BAR_CHART:
            return "Bar Chart";
        case ChartType::AREA_CHART:
            return "Area Chart";
        case ChartType::SCATTER_CHART:
            return "Scatter Chart";
        default:
            return "Pie Chart";
    }
}

// Utility function to generate unique record ID
QString generateRecordId() {
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

// Utility function to calculate time difference in milliseconds
qint64 calculateTimeDifference(const QDateTime& start, const QDateTime& end) {
    if (!start.isValid() || !end.isValid()) {
        return 0;
    }
    return start.msecsTo(end);
}

// Utility function to format time duration
QString formatDuration(qint64 milliseconds) {
    if (milliseconds < 0) {
        return "0ms";
    }
    
    qint64 seconds = milliseconds / 1000;
    qint64 minutes = seconds / 60;
    qint64 hours = minutes / 60;
    
    if (hours > 0) {
        return QString("%1h %2m %3s")
            .arg(hours)
            .arg(minutes % 60)
            .arg(seconds % 60);
    } else if (minutes > 0) {
        return QString("%1m %2s")
            .arg(minutes)
            .arg(seconds % 60);
    } else if (seconds > 0) {
        return QString("%1s").arg(seconds);
    } else {
        return QString("%1ms").arg(milliseconds);
    }
}

// Utility function to get period start time
QDateTime getPeriodStartTime(const QDateTime& referenceTime, StatisticsPeriod period) {
    QDateTime startTime = referenceTime;
    
    switch (period) {
        case StatisticsPeriod::HOUR:
            startTime = QDateTime(referenceTime.date(), 
                                QTime(referenceTime.time().hour(), 0, 0));
            break;
        case StatisticsPeriod::DAY:
            startTime = QDateTime(referenceTime.date(), QTime(0, 0, 0));
            break;
        case StatisticsPeriod::WEEK:
            startTime = QDateTime(referenceTime.date().addDays(1 - referenceTime.date().dayOfWeek()), 
                                QTime(0, 0, 0));
            break;
        case StatisticsPeriod::MONTH:
            startTime = QDateTime(QDate(referenceTime.date().year(), 
                                      referenceTime.date().month(), 1), 
                                QTime(0, 0, 0));
            break;
        case StatisticsPeriod::YEAR:
            startTime = QDateTime(QDate(referenceTime.date().year(), 1, 1), 
                                QTime(0, 0, 0));
            break;
        default:
            break;
    }
    
    return startTime;
}

// Utility function to get period end time
QDateTime getPeriodEndTime(const QDateTime& referenceTime, StatisticsPeriod period) {
    QDateTime endTime = referenceTime;
    
    switch (period) {
        case StatisticsPeriod::HOUR:
            endTime = QDateTime(referenceTime.date(), 
                              QTime(referenceTime.time().hour(), 59, 59, 999));
            break;
        case StatisticsPeriod::DAY:
            endTime = QDateTime(referenceTime.date(), QTime(23, 59, 59, 999));
            break;
        case StatisticsPeriod::WEEK:
            endTime = QDateTime(referenceTime.date().addDays(7 - referenceTime.date().dayOfWeek()), 
                              QTime(23, 59, 59, 999));
            break;
        case StatisticsPeriod::MONTH:
            endTime = QDateTime(QDate(referenceTime.date().year(), 
                                    referenceTime.date().month(), 
                                    referenceTime.date().daysInMonth()), 
                              QTime(23, 59, 59, 999));
            break;
        case StatisticsPeriod::YEAR:
            endTime = QDateTime(QDate(referenceTime.date().year(), 12, 31), 
                              QTime(23, 59, 59, 999));
            break;
        default:
            break;
    }
    
    return endTime;
}

// Helper function to get period duration in seconds
qint64 getPeriodSeconds(StatisticsPeriod period)
{
    switch (period) {
        case StatisticsPeriod::HOUR:
            return 3600;
        case StatisticsPeriod::DAY:
            return 86400;
        case StatisticsPeriod::WEEK:
            return 604800;
        case StatisticsPeriod::MONTH:
            return 2592000; // 30 days
        case StatisticsPeriod::YEAR:
            return 31536000; // 365 days
        default:
            return 86400;
    }
}
