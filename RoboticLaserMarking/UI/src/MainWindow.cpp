#include "MainWindow.h"
#include "AppStyle.h"
#include "RegisterDialog.h"
#include "ProductionStatisticsManager.h"
#include "DetailedReportWidget.h"
#include "StatisticsExporter.h"
#include "UIUtils.h"
#include "DeviceControlUtils.h"
#include <QApplication>
#include <QScreen>
#include <QSettings>
#include <QStandardPaths>
#include <QDir>
#include <QMenu>
#include <QMenuBar>
#include <QToolBar>
#include <QStatusBar>
#include <QCloseEvent>
#include <QMessageBox>
#include <QFileDialog>
#include <QtCharts/QPieSeries>
#include <QtCharts/QPieSlice>
#include <QTextCodec>
#include <QProgressDialog>
#include <QtConcurrent/QtConcurrent>
#include <QFuture>
#include <QFutureWatcher>
#include <thread>
#include <chrono>


#pragma execution_character_set("utf-8")

QT_CHARTS_USE_NAMESPACE

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent),
      systemRunning(false),
      systemPaused(false),
      currentWorkflowState(WorkflowState::IDLE),
      totalProcessed(0),
      currentSessionProcessed(0),
      m_currentFace(101),
      m_totalFaces(1),
      m_laserNotWorkingCount(0),
      m_isLaserProcessing(false),
      abbErrorOccurred(false),
      savedWorkflowState(WorkflowState::IDLE),
      savedCurrentFace(101),
      savedRFIDCode("") {

    // 注册ABBMessage类型到Qt元对象系统
    qRegisterMetaType<RobotDriver::ABBMessage>("RobotDriver::ABBMessage");

    // 初始化成员变量
    laserControl = std::make_shared<LaserDriver::LaserControl>();
    abbDriver = std::make_shared<RobotDriver::ABBSocketDriver>();
    rfidDriver = std::make_shared<RFIDModbusDriver>();
    templateManager = std::make_shared<TemplateManager>(this);
    licenseManager = std::make_shared<LicenseManager>();
    QString logoPath = QApplication::applicationDirPath() + "/LOGO1.png";

    // 设置窗口标题和图标
    setWindowTitle("3515皮鞋激光打粗控制系统V2.0");
    setWindowIcon(QIcon(logoPath));
    _executor = std::make_shared<Fuxi::Common::Executor>(1);
    _executor1= std::make_shared<Fuxi::Common::Executor>(1);

    // 设置日志回调
    LogUtils::setLogCallback([this](const QString& message, const QString& category) {
        this->onLogMessage(message, category);
    });

    // 初始化UI
    setupUI();

    // 初始化统计管理器（在UI创建完成后）
    initializeStatisticsManager();

    // 初始化目录显示标签（在UI创建完成后）
    if (lblCurrentMarkDataDir && lblCurrentProcessParamDir && lblCurrentWorkDir) {
        QString markDataDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/MarkData";
        QString processParamDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) +
                                  "/ProcessParams";
        QString currentWorkDir = QDir::currentPath();

        lblCurrentMarkDataDir->setText("打粗数据目录: " + markDataDir);
        lblCurrentProcessParamDir->setText("工艺参数目录: " + processParamDir);
        lblCurrentWorkDir->setText("工作目录: " + currentWorkDir);
    }

    // 创建状态更新定时器
    statusUpdateTimer = new QTimer(this);
    connect(statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateDeviceStatus);
    statusUpdateTimer->start(1000); // 每秒更新一次

    // 创建统计更新定时器
    statisticsUpdateTimer = new QTimer(this);
    connect(statisticsUpdateTimer, &QTimer::timeout, this, &MainWindow::updateStatistics);
    statisticsUpdateTimer->start(5000); // 每5秒更新一次

    // 创建RFID定时器
    rfidTimer = new QTimer(this);
    connect(rfidTimer, &QTimer::timeout, this, &MainWindow::handleRFIDRequest);
    //初始化RFID相关变量
    lastRFIDCode = "";

    // 创建激光工作状态检查定时器
    laserWorkingCheckTimer = new QTimer(this);
    connect(laserWorkingCheckTimer, &QTimer::timeout, this, &MainWindow::checkLaserWorkingStatus);

    // 连接ABB消息回调
    abbDriver->SetMessageCallback([this](const RobotDriver::ABBMessage &message) {
        // 由于回调可能在不同线程中，使用Qt信号-槽机制处理
        QMetaObject::invokeMethod(this, "onABBMessageReceived",
                                  Qt::AutoConnection,
                                  Q_ARG(RobotDriver::ABBMessage, message));
    });

    // 设置激光控制回调
    laserControl->SetCallback([this](const char *msg) {
        QString message = QString::fromUtf8(msg);
        onLogMessage(message, "激光打粗");
    });

    // 初始化日志文件
    QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
    QDir().mkpath(logDir);

    QString logFileName = logDir + "/log_" +
                          QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss") + ".log";
    logFile = std::make_shared<QFile>(logFileName);
    if (logFile->open(QIODevice::WriteOnly | QIODevice::Text)) {
        // 初始化日志文件，不写入BOM
        QTextStream initStream(logFile.get());
        initStream.setCodec("UTF-8");
        initStream.setAutoDetectUnicode(true);
        initStream.setGenerateByteOrderMark(false);

        // 写入日志头
        initStream << "[系统启动时间: " << QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss") << "]\n";
        initStream.flush();

        logWidget->setAutoSave(true, logFileName);
    }

    // 加载模板
    QString templateCSVPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/templates.csv";
    if (QFile::exists(templateCSVPath)) {
        templateManager->loadFromCSV(templateCSVPath);
    }

    // 检查是否有模板，如果没有则创建默认模板
    if (templateManager->getTemplateCount() == 0) {
        templateManager->createDefaultTemplate();
        onLogMessage("已创建默认模板", "系统");
    }

    // 记录启动日志
    onLogMessage("系统启动成功", "系统");

    // 更新设备状态
    updateDeviceStatus();

    // 开始计时
    sessionStartTime = QDateTime::currentDateTime();

    // 确保应用样式表
    this->setStyleSheet(AppStyle::MainStyleSheet);
    this->style()->unpolish(this);
    this->style()->polish(this);
    this->update();

    // 应用样式到所有子控件
    QList<QWidget *> allWidgets = this->findChildren<QWidget *>();
    for (QWidget *widget: allWidgets) {
        widget->setStyle(QApplication::style());
        widget->update();
    }

    // 检查注册状态并更新状态栏
    updateLicenseStatus();

    // 使用延时确保UI完全初始化后再启动ABB服务器
    QTimer::singleShot(100, this, [this]() {
        // 自动启动ABB服务器

        int abbPort = 8080; // 默认端口，可从设置获取
        if (abbDriver->Initialize(abbPort)) {
            if (abbDriver->Start()) {
                onLogMessage("ABB机器人Socket服务器已自动启动，等待机器人连接", "系统");
                if (btnConnectABB && btnDisconnectABB) {
                    btnConnectABB->setEnabled(false);
                    btnDisconnectABB->setEnabled(true);
                }
            } else {
                onLogMessage("ABB机器人Socket服务器自动启动失败", "错误");
            }
        } else {
            onLogMessage("ABB机器人Socket服务器初始化失败", "错误");
        }
    });

    // 初始化其他设备
    QTimer::singleShot(500, this, [this]() {
        // 从配置文件读取自动连接设置
        SystemSettings settings = getSystemSettings();

        // 如果配置了自动连接，则直接连接设备
        if (settings.autoConnect) {
            connectDevicesAsync();
        } else {
            // 否则询问用户是否要连接设备
            if (QMessageBox::question(this, "设备初始化",
                                      "是否要自动连接设备？",
                                      QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
                connectDevicesAsync();
            }
        }
    });
}

// 异步连接设备
void MainWindow::connectDevicesAsync() {
    // 创建进度对话框
    QProgressDialog *progress = new QProgressDialog("正在连接设备...", "取消", 0, 2, this);
    progress->setWindowTitle("设备连接");
    progress->setWindowModality(Qt::WindowModal);
    progress->setMinimumDuration(0);
    progress->setValue(0);
    progress->show();

    // 创建 QFutureWatcher 监视异步操作
    QFutureWatcher<void> *watcher = new QFutureWatcher<void>();

    // 连接完成信号
    connect(watcher, &QFutureWatcher<void>::finished, this, [this, progress, watcher]() {
        // 完成连接
        progress->setValue(2);
        progress->close();
        progress->deleteLater();
        watcher->deleteLater();

        // 更新设备状态显示
        updateDeviceStatus();
    });

    // 启动异步任务
    QFuture<void> future = QtConcurrent::run([this, progress]() {
        // 连接激光打粗
        if (!laserControl->IsConnected()) {
            // 在主线程中更新UI
            QMetaObject::invokeMethod(progress, "setLabelText", Qt::QueuedConnection,
                                      Q_ARG(QString, "正在连接激光打粗..."));
            QMetaObject::invokeMethod(progress, "setValue", Qt::QueuedConnection,
                                      Q_ARG(int, 0));

            // 连接激光设备
            bool laserConnected = laserControl->Connect();

            // 在主线程中处理连接结果
            QMetaObject::invokeMethod(this, [this, laserConnected]() {
                if (laserConnected) {
                    onLogMessage("激光打粗连接成功", "系统");
                    btnConnectLaser->setEnabled(false);
                    btnDisconnectLaser->setEnabled(true);
                    deviceStatusWidget->setLaserConnected(true);

                    // 设置激光回调
                    laserControl->SetCallback([this](const char *msg) {
                        onLogMessage(QString::fromUtf8(msg), "激光打粗");
                    });
                } else {
                    onLogMessage("激光打粗连接失败", "错误");
                    QMessageBox::critical(this, "连接错误", "无法连接到激光打粗，请检查设备连接。");
                }
            }, Qt::QueuedConnection);
        }

        // 连接RFID设备
        if (!rfidDriver->isConnected()) {

            // 在主线程中更新UI
            QMetaObject::invokeMethod(progress, "setLabelText", Qt::QueuedConnection,
                                      Q_ARG(QString, "正在连接RFID设备..."));
            QMetaObject::invokeMethod(progress, "setValue", Qt::QueuedConnection,
                                      Q_ARG(int, 1));
        
            // 从设置获取IP和端口
            SystemSettings settings = getSystemSettings();
            QString ipAddress = settings.rfidIP.isEmpty() ? "*************" : settings.rfidIP;
            int port = settings.rfidPort > 0 ? settings.rfidPort : 502;
        
            // 连接RFID设备
            int rfidResult = rfidDriver->connect(ipAddress.toStdString(), port);

            // 在主线程中处理连接结果
            QMetaObject::invokeMethod(this, [this, rfidResult]() {
                if (rfidResult == 0) {
                    onLogMessage("RFID设备连接成功", "系统");
                    btnConnectRFID->setEnabled(false);
                    btnDisconnectRFID->setEnabled(true);
                    deviceStatusWidget->setRFIDConnected(true);
                    // 在主线程中启动RFID定时器
                    startRFIDTimer();
                } else {
                    onLogMessage("RFID设备连接失败", "错误");
                    QMessageBox::critical(this, "连接错误", "无法连接到RFID设备，请检查网络连接和设置。");
                }
            }, Qt::QueuedConnection);
        }
    });

    // 设置 watcher 监视 future
    watcher->setFuture(future);

}

MainWindow::~MainWindow() {
    // 停止线程和定时器
    if (statusUpdateTimer) {
        statusUpdateTimer->stop();
        delete statusUpdateTimer;
    }

    if (statisticsUpdateTimer) {
        statisticsUpdateTimer->stop();
        delete statisticsUpdateTimer;
    }

    if (rfidTimer) {
        rfidTimer->stop();
        delete rfidTimer;
    }

    if (laserWorkingCheckTimer) {
        laserWorkingCheckTimer->stop();
        delete laserWorkingCheckTimer;
    }

    if (errorRetryTimer) {
        errorRetryTimer->stop();
        delete errorRetryTimer;
    }

    // 断开设备连接
    if (abbDriver) {
        // 确保关闭ABB服务器
        if (abbDriver->IsRunning()) {
            onLogMessage("正在关闭ABB机器人Socket服务器...", "系统");
            abbDriver->Stop();
            onLogMessage("ABB机器人Socket服务器已关闭", "系统");
        }
        // 智能指针会自动释放内存
    }

    if (laserControl) {
        if (laserControl->IsConnected()) {
            laserControl->Disconnect();
        }
        // 智能指针会自动释放内存
    }

    if (rfidDriver) {
        if (rfidDriver->isConnected()) {
            rfidDriver->disconnect();
        }
        // 智能指针会自动释放内存
    }

    // 智能指针会自动释放许可证管理器内存

    // 关闭日志文件
    if (logFile) {
        if (logFile->isOpen()) {
            logFile->close();
        }
        // 智能指针会自动释放内存
    }

    // 记录日志
    onLogMessage("系统关闭", "系统");
}

void MainWindow::setupUI() {
    // 创建中央窗口部件
    centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    // 创建主布局
    mainLayout = new QGridLayout(centralWidget);

    // 创建菜单和工具栏
    createActions();
    createMenus();
    createStatusBar();

    // 创建LOGO面板
    createLogoPanel();

    // 创建设备面板
    createDevicePanel();

    // 创建导航栏和主区域
    createNavigation();
    createMainArea();

    // 创建系统控制面板
    createSystemControlPanel();

    // 设置布局
    mainLayout->addWidget(logoPanel, 0, 0); // LOGO面板在最上方
    mainLayout->addWidget(devicePanel, 1, 0); // 设备面板在LOGO下方
    mainLayout->addWidget(navigationList, 2, 0);
    mainLayout->addWidget(systemControlPanel, 3, 0);
    mainLayout->addWidget(mainAreaStack, 0, 1, 4, 1); // 主区域跨越所有行

    // 设置布局比例
    mainLayout->setColumnStretch(0, 1); // 左侧导航和控制区域
    mainLayout->setColumnStretch(1, 4); // 主内容区域

    // 设置垂直比例
    mainLayout->setRowStretch(0, 0); // LOGO面板 - 固定高度
    mainLayout->setRowStretch(1, 1); // 设备面板
    mainLayout->setRowStretch(2, 3); // 导航列表 - 给予更多空间
    mainLayout->setRowStretch(3, 1); // 系统控制面板

    // 设置左侧面板的固定宽度
    int leftPanelWidth = 600;
    logoPanel->setFixedWidth(leftPanelWidth);
    devicePanel->setFixedWidth(leftPanelWidth);
    navigationList->setFixedWidth(leftPanelWidth);
    systemControlPanel->setFixedWidth(leftPanelWidth);

    // 设置窗口大小
    resize(1200, 800);
}

void MainWindow::createLogoPanel() {
    logoPanel = new QWidget(this);
    logoPanel->setStyleSheet("background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;");
    logoPanel->setFixedHeight(100); // 设置固定高度

    QHBoxLayout *logoLayout = new QHBoxLayout(logoPanel);
    logoLayout->setContentsMargins(10, 10, 10, 10);

    // 创建LOGO标签
    QLabel *logoLabel = new QLabel(this);

    // 从本地目录加载LOGO
    QString logoPath = QApplication::applicationDirPath() + "/LOGO.png";
    QPixmap pixmap(logoPath);
    pixmap = pixmap.scaled(80, 80, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    logoLabel->setPixmap(pixmap);
    logoLabel->setAlignment(Qt::AlignCenter);
    logoLabel->setFixedSize(80, 80);

    // 创建系统名称标签
    QLabel *titleLabel = new QLabel("皮鞋激光打粗控制系统", this);
    titleLabel->setStyleSheet("font-size: 32px; font-weight: bold; color: #2980b9;");
    titleLabel->setAlignment(Qt::AlignVCenter | Qt::AlignHCenter);

    // 添加到布局
    logoLayout->addWidget(logoLabel);
    logoLayout->addWidget(titleLabel, 1);
}

void MainWindow::createActions() {
    // 文件菜单动作
    QAction *exitAction = new QAction("退出", this);
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);

    // 设置菜单动作
    QAction *settingsAction = new QAction("系统设置", this);
    settingsAction->setShortcut(QKeySequence("Ctrl+S"));
    connect(settingsAction, &QAction::triggered, this, &MainWindow::onSystemSettings);

    // 注册菜单动作
    QAction *registerAction = new QAction("软件注册/更新", this);
    connect(registerAction, &QAction::triggered, this, &MainWindow::onRegisterSoftware);

    QAction *licenseInfoAction = new QAction("许可证信息", this);
    connect(licenseInfoAction, &QAction::triggered, this, &MainWindow::onShowLicenseInfo);

    // 模板菜单动作
    QAction *addTemplateAction = new QAction("添加模板", this);
    connect(addTemplateAction, &QAction::triggered, this, &MainWindow::onAddTemplate);

    QAction *importTemplatesAction = new QAction("导入模板", this);
    connect(importTemplatesAction, &QAction::triggered, this, &MainWindow::onImportTemplates);

    QAction *exportTemplatesAction = new QAction("导出模板", this);
    connect(exportTemplatesAction, &QAction::triggered, this, &MainWindow::onExportTemplates);

    // 帮助菜单动作
    QAction *aboutAction = new QAction("关于", this);
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "关于激光打粗控制系统",
                           "<h3>皮鞋激光打粗控制系统</h3>"
                           "<p>版本: V2.0.0</p>"
                           "<p>皮鞋激光打粗控制系统</p>"
                           "<p>Copyright © 2025</p>");
    });

    // 将动作添加到成员变量，以便在createMenus()中使用
    this->addAction(exitAction);
    this->addAction(settingsAction);
    this->addAction(registerAction);
    this->addAction(licenseInfoAction);
    this->addAction(addTemplateAction);
    this->addAction(importTemplatesAction);
    this->addAction(exportTemplatesAction);
    this->addAction(aboutAction);
}

void MainWindow::createMenus() {
    // 创建菜单栏
    QMenuBar *menuBar = this->menuBar();

    // 文件菜单
    QMenu *fileMenu = menuBar->addMenu("文件");
    fileMenu->addAction(this->actions().at(0)); // exitAction

    // 设置菜单
    QMenu *settingsMenu = menuBar->addMenu("设置");
    settingsMenu->addAction(this->actions().at(1)); // settingsAction

    // 帮助菜单
    QMenu *helpMenu = menuBar->addMenu("帮助");
    helpMenu->addAction(this->actions().at(7)); // aboutAction
}

void MainWindow::createStatusBar() {
    statusBar = new QStatusBar(this);
    setStatusBar(statusBar);

    // 添加注册状态标签
    lblLicenseStatus = new QLabel(this);
    lblLicenseStatus->setCursor(Qt::PointingHandCursor); // 鼠标悬停时显示手形光标
    connect(lblLicenseStatus, &QLabel::linkActivated, [this](const QString &) {
        onShowLicenseInfo();
    });

    // 点击标签显示注册信息
    lblLicenseStatus->installEventFilter(this);

    // 添加到状态栏
    statusBar->addPermanentWidget(lblLicenseStatus);

    statusBar->showMessage("就绪", 3000);
}

bool MainWindow::eventFilter(QObject *obj, QEvent *event) {
    if (obj == lblLicenseStatus && event->type() == QEvent::MouseButtonPress) {
        onShowLicenseInfo();
        return true;
    }
    return QMainWindow::eventFilter(obj, event);
}

void MainWindow::createDevicePanel() {
    devicePanel = new QGroupBox("设备控制", this);
    QGridLayout *layout = new QGridLayout(devicePanel);

    // 添加初始化按钮
    QPushButton *btnInitializeAll = new QPushButton("手动连接所有设备", this);
    btnInitializeAll->setMinimumHeight(60);
    layout->addWidget(btnInitializeAll, 0, 0, 1, 4);

    // ABB机器人控制
    btnConnectABB = new QPushButton("启动ABB服务器", this);
    btnConnectABB->setProperty("cssClass", "functionButton");
    btnDisconnectABB = new QPushButton("关闭ABB服务器", this);
    btnDisconnectABB->setProperty("cssClass", "functionButton");


    // 激光打粗控制
    btnConnectLaser = new QPushButton("连接激光", this);
    btnConnectLaser->setProperty("cssClass", "functionButton");
    btnDisconnectLaser = new QPushButton("断开激光", this);
    btnDisconnectLaser->setProperty("cssClass", "functionButton");


    // RFID控制
    btnConnectRFID = new QPushButton("连接RFID", this);
    btnConnectRFID->setProperty("cssClass", "functionButton");
    btnDisconnectRFID = new QPushButton("断开RFID", this);
    btnDisconnectRFID->setProperty("cssClass", "functionButton");


    // 添加到布局
    QLabel *abbLabel = new QLabel("ABB机器人:", this);
    abbLabel->setStyleSheet("font-size: 16px");
    layout->addWidget(abbLabel, 1, 0);
    layout->addWidget(btnConnectABB, 1, 1);
    layout->addWidget(btnDisconnectABB, 1, 2);

    QLabel *laserLabel = new QLabel("激光器:", this);
    laserLabel->setStyleSheet("font-size: 16px");
    layout->addWidget(laserLabel, 2, 0);
    layout->addWidget(btnConnectLaser, 2, 1);
    layout->addWidget(btnDisconnectLaser, 2, 2);

    QLabel *rfidLabel = new QLabel("RFID设备:", this);
    rfidLabel->setStyleSheet("font-size: 16px");
    layout->addWidget(rfidLabel, 3, 0);
    layout->addWidget(btnConnectRFID, 3, 1);
    layout->addWidget(btnDisconnectRFID, 3, 2);


    // 连接信号与槽
    connect(btnInitializeAll, &QPushButton::clicked, this, &MainWindow::onInitializeAllDevices);
    connect(btnConnectABB, &QPushButton::clicked, this, &MainWindow::onConnectABB);
    connect(btnDisconnectABB, &QPushButton::clicked, this, &MainWindow::onDisconnectABB);
    connect(btnConnectLaser, &QPushButton::clicked, this, &MainWindow::onConnectLaser);
    connect(btnDisconnectLaser, &QPushButton::clicked, this, &MainWindow::onDisconnectLaser);
    connect(btnConnectRFID, &QPushButton::clicked, this, &MainWindow::onConnectRFID);
    connect(btnDisconnectRFID, &QPushButton::clicked, this, &MainWindow::onDisconnectRFID);

    // 初始状态设置
    btnDisconnectABB->setEnabled(false);
    btnDisconnectLaser->setEnabled(false);
    btnDisconnectRFID->setEnabled(false);
}

void MainWindow::createNavigation() {
    navigationList = new QListWidget(this);

    // 添加导航项
    QListWidgetItem *itemDeviceStatus = new QListWidgetItem("设备状态");
    QListWidgetItem *itemTemplateManage = new QListWidgetItem("模板管理");
    QListWidgetItem *itemStatistics = new QListWidgetItem("统计信息");
    QListWidgetItem *itemLaserControl = new QListWidgetItem("手动激光控制");
    QListWidgetItem *itemSystemLog = new QListWidgetItem("系统日志");

    // 设置项目高度
    int itemHeight = 60;
    itemDeviceStatus->setSizeHint(QSize(0, itemHeight));
    itemTemplateManage->setSizeHint(QSize(0, itemHeight));
    itemStatistics->setSizeHint(QSize(0, itemHeight));
    itemLaserControl->setSizeHint(QSize(0, itemHeight));
    itemSystemLog->setSizeHint(QSize(0, itemHeight));

    // 添加到列表
    navigationList->addItem(itemDeviceStatus);
    navigationList->addItem(itemTemplateManage);
    navigationList->addItem(itemStatistics);
    navigationList->addItem(itemLaserControl);
    navigationList->addItem(itemSystemLog);

    // 设置默认选中项
    navigationList->setCurrentRow(0);

    // 设置样式
    navigationList->setStyleSheet(
        "QListWidget { "
        "   background-color: #f5f7fa; "
        "   border: 1px solid #e1e4e8; "
        "   font-size: 16px; "
        "   font-weight: bold; "
        "}"
        "QListWidget::item { "
        "   padding-left: 15px; "
        "   border-bottom: 1px solid #ecf0f1; "
        "}"
        "QListWidget::item:selected { "
        "   background-color: #3498db; "
        "   color: white; "
        "}"
        "QListWidget::item:hover:!selected { "
        "   background-color: #ecf0f1; "
        "}"
    );

    // 连接信号与槽
    connect(navigationList, &QListWidget::currentRowChanged, this, &MainWindow::onNavItemChanged);
}

void MainWindow::createMainArea() {
    mainAreaStack = new QStackedWidget(this);

    // 创建设备状态页面
    deviceStatusWidget = new DeviceStatusWidget(this);
    mainAreaStack->addWidget(deviceStatusWidget);

    // 创建模板管理页面
    QWidget *templatePage = new QWidget(this);
    QVBoxLayout *templateLayout = new QVBoxLayout(templatePage);

    // 模板表格
    templateModel = new QStandardItemModel(this);
    templateTableView = new QTableView(this);
    templateTableView->setModel(templateModel);
    templateTableView->setSelectionBehavior(QAbstractItemView::SelectRows);
    templateTableView->setSelectionMode(QAbstractItemView::SingleSelection);
    templateTableView->setEditTriggers(QAbstractItemView::NoEditTriggers);
    templateTableView->horizontalHeader()->setStretchLastSection(true);
    templateTableView->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);

    // 模板操作按钮
    QHBoxLayout *templateBtnLayout = new QHBoxLayout();
    btnAddTemplate = new QPushButton("添加模板", templatePage);
    btnEditTemplate = new QPushButton("编辑模板", templatePage);
    btnDeleteTemplate = new QPushButton("删除模板", templatePage);
    btnImportTemplates = new QPushButton("导入模板", templatePage);
    btnExportTemplates = new QPushButton("导出模板", templatePage);
    QPushButton *btnSaveTemplates = new QPushButton("保存模板", templatePage);
    btnOpenShoePathEditor = new QPushButton("鞋子路径编辑", templatePage);

    // 设置模板管理按钮统一样式
    QString templateButtonStyle =
            "QPushButton { font-size: 14px; font-weight: bold; padding: 8px; min-height: 25px; min-width: 100px; }";
    btnAddTemplate->setStyleSheet(templateButtonStyle + "QPushButton { background-color: #9b59b6; color: white; }");
    btnEditTemplate->setStyleSheet(templateButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnDeleteTemplate->setStyleSheet(templateButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnImportTemplates->setStyleSheet(templateButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnExportTemplates->setStyleSheet(templateButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnSaveTemplates->setStyleSheet(templateButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnOpenShoePathEditor->setStyleSheet(
        templateButtonStyle + "QPushButton { background-color: #9b59b6; color: white; }");

    // 为按钮添加cssClass属性
    btnAddTemplate->setProperty("cssClass", "functionButton");
    btnEditTemplate->setProperty("cssClass", "functionButton");
    btnDeleteTemplate->setProperty("cssClass", "functionButton");
    btnImportTemplates->setProperty("cssClass", "functionButton");
    btnExportTemplates->setProperty("cssClass", "functionButton");
    btnSaveTemplates->setProperty("cssClass", "functionButton");
    btnOpenShoePathEditor->setProperty("cssClass", "functionButton");

    templateBtnLayout->addWidget(btnAddTemplate);
    templateBtnLayout->addWidget(btnOpenShoePathEditor);
    templateBtnLayout->addWidget(btnEditTemplate);
    templateBtnLayout->addWidget(btnDeleteTemplate);
    templateBtnLayout->addWidget(btnSaveTemplates);
    templateBtnLayout->addWidget(btnImportTemplates);
    templateBtnLayout->addWidget(btnExportTemplates);


    templateBtnLayout->addStretch();

    templateLayout->addLayout(templateBtnLayout);
    templateLayout->addWidget(templateTableView);

    mainAreaStack->addWidget(templatePage);

    // 创建统计信息页面
    QWidget *statsPage = new QWidget(this);
    QVBoxLayout *statsLayout = new QVBoxLayout(statsPage);

    // 创建水平分割器，左侧是图表，右侧是详细报表
    QSplitter* statsMainSplitter = new QSplitter(Qt::Horizontal, statsPage);
    statsMainSplitter->setChildrenCollapsible(false); // 防止子组件被完全折叠

    // 创建原有的简单统计图表
    statisticsChart = new QChart();
    statisticsChart->setTitle("打粗统计");
    statisticsChart->setAnimationOptions(QChart::SeriesAnimations);

    statisticsChartView = new QChartView(statisticsChart);
    statisticsChartView->setRenderHint(QPainter::Antialiasing);
    statisticsChartView->setMinimumWidth(300);
    statisticsChartView->setMaximumWidth(500);

    // 创建详细报表组件（延迟初始化，等统计管理器准备好）
    detailedReportWidget = nullptr;

    // 添加到分割器
    statsMainSplitter->addWidget(statisticsChartView);

    // 创建占位符，稍后替换为详细报表组件
    QLabel* placeholderLabel = new QLabel("详细报表将在统计管理器初始化后显示", statsPage);
    placeholderLabel->setAlignment(Qt::AlignCenter);
    placeholderLabel->setFont(QFont("Microsoft YaHei", 14));
    placeholderLabel->setStyleSheet("QLabel { color: #666; background-color: #f5f5f5; border: 1px dashed #ccc; }");
    placeholderLabel->setMinimumWidth(400);
    statsMainSplitter->addWidget(placeholderLabel);

    // 设置分割器比例 (图表:详细报表 = 1:2)
    statsMainSplitter->setStretchFactor(0, 1);
    statsMainSplitter->setStretchFactor(1, 2);
    // 不在这里设置具体大小，让它自然布局

    // 添加到布局
    statsLayout->addWidget(statsMainSplitter, 1);

    mainAreaStack->addWidget(statsPage);

    // 创建激光控制页面
    QWidget *laserPage = new QWidget(this);
    QVBoxLayout *laserLayout = new QVBoxLayout(laserPage);

    laserControlPanel = new QGroupBox("手动激光控制", laserPage);
    QGridLayout *laserControlLayout = new QGridLayout(laserControlPanel);

    btnLaserOn = new QPushButton("激光开启", laserControlPanel);
    btnLaserOff = new QPushButton("激光关闭", laserControlPanel);
    btnRedGuide = new QCheckBox("红光指示", laserControlPanel);
    btnLoadMarkData = new QPushButton("加载打粗数据", laserControlPanel);
    btnLoadProcessParam = new QPushButton("加载工艺参数", laserControlPanel);
    btnSetWorkDir = new QPushButton("设置工作目录", laserControlPanel);
    btnSingleMark = new QPushButton("单次打粗", laserControlPanel);
    btnClearLaserData = new QPushButton("清除激光数据", laserControlPanel);
    btnResetLaserStatus = new QPushButton("复位激光状态", laserControlPanel);

    // 设置激光控制面板按钮统一样式
    QString laserButtonStyle =
            "QPushButton { font-size: 14px; font-weight: bold; padding: 8px; min-height: 25px; min-width: 80px; }";

    // 激光开启按钮使用绿色
    btnLaserOn->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #27ae60; color: white; }");
    btnLaserOn->setProperty("cssClass", "functionButton");

    // 激光关闭按钮使用红色
    btnLaserOff->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #e74c3c; color: white; }");
    btnLaserOff->setProperty("cssClass", "functionButton");

    // 红光指示使用橙色
    btnRedGuide->setStyleSheet(
        "QCheckBox::indicator {"
        "    width: 16px;"
        "    height: 16px;"
        "    border: 2px solid #999;"
        "    border-radius: 3px;"
        "    background-color: white;"
        "}"
        "QCheckBox::indicator:checked {"
        "    background-color: #4CAF50;"
        "    border: 2px solid #4CAF50;"
        "    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIgNkw0LjUgOC41TDEwIDMiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMS41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);"
        "}"
        "QCheckBox::indicator:hover {"
        "    border: 2px solid #666;"
        "}"
        "QCheckBox::indicator:checked:hover {"
        "    background-color: #45a049;"
        "    border: 2px solid #45a049;"
        "}"
    );

    btnRedGuide->setChecked(false);

    // 文件操作相关按钮使用蓝色
    btnLoadMarkData->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnLoadMarkData->setProperty("cssClass", "functionButton");

    btnLoadProcessParam->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnLoadProcessParam->setProperty("cssClass", "functionButton");

    btnSetWorkDir->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnSetWorkDir->setProperty("cssClass", "functionButton");

    // 打粗操作按钮使用紫色
    btnSingleMark->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #3498db; color: white; }");
    btnSingleMark->setProperty("cssClass", "functionButton");

    // 清除和重置按钮使用灰色
    btnClearLaserData->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #808080; color: white; }");
    btnClearLaserData->setProperty("cssClass", "functionButton");

    btnResetLaserStatus->setStyleSheet(laserButtonStyle + "QPushButton { background-color: #808080; color: white; }");
    btnResetLaserStatus->setProperty("cssClass", "functionButton");

    // 创建显示当前文件夹路径的标签
    lblCurrentMarkDataDir = new QLabel("打粗数据目录: 未设置", laserControlPanel);
    lblCurrentProcessParamDir = new QLabel("工艺参数目录: 未设置", laserControlPanel);
    lblCurrentWorkDir = new QLabel("工作目录: 未设置", laserControlPanel);

    // 设置标签样式
    QString labelStyle = "QLabel { color: #666; font-size: 12px; padding: 2px; }";
    lblCurrentMarkDataDir->setStyleSheet(labelStyle);
    lblCurrentProcessParamDir->setStyleSheet(labelStyle);
    lblCurrentWorkDir->setStyleSheet(labelStyle);

    // 两列布局，操作按钮在右侧上方
    laserControlLayout->addWidget(btnLaserOn, 0, 0);
    laserControlLayout->addWidget(btnLaserOff, 0, 1);

    laserControlLayout->addWidget(btnRedGuide, 1, 0);

    laserControlLayout->addWidget(btnSingleMark, 1, 1);


    laserControlLayout->addWidget(lblCurrentMarkDataDir, 2, 0);
    laserControlLayout->addWidget(btnLoadMarkData, 2, 1);

    laserControlLayout->addWidget(lblCurrentProcessParamDir, 3, 0);
    laserControlLayout->addWidget(btnLoadProcessParam, 3, 1);

    laserControlLayout->addWidget(lblCurrentWorkDir, 4, 0);
    laserControlLayout->addWidget(btnSetWorkDir, 4, 1);

    laserControlLayout->addWidget(btnResetLaserStatus, 5, 1);

    laserControlLayout->addWidget(btnClearLaserData, 5, 0);
    // 隐藏指定的按钮
    btnLaserOn->hide();
    btnLaserOff->hide();
    btnLoadMarkData->hide();
    btnLoadProcessParam->hide();
    lblCurrentMarkDataDir->hide();
    lblCurrentProcessParamDir->hide();

    laserLayout->addWidget(laserControlPanel);
    laserLayout->addStretch();

    mainAreaStack->addWidget(laserPage);

    // 创建日志页面
    logWidget = new LogWidget(this);
    mainAreaStack->addWidget(logWidget);

    // 连接按钮信号
    connect(btnAddTemplate, &QPushButton::clicked, this, &MainWindow::onAddTemplate);
    connect(btnEditTemplate, &QPushButton::clicked, this, &MainWindow::onEditTemplate);
    connect(btnDeleteTemplate, &QPushButton::clicked, this, &MainWindow::onDeleteTemplate);
    connect(btnImportTemplates, &QPushButton::clicked, this, &MainWindow::onImportTemplates);
    connect(btnExportTemplates, &QPushButton::clicked, this, &MainWindow::onExportTemplates);
    connect(btnSaveTemplates, &QPushButton::clicked, this, &MainWindow::saveTemplates);
    connect(btnOpenShoePathEditor, &QPushButton::clicked, this, &MainWindow::onOpenShoePathEditor);

    connect(btnLaserOn, &QPushButton::clicked, this, &MainWindow::onLaserOn);
    connect(btnLaserOff, &QPushButton::clicked, this, &MainWindow::onLaserOff);
    connect(btnRedGuide, &QCheckBox::toggled, [this](bool checked) {
        onRedGuideToggle(checked);
    });
    connect(btnLoadMarkData, &QPushButton::clicked, this, &MainWindow::onLoadMarkData);
    connect(btnLoadProcessParam, &QPushButton::clicked, this, &MainWindow::onLoadProcessParam);
    connect(btnSetWorkDir, &QPushButton::clicked, this, &MainWindow::onSetWorkDir);
    connect(btnSingleMark, &QPushButton::clicked, this, &MainWindow::onSingleMark);
    connect(btnClearLaserData, &QPushButton::clicked, this, &MainWindow::onClearLaserData);
    connect(btnResetLaserStatus, &QPushButton::clicked, this, &MainWindow::onResetLaserStatus);

    // 加载模板数据
    templateManager->updateTemplateModel(templateModel);
}

void MainWindow::createSystemControlPanel() {
    systemControlPanel = new QGroupBox("系统控制", this);
    QHBoxLayout *layout = new QHBoxLayout(systemControlPanel);

    btnStart = new QPushButton("启动", this);
    btnStop = new QPushButton("停止", this);
    btnPause = new QPushButton("暂停", this);
    btnReset = new QPushButton("重置", this);
    btnExit = new QPushButton("退出", this);

    // 设置按钮ID
    btnStart->setObjectName("btnStart");
    btnStop->setObjectName("btnStop");
    btnPause->setObjectName("btnPause");
    btnReset->setObjectName("btnReset");
    btnExit->setObjectName("btnExit");


    btnStart->setStyleSheet(btnStart->styleSheet());
    btnStop->setStyleSheet(btnStop->styleSheet());
    btnPause->setStyleSheet(btnPause->styleSheet());
    btnReset->setStyleSheet(btnReset->styleSheet());
    btnExit->setStyleSheet(btnExit->styleSheet());

    // 设置反馈效果
    QApplication::processEvents();
    btnStart->setAutoFillBackground(true);
    btnStop->setAutoFillBackground(true);
    btnPause->setAutoFillBackground(true);
    btnReset->setAutoFillBackground(true);
    btnExit->setAutoFillBackground(true);

    layout->addWidget(btnStart);
    layout->addWidget(btnStop);
    layout->addWidget(btnPause);
    layout->addWidget(btnReset);
    layout->addWidget(btnExit);

    // 连接信号与槽
    connect(btnStart, &QPushButton::clicked, this, &MainWindow::onStartSystem);
    connect(btnStop, &QPushButton::clicked, this, &MainWindow::onStopSystem);
    connect(btnPause, &QPushButton::clicked, this, &MainWindow::onPauseSystem);
    connect(btnReset, &QPushButton::clicked, this, &MainWindow::onResetSystem);
    connect(btnExit, &QPushButton::clicked, this, &MainWindow::onExitProgram);

    // 初始状态设置
    btnStop->setEnabled(false);
    btnPause->setEnabled(false);
    btnReset->setEnabled(true);

    btnPause->hide();
}

// 设备连接控制实现
void MainWindow::onConnectABB() {
    // 设置端口号
    int port = 8080; // 默认端口，可从设置获取

    // 初始化并启动Socket服务器
    if (!abbDriver->Initialize(port)) {
        onLogMessage("ABB机器人Socket服务器初始化失败", "错误");
        QMessageBox::critical(this, "服务器错误", "无法初始化ABB机器人Socket服务器，请检查端口是否被占用。");
        return;
    }

    // 启动服务器
    if (abbDriver->Start()) {
        onLogMessage("ABB机器人Socket服务器启动成功，等待机器人连接", "系统");
        btnConnectABB->setEnabled(false);
        btnDisconnectABB->setEnabled(true);


        // 不立即设置为已连接，等待机器人实际连接
        // 连接状态会通过更新定时器检测并更新UI
    } else {
        onLogMessage("ABB机器人Socket服务器启动失败", "错误");
        QMessageBox::critical(this, "服务器错误", "无法启动ABB机器人Socket服务器，请检查网络设置。");
    }
}

void MainWindow::onDisconnectABB() {
    if (abbDriver->IsRunning()) {
        abbDriver->Stop();
        onLogMessage("ABB机器人Socket服务器已关闭", "系统");
    }

    btnConnectABB->setEnabled(true);
    btnDisconnectABB->setEnabled(false);

    deviceStatusWidget->setABBConnected(false);
}

void MainWindow::onConnectLaser() {
    if (laserControl->Connect()) {
        onLogMessage("激光打粗连接成功", "系统");
        btnConnectLaser->setEnabled(false);
        btnDisconnectLaser->setEnabled(true);

        deviceStatusWidget->setLaserConnected(true);
        // 初始化激光工作状态为空闲
        deviceStatusWidget->setLaserWorkingStatus(false);

        // 设置激光回调
        laserControl->SetCallback([this](const char *msg) {
            onLogMessage(QString::fromUtf8(msg), "激光打粗");
        });
    } else {
        onLogMessage("激光打粗连接失败", "错误");
        QMessageBox::critical(this, "连接错误", "无法连接到激光打粗，请检查设备连接。");
    }
}

void MainWindow::onDisconnectLaser() {
    if (laserControl->IsConnected()) {
        // 关闭激光
        if (laserControl->IsWorking()) {
            laserControl->LaserOff();
            laserControl->EndMark();
        }

        // 断开连接
        laserControl->Disconnect();
        onLogMessage("激光打粗已断开连接", "系统");
    }

    btnConnectLaser->setEnabled(true);
    btnDisconnectLaser->setEnabled(false);
    // 更新激光工作状态为未连接
    deviceStatusWidget->setLaserWorkingStatus(false);

    // 激光工作状态指示器和红光指示器已移除
}

void MainWindow::onConnectRFID() {
    // 从设置获取IP和端口
    SystemSettings settings = getSystemSettings();
    QString ipAddress = settings.rfidIP.isEmpty() ? "*************" : settings.rfidIP; // 从配置读取IP
    int port = settings.rfidPort > 0 ? settings.rfidPort : 502; // 从配置读取端口

    if (rfidDriver->connect(ipAddress.toStdString(), port) == 0) {
        startRFIDTimer();
        onLogMessage("RFID设备连接成功", "系统");
        btnConnectRFID->setEnabled(false);
        btnDisconnectRFID->setEnabled(true);

        deviceStatusWidget->setRFIDConnected(true);
    } else {
        onLogMessage("RFID设备连接失败", "错误");
        QMessageBox::critical(this, "连接错误", "无法连接到RFID设备，请检查网络连接和设置。");
    }
}

void MainWindow::onDisconnectRFID() {
    if (rfidDriver->isConnected()) {
        rfidDriver->disconnect();
        onLogMessage("RFID设备已断开连接", "系统");
    }

    btnConnectRFID->setEnabled(true);
    btnDisconnectRFID->setEnabled(false);

    deviceStatusWidget->setRFIDConnected(false);
}

// 激光控制实现
void MainWindow::onLaserOn() {
    // 使用DeviceControlUtils进行设备连接检查
    if (!DeviceControlUtils::checkDeviceConnection(laserControl.get(), "激光打粗", this)) {
        return;
    }

    // 使用DeviceControlUtils处理激光操作
    auto result = DeviceControlUtils::handleLaserOperation(laserControl.get(),
        [this]() { return laserControl->LaserOn(); }, "激光开启");

    if (result.success) {
        LogUtils::logInfo(result.message, "激光打粗");
        deviceStatusWidget->setLaserWorkingStatus(true, "激光开启");
    } else {
        LogUtils::logError(result.message + " - " + result.errorDetails, "激光打粗");
        UIUtils::showErrorMessage(this, "激光控制错误", result.errorDetails);
    }
}

void MainWindow::onLaserOff() {
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "激光打粗未连接，请先连接设备。");
        return;
    }

    if (laserControl->LaserOff()) {
        onLogMessage("激光已关闭", "激光打粗");
        // 更新激光工作状态
        deviceStatusWidget->setLaserWorkingStatus(false);
    } else {
        onLogMessage("激光关闭失败", "错误");
        // 获取错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError) {
            QString errorText = QString::fromStdString(errorMsg.c_str());
            onLogMessage(errorText, "错误");
            QMessageBox::critical(this, "激光控制错误", errorText);
        }
    }
}

void MainWindow::onRedGuideToggle(bool enable) {
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "激光打粗未连接，请先连接设备。");
        btnRedGuide->setChecked(false);
        return;
    }
}

void MainWindow::onLoadMarkData() {
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "激光打粗未连接，请先连接设备。");
        return;
    }

    // 获取工作目录
    QString markDataDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/MarkData";
    QDir().mkpath(markDataDir); // 确保目录存在

    QString filePath = QFileDialog::getOpenFileName(this, "选择打粗数据文件",
                                                    markDataDir,
                                                    "打粗数据文件 (*.txt *.dxf *.dwg);;所有文件 (*.*)");

    if (filePath.isEmpty()) {
        return;
    }

    if (laserControl->LoadMarkData(filePath.toStdString())) {
        onLogMessage("打粗数据已加载: " + filePath, "激光打粗");
        statusBar->showMessage("打粗数据已加载", 3000);

        // 更新打粗数据目录显示
        QFileInfo fileInfo(filePath);
        lblCurrentMarkDataDir->setText("打粗数据目录: " + fileInfo.absolutePath());
    } else {
        onLogMessage("打粗数据加载失败: " + filePath, "错误");
        // 获取错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError) {
            QString errorText = QString::fromStdString(errorMsg.c_str());
            onLogMessage(errorText, "错误");
            QMessageBox::critical(this, "加载错误", errorText);
        }
    }
}

void MainWindow::onLoadProcessParam() {
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "激光打粗未连接，请先连接设备。");
        return;
    }

    // 获取工艺参数目录
    QString processParamDir = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/ProcessParams";
    QDir().mkpath(processParamDir); // 确保目录存在

    QString filePath = QFileDialog::getOpenFileName(this, "选择工艺参数文件",
                                                    processParamDir,
                                                    "工艺参数文件 (*.ini *.cfg *.xml);;所有文件 (*.*)");

    if (filePath.isEmpty()) {
        return;
    }

    if (laserControl->LoadProcessParam(filePath.toStdString())) {
        onLogMessage("工艺参数已加载: " + filePath, "激光打粗");
        statusBar->showMessage("工艺参数已加载", 3000);

        // 更新工艺参数目录显示
        QFileInfo fileInfo(filePath);
        lblCurrentProcessParamDir->setText("工艺参数目录: " + fileInfo.absolutePath());
    } else {
        onLogMessage("工艺参数加载失败: " + filePath, "错误");
        // 获取错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError) {
            QString errorText = QString::fromStdString(errorMsg.c_str());
            onLogMessage(errorText, "错误");
            QMessageBox::critical(this, "加载错误", errorText);
        }
    }
}

void MainWindow::onSetWorkDir() {
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "激光打粗未连接，请先连接设备。");
        return;
    }

    QString dir = QFileDialog::getExistingDirectory(this, "选择工作目录",
                                                    QDir::currentPath(),
                                                    QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);

    if (dir.isEmpty()) {
        return;
    }

    if (laserControl->SetWorkDir(dir.toStdString())) {
        onLogMessage("工作目录已设置: " + dir, "激光打粗");
        statusBar->showMessage("工作目录已设置", 3000);

        // 更新工作目录显示
        lblCurrentWorkDir->setText("工作目录: " + dir);
    } else {
        onLogMessage("工作目录设置失败: " + dir, "错误");
        // 获取错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError) {
            QString errorText = QString::fromStdString(errorMsg.c_str());
            onLogMessage(errorText, "错误");
            QMessageBox::critical(this, "设置错误", errorText);
        }
    }
}

void MainWindow::onSingleMark() {
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "激光控制", "激光打粗未连接，请先连接设备。");
        return;
    }


    // 确认对话框
    int ret = QMessageBox::question(this, "单次打粗确认",
                                    "确定要执行单次打粗操作吗？\n\n注意：这是调试功能，请确保激光头位置安全。",
                                    QMessageBox::Yes | QMessageBox::No,
                                    QMessageBox::No);

    if (ret != QMessageBox::Yes) {
        return;
    }

    onLogMessage("开始单次打粗调试", "激光打粗");
    bool isPreviewMode = btnRedGuide->isChecked();
    // 开始单次打粗
    if (laserControl->BeginMark(isPreviewMode)) {
        // false表示非预览模式
        onLogMessage("单次打粗开始", "激光打粗");
        // 启动打粗完成检查（异步版本）
        QTimer *checkTimer = new QTimer(this);
        connect(checkTimer, &QTimer::timeout, this, [this, checkTimer]() {
            // 检查打粗是否完成
            if (!laserControl->IsWorking()) {
                onLogMessage("单次打粗完成", "激光打粗");
                // 更新激光工作状态为空闲
                deviceStatusWidget->setLaserWorkingStatus(false);

                // 更新统计（调试打粗也计入统计）
                currentSessionProcessed = 1;
                totalProcessed++;
                updateStatistics();

                QMessageBox::information(this, "单次打粗", "单次打粗调试完成！");

                // 停止并删除定时器
                checkTimer->stop();
                checkTimer->deleteLater();
            }
        });
        // 每100毫秒检查一次
        checkTimer->start(100);
    } else {
        onLogMessage("单次打粗启动失败", "错误");

        // 获取错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError) {
            QString errorText = QString::fromStdString(errorMsg.c_str());
            onLogMessage(errorText, "错误");
            QMessageBox::critical(this, "打粗错误", errorText);
        } else {
            QMessageBox::critical(this, "打粗错误", "单次打粗启动失败，请检查设备状态和打粗数据。");
        }
    }
}

// 系统控制实现
void MainWindow::onStartSystem() {
    // 检查注册状态
    if (!checkLicense()) {
        QMessageBox::warning(this, "功能受限", "软件未注册，系统启动功能不可用。");
        return;
    }

    // 检查设备连接状态
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "系统控制", "激光打粗未连接，请先连接设备。");
        return;
    }

    if (!abbDriver->IsRunning()) {
        QMessageBox::warning(this, "系统控制", "ABB机器人服务器未启动，请先启动服务器。");
        return;
    }

    if (!abbDriver->IsConnected()) {
        QMessageBox::warning(this, "系统控制", "ABB机器人未连接到服务器，请确保机器人已连接。");
        return;
    }

    if (!rfidDriver->isConnected()) {
        QMessageBox::warning(this, "系统控制", "RFID设备未连接，请先连接设备。");
        return;
    }

    // 开始系统运行
    systemRunning = true;
    systemPaused = false;

    // 初始化工作流程状态为等待取料完成
    currentWorkflowState = WorkflowState::WAITING_PICKUP;
    onLogMessage("工作流程状态初始化: WAITING_PICKUP", "系统");

    // 重置面编号为第一面
    m_currentFace = 101;

    // 更新UI状态
    btnStart->setEnabled(false);
    btnStop->setEnabled(true);
    btnPause->setEnabled(true);

    // 记录日志
    onLogMessage("系统已启动，等待机器人取料", "系统");
    statusBar->showMessage("系统运行中...");

    // 更新设备状态
    deviceStatusWidget->setSystemRunning(true);


}

void MainWindow::onStopSystem() {
    // 停止系统运行
    systemRunning = false;
    systemPaused = false;

    // 处理当前的统计记录（如果有的话）
    if (statisticsManager && !currentProductionRecordId.isEmpty()) {
        // 修复：统一面数计算逻辑，从101开始所以减101
        int completedFaces = qMax(0, m_currentFace - 101);
        statisticsManager->updateProductionRecord(currentProductionRecordId, completedFaces);
        statisticsManager->finishProductionRecord(currentProductionRecordId,
                                                ProductionStatus::INTERRUPTED,
                                                "系统停止");
        currentProductionRecordId.clear();
    }

    // 重置工作流程状态为空闲
    currentWorkflowState = WorkflowState::IDLE;
    onLogMessage("工作流程状态重置: IDLE", "系统");

    // 更新UI状态
    btnStart->setEnabled(true);
    btnStop->setEnabled(false);
    btnPause->setEnabled(false);

    // 启用设备断开按钮
    btnDisconnectABB->setEnabled(true);
    btnDisconnectLaser->setEnabled(true);
    btnDisconnectRFID->setEnabled(true);

    // 通知ABB机器人停止运行 - 修正调用方式
    abbDriver->SendCommand(RobotDriver::ABBCommandType::STOP);

    // 关闭激光
    if (laserControl->IsWorking()) {
        laserControl->LaserOff();
        laserControl->EndMark();
    }

    // 记录日志
    onLogMessage("系统已停止", "系统");
    statusBar->showMessage("系统已停止", 3000);

    // 更新设备状态
    deviceStatusWidget->setSystemRunning(false);

    // 停止RFID定时读取
    stopRFIDTimer();
}

void MainWindow::onPauseSystem() {
    if (systemRunning) {
        systemPaused = !systemPaused;

        if (systemPaused) {
            // 暂停系统
            btnPause->setText("继续");

            // 通知ABB机器人暂停 - 修正调用方式
            abbDriver->SendCommand(RobotDriver::ABBCommandType::STOP);

            // 记录日志
            onLogMessage("系统已暂停", "系统");
            statusBar->showMessage("系统已暂停");

            // 更新设备状态
            deviceStatusWidget->setSystemPaused(true);
        } else {
            // 继续系统
            btnPause->setText("暂停");

            // 通知ABB机器人继续 - 修正调用方式
            abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT);

            // 记录日志
            onLogMessage("系统已继续运行", "系统");
            statusBar->showMessage("系统运行中...");

            // 更新设备状态
            deviceStatusWidget->setSystemPaused(false);
        }
    }
}

void MainWindow::onResetSystem() {
    // 如果系统正在运行，先停止
    if (systemRunning) {
        onStopSystem();
    }

    // 重置工作流程状态为空闲
    currentWorkflowState = WorkflowState::IDLE;
    onLogMessage("系统重置，工作流程状态重置: IDLE", "系统");

    // 重置面编号为第一面
    m_currentFace = 101;

    // 重置激光状态
    if (laserControl->IsConnected()) {
        laserControl->ResetLaserState();
        laserControl->ClearData();
    }

    // 通知ABB机器人重置 - 修正调用方式
    if (abbDriver->IsRunning()) {
        abbDriver->SendCommand(RobotDriver::ABBCommandType::RESET);
    }

    // 重置统计信息
    currentSessionProcessed = 0;
    sessionStartTime = QDateTime::currentDateTime();

    // 更新统计显示
    updateStatistics();

    // 记录日志
    onLogMessage("系统已重置", "系统");
    statusBar->showMessage("系统已重置", 3000);
}


void MainWindow::onExitProgram() {
    // 确认退出对话框
    int ret = QMessageBox::question(this, "确认退出",
                                    "确定要退出程序吗？\n系统将停止运行并断开所有设备连接。",
                                    QMessageBox::Yes | QMessageBox::No,
                                    QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 如果系统正在运行，先停止系统
        if (systemRunning) {
            onLogMessage("程序退出前停止系统运行", "系统");
            onStopSystem();
        }

        // 保存模板数据
        if (templateManager) {
            saveTemplates();
            onLogMessage("模板数据已保存", "系统");
        }

        // 记录退出日志
        onLogMessage("程序正在退出...", "系统");

        // 关闭主窗口，这将触发程序退出
        this->close();
    }
}

void MainWindow::onClearLaserData() {
    // 检查激光设备连接状态
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "设备未连接", "激光设备未连接，无法清除数据。");
        return;
    }

    // 确认对话框
    int ret = QMessageBox::question(this, "确认清除",
                                    "确定要清除所有激光数据吗？\n此操作不可撤销。",
                                    QMessageBox::Yes | QMessageBox::No,
                                    QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 清除激光数据
        if (laserControl->ClearData()) {
            onLogMessage("激光数据已清除", "激光控制");
            statusBar->showMessage("激光数据已清除", 3000);

            // 更新UI状态
            lblCurrentMarkDataDir->setText("打粗数据目录: 未设置");
            lblCurrentProcessParamDir->setText("工艺参数目录: 未设置");
            lblCurrentWorkDir->setText("工作目录: 未设置");

            QMessageBox::information(this, "操作成功", "激光数据已成功清除。");
        } else {
            bool hasError;
            std::string errorMsg;
            laserControl->GetErrorMessage(hasError, errorMsg);

            QString message = "清除激光数据失败";
            if (hasError) {
                message += "：" + QString::fromStdString(errorMsg.c_str());
            }

            onLogMessage(message, "错误");
            QMessageBox::critical(this, "操作失败", message);
        }
    }
}

void MainWindow::onResetLaserStatus() {
    // 检查激光设备连接状态
    if (!laserControl->IsConnected()) {
        QMessageBox::warning(this, "设备未连接", "激光设备未连接，无法复位状态。");
        return;
    }

    // 确认对话框
    int ret = QMessageBox::question(this, "确认复位",
                                    "确定要复位激光状态吗？\n这将停止当前所有激光操作。",
                                    QMessageBox::Yes | QMessageBox::No,
                                    QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        // 如果激光正在工作，先停止
        if (laserControl->IsWorking()) {
            laserControl->LaserOff();
            laserControl->EndMark();
            onLogMessage("停止当前激光操作", "激光控制");
        }

        // 复位激光状态
        if (laserControl->ResetLaserState()) {
            onLogMessage("激光状态已复位", "激光控制");
            statusBar->showMessage("激光状态已复位", 3000);


            QMessageBox::information(this, "操作成功", "激光状态已成功复位。");
        } else {
            bool hasError;
            std::string errorMsg;
            laserControl->GetErrorMessage(hasError, errorMsg);

            QString message = "复位激光状态失败";
            if (hasError) {
                message += "：" + QString::fromStdString(errorMsg.c_str());
            }

            onLogMessage(message, "错误");
            QMessageBox::critical(this, "操作失败", message);
        }
    }
}

// 状态更新实现
void MainWindow::updateDeviceStatus() {
    // 检查UI元素是否已初始化
    if (!deviceStatusWidget) {
        return;
    }

    // 更新ABB机器人状态
    if (abbDriver && abbDriver->IsRunning()) {
        // 检查是否有客户端连接
        bool clientConnected = abbDriver->IsConnected();
        deviceStatusWidget->setABBConnected(clientConnected);

        if (clientConnected) {
            // 获取ABB机器人状态
            RobotDriver::RobotStatus status = abbDriver->GetStatus();
            deviceStatusWidget->setABBPosition(QString::fromStdString(status.position));
            deviceStatusWidget->setABBStatus(QString::fromStdString(status.statusMessage));
        } else {
            // 服务器运行但客户端未连接
        }
    } else {
        deviceStatusWidget->setABBConnected(false);
        deviceStatusWidget->setABBStatus("服务器未启动");
    }

    // 更新激光打粗状态
    if (laserControl && laserControl->IsConnected()) {
        deviceStatusWidget->setLaserConnected(true);

        // 获取激光打粗状态
        // 激光工作状态检查（UI指示器已移除）
        bool isWorking = laserControl->IsWorking();

        // 获取错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError && !errorMsg.empty()) {
            QString errorText = QString::fromStdString(errorMsg.c_str());
            deviceStatusWidget->setLaserError(true, errorText);

            // 记录错误日志
            onLogMessage("激光打粗错误: " + errorText, "错误");
        } else {
            deviceStatusWidget->setLaserError(false, "");
        }
    } else {
        deviceStatusWidget->setLaserConnected(false);
        // 激光工作状态指示器已移除
        deviceStatusWidget->setLaserError(false, "");
    }

    // 更新RFID设备状态
    if (rfidDriver && rfidDriver->isConnected()) {
        deviceStatusWidget->setRFIDConnected(true);

        // 获取RFID设备状态
        bool isReading = rfidDriver->isReading();
        QString lastTag = QString::fromStdString(rfidDriver->getLastTagID());
        deviceStatusWidget->setRFIDStatus(isReading, lastTag);
    } else {
        deviceStatusWidget->setRFIDConnected(false);
        deviceStatusWidget->setRFIDStatus(false, "");
    }

    // 更新系统状态
    // deviceStatusWidget->setSystemRunning(systemRunning);
    // deviceStatusWidget->setSystemPaused(systemPaused);
}

void MainWindow::updateStatistics() {
    // 计算系统运行时间
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 runTimeSeconds = sessionStartTime.secsTo(currentTime);

    int hours = runTimeSeconds / 3600;
    int minutes = (runTimeSeconds % 3600) / 60;
    int seconds = runTimeSeconds % 60;

    QString runTimeText = QString("%1:%2:%3")
            .arg(hours, 2, 10, QChar('0'))
            .arg(minutes, 2, 10, QChar('0'))
            .arg(seconds, 2, 10, QChar('0'));

    // 设置运行时间
    deviceStatusWidget->setRunTime(runTimeText);

    // 更新统计图表
    updateStatisticsChart();
}

void MainWindow::updateStatisticsChart() {
    // 清除之前的图表
    statisticsChart->removeAllSeries();

    // 设置图表主题和样式
    statisticsChart->setTheme(QChart::ChartThemeLight);
    statisticsChart->setBackgroundBrush(QBrush(QColor("#ffffff")));
    statisticsChart->setTitleBrush(QBrush(QColor("#2980b9")));
    statisticsChart->setTitleFont(QFont("Microsoft YaHei", 12, QFont::Bold));

    // 创建饼图系列
    QPieSeries *series = new QPieSeries();
    int historyCount = totalProcessed - currentSessionProcessed;
    series->append(QString("本次打粗数量: %1").arg(currentSessionProcessed), currentSessionProcessed);
    series->append(QString("历史打粗总数: %1").arg(historyCount), historyCount);

    // 设置系列属性
    series->setLabelsVisible(true);
    series->setLabelsPosition(QPieSlice::LabelOutside);

    // 突出显示当前会话
    QPieSlice *slice = series->slices().at(0);
    slice->setExploded(true);
    slice->setLabelVisible(true);
    slice->setPen(QPen(QColor("#3498db"), 2));
    slice->setBrush(QColor("#3498db"));

    // 设置历史数据切片样式
    QPieSlice *historySlice = series->slices().at(1);
    historySlice->setPen(QPen(QColor("#2c3e50"), 2));
    historySlice->setBrush(QColor("#95a5a6"));

    // 添加系列到图表
    statisticsChart->addSeries(series);
    statisticsChart->setTitle("打粗统计 (总计: " + QString::number(totalProcessed) + ")");

    // 设置图例
    statisticsChart->legend()->setAlignment(Qt::AlignBottom);
    statisticsChart->legend()->setFont(QFont("Microsoft YaHei", 9));
    statisticsChart->legend()->setLabelBrush(QBrush(QColor("#2c3e50")));

    // 通知视图更新
    statisticsChartView->update();
}

// ABB消息处理
void MainWindow::onABBMessageReceived(const RobotDriver::ABBMessage &message) {
    // m_abbMessage = message;

      if (!systemRunning) {
        return;
    }
    switch (message.type) {
        case RobotDriver::ABBMessageType::PICKUP_COMPLETED:
            // 机器人取料完成，开始处理RFID请求
            onLogMessage("机器人取料完成，开始处理RFID请求", "系统");
            processRFIDQueue();
            abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, {static_cast<double>(101)});
            break;

        case RobotDriver::ABBMessageType::REACHED_TARGET_POSITION:
        {
            // 检查当前面状态，如果为0表示已完成所有处理，机器人到达输送线位置
            if (m_currentFace == 0) {
                onLogMessage("机器人已到达输送线位置，等待下一个工件", "系统");
                break;
            }
            
            // 检查激光是否正在处理中，避免重复调用
            if (m_isLaserProcessing) {
                onLogMessage("激光正在处理中，跳过重复调用", "警告");
                break;
            }
            
            startLaserMarking();
            // 机器人到达目标位置，开始激光打粗
            onLogMessage("机器人到达目标位置，开始激光打粗", "系统");

            break;
        }

        case RobotDriver::ABBMessageType::OPERATION_COMPLETED:
        {
            // 机器人操作完成，检查是否需要处理下一面
            onLogMessage("所有面处理完成", "系统");

            break;
        }

        case RobotDriver::ABBMessageType::ABB_ERROR:
        {
            // 处理机器人错误
            onLogMessage("机器人报告错误: " + QString::fromStdString(message.GetContent()), "错误");
            QMessageBox::warning(this, "机器人错误", "机器人报告错误: " + QString::fromStdString(message.GetContent()));
            break;
        }

        default:
        {
            onLogMessage("收到未知类型的机器人消息: " + QString::fromStdString(message.rawMessage), "警告");
            break;
        }
    }
}


void MainWindow::handleRFIDRequest() {
    // 检查RFID设备连接状态 - 如果未连接直接返回，避免网络阻塞
    if (!rfidDriver || !rfidDriver->isConnected()) {
        // 不记录错误日志，避免刷屏，直接返回
        return;
    }

    // 从施耐德PLC Modbus TCP读取RFID数据（小端模式，地址16，2个字）
    uint16_t rfidData[2] = {0}; // 2个寄存器

    try {
        int result = rfidDriver->readHoldingRegisters(16, 2, rfidData);
        if (result == -1) {
            // 读取失败，直接返回避免阻塞
            return;
        }
    } catch (...) {
        // 网络异常时直接返回，避免UI阻塞
        return;
    }

    // 小端字节序组合为32位值（低字在前，高字在后）
    uint32_t rfidValue = (static_cast<uint32_t>(rfidData[1]) << 16) | rfidData[0];
    QString rfidDecimal = QString::number(rfidValue);

    // 检查RFID编码是否有效（不为空且符合编码规则）
    if (rfidValue == 0 || !isValidRFIDCode(rfidDecimal)) {
        return; // 无效编码，直接返回
    }

    //这个编码可能会重复的，只要上一次和这一次不同就可以，如果前面是空的，是可以重复的
    if (rfidDecimal == lastRFIDCode && !rfidQueue.empty() ) {
        return; // 相同编码，直接返回
    }

    // 更新上次读取的编码
    lastRFIDCode = rfidDecimal;
    onLogMessage("RFID读取(小端): " + rfidDecimal, "RFID");

    // 将有效的RFID编码加入队列
    {
        QMutexLocker locker(&rfidQueueMutex);
        // 检查队列中是否已存在相同的RFID，避免重复添加
        if (!rfidQueue.contains(rfidDecimal)) {
            rfidQueue.append(rfidDecimal);
            onLogMessage(QString("RFID编码已加入队列，当前队列长度: %1").arg(rfidQueue.size()), "RFID");
        } else {
            onLogMessage(QString("RFID编码已存在于队列中，跳过添加: %1").arg(rfidDecimal), "RFID");
        }
    }
}

/**
 * @brief 处理RFID队列
 * 当机器人到达目标位置时，从队列中取出一个RFID编码进行处理
 */
void MainWindow::processRFIDQueue() {
    // // 检查当前工作流程状态，只有在等待取料完成或空闲状态下才处理队列
    // if (currentWorkflowState != WorkflowState::WAITING_PICKUP &&
    //     currentWorkflowState != WorkflowState::IDLE) {
    //     return;
    // }

    // 修复：使用更安全的队列处理方式，直接取出并删除头部元素
    QString currentRFID;
    {
        QMutexLocker locker(&rfidQueueMutex);
        if (rfidQueue.isEmpty()) {
            onLogMessage("RFID队列为空，无法处理", "RFID");
            return;
        }

        // 安全地取出并删除队列头部的RFID编码
        currentRFID = rfidQueue.takeFirst();
        onLogMessage(QString("从队列中取出RFID: %1，剩余队列长度: %2").arg(currentRFID).arg(rfidQueue.size()), "RFID");
    }

    // 设置当前处理的RFID
    rfidDecimal = currentRFID;

    // 查找匹配的模板
    ShoeTemplate templ;
    if (templateManager->findTemplateByRFID(rfidDecimal, templ)) {
        onLogMessage("找到匹配的模板: " + templ.templateName, "系统");
        deviceStatusWidget->setCurrentRFID(rfidDecimal);
        deviceStatusWidget->setCurrentTemplate(templ.templateName);
        deviceStatusWidget->setCurrentShoeInfo(templ.shoeCode, templ.isLeftFoot);

        // 开始统计记录
        if (statisticsManager && currentProductionRecordId.isEmpty()) {
            m_totalFaces = countTotalFaces(templ.laserWorkDir);
            currentProductionRecordId = statisticsManager->startProductionRecord(
                rfidDecimal,
                templ.templateName,
                templ.shoeCode,
                templ.isLeftFoot,
                m_totalFaces
            );
        }

        if (laserControl->IsConnected()) {
            if (!laserControl->ClearData()) {
                onLogMessage("清除激光参数失败", "错误");
                return;
            }
            onLogMessage("已清除激光参数", "系统");
            // 初始化面计数器为101，表示开始处理第一面
            m_currentFace = 101;
            m_totalFaces = countTotalFaces(templ.laserWorkDir);
            onLogMessage(QString("检测到%1个打粗面").arg(m_totalFaces), "系统");
            m_currentTemplate = templ;
        }
        std::vector<double> params = {static_cast<double>(templ.shoeCode), templ.isLeftFoot ? 1.0 : 0.0};
        abbDriver->SendCommand(RobotDriver::ABBCommandType::LASER_READY, params);
        onLogMessage("已通知ABB机器人激光准备就绪", "系统");

        // 修复：RFID已在取出时删除，这里只需要记录成功处理
        onLogMessage(QString("RFID编码处理成功: %1").arg(rfidDecimal), "RFID");

    } else {
        onLogMessage("未找到匹配的RFID模板: " + rfidDecimal, "错误");
        QMessageBox::warning(this, "模板错误", "未找到匹配的RFID模板: " + rfidDecimal);

        // 修复：RFID已在取出时删除，这里只需要记录处理失败
        onLogMessage(QString("无效的RFID编码处理失败: %1").arg(rfidDecimal), "RFID");
    }
}


QString MainWindow::getNextRFIDFromQueue() {
    QMutexLocker locker(&rfidQueueMutex);
    if (!rfidQueue.isEmpty()) {
        return rfidQueue.takeFirst();
    }

    return QString(); // 返回空字符串表示队列为空
}




bool MainWindow::isRFIDQueueEmpty() {
    QMutexLocker locker(&rfidQueueMutex);
    return rfidQueue.isEmpty();
}

int MainWindow::getRFIDQueueSize() {
    QMutexLocker locker(&rfidQueueMutex);
    return rfidQueue.size();
}

void MainWindow::startLaserMarking() {
    if (!laserControl->IsConnected()) {
        onLogMessage("激光打粗未连接，无法开始打粗", "错误");
        return;
    }

    // 检查是否已经在处理中
    if (m_isLaserProcessing) {
        onLogMessage("激光已在处理中，跳过重复调用", "警告");
        return;
    }

    // 设置激光处理标志
    m_isLaserProcessing = true;

    // 处理当前面的打粗
    if (!processMarkingFace()) {
        onLogMessage("无法处理当前打粗面", "错误");
        m_isLaserProcessing = false;  // 重置标志
        return;
    }

    // 开始打粗
    if (laserControl->BeginMark(false)) {
        onLogMessage(QString::number(laserControl->GetLaserMarkTime()), "警告");

        // false表示非预览模式
        // 修复：统一面数计算逻辑，当前正在处理的面是m_currentFace-100，已完成的面是m_currentFace-101
        int currentFaceNumber = m_currentFace - 100;  // 当前正在处理的面编号（1,2,3...）
        int completedFaces = qMax(0, m_currentFace - 101);  // 已完成的面数
        int remainingFaces = m_totalFaces - currentFaceNumber;  // 剩余面数（包括当前面）
        onLogMessage(QString("开始激光打粗 (面 %1/%2) - 已完成: %3面, 剩余: %4面")
                     .arg(currentFaceNumber).arg(m_totalFaces).arg(completedFaces).arg(remainingFaces), "激光打粗");
        // 更新激光工作状态为打粗中
        deviceStatusWidget->setLaserWorkingStatus(true, "打粗中");

        // 更新面进度显示
        deviceStatusWidget->setFaceProgress(m_currentFace, m_totalFaces, completedFaces);

        // 启动打粗进度更新定时器
        QTimer::singleShot(500, this, [this]() {
            updateMarkingProgress(10); // 10%进度
        });

        // 启动激光工作状态检查定时器，每500ms检查一次
        laserWorkingCheckTimer->start(100);
    } else {
        // 获取具体错误信息
        bool hasError;
        std::string errorMsg;
        laserControl->GetErrorMessage(hasError, errorMsg);

        if (hasError) {
            QString errorText = QString::fromStdString(errorMsg);
            onLogMessage(QString("激光打粗错误: %1").arg(errorText), "错误");

            // 记录统计失败，但保留已完成的面数
            if (statisticsManager && !currentProductionRecordId.isEmpty()) {
                // 修复：记录当前已完成的面数，即使失败也要保留进度
                int completedFaces = qMax(0, m_currentFace - 101);
                statisticsManager->updateProductionRecord(currentProductionRecordId, completedFaces);
                statisticsManager->finishProductionRecord(currentProductionRecordId,
                                                        ProductionStatus::FAILED,
                                                        errorText);
                currentProductionRecordId.clear();
            }

        } else {
            onLogMessage("开始激光打粗失败", "错误");

            // 记录统计失败
            if (statisticsManager && !currentProductionRecordId.isEmpty()) {
                statisticsManager->finishProductionRecord(currentProductionRecordId,
                                                        ProductionStatus::FAILED,
                                                        "开始激光打粗失败");
                currentProductionRecordId.clear();
            }

            m_isLaserProcessing = false;  // 重置标志
        }
    }

}

/**
 * @brief 检查激光工作状态
 * 异步检查激光是否完成工作，替代阻塞式的while循环
 */
void MainWindow::checkLaserWorkingStatus() {
    if (!laserControl->IsConnected()) {
        // 激光设备断开连接，停止检查
        laserWorkingCheckTimer->stop();
        m_laserNotWorkingCount = 0;  // 重置计数器
        onLogMessage("激光设备连接断开，停止工作状态检查", "错误");
        return;
    }

    if (!laserControl->IsWorking()) {
        // 激光未工作，递增计数器
        m_laserNotWorkingCount++;
        onLogMessage(QString("激光未工作检测次数: %1/10").arg(m_laserNotWorkingCount), "激光打粗");

        // 检查是否连续10次都没有工作
        if (m_laserNotWorkingCount >= 3) {
            // 激光工作完成，停止定时器
            laserWorkingCheckTimer->stop();
            m_laserNotWorkingCount = 0;  // 重置计数器

            onLogMessage("激光工作完成（连续3次未工作），继续后续流程", "激光打粗");

            // 修复：当前面已完成，更新统计记录 - 当前面完成后的已完成面数
            int currentFaceNumber = m_currentFace - 100;  // 当前面编号
            int completedFaces = currentFaceNumber;  // 当前面完成后的已完成面数

            if (statisticsManager && !currentProductionRecordId.isEmpty()) {
                statisticsManager->updateProductionRecord(currentProductionRecordId, completedFaces);
            }

            // 更新激光工作状态为空闲
            deviceStatusWidget->setLaserWorkingStatus(false, "空闲");

            // 等待激光完全停止，避免"Laser is Busy"错误
            Sleep(100);

            // 继续ABB机器人的后续流程
            // 检查是否还有更多面需要处理
            if (currentFaceNumber < m_totalFaces) {
                // 递增到下一面
                m_currentFace++;
                std::vector<double> params = {static_cast<double>(m_currentTemplate.shoeCode), m_currentTemplate.isLeftFoot ? 1.0 : 0.0};
                abbDriver->SendCommand(RobotDriver::ABBCommandType::LASER_READY, params);
                // 还有下一面，发送移动到下一位置的命令
                abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, {static_cast<double>(m_currentFace)});
                onLogMessage(QString("通知机器人移动到下一面 %1 的位置").arg(m_currentFace - 100), "系统");

                // 注意：这里不再立即开始激光打粗，而是等待机器人到达目标位置后
                // 通过REACHED_TARGET_POSITION消息触发startLaserMarking()
            } else {
                std::vector<double> params = {static_cast<double>(m_currentTemplate.shoeCode), m_currentTemplate.isLeftFoot ? 1.0 : 0.0};
                abbDriver->SendCommand(RobotDriver::ABBCommandType::LASER_READY, params);
                // 所有面都已完成，通知机器人放到输送线
                abbDriver->SendCommand(RobotDriver::ABBCommandType::MOVE_NEXT, {0.0});
                onLogMessage(QString("所有面处理完成，通知机器人放到输送线"), "系统");
                finishLaserMarking();
            }
            m_isLaserProcessing = false;  // 重置激光处理标志
        }

    } else {
        // 激光正在工作，重置计数器
        m_laserNotWorkingCount = 0;
    }
}

// 新增处理打粗面的方法
bool MainWindow::processMarkingFace() {
    // 检查是否有有效的当前模板
    if (m_currentTemplate.laserWorkDir.isEmpty()) {
        onLogMessage("没有有效的模板信息", "错误");
        return false;
    }

    // 构建当前面的目录路径
    QString faceDirPath = m_currentTemplate.laserWorkDir + "/" + QString::number(m_currentFace);
    QDir faceDir(faceDirPath);

    // 检查当前面的目录是否存在
    if (!faceDir.exists()) {
        onLogMessage(QString("找不到面 %1 的目录: %2").arg(m_currentFace - 100).arg(faceDirPath), "错误");
        return false;
    }

    // 设置当前面的工作目录
    if (!laserControl->SetWorkDir(faceDirPath.toStdString())) {
        onLogMessage(QString("设置面 %1 的工作目录失败: %2").arg(m_currentFace - 100).arg(faceDirPath), "错误");
        return false;
    }

    onLogMessage(QString("切换到面 %1 的工作目录: %2").arg(m_currentFace - 100).arg(faceDirPath), "激光打粗");

    // 更新显示
    lblCurrentWorkDir->setText("工作目录: " + faceDirPath);

    return true;
}

void MainWindow::finishLaserMarking() {
    if (!laserControl->IsConnected()) {
        return;
    }

    // 更新激光工作状态为完成
    deviceStatusWidget->setLaserWorkingStatus(false, "打粗完成");

    // 修复：所有面都已完成，计算最终的完成面数
    int completedFaces = m_totalFaces; // 所有面都已完成
    deviceStatusWidget->setFaceProgress(m_currentFace, m_totalFaces, completedFaces);


    if (!laserControl->IsWorking()) {
        // 所有面都已完成
        int remainingFaces = 0;  // 没有剩余面数
        onLogMessage(QString("所有面激光打粗完成 - 总共完成: %1面")
                     .arg(completedFaces), "激光打粗");

        // 重置面计数器，为下一个鞋子做准备
        // 设置为0表示当前鞋子已完成处理，避免重复处理
        m_currentFace = 0;

        // 重置面进度显示
        deviceStatusWidget->setFaceProgress(0, m_totalFaces, 0);

        // 完成统计记录
        if (statisticsManager && !currentProductionRecordId.isEmpty()) {
            // 更新完成的面数为总面数
            statisticsManager->updateProductionRecord(currentProductionRecordId, completedFaces);
            // 完成记录
            statisticsManager->finishProductionRecord(currentProductionRecordId, ProductionStatus::SUCCESS);
        }

        // 更新打粗计数
        currentSessionProcessed = 1;
        totalProcessed++;
        updateStatistics();

        // 更新模板的打粗次数
        if (!m_currentTemplate.rfidId.isEmpty()) {
            templateManager->updateMarkCount(m_currentTemplate.rfidId);
            templateManager->updateTemplateModel(templateModel);
        }

        onLogMessage(QString("激光打粗完成 - 总共完成: %1面").arg(m_totalFaces), "系统");
    } else {
        onLogMessage("结束激光打粗失败", "错误");
    }
}

// 计算总面数的辅助方法
int MainWindow::countTotalFaces(const QString &workDir) {
    QDir dir(workDir);
    if (!dir.exists()) {
        onLogMessage(QString("工作目录不存在: %1").arg(workDir), "错误");
        return 1; // 默认返回1
    }

    QStringList faceDirs = dir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

    int count = 0;
    QList<int> validFaceNumbers;

    for (const QString &dirName: faceDirs) {
        bool ok;
        int faceNum = dirName.toInt(&ok);
        if (ok && faceNum >= 101 && faceNum <= 199) {
            // 检查面目录是否包含必要的文件（可选验证）
            QDir faceDir(dir.absoluteFilePath(dirName));
            if (faceDir.exists()) {
                validFaceNumbers.append(faceNum);
                count++;
            }
        }
    }

    // 记录找到的有效面数
    if (count > 0) {
        std::sort(validFaceNumbers.begin(), validFaceNumbers.end());
        onLogMessage(QString("找到 %1 个有效面目录，面编号: %2 到 %3")
                     .arg(count)
                     .arg(validFaceNumbers.first() - 100)
                     .arg(validFaceNumbers.last() - 100), "系统");
    } else {
        onLogMessage(QString("未找到有效的面目录，使用默认值1"), "警告");
    }

    return count > 0 ? count : 1; // 至少返回1
}

// 更新打粗进度的辅助方法
void MainWindow::updateMarkingProgress(int progress) {
    if (!laserControl->IsConnected() || !laserControl->IsWorking()) {
        return;
    }

    // 修复：统一面数计算逻辑
    int currentFaceNumber = m_currentFace - 100;  // 当前正在处理的面编号（1,2,3...）
    int completedFaces = qMax(0, currentFaceNumber - 1); // 已完成的面数（当前面还在处理中）
    int remainingFaces = m_totalFaces - currentFaceNumber; // 剩余面数（不包括当前面）

    // 计算当前面的进度（0-100）
    int currentFaceProgress = progress;

    // 计算整体进度：已完成面数的进度 + 当前面的进度
    int totalProgress = 0;
    if (m_totalFaces > 0) {
        // 每个面占总进度的百分比
        double progressPerFace = 100.0 / m_totalFaces;
        // 已完成面数的进度
        double completedProgress = completedFaces * progressPerFace;
        // 当前面的进度贡献
        double currentFaceContribution = (currentFaceProgress / 100.0) * progressPerFace;
        // 总进度
        totalProgress = static_cast<int>(completedProgress + currentFaceContribution);

        // 确保进度不超过100
        if (totalProgress > 100) {
            totalProgress = 100;
        }
    }

    // 进度条已移除，不再更新进度条显示

    // 更新面进度显示
    deviceStatusWidget->setFaceProgress(m_currentFace, m_totalFaces, completedFaces);

    // 更新日志信息，显示当前面进度和整体进度
    QString progressInfo = QString("面 %1/%2 进度: %3%, 整体进度: %4%, 已完成: %5面, 剩余: %6面")
            .arg(currentFaceNumber)
            .arg(m_totalFaces)
            .arg(currentFaceProgress)
            .arg(totalProgress)
            .arg(completedFaces)
            .arg(remainingFaces);

    // 每隔一定进度输出一次日志，避免日志过多
    static int lastLoggedProgress = -1;
    if (currentFaceProgress - lastLoggedProgress >= 20 || currentFaceProgress == 100) {
        onLogMessage(progressInfo, "打粗进度");
        lastLoggedProgress = currentFaceProgress;
    }

    // 如果当前面未完成，继续更新进度
    if (progress < 100) {
        // 计算下一个进度值（非线性增长模拟真实打粗过程）
        int nextProgress = progress;
        if (progress < 30) {
            nextProgress += 5; // 初期进度快一些
        } else if (progress < 70) {
            nextProgress += 3; // 中期进度稍慢
        } else {
            nextProgress += 2; // 后期进度更慢
        }

        // 安排下一次更新
        QTimer::singleShot(300, this, [this, nextProgress]() {
            updateMarkingProgress(nextProgress);
        });
    } else {
        // 当前面完成时，重置日志进度记录
        lastLoggedProgress = -1;
    }
}

// 导航切换实现
void MainWindow::onNavItemChanged(int index) {
    // 切换到选中的页面
    mainAreaStack->setCurrentIndex(index);

    // 更新状态栏
    switch (index) {
        case 0: // 设备状态
            statusBar->showMessage("设备状态", 3000);
            break;
        case 1: // 模板管理
            statusBar->showMessage("模板管理", 3000);
            // 自动加载默认位置的模板
            {
                QString templateCSVPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) +
                                          "/templates.csv";
                if (QFile::exists(templateCSVPath)) {
                    templateManager->loadFromCSV(templateCSVPath);
                    templateManager->updateTemplateModel(templateModel);
                    onLogMessage("已从默认位置加载模板", "模板管理");
                }

                // 检查是否有模板，如果没有则创建默认模板
                if (templateManager->getTemplateCount() == 0) {
                    templateManager->createDefaultTemplate();
                    templateManager->updateTemplateModel(templateModel);
                    onLogMessage("已创建默认模板", "模板管理");
                    QMessageBox::information(this, "创建默认模板", "系统未检测到模板，已创建一个默认模板供您使用。\n您可以根据需要编辑此模板或添加新模板。");
                }
            }
            break;
        case 2: // 统计信息
            statusBar->showMessage("统计信息", 3000);
            // 延迟更新统计数据，避免与EnhancedStatisticsWidget的初始化冲突
            QTimer::singleShot(100, this, [this]() {
                updateStatistics();
            });
            break;
        case 3: // 手动激光控制
            statusBar->showMessage("手动激光控制", 3000);
            break;
        case 4: // 系统日志
            statusBar->showMessage("系统日志", 3000);
            break;
    }
}

// 模板管理实现
void MainWindow::onAddTemplate() {
    // 检查注册状态
    if (!checkLicense()) {
        QMessageBox::warning(this, "功能受限", "软件未注册，模板管理功能不可用。");
        return;
    }

    // 创建模板编辑对话框 - 使用堆分配而非栈分配
    TemplateEditDialog *dialog = new TemplateEditDialog(templateManager, rfidDriver, abbDriver, this, this);
    dialog->setAttribute(Qt::WA_DeleteOnClose); // 确保对话框关闭时自动删除

    // 连接信号槽，以便在对话框关闭后获取结果
    connect(dialog, &QDialog::accepted, [this, dialog]() {
        // 获取模板数据
        ShoeTemplate newTemplate = dialog->getTemplateData();

        // 添加到模板管理器
        if (templateManager->addTemplate(newTemplate)) {
            // 更新模板表格
            templateManager->updateTemplateModel(templateModel);

            // 记录日志
            onLogMessage("添加模板: " + newTemplate.templateName, "模板管理");

            // 保存模板到CSV
            saveTemplates();
        }
    });

    // 显示对话框（非模态方式）
    dialog->show();
}

void MainWindow::onEditTemplate() {
    // 获取当前选中的模板
    QModelIndex currentIndex = templateTableView->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, "编辑模板", "请先选择要编辑的模板。");
        return;
    }

    // 获取模板RFID
    QString rfidId = templateModel->data(templateModel->index(currentIndex.row(), 0), Qt::DisplayRole).toString();

    // 从模板管理器获取模板
    ShoeTemplate templateToEdit;
    if (templateManager->findTemplateByRFID(rfidId, templateToEdit)) {
        // 创建模板编辑对话框 - 使用堆分配而非栈分配
        TemplateEditDialog *dialog = new TemplateEditDialog(templateManager, rfidDriver, abbDriver, templateToEdit,
                                                            this, this);
        dialog->setAttribute(Qt::WA_DeleteOnClose); // 确保对话框关闭时自动删除

        // 连接信号槽，以便在对话框关闭后获取结果
        connect(dialog, &QDialog::accepted, [this, dialog]() {
            // 获取更新后的模板数据
            ShoeTemplate updatedTemplate = dialog->getTemplateData();

            // 更新模板
            if (templateManager->updateTemplate(updatedTemplate)) {
                // 更新模板表格
                templateManager->updateTemplateModel(templateModel);

                // 记录日志
                onLogMessage("更新模板: " + updatedTemplate.templateName, "模板管理");

                // 保存模板到CSV
                saveTemplates();
            }
        });

        // 显示对话框（非模态方式）
        dialog->show();
    }
}

void MainWindow::onDeleteTemplate() {
    // 获取当前选中的模板
    QModelIndex currentIndex = templateTableView->currentIndex();
    if (!currentIndex.isValid()) {
        QMessageBox::warning(this, "删除模板", "请先选择要删除的模板。");
        return;
    }

    // 获取模板RFID和名称
    QString rfidId = templateModel->data(templateModel->index(currentIndex.row(), 0), Qt::DisplayRole).toString();
    QString templateName = templateModel->data(templateModel->index(currentIndex.row(), 1), Qt::DisplayRole).toString();

    // 确认删除
    QMessageBox::StandardButton reply = QMessageBox::question(this, "删除模板",
                                                              "确定要删除模板 \"" + templateName + "\" 吗？",
                                                              QMessageBox::Yes | QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        // 从模板管理器删除模板
        if (templateManager->deleteTemplate(rfidId)) {
            // 更新模板表格
            templateManager->updateTemplateModel(templateModel);

            // 记录日志
            onLogMessage("删除模板: " + templateName, "模板管理");

            // 保存模板到CSV
            saveTemplates();
        }
    }
}

void MainWindow::onImportTemplates() {
    // 选择CSV文件
    QString filePath = QFileDialog::getOpenFileName(this, "导入模板",
                                                    QDir::homePath(),
                                                    "CSV文件 (*.csv)");

    if (filePath.isEmpty()) {
        return;
    }

    // 导入模板
    int count = templateManager->loadFromCSV(filePath);
    if (count > 0) {
        // 更新模板表格
        templateManager->updateTemplateModel(templateModel);

        // 记录日志
        onLogMessage("导入 " + QString::number(count) + " 个模板", "模板管理");

        // 保存合并后的模板到默认CSV
        saveTemplates();

        QMessageBox::information(this, "导入模板", "成功导入 " + QString::number(count) + " 个模板。");
    } else {
        QMessageBox::warning(this, "导入模板", "导入失败或没有可导入的模板。");
    }
}

void MainWindow::onExportTemplates() {
    // 选择保存路径
    QString filePath = QFileDialog::getSaveFileName(this, "导出模板",
                                                    QDir::homePath() + "/templates.csv",
                                                    "CSV文件 (*.csv)");

    if (filePath.isEmpty()) {
        return;
    }

    // 导出模板
    int count = templateManager->saveToCSV(filePath);
    if (count > 0) {
        // 记录日志
        onLogMessage("导出 " + QString::number(count) + " 个模板", "模板管理");

        QMessageBox::information(this, "导出模板", "成功导出 " + QString::number(count) + " 个模板。");
    } else {
        QMessageBox::warning(this, "导出模板", "导出失败或没有可导出的模板。");
    }
}

void MainWindow::saveTemplates() {
    // 获取保存路径
    QString templateCSVPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/templates.csv";

    // 确保目录存在
    QDir dir(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation));
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    // 保存模板
    int count = templateManager->saveToCSV(templateCSVPath);
    if (count > 0) {
        onLogMessage("已保存 " + QString::number(count) + " 个模板到默认位置", "模板管理");
        QMessageBox::information(this, "保存成功", "已成功保存 " + QString::number(count) + " 个模板到默认位置");
    } else {
        onLogMessage("保存模板失败", "错误");
        QMessageBox::warning(this, "保存失败", "保存模板时出现错误，可能没有模板需要保存");
    }
}

// 系统设置实现
void MainWindow::onSystemSettings() {
    // 创建系统设置对话框
    SystemSettingsDialog dialog(this);

    // 设置当前配置
    dialog.setSettings(getSystemSettings());

    // 如果用户确认设置
    if (dialog.exec() == QDialog::Accepted) {
        // 获取新的设置
        SystemSettings newSettings = dialog.getSettings();

        // 保存设置
        saveSystemSettings(newSettings);

        // 应用设置
        applySystemSettings(newSettings);

        // 记录日志
        onLogMessage("系统设置已更新", "系统");
    }
}

SystemSettings MainWindow::getSystemSettings() {
    SystemSettings settings;

    // 从配置文件加载设置
    QSettings config(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/config.ini",
                     QSettings::IniFormat);

    // ABB机器人设置
    settings.abbPort = config.value("ABB/Port", 8080).toInt();

    // RFID设置
    settings.rfidIP = config.value("RFID/IPAddress", "*************").toString();
    settings.rfidPort = config.value("RFID/Port", 502).toInt();

    // 打粗设置
    settings.laserWorkDir = config.value("Laser/WorkDir",
                                         QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) +
                                         "/LaserWork").toString();

    // 系统设置
    settings.autoConnect = config.value("System/AutoConnect", false).toBool();
    settings.logLevel = config.value("System/LogLevel", "INFO").toString();

    // 鞋子路径编辑器设置
    settings.shoePathEditorPath = config.value("Paths/ShoePathEditorPath", "").toString();

    return settings;
}

void MainWindow::saveSystemSettings(const SystemSettings &settings) {
    // 保存设置到配置文件
    QSettings config(QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/config.ini",
                     QSettings::IniFormat);

    // ABB机器人设置
    config.setValue("ABB/Port", settings.abbPort);

    // RFID设置
    config.setValue("RFID/IPAddress", settings.rfidIP);
    config.setValue("RFID/Port", settings.rfidPort);

    // 打粗设置
    config.setValue("Laser/WorkDir", settings.laserWorkDir);

    // 系统设置
    config.setValue("System/AutoConnect", settings.autoConnect);
    config.setValue("System/LogLevel", settings.logLevel);

    // 鞋子路径编辑器设置
    config.setValue("Paths/ShoePathEditorPath", settings.shoePathEditorPath);

    // 立即保存配置
    config.sync();
}

void MainWindow::applySystemSettings(const SystemSettings &settings) {
    // 应用RFID设置
    if (rfidDriver->isConnected()) {
        // 如果已连接且IP或端口改变，需要重新连接
        if (rfidDriver->getIPAddress() != settings.rfidIP.toStdString() ||
            rfidDriver->getPort() != settings.rfidPort) {
            // 断开连接
            rfidDriver->disconnect();

            // 重新连接
            if (settings.autoConnect) {
                rfidDriver->connect(settings.rfidIP.toStdString(), settings.rfidPort);
                // 启动RFID定时读取
                startRFIDTimer();
            }
        }
    } else if (settings.autoConnect) {
        // 如果未连接且设置了自动连接，则连接
        rfidDriver->connect(settings.rfidIP.toStdString(), settings.rfidPort);
        // 启动RFID定时读取
        startRFIDTimer();
    }

    // 应用ABB设置
    if (abbDriver->IsRunning()) {
        // 如果已连接且端口改变，需要重新连接
        if (abbDriver->GetPort() != settings.abbPort) {
            // 断开连接
            abbDriver->Stop();

            // 重新连接
            if (settings.autoConnect) {
                abbDriver->Start(settings.abbPort);
            }
        }
    } else if (settings.autoConnect) {
        // 如果未连接且设置了自动连接，则连接
        abbDriver->Start(settings.abbPort);
    }

    // 应用激光打粗设置
    if (settings.autoConnect && !laserControl->IsConnected()) {
        // 如果设置了自动连接且未连接，则连接
        laserControl->Connect();
    }

    // 创建激光工作目录（如果不存在）
    QDir().mkpath(settings.laserWorkDir);

    // 应用UI更新
    refreshUIStyle();
}

// 日志处理实现
void MainWindow::onLogMessage(const QString &message, const QString &source) {
    // 获取当前时间
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");

    // 构建日志消息
    QString logMsg = QString("[%1] [%2] %3").arg(timestamp).arg(source).arg(message);

    // 添加到日志窗口
    if (logWidget) {
        logWidget->addLogMessage(logMsg, source);
    }

    // 写入日志文件
    if (logFile && logFile->isOpen()) {
        QTextStream stream(logFile.get());
        stream.setCodec("UTF-8");
        stream.setAutoDetectUnicode(true);
        stream.setGenerateByteOrderMark(false); // 不生成BOM标记
        stream << logMsg << "\n";
        stream.flush();
    }
}

// 刷新UI样式实现
void MainWindow::refreshUIStyle() {
    // 应用样式表到主窗口
    this->setStyleSheet(AppStyle::MainStyleSheet);
    this->style()->unpolish(this);
    this->style()->polish(this);
    this->update();

    // 应用样式到所有子控件
    QList<QWidget *> allWidgets = this->findChildren<QWidget *>();
    for (QWidget *widget: allWidgets) {
        widget->style()->unpolish(widget);
        widget->style()->polish(widget);
        widget->update();

        // 特别处理按钮，确保样式属性能够正确应用
        if (QPushButton *btn = qobject_cast<QPushButton *>(widget)) {
            // 强制刷新按钮状态
            btn->setDown(false);
            btn->setFocus();
        }
    }

    // 确保按钮焦点和状态正确更新
    QList<QPushButton *> buttons = this->findChildren<QPushButton *>();
    for (QPushButton *btn: buttons) {
        // 触发样式重新计算
        btn->setEnabled(btn->isEnabled());
    }

    // 更新统计图表样式
    if (statisticsChart) {
        statisticsChart->setTheme(QChart::ChartThemeLight);
        statisticsChart->setBackgroundBrush(QBrush(QColor("#ffffff")));
        statisticsChart->setTitleBrush(QBrush(QColor("#2980b9")));
        statisticsChart->setTitleFont(QFont("Microsoft YaHei", 12, QFont::Bold));
    }

    // 更新统计图表
    updateStatisticsChart();

    // 更新状态栏消息
    statusBar->showMessage("样式已更新", 3000);

    // 强制处理事件，确保样式立即应用
    QApplication::processEvents();
}

// 初始化并连接所有设备
void MainWindow::onInitializeAllDevices() {
    connectDevicesAsync();
}

// 更新状态栏中的注册状态
void MainWindow::updateLicenseStatus() {
}

// 检查注册状态
bool MainWindow::checkLicense() {
    bool canContinue = true;

    if (!licenseManager->isRegistered()) {
        if (!licenseManager->isTrialAvailable()) {
            // 试用期已结束，必须注册
            QMessageBox::warning(this, "注册提示", "软件试用期已结束，请注册后继续使用。");

            // 显示注册对话框
            RegisterDialog registerDialog(licenseManager.get(), this);
            if (registerDialog.exec() != QDialog::Accepted) {
                QMessageBox::critical(this, "注册失败", "软件未注册，部分功能将不可用。");
                canContinue = false;
            } else {
                // 更新状态栏
                updateLicenseStatus();
            }
        }
    }

    return canContinue;
}

// 显示注册对话框
void MainWindow::onRegisterSoftware() {
    RegisterDialog registerDialog(licenseManager.get(), this);
    if (registerDialog.exec() == QDialog::Accepted) {
        updateLicenseStatus();
        onLogMessage("软件注册状态已更新", "系统");
    }
}

// 显示许可证信息
void MainWindow::onShowLicenseInfo() {
    QString message;
    if (licenseManager->isRegistered()) {
        message = "软件已注册\n\n"
                  "有效期至: " + licenseManager->getExpirationDate().toString("yyyy-MM-dd") + "\n"
                  "机器码: " + licenseManager->getMachineCode();
    } else if (licenseManager->isTrialAvailable()) {
        message = "软件正在试用期\n\n"
                  "剩余试用天数: " + QString::number(licenseManager->getTrialDaysLeft()) + " 天\n"
                  "机器码: " + licenseManager->getMachineCode() + "\n\n"
                  "请在试用期结束前注册软件";
    } else {
        message = "软件试用期已结束\n\n"
                  "请注册软件以继续使用\n"
                  "机器码: " + licenseManager->getMachineCode();
    }

    QMessageBox::information(this, "许可证信息", message);
}

// 在MainWindow类的实现部分添加新函数
void MainWindow::onOpenShoePathEditor() {
    // 从系统设置中获取编辑器路径
    SystemSettings settings = getSystemSettings();
    QString editorPath = settings.shoePathEditorPath;

    // 如果路径为空或文件不存在，则引导用户选择
    QFileInfo editorInfo(editorPath);
    if (editorPath.isEmpty() || !editorInfo.exists()) {
        QMessageBox::information(this, "选择编辑器",
                                 "请选择鞋子路径编辑软件的可执行文件\n\n"
                                 "此软件用于编辑鞋子的轮廓路径，通常由供应商提供");

        // 让用户选择路径
        editorPath = QFileDialog::getOpenFileName(this, "选择鞋子路径编辑软件",
                                                  QDir::rootPath(),
                                                  "可执行文件 (*.exe);;所有文件 (*)");

        if (editorPath.isEmpty()) {
            return;
        }

        // 更新设置
        settings.shoePathEditorPath = editorPath;
        saveSystemSettings(settings);
    }

    // 启动外部编辑器程序
    QProcess *process = new QProcess(this);
    if (!process->startDetached(editorPath)) {
        onLogMessage("启动鞋子路径编辑软件失败: " + editorPath, "错误");
        QMessageBox::critical(this, "启动失败", "无法启动鞋子路径编辑软件。");
        delete process;
        return;
    }

    onLogMessage("已启动鞋子路径编辑软件: " + editorPath, "系统");
}

/**
 * @brief 验证RFID编码是否符合规则
 * @param rfidCode RFID编码字符串
 * @return 如果编码有效返回true，否则返回false
 */
bool MainWindow::isValidRFIDCode(const QString &rfidCode) {
    // 检查编码是否为空
    if (rfidCode.isEmpty()) {
        return false;
    }

    // 检查编码长度是否为9位
    if (rfidCode.length() != 9) {
        return false;
    }

    bool ok;

    // 解析模具楦头号（第1位）
    int moldHeadNumber = rfidCode.mid(0, 1).toInt(&ok);
    if (!ok) return false;

    // 解析模具编号（第2-5位）
    int moldNumber = rfidCode.mid(1, 4).toInt(&ok);
    if (!ok) return false;

    // 解析尺码（第6-7位）
    int shoeSize = rfidCode.mid(5, 2).toInt(&ok);
    if (!ok || shoeSize < 16 || shoeSize > 47) return false;

    // 解析尺码序号（第8位）
    int sizeSequence = rfidCode.mid(7, 1).toInt(&ok);
    if (!ok) return false;

    // 解析左右脚（第9位）
    int footSide = rfidCode.mid(8, 1).toInt(&ok);
    if (!ok || (footSide != 1 && footSide != 2)) return false;


    return true;
}

/**
 * @brief 启动RFID定时器
 */
void MainWindow::startRFIDTimer() {
    // 停止现有的定时器
    if (rfidTimer && rfidTimer->isActive()) {
        rfidTimer->stop();
    }
    
    // 确保定时器已创建并连接信号（只在首次创建时连接）
    if (!rfidTimer) {
        rfidTimer = new QTimer(this);
        connect(rfidTimer, &QTimer::timeout, this, &MainWindow::handleRFIDRequest);
    }
    
    // 启动定时器，每300ms执行一次
    rfidTimer->start(300);
    
    onLogMessage("RFID定时读取已启动", "系统");
}

/**
 * @brief 停止RFID定时器
 */
void MainWindow::stopRFIDTimer() {
    if (rfidTimer && rfidTimer->isActive()) {
        rfidTimer->stop();
        onLogMessage("RFID定时读取已停止", "系统");
    }
}

/**
 * @brief 初始化统计管理器
 */
void MainWindow::initializeStatisticsManager() {
    // 创建统计管理器
    statisticsManager = std::make_shared<ProductionStatisticsManager>(this);

    // 设置数据目录
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/Statistics";
    onLogMessage(dataDir, "系统");

    if (statisticsManager->initialize(dataDir)) {
        onLogMessage("统计管理器初始化成功", "系统");

        // 创建统计导出器
        statisticsExporter = std::make_shared<StatisticsExporter>(statisticsManager, this);

        // 连接统计管理器信号
        connect(statisticsManager.get(), &ProductionStatisticsManager::recordStarted,
                this, &MainWindow::onStatisticsRecordStarted);
        connect(statisticsManager.get(), &ProductionStatisticsManager::recordFinished,
                this, &MainWindow::onStatisticsRecordFinished);
        connect(statisticsManager.get(), &ProductionStatisticsManager::statisticsUpdated,
                this, &MainWindow::updateStatistics);

        // 启动实时监控，降低频率以减少系统负载
        statisticsManager->startRealTimeMonitoring(30000); // 每30秒更新一次

        // 创建详细报表组件并替换占位符
        initializeDetailedReportWidget();
    } else {
        onLogMessage("统计管理器初始化失败", "错误");
    }
}

/**
 * @brief 初始化详细报表组件
 */
void MainWindow::initializeDetailedReportWidget() {
    onLogMessage("开始初始化详细报表组件", "系统");

    if (!statisticsManager) {
        onLogMessage("统计管理器未初始化，无法创建详细报表组件", "错误");
        return;
    }

    try {
        // 创建详细报表组件
        onLogMessage("正在创建详细报表组件...", "系统");
        detailedReportWidget = new DetailedReportWidget(statisticsManager, this);
        onLogMessage("详细报表组件创建成功", "系统");

        // 连接信号
        connect(detailedReportWidget, &DetailedReportWidget::dataLoaded,
                this, [this](int recordCount) {
                    onLogMessage(QString("详细报表加载了 %1 条记录").arg(recordCount), "统计");
                });

        connect(detailedReportWidget, &DetailedReportWidget::errorOccurred,
                this, [this](const QString& error) {
                    onLogMessage(QString("详细报表错误: %1").arg(error), "错误");
                });

        // 找到统计页面的分割器并替换占位符
        onLogMessage(QString("主区域堆栈有 %1 个页面").arg(mainAreaStack->count()), "系统");
        QWidget* statsPage = mainAreaStack->widget(2); // 统计页面是第3个页面 (索引2)
        if (statsPage) {
            onLogMessage("找到统计页面", "系统");
            QSplitter* splitter = statsPage->findChild<QSplitter*>();
            if (splitter) {
                onLogMessage(QString("找到分割器，有 %1 个子组件").arg(splitter->count()), "系统");
                if (splitter->count() >= 2) {
                    // 删除占位符
                    QWidget* placeholder = splitter->widget(1);
                    if (placeholder) {
                        onLogMessage("删除占位符组件", "系统");
                        placeholder->deleteLater();
                    }

                    // 添加详细报表组件
                    onLogMessage("添加详细报表组件到分割器", "系统");

                    splitter->addWidget(detailedReportWidget);

                    // 设置分割器比例
                    splitter->setStretchFactor(0, 1);
                    splitter->setStretchFactor(1, 2);

                    // 延迟设置分割器大小，等界面完全加载后
                    QTimer::singleShot(500, [this, splitter]() {
                        // 获取分割器的父窗口大小
                        QWidget* parentWidget = splitter->parentWidget();
                        if (parentWidget) {
                            int parentWidth = parentWidget->width();
                            onLogMessage(QString("父窗口宽度: %1").arg(parentWidth), "系统");

                            if (parentWidth > 200) {
                                int chartWidth = parentWidth / 3;
                                int reportWidth = parentWidth * 2 / 3;
                                splitter->setSizes({chartWidth, reportWidth});
                                onLogMessage(QString("设置分割器大小: [%1, %2]")
                                            .arg(chartWidth).arg(reportWidth), "系统");
                            } else {
                                // 如果父窗口还没有合适的大小，再延迟一点
                                QTimer::singleShot(1000, [this, splitter]() {
                                    splitter->setSizes({400, 800});
                                    onLogMessage("使用默认分割器大小: [400, 800]", "系统");
                                });
                            }
                        }

                        // 强制更新布局
                        splitter->update();
                        if (detailedReportWidget) {
                            detailedReportWidget->update();
                            onLogMessage(QString("详细报表组件最终大小: %1x%2")
                                        .arg(detailedReportWidget->width())
                                        .arg(detailedReportWidget->height()), "系统");
                        }
                    });

                    onLogMessage("详细报表组件已集成到统计界面", "系统");

                    // 自动加载初始数据
                    QTimer::singleShot(1000, detailedReportWidget, &DetailedReportWidget::refreshData);
                } else {
                    onLogMessage(QString("分割器子组件数量不足: %1").arg(splitter->count()), "错误");
                }
            } else {
                onLogMessage("未找到分割器组件", "错误");
            }
        } else {
            onLogMessage("未找到统计页面", "错误");
        }

    } catch (const std::exception& e) {
        onLogMessage(QString("创建详细报表组件失败: %1").arg(e.what()), "错误");
    } catch (...) {
        onLogMessage("创建详细报表组件时发生未知错误", "错误");
    }
}





/**
 * @brief 统计记录开始回调
 */
void MainWindow::onStatisticsRecordStarted(const QString& recordId) {
    currentProductionRecordId = recordId;
    onLogMessage(QString("开始记录生产统计: %1").arg(recordId), "统计");
}

/**
 * @brief 统计记录完成回调
 */
void MainWindow::onStatisticsRecordFinished(const QString& recordId) {
    if (currentProductionRecordId == recordId) {
        currentProductionRecordId.clear();
    }
    onLogMessage(QString("完成生产统计记录: %1").arg(recordId), "统计");
}
