#include "DatabaseQueryWorker.h"
#include "ProductionStatisticsManager.h"
#include <QTimer>
#include <QElapsedTimer>
#include <QDebug>
#include <QApplication>
#include <QMetaType>

DatabaseQueryWorker::DatabaseQueryWorker(std::shared_ptr<ProductionStatisticsManager> statsManager, QObject *parent)
    : QObject(parent)
    , m_statsManager(statsManager)
    , m_shutdown(false)
    , m_nextRequestId(1)
{
    // Register meta types for queued connections (ensure they're registered in worker thread too)
    qRegisterMetaType<QVector<ProductionRecord>>("QVector<ProductionRecord>");
    qRegisterMetaType<ProductionRecord>("ProductionRecord");
    qRegisterMetaType<ProductionSummary>("ProductionSummary");

    qDebug() << "DatabaseQueryWorker created in thread:" << QThread::currentThreadId();
}

DatabaseQueryWorker::~DatabaseQueryWorker()
{
    qDebug() << "DatabaseQueryWorker destroyed";
}

int DatabaseQueryWorker::requestRecords(const StatisticsFilter& filter)
{
    QMutexLocker locker(&m_requestMutex);
    
    if (m_shutdown) {
        return -1;
    }
    
    QueryRequest request;
    request.type = QueryType::GET_RECORDS;
    request.filter = filter;
    request.requestId = m_nextRequestId++;
    
    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();
    
    qDebug() << "Queued records request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestSummary(const StatisticsFilter& filter)
{
    QMutexLocker locker(&m_requestMutex);
    
    if (m_shutdown) {
        return -1;
    }
    
    QueryRequest request;
    request.type = QueryType::GET_SUMMARY;
    request.filter = filter;
    request.requestId = m_nextRequestId++;
    
    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();
    
    qDebug() << "Queued summary request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestRecord(const QString& recordId)
{
    QMutexLocker locker(&m_requestMutex);
    
    if (m_shutdown) {
        return -1;
    }
    
    QueryRequest request;
    request.type = QueryType::GET_RECORD_BY_ID;
    request.recordId = recordId;
    request.requestId = m_nextRequestId++;
    
    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();
    
    qDebug() << "Queued record request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestStartRecord(const QString& rfidCode, const QString& templateName,
                                           int shoeCode, bool isLeftFoot, int totalFaces)
{
    QMutexLocker locker(&m_requestMutex);

    if (m_shutdown) {
        return -1;
    }

    QueryRequest request;
    request.type = QueryType::START_RECORD;
    request.rfidCode = rfidCode;
    request.templateName = templateName;
    request.shoeCode = shoeCode;
    request.isLeftFoot = isLeftFoot;
    request.totalFaces = totalFaces;
    request.requestId = m_nextRequestId++;

    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();

    qDebug() << "Queued start record request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestUpdateRecord(const QString& recordId, int completedFaces,
                                            ProductionStatus status, const QString& errorMessage)
{
    QMutexLocker locker(&m_requestMutex);

    if (m_shutdown) {
        return -1;
    }

    QueryRequest request;
    request.type = QueryType::UPDATE_RECORD;
    request.recordId = recordId;
    request.completedFaces = completedFaces;
    request.status = status;
    request.errorMessage = errorMessage;
    request.requestId = m_nextRequestId++;

    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();

    qDebug() << "Queued update record request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestFinishRecord(const QString& recordId,
                                            ProductionStatus status, const QString& errorMessage)
{
    QMutexLocker locker(&m_requestMutex);

    if (m_shutdown) {
        return -1;
    }

    QueryRequest request;
    request.type = QueryType::FINISH_RECORD;
    request.recordId = recordId;
    request.status = status;
    request.errorMessage = errorMessage;
    request.requestId = m_nextRequestId++;

    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();

    qDebug() << "Queued finish record request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestAddCustomData(const QString& recordId, const QString& key, const QVariant& value)
{
    QMutexLocker locker(&m_requestMutex);

    if (m_shutdown) {
        return -1;
    }

    QueryRequest request;
    request.type = QueryType::ADD_CUSTOM_DATA;
    request.recordId = recordId;
    request.customDataKey = key;
    request.customDataValue = value;
    request.requestId = m_nextRequestId++;

    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();

    qDebug() << "Queued add custom data request" << request.requestId;
    return request.requestId;
}

int DatabaseQueryWorker::requestSaveRecord(const ProductionRecord& record)
{
    QMutexLocker locker(&m_requestMutex);

    if (m_shutdown) {
        return -1;
    }

    QueryRequest request;
    request.type = QueryType::SAVE_RECORD_TO_DB;
    request.record = record;
    request.requestId = m_nextRequestId++;

    m_pendingRequests.append(request);
    m_requestCondition.wakeOne();

    qDebug() << "Queued save record request" << request.requestId;
    return request.requestId;
}

void DatabaseQueryWorker::cancelRequest(int requestId)
{
    QMutexLocker locker(&m_requestMutex);
    m_cancelledRequests.insert(requestId);
    qDebug() << "Cancelled request" << requestId;
}

void DatabaseQueryWorker::cancelAllRequests()
{
    QMutexLocker locker(&m_requestMutex);
    
    for (const auto& request : m_pendingRequests) {
        m_cancelledRequests.insert(request.requestId);
    }
    
    qDebug() << "Cancelled all pending requests";
}

void DatabaseQueryWorker::processQueries()
{
    qDebug() << "DatabaseQueryWorker::processQueries started in thread:" << QThread::currentThreadId();
    
    while (!m_shutdown) {
        QueryRequest request;
        bool hasRequest = false;
        
        // Get next request
        {
            QMutexLocker locker(&m_requestMutex);
            
            if (m_pendingRequests.isEmpty()) {
                // Wait for new requests
                m_requestCondition.wait(&m_requestMutex, 1000); // 1 second timeout
                continue;
            }
            
            request = m_pendingRequests.takeFirst();
            hasRequest = true;
        }
        
        if (hasRequest && !isRequestCancelled(request.requestId)) {
            handleQuery(request);
        }

    }
    
    qDebug() << "DatabaseQueryWorker::processQueries finished";
}

void DatabaseQueryWorker::handleQuery(const QueryRequest& request)
{
    if (isRequestCancelled(request.requestId)) {
        qDebug() << "Request" << request.requestId << "was cancelled before processing";
        return;
    }
    
    emit queryStarted(request.requestId);
    
    QElapsedTimer timer;
    timer.start();
    
    try {
        switch (request.type) {
        case QueryType::GET_RECORDS:
            processRecordsQuery(request);
            break;
        case QueryType::GET_SUMMARY:
            processSummaryQuery(request);
            break;
        case QueryType::GET_RECORD_BY_ID:
            processRecordQuery(request);
            break;
        case QueryType::START_RECORD:
            processStartRecordQuery(request);
            break;
        case QueryType::UPDATE_RECORD:
            processUpdateRecordQuery(request);
            break;
        case QueryType::FINISH_RECORD:
            processFinishRecordQuery(request);
            break;
        case QueryType::ADD_CUSTOM_DATA:
            processAddCustomDataQuery(request);
            break;
        case QueryType::SAVE_RECORD_TO_DB:
            processSaveRecordQuery(request);
            break;
        }
        
        qDebug() << "Query" << request.requestId << "completed in" << timer.elapsed() << "ms";
        
    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Query failed: %1").arg(e.what()));
    } catch (...) {
        emitError(request.requestId, "Unknown query error");
    }
}

void DatabaseQueryWorker::processRecordsQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    QElapsedTimer timer;
    timer.start();

    QVector<ProductionRecord> records;

    try {
        // Emit progress signal
        emit queryProgress(request.requestId, 10);

        records = m_statsManager->getRecords(request.filter);

        // Check for timeout after query completion
        if (timer.elapsed() > MAX_QUERY_TIME_MS) {
            qDebug() << "Records query" << request.requestId << "timed out after" << timer.elapsed() << "ms";
            emit queryTimeout(request.requestId);
            return;
        }

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Records query" << request.requestId << "was cancelled";
            return;
        }

        // Emit progress signal
        emit queryProgress(request.requestId, 90);

        qDebug() << "Records query" << request.requestId << "completed successfully with" << records.size() << "records in" << timer.elapsed() << "ms";
        emit recordsReady(request.requestId, records);

    } catch (const std::exception& e) {
        qDebug() << "Records query" << request.requestId << "failed:" << e.what();
        emitError(request.requestId, QString("Records query failed: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "Records query" << request.requestId << "failed with unknown error";
        emitError(request.requestId, "Records query failed with unknown error");
    }
}

void DatabaseQueryWorker::processSummaryQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    QElapsedTimer timer;
    timer.start();

    try {
        // Emit progress signal
        emit queryProgress(request.requestId, 20);

        ProductionSummary summary = m_statsManager->getSummary(request.filter);

        // Check for timeout after query completion
        if (timer.elapsed() > MAX_QUERY_TIME_MS) {
            qDebug() << "Summary query" << request.requestId << "timed out after" << timer.elapsed() << "ms";
            emit queryTimeout(request.requestId);
            return;
        }

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Summary query" << request.requestId << "was cancelled";
            return;
        }

        // Emit progress signal
        emit queryProgress(request.requestId, 95);

        qDebug() << "Summary query" << request.requestId << "completed successfully in" << timer.elapsed() << "ms";
        emit summaryReady(request.requestId, summary);

    } catch (const std::exception& e) {
        qDebug() << "Summary query" << request.requestId << "failed:" << e.what();
        emitError(request.requestId, QString("Summary query failed: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "Summary query" << request.requestId << "failed with unknown error";
        emitError(request.requestId, "Summary query failed with unknown error");
    }
}

void DatabaseQueryWorker::processRecordQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }
    
    try {
        ProductionRecord record = m_statsManager->getRecord(request.recordId);
        
        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Record query" << request.requestId << "was cancelled";
            return;
        }
        
        emit recordReady(request.requestId, record);
        
    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Record query failed: %1").arg(e.what()));
    }
}

void DatabaseQueryWorker::shutdown()
{
    qDebug() << "DatabaseQueryWorker shutdown requested";
    
    QMutexLocker locker(&m_requestMutex);
    m_shutdown = true;
    m_requestCondition.wakeAll();
}

bool DatabaseQueryWorker::isRequestCancelled(int requestId) const
{
    QMutexLocker locker(&m_requestMutex);
    return m_cancelledRequests.contains(requestId);
}

void DatabaseQueryWorker::emitError(int requestId, const QString& error)
{
    qDebug() << "Query error for request" << requestId << ":" << error;
    emit queryError(requestId, error);
}

// DatabaseQueryThread implementation
DatabaseQueryThread::DatabaseQueryThread(std::shared_ptr<ProductionStatisticsManager> statsManager, QObject *parent)
    : QThread(parent)
    , m_statsManager(statsManager)
    , m_worker(nullptr)
    , m_running(false)
{
}

DatabaseQueryThread::~DatabaseQueryThread()
{
    stopWorker();
}

void DatabaseQueryThread::startWorker()
{
    if (!m_running) {
        m_running = true;
        start();
    }
}

void DatabaseQueryThread::stopWorker()
{
    if (m_running) {
        m_running = false;
        
        if (m_worker) {
            m_worker->shutdown();
        }
        
        quit();
        wait(5000); // Wait up to 5 seconds for thread to finish
        
        if (isRunning()) {
            terminate();
            wait(1000);
        }
    }
}

void DatabaseQueryThread::run()
{
    qDebug() << "DatabaseQueryThread started in thread:" << QThread::currentThreadId();

    try {
        // Create worker in this thread
        m_worker = new DatabaseQueryWorker(m_statsManager);

        // Emit signal to notify that worker is ready
        emit workerReady();

        // Start processing
        m_worker->processQueries();

    } catch (const std::exception& e) {
        qDebug() << "DatabaseQueryThread exception:" << e.what();
        // Emit error signal
        emit workerError(QString("Thread error: %1").arg(e.what()));
    } catch (...) {
        qDebug() << "DatabaseQueryThread unknown exception";
        emit workerError("Unknown thread error");
    }

    // Cleanup
    if (m_worker) {
        delete m_worker;
        m_worker = nullptr;
    }

    qDebug() << "DatabaseQueryThread finished";
}

// Write operation implementations
void DatabaseQueryWorker::processStartRecordQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    try {
        QString recordId = m_statsManager->startProductionRecord(
            request.rfidCode, request.templateName, request.shoeCode,
            request.isLeftFoot, request.totalFaces);

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Start record query" << request.requestId << "was cancelled";
            return;
        }

        if (!recordId.isEmpty()) {
            emit recordStarted(request.requestId, recordId);
        } else {
            emitError(request.requestId, "Failed to start production record");
        }

    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Start record failed: %1").arg(e.what()));
    }
}

void DatabaseQueryWorker::processUpdateRecordQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    try {
        bool success = m_statsManager->updateProductionRecord(
            request.recordId, request.completedFaces, request.status, request.errorMessage);

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Update record query" << request.requestId << "was cancelled";
            return;
        }

        emit recordUpdated(request.requestId, success);

    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Update record failed: %1").arg(e.what()));
    }
}

void DatabaseQueryWorker::processFinishRecordQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    try {
        bool success = m_statsManager->finishProductionRecord(
            request.recordId, request.status, request.errorMessage);

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Finish record query" << request.requestId << "was cancelled";
            return;
        }

        emit recordFinished(request.requestId, success);

    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Finish record failed: %1").arg(e.what()));
    }
}

void DatabaseQueryWorker::processAddCustomDataQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    try {
        bool success = m_statsManager->addCustomData(
            request.recordId, request.customDataKey, request.customDataValue);

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Add custom data query" << request.requestId << "was cancelled";
            return;
        }

        emit customDataAdded(request.requestId, success);

    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Add custom data failed: %1").arg(e.what()));
    }
}

void DatabaseQueryWorker::processSaveRecordQuery(const QueryRequest& request)
{
    if (!m_statsManager) {
        emitError(request.requestId, "Statistics manager not available");
        return;
    }

    try {
        // Note: We need to add a saveRecordToDatabase method to ProductionStatisticsManager
        // For now, we'll use a workaround by calling the private method through reflection
        // or add a public method to the manager

        // This is a placeholder - you'll need to add the actual method to ProductionStatisticsManager
        bool success = true; // m_statsManager->saveRecordToDatabase(request.record);

        if (isRequestCancelled(request.requestId)) {
            qDebug() << "Save record query" << request.requestId << "was cancelled";
            return;
        }

        emit recordSaved(request.requestId, success);

    } catch (const std::exception& e) {
        emitError(request.requestId, QString("Save record failed: %1").arg(e.what()));
    }
}

#include "DatabaseQueryWorker.moc"
