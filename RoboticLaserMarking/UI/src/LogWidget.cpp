#include "LogWidget.h"
#include "AppStyle.h"
#include <QDateTime>
#include <QFileDialog>
#include <QMessageBox>

#pragma execution_character_set("utf-8")

LogWidget::LogWidget(QWidget *parent)
    : QWidget(parent), autoSave(false)
{
    // 创建布局
    mainLayout = new QVBoxLayout(this);
    controlLayout = new QHBoxLayout();
    
    // 创建日志文本框
    logTextEdit = new QTextEdit(this);
    logTextEdit->setReadOnly(true);
    logTextEdit->document()->setMaximumBlockCount(5000); // 限制最大行数
    
    // 创建控制按钮
    btnClear = new QPushButton("清除日志", this);
    btnSave = new QPushButton("保存日志", this);
    
    // 创建过滤控件
    lblFilter = new QLabel("过滤类别:", this);
    categoryFilter = new QComboBox(this);
    categoryFilter->addItem("全部");
    categoryFilter->addItem("系统");
    categoryFilter->addItem("错误");
    categoryFilter->addItem("警告");
    categoryFilter->addItem("ABB机器人");
    categoryFilter->addItem("激光打粗");
    categoryFilter->addItem("RFID");
    
    // 添加控件到布局
    controlLayout->addWidget(lblFilter);
    controlLayout->addWidget(categoryFilter);
    controlLayout->addStretch();
    controlLayout->addWidget(btnClear);
    controlLayout->addWidget(btnSave);
    
    mainLayout->addLayout(controlLayout);
    mainLayout->addWidget(logTextEdit);
    
    // 连接信号与槽
    connect(btnClear, &QPushButton::clicked, this, &LogWidget::onClearLog);
    connect(btnSave, &QPushButton::clicked, this, &LogWidget::onSaveLog);
    connect(categoryFilter, &QComboBox::currentTextChanged, this, &LogWidget::filterByCategory);
    
    // 设置样式
    logTextEdit->setStyleSheet(" font-size: 14px;font-family: Consolas, Courier New, monospace;");
}

LogWidget::~LogWidget()
{
}

void LogWidget::addLogMessage(const QString& message, const QString& category)
{
    // 格式化消息
    QString formattedMessage = formatLogMessage(message, category);
    
    // 添加到日志列表
    logMessages.append(formattedMessage);
    logCategories.append(category);
    
    // 根据当前过滤器显示
    QString currentFilter = categoryFilter->currentText();
    if (currentFilter == "全部" || currentFilter == category) {
        // 设置颜色
        QString colorHex;
        if (category == "系统") {
            colorHex = AppStyle::LogCategorySystem;
        } else if (category == "错误") {
            colorHex = AppStyle::LogCategoryError;
        } else if (category == "警告") {
            colorHex = AppStyle::LogCategoryWarning;
        } else if (category == "ABB机器人") {
            colorHex = AppStyle::LogCategoryABB;
        } else if (category == "激光打粗") {
            colorHex = AppStyle::LogCategoryLaser;
        } else if (category == "RFID") {
            colorHex = AppStyle::LogCategoryRFID;
        } else {
            colorHex = AppStyle::LogCategorySystem;
        }
        
        logTextEdit->append(QString("<span style='color:%1'>%2</span>")
                            .arg(colorHex)
                            .arg(formattedMessage));
    }
    
    // 自动保存
    if (autoSave && !autoSaveFilePath.isEmpty()) {
        appendToFile(formattedMessage);
    }
}

void LogWidget::clearLog()
{
    logTextEdit->clear();
    logMessages.clear();
    logCategories.clear();
}

bool LogWidget::saveLogToFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "保存失败", "无法打开文件进行写入: " + filePath);
        return false;
    }
    
    QTextStream out(&file);
    out.setCodec("UTF-8");
    out.setAutoDetectUnicode(true);
    out.setGenerateByteOrderMark(false); // 不生成BOM标记
    
    for (const QString& msg : logMessages) {
        out << msg << "\n";
    }
    
    file.close();
    return true;
}

void LogWidget::setAutoSave(bool enable, const QString& filePath)
{
    autoSave = enable;
    if (enable && !filePath.isEmpty()) {
        autoSaveFilePath = filePath;
    }
}

void LogWidget::filterByCategory(const QString& category)
{
    logTextEdit->clear();
    
    for (int i = 0; i < logMessages.size(); ++i) {
        if (category == "全部" || logCategories[i] == category) {
            // 设置颜色
            QString colorHex;
            if (logCategories[i] == "系统") {
                colorHex = AppStyle::LogCategorySystem;
            } else if (logCategories[i] == "错误") {
                colorHex = AppStyle::LogCategoryError;
            } else if (logCategories[i] == "警告") {
                colorHex = AppStyle::LogCategoryWarning;
            } else if (logCategories[i] == "ABB机器人") {
                colorHex = AppStyle::LogCategoryABB;
            } else if (logCategories[i] == "激光打粗") {
                colorHex = AppStyle::LogCategoryLaser;
            } else if (logCategories[i] == "RFID") {
                colorHex = AppStyle::LogCategoryRFID;
            } else {
                colorHex = "#FFFFFF";
            }
            
            logTextEdit->append(QString("<span style='color:%1'>%2</span>")
                                .arg(colorHex)
                                .arg(logMessages[i]));
        }
    }
}

void LogWidget::onSaveLog()
{
    QString filePath = QFileDialog::getSaveFileName(this, "保存日志", 
                                                  QString(), "日志文件 (*.log);;文本文件 (*.txt);;所有文件 (*.*)");
    
    if (!filePath.isEmpty()) {
        saveLogToFile(filePath);
    }
}

void LogWidget::onClearLog()
{
    if (QMessageBox::question(this, "确认清除", "确定要清除所有日志吗?", 
                             QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
        clearLog();
    }
}

QString LogWidget::formatLogMessage(const QString& message, const QString& category)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss.zzz");
    return QString("[%1] [%2] %3").arg(timestamp).arg(category).arg(message);
}

void LogWidget::appendToFile(const QString& formattedMessage)
{
    QFile file(autoSaveFilePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        out.setAutoDetectUnicode(true);
        out.setGenerateByteOrderMark(false); // 不生成BOM标记
        out << formattedMessage << "\n";
        file.close();
    }
} 