#include "DeviceStatusWidget.h"

#include <QApplication>
#include <QDebug>
#include "AppStyle.h"
#include <QDateTime>

#pragma execution_character_set("utf-8")

DeviceStatusWidget::DeviceStatusWidget(QWidget *parent)
    : QWidget(parent),
      blinkState(false)
{
    // 创建布局
    mainLayout = new QGridLayout(this);
    
    // 创建设备状态组
    deviceGroup = new QGroupBox("设备状态", this);
    QGridLayout* deviceLayout = new QGridLayout(deviceGroup);
    
    // 创建状态指示器
    abbStatusIndicator = createStatusIndicator();
    laserStatusIndicator = createStatusIndicator();
    rfidStatusIndicator = createStatusIndicator();
    systemStatusIndicator = createStatusIndicator();
    
    // 创建状态标签
    lblABBStatus = new QLabel("ABB机器人: 未连接", deviceGroup);
    lblLaserStatus = new QLabel("激光器: 未连接", deviceGroup);
    lblRFIDStatus = new QLabel("RFID设备: 未连接", deviceGroup);
    lblSystemState = new QLabel("系统状态: 已停止", deviceGroup);
    
    // 设置设备状态标签样式 - 更大更粗的字体
    QString deviceStatusStyle = "font-size: 18px; font-weight: bold; color: #2c3e50; padding: 2px;";
    lblABBStatus->setStyleSheet(deviceStatusStyle);
    lblLaserStatus->setStyleSheet(deviceStatusStyle);
    lblRFIDStatus->setStyleSheet(deviceStatusStyle);
    lblSystemState->setStyleSheet(deviceStatusStyle);
    
    // 添加到设备布局
    deviceLayout->addWidget(abbStatusIndicator, 0, 0);
    deviceLayout->addWidget(lblABBStatus, 0, 1);
    deviceLayout->addWidget(laserStatusIndicator, 1, 0);
    deviceLayout->addWidget(lblLaserStatus, 1, 1);
    deviceLayout->addWidget(rfidStatusIndicator, 2, 0);
    deviceLayout->addWidget(lblRFIDStatus, 2, 1);
    deviceLayout->addWidget(systemStatusIndicator, 3, 0);
    deviceLayout->addWidget(lblSystemState, 3, 1);
    
    // 创建作业状态组
    jobGroup = new QGroupBox("作业状态", this);
    QGridLayout* jobLayout = new QGridLayout(jobGroup);
    
    // 创建作业标签
    lblLaserWorkingStatus = new QLabel("激光状态: 空闲", jobGroup);
    lblLaserWorkingStatus->hide();
    lblCurrentRFID = new QLabel("当前RFID: 无", jobGroup);
    lblCurrentTemplate = new QLabel("当前模板: 无", jobGroup);
    lblCurrentShoeInfo = new QLabel("当前鞋码: 无", jobGroup);
    lblCurrentFace = new QLabel("当前打粗面: 101", jobGroup);
    lblFaceProgress = new QLabel("打粗进度: 0/1 (已完成: 0, 剩余: 1)", jobGroup);
    
    // 设置作业状态标签样式 - 用户经常查看的重要信息
    QString jobStatusStyle = "font-size: 18px; font-weight: bold; color: #2c3e50; padding: 4px;";
    lblLaserWorkingStatus->setStyleSheet(jobStatusStyle);
    lblCurrentRFID->setStyleSheet(jobStatusStyle);
    lblCurrentTemplate->setStyleSheet(jobStatusStyle);
    lblCurrentShoeInfo->setStyleSheet(jobStatusStyle);
    lblCurrentFace->setStyleSheet(jobStatusStyle);
    lblFaceProgress->setStyleSheet(jobStatusStyle);
    
    // 添加到作业布局
    jobLayout->addWidget(lblLaserWorkingStatus, 0, 0, 1, 2);
    jobLayout->addWidget(lblCurrentRFID, 1, 0, 1, 2);
    jobLayout->addWidget(lblCurrentTemplate, 2, 0, 1, 2);
    jobLayout->addWidget(lblCurrentShoeInfo, 3, 0, 1, 2);
    jobLayout->addWidget(lblCurrentFace, 4, 0, 1, 2);
    jobLayout->addWidget(lblFaceProgress, 5, 0, 1, 2);
    

    
    // 创建统计组
    statisticsGroup = new QGroupBox("统计信息", this);
    QGridLayout* statsLayout = new QGridLayout(statisticsGroup);
    
    // 创建统计标签
    lblMarkTime = new QLabel("单个打粗时间: 0 ms", statisticsGroup);
    lblMarkCount = new QLabel("打粗数量: 0", statisticsGroup);
    
    // 设置统计标签样式 - 加粗和大字体
    QString statisticsStyle = "font-size: 18px; font-weight: bold; color: #2c3e50; padding: 4px;";
    lblMarkTime->setStyleSheet(statisticsStyle);
    lblMarkCount->setStyleSheet(statisticsStyle);
    
    // 添加到统计布局
    statsLayout->addWidget(lblMarkTime, 0, 0);
    statsLayout->addWidget(lblMarkCount, 1, 0);
    
    // 添加所有组到主布局
    mainLayout->addWidget(deviceGroup, 0, 0);
    mainLayout->addWidget(jobGroup, 1, 0);
    mainLayout->addWidget(statisticsGroup, 2, 0);


    QLabel* logoLabel = new QLabel(this);
    QString logoPath = QApplication::applicationDirPath() + "/LOGO2.png";
    QPixmap pixmap(logoPath);
    pixmap = pixmap.scaled(160, 80, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    logoLabel->setPixmap(pixmap);
    logoLabel->setAlignment(Qt::AlignCenter);
    logoLabel->setFixedSize(160, 80);



    // 创建标语标签
    QLabel* sloganLabel = new QLabel("     服务国防建设，装点百姓生活。", this);
    sloganLabel->setStyleSheet("font-size: 32px; font-weight: bold; color:rgb(218, 59, 59); padding: 8px;");
    sloganLabel->setAlignment(Qt::AlignCenter);
    
    // 创建垂直布局容器
    QVBoxLayout* verticalLayout = new QVBoxLayout();
    verticalLayout->addStretch();
    verticalLayout->addWidget(sloganLabel);
    verticalLayout->addStretch();
    
    // 创建水平布局容器使LOGO和标语居中
    QHBoxLayout* sloganLayout = new QHBoxLayout();
    sloganLayout->addStretch();
    sloganLayout->addWidget(logoLabel);
    sloganLayout->addLayout(verticalLayout);
    sloganLayout->addStretch();
    
    mainLayout->addLayout(sloganLayout, 3, 0);
    
    // mainLayout->setRowStretch(4, 1); // 底部添加弹性空间
    
    
    // 设置定时器用于状态更新 - 降低频率以减少CPU负载
    blinkTimer = new QTimer(this);
    connect(blinkTimer, &QTimer::timeout, this, &DeviceStatusWidget::updateUI);
    blinkTimer->start(2000); // 每2秒更新一次，减少CPU负载
    
    // 更新初始UI
    updateUI();
}

DeviceStatusWidget::~DeviceStatusWidget()
{
    // Safe cleanup
    if (blinkTimer) {
        blinkTimer->stop();
        blinkTimer->deleteLater();
        blinkTimer = nullptr;
    }
}

QFrame* DeviceStatusWidget::createStatusIndicator()
{
    QFrame* indicator = new QFrame(this);
    indicator->setStyleSheet(AppStyle::StatusIndicatorInactive);
    indicator->setFixedSize(24, 24);  // 增大指示器尺寸
    return indicator;
}

void DeviceStatusWidget::updateStatusIndicator(QFrame* indicator, bool active)
{
    // Add null pointer check
    if (!indicator) {
        qDebug() <<"WARNING: updateStatusIndicator called with null indicator";
        return;
    }

    try {
        indicator->setStyleSheet(active ? AppStyle::StatusIndicatorActive : AppStyle::StatusIndicatorInactive);
    } catch (...) {
        qDebug() << "ERROR: Exception in updateStatusIndicator";
    }
}

void DeviceStatusWidget::setABBConnected(bool connected)
{
    lblABBStatus->setText(QString("ABB机器人: %1").arg(connected ? "已连接" : "未连接"));
    updateStatusIndicator(abbStatusIndicator, connected);
}

void DeviceStatusWidget::setLaserConnected(bool connected)
{
    lblLaserStatus->setText(QString("激光器: %1").arg(connected ? "已连接" : "未连接"));
    updateStatusIndicator(laserStatusIndicator, connected);
}

void DeviceStatusWidget::setRFIDConnected(bool connected)
{
    lblRFIDStatus->setText(QString("RFID设备: %1").arg(connected ? "已连接" : "未连接"));
    updateStatusIndicator(rfidStatusIndicator, connected);
}



void DeviceStatusWidget::setCurrentRFID(const QString& rfidId)
{
    lblCurrentRFID->setText(QString("当前RFID: %1").arg(rfidId.isEmpty() ? "无" : rfidId));
}

void DeviceStatusWidget::setCurrentTemplate(const QString& templateName)
{
    lblCurrentTemplate->setText(QString("当前模板: %1").arg(templateName.isEmpty() ? "无" : templateName));
}

void DeviceStatusWidget::setCurrentShoeInfo(int shoeCode, bool isLeft)
{
    lblCurrentShoeInfo->setText(QString("当前鞋码: %1 (%2)")
                               .arg(shoeCode > 0 ? QString::number(shoeCode) : "无")
                               .arg(isLeft ? "左脚" : "右脚"));
}



void DeviceStatusWidget::setFaceProgress(int currentFace, int totalFaces, int completedFaces)
{
    // 更新当前面显示
    lblCurrentFace->setText(QString("当前面: %1").arg(currentFace));
    
    // 计算剩余面数
    int remainingFaces = totalFaces - completedFaces;
    
    // 更新面进度文本
    lblFaceProgress->setText(QString("面进度: %1/%2 (已完成: %3, 剩余: %4)")
                            .arg(completedFaces)
                            .arg(totalFaces)
                            .arg(completedFaces)
                            .arg(remainingFaces));
}

void DeviceStatusWidget::setMarkTime(uint64_t timeMs)
{
    lblMarkTime->setText(QString("打粗时间: %1 ms").arg(timeMs));
}

void DeviceStatusWidget::setMarkCount(int count)
{
    lblMarkCount->setText(QString("打粗数量: %1").arg(count));
}

void DeviceStatusWidget::setSystemState(const QString& state)
{
    lblSystemState->setText(QString("系统状态: %1").arg(state));
    // 更新系统状态指示器
    bool systemActive = (state == "运行中" || state == "已暂停");
    updateStatusIndicator(systemStatusIndicator, systemActive);
}

void DeviceStatusWidget::setSystemRunning(bool running)
{
    QString state = running ? "运行中" : "已停止";
    lblSystemState->setText(QString("系统状态: %1").arg(state));
    updateStatusIndicator(systemStatusIndicator, running);
}

void DeviceStatusWidget::setSystemPaused(bool paused)
{
    QString state = paused ? "已暂停" : "已停止";
    lblSystemState->setText(QString("系统状态: %1").arg(state));
    updateStatusIndicator(systemStatusIndicator, true); // 暂停状态仍然显示为激活
}

void DeviceStatusWidget::setRunTime(const QString& timeText)
{
    QLabel* lblRunTime = new QLabel(this);
    if (!statisticsGroup->layout()->findChild<QLabel*>("lblRunTime")) {
        lblRunTime->setObjectName("lblRunTime");
        lblRunTime->setText(QString("系统运行时间: %1").arg(timeText));
        // 设置运行时间标签样式 - 与其他统计标签保持一致
        QString statisticsStyle = "font-size: 18px; font-weight: bold; color: #2c3e50; padding: 4px;";
        lblRunTime->setStyleSheet(statisticsStyle);
        static_cast<QGridLayout*>(statisticsGroup->layout())->addWidget(lblRunTime, 2, 0);
    } else {
        lblRunTime = findChild<QLabel*>("lblRunTime");
        lblRunTime->setText(QString("运行时间: %1").arg(timeText));
    }
}

void DeviceStatusWidget::setABBPosition(const QString& position)
{
    // 从当前显示文本判断连接状态
    QString currentText = lblABBStatus->text();
    bool connected = currentText.contains("已连接");
    
    QString statusText = connected ? "已连接" : "未连接";
    if (!position.isEmpty() && connected) {
        statusText += " (位置: " + position + ")";
    }
    lblABBStatus->setText(QString("ABB机器人: %1").arg(statusText));
}

void DeviceStatusWidget::setABBStatus(const QString& status)
{
    // 从当前显示文本判断连接状态
    QString currentText = lblABBStatus->text();
    bool connected = currentText.contains("已连接");
    
    QString statusText = connected ? "已连接" : "未连接";
    if (!status.isEmpty() && connected) {
        statusText += " (" + status + ")";
    }
    lblABBStatus->setText(QString("ABB机器人: %1").arg(statusText));
}

void DeviceStatusWidget::setLaserError(bool hasError, const QString& errorMsg)
{
    // 从当前显示文本判断连接状态
    QString currentText = lblLaserStatus->text();
    bool connected = currentText.contains("已连接");
    
    QString statusText = connected ? "已连接" : "未连接";

    lblLaserStatus->setText(QString("激光器: %1").arg(statusText));
}

void DeviceStatusWidget::setRFIDStatus(bool isReading, const QString& lastTag)
{
    // 从当前显示文本判断连接状态
    QString currentText = lblRFIDStatus->text();
    bool connected = currentText.contains("已连接");
    
    QString statusText = connected ? "已连接" : "未连接";
    if (isReading) {
        statusText += " (读取中)";
    }
    if (!lastTag.isEmpty() && connected) {
        statusText += " [最近读取: " + lastTag + "]";
    }
    lblRFIDStatus->setText(QString("RFID设备: %1").arg(statusText));
}

void DeviceStatusWidget::setLaserWorkingStatus(bool isWorking, const QString& workingState)
{
    // 从激光器状态文本判断连接状态
    QString laserText = lblLaserStatus->text();
    bool connected = laserText.contains("已连接");
    
    QString statusText;
    if (!connected) {
        statusText = "未连接";
    } else if (isWorking) {
        statusText = workingState.isEmpty() ? "工作中" : workingState;
    } else {
        statusText = "空闲";
    }
    
    lblLaserWorkingStatus->setText(QString("激光状态: %1").arg(statusText));
}

void DeviceStatusWidget::updateUI()
{
    try {
        // Add null pointer checks for all labels
        if (!lblABBStatus || !lblLaserStatus || !lblRFIDStatus || !lblSystemState) {
            qDebug() << "ERROR: One or more status labels are null";
            return;
        }

        // 从当前显示文本判断连接状态并更新指示器
        bool abbConnected = lblABBStatus->text().contains("已连接");
        bool laserConnected = lblLaserStatus->text().contains("已连接");
        bool rfidConnected = lblRFIDStatus->text().contains("已连接");

        updateStatusIndicator(abbStatusIndicator, abbConnected);
        updateStatusIndicator(laserStatusIndicator, laserConnected);
        updateStatusIndicator(rfidStatusIndicator, rfidConnected);

        // 从系统状态文本判断是否激活
        QString systemText = lblSystemState->text();
        bool systemActive = (systemText.contains("运行中") || systemText.contains("已暂停"));
        updateStatusIndicator(systemStatusIndicator, systemActive);

        // 更新激光工作状态显示
        if (!laserConnected && lblLaserWorkingStatus) {
            lblLaserWorkingStatus->setText("激光状态: 未连接");
        }

    } catch (const std::exception& e) {
        qDebug() <<"ERROR in updateUI:" << e.what();
    } catch (...) {
        qDebug() << "ERROR: Unknown exception in updateUI";
    }
}