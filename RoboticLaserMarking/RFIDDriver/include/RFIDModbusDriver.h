//
// Created by xu260 on 2025/5/27.
//

#ifndef RFIDMODBUSDRIVER_H
#define RFIDMODBUSDRIVER_H

#include <string>
#include <modbus.h>

class RFIDModbusDriver {
private:
    modbus_t *ctx;
    int connected;
    std::string ipAddress;
    int port;
    bool reading;
    std::string lastTagID;

public:
    // 构造和析构函数
    RFIDModbusDriver();
    ~RFIDModbusDriver();
    
    // 连接和断开连接
    int connectTCP(const std::string& ip, int port);
    int connectRTU(const std::string& serialPort, int baudRate, char parity, int dataBit, int stopBit);
    void disconnect();
    
    // 读取寄存器
    int readHoldingRegisters(int addr, int nb, uint16_t *dest);
    int readInputRegisters(int addr, int nb, uint16_t *dest);
    
    // 写入寄存器
    int writeRegister(int addr, int value);
    int writeRegisters(int addr, int nb, const uint16_t *data);
    
    // 读取/写入线圈
    int readCoils(int addr, int nb, uint8_t *dest);
    int writeCoil(int addr, int status);
    
    // 设置超时时间
    void setTimeout(long sec, long usec);
    
    // 获取连接状态
    bool isConnected() const;
    
    // 新增方法
    int connect(const std::string& ip, int port) { return connectTCP(ip, port); }
    bool isReading() const { return reading; }
    std::string getLastTagID() const { return lastTagID; }
    std::string getIPAddress() const { return ipAddress; }
    int getPort() const { return port; }
};

#endif //RFIDMODBUSDRIVER_H