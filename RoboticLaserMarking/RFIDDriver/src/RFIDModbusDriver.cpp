//
// Created by xu260 on 2025/5/27.
//

#include "RFIDModbusDriver.h"
#include <iostream>

// 构造函数
RFIDModbusDriver::RFIDModbusDriver() {
    ctx = nullptr;
    connected = 0;
    ipAddress = "";
    port = 0;
    reading = false;
    lastTagID = "";
}

// 析构函数
RFIDModbusDriver::~RFIDModbusDriver() {
    disconnect();
}

// 连接到Modbus TCP设备
int RFIDModbusDriver::connectTCP(const std::string& ip, int port) {
    // 如果已经连接，先断开
    if (connected)
        disconnect();
    
    // 保存连接参数
    this->ipAddress = ip;
    this->port = port;
    
    // 创建TCP连接上下文
    ctx = modbus_new_tcp(ip.c_str(), port);
    if (ctx == nullptr) {
        std::cerr << "创建Modbus TCP连接失败: " << modbus_strerror(errno) << std::endl;
        return -1;
    }
    
    // 建立连接
    if (modbus_connect(ctx) == -1) {
        std::cerr << "Modbus TCP连接失败: " << modbus_strerror(errno) << std::endl;
        modbus_free(ctx);
        ctx = nullptr;
        return -1;
    }
    
    connected = 1;
    return 0;
}

// 连接到Modbus RTU设备
int RFIDModbusDriver::connectRTU(const std::string& serialPort, int baudRate, char parity, int dataBit, int stopBit) {
    // 如果已经连接，先断开
    if (connected)
        disconnect();
    
    // 创建RTU连接上下文
    ctx = modbus_new_rtu(serialPort.c_str(), baudRate, parity, dataBit, stopBit);
    if (ctx == nullptr) {
        std::cerr << "创建Modbus RTU连接失败: " << modbus_strerror(errno) << std::endl;
        return -1;
    }
    
    // 建立连接
    if (modbus_connect(ctx) == -1) {
        std::cerr << "Modbus RTU连接失败: " << modbus_strerror(errno) << std::endl;
        modbus_free(ctx);
        ctx = nullptr;
        return -1;
    }
    
    connected = 1;
    return 0;
}

// 断开连接
void RFIDModbusDriver::disconnect() {
    if (ctx != nullptr) {
        modbus_close(ctx);
        modbus_free(ctx);
        ctx = nullptr;
    }
    connected = 0;
}

// 读取保持寄存器
int RFIDModbusDriver::readHoldingRegisters(int addr, int nb, uint16_t *dest) {
    if (!connected || ctx == nullptr)
        return -1;
        
    return modbus_read_registers(ctx, addr, nb, dest);
}

// 读取输入寄存器
int RFIDModbusDriver::readInputRegisters(int addr, int nb, uint16_t *dest) {
    if (!connected || ctx == nullptr)
        return -1;
        
    return modbus_read_input_registers(ctx, addr, nb, dest);
}

// 写入单个寄存器
int RFIDModbusDriver::writeRegister(int addr, int value) {
    if (!connected || ctx == nullptr)
        return -1;
        
    return modbus_write_register(ctx, addr, value);
}

// 写入多个寄存器
int RFIDModbusDriver::writeRegisters(int addr, int nb, const uint16_t *data) {
    if (!connected || ctx == nullptr)
        return -1;
        
    return modbus_write_registers(ctx, addr, nb, data);
}

// 读取线圈
int RFIDModbusDriver::readCoils(int addr, int nb, uint8_t *dest) {
    if (!connected || ctx == nullptr)
        return -1;
        
    return modbus_read_bits(ctx, addr, nb, dest);
}

// 写入单个线圈
int RFIDModbusDriver::writeCoil(int addr, int status) {
    if (!connected || ctx == nullptr)
        return -1;
        
    return modbus_write_bit(ctx, addr, status);
}

// 设置超时
void RFIDModbusDriver::setTimeout(long sec, long usec) {
    if (ctx != nullptr) {
        modbus_set_response_timeout(ctx, sec,usec);
    }
}

// 检查是否已连接
bool RFIDModbusDriver::isConnected() const {
    return connected == 1;
}