#pragma once

#include <QMainWindow>
#include <QLineEdit>
#include <QLabel>
#include <QPushButton>
#include <QGridLayout>
#include <QDateEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QGroupBox>
#include <QClipboard>
#include <QMessageBox>
#include <QCryptographicHash>
#include <QSettings>
#include <QFile>
#include <QFileDialog>
#include <QTextStream>
#include <QDateTime>

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    // 生成注册码
    void onGenerateLicense();
    
    // 复制注册码
    void onCopyLicense();
    
    // 保存注册码
    void onSaveLicense();
    
    // 读取机器码
    void onLoadMachineCode();
    
    // 清除所有字段
    void onClear();
    
    // 验证注册码
    void onVerifyLicense();
    
    // 管理已生成的注册码
    void onManageLicenses();

private:
    // 初始化UI
    void setupUI();
    
    // 根据机器码生成注册码
    QString generateLicenseKey(const QString& machineCode, const QDate& expirationDate);
    
    // 保存注册记录
    void saveLicenseRecord(const QString& machineCode, const QString& licenseKey, 
                           const QDate& expirationDate, const QString& customerInfo);
    
    // 加载许可证记录
    void loadLicenseRecords();
    
    // UI控件
    QLineEdit* edtMachineCode;
    QLineEdit* edtLicenseKey;
    QDateEdit* dtpExpirationDate;
    QLineEdit* edtCustomerName;
    QLineEdit* edtCustomerEmail;
    QCheckBox* chkNeverExpire;
    
    QPushButton* btnGenerateLicense;
    QPushButton* btnCopyLicense;
    QPushButton* btnSaveLicense;
    QPushButton* btnLoadMachineCode;
    QPushButton* btnClear;
    QPushButton* btnVerifyLicense;
    QPushButton* btnManageLicenses;
    
    QGridLayout* mainLayout;
    QWidget* centralWidget;
    
    // 存储已生成的许可证记录
    QString licenseDatabasePath;
    
    // 常量
    const QString SALT = "FuxiRoboticLaser2025"; // 必须与主程序中的SALT一致
}; 