# 激光打标控制系统 - 注册码生成器

这是为激光打标控制系统开发的注册码生成工具，用于生成与特定机器绑定的注册码。

## 功能特点

- 基于机器码生成唯一注册码
- 支持设置注册码过期时间
- 可管理和导出已生成的注册记录
- 验证注册码有效性
- 支持不同的许可证类型（标准版、专业版、企业版）

## 构建方法

### 依赖项

- Qt 5.12或更高版本
- CMake 3.10或更高版本
- C++11兼容的编译器

### 在Windows平台构建

```
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

生成的可执行文件位于`build/bin/Release/`目录。

### 在Linux平台构建

```
mkdir build
cd build
cmake ..
make
```

生成的可执行文件位于`build/bin/`目录。

## 使用说明

1. 从主程序导出机器码（在"帮助->关于"菜单中）
2. 在注册码生成器中输入机器码
3. 填写客户信息和许可类型
4. 设置过期日期或选择"永不过期"
5. 点击"生成注册码"按钮
6. 使用"复制注册码"或"保存注册码"功能将注册码提供给客户

## 注意事项

- 本工具仅供内部使用，请勿外传
- 生成的注册码与特定机器硬件绑定，不可在其他设备上使用
- 所有生成的注册记录都会保存在本地数据库中，可通过"管理注册记录"功能查看和导出 