#include "MainWindow.h"
#include <QApplication>
#include <QDesktopWidget>
#include <QMessageBox>
#include <QDir>
#include <QStandardPaths>
#include <QFileDialog>
#include <QClipboard>
#include <QTableWidget>
#include <QHeaderView>
#include <QVBoxLayout>
#include <QDialogButtonBox>
#include <QDateTimeEdit>
#include <QScreen>
#include <QDebug>


#pragma execution_character_set("utf-8")

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    setWindowTitle("激光打标控制系统 - 注册码生成器");
    
    // 设置固定大小
    setFixedSize(1280, 720);
    
    // 初始化UI
    setupUI();
    
    // 初始化许可证数据库路径
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir appDataDir(appDataPath);
    if (!appDataDir.exists()) {
        appDataDir.mkpath(".");
    }
    
    licenseDatabasePath = appDataPath + "/licenses.csv";
    
    // 将窗口居中显示
    QRect screenGeometry = QGuiApplication::primaryScreen()->geometry();
    int x = (screenGeometry.width() - width()) / 2;
    int y = (screenGeometry.height() - height()) / 2;
    move(x, y);
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    mainLayout = new QGridLayout(centralWidget);
    
    // 创建标题标签
    QLabel* lblTitle = new QLabel("激光打标控制系统 - 注册码生成器", this);
    lblTitle->setStyleSheet("font-size: 16px; font-weight: bold; color: #2980b9;");
    lblTitle->setAlignment(Qt::AlignCenter);
    
    // 创建机器码输入框
    QLabel* lblMachineCode = new QLabel("机器码:", this);
    edtMachineCode = new QLineEdit(this);
    edtMachineCode->setPlaceholderText("请输入或粘贴机器码 (格式: XXXX-XXXX-XXXX-XXXX)");
    
    // 添加提示说明
    QLabel* lblMachineCodeInfo = new QLabel("<i>注意: 用户电脑可能有多个MAC地址，系统会显示其中一个作为机器码。验证时，任何一个MAC地址匹配即视为有效。</i>", this);
    lblMachineCodeInfo->setStyleSheet("color: #e67e22; font-size: 11px;");
    lblMachineCodeInfo->setWordWrap(true);
    
    // 创建客户信息输入框
    QGroupBox* grpCustomerInfo = new QGroupBox("客户信息", this);
    QGridLayout* customerLayout = new QGridLayout(grpCustomerInfo);
    
    QLabel* lblCustomerName = new QLabel("客户名称:", grpCustomerInfo);
    edtCustomerName = new QLineEdit(grpCustomerInfo);
    
    QLabel* lblCustomerEmail = new QLabel("客户邮箱:", grpCustomerInfo);
    edtCustomerEmail = new QLineEdit(grpCustomerInfo);
    
    // 添加有效期快速选择
    QLabel* lblLicenseDuration = new QLabel("有效期:", grpCustomerInfo);
    QComboBox* cmbLicenseDuration = new QComboBox(grpCustomerInfo);
    cmbLicenseDuration->addItem("1年", 1);
    cmbLicenseDuration->addItem("2年", 2);
    cmbLicenseDuration->addItem("3年", 3);
    cmbLicenseDuration->addItem("5年", 5);
    cmbLicenseDuration->addItem("10年", 10);
    cmbLicenseDuration->addItem("自定义", 0);
    
    QLabel* lblExpirationDate = new QLabel("过期日期:", grpCustomerInfo);
    
    dtpExpirationDate = new QDateEdit(grpCustomerInfo);
    dtpExpirationDate->setCalendarPopup(true);
    dtpExpirationDate->setDate(QDate::currentDate().addYears(1));
    dtpExpirationDate->setMinimumDate(QDate::currentDate());
    
    // 当选择有效期时更新过期日期
    connect(cmbLicenseDuration, QOverload<int>::of(&QComboBox::currentIndexChanged), 
            [this, cmbLicenseDuration](int index) {
        int years = cmbLicenseDuration->currentData().toInt();
        if (years > 0) {
            dtpExpirationDate->setDate(QDate::currentDate().addYears(years));
        }
    });
    
    chkNeverExpire = new QCheckBox("长期许可证 (99年)", grpCustomerInfo);
    connect(chkNeverExpire, &QCheckBox::toggled, [this, cmbLicenseDuration](bool checked) {
        dtpExpirationDate->setEnabled(!checked);
        cmbLicenseDuration->setEnabled(!checked);
    });
    
    customerLayout->addWidget(lblCustomerName, 0, 0);
    customerLayout->addWidget(edtCustomerName, 0, 1);
    customerLayout->addWidget(lblCustomerEmail, 1, 0);
    customerLayout->addWidget(edtCustomerEmail, 1, 1);
    customerLayout->addWidget(lblLicenseDuration, 2, 0);
    customerLayout->addWidget(cmbLicenseDuration, 2, 1);
    customerLayout->addWidget(lblExpirationDate, 3, 0);
    customerLayout->addWidget(dtpExpirationDate, 3, 1);
    customerLayout->addWidget(chkNeverExpire, 4, 1);
    
    // 创建注册码显示框
    QLabel* lblLicenseKey = new QLabel("注册码:", this);
    edtLicenseKey = new QLineEdit(this);
    edtLicenseKey->setReadOnly(true);
    edtLicenseKey->setStyleSheet("background-color: #f8f9fa; font-family: Consolas, monospace;");
    
    // 创建按钮
    btnGenerateLicense = new QPushButton("生成注册码", this);
    btnCopyLicense = new QPushButton("复制注册码", this);
    btnSaveLicense = new QPushButton("保存注册码", this);
    btnLoadMachineCode = new QPushButton("读取机器码", this);
    btnClear = new QPushButton("清除", this);
    btnVerifyLicense = new QPushButton("验证注册码", this);
    btnManageLicenses = new QPushButton("管理注册记录", this);
    
    // 设置按钮样式
    btnGenerateLicense->setStyleSheet("background-color: #2980b9; color: white;");
    btnCopyLicense->setEnabled(false);
    btnSaveLicense->setEnabled(false);
    
    // 添加控件到布局
    int row = 0;
    mainLayout->addWidget(lblTitle, row++, 0, 1, 4);
    
    mainLayout->addWidget(lblMachineCode, row, 0);
    mainLayout->addWidget(edtMachineCode, row, 1, 1, 2);
    mainLayout->addWidget(btnLoadMachineCode, row++, 3);
    mainLayout->addWidget(lblMachineCodeInfo, row++, 0, 1, 4);
    
    mainLayout->addWidget(grpCustomerInfo, row++, 0, 1, 4);
    
    mainLayout->addWidget(lblLicenseKey, row, 0);
    mainLayout->addWidget(edtLicenseKey, row++, 1, 1, 3);
    
    // 创建按钮布局
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(btnGenerateLicense);
    buttonLayout->addWidget(btnCopyLicense);
    buttonLayout->addWidget(btnSaveLicense);
    buttonLayout->addWidget(btnVerifyLicense);
    buttonLayout->addWidget(btnClear);
    mainLayout->addLayout(buttonLayout, row++, 0, 1, 4);
    
    mainLayout->addWidget(btnManageLicenses, row++, 0, 1, 4);
    
    // 设置版权信息
    QLabel* lblCopyright = new QLabel("© 2025 Fuxi科技 - 内部工具，请勿外传", this);
    lblCopyright->setAlignment(Qt::AlignCenter);
    lblCopyright->setStyleSheet("color: #7f8c8d; font-size: 11px;");
    mainLayout->addWidget(lblCopyright, row++, 0, 1, 4);
    
    // 连接信号与槽
    connect(btnGenerateLicense, &QPushButton::clicked, this, &MainWindow::onGenerateLicense);
    connect(btnCopyLicense, &QPushButton::clicked, this, &MainWindow::onCopyLicense);
    connect(btnSaveLicense, &QPushButton::clicked, this, &MainWindow::onSaveLicense);
    connect(btnLoadMachineCode, &QPushButton::clicked, this, &MainWindow::onLoadMachineCode);
    connect(btnClear, &QPushButton::clicked, this, &MainWindow::onClear);
    connect(btnVerifyLicense, &QPushButton::clicked, this, &MainWindow::onVerifyLicense);
    connect(btnManageLicenses, &QPushButton::clicked, this, &MainWindow::onManageLicenses);
}

void MainWindow::onGenerateLicense()
{
    // 获取机器码
    QString machineCode = edtMachineCode->text().trimmed();
    
    // 验证机器码格式
    QRegExp machineCodeRegex("^[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}$");
    if (!machineCodeRegex.exactMatch(machineCode)) {
        QMessageBox::warning(this, "输入错误", "请输入有效的机器码 (格式: XXXX-XXXX-XXXX-XXXX)");
        return;
    }
    
    // 获取过期日期
    QDate expirationDate;
    if (chkNeverExpire->isChecked()) {
        // 设置为99年后
        expirationDate = QDate::currentDate().addYears(99);
    } else {
        expirationDate = dtpExpirationDate->date();
    }
    
    // 生成注册码
    QString licenseKey = generateLicenseKey(machineCode, expirationDate);
    
    // 显示注册码
    edtLicenseKey->setText(licenseKey);
    
    // 启用复制和保存按钮
    btnCopyLicense->setEnabled(true);
    btnSaveLicense->setEnabled(true);
    
    // 保存注册记录
    QString customerInfo = QString("%1,%2")
                          .arg(edtCustomerName->text())
                          .arg(edtCustomerEmail->text());
    saveLicenseRecord(machineCode, licenseKey, expirationDate, customerInfo);
}

void MainWindow::onCopyLicense()
{
    QString licenseKey = edtLicenseKey->text();
    if (!licenseKey.isEmpty()) {
        QClipboard* clipboard = QApplication::clipboard();
        clipboard->setText(licenseKey);
        QMessageBox::information(this, "复制成功", "注册码已复制到剪贴板");
    }
}

void MainWindow::onSaveLicense()
{
    QString licenseKey = edtLicenseKey->text();
    if (licenseKey.isEmpty()) {
        return;
    }
    
    // 获取客户名称（用于文件名）
    QString customerName = edtCustomerName->text().trimmed();
    if (customerName.isEmpty()) {
        customerName = "未命名客户";
    }
    
    // 设置默认文件名
    QString defaultFileName = QString("%1_注册码_%2.txt")
                             .arg(customerName)
                             .arg(QDate::currentDate().toString("yyyyMMdd"));
    
    QString filePath = QFileDialog::getSaveFileName(this, "保存注册码", 
                                                  defaultFileName, 
                                                  "文本文件 (*.txt)");
    
    if (filePath.isEmpty()) {
        return;
    }
    
    QFile file(filePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        
        // 写入注册信息
        out << "机器码: " << edtMachineCode->text() << "\n";
        out << "注册码: " << licenseKey << "\n";
        out << "客户名称: " << edtCustomerName->text() << "\n";
        out << "客户邮箱: " << edtCustomerEmail->text() << "\n";
        
        // 写入过期信息
        if (chkNeverExpire->isChecked()) {
            out << "过期日期: 长期许可证 (99年)\n";
        } else {
            out << "过期日期: " << dtpExpirationDate->date().toString("yyyy-MM-dd") << "\n";
        }
        
        out << "\n";
        out << "激光打标控制系统 - 注册信息\n";
        out << "生成日期: " << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") << "\n";
        
        // 添加使用说明
        out << "\n";
        out << "使用说明：\n";
        out << "1. 打开激光打标控制系统\n";
        out << "2. 点击菜单「注册」->「软件注册/更新」\n";
        out << "3. 在注册对话框中输入上述注册码\n";
        out << "4. 点击「验证注册码」按钮完成注册\n";
        out << "\n";
        out << "注意事项：\n";
        out << "• 注册码与计算机硬件绑定，不可在其他计算机上使用\n";
        out << "• 系统会自动识别计算机的网络适配器，任一适配器匹配即视为有效\n";
        out << "• 如更换主板或所有网络适配器，可能需要重新申请注册码\n";
        
        file.close();
        QMessageBox::information(this, "保存成功", "注册码已保存到文件: " + filePath);
    } else {
        QMessageBox::warning(this, "保存失败", "无法写入文件: " + filePath);
    }
}

void MainWindow::onLoadMachineCode()
{
    QString filePath = QFileDialog::getOpenFileName(this, "读取机器码文件", 
                                                  QString(), 
                                                  "文本文件 (*.txt);;所有文件 (*.*)");
    
    if (filePath.isEmpty()) {
        return;
    }
    
    QFile file(filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        QString line;
        bool machineCodeFound = false;
        
        // 逐行读取，查找机器码
        while (!in.atEnd()) {
            line = in.readLine();
            
            // 查找包含机器码的行
            if (line.contains("机器码", Qt::CaseInsensitive)) {
                QRegExp regex("[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}");
                if (regex.indexIn(line) != -1) {
                    edtMachineCode->setText(regex.cap(0));
                    machineCodeFound = true;
                    break;
                }
            }
        }
        
        file.close();
        
        if (!machineCodeFound) {
            QMessageBox::warning(this, "读取失败", "文件中未找到有效的机器码");
        }
    } else {
        QMessageBox::warning(this, "读取失败", "无法打开文件: " + filePath);
    }
}

void MainWindow::onClear()
{
    edtMachineCode->clear();
    edtLicenseKey->clear();
    edtCustomerName->clear();
    edtCustomerEmail->clear();
    dtpExpirationDate->setDate(QDate::currentDate().addYears(1));
    chkNeverExpire->setChecked(false);
    
    btnCopyLicense->setEnabled(false);
    btnSaveLicense->setEnabled(false);
}

void MainWindow::onVerifyLicense()
{
    // 获取机器码和注册码
    QString machineCode = edtMachineCode->text().trimmed();
    QString licenseKey = edtLicenseKey->text().trimmed();
    
    // 验证机器码格式
    QRegExp machineCodeRegex("^[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}$");
    if (!machineCodeRegex.exactMatch(machineCode)) {
        QMessageBox::warning(this, "验证失败", "请输入有效的机器码");
        return;
    }
    
    // 验证注册码格式
    QRegExp licenseKeyRegex("^[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}$");
    if (!licenseKeyRegex.exactMatch(licenseKey)) {
        QMessageBox::warning(this, "验证失败", "请输入有效的注册码");
        return;
    }
    
    // 移除破折号
    QString plainMachineCode = machineCode;
    plainMachineCode.remove('-');
    
    // 检查是否为长期许可证（基于复选框）
    bool isLongTerm = chkNeverExpire->isChecked();
    QDate expirationDate = dtpExpirationDate->date();
    
    // 创建预期的许可证密钥
    QString seedString;
    if (isLongTerm) {
        // 使用99年后的日期
        QDate longTermDate = QDate::currentDate().addYears(99);
        QString expirationStr = longTermDate.toString("yyyyMMdd");
        seedString = plainMachineCode + "-" + expirationStr + "-" + SALT;
    } else {
        // 对于时限许可证，使用过期日期
        QString expirationStr = expirationDate.toString("yyyyMMdd");
        seedString = plainMachineCode + "-" + expirationStr + "-" + SALT;
    }
    
    QByteArray combined = seedString.toUtf8();
    QByteArray hash = QCryptographicHash::hash(combined, QCryptographicHash::Sha256);
    QString expectedKey = hash.toHex().left(24).toUpper();
    
    // 格式化为 XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
    expectedKey = expectedKey.mid(0, 4) + "-" + 
                 expectedKey.mid(4, 4) + "-" + 
                 expectedKey.mid(8, 4) + "-" + 
                 expectedKey.mid(12, 4) + "-" + 
                 expectedKey.mid(16, 4) + "-" + 
                 expectedKey.mid(20, 4);
    
    // 比较输入的许可证密钥和预期的许可证密钥
    bool isValid = (licenseKey.compare(expectedKey, Qt::CaseInsensitive) == 0);
    
    if (isValid) {
        QString message = isLongTerm ? 
            "注册码有效，与机器码匹配。这是一个长期许可证 (99年)。" : 
            "注册码有效，与机器码匹配。这是一个时限许可证，过期日期: " + expirationDate.toString("yyyy-MM-dd");
        QMessageBox::information(this, "验证成功", message);
    } else {
        // 尝试不同的过期日期，但使用更高效的方法
        // 首先尝试一些常见的日期间隔（1年、2年、3年、5年、10年、99年）
        QList<int> commonYearIntervals = {1, 2, 3, 5, 10, 99};
        QDate currentDate = QDate::currentDate();
        bool foundValidDate = false;
        
        for (int years : commonYearIntervals) {
            QDate tryDate = currentDate.addYears(years);
            QString dateStr = tryDate.toString("yyyyMMdd");
            QString dateSeedString = plainMachineCode + "-" + dateStr + "-" + SALT;
            
            QByteArray dateCombined = dateSeedString.toUtf8();
            QByteArray dateHash = QCryptographicHash::hash(dateCombined, QCryptographicHash::Sha256);
            QString dateExpectedKey = dateHash.toHex().left(24).toUpper();
            
            // 格式化
            dateExpectedKey = dateExpectedKey.mid(0, 4) + "-" + 
                            dateExpectedKey.mid(4, 4) + "-" + 
                            dateExpectedKey.mid(8, 4) + "-" + 
                            dateExpectedKey.mid(12, 4) + "-" + 
                            dateExpectedKey.mid(16, 4) + "-" + 
                            dateExpectedKey.mid(20, 4);
            
            if (licenseKey.compare(dateExpectedKey, Qt::CaseInsensitive) == 0) {
                foundValidDate = true;
                QString licenseType = (years == 99) ? "长期许可证 (99年)" : "时限许可证";
                QString message = QString("注册码有效，与机器码匹配。这是一个%1，过期日期: %2").arg(licenseType).arg(tryDate.toString("yyyy-MM-dd"));
                if (isLongTerm != (years == 99)) {
                    message += "\n\n注意：您当前设置的许可证类型与注册码不匹配。";
                }
                QMessageBox::information(this, "验证成功", message);
                
                // 询问是否更新日期和复选框状态
                if (QMessageBox::question(this, "更新设置", 
                                        "是否要将过期日期更新为: " + tryDate.toString("yyyy-MM-dd") + "？", 
                                        QMessageBox::Yes | QMessageBox::No) == QMessageBox::Yes) {
                    dtpExpirationDate->setDate(tryDate);
                    chkNeverExpire->setChecked(years == 99);
                }
                break;
            }
        }
        
        // 如果常见间隔没有匹配，尝试当前设置的日期
        if (!foundValidDate) {
            QDate tryDate = dtpExpirationDate->date();
            QString dateStr = tryDate.toString("yyyyMMdd");
            QString dateSeedString = plainMachineCode + "-" + dateStr + "-" + SALT;
            
            QByteArray dateCombined = dateSeedString.toUtf8();
            QByteArray dateHash = QCryptographicHash::hash(dateCombined, QCryptographicHash::Sha256);
            QString dateExpectedKey = dateHash.toHex().left(24).toUpper();
            
            // 格式化
            dateExpectedKey = dateExpectedKey.mid(0, 4) + "-" + 
                            dateExpectedKey.mid(4, 4) + "-" + 
                            dateExpectedKey.mid(8, 4) + "-" + 
                            dateExpectedKey.mid(12, 4) + "-" + 
                            dateExpectedKey.mid(16, 4) + "-" + 
                            dateExpectedKey.mid(20, 4);
            
            if (licenseKey.compare(dateExpectedKey, Qt::CaseInsensitive) == 0) {
                foundValidDate = true;
                QString message = QString("注册码有效，与机器码匹配。这是一个时限许可证，过期日期: %1").arg(tryDate.toString("yyyy-MM-dd"));
                QMessageBox::information(this, "验证成功", message);
            }
        }
        
        if (!foundValidDate) {
            QMessageBox::critical(this, "验证失败", "注册码无效，与机器码不匹配。\n\n提示：客户可能需要提供准确的机器码，一台电脑可能有多个机器码，客户需确保提供的是其软件显示的那个。");
        }
    }
}

void MainWindow::onManageLicenses()
{
    // 创建一个对话框
    QDialog dialog(this);
    dialog.setWindowTitle("管理注册记录");
    dialog.resize(800, 500);
    
    // 创建表格
    QTableWidget* table = new QTableWidget(&dialog);
    table->setColumnCount(7);
    table->setHorizontalHeaderLabels({
        "生成日期", "机器码", "注册码", "过期日期", 
        "客户名称", "客户邮箱", "许可期限"
    });
    table->horizontalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    table->horizontalHeader()->setStretchLastSection(true);
    table->setSelectionBehavior(QAbstractItemView::SelectRows);
    
    // 加载注册记录
    QFile file(licenseDatabasePath);
    if (file.exists() && file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        in.setCodec("UTF-8");
        
        // 跳过标题行
        if (!in.atEnd()) {
            in.readLine();
        }
        
        // 读取所有记录
        while (!in.atEnd()) {
            QString line = in.readLine();
            QStringList fields = line.split(",");
            
            if (fields.size() >= 7) {
                int row = table->rowCount();
                table->insertRow(row);
                
                // 填充表格
                for (int i = 0; i < fields.size() && i < 7; i++) {
                    table->setItem(row, i, new QTableWidgetItem(fields[i]));
                }
                
                // 设置单元格样式
                for (int col = 0; col < table->columnCount(); ++col) {
                    QTableWidgetItem* item = table->item(row, col);
                    if (item) {
                        // 为永久许可证设置特殊颜色
                        if (col == 6 && item->text() == "永久许可证") {
                            item->setBackground(QBrush(QColor("#d4edda")));
                            item->setForeground(QBrush(QColor("#155724")));
                            item->setFont(QFont("Microsoft YaHei", 9, QFont::Bold));
                        }
                        // 设置过期日期颜色
                        else if (col == 3) {
                            QDate date = QDate::fromString(item->text(), "yyyy-MM-dd");
                            if (date <= QDate::currentDate()) {
                                item->setBackground(QBrush(QColor("#f8d7da")));
                                item->setForeground(QBrush(QColor("#721c24")));
                            }
                        }
                    }
                }
            }
        }
        
        file.close();
    }
    
    // 创建按钮
    QDialogButtonBox* buttonBox = new QDialogButtonBox(&dialog);
    QPushButton* btnExport = new QPushButton("导出全部记录", &dialog);
    QPushButton* btnClose = new QPushButton("关闭", &dialog);
    buttonBox->addButton(btnExport, QDialogButtonBox::ActionRole);
    buttonBox->addButton(btnClose, QDialogButtonBox::RejectRole);
    
    // 创建布局
    QVBoxLayout* layout = new QVBoxLayout(&dialog);
    layout->addWidget(table);
    layout->addWidget(buttonBox);
    
    // 连接信号
    connect(btnClose, &QPushButton::clicked, &dialog, &QDialog::reject);
    connect(btnExport, &QPushButton::clicked, [&]() {
        QString filePath = QFileDialog::getSaveFileName(&dialog, "导出注册记录", 
                                                     "注册记录.csv", 
                                                     "CSV文件 (*.csv)");
        
        if (!filePath.isEmpty()) {
            QFile exportFile(filePath);
            if (exportFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&exportFile);
                out.setCodec("UTF-8");
                
                // 写入标题行
                out << "生成日期,机器码,注册码,过期日期,客户名称,客户邮箱,许可期限\n";
                
                // 从表格中导出数据
                for (int row = 0; row < table->rowCount(); row++) {
                    QStringList rowData;
                    for (int col = 0; col < table->columnCount(); col++) {
                        QTableWidgetItem* item = table->item(row, col);
                        rowData << (item ? item->text() : "");
                    }
                    out << rowData.join(",") << "\n";
                }
                
                exportFile.close();
                QMessageBox::information(&dialog, "导出成功", "注册记录已导出到: " + filePath);
            } else {
                QMessageBox::warning(&dialog, "导出失败", "无法写入文件: " + filePath);
            }
        }
    });
    
    // 显示对话框
    dialog.exec();
}

QString MainWindow::generateLicenseKey(const QString& machineCode, const QDate& expirationDate)
{
    // 移除破折号
    QString plainMachineCode = machineCode;
    plainMachineCode.remove('-');
    
    // 创建许可证密钥的基础
    QString seedString;
    // 对于所有许可证，将过期日期添加到种子字符串中
    QString expirationStr = expirationDate.toString("yyyyMMdd");
    seedString = plainMachineCode + "-" + expirationStr + "-" + SALT;
    qDebug() << "正在生成许可证，过期日期：" << expirationStr;
    
    // 计算哈希
    QByteArray combined = seedString.toUtf8();
    QByteArray hash = QCryptographicHash::hash(combined, QCryptographicHash::Sha256);
    QString licenseKey = hash.toHex().left(24).toUpper();
    
    // 格式化为 XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
    licenseKey = licenseKey.mid(0, 4) + "-" + 
                licenseKey.mid(4, 4) + "-" + 
                licenseKey.mid(8, 4) + "-" + 
                licenseKey.mid(12, 4) + "-" + 
                licenseKey.mid(16, 4) + "-" + 
                licenseKey.mid(20, 4);
    
    return licenseKey;
}

void MainWindow::saveLicenseRecord(const QString& machineCode, const QString& licenseKey, 
                                  const QDate& expirationDate, const QString& customerInfo)
{
    // 检查许可证数据库文件是否存在
    bool needHeader = false;
    if (!QFile::exists(licenseDatabasePath)) {
        needHeader = true;
    }
    
    // 打开文件用于追加
    QFile file(licenseDatabasePath);
    if (file.open(QIODevice::WriteOnly | QIODevice::Append | QIODevice::Text)) {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        
        // 如果需要，写入标题行
        if (needHeader) {
            out << "生成日期,机器码,注册码,过期日期,客户名称,客户邮箱,许可期限\n";
        }
        
        // 检查是否为长期许可证
        bool isLongTerm = expirationDate > QDate::currentDate().addYears(90);
        QString licenseTypeStr = isLongTerm ? "长期许可证 (99年)" : "时限许可证";
        
        // 写入记录
        out << QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss") << ",";
        out << machineCode << ",";
        out << licenseKey << ",";
        out << expirationDate.toString("yyyy-MM-dd") << ",";
        out << customerInfo << ",";
        out << licenseTypeStr << "\n";
        
        file.close();
        
        QMessageBox::information(this, "注册码生成成功", 
                              QString("已成功生成%1注册码，并保存到记录中。\n\n"
                                     "请妥善保管注册码，并提供给客户。")
                              .arg(isLongTerm ? "长期" : "时限"));
    }
}

void MainWindow::loadLicenseRecords()
{
    // 读取许可证记录用于显示或其他用途
    QFile file(licenseDatabasePath);
    if (file.exists() && file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        QTextStream in(&file);
        in.setCodec("UTF-8");
        
        // 跳过标题行
        if (!in.atEnd()) {
            in.readLine();
        }
        
        // 读取所有记录
        while (!in.atEnd()) {
            QString line = in.readLine();
            // 处理每条记录...
        }
        
        file.close();
    }
} 