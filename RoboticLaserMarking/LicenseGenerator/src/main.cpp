#include "MainWindow.h"
#include <QApplication>
#include <QTextCodec>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("LaserMarkingLicenseGenerator");
    app.setOrganizationName("FuxiRobotics");
    app.setOrganizationDomain("fuxi-robotics.com");
    
    // 设置中文编码
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    
    // 设置应用程序样式
    app.setStyle("Fusion");
    
    // 创建并显示主窗口
    MainWindow mainWindow;
    mainWindow.show();
    
    return app.exec();
} 