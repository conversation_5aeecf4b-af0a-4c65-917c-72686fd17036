类名：ControlCard
特殊说明：在启动外部触发的模式下，IO板卡的输入引脚Pin0已绑定启动标刻信号，Pin1已绑定终止标刻信号，输出引脚Pin0绑定标刻状态信号，例：当Pin0输入高电平时，激光启动雕刻；当Pin1输入高电平时，激光停止雕刻；当激光处于工作状态时，输出引脚Pin0输出低电平，反之输出高电平
函数接口：
函数名	bool OpenCard (UInt16 _card_no)
说明	初始化并连接IO控制卡，_card_no：卡的序号，默认为0
返回值	成功返回True，失败返回False

函数名	bool CloseCard ()
说明	关闭IO控制卡连接并释放资源
返回值	成功返回True，失败返回False

函数名	bool IsCardOpened ()
说明	获取控制卡的连接状态
返回值	已连接返回True，未连接返回False

函数名	bool WriteOutputIO (UInt16 _value)
说明	设置IO卡输出口值，_value:二进制下，每个位代表对应输出IO的状态
返回值	已连接返回True，未连接返回False

函数名	bool ReadOutputIO (ref UInt16 _value)
说明	读取IO卡输出口值，_value:二进制下，每个位代表对应输出IO的状态
返回值	已连接返回True，未连接返回False

函数名	bool WriteOutputIOBit(UInt16 _io_index, bool _value)
说明	按位设置IO卡输出口值，_io_index:IO序号(从0开始)，_value:True代表输出高电平，反之低电平 
返回值	已连接返回True，未连接返回False

函数名	bool ReadOutputIOBit (UInt16 _io_index, ref bool _value)
说明	按位读取IO卡输出口值，_io_index:IO序号(从0开始)，_value:True代表输出高电平，反之低电平 
返回值	已连接返回True，未连接返回False

函数名	bool ReadInputIO (ref UInt16 _value)
说明	读取IO卡输入口值，_value: 二进制下，每个位代表对应输入IO的状态
返回值	已连接返回True，未连接返回False

函数名	bool ReadInputIOBit (UInt16 _io_index, ref bool _value)
说明	按位读取IO卡输入口值，_io_index:IO序号(从0开始)，_value:True代表输入高电平，反之低电平 
返回值	已连接返回True，未连接返回False

函数名	bool WaitForIOBit (UInt16 _io_index, bool _cond, double _timeouts)
说明	按位读取IO卡输入口值，_io_index:IO序号(从0开始)，_cond:True代表要求输入高电平，反之低电平， _timeouts: 超时设置，值小于等于0时表示无限循环等待直到满足目标_cond状态
返回值	已连接返回True，未连接返回False
 
