using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.StartPanel;

namespace BDLaser
{
    public class LaserControlHelper
    {
        public LaserControlHelper()
        {
        }

        #region member
        bool m_error = false;
        string m_errorMsg = string.Empty;
        private static LaserCallbackDelegate m_frameCallBack;
        #endregion


        /// <summary>
        /// 激光连接
        /// </summary>
        /// <returns></returns>
        public bool Connect()
        {
            m_frameCallBack = LaserCallBackFunction;
            return LaserControl.ConnectLaser(m_frameCallBack);
        }

        /// <summary>
        /// 激光断开连接
        /// </summary>
        /// <returns></returns>
        public bool DisConnect()
        {
            return LaserControl.CloseLaser();
        }

        /// <summary>
        /// 启动
        /// </summary>
        /// <returns></returns>
        public bool BeginMark(bool _preview)
        {
            return LaserControl.BeginMark(_preview);
        }

        /// <summary>
        /// 停止
        /// </summary>
        /// <returns></returns>
        public bool EndMark()
        {
            return LaserControl.EndMark();
        }

        public bool RedGuide(bool _enable)
        {
            return LaserControl.RedGuide(_enable);
        }

        public bool ResetLaserState()
        {
            return LaserControl.ResetLaserState();
        }

        public bool LaserOn()
        {
            return LaserControl.LaserHandON();
        }

        public bool LaserOff()
        {
            return LaserControl.LaserHandOFF();
        }

        public bool IsConnected()
        {
            return LaserControl.IsLaserConnected();
        }

        public bool IsWorking()
        {
            return LaserControl.IsLaserWorking();
        }

        public UInt64 GetLaserMarkTime()
        {
            return LaserControl.GetLaserMarkTime();
        }

        public bool LoadProcessParam(string _path)
        {
            return LaserControl.LoadProcessParam(_path);
        }

        public bool LoadMarkData(string _path)
        {
            return LaserControl.LoadMarkData(_path);
        }

        public bool SetWorkDir(string _path)
        {
            return LaserControl.SetWorkDir(_path);
        }

        public bool ClearData()
        {
            return LaserControl.ClearData();
        }

        public long DataSize()
        {
            return LaserControl.DataSize();
        }

        public bool ExternalStart_StopSignal(bool _enable, ref bool _start, ref bool _stop)
        {
            return LaserControl.ExternalStart_StopSignal(_enable, ref _start, ref _stop);
        }

        public void GetErrorMsg(ref bool error, ref string errorMsg)
        {
            StringBuilder errorStr = new StringBuilder(4096);
            LaserControl.GetLastErrorMsg(ref error, errorStr);
            byte[] utf8Bytes = Encoding.Unicode.GetBytes(errorStr.ToString());
            errorMsg = Encoding.UTF8.GetString(utf8Bytes);
            //Console.WriteLine(errorMsg);
        }

        /// <summary>
        /// 激光回调函数
        /// </summary>
        /// <param name="msg">消息</param>
        public void LaserCallBackFunction([MarshalAs(UnmanagedType.LPStr)] string msg)
        {
            Console.Write("LaserCallBackFunction:");
            Console.WriteLine(msg);
            m_error = true;
            m_errorMsg = msg;
        }
    }
}
