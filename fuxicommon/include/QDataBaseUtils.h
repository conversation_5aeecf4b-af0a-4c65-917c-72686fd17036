//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/14.
//
#pragma once

#include <QSqlDatabase>
#include "rttr/type.h"

namespace Fuxi {
    namespace Common {
        class QDataBaseUtils {
        public:
            static QSqlDatabase
            createMysqlDatabase(std::string host, std::string userName, std::string password, std::string database,
                                std::string connectionName, int port = 3306);

            static QSqlDatabase
            createQqliteDatabase(std::string name, std::string connectionName);

            static bool checkDataBaseExist(std::string host, std::string userName, std::string password,
                                           std::string database, std::string connectionName, int port = 3306);

        };

    }
}
