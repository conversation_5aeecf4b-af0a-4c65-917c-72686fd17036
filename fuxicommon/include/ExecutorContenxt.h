#pragma once

#include <glog.h>
#include <map>
#include <vector>
#include <type_traits>
#include <memory>
#include "Executor.h"

// Check C++ standard version and include appropriate any header
#if __cplusplus >= 201703L  // C++17 or later
    #include <any>
    namespace anyns = std;  // Use std::any
#else
    #include <boost/any.hpp>
    namespace anyns = boost;  // Use boost::any as fallback
#endif

namespace Fuxi {
    namespace Common {
        extern thread_local std::map<std::string, anyns::any> _valueMap;

        template<class T>
        struct is_shared_ptr
                : std::false_type {
        };

        template<class T>
        struct is_shared_ptr<std::shared_ptr<T>>
                : std::true_type {
        };

        template<class T>
        struct is_shared_ptr<std::weak_ptr<T>>
                : std::true_type {
        };

        template<>
        struct is_shared_ptr<int>
                : std::true_type {
        };

        template<class T>
        struct is_shared_ptr<std::is_arithmetic<T>>
                : std::true_type {
        };

        template<>
        struct is_shared_ptr<std::string>
                : std::true_type {
        };

        template<class T>
        struct is_shared_ptr<std::vector<T>>
                : std::true_type {
        };

        /**
         * 线程上下文存储,
         */
        class ExecutorContenxt {
        public:
            ExecutorContenxt();

            ~ExecutorContenxt();

            template<class Value,
                    typename std::enable_if<is_shared_ptr<Value>::value, int>::type = 0>
            bool setValue(std::string key, Value v,
                          std::shared_ptr<Fuxi::Common::Executor> executor = nullptr) {
                if (!executor) {
                    executor = _actionExecutor;
                }
                auto fb = executor->postTask<bool>([this, key, v]() -> bool {
                    anyns::any any = v;
                    if (_valueMap.count(key) > 0 && any.type() != _valueMap[key].type()) {
                        LOG(FATAL) << " any type not same " << key << " value " << any.type().name();
                    }
                    _valueMap[key] = any;
                    return true;
                });
                return fb.get();
            }

            bool isExists(std::string key, std::shared_ptr<Fuxi::Common::Executor> executor = nullptr) {
                if (!executor) {
                    executor = _actionExecutor;
                }
                auto fb = executor->postTask<bool>([this, key]() {
                    return _valueMap.count(key) > 0;
                });
                return fb.get();
            }

            void remove(std::string key, std::shared_ptr<Fuxi::Common::Executor> executor = nullptr) {
                if (!executor) {
                    executor = _actionExecutor;
                }
                auto fb = executor->postTask<int>([this, key]() {
                    _valueMap.erase(key);
                    return 1;
                });
                fb.get();
            }

            template<class Value,
                    typename std::enable_if<is_shared_ptr<Value>::value, int>::type = 0>
            Value getValue(std::string key,
                           std::shared_ptr<Fuxi::Common::Executor> executor = nullptr) {
                if (!executor) {
                    executor = _actionExecutor;
                }
                auto fb = executor->postTask<Value>([this, key]() -> Value {
                    if (_valueMap.find(key) != _valueMap.end()) {
                        try {
                            return anyns::any_cast<Value>(_valueMap[key]);
                        } catch (const anyns::bad_any_cast &ex) {
                            LOG(ERROR) << " key " << key << " missing type " << ex.what();
                            return {};
                        }
                    } else {
                        LOG(ERROR) << key << " not found";
                        return {};
                    }
                });
                return fb.get();
            }

            template<class Value,
                    typename std::enable_if<is_shared_ptr<Value>::value, int>::type = 0>
            bool setCurrentValue(std::string key, Value v) {
                anyns::any any = v;
                if (_valueMap.count(key) > 0 && any.type() != _valueMap[key].type()) {
                    LOG(FATAL) << " any type not same " << key << " value " << any.type().name();
                }
                _valueMap[key] = any;
                return true;
            }

            void removeCurrent(std::string key) {
                _valueMap.erase(key);
            }

            bool isCurrentExists(std::string key, std::shared_ptr<Fuxi::Common::Executor> executor = nullptr) {
                return _valueMap.count(key) > 0;
            }

            template<class Value,
                    typename std::enable_if<is_shared_ptr<Value>::value, int>::type = 0>
            Value getCurrentValue(std::string key) {
                if (_valueMap.find(key) != _valueMap.end()) {
                    try {
                        return anyns::any_cast<Value>(_valueMap[key]);
                    } catch (...) {
                        LOG(ERROR) << " key " << key << " missing type ";
                        return {};
                    }
                } else {
                    LOG(ERROR) << key << " not found";
                    return {};
                }
            }

        private:
            /**
             * 从action里调用获取便利的线程.
             */
            std::shared_ptr<Fuxi::Common::Executor> _actionExecutor;
        };
    }
}