

#ifndef FUXI_TIMER_H
#define FUXI_TIMER_H


#include <chrono>
#include <string>


using namespace std;
using namespace std::chrono;

class getTimer {
public:
    // 获取当前时间，1970来的毫秒数 //
    static time_t getCurrentTimeMilliSec();

    static std::string getCurrentTimeString();
};


// 一个计时器类，可以方便的测试程序执行的时间 //
class Timer {
public:
    Timer() : m_time(high_resolution_clock::now()) {}

    void reset() { m_time = high_resolution_clock::now(); }

    // 默认返回毫秒 //
    template<class Duration = milliseconds>
    int64_t elapsed() const {
        return duration_cast<Duration>(high_resolution_clock::now() - m_time).count();
    }

    // 微秒  //
    int64_t elapsed_micro() const { return elapsed<microseconds>(); }

    // 秒 //
    int64_t elapsed_second() const { return elapsed<seconds>(); }

    // 分 //
    int64_t elapsed_minute() const { return elapsed<minutes>(); }

    // 时 //
    int64_t elapsed_hour() const { return elapsed<hours>(); }

    // 纳秒 //
    int64_t elapsed_nano() const { return elapsed<nanoseconds>(); }

private:
    time_point<high_resolution_clock> m_time;
};


#endif
