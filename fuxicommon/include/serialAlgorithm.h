//
// Created by tian on 2022/3/1.
//

#ifndef FUXIOS_SERIALALGORITHM_H
#define FUXIOS_SERIALALGORITHM_H
#include <cstdint>

class serialAlgorithm {
public:
    serialAlgorithm(){};
    ~serialAlgorithm(){};
    /**
     * 计算crc16
     * @param req
     * @param req_length
     * @return
     */
    static uint16_t crc16(uint8_t *req, uint8_t req_length);
    /**
     * 计算msg长度
     * @param msg
     * @param msg_length
     * @return
     */
    static int check_integrity(uint8_t *msg, uint8_t msg_length);

    /**
     * DEPRECATED - Set a float to 4 bytes in a sort of Modbus format!
     * @param f
     * @param dest
     */
    static void set_float(float f, uint16_t *dest);
    /**
     * "Fallback on C functions for bswap_16"
     * @param x
     * @return
     */
    static uint16_t bswap_16(uint16_t x);
    /**
     * "Fallback on C functions for bswap_32"
     * @param x
     * @return
     */
    static uint32_t bswap_32(uint32_t x);
    /**
     * Sets many bits from a single byte value (all 8 bits of the byte value are
   set)
     * @param dest
     * @param idx
     * @param value
     */
    static void set_bits_from_byte(uint8_t *dest, int idx, const uint8_t value);
    /**
     * Sets many bits from a table of bytes (only the bits between idx and
   idx + nb_bits are set)
     * @param dest
     * @param idx
     * @param nb_bits
     * @param tab_byte
     */
    static void set_bits_from_bytes(uint8_t *dest, int idx, unsigned int nb_bits, const uint8_t *tab_byte);
    /**
     * Gets the byte value from many bits.
   To obtain a full byte, set nb_bits to 8.
     * @param src
     * @param idx
     * @param nb_bits
     * @return
     */
    static uint8_t get_byte_from_bits(const uint8_t *src, int idx, unsigned int nb_bits);
    /**
     * Get a float from 4 bytes (Modbus) without any conversion (ABCD)
     * @param src
     * @return
     */
    static float get_float_abcd(const uint16_t *src);
    /**
     * Get a float from 4 bytes (Modbus) in inversed format (DCBA)
     * @param src
     * @return
     */
    static float get_float_dcba(const uint16_t *src);
    /**
     * Get a float from 4 bytes (Modbus) with swapped bytes (BADC)
     * @param src
     * @return
     */
    static float get_float_badc(const uint16_t *src);
    /**
     * Get a float from 4 bytes (Modbus) with swapped words (CDAB)
     * @param src
     * @return
     */
    static float get_float_cdab(const uint16_t *src);
    /**
     * DEPRECATED - Get a float from 4 bytes in sort of Modbus format
     * @param src
     * @return
     */
    static float get_float(const uint16_t *src);
    /**
     * Set a float to 4 bytes for Modbus w/o any conversion (ABCD)
     * @param f
     * @param dest
     */
    static void set_float_abcd(float f, uint16_t *dest);
    /**
     * Set a float to 4 bytes for Modbus with byte and word swap conversion (DCBA)
     * @param f
     * @param dest
     */
    static void set_float_dcba(float f, uint16_t *dest);
    /**
     * Set a float to 4 bytes for Modbus with byte swap conversion (BADC)
     * @param f
     * @param dest
     */
    static void set_float_badc(float f, uint16_t *dest);
    /**
     * Set a float to 4 bytes for Modbus with word swap conversion (CDAB)
     * @param f
     * @param dest
     */
    static void set_float_cdab(float f, uint16_t *dest);
    /**
     * 数字转16进制字符串
     * @param num
     * @return
     */
    static std::string toHex(int num);
    /**
     * char转字符串
     * @param ch
     * @return
     */
    static std::string unsignedCharToHexString(unsigned char ch);
};


#endif //FUXIOS_SERIALALGORITHM_H
