#ifndef FUXI_FILEMANAGER_H
#define FUXI_FILEMANAGER_H

// Fix for C++17 std::byte conflict with Windows SDK byte
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX
    #include <windows.h>
    #undef byte  // Remove Windows SDK byte definition
#endif

#include <memory>
#include <opencv2/opencv.hpp>
#include "Executor.h"
#include <rttr/registration.h>
#include <atomic>


class FileManager {
public:
    /**
     * 读取文件夹下所有的图像
     * @param imagesFolder 图像所在的文件夹
     * @return
     */
    static std::vector<cv::Mat> readImages(const std::string &imagesFolder);

    /**
     * 将图像保存到文件夹下
     * @param imagesFolder
     * @param images
     * @param imageFormat 图像格式
     * @return
     */
    static bool writeImages(const std::string &imagesFolder, const std::vector<cv::Mat> &images,
                            std::string imageFormat = ".png");

    /**
     * 清空文件夹
     * @param folderPath
     */
    static void clearFolder(const std::string &folderPath);

    /**
     * 删除文件夹
     * @param folderPath
     */
    static void deleteFolder(const std::string &folderPath);

    /**
     * 获取文件夹下的所有文件路径
     * @param folderPath
     * @return
     */
    static std::vector<std::string> getFiles(const std::string &folderPath);

    /**
     * 获取文件夹下的所有子文件夹
     * @param folderPath
     * @return
     */
    static std::vector<std::string> getChildrenFolders(const std::string &folderPath);

    /**
     * 将fromFolder下的所有文件夹移动到toFolder文件夹下
     * @param fromFolder
     * @param toFolder
     */
    static void moveChildrenFolders(const std::string &fromFolder, const std::string &toFolder);

    /**
     * 移动文件夹beMovedFolder到另一个文件夹toFolder下
     * @param beMovedFolder
     * @param toFolder
     */
    static void moveFolderTo(const std::string &beMovedFolder, const std::string &toFolder);

    /**
     * 获取文件名
     * @param folderPath
     * @return
     */
    static std::string getFileName(std::string folderPath);

    /**
     * 获取上一级文件夹的名字
     * @param folderPath
     * @return
     */
    static std::string getParentFolderName(std::string folderPath);

    /**
     * 获取文件内容
     * @param filePath
     * @return
     */
    static std::string readFile(const std::string &filePath);

    /**
     * 保存图片，可设置子文件夹名称
     * @param saveImage 是否存图的控制位
     * @param images 图片序列，按顺序给编号并存入同一个子文件夹
     * @param subDirName 子文件夹名称
     * @param pathName 主文件夹路径
     * @return
     */
    static bool
    saveImgsWithSubDir(int saveImage, const std::vector<cv::Mat> &images, const std::string &subDirName,
                       const std::string &pathName,
                       const std::string &preffix = "", const std::string &suffix = "");

    static bool
    saveImgsWithSubDirAsync(int saveImage, const std::vector<cv::Mat> &images, const std::string &subDirName,
                            const std::string &pathName, const std::string &preffix = "",
                            const std::string &suffix = "");

    /**
     * 对输入字符串按照指定关键字进行分割     待测试boost::split差别
     * @param srcStr 待分割字符串
     * @param delimStr 分割字段
     * @param repeatedCharIgnored 忽略重复字段
     * @return
     */
    static std::vector<std::string>
    splitString(std::string srcStr, std::string delimStr, bool repeatedCharIgnored = true);

    /**
     * 文件重命名
     * @param beRenamedFile 待重命名文件路径
     * @param targetNameFile 保存路径
     */
    static void renameFile(const std::string &beRenamedFile, const std::string &targetNameFile);

    /**
     * 创建文件夹，可多级创建
     * @param path 该文件夹目录
     * @param clearExist  当需要创建的目录已存在时，true代表删除重新创建，false代表不重新创建
     * @return
     */
    static bool createFolder(const std::string &path, bool clearExist = false);

    /**
     * 通过rttr的方式加载并解析配置文件
     */
    static bool loadParamFile(const std::string &fileName, rttr::instance param);

    static void toJsontxt(std::string path, rttr::instance obj);

private:
    static std::shared_ptr<Fuxi::Common::Executor> _saveExecutor;
    static std::atomic_int _waittingTaskCount;
    static const int MAX_WAITTING_TASK = 100;


};


#endif //FUXI_FILEMANAGER_H
