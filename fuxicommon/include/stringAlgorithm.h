//
// Created by tian on 2022/4/18.
//

#ifndef FUXIOS_STRINGALGORITHM_H
#define FUXIOS_STRINGALGORITHM_H

#include <string>
#include <vector>
#include <sstream>
#include <algorithm>
#include <iostream>
#include <type_traits>

class stringAlgorithm {
public:
    // Split string function to replace boost::algorithm::split
    static std::vector<std::string> split(const std::string& str, char delimiter) {
        std::vector<std::string> tokens;
        std::stringstream ss(str);
        std::string token;
        while (std::getline(ss, token, delimiter)) {
            if (!token.empty()) {
                tokens.push_back(token);
            }
        }
        return tokens;
    }


    static std::vector<std::string> splitString(const std::string& str, const std::string& delimiters) {
        std::vector<std::string> tokens;
        size_t start = 0;
        size_t end = 0;

        while ((end = str.find_first_of(delimiters, start)) != std::string::npos) {
            if (end != start) {
                tokens.push_back(str.substr(start, end - start));
            }
            start = end + 1;
        }

        if (start < str.length()) {
            tokens.push_back(str.substr(start));
        }

        return tokens;
    }

    // Trim functions to replace boost::trim
    static std::string trim(const std::string& str) {
        auto start = str.begin();
        while (start != str.end() && std::isspace(*start)) {
            start++;
        }
        auto end = str.end();
        do {
            end--;
        } while (end > start && std::isspace(*end));
        return std::string(start, end + 1);
    }

    // Helper function templates for C++14 compatible type conversion
    template<typename T>
    static T convertString(const std::string& str, std::true_type) {
        return std::stoi(str);
    }

    template<typename T>
    static T convertString(const std::string& str, std::false_type) {
        return convertStringImpl<T>(str);
    }

    template<typename T>
    static typename std::enable_if<std::is_same<T, double>::value, T>::type
    convertStringImpl(const std::string& str) {
        return std::stod(str);
    }

    template<typename T>
    static typename std::enable_if<std::is_same<T, float>::value, T>::type
    convertStringImpl(const std::string& str) {
        return std::stof(str);
    }

    template<typename T>
    static typename std::enable_if<std::is_same<T, long>::value, T>::type
    convertStringImpl(const std::string& str) {
        return std::stol(str);
    }

    template<typename T>
    static typename std::enable_if<std::is_same<T, std::string>::value, T>::type
    convertStringImpl(const std::string& str) {
        return str;
    }

    template<class T>
    static std::vector<T> stringToValues(std::string value) {
        std::vector<T> doubles;
        std::vector<std::string> strValues = split(value, ' ');
        for (auto& str : strValues) {
            try {
                doubles.push_back(convertString<T>(str, typename std::is_same<T, int>::type()));
            } catch (...) {
                // Silently ignore conversion errors
            }
        }
        return doubles;
    }

    template<class T>
    static std::string toString(std::vector<T> values) {
        std::string value;
        for (auto& t : values) {
            value.append(std::to_string(t)).append(" ");
        }
        return trim(value);
    }

    // Specialized version for std::string to avoid std::to_string
    static std::string toString(std::vector<std::string> values) {
        std::string value;
        for (auto& t : values) {
            value.append(t).append(" ");
        }
        return trim(value);
    }

    stringAlgorithm()=default;
    ~stringAlgorithm()=default;
    static std::vector<int> getCharacterSegmentsToInt(std::string text);
    static std::vector<std::string> getCharacterSegmentsToString(std::string text);
};

#endif //FUXIOS_STRINGALGORITHM_H
