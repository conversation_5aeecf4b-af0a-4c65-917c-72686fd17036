
#pragma once


#include <string>
#include <vector>
#include <parser/StringTransform.h>
#include <boost/asio/ip/address.hpp>


class StringTransform {

public:
    static int parseInt(std::string value, int defaultValue = 0);

    static int parseInt(std::string value, int defaultValue, bool noKeyPrint);

    static float parseFloat(std::string value, float defaultValue = 0);

    static float parseFloat(std::string value, float defaultValue, bool noKeyPrint);

    static double parseDouble(std::string value, double defaultValue = 0);

    static double parseDouble(std::string value, double defaultValue, bool noKeyPrint);

    static bool parseBoolean(std::string value, bool defaultValue = 0);

    static bool parseBoolean(std::string value, bool defaultValue, bool noKeyPrint);

    static boost::asio::ip::address parseIP(std::string value);

    static boost::asio::ip::address parseIP(
            std::string value,
            boost::asio::ip::address defaultValue,
            bool noKeyPrint);

    static std::vector<std::string> split(const std::string &value, const std::string &separator = ";");
};
