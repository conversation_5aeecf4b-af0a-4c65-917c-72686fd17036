#pragma once

#include <stdio.h>

#include <string.h>

#include <curl/curl.h>
#include <iostream>
#include <boost/filesystem/path.hpp>
#include <glog.h>

namespace Fuxi {
    namespace Common {
        /**
         * 使用标准的http上传和下载文件接口
         */
        class FileUtil {
        public:
            /**
             * 上传文件
             * @param url  上传的http url地址
             * @param file  上传的文件内容
             * @param license 请求头
             * @return
             */
            static bool uploadFile(std::string url, std::string file, std::string license = "");

            /**
             * 从指定的url下载文件
             * @param url  文件下载地址
             * @param file  文件的保存路径
             * @return
             */
            static bool downloadFile(const char *url, const char *file);
        };
    }
}
