#pragma once

#include <memory>
#include <functional>
#include <atomic>
#include <vector>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <future>
#include <chrono>

namespace Fuxi {
    namespace Common {
        // Forward declaration
        class Timer;

        /**
         * 任务的包装对象，可以通过这个对象来取消未执行的任务。
         */
        struct Task {
            Task();

            /**
             * 任务是否已经被取消
             * @return true表示已经取消
             */
            bool isCancle();

            /**
             * 取消任务
             */
            void cancle();

        public:
            friend class Executor;

        private:
            std::atomic_bool _flag;
            std::shared_ptr<Timer> _timer;
            std::mutex _timer_mutex;

            void setTimer(std::shared_ptr<Timer> timer);
        };

        /**
         * A timer class to replace boost::asio::deadline_timer
         */
        class Timer {
        public:
            Timer(std::function<void()> callback, std::chrono::milliseconds duration);
            ~Timer();

            void start();
            void cancel();

        private:
            std::function<void()> _callback;
            std::chrono::milliseconds _duration;
            std::mutex _mutex;
            std::condition_variable _cv;
            std::atomic_bool _cancelled;
            std::thread _thread;

            void run();
        };

        /**
         * 异步消息函数的执行器，使用指定的线程来执行对应的函数，如果函数不退出会导致执行器直接卡主，无法执行新的任务
         */
        class Executor {
        public:
            /**
             * 使用执行的线程数构建执行器
             * @param threadCount  线程数
             */
            explicit Executor(int threadCount);

            /**
             * 使用执行的线程数构建执行器, 在创建时指定线程的优先级
             * @param threadCount  线程数
             */
            Executor(int threadCount, bool enablePriority, int value = 0);

            ~Executor();

            /**
             * 执行带返回值的异步函数
             * @tparam T 返回值
             * @param function  要执行的函数
             * @return std::shared_future<T>对象
             */
            template<class T>
            std::shared_future<T> postTask(std::function<T()> function) {
                std::shared_ptr<std::packaged_task<T()>> task = 
                    std::make_shared<std::packaged_task<T()>>(function);
                std::shared_future<T> fut(task->get_future());
                postTask([task]() { (*task)(); });
                return fut;
            }

            /**
             * 异步执行一个没有返回的函数
             * @param function
             */
            void postTask(std::function<void()> function);

            /**
             * 在指定的秒后执行函数,此函数只是在等待的时间后将函数放入执行器，如果执行器本身在执行任务，真实执行时间就会更长。
             * @param function 待执行的函数
             * @param second   等待时间 秒
             * @param task     待执行的任务，可以通过此任务来取消函数的执行。
             * @return  任务对象
             */
            std::shared_ptr<Task> postTimerTaskSecond(std::function<void()> function, int second = 3,
                                                      std::shared_ptr<Task> task = std::make_shared<Task>());

            /**
             * 执行一个定时的循环间隔的任务 时间单位秒
             * @param function 要执行的函数
             * @param second  时间 单位秒
             * @param task  返回定义的任务对象
             * @return 任务对象
             */
            std::shared_ptr<Task>
            postTimerTaskWithFixRate(std::function<void()> function, int second = 3,
                                     std::shared_ptr<Task> task = std::make_shared<Task>());

            /**
             * 构建一个以毫秒为单位的周期性执行的定时任务
             * @param function  任务函数
             * @param ms   时间 毫秒
             * @param task  任务对象
             * @return   任务对象
             */
            std::shared_ptr<Task>
            postTimerTaskWithFixRateMs(std::function<void()> function, int ms = 3,
                                       std::shared_ptr<Task> task = std::make_shared<Task>());

            /**
             * 创建一个延迟毫秒的定时任务
             * @param function 任务函数
             * @param ms   毫秒
             * @param task 任务对象
             * @return 任务对象
             */
            std::shared_ptr<Task>
            postTimerTaskMilliSecond(std::function<void()> function, int ms = 3,
                                     std::shared_ptr<Task> task = std::make_shared<Task>());

        private:
            std::atomic_bool _running;
            std::mutex _mutex;
            std::condition_variable _condition;
            std::queue<std::function<void()>> _tasks;
            std::vector<std::thread> _threads;

            void workerThread();
        };
    }
}
