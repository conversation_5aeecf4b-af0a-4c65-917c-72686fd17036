#pragma once

#include <functional>
#include <memory>
#include <vector>
#include <mutex>
#include "Executor.h"

namespace Fuxi {
    namespace Common {
        template<class Data, class DataType>
        struct EventData {
            EventData() {

            };

            ~EventData() {

            }

            EventData(Data event, DataType eventType) : _event(event), _eventType(eventType) {

            }

            Data _event;
            DataType _eventType;
        };

        enum CommonType {
            ADD,
            DEL,
            UPDATE
        };

        template<class Event, class EventType>
        struct EventListenerSupport {
            typedef EventData<Event, EventType> EventDataType;

            EventListenerSupport() {
                _executor = std::make_shared<Fuxi::Common::Executor>(1);
            };

            ~EventListenerSupport() {

            };

            void addEventListener(std::function<void(EventData<Event, EventType>)> function) {
                std::lock_guard<std::mutex> lock{_mutex};
                _listeners.push_back(function);
            };

            void notifyEvent(EventDataType eventData) {
                _executor->postTask([this, eventData]() {
                    for (auto function : _listeners) {
                        function(eventData);
                    }
                });
            }

            void notifyEvent(Event event, EventType type) {
                EventDataType eventData(event, type);
                _executor->postTask([this, eventData]() {
                    for (auto function : _listeners) {
                        function(eventData);
                    }
                });
            }

        protected:
            std::vector<std::function<void(EventData<Event, EventType>)>> _listeners;
            std::shared_ptr<Fuxi::Common::Executor> _executor;
            std::mutex _mutex;
        };


    }
}
