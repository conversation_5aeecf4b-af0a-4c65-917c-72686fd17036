//
// Created by tian on 2022/4/18.
//

#include "stringAlgorithm.h"
#include <glog.h>

std::vector<int> stringAlgorithm::getCharacterSegmentsToInt(std::string text) {
    std::vector<int> result;
    std::vector<std::string> strValues = split(text, ' ');
    for (auto& str: strValues) {
        try {
            result.push_back(std::stoi(str));
        } catch (...) {
            LOG(INFO)<<"getCharacterSegmentsToInt";
        }
    }
    return result;
}

std::vector<std::string> stringAlgorithm::getCharacterSegmentsToString(std::string text) {
    return split(text, ' ');
}
