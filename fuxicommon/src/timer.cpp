//
// Created by cobot on 2021/9/6.
//

#include "timer.h"
#include <chrono>



time_t getTimer::getCurrentTimeMilliSec() {
    auto now = std::chrono::system_clock::now().time_since_epoch();
    return std::chrono::duration_cast<std::chrono::milliseconds>(now).count();
}

std::string getTimer::getCurrentTimeString() {
    time_t ttt = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());;
    char tmp[64];
    strftime(tmp, sizeof(tmp), "%Y%m%d%H%M%S", localtime(&ttt));
    return tmp;
}

