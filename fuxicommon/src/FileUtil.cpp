

#include "FileUtil.h"
#include <stdio.h>
#include <string.h>
#include <curl/curl.h>
#include <iostream>
#include <boost/filesystem/path.hpp>

using namespace Fuxi::Common;

bool FileUtil::uploadFile(std::string url, std::string file, std::string license) {
    boost::filesystem::path p(file);
    CURL *curl;
    CURLcode res;

    struct curl_httppost *formpost = NULL;
    struct curl_httppost *lastptr = NULL;
    struct curl_slist *headerlist = NULL;
    license = "fuxi_auth: " + license;
    headerlist = curl_slist_append(headerlist, license.data());
    const char buf[] = "Expect:";

    curl_global_init(CURL_GLOBAL_ALL);

    /* Fill in the file upload field */
    curl_formadd(&formpost,
                 &lastptr,
                 CURLFORM_COPYNAME, "image_file",
                 CURLFORM_FILE, file.data(),
                 CURLFORM_END);

    /* Fill in the filename field */
    curl_formadd(&formpost,
                 &lastptr,
                 CURLFORM_COPYNAME, "filename",
                 CURLFORM_COPYCONTENTS, p.filename().string().data(),
                 CURLFORM_END);

    /* Fill in the submit field too, even if this is rarely needed */
    curl_formadd(&formpost,
                 &lastptr,
                 CURLFORM_COPYNAME, "submit",
                 CURLFORM_COPYCONTENTS, "Submit",
                 CURLFORM_END);

    curl = curl_easy_init();
    /* initalize custom header list (stating that Expect: 100-continue is not
       wanted */
    headerlist = curl_slist_append(headerlist, buf);
    bool ret = false;
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_URL, url.data());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headerlist);
        curl_easy_setopt(curl, CURLOPT_HTTPPOST, formpost);

        /* Perform the request, res will get the return code */
        res = curl_easy_perform(curl);
        /* Check for errors */
        if (res != CURLE_OK) {
            fprintf(stderr, "curl_easy_perform() failed: %s\n",
                    curl_easy_strerror(res));
        } else {
            long http_code = 0;
            curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &http_code);
            if (http_code == 200 && res != CURLE_ABORTED_BY_CALLBACK) {
                ret = true;
            } else {
                ret = false;
            }
        }

        /* always cleanup */
        curl_easy_cleanup(curl);

        /* then cleanup the formpost chain */
        curl_formfree(formpost);
        /* free slist */
        curl_slist_free_all(headerlist);
    }
    curl_global_cleanup();
    return ret;
}

size_t write_data(void *ptr, size_t size, size_t nmemb, FILE *stream) {
    size_t written = fwrite(ptr, size, nmemb, stream);
    return written;
}

bool FileUtil::downloadFile(const char *url, const char *file) {
    CURL *curl;
    FILE *fp;
    CURLcode res;
    res = curl_global_init(CURL_GLOBAL_ALL);
    if (CURLE_OK != res) {
        printf("init libcurl failed.");
        curl_global_cleanup();
        return -1;
    }
    curl = curl_easy_init();
    if (curl) {

        fp = fopen(file, "wb");

        res = curl_easy_setopt(curl, CURLOPT_URL, url);
        if (res != CURLE_OK) {
            fclose(fp);
            curl_easy_cleanup(curl);
            return false;
        }
        res = curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_data);
        if (res != CURLE_OK) {
            fclose(fp);
            curl_easy_cleanup(curl);
            return false;
        }
        res = curl_easy_setopt(curl, CURLOPT_WRITEDATA, fp);
        if (res != CURLE_OK) {
            fclose(fp);
            curl_easy_cleanup(curl);
            return false;
        }

        res = curl_easy_perform(
                curl);
        fclose(fp);
        if (res != CURLE_OK) {
            fprintf(stderr, "curl_easy_perform() failed: %s\n", curl_easy_strerror(res));
            curl_easy_cleanup(curl);
            return false;
        }

        curl_easy_cleanup(curl);
        curl_global_cleanup();
        return true;
    } else {
        curl_easy_cleanup(curl);
        curl_global_cleanup();
    }
    return false;
}
