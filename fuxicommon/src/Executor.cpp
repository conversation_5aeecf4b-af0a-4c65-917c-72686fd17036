#include "Executor.h"
#include "glog.h"
#include <chrono>
#include <thread>
#include <functional>

using namespace Fuxi::Common;

// Timer implementation
Timer::Timer(std::function<void()> callback, std::chrono::milliseconds duration)
    : _callback(callback), _duration(duration), _cancelled(false) {
}

Timer::~Timer() {
    cancel();
    if (_thread.joinable()) {
        // 检查是否试图从自己的线程中join自己（这会导致死锁）
        if (_thread.get_id() != std::this_thread::get_id()) {
            try {
                _thread.join();
            } catch (const std::system_error& e) {
                // Log the error but don't throw from destructor
                LOG(ERROR) << "Timer destructor: thread join failed: " << e.what();
            }
        } else {
            // 如果是从自己的线程调用析构函数，则分离线程而不是join
            _thread.detach();
        }
    }
}

void Timer::start() {
    _thread = std::thread(&Timer::run, this);
}

void Timer::cancel() {
    {
        std::lock_guard<std::mutex> lock(_mutex);
        _cancelled = true;
    }
    _cv.notify_all();
}

void Timer::run() {
    std::function<void()> callback_to_execute;
    {
        std::unique_lock<std::mutex> lock(_mutex);
        if (!_cv.wait_for(lock, _duration, [this] { return _cancelled.load(); })) {
            // If we're here, the wait timed out and wasn't canceled
            if (!_cancelled) {
                // 保存回调函数的副本，避免在回调中销毁Timer对象时出现问题
                callback_to_execute = _callback;
            }
        }
    }

    // 在锁外执行回调函数
    if (callback_to_execute) {
        callback_to_execute();
    }
}

// Task implementation
Task::Task() : _flag(false) {
}

bool Task::isCancle() {
    return _flag;
}

void Task::cancle() {
    _flag = true;
    std::lock_guard<std::mutex> lock(_timer_mutex);
    if (_timer) {
        _timer->cancel();
    }
}

void Task::setTimer(std::shared_ptr<Timer> timer) {
    std::lock_guard<std::mutex> lock(_timer_mutex);
    _timer = timer;
}

// Executor implementation
void enableHightPolicy(std::thread* daThread, int value) {
#ifdef __GNUC__
    int retcode;
    int policy;

    pthread_t threadID = (pthread_t) daThread->native_handle();

    struct sched_param param;

    if ((retcode = pthread_getschedparam(threadID, &policy, &param)) != 0) {
        errno = retcode;
        perror("pthread_getschedparam");
        exit(EXIT_FAILURE);
    }

    LOG(INFO) << "INHERITED: ";
    LOG(INFO) << "policy=" << ((policy == SCHED_FIFO) ? "SCHED_FIFO" :
                              (policy == SCHED_RR) ? "SCHED_RR" :
                              (policy == SCHED_OTHER) ? "SCHED_OTHER" :
                              "???")
             << ", priority=" << param.sched_priority;

    policy = SCHED_FIFO;
    param.sched_priority = value;

    if ((retcode = pthread_setschedparam(threadID, policy, &param)) != 0) {
        errno = retcode;
        LOG(FATAL) << ("pthread_setschedparam");
    }

    LOG(INFO) << "  CHANGED: ";
    LOG(INFO) << "policy=" << ((policy == SCHED_FIFO) ? "SCHED_FIFO" :
                              (policy == SCHED_RR) ? "SCHED_RR" :
                              (policy == SCHED_OTHER) ? "SCHED_OTHER" :
                              "???")
             << ", priority=" << param.sched_priority;
#endif
}

Executor::Executor(int threadCount) : _running(true) {
    for (int i = 0; i < threadCount; ++i) {
        _threads.emplace_back(&Executor::workerThread, this);
    }
}

Executor::Executor(int threadCount, bool enablePriority, int value) : _running(true) {
    for (int i = 0; i < threadCount; ++i) {
        _threads.emplace_back(&Executor::workerThread, this);
        if (enablePriority) {
            enableHightPolicy(&_threads.back(), value);
        }
    }
}

Executor::~Executor() {
    {
        std::unique_lock<std::mutex> lock(_mutex);
        _running = false;
    }
    _condition.notify_all();
    
    for (auto& thread : _threads) {
        if (thread.joinable()) {
            thread.join();
        }
    }
}

void Executor::workerThread() {
    while (true) {
        std::function<void()> task;
        {
            std::unique_lock<std::mutex> lock(_mutex);
            _condition.wait(lock, [this] { 
                return !_tasks.empty() || !_running; 
            });
            
            if (!_running && _tasks.empty()) {
                return;
            }
            
            task = std::move(_tasks.front());
            _tasks.pop();
        }
        
        task();
    }
}

void Executor::postTask(std::function<void()> function) {
    {
        std::unique_lock<std::mutex> lock(_mutex);
        _tasks.emplace(std::move(function));
    }
    _condition.notify_one();
}

std::shared_ptr<Task>
Executor::postTimerTaskSecond(std::function<void()> function, int second,
                             std::shared_ptr<Task> task) {
    auto duration = std::chrono::seconds(second);
    auto timer = std::make_shared<Timer>(
        [function, task]() {
            if (!task->isCancle()) {
                function();
            } else {
                LOG(INFO) << "task was cancle";
            }
        },
        std::chrono::duration_cast<std::chrono::milliseconds>(duration)
    );
    
    task->setTimer(timer);
    timer->start();
    return task;
}

std::shared_ptr<Task>
Executor::postTimerTaskMilliSecond(std::function<void()> function, int millisecond,
                                 std::shared_ptr<Task> task) {
    auto duration = std::chrono::milliseconds(millisecond);
    auto timer = std::make_shared<Timer>(
        [function, task]() {
            if (!task->isCancle()) {
                function();
            } else {
                LOG(INFO) << "task was cancle";
            }
        },
        duration
    );
    
    task->setTimer(timer);
    timer->start();
    return task;
}

std::shared_ptr<Task>
Executor::postTimerTaskWithFixRate(std::function<void()> function, int second,
                                 std::shared_ptr<Task> task) {
    auto timer = std::make_shared<Timer>(
        [this, function, second, task]() {
            if (!task->isCancle()) {
                function();
                postTimerTaskWithFixRate(function, second, task);
            } else {
                LOG(INFO) << "fix rate task is cancle";
            }
        },
        std::chrono::milliseconds(second * 1000)
    );
    
    task->setTimer(timer);
    timer->start();
    return task;
}

std::shared_ptr<Task>
Executor::postTimerTaskWithFixRateMs(std::function<void()> function, int ms,
                                   std::shared_ptr<Task> task) {
    auto timer = std::make_shared<Timer>(
        [this, function, ms, task]() {
            if (!task->isCancle()) {
                try {
                    function();
                    // 给一点时间让当前timer完全结束，避免竞态条件
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));
                    postTimerTaskWithFixRateMs(function, ms, task);
                } catch (const std::exception& e) {
                    LOG(ERROR) << "Exception in fixed rate task: " << e.what();
                }
            } else {
                LOG(INFO) << "fix rate task is cancle";
            }
        },
        std::chrono::milliseconds(ms)
    );

    task->setTimer(timer);
    timer->start();
    return task;
}
