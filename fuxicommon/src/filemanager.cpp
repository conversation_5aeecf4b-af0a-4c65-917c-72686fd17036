// Fix for C++17 std::byte conflict with Windows SDK byte
#ifdef _WIN32
    #define WIN32_LEAN_AND_MEAN
    #define NOMINMAX
    #include <windows.h>
    #undef byte  // Remove Windows SDK byte definition
#endif

#include "filemanager.h"
#include "timer.h"
#include "JSON.h"

// Based on C++ standard version, include appropriate filesystem header
#if defined(__cplusplus) && __cplusplus >= 201703L
    // C++17 or later
    #include <filesystem>
    namespace fs = std::filesystem;
#else
    // C++14 or earlier
    #define _SILENCE_EXPERIMENTAL_FILESYSTEM_DEPRECATION_WARNING
    #include <experimental/filesystem>
    namespace fs = std::experimental::filesystem;
#endif

#include <glog.h>
#include <algorithm>
#include <chrono>
#include <ctime>
#include <sstream>
#include <fstream>
#include <string>
#include <iostream>

std::shared_ptr<Fuxi::Common::Executor> FileManager::_saveExecutor = std::make_shared<Fuxi::Common::Executor>(8);
std::atomic_int FileManager::_waittingTaskCount{0};

std::vector<cv::Mat> FileManager::readImages(const std::string &imagesFolder) {
    std::vector<cv::Mat> images;
    if (!fs::exists(imagesFolder)) {
        LOG(ERROR) << "folder path : " << imagesFolder << " is not exist .";
        return images;
    }
    
    for (const auto& entry : fs::directory_iterator(imagesFolder)) {
        if (!fs::is_regular_file(entry.status())) {
            continue;
        }
        cv::Mat img = cv::imread(entry.path().string());
        images.push_back(img);
    }
    return images;
}

std::vector<std::string> FileManager::getFiles(const std::string &folderPath) {
    std::vector<std::string> files;
    if (!fs::exists(folderPath)) {
        LOG(ERROR) << "folder path : " << folderPath << " is not exist .";
        return files;
    }
    
    for (const auto& entry : fs::directory_iterator(folderPath)) {
        if (!fs::is_regular_file(entry.status())) {
            continue;
        }
        files.push_back(entry.path().string());
    }
    return files;
}

bool FileManager::writeImages(const std::string &imagesFolder, const std::vector<cv::Mat> &images, std::string imageFormat) {
    if (!fs::exists(imagesFolder)) {
        LOG(ERROR) << "to folder path : " << imagesFolder << " is not exist";
        if (!fs::create_directories(imagesFolder)) {
            LOG(ERROR) << "create to folder failed .";
            return false;
        }
    }
    
    // Get current time as string
    auto now = std::chrono::system_clock::now();
    auto now_c = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&now_c), "%Y%m%d%H%M%S");
    std::string time = ss.str();
    
    for (int i = 0; i < images.size(); ++i) {
        std::string imageName = imagesFolder + "/" + time + "_" + std::to_string(i) + imageFormat;
        cv::imwrite(imageName, images.at(i));
    }
    return true;
}

void FileManager::clearFolder(const std::string &folderPath) {
    if (!fs::exists(folderPath)) {
        LOG(ERROR) << "folder path : " << folderPath << " is not exist .";
        return;
    }
    fs::remove_all(folderPath);
    fs::create_directories(folderPath);
}

void FileManager::deleteFolder(const std::string &folderPath) {
    if (!fs::exists(folderPath)) {
        LOG(ERROR) << "folder path : " << folderPath << " is not exist .";
        return;
    }
    fs::remove_all(folderPath);
}

std::vector<std::string> FileManager::getChildrenFolders(const std::string &folderPath) {
    if (!fs::exists(folderPath)) {
        LOG(ERROR) << "folder path : " << folderPath << " is not exist .";
        return std::vector<std::string>();
    }
    
    std::vector<std::string> childrenFoldersName;
    for (const auto& entry : fs::directory_iterator(folderPath)) {
        if (!fs::is_directory(entry.status())) {
            continue;
        }
        childrenFoldersName.push_back(entry.path().string());
    }
    return childrenFoldersName;
}

void FileManager::moveChildrenFolders(const std::string &fromFolder, const std::string &toFolder) {
    if (fromFolder == toFolder) {
        LOG(ERROR) << "No need to move .";
        return;
    }
    if (!fs::exists(fromFolder)) {
        LOG(ERROR) << "from folder path : " << fromFolder << " is not exist .";
        return;
    }
    if (!fs::exists(toFolder)) {
        LOG(ERROR) << "to folder path : " << toFolder << " is not exist . try to create.";
        if (!fs::create_directories(toFolder)) {
            LOG(ERROR) << "create to folder failed .";
            return;
        }
    }
    
    for (const auto& entry : fs::directory_iterator(fromFolder)) {
        if (!fs::is_directory(entry.status())) {
            continue;
        }
        
        fs::path destPath = fs::path(toFolder) / entry.path().filename();
        if (!fs::exists(destPath)) {
            if (!fs::create_directories(destPath)) {
                LOG(ERROR) << "create " << destPath << " failed .";
                return;
            }
        }
        
        for (const auto& subEntry : fs::directory_iterator(entry.path())) {
            try {
                fs::path targetPath = destPath / subEntry.path().filename();
                fs::copy_file(subEntry.path(), targetPath, 
                              fs::copy_options::overwrite_existing);
            } catch (std::exception& e) {
                LOG(ERROR) << "move error : from " << subEntry.path() << " to "
                           << destPath / subEntry.path().filename();
                LOG(ERROR) << "error info is : " << e.what();
            }
        }
        fs::remove_all(entry.path());
    }
}

void FileManager::moveFolderTo(const std::string &beMovedFolder, const std::string &toFolder) {
    fs::path p(beMovedFolder);
    if (!fs::exists(p)) {
        LOG(ERROR) << "be moved folder : " << beMovedFolder << " is not exist .";
        return;
    }
    if (!fs::exists(toFolder)) {
        LOG(ERROR) << "to folder path : " << toFolder << " is not exist . try to create.";
        if (!fs::create_directories(toFolder)) {
            LOG(ERROR) << "create to folder failed .";
            return;
        }
    }
    
    fs::path destPath = fs::path(toFolder) / p.filename();
    if (!fs::exists(destPath)) {
        if (!fs::create_directories(destPath)) {
            LOG(ERROR) << "create " << destPath << " failed .";
            return;
        }
    }
    
    for (const auto& entry : fs::directory_iterator(p)) {
        try {
            fs::path targetPath = destPath / entry.path().filename();
            fs::copy_file(entry.path(), targetPath, 
                          fs::copy_options::overwrite_existing);
        } catch (std::exception& e) {
            LOG(ERROR) << "move error : from " << entry.path() << " to "
                       << destPath / entry.path().filename();
            LOG(ERROR) << "error info is : " << e.what();
        }
    }
    fs::remove_all(p);
}

std::string FileManager::getFileName(std::string folderPath) {
    fs::path p(folderPath);
    return p.filename().string();
}

std::string FileManager::getParentFolderName(std::string folderPath) {
    fs::path p(folderPath);
    return p.parent_path().string();
}

std::string FileManager::readFile(const std::string &filePath) {
    std::stringstream ss;
    std::string line;
    std::ifstream myfile(filePath);
    if (myfile.is_open()) {
        while (getline(myfile, line)) {
            ss << line;
        }
        myfile.close();
    }
    std::string text = ss.str();

    return text;
}

bool FileManager::saveImgsWithSubDir(int saveImage, const std::vector<cv::Mat> &images, const std::string &subDirName,
                                     const std::string &pathName, const std::string &preffix,
                                     const std::string &suffix) {
    if (saveImage == 1) {
        std::string save_dir = pathName;
        if (!subDirName.empty()) {
            save_dir = save_dir + "/" + subDirName;
        }
        // 判斷文件夾是否存在，不存在就創建
        createFolder(save_dir);

        for (int i = 0; i < images.size(); ++i) {
            auto now = std::chrono::system_clock::now();
            auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();
            
            std::string save_path =
                    save_dir + "/" + preffix + getTimer::getCurrentTimeString() + "_" + std::to_string(ms) +
                    "_" + std::to_string(i) + suffix + ".jpg";
            if (!cv::imwrite(save_path, images[i])) {
                LOG(INFO) << "保存失败: " << save_path;
                return false;
            }
        }
        return true;
    }

    return false;
}

bool FileManager::saveImgsWithSubDirAsync(int saveImage, const std::vector<cv::Mat> &images, const std::string &subDirName,
                                     const std::string &pathName, const std::string &preffix,
                                     const std::string &suffix) {
    if (saveImage == 1 && _waittingTaskCount < MAX_WAITTING_TASK) {
        _waittingTaskCount++;
        LOG(INFO) << "save image task num: " << _waittingTaskCount;
        _saveExecutor->postTask([=]() {
            FileManager::saveImgsWithSubDir(saveImage, images, subDirName, pathName, preffix, suffix);
            _waittingTaskCount--;
        });

        return true;
    } else {
        return false;
    }
}

std::vector<std::string> FileManager::splitString(std::string srcStr, std::string delimStr, bool repeatedCharIgnored) {
    std::vector<std::string> resultStringVector;
    
    // Replace all delimiters with the first delimiter character for consistency
    std::replace_if(srcStr.begin(), srcStr.end(), 
                    [&](const char &c) {
                        return delimStr.find(c) != std::string::npos;
                    }, 
                    delimStr.at(0));
    
    // Split the string
    std::string token;
    std::istringstream tokenStream(srcStr);
    char delimiter = delimStr.at(0);
    
    while (std::getline(tokenStream, token, delimiter)) {
        if (!token.empty() || !repeatedCharIgnored) {
            resultStringVector.push_back(token);
        }
    }
    
    return resultStringVector;
}

void FileManager::renameFile(const std::string &beRenamedFile, const std::string &targetNameFile) {
    fs::path np(targetNameFile);

    // Check if parent directory exists
    std::string dir = np.parent_path().string();
    if (!fs::exists(dir)) {
        createFolder(dir, false);
    }
    
    fs::path op(beRenamedFile);
    fs::rename(op, np);
}

bool FileManager::createFolder(const std::string &path, bool clearExist) {
    if (fs::exists(path)) {
        if (clearExist) {
            LOG(INFO) << "文件夹已存在,删除.";
            fs::remove_all(path);
        } else {
            LOG(INFO) << "文件夹已存在.";
            return true;
        }
    }

    if (fs::create_directories(path)) {
        return true;
    } else {
        LOG(ERROR) << "create image folder failed . ImageFolder path is : " << path;
        return false;
    }
}

bool FileManager::loadParamFile(const std::string &fileName, rttr::instance param) {
    if (fileName.empty()) {
        LOG(ERROR) << "FileManager::loadParamFile is null ";
        return false;
    }
    //读json文件//
    std::string text = readFile(fileName);
    if (text.empty()) {
        LOG(ERROR) << "FileManager::loadParamFile is null ";
        return false;
    }
    LOG(INFO) << text;
    Fuxi::Common::JSON::parseJSON(text, param);
    LOG(INFO) << Fuxi::Common::JSON::toJSONString(param);
    return true;
}

void FileManager::toJsontxt(std::string path, rttr::instance obj) {
    auto hkstr = Fuxi::Common::JSON::toJSONString(obj);
    std::cout << hkstr << std::endl;
    std::ofstream ofs;
    ofs.open(path, std::ios::out);
    ofs << hkstr;
    ofs << std::endl;
    ofs.close();
}