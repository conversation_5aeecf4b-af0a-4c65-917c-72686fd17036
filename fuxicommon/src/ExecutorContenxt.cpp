#include "ExecutorContenxt.h"

// Define the thread_local variable
namespace Fuxi {
    namespace Common {
        thread_local std::map<std::string, anyns::any> _valueMap;
    }
}

Fuxi::Common::ExecutorContenxt::ExecutorContenxt() {
    // Initialize with a default single-threaded executor
    _actionExecutor = std::make_shared<Executor>(1);
}

Fuxi::Common::ExecutorContenxt::~ExecutorContenxt() {

}
