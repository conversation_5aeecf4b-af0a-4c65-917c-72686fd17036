//
// Created by kmc on 19-4-22.
//

#include "parser/NumberUtils.h"
#include "glog.h"




int NumberUtils::bytesToInt(std::vector<unsigned char> bytes) {
    if (bytes.size() == 4) {
        int addr = bytes[0] & 0xFF;
        addr |= ((bytes[1] << 8) & 0xFF00);
        addr |= ((bytes[2] << 16) & 0xFF0000);
        addr |= ((bytes[3] << 24) & 0xFF000000);
        return addr;
    } else {
        LOG(ERROR) << "wrong byte size ,the bytes leng only 4";
        return -1;
    }
}

std::vector<unsigned char> NumberUtils::intToBytes(int i) {
    std::vector<unsigned char> bytes(4);
    bytes[0] = (unsigned char) (0xff & i);
    bytes[1] = (unsigned char) ((0xff00 & i) >> 8);
    bytes[2] = (unsigned char) ((0xff0000 & i) >> 16);
    bytes[3] = (unsigned char) ((0xff000000 & i) >> 24);
    return bytes;
}


