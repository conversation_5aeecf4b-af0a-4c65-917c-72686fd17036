

#include "parser/FileIOOperation.h"




const int MAX_LEN = 256;

std::vector<std::string> FileIOOperation::readLines(const std::string& filePath)
{
    std::vector<std::string> read_lines;

    if(filePath.empty())
    {
        return read_lines;
    }

    std::ifstream input(filePath.c_str());

    while(!input.eof())
    {
        char line[MAX_LEN]={0};
        input.getline(line, MAX_LEN);
        read_lines.push_back(line);
    }

    return read_lines;
}



int FileIOOperation::writeLines(const std::string& filePath, std::vector<std::string> lines)
{
    int ret = 0 ;
    std::ofstream output(filePath.c_str());

    for(auto line: lines)
    {
        output << line <<std::endl;
    }

    return ret;
}


