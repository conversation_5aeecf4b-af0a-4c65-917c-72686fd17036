

#include "CSVParserImpl.h"
#include <iostream>
#include <fstream>
#include <boost/algorithm/string.hpp>
using namespace std;

CSVParserImpl::CSVParserImpl() {

}
std::vector<std::vector<std::string>> CSVParserImpl::parseCSV(const std::string& file)
{
    std::vector<std::vector<std::string>> ret;
    ifstream in(file.c_str());
    if(!in.is_open())
    {
        return ret;
    }
    vector<string> vec;
    string line;
    while(getline(in,line))
    {
        boost::split(vec,line,boost::is_any_of((",")));
        ret.push_back(vec);
        vec.clear();
    }
    return ret;
}
boost::shared_ptr<CSVParser> CSVParser::create() {
    return boost::shared_ptr<CSVParser>(new CSVParserImpl());
}

CSVParser::~CSVParser() {

}