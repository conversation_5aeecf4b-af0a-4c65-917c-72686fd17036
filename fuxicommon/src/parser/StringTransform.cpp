
#include <boost/lexical_cast.hpp>
#include "glog.h"
#include <exception>
#include <boost/algorithm/string.hpp>
#include "parser/StringTransform.h"
using namespace std;

bool StringTransform::parseBoolean(string value, bool defaultValue) {

    if (boost::iequals("true",value) || boost::equals("1", value))
    {
        return true;
    }
    else if(boost::iequals("false",value) || boost::equals("0", value))
    {
        return false;
    }
    else
    {
        return defaultValue;
    }
}
bool StringTransform::parseBoolean(string value, bool defaultValue, bool noKeyPrint) {
    if (boost::iequals("true",value) || boost::equals("1", value))
    {
        return true;
    }
    else if (boost::iequals("false",value) || boost::equals("0", value))
    {
        return false;
    }
    else
    {
        if (noKeyPrint)
            LOG(ERROR) << "parseBoolean fail  value =" << value;
        return defaultValue;
    }
}

float StringTransform::parseFloat(string value, float defaultValue) {
    try {
        return boost::lexical_cast<float>(value);
    } catch (exception& e) {
        //LOG_ERROR << "parseFloat fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}

float StringTransform::parseFloat(string value, float defaultValue, bool noKeyPrint) {
    try {
        return boost::lexical_cast<float>(value);
    } catch (exception& e) {
        if (noKeyPrint)
            LOG(ERROR) << "parseFloat fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}

double StringTransform::parseDouble(string value, double defaultValue, bool noKeyPrint) {
    try {
        return boost::lexical_cast<double>(value);
    } catch (exception& e) {
        if (noKeyPrint)
            LOG(ERROR) << "parseDouble fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}

double StringTransform::parseDouble(string value, double defaultValue) {
    try {
        return boost::lexical_cast<double>(value);
    } catch (exception& e) {
        LOG(ERROR) << "parseDouble fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}

int StringTransform::parseInt(string value, int defaultValue, bool noKeyPrint) {
    try {
        return boost::lexical_cast<int>(value);
    } catch (exception& e) {
        if (noKeyPrint)
            LOG(ERROR) << "parseInt fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}

int StringTransform::parseInt(string value, int defaultValue) {
    try {
        return boost::lexical_cast<int>(value);
    } catch (exception& e) {
        //LOG_ERROR << "parseInt fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}

boost::asio::ip::address StringTransform::parseIP(std::string value) {
    try {
        return boost::asio::ip::address::from_string(value);
    }catch (exception& e){
        LOG(ERROR) << "parseIp fail  value =" << value << " what="<<e.what();;
        boost::asio::ip::address emptyAddress;
        return emptyAddress;
    }
}

boost::asio::ip::address StringTransform::parseIP(std::string value, boost::asio::ip::address defaultValue, bool noKeyPrint) {
    try {
        return boost::asio::ip::address::from_string(value);
    }catch (exception& e){
        if (noKeyPrint)
            LOG(ERROR) << "parseIp fail  value =" << value << " what="<<e.what();;
        return defaultValue;
    }
}


vector<string> StringTransform::split(const string &value, const string &separator)
{
    vector<string> retVector;
    try {
        if (!value.empty()) {
            boost::split(retVector, value, boost::is_any_of(separator));
        }
    } catch (exception &e) {
        LOG(ERROR)<<"split failed for " << value << " exception:" << e.what();
        return retVector;
    }

    return retVector;
}

