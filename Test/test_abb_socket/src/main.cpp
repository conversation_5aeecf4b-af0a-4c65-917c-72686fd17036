#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include "AbbRobotDriver.h"
#include <glog.h>

int main() {
    // 初始化日志
    // google::InitGoogleLogging("test_abb_socket");
    
    std::cout << "Testing ABB Robot Driver with FuxiCore Socket..." << std::endl;
    
    try {
        // 创建 ABB 机器人驱动实例
        auto abbDriver = std::make_unique<AbbRobotDriverEgmUpdate>();
        
        // 测试连接
        std::string robotIP = "*************";  // 默认 ABB 机器人 IP
        int robotPort = 80;  // 默认端口
        
        std::cout << "Attempting to connect to ABB robot at " << robotIP << ":" << robotPort << std::endl;
        
        bool connected = abbDriver->connect(robotIP, robotPort);
        
        if (connected) {
            std::cout << "Successfully connected to ABB robot!" << std::endl;
            
            // 测试连接状态
            if (abbDriver->isConnect()) {
                std::cout << "Robot connection status: Connected" << std::endl;
            } else {
                std::cout << "Robot connection status: Disconnected" << std::endl;
            }
            
            // 等待一段时间
            std::this_thread::sleep_for(std::chrono::seconds(2));
            
            // 测试断开连接
            std::cout << "Disconnecting from robot..." << std::endl;
            abbDriver->disconnect();
            
        } else {
            std::cout << "Failed to connect to ABB robot" << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Exception occurred: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "Test completed." << std::endl;
    return 0;
}
