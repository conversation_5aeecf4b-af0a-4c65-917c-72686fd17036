# ExecutorContext类测试程序

## 📋 测试概述

这是一个针对fuxicommon库中ExecutorContext类的全面测试程序，验证了线程上下文存储器的各种功能。

## 🎯 测试目标

验证ExecutorContext类的以下功能：
- 基本的键值对存储和检索
- 键的存在性检查和删除操作
- 当前线程的直接值操作
- 智能指针类型的存储和检索
- 容器类型（vector）的存储和检索
- 并发访问的线程安全性
- 错误处理机制

## 🔧 构建和运行

### 构建
```bash
cd test_executor_context
cmake -B build -S . -DCMAKE_BUILD_TYPE=Debug
cmake --build build --config Debug
```

### 运行
```bash
build/Debug/test_executor_context.exe
```

## ✅ 测试内容

### 测试1: 基本设置和获取 (testBasicSetAndGet)
- **目标**: 验证基本的setValue和getValue功能
- **方法**: 设置和获取int和string类型的值
- **验证点**: 
  - setValue返回true
  - getValue返回正确的值
  - 支持不同的基本数据类型

### 测试2: 存在性检查和删除 (testExistsAndRemove)
- **目标**: 验证键的存在性检查和删除功能
- **方法**: 设置值后检查存在性，删除后再次检查
- **验证点**:
  - isExists正确识别存在的键
  - isExists正确识别不存在的键
  - remove操作正确删除键值对

### 测试3: 当前值操作 (testCurrentValueOperations)
- **目标**: 验证当前线程的直接值操作
- **方法**: 使用setCurrentValue、getCurrentValue等方法
- **验证点**:
  - setCurrentValue正确设置值
  - getCurrentValue正确获取值
  - isCurrentExists正确检查存在性
  - removeCurrent正确删除值

### 测试4: 智能指针值 (testSharedPtrValues)
- **目标**: 验证shared_ptr类型的存储和检索
- **方法**: 存储和检索shared_ptr<int>和shared_ptr<string>
- **验证点**:
  - 智能指针正确存储
  - 智能指针正确检索
  - 指针内容保持一致

### 测试5: 容器值 (testVectorValues)
- **目标**: 验证vector容器类型的存储和检索
- **方法**: 存储和检索vector<int>
- **验证点**:
  - 容器正确存储
  - 容器正确检索
  - 容器内容保持一致

### 测试6: 并发访问 (testConcurrentAccess)
- **目标**: 验证多线程环境下的线程安全性
- **方法**: 启动多个线程同时进行设置、获取、检查操作
- **验证点**:
  - 多线程操作不会产生竞态条件
  - 所有线程操作都能正确完成
  - 数据一致性得到保证

### 测试7: 错误处理 (testErrorHandling)
- **目标**: 验证错误情况的处理
- **方法**: 尝试获取不存在的键
- **验证点**:
  - 获取不存在的键返回默认值
  - 不会抛出异常或崩溃

## 🏗️ 技术实现

### 依赖库
- **glog**: Google日志库，用于日志输出
- **fuxicommon**: 核心库，包含ExecutorContext和Executor类实现

### 关键特性验证
1. **类型安全**: 模板化的类型检查和转换
2. **线程安全**: 基于Executor的异步操作
3. **内存管理**: 智能指针的正确处理
4. **容器支持**: STL容器的完整支持
5. **错误处理**: 优雅的错误处理机制

## 📊 ExecutorContext架构分析

### 核心组件
- **thread_local存储**: 使用thread_local的_valueMap提供线程隔离
- **类型擦除**: 使用std::any/boost::any实现类型擦除
- **异步操作**: 通过Executor实现线程间的安全访问
- **类型特征**: 使用is_shared_ptr特征类限制支持的类型

### 操作模式
1. **异步模式**: 通过Executor执行操作（setValue、getValue等）
2. **同步模式**: 直接操作当前线程存储（setCurrentValue、getCurrentValue等）

## 🔍 测试覆盖

### 已覆盖的功能
- ✅ `setValue<T>()` - 异步设置值
- ✅ `getValue<T>()` - 异步获取值
- ✅ `isExists()` - 异步检查存在性
- ✅ `remove()` - 异步删除
- ✅ `setCurrentValue<T>()` - 同步设置当前值
- ✅ `getCurrentValue<T>()` - 同步获取当前值
- ✅ `isCurrentExists()` - 同步检查当前存在性
- ✅ `removeCurrent()` - 同步删除当前值

### 支持的类型
- ✅ 基本类型 (int, string等)
- ✅ 智能指针 (shared_ptr, weak_ptr)
- ✅ 容器类型 (vector)
- ✅ 自定义类型（通过is_shared_ptr特征类）

## 🎉 结论

ExecutorContext类的核心功能全部正常工作：
- **线程安全存储** - 完全正常
- **类型安全操作** - 精确可靠
- **异步访问机制** - 安全有效
- **并发支持** - 稳定可靠

该测试验证了ExecutorContext类可以安全地用于生产环境中的线程上下文存储需求。

---

**测试时间**: 2024年12月  
**测试平台**: Windows 11  
**编译器**: MSVC 19.29  
**测试状态**: 🎯 待验证
