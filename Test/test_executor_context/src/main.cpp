#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <cassert>
#include <memory>
#include <string>

#include "ExecutorContenxt.h"
#include "Executor.h"
#include "glog.h"

using namespace Fuxi::Common;
using namespace std::chrono_literals;

class ExecutorContextTester {
private:
    std::shared_ptr<Executor> executor;
    std::unique_ptr<ExecutorContenxt> context;

public:
    ExecutorContextTester() {
        executor = std::make_shared<Executor>(2);  // 2 threads
        context = std::make_unique<ExecutorContenxt>();
    }

    void testBasicSetAndGet() {
        std::cout << "\n=== Testing Basic Set and Get ===" << std::endl;

        // Test setting and getting integer value
        int testValue = 42;
        bool setResult = context->setValue("test_int", testValue);
        assert(setResult);
        std::cout << "✓ Set integer value: " << testValue << std::endl;

        int retrievedValue = context->getValue<int>("test_int");
        std::cout << "✓ Retrieved integer value: " << retrievedValue << std::endl;
        assert(retrievedValue == testValue);

        // Test setting and getting string value
        std::string testString = "Hello, ExecutorContext!";
        setResult = context->setValue("test_string", testString);
        assert(setResult);
        std::cout << "✓ Set string value: " << testString << std::endl;

        std::string retrievedString = context->getValue<std::string>("test_string");
        std::cout << "✓ Retrieved string value: " << retrievedString << std::endl;
        assert(retrievedString == testString);

        std::cout << "✓ Basic set and get test passed!" << std::endl;
    }

    void testExistsAndRemove() {
        std::cout << "\n=== Testing Exists and Remove ===" << std::endl;

        // Set a test value
        int testValue = 123;
        context->setValue("test_exists", testValue);

        // Test exists
        bool exists = context->isExists("test_exists");
        assert(exists);
        std::cout << "✓ Key exists check passed" << std::endl;

        // Test non-existent key
        bool notExists = context->isExists("non_existent_key");
        assert(!notExists);
        std::cout << "✓ Non-existent key check passed" << std::endl;

        // Test remove
        context->remove("test_exists");
        bool existsAfterRemove = context->isExists("test_exists");
        assert(!existsAfterRemove);
        std::cout << "✓ Remove operation passed" << std::endl;

        std::cout << "✓ Exists and remove test passed!" << std::endl;
    }

    void testCurrentValueOperations() {
        std::cout << "\n=== Testing Current Value Operations ===" << std::endl;
        
        // Test setCurrentValue and getCurrentValue
        int currentTestValue = 999;
        bool setResult = context->setCurrentValue("current_test", currentTestValue);
        assert(setResult);
        std::cout << "✓ Set current value: " << currentTestValue << std::endl;
        
        int retrievedCurrentValue = context->getCurrentValue<int>("current_test");
        std::cout << "✓ Retrieved current value: " << retrievedCurrentValue << std::endl;
        assert(retrievedCurrentValue == currentTestValue);
        
        // Test isCurrentExists
        bool currentExists = context->isCurrentExists("current_test");
        assert(currentExists);
        std::cout << "✓ Current exists check passed" << std::endl;
        
        // Test removeCurrent
        context->removeCurrent("current_test");
        bool existsAfterCurrentRemove = context->isCurrentExists("current_test");
        assert(!existsAfterCurrentRemove);
        std::cout << "✓ Current remove operation passed" << std::endl;
        
        std::cout << "✓ Current value operations test passed!" << std::endl;
    }

    void testSharedPtrValues() {
        std::cout << "\n=== Testing Shared Pointer Values ===" << std::endl;

        // Test with shared_ptr<int>
        auto sharedInt = std::make_shared<int>(777);
        bool setResult = context->setValue("shared_int", sharedInt);
        assert(setResult);
        std::cout << "✓ Set shared_ptr<int> value: " << *sharedInt << std::endl;

        auto retrievedSharedInt = context->getValue<std::shared_ptr<int>>("shared_int");
        assert(retrievedSharedInt != nullptr);
        assert(*retrievedSharedInt == 777);
        std::cout << "✓ Retrieved shared_ptr<int> value: " << *retrievedSharedInt << std::endl;

        // Test with shared_ptr<string>
        auto sharedString = std::make_shared<std::string>("Shared String Test");
        setResult = context->setValue("shared_string", sharedString);
        assert(setResult);
        std::cout << "✓ Set shared_ptr<string> value: " << *sharedString << std::endl;

        auto retrievedSharedString = context->getValue<std::shared_ptr<std::string>>("shared_string");
        assert(retrievedSharedString != nullptr);
        assert(*retrievedSharedString == "Shared String Test");
        std::cout << "✓ Retrieved shared_ptr<string> value: " << *retrievedSharedString << std::endl;

        std::cout << "✓ Shared pointer values test passed!" << std::endl;
    }

    void testVectorValues() {
        std::cout << "\n=== Testing Vector Values ===" << std::endl;

        // Test with vector<int>
        std::vector<int> testVector = {1, 2, 3, 4, 5};
        bool setResult = context->setValue("test_vector", testVector);
        assert(setResult);
        std::cout << "✓ Set vector<int> with " << testVector.size() << " elements" << std::endl;

        auto retrievedVector = context->getValue<std::vector<int>>("test_vector");
        assert(retrievedVector.size() == testVector.size());
        for (size_t i = 0; i < testVector.size(); ++i) {
            assert(retrievedVector[i] == testVector[i]);
        }
        std::cout << "✓ Retrieved vector<int> with matching elements" << std::endl;

        std::cout << "✓ Vector values test passed!" << std::endl;
    }

    void testConcurrentAccess() {
        std::cout << "\n=== Testing Concurrent Access ===" << std::endl;

        std::atomic<int> completedTasks{0};
        const int numTasks = 10;

        // Launch multiple tasks that set and get values concurrently
        std::vector<std::thread> threads;

        for (int i = 0; i < numTasks; ++i) {
            threads.emplace_back([this, i, &completedTasks]() {
                std::string key = "concurrent_" + std::to_string(i);
                int value = i * 10;

                // Set value
                bool setResult = context->setValue(key, value);
                assert(setResult);

                // Get value
                int retrievedValue = context->getValue<int>(key);
                assert(retrievedValue == value);

                // Check exists
                bool exists = context->isExists(key);
                assert(exists);

                completedTasks++;
                std::cout << "✓ Concurrent task " << i << " completed" << std::endl;
            });
        }

        // Wait for all threads to complete
        for (auto& thread : threads) {
            thread.join();
        }

        assert(completedTasks.load() == numTasks);
        std::cout << "✓ All " << numTasks << " concurrent tasks completed successfully" << std::endl;

        std::cout << "✓ Concurrent access test passed!" << std::endl;
    }

    void testErrorHandling() {
        std::cout << "\n=== Testing Error Handling ===" << std::endl;

        // Test getting non-existent key
        int nonExistentValue = context->getValue<int>("non_existent");
        std::cout << "✓ Non-existent key returned default value: " << nonExistentValue << std::endl;

        // Test getting non-existent current value
        int nonExistentCurrentValue = context->getCurrentValue<int>("non_existent_current");
        std::cout << "✓ Non-existent current key returned default value: " << nonExistentCurrentValue << std::endl;

        std::cout << "✓ Error handling test passed!" << std::endl;
    }
};

int main() {
    // Initialize Google logging
    google::InitGoogleLogging("executor_context_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting ExecutorContext Tests..." << std::endl;
    
    try {
        ExecutorContextTester tester;
        
        tester.testBasicSetAndGet();
        tester.testExistsAndRemove();
        tester.testCurrentValueOperations();
        tester.testSharedPtrValues();
        tester.testVectorValues();
        tester.testConcurrentAccess();
        tester.testErrorHandling();
        
        std::cout << "\n🎉 All ExecutorContext tests passed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
