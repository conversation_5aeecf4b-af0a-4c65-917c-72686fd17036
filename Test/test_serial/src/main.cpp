#include <iostream>
#include <thread>
#include <chrono>
#include <string>
#include <vector>
#include <iomanip>
#include "SerialManager.h"

using namespace fuxicore;
using namespace std::chrono_literals;

void print_separator(const std::string& title) {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "  " << title << std::endl;
    std::cout << std::string(50, '=') << std::endl;
}

void print_test_result(const std::string& test_name, bool success) {
    std::cout << "[" << (success ? "PASS" : "FAIL") << "] " << test_name << std::endl;
}

void test_serial_utils() {
    print_separator("Serial Utils Tests");
    
    // 测试波特率验证
    bool baud_test1 = SerialUtils::is_valid_baud_rate(9600);
    bool baud_test2 = !SerialUtils::is_valid_baud_rate(12345);
    print_test_result("Valid baud rate (9600)", baud_test1);
    print_test_result("Invalid baud rate (12345)", baud_test2);
    
    // 测试标准波特率
    auto standard_rates = SerialUtils::get_standard_baud_rates();
    std::cout << "Standard baud rates: ";
    for (size_t i = 0; i < standard_rates.size(); ++i) {
        std::cout << standard_rates[i];
        if (i < standard_rates.size() - 1) std::cout << ", ";
    }
    std::cout << std::endl;
    
    // 测试十六进制转换
    std::vector<uint8_t> test_data = {0x48, 0x65, 0x6C, 0x6C, 0x6F}; // "Hello"
    std::string hex_string = SerialUtils::bytes_to_hex_string(test_data, true);
    auto converted_back = SerialUtils::hex_string_to_bytes(hex_string);
    bool hex_test = (test_data == converted_back);
    print_test_result("Hex conversion (" + hex_string + ")", hex_test);
    
    // 测试校验和
    uint8_t checksum = SerialUtils::calculate_checksum(test_data);
    std::cout << "Checksum: 0x" << std::hex << std::uppercase << static_cast<int>(checksum) << std::dec << std::endl;
    
    // 测试CRC16
    uint16_t crc16 = SerialUtils::calculate_crc16(test_data);
    std::cout << "CRC16: 0x" << std::hex << std::uppercase << crc16 << std::dec << std::endl;
    
    // 测试CRC32
    uint32_t crc32 = SerialUtils::calculate_crc32(test_data);
    std::cout << "CRC32: 0x" << std::hex << std::uppercase << crc32 << std::dec << std::endl;
    
    // 测试数据分割
    std::vector<uint8_t> delimited_data = {0x01, 0x02, 0x0A, 0x03, 0x04, 0x0A, 0x05};
    auto split_result = SerialUtils::split_by_delimiter(delimited_data, 0x0A);
    print_test_result("Split by delimiter (found " + std::to_string(split_result.size()) + " parts)", split_result.size() == 2);
    
    auto length_split = SerialUtils::split_by_length(test_data, 2);
    print_test_result("Split by length (found " + std::to_string(length_split.size()) + " parts)", length_split.size() == 3);
    
    // 测试转义
    std::vector<uint8_t> escape_test = {0x01, 0x7D, 0x02, 0x7D, 0x7D, 0x03};
    auto escaped = SerialUtils::escape_data(escape_test, 0x7D);
    auto unescaped = SerialUtils::unescape_data(escaped, 0x7D);
    bool escape_test_result = (escape_test == unescaped);
    print_test_result("Escape/Unescape data", escape_test_result);
}

void test_serial_config() {
    print_separator("Serial Configuration Tests");
    
    // 测试默认配置
    SerialConfig default_config;
    std::cout << "Default configuration:" << std::endl;
    std::cout << "  Port: " << default_config.port_name << std::endl;
    std::cout << "  Baud Rate: " << default_config.baud_rate << std::endl;
    std::cout << "  Data Bits: " << static_cast<int>(default_config.data_bits) << std::endl;
    std::cout << "  Parity: " << static_cast<int>(default_config.parity) << std::endl;
    std::cout << "  Stop Bits: " << static_cast<int>(default_config.stop_bits) << std::endl;
    std::cout << "  Flow Control: " << static_cast<int>(default_config.flow_control) << std::endl;
    std::cout << "  Read Timeout: " << default_config.read_timeout_ms << "ms" << std::endl;
    std::cout << "  Write Timeout: " << default_config.write_timeout_ms << "ms" << std::endl;
    std::cout << "  Buffer Size: " << default_config.buffer_size << " bytes" << std::endl;
    
    // 测试自定义配置
    SerialConfig custom_config;
    custom_config.port_name = "COM3";
    custom_config.baud_rate = 115200;
    custom_config.data_bits = 8;
    custom_config.parity = SerialParity::Even;
    custom_config.stop_bits = SerialStopBits::Two;
    custom_config.flow_control = SerialFlowControl::Hardware;
    custom_config.read_timeout_ms = 2000;
    custom_config.write_timeout_ms = 2000;
    custom_config.buffer_size = 8192;
    custom_config.enable_dtr = true;
    custom_config.enable_rts = true;
    
    std::cout << "\nCustom configuration:" << std::endl;
    std::cout << "  Port: " << custom_config.port_name << std::endl;
    std::cout << "  Baud Rate: " << custom_config.baud_rate << std::endl;
    std::cout << "  Data Bits: " << static_cast<int>(custom_config.data_bits) << std::endl;
    std::cout << "  Parity: " << static_cast<int>(custom_config.parity) << std::endl;
    std::cout << "  Stop Bits: " << static_cast<int>(custom_config.stop_bits) << std::endl;
    std::cout << "  Flow Control: " << static_cast<int>(custom_config.flow_control) << std::endl;
    std::cout << "  DTR Enabled: " << (custom_config.enable_dtr ? "Yes" : "No") << std::endl;
    std::cout << "  RTS Enabled: " << (custom_config.enable_rts ? "Yes" : "No") << std::endl;
}

void test_port_discovery() {
    print_separator("Port Discovery Tests");
    
    try {
        // 获取可用端口列表
        auto ports = SerialManager::get_available_ports();
        std::cout << "Found " << ports.size() << " serial ports:" << std::endl;
        
        for (const auto& port : ports) {
            std::cout << "\n  Port: " << port.port_name << std::endl;
            std::cout << "    Description: " << port.description << std::endl;
            std::cout << "    Manufacturer: " << port.manufacturer << std::endl;
            std::cout << "    Hardware ID: " << port.hardware_id << std::endl;
            std::cout << "    Available: " << (port.is_available ? "Yes" : "No") << std::endl;
        }
        
        // 获取端口名称列表
        auto port_names = SerialManager::get_port_names();
        std::cout << "\nPort names only: ";
        for (size_t i = 0; i < port_names.size(); ++i) {
            std::cout << port_names[i];
            if (i < port_names.size() - 1) std::cout << ", ";
        }
        std::cout << std::endl;
        
        // 测试特定端口可用性
        std::vector<std::string> test_ports = {"COM1", "COM2", "COM3", "COM4"};
        for (const auto& port : test_ports) {
            bool available = SerialManager::is_port_available(port);
            std::cout << "  " << port << ": " << (available ? "Available" : "Not available") << std::endl;
        }
        
    } catch (const std::exception& e) {
        std::cout << "Error during port discovery: " << e.what() << std::endl;
    }
}

void test_serial_state() {
    print_separator("Serial State Tests");
    
    SerialConfig config;
    config.port_name = "COM1"; // 使用一个可能不存在的端口进行测试
    config.read_timeout_ms = 100;
    config.write_timeout_ms = 100;
    
    SerialManager serial(config);
    
    // 测试初始状态
    SerialState initial_state = serial.get_state();
    print_test_result("Initial state is Closed", initial_state == SerialState::Closed);
    print_test_result("Initially not open", !serial.is_open());
    
    // 测试配置获取
    const auto& retrieved_config = serial.get_config();
    bool config_test = (retrieved_config.port_name == config.port_name && 
                       retrieved_config.read_timeout_ms == config.read_timeout_ms);
    print_test_result("Configuration retrieval", config_test);
}

void test_concepts() {
    print_separator("C++20 Concepts Tests");
    
    // 测试不同数据类型是否符合 SerialData concept
    bool string_concept = SerialData<std::string>;
    bool vector_concept = SerialData<std::vector<uint8_t>>;
    bool int_concept = SerialData<int>;
    bool double_concept = SerialData<double>;
    
    print_test_result("std::string satisfies SerialData concept", string_concept);
    print_test_result("std::vector<uint8_t> satisfies SerialData concept", vector_concept);
    print_test_result("int satisfies SerialData concept", int_concept);
    print_test_result("double satisfies SerialData concept", double_concept);
    
    std::cout << "\nC++20 Concepts validation completed at compile time." << std::endl;
}

void test_callbacks() {
    print_separator("Callback Tests");
    
    SerialConfig config;
    config.port_name = "COM1";
    config.read_timeout_ms = 100;
    
    SerialManager serial(config);
    
    bool data_callback_set = false;
    bool error_callback_set = false;
    bool opened_callback_set = false;
    bool closed_callback_set = false;
    
    // 设置回调函数
    serial.set_data_received_callback([&](const std::vector<uint8_t>& data) {
        std::cout << "Data received: " << data.size() << " bytes" << std::endl;
        data_callback_set = true;
    });
    
    serial.set_error_callback([&](const std::string& error) {
        std::cout << "Error occurred: " << error << std::endl;
        error_callback_set = true;
    });
    
    serial.set_opened_callback([&]() {
        std::cout << "Port opened successfully" << std::endl;
        opened_callback_set = true;
    });
    
    serial.set_closed_callback([&]() {
        std::cout << "Port closed" << std::endl;
        closed_callback_set = true;
    });
    
    print_test_result("Data received callback set", true);
    print_test_result("Error callback set", true);
    print_test_result("Opened callback set", true);
    print_test_result("Closed callback set", true);
}

void test_serial_operations() {
    print_separator("Serial Operations Tests");
    
    SerialConfig config;
    config.port_name = "COM1"; // 使用一个可能不存在的端口
    config.read_timeout_ms = 100;
    config.write_timeout_ms = 100;
    
    SerialManager serial(config);
    
    // 测试打开不存在的端口（预期失败）
    try {
        bool opened = serial.open(1000); // 1秒超时
        print_test_result("Open non-existent port (expected to fail)", !opened);
    } catch (const SerialException& e) {
        std::cout << "Expected exception when opening non-existent port: " << e.what() << std::endl;
        print_test_result("Exception handling for non-existent port", true);
    }
    
    // 测试异步打开
    try {
        auto future_result = serial.open_async();
        // 不等待结果，因为端口可能不存在
        print_test_result("Async open initiated", true);
    } catch (const std::exception& e) {
        std::cout << "Async open exception: " << e.what() << std::endl;
    }
    
    // 测试在未打开状态下的操作
    try {
        std::string test_data = "Hello, Serial!";
        bool send_result = serial.send_data(test_data);
        print_test_result("Send data when port closed (expected to fail)", !send_result);
    } catch (const std::exception& e) {
        std::cout << "Expected exception when sending to closed port: " << e.what() << std::endl;
    }
    
    try {
        auto data = serial.receive_data(100);
        print_test_result("Receive data when port closed (expected to fail)", false);
    } catch (const SerialException& e) {
        std::cout << "Expected exception when receiving from closed port: " << e.what() << std::endl;
        print_test_result("Exception handling for receive on closed port", true);
    }
}

void test_data_types() {
    print_separator("Data Type Tests");
    
    SerialConfig config;
    config.port_name = "COM1";
    
    SerialManager serial(config);
    
    // 测试不同数据类型的发送（在未连接状态下，只测试编译通过）
    std::string string_data = "Hello";
    std::vector<uint8_t> vector_data = {0x48, 0x65, 0x6C, 0x6C, 0x6F};
    int int_data = 12345;
    double double_data = 3.14159;
    
    // 这些调用在端口未打开时会返回false，但能验证模板编译正确
    bool string_send = serial.send_data(string_data);
    bool vector_send = serial.send_data(vector_data);
    bool int_send = serial.send_data(int_data);
    bool double_send = serial.send_data(double_data);
    
    print_test_result("String data send (compile test)", true);
    print_test_result("Vector data send (compile test)", true);
    print_test_result("Integer data send (compile test)", true);
    print_test_result("Double data send (compile test)", true);
    
    // 测试异步发送
    try {
        auto future1 = serial.send_data_async(string_data);
        auto future2 = serial.send_data_async(vector_data);
        print_test_result("Async send operations initiated", true);
    } catch (const std::exception& e) {
        std::cout << "Async send exception: " << e.what() << std::endl;
    }
}

void test_control_signals() {
    print_separator("Control Signals Tests");
    
    SerialConfig config;
    config.port_name = "COM1";
    
    SerialManager serial(config);
    
    // 在端口未打开时测试控制信号（预期返回false）
    bool dtr_set = serial.set_dtr(true);
    bool rts_set = serial.set_rts(true);
    bool dtr_get = serial.get_dtr();
    bool rts_get = serial.get_rts();
    bool cts_get = serial.get_cts();
    bool dsr_get = serial.get_dsr();
    bool ring_get = serial.get_ring();
    bool rlsd_get = serial.get_rlsd();
    
    print_test_result("DTR control (port closed, expected false)", !dtr_set);
    print_test_result("RTS control (port closed, expected false)", !rts_set);
    print_test_result("Signal reading when port closed", true); // 只要不崩溃就算通过
}

void test_buffer_operations() {
    print_separator("Buffer Operations Tests");
    
    SerialConfig config;
    config.port_name = "COM1";
    
    SerialManager serial(config);
    
    // 在端口未打开时测试缓冲区操作（预期返回false或0）
    bool flush_input = serial.flush_input_buffer();
    bool flush_output = serial.flush_output_buffer();
    bool flush_all = serial.flush_all_buffers();
    size_t input_count = serial.get_input_buffer_count();
    size_t output_count = serial.get_output_buffer_count();
    
    print_test_result("Flush operations when port closed", !flush_input && !flush_output && !flush_all);
    print_test_result("Buffer count when port closed", input_count == 0 && output_count == 0);
}

void test_exception_handling() {
    print_separator("Exception Handling Tests");
    
    SerialConfig config;
    config.port_name = "INVALID_PORT";
    
    SerialManager serial(config);
    
    // 测试配置更改异常
    try {
        serial.open(); // 这应该失败
    } catch (const SerialException& e) {
        std::cout << "Caught SerialException: " << e.what() << std::endl;
        std::cout << "Error code: " << e.error_code() << std::endl;
        print_test_result("SerialException handling", true);
    } catch (const std::exception& e) {
        std::cout << "Caught std::exception: " << e.what() << std::endl;
        print_test_result("Exception handling", true);
    }
    
    // 测试在打开状态下更改配置（模拟）
    SerialConfig new_config;
    new_config.port_name = "COM2";
    
    try {
        serial.set_config(new_config); // 在关闭状态下应该成功
        print_test_result("Config change when port closed", true);
    } catch (const SerialException& e) {
        std::cout << "Unexpected exception: " << e.what() << std::endl;
        print_test_result("Config change when port closed", false);
    }
}

int main() {
    std::cout << "FuxiCore Serial Manager Test Suite" << std::endl;
    std::cout << "===================================" << std::endl;
    
    try {
        // 运行所有测试
        test_serial_utils();
        test_serial_config();
        test_port_discovery();
        test_serial_state();
        test_concepts();
        test_callbacks();
        test_serial_operations();
        test_data_types();
        test_control_signals();
        test_buffer_operations();
        test_exception_handling();
        
        print_separator("Test Summary");
        std::cout << "All tests completed successfully!" << std::endl;
        std::cout << "\nNote: Some tests are expected to fail when no serial ports are available." << std::endl;
        std::cout << "This is normal behavior and indicates proper error handling." << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "\nUnexpected error during testing: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}