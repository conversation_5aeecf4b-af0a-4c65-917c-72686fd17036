#ifndef LICENSEMANAGERUI_H
#define LICENSEMANAGERUI_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>
#include <QLineEdit>
#include <QTextEdit>
#include <QPushButton>
#include <QComboBox>
#include <QDateEdit>
#include <QListWidget>
#include <QCheckBox>
#include <QProgressBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>
#include <QFileDialog>
#include <QMessageBox>
#include <QTimer>
#include <QSplitter>
#include <QTableWidget>
#include <QHeaderView>
#include <QApplication>
#include <QClipboard>
#include <QDateTime>
#include <QDir>
#include <QStandardPaths>
#include <memory>

#include "LicenseManager.h"
#include "qaesencryption.h"

using namespace Fuxi::Core;

class LicenseManagerUI : public QMainWindow
{
    Q_OBJECT

public:
    explicit LicenseManagerUI(QWidget *parent = nullptr);
    ~LicenseManagerUI();

private slots:
    // License generation slots
    void onGenerateLicense();
    void onCopyLicense();
    void onSaveLicenseToFile();
    void onClearGenerationForm();
    
    // License validation slots
    void onValidateLicense();
    void onInstallLicense();
    void onLoadLicenseFromFile();
    void onRemoveLicense();
    void onClearValidationForm();
    
    // License info slots
    void onRefreshLicenseInfo();
    void onExportLicenseInfo();
    
    // Feature management slots
    void onAddFeature();
    void onRemoveFeature();
    void onClearFeatures();
    
    // File operations
    void onOpenLicenseFile();
    void onSaveLicenseFile();
    void onExportReport();
    
    // License Manager signals
    void onLicenseValidated(LicenseManager::ValidationResult result);
    void onLicenseExpiring(int daysRemaining);
    void onFeatureAccessDenied(const QString& featureName);
    
    // UI update slots
    void updateLicenseStatus();
    void updateFeatureList();
    void updateHardwareInfo();

    // Debug and testing slots
    void onTestEncryption();
    void onTestQAESEncryption();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupGenerationTab();
    void setupValidationTab();
    void setupInfoTab();
    void setupLogTab();
    
    void connectSignals();
    void loadSettings();
    void saveSettings();
    
    void addLogMessage(const QString& message, const QString& type = "INFO");
    void showStatusMessage(const QString& message, int timeout = 3000);
    void updateValidationResult(LicenseManager::ValidationResult result);
    
    QString formatLicenseType(LicenseManager::LicenseType type) const;
    QString formatValidationResult(LicenseManager::ValidationResult result) const;
    QColor getResultColor(LicenseManager::ValidationResult result) const;

private:
    // Core components
    std::unique_ptr<LicenseManager> m_licenseManager;
    
    // Main UI components
    QTabWidget* m_tabWidget;
    QStatusBar* m_statusBar;
    QProgressBar* m_progressBar;
    QLabel* m_statusLabel;
    
    // Generation tab components
    QWidget* m_generationTab;
    QLineEdit* m_customerNameEdit;
    QLineEdit* m_companyNameEdit;
    QComboBox* m_licenseTypeCombo;
    QDateEdit* m_expiryDateEdit;
    QListWidget* m_featuresList;
    QLineEdit* m_newFeatureEdit;
    QTextEdit* m_generatedLicenseEdit;
    QPushButton* m_generateBtn;
    QPushButton* m_copyLicenseBtn;
    QPushButton* m_saveLicenseBtn;
    QPushButton* m_clearGenBtn;
    
    // Validation tab components
    QWidget* m_validationTab;
    QTextEdit* m_licenseInputEdit;
    QLabel* m_validationResultLabel;
    QLabel* m_validationStatusLabel;
    QPushButton* m_validateBtn;
    QPushButton* m_installBtn;
    QPushButton* m_loadLicenseBtn;
    QPushButton* m_removeLicenseBtn;
    QPushButton* m_clearValBtn;
    
    // Info tab components
    QWidget* m_infoTab;
    QTableWidget* m_licenseInfoTable;
    QTableWidget* m_hardwareInfoTable;
    QTableWidget* m_featuresTable;
    QPushButton* m_refreshInfoBtn;
    QPushButton* m_exportInfoBtn;
    
    // Log tab components
    QWidget* m_logTab;
    QTextEdit* m_logEdit;
    QPushButton* m_clearLogBtn;
    QPushButton* m_saveLogBtn;
    
    // Settings
    QString m_lastLicenseDir;
    QString m_defaultCustomer;
    QString m_defaultCompany;
};

#endif // LICENSEMANAGERUI_H
