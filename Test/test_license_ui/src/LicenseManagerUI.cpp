#include "LicenseManagerUI.h"
#include <QSettings>
#include <QSplitter>
#include <QGroupBox>
#include <QGridLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>

LicenseManagerUI::LicenseManagerUI(QWidget *parent)
    : QMainWindow(parent)
    , m_licenseManager(std::make_unique<LicenseManager>())
    , m_tabWidget(nullptr)
    , m_statusBar(nullptr)
    , m_progressBar(nullptr)
    , m_statusLabel(nullptr)
{
    setWindowTitle("License Manager - Registration & Validation Tool");
    setWindowIcon(QIcon(":/icons/license.png"));
    resize(1000, 700);
    
    setupUI();
    setupMenuBar();
    setupStatusBar();
    connectSignals();
    loadSettings();
    
    // Initialize with current license info
    updateLicenseStatus();
    updateHardwareInfo();
    
    addLogMessage("License Manager UI initialized successfully");
}

LicenseManagerUI::~LicenseManagerUI()
{
    saveSettings();
}

void LicenseManagerUI::setupUI()
{
    m_tabWidget = new QTabWidget(this);
    setCentralWidget(m_tabWidget);
    
    setupGenerationTab();
    setupValidationTab();
    setupInfoTab();
    setupLogTab();
}

void LicenseManagerUI::setupMenuBar()
{
    QMenuBar* menuBar = this->menuBar();
    
    // File menu
    QMenu* fileMenu = menuBar->addMenu("&File");
    
    QAction* openAction = new QAction("&Open License File...", this);
    openAction->setShortcut(QKeySequence::Open);
    connect(openAction, &QAction::triggered, this, &LicenseManagerUI::onOpenLicenseFile);
    fileMenu->addAction(openAction);
    
    QAction* saveAction = new QAction("&Save License File...", this);
    saveAction->setShortcut(QKeySequence::Save);
    connect(saveAction, &QAction::triggered, this, &LicenseManagerUI::onSaveLicenseFile);
    fileMenu->addAction(saveAction);
    
    fileMenu->addSeparator();
    
    QAction* exportAction = new QAction("&Export Report...", this);
    connect(exportAction, &QAction::triggered, this, &LicenseManagerUI::onExportReport);
    fileMenu->addAction(exportAction);
    
    fileMenu->addSeparator();
    
    QAction* exitAction = new QAction("E&xit", this);
    exitAction->setShortcut(QKeySequence::Quit);
    connect(exitAction, &QAction::triggered, this, &QWidget::close);
    fileMenu->addAction(exitAction);
    
    // Tools menu
    QMenu* toolsMenu = menuBar->addMenu("&Tools");
    
    QAction* refreshAction = new QAction("&Refresh License Info", this);
    refreshAction->setShortcut(QKeySequence::Refresh);
    connect(refreshAction, &QAction::triggered, this, &LicenseManagerUI::onRefreshLicenseInfo);
    toolsMenu->addAction(refreshAction);
    
    QAction* hardwareAction = new QAction("&Update Hardware Info", this);
    connect(hardwareAction, &QAction::triggered, this, &LicenseManagerUI::updateHardwareInfo);
    toolsMenu->addAction(hardwareAction);

    toolsMenu->addSeparator();

    QAction* testAction = new QAction("&Test Encryption", this);
    connect(testAction, &QAction::triggered, this, &LicenseManagerUI::onTestEncryption);
    toolsMenu->addAction(testAction);

    QAction* testQAESAction = new QAction("Test &QAESEncryption", this);
    connect(testQAESAction, &QAction::triggered, this, &LicenseManagerUI::onTestQAESEncryption);
    toolsMenu->addAction(testQAESAction);
    
    // Help menu
    QMenu* helpMenu = menuBar->addMenu("&Help");
    
    QAction* aboutAction = new QAction("&About", this);
    connect(aboutAction, &QAction::triggered, [this]() {
        QMessageBox::about(this, "About License Manager",
            "License Manager UI v1.0\n\n"
            "A comprehensive tool for license generation and validation\n"
            "Built with Qt 5.15 and AES encryption\n\n"
            "© 2025 Fuxi Core");
    });
    helpMenu->addAction(aboutAction);
}

void LicenseManagerUI::setupStatusBar()
{
    m_statusBar = statusBar();
    
    m_statusLabel = new QLabel("Ready");
    m_statusBar->addWidget(m_statusLabel);
    
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMaximumWidth(200);
    m_statusBar->addPermanentWidget(m_progressBar);
    
    // License status indicator
    QLabel* licenseStatusLabel = new QLabel();
    licenseStatusLabel->setObjectName("licenseStatus");
    m_statusBar->addPermanentWidget(licenseStatusLabel);
}

void LicenseManagerUI::setupGenerationTab()
{
    m_generationTab = new QWidget();
    m_tabWidget->addTab(m_generationTab, "License Generation");
    
    QHBoxLayout* mainLayout = new QHBoxLayout(m_generationTab);
    
    // Left panel - Input form
    QGroupBox* inputGroup = new QGroupBox("License Information");
    QGridLayout* inputLayout = new QGridLayout(inputGroup);
    
    // Customer information
    inputLayout->addWidget(new QLabel("Customer Name:"), 0, 0);
    m_customerNameEdit = new QLineEdit();
    m_customerNameEdit->setPlaceholderText("Enter customer name");
    inputLayout->addWidget(m_customerNameEdit, 0, 1);
    
    inputLayout->addWidget(new QLabel("Company Name:"), 1, 0);
    m_companyNameEdit = new QLineEdit();
    m_companyNameEdit->setPlaceholderText("Enter company name");
    inputLayout->addWidget(m_companyNameEdit, 1, 1);
    
    // License type
    inputLayout->addWidget(new QLabel("License Type:"), 2, 0);
    m_licenseTypeCombo = new QComboBox();
    m_licenseTypeCombo->addItem("Trial", static_cast<int>(LicenseManager::LicenseType::Trial));
    m_licenseTypeCombo->addItem("Standard", static_cast<int>(LicenseManager::LicenseType::Standard));
    m_licenseTypeCombo->addItem("Professional", static_cast<int>(LicenseManager::LicenseType::Professional));
    m_licenseTypeCombo->addItem("Enterprise", static_cast<int>(LicenseManager::LicenseType::Enterprise));
    inputLayout->addWidget(m_licenseTypeCombo, 2, 1);
    
    // Expiry date
    inputLayout->addWidget(new QLabel("Expiry Date:"), 3, 0);
    m_expiryDateEdit = new QDateEdit();
    m_expiryDateEdit->setDate(QDate::currentDate().addDays(30));
    m_expiryDateEdit->setCalendarPopup(true);
    m_expiryDateEdit->setMinimumDate(QDate::currentDate());
    inputLayout->addWidget(m_expiryDateEdit, 3, 1);
    
    // Features section
    QGroupBox* featuresGroup = new QGroupBox("Enabled Features");
    QVBoxLayout* featuresLayout = new QVBoxLayout(featuresGroup);
    
    QHBoxLayout* featureInputLayout = new QHBoxLayout();
    m_newFeatureEdit = new QLineEdit();
    m_newFeatureEdit->setPlaceholderText("Enter feature name");
    QPushButton* addFeatureBtn = new QPushButton("Add");
    QPushButton* removeFeatureBtn = new QPushButton("Remove");
    QPushButton* clearFeaturesBtn = new QPushButton("Clear All");
    
    featureInputLayout->addWidget(m_newFeatureEdit);
    featureInputLayout->addWidget(addFeatureBtn);
    featureInputLayout->addWidget(removeFeatureBtn);
    featureInputLayout->addWidget(clearFeaturesBtn);
    
    m_featuresList = new QListWidget();
    m_featuresList->addItem("basic_features");
    m_featuresList->addItem("advanced_features");
    
    featuresLayout->addLayout(featureInputLayout);
    featuresLayout->addWidget(m_featuresList);
    
    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_generateBtn = new QPushButton("Generate License");
    m_generateBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }");
    m_clearGenBtn = new QPushButton("Clear Form");
    
    buttonLayout->addWidget(m_generateBtn);
    buttonLayout->addWidget(m_clearGenBtn);
    buttonLayout->addStretch();
    
    // Left panel layout
    QVBoxLayout* leftLayout = new QVBoxLayout();
    leftLayout->addWidget(inputGroup);
    leftLayout->addWidget(featuresGroup);
    leftLayout->addLayout(buttonLayout);
    leftLayout->addStretch();
    
    QWidget* leftPanel = new QWidget();
    leftPanel->setLayout(leftLayout);
    leftPanel->setMaximumWidth(400);
    
    // Right panel - Generated license
    QGroupBox* outputGroup = new QGroupBox("Generated License");
    QVBoxLayout* outputLayout = new QVBoxLayout(outputGroup);
    
    m_generatedLicenseEdit = new QTextEdit();
    m_generatedLicenseEdit->setReadOnly(true);
    m_generatedLicenseEdit->setPlaceholderText("Generated license will appear here...");
    outputLayout->addWidget(m_generatedLicenseEdit);
    
    QHBoxLayout* outputButtonLayout = new QHBoxLayout();
    m_copyLicenseBtn = new QPushButton("Copy to Clipboard");
    m_saveLicenseBtn = new QPushButton("Save to File");
    
    outputButtonLayout->addWidget(m_copyLicenseBtn);
    outputButtonLayout->addWidget(m_saveLicenseBtn);
    outputButtonLayout->addStretch();
    
    outputLayout->addLayout(outputButtonLayout);
    
    // Add panels to main layout
    mainLayout->addWidget(leftPanel);
    mainLayout->addWidget(outputGroup, 1);
    
    // Connect signals
    connect(addFeatureBtn, &QPushButton::clicked, this, &LicenseManagerUI::onAddFeature);
    connect(removeFeatureBtn, &QPushButton::clicked, this, &LicenseManagerUI::onRemoveFeature);
    connect(clearFeaturesBtn, &QPushButton::clicked, this, &LicenseManagerUI::onClearFeatures);
    connect(m_generateBtn, &QPushButton::clicked, this, &LicenseManagerUI::onGenerateLicense);
    connect(m_clearGenBtn, &QPushButton::clicked, this, &LicenseManagerUI::onClearGenerationForm);
    connect(m_copyLicenseBtn, &QPushButton::clicked, this, &LicenseManagerUI::onCopyLicense);
    connect(m_saveLicenseBtn, &QPushButton::clicked, this, &LicenseManagerUI::onSaveLicenseToFile);
    
    // Enable/disable buttons initially
    m_copyLicenseBtn->setEnabled(false);
    m_saveLicenseBtn->setEnabled(false);
}

void LicenseManagerUI::setupValidationTab()
{
    m_validationTab = new QWidget();
    m_tabWidget->addTab(m_validationTab, "License Validation");

    QVBoxLayout* mainLayout = new QVBoxLayout(m_validationTab);

    // Input section
    QGroupBox* inputGroup = new QGroupBox("License Input");
    QVBoxLayout* inputLayout = new QVBoxLayout(inputGroup);

    QLabel* inputLabel = new QLabel("Enter License Key:");
    m_licenseInputEdit = new QTextEdit();
    m_licenseInputEdit->setMaximumHeight(100);
    m_licenseInputEdit->setPlaceholderText("Paste license key here or load from file...");

    inputLayout->addWidget(inputLabel);
    inputLayout->addWidget(m_licenseInputEdit);

    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_validateBtn = new QPushButton("Validate License");
    m_validateBtn->setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }");
    m_installBtn = new QPushButton("Install License");
    m_installBtn->setStyleSheet("QPushButton { background-color: #FF9800; color: white; font-weight: bold; }");
    m_loadLicenseBtn = new QPushButton("Load from File");
    m_removeLicenseBtn = new QPushButton("Remove License");
    m_removeLicenseBtn->setStyleSheet("QPushButton { background-color: #F44336; color: white; font-weight: bold; }");
    m_clearValBtn = new QPushButton("Clear");

    buttonLayout->addWidget(m_validateBtn);
    buttonLayout->addWidget(m_installBtn);
    buttonLayout->addWidget(m_loadLicenseBtn);
    buttonLayout->addWidget(m_removeLicenseBtn);
    buttonLayout->addWidget(m_clearValBtn);
    buttonLayout->addStretch();

    // Results section
    QGroupBox* resultsGroup = new QGroupBox("Validation Results");
    QGridLayout* resultsLayout = new QGridLayout(resultsGroup);

    resultsLayout->addWidget(new QLabel("Status:"), 0, 0);
    m_validationResultLabel = new QLabel("Not validated");
    m_validationResultLabel->setStyleSheet("QLabel { font-weight: bold; }");
    resultsLayout->addWidget(m_validationResultLabel, 0, 1);

    resultsLayout->addWidget(new QLabel("Details:"), 1, 0);
    m_validationStatusLabel = new QLabel("No license validated yet");
    m_validationStatusLabel->setWordWrap(true);
    resultsLayout->addWidget(m_validationStatusLabel, 1, 1);

    mainLayout->addWidget(inputGroup);
    mainLayout->addLayout(buttonLayout);
    mainLayout->addWidget(resultsGroup);
    mainLayout->addStretch();

    // Connect signals
    connect(m_validateBtn, &QPushButton::clicked, this, &LicenseManagerUI::onValidateLicense);
    connect(m_installBtn, &QPushButton::clicked, this, &LicenseManagerUI::onInstallLicense);
    connect(m_loadLicenseBtn, &QPushButton::clicked, this, &LicenseManagerUI::onLoadLicenseFromFile);
    connect(m_removeLicenseBtn, &QPushButton::clicked, this, &LicenseManagerUI::onRemoveLicense);
    connect(m_clearValBtn, &QPushButton::clicked, this, &LicenseManagerUI::onClearValidationForm);

    // Initially disable install button
    m_installBtn->setEnabled(false);
}

void LicenseManagerUI::setupInfoTab()
{
    m_infoTab = new QWidget();
    m_tabWidget->addTab(m_infoTab, "License Information");

    QVBoxLayout* mainLayout = new QVBoxLayout(m_infoTab);

    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_refreshInfoBtn = new QPushButton("Refresh Information");
    m_exportInfoBtn = new QPushButton("Export Report");

    buttonLayout->addWidget(m_refreshInfoBtn);
    buttonLayout->addWidget(m_exportInfoBtn);
    buttonLayout->addStretch();

    mainLayout->addLayout(buttonLayout);

    // Create splitter for tables
    QSplitter* splitter = new QSplitter(Qt::Vertical);

    // License info table
    QGroupBox* licenseGroup = new QGroupBox("Current License Information");
    QVBoxLayout* licenseLayout = new QVBoxLayout(licenseGroup);

    m_licenseInfoTable = new QTableWidget(0, 2);
    m_licenseInfoTable->setHorizontalHeaderLabels({"Property", "Value"});
    m_licenseInfoTable->horizontalHeader()->setStretchLastSection(true);
    m_licenseInfoTable->setAlternatingRowColors(true);
    m_licenseInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    licenseLayout->addWidget(m_licenseInfoTable);

    // Hardware info table
    QGroupBox* hardwareGroup = new QGroupBox("Hardware Information");
    QVBoxLayout* hardwareLayout = new QVBoxLayout(hardwareGroup);

    m_hardwareInfoTable = new QTableWidget(0, 2);
    m_hardwareInfoTable->setHorizontalHeaderLabels({"Property", "Value"});
    m_hardwareInfoTable->horizontalHeader()->setStretchLastSection(true);
    m_hardwareInfoTable->setAlternatingRowColors(true);
    m_hardwareInfoTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    hardwareLayout->addWidget(m_hardwareInfoTable);

    // Features table
    QGroupBox* featuresGroup = new QGroupBox("Enabled Features");
    QVBoxLayout* featuresLayout = new QVBoxLayout(featuresGroup);

    m_featuresTable = new QTableWidget(0, 2);
    m_featuresTable->setHorizontalHeaderLabels({"Feature", "Status"});
    m_featuresTable->horizontalHeader()->setStretchLastSection(true);
    m_featuresTable->setAlternatingRowColors(true);
    m_featuresTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    featuresLayout->addWidget(m_featuresTable);

    splitter->addWidget(licenseGroup);
    splitter->addWidget(hardwareGroup);
    splitter->addWidget(featuresGroup);
    splitter->setSizes({200, 150, 100});

    mainLayout->addWidget(splitter);

    // Connect signals
    connect(m_refreshInfoBtn, &QPushButton::clicked, this, &LicenseManagerUI::onRefreshLicenseInfo);
    connect(m_exportInfoBtn, &QPushButton::clicked, this, &LicenseManagerUI::onExportLicenseInfo);
}

void LicenseManagerUI::setupLogTab()
{
    m_logTab = new QWidget();
    m_tabWidget->addTab(m_logTab, "Activity Log");

    QVBoxLayout* mainLayout = new QVBoxLayout(m_logTab);

    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_clearLogBtn = new QPushButton("Clear Log");
    m_saveLogBtn = new QPushButton("Save Log");

    buttonLayout->addWidget(m_clearLogBtn);
    buttonLayout->addWidget(m_saveLogBtn);
    buttonLayout->addStretch();

    mainLayout->addLayout(buttonLayout);

    // Log display
    m_logEdit = new QTextEdit();
    m_logEdit->setReadOnly(true);
    m_logEdit->setFont(QFont("Consolas", 9));
    mainLayout->addWidget(m_logEdit);

    // Connect signals
    connect(m_clearLogBtn, &QPushButton::clicked, [this]() {
        m_logEdit->clear();
        addLogMessage("Log cleared by user");
    });
    connect(m_saveLogBtn, &QPushButton::clicked, [this]() {
        QString fileName = QFileDialog::getSaveFileName(this, "Save Log",
            QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/license_log.txt",
            "Text Files (*.txt)");
        if (!fileName.isEmpty()) {
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out << m_logEdit->toPlainText();
                addLogMessage("Log saved to: " + fileName);
                showStatusMessage("Log saved successfully");
            }
        }
    });
}

void LicenseManagerUI::connectSignals()
{
    // Connect LicenseManager signals
    connect(m_licenseManager.get(), &LicenseManager::licenseValidated,
            this, &LicenseManagerUI::onLicenseValidated);
    connect(m_licenseManager.get(), &LicenseManager::licenseExpiring,
            this, &LicenseManagerUI::onLicenseExpiring);
    connect(m_licenseManager.get(), &LicenseManager::featureAccessDenied,
            this, &LicenseManagerUI::onFeatureAccessDenied);
}

void LicenseManagerUI::loadSettings()
{
    QSettings settings;

    // Window geometry
    restoreGeometry(settings.value("geometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());

    // Application settings
    m_lastLicenseDir = settings.value("lastLicenseDir",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation)).toString();
    m_defaultCustomer = settings.value("defaultCustomer", "").toString();
    m_defaultCompany = settings.value("defaultCompany", "").toString();

    // Set default values
    if (!m_defaultCustomer.isEmpty()) {
        m_customerNameEdit->setText(m_defaultCustomer);
    }
    if (!m_defaultCompany.isEmpty()) {
        m_companyNameEdit->setText(m_defaultCompany);
    }
}

void LicenseManagerUI::saveSettings()
{
    QSettings settings;

    // Window geometry
    settings.setValue("geometry", saveGeometry());
    settings.setValue("windowState", saveState());

    // Application settings
    settings.setValue("lastLicenseDir", m_lastLicenseDir);
    settings.setValue("defaultCustomer", m_customerNameEdit->text());
    settings.setValue("defaultCompany", m_companyNameEdit->text());
}

// License generation slots
void LicenseManagerUI::onGenerateLicense()
{
    QString customerName = m_customerNameEdit->text().trimmed();
    QString companyName = m_companyNameEdit->text().trimmed();

    if (customerName.isEmpty() || companyName.isEmpty()) {
        QMessageBox::warning(this, "Input Error",
            "Please enter both customer name and company name.");
        return;
    }

    // Get license type
    LicenseManager::LicenseType licenseType =
        static_cast<LicenseManager::LicenseType>(m_licenseTypeCombo->currentData().toInt());

    // Get expiry date
    QDateTime expiryDate = QDateTime(m_expiryDateEdit->date());

    // Get features
    QStringList features;
    for (int i = 0; i < m_featuresList->count(); ++i) {
        features << m_featuresList->item(i)->text();
    }

    if (features.isEmpty()) {
        features << "basic_features"; // Default feature
    }

    // Show progress
    m_progressBar->setVisible(true);
    m_progressBar->setRange(0, 0); // Indeterminate progress
    showStatusMessage("Generating license...");

    try {
        // Debug information before generation
        addLogMessage(QString("DEBUG: Generating license for %1 (%2)").arg(customerName).arg(companyName), "DEBUG");
        addLogMessage(QString("DEBUG: License type: %1, Features: %2").arg(formatLicenseType(licenseType)).arg(features.join(", ")), "DEBUG");
        addLogMessage(QString("DEBUG: Hardware fingerprint: %1").arg(m_licenseManager->getHardwareFingerprint()), "DEBUG");

        QString licenseKey = m_licenseManager->generateLicense(
            customerName, companyName, licenseType, expiryDate, features);

        if (!licenseKey.isEmpty()) {
            m_generatedLicenseEdit->setPlainText(licenseKey);
            m_copyLicenseBtn->setEnabled(true);
            m_saveLicenseBtn->setEnabled(true);

            addLogMessage(QString("License generated for %1 (%2), Type: %3, Expires: %4")
                .arg(customerName)
                .arg(companyName)
                .arg(formatLicenseType(licenseType))
                .arg(expiryDate.toString(Qt::ISODate)), "SUCCESS");

            addLogMessage(QString("DEBUG: Generated license length: %1").arg(licenseKey.length()), "DEBUG");
            addLogMessage(QString("DEBUG: License preview: %1...").arg(licenseKey.left(50)), "DEBUG");

            showStatusMessage("License generated successfully");
        } else {
            QMessageBox::critical(this, "Generation Error",
                "Failed to generate license. Please check the input parameters.");
            addLogMessage("License generation failed", "ERROR");
        }
    } catch (const std::exception& e) {
        QMessageBox::critical(this, "Generation Error",
            QString("Error generating license: %1").arg(e.what()));
        addLogMessage(QString("License generation error: %1").arg(e.what()), "ERROR");
    }

    m_progressBar->setVisible(false);
}

void LicenseManagerUI::onCopyLicense()
{
    QString licenseText = m_generatedLicenseEdit->toPlainText();
    if (!licenseText.isEmpty()) {
        QApplication::clipboard()->setText(licenseText);
        showStatusMessage("License copied to clipboard");
        addLogMessage("License copied to clipboard");
    }
}

void LicenseManagerUI::onSaveLicenseToFile()
{
    QString licenseText = m_generatedLicenseEdit->toPlainText();
    if (licenseText.isEmpty()) {
        QMessageBox::warning(this, "Save Error", "No license to save.");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this, "Save License File",
        m_lastLicenseDir + "/license.dat", "License Files (*.dat);;All Files (*)");

    if (!fileName.isEmpty()) {
        if (m_licenseManager->saveLicenseToFile(fileName, licenseText)) {
            m_lastLicenseDir = QFileInfo(fileName).absolutePath();
            showStatusMessage("License saved successfully");
            addLogMessage("License saved to: " + fileName, "SUCCESS");
        } else {
            QMessageBox::critical(this, "Save Error", "Failed to save license file.");
            addLogMessage("Failed to save license to: " + fileName, "ERROR");
        }
    }
}

void LicenseManagerUI::onClearGenerationForm()
{
    m_customerNameEdit->clear();
    m_companyNameEdit->clear();
    m_licenseTypeCombo->setCurrentIndex(0);
    m_expiryDateEdit->setDate(QDate::currentDate().addDays(30));
    m_featuresList->clear();
    m_featuresList->addItem("basic_features");
    m_generatedLicenseEdit->clear();
    m_copyLicenseBtn->setEnabled(false);
    m_saveLicenseBtn->setEnabled(false);

    addLogMessage("Generation form cleared");
}

// License validation slots
void LicenseManagerUI::onValidateLicense()
{
    QString licenseKey = m_licenseInputEdit->toPlainText().trimmed();
    if (licenseKey.isEmpty()) {
        QMessageBox::warning(this, "Input Error", "Please enter a license key.");
        return;
    }

    m_progressBar->setVisible(true);
    m_progressBar->setRange(0, 0);
    showStatusMessage("Validating license...");

    // Debug information
    addLogMessage(QString("DEBUG: License key length: %1").arg(licenseKey.length()), "DEBUG");
    addLogMessage(QString("DEBUG: License key preview: %1...").arg(licenseKey.left(50)), "DEBUG");
    addLogMessage(QString("DEBUG: Hardware fingerprint: %1").arg(m_licenseManager->getHardwareFingerprint()), "DEBUG");

    try {
        LicenseManager::ValidationResult result = m_licenseManager->validateLicense(licenseKey);
        updateValidationResult(result);

        if (result == LicenseManager::ValidationResult::Valid) {
            m_installBtn->setEnabled(true);
            addLogMessage("License validation successful", "SUCCESS");
        } else {
            m_installBtn->setEnabled(false);
            addLogMessage(QString("License validation failed: %1").arg(formatValidationResult(result)), "WARNING");

            // Additional debug information for failed validation
            if (result == LicenseManager::ValidationResult::InvalidFormat) {
                addLogMessage("DEBUG: This usually means AES decryption failed or JSON parsing failed", "DEBUG");
                addLogMessage("DEBUG: Check if the license was generated on the same machine", "DEBUG");
            } else if (result == LicenseManager::ValidationResult::HardwareMismatch) {
                addLogMessage("DEBUG: Hardware fingerprint mismatch - license is bound to different hardware", "DEBUG");
            } else if (result == LicenseManager::ValidationResult::Corrupted) {
                addLogMessage("DEBUG: License data is corrupted or AES decryption failed", "DEBUG");
            }
        }
    } catch (const std::exception& e) {
        updateValidationResult(LicenseManager::ValidationResult::Corrupted);
        m_installBtn->setEnabled(false);
        addLogMessage(QString("License validation error: %1").arg(e.what()), "ERROR");
    }

    m_progressBar->setVisible(false);
}

void LicenseManagerUI::onInstallLicense()
{
    QString licenseKey = m_licenseInputEdit->toPlainText().trimmed();
    if (licenseKey.isEmpty()) {
        QMessageBox::warning(this, "Input Error", "Please enter a license key.");
        return;
    }

    m_progressBar->setVisible(true);
    m_progressBar->setRange(0, 0);
    showStatusMessage("Installing license...");

    try {
        bool success = m_licenseManager->installLicense(licenseKey);
        if (success) {
            showStatusMessage("License installed successfully");
            addLogMessage("License installed successfully", "SUCCESS");
            updateLicenseStatus();
            updateFeatureList();

            // Switch to info tab to show installed license
            m_tabWidget->setCurrentWidget(m_infoTab);
            onRefreshLicenseInfo();
        } else {
            QMessageBox::critical(this, "Installation Error", "Failed to install license.");
            addLogMessage("License installation failed", "ERROR");
        }
    } catch (const std::exception& e) {
        QMessageBox::critical(this, "Installation Error",
            QString("Error installing license: %1").arg(e.what()));
        addLogMessage(QString("License installation error: %1").arg(e.what()), "ERROR");
    }

    m_progressBar->setVisible(false);
}

void LicenseManagerUI::onLoadLicenseFromFile()
{
    QString fileName = QFileDialog::getOpenFileName(this, "Load License File",
        m_lastLicenseDir, "License Files (*.dat);;All Files (*)");

    if (!fileName.isEmpty()) {
        QString licenseKey = m_licenseManager->loadLicenseFromFile(fileName);
        if (!licenseKey.isEmpty()) {
            m_licenseInputEdit->setPlainText(licenseKey);
            m_lastLicenseDir = QFileInfo(fileName).absolutePath();
            addLogMessage("License loaded from: " + fileName);
            showStatusMessage("License loaded from file");
        } else {
            QMessageBox::critical(this, "Load Error", "Failed to load license from file.");
            addLogMessage("Failed to load license from: " + fileName, "ERROR");
        }
    }
}

void LicenseManagerUI::onRemoveLicense()
{
    int ret = QMessageBox::question(this, "Remove License",
        "Are you sure you want to remove the current license?\n"
        "This action cannot be undone.",
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        try {
            bool success = m_licenseManager->removeLicense();
            if (success) {
                showStatusMessage("License removed successfully");
                addLogMessage("License removed successfully", "SUCCESS");
                updateLicenseStatus();
                updateFeatureList();
                onRefreshLicenseInfo();
            } else {
                QMessageBox::critical(this, "Remove Error", "Failed to remove license.");
                addLogMessage("License removal failed", "ERROR");
            }
        } catch (const std::exception& e) {
            QMessageBox::critical(this, "Remove Error",
                QString("Error removing license: %1").arg(e.what()));
            addLogMessage(QString("License removal error: %1").arg(e.what()), "ERROR");
        }
    }
}

void LicenseManagerUI::onClearValidationForm()
{
    m_licenseInputEdit->clear();
    m_validationResultLabel->setText("Not validated");
    m_validationResultLabel->setStyleSheet("QLabel { font-weight: bold; color: gray; }");
    m_validationStatusLabel->setText("No license validated yet");
    m_installBtn->setEnabled(false);

    addLogMessage("Validation form cleared");
}

// Feature management slots
void LicenseManagerUI::onAddFeature()
{
    QString feature = m_newFeatureEdit->text().trimmed();
    if (!feature.isEmpty()) {
        // Check if feature already exists
        bool exists = false;
        for (int i = 0; i < m_featuresList->count(); ++i) {
            if (m_featuresList->item(i)->text() == feature) {
                exists = true;
                break;
            }
        }

        if (!exists) {
            m_featuresList->addItem(feature);
            m_newFeatureEdit->clear();
            addLogMessage("Feature added: " + feature);
        } else {
            QMessageBox::information(this, "Feature Exists",
                "This feature is already in the list.");
        }
    }
}

void LicenseManagerUI::onRemoveFeature()
{
    QListWidgetItem* currentItem = m_featuresList->currentItem();
    if (currentItem) {
        QString feature = currentItem->text();
        delete currentItem;
        addLogMessage("Feature removed: " + feature);
    }
}

void LicenseManagerUI::onClearFeatures()
{
    m_featuresList->clear();
    addLogMessage("All features cleared");
}

// License info slots
void LicenseManagerUI::onRefreshLicenseInfo()
{
    updateLicenseStatus();
    updateFeatureList();
    updateHardwareInfo();

    // Update license info table
    LicenseManager::LicenseInfo info = m_licenseManager->getCurrentLicenseInfo();

    m_licenseInfoTable->setRowCount(0);

    if (info.isValid()) {
        QStringList properties = {
            "License Key", "Customer Name", "Company Name", "License Type",
            "Issue Date", "Expiry Date", "Days Remaining", "Hardware Fingerprint",
            "Status"
        };

        QStringList values = {
            info.licenseKey.left(20) + "...",
            info.customerName,
            info.companyName,
            formatLicenseType(info.type),
            info.issueDate.toString(Qt::DefaultLocaleLongDate),
            info.expiryDate.toString(Qt::DefaultLocaleLongDate),
            QString::number(info.daysRemaining()),
            info.hardwareFingerprint.left(16) + "...",
            info.isExpired() ? "Expired" : "Active"
        };

        m_licenseInfoTable->setRowCount(properties.size());
        for (int i = 0; i < properties.size(); ++i) {
            m_licenseInfoTable->setItem(i, 0, new QTableWidgetItem(properties[i]));
            QTableWidgetItem* valueItem = new QTableWidgetItem(values[i]);

            // Color code status
            if (properties[i] == "Status") {
                if (info.isExpired()) {
                    valueItem->setBackground(QColor(255, 200, 200));
                } else {
                    valueItem->setBackground(QColor(200, 255, 200));
                }
            }

            m_licenseInfoTable->setItem(i, 1, valueItem);
        }
    } else {
        m_licenseInfoTable->setRowCount(1);
        m_licenseInfoTable->setItem(0, 0, new QTableWidgetItem("Status"));
        QTableWidgetItem* statusItem = new QTableWidgetItem("No valid license installed");
        statusItem->setBackground(QColor(255, 200, 200));
        m_licenseInfoTable->setItem(0, 1, statusItem);
    }

    m_licenseInfoTable->resizeColumnsToContents();

    addLogMessage("License information refreshed");
}

void LicenseManagerUI::onExportLicenseInfo()
{
    QString fileName = QFileDialog::getSaveFileName(this, "Export License Report",
        QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation) + "/license_report.txt",
        "Text Files (*.txt);;HTML Files (*.html)");

    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);

            out << "License Manager Report\n";
            out << "Generated: " << QDateTime::currentDateTime().toString() << "\n";
            out << "=" << QString(50, '=') << "\n\n";

            // License information
            LicenseManager::LicenseInfo info = m_licenseManager->getCurrentLicenseInfo();
            if (info.isValid()) {
                out << "LICENSE INFORMATION:\n";
                out << "Customer: " << info.customerName << "\n";
                out << "Company: " << info.companyName << "\n";
                out << "Type: " << formatLicenseType(info.type) << "\n";
                out << "Issue Date: " << info.issueDate.toString() << "\n";
                out << "Expiry Date: " << info.expiryDate.toString() << "\n";
                out << "Days Remaining: " << info.daysRemaining() << "\n";
                out << "Status: " << (info.isExpired() ? "Expired" : "Active") << "\n\n";

                out << "ENABLED FEATURES:\n";
                for (const QString& feature : info.enabledFeatures) {
                    out << "- " << feature << "\n";
                }
            } else {
                out << "No valid license installed\n";
            }

            out << "\nHARDWARE INFORMATION:\n";
            out << "Hardware Fingerprint: " << m_licenseManager->getHardwareFingerprint() << "\n";

            addLogMessage("License report exported to: " + fileName, "SUCCESS");
            showStatusMessage("Report exported successfully");
        }
    }
}

// File operations
void LicenseManagerUI::onOpenLicenseFile()
{
    m_tabWidget->setCurrentWidget(m_validationTab);
    onLoadLicenseFromFile();
}

void LicenseManagerUI::onSaveLicenseFile()
{
    m_tabWidget->setCurrentWidget(m_generationTab);
    onSaveLicenseToFile();
}

void LicenseManagerUI::onExportReport()
{
    m_tabWidget->setCurrentWidget(m_infoTab);
    onExportLicenseInfo();
}

// License Manager signal handlers
void LicenseManagerUI::onLicenseValidated(LicenseManager::ValidationResult result)
{
    updateValidationResult(result);
    addLogMessage(QString("License validation signal received: %1").arg(formatValidationResult(result)));
}

void LicenseManagerUI::onLicenseExpiring(int daysRemaining)
{
    QString message = QString("License expires in %1 days").arg(daysRemaining);
    showStatusMessage(message, 5000);
    addLogMessage(message, "WARNING");

    QMessageBox::warning(this, "License Expiring",
        QString("Your license will expire in %1 days.\nPlease renew your license.").arg(daysRemaining));
}

void LicenseManagerUI::onFeatureAccessDenied(const QString& featureName)
{
    QString message = QString("Access denied to feature: %1").arg(featureName);
    addLogMessage(message, "WARNING");
}

// UI update methods
void LicenseManagerUI::updateLicenseStatus()
{
    LicenseManager::LicenseInfo info = m_licenseManager->getCurrentLicenseInfo();
    QLabel* statusLabel = findChild<QLabel*>("licenseStatus");

    if (statusLabel) {
        if (info.isValid()) {
            if (info.isExpired()) {
                statusLabel->setText("License: EXPIRED");
                statusLabel->setStyleSheet("QLabel { color: red; font-weight: bold; }");
            } else {
                statusLabel->setText(QString("License: ACTIVE (%1 days)").arg(info.daysRemaining()));
                statusLabel->setStyleSheet("QLabel { color: green; font-weight: bold; }");
            }
        } else {
            statusLabel->setText("License: NOT INSTALLED");
            statusLabel->setStyleSheet("QLabel { color: orange; font-weight: bold; }");
        }
    }
}

void LicenseManagerUI::updateFeatureList()
{
    m_featuresTable->setRowCount(0);

    LicenseManager::LicenseInfo info = m_licenseManager->getCurrentLicenseInfo();
    if (info.isValid()) {
        m_featuresTable->setRowCount(info.enabledFeatures.size());
        for (int i = 0; i < info.enabledFeatures.size(); ++i) {
            QString feature = info.enabledFeatures[i];
            m_featuresTable->setItem(i, 0, new QTableWidgetItem(feature));

            QTableWidgetItem* statusItem = new QTableWidgetItem("Enabled");
            statusItem->setBackground(QColor(200, 255, 200));
            m_featuresTable->setItem(i, 1, statusItem);
        }
    }

    m_featuresTable->resizeColumnsToContents();
}

void LicenseManagerUI::updateHardwareInfo()
{
    m_hardwareInfoTable->setRowCount(0);

    QString fingerprint = m_licenseManager->getHardwareFingerprint();

    QStringList properties = {"Hardware Fingerprint", "System Time", "Application Path"};
    QStringList values = {
        fingerprint,
        QDateTime::currentDateTime().toString(),
        QApplication::applicationFilePath()
    };

    m_hardwareInfoTable->setRowCount(properties.size());
    for (int i = 0; i < properties.size(); ++i) {
        m_hardwareInfoTable->setItem(i, 0, new QTableWidgetItem(properties[i]));
        m_hardwareInfoTable->setItem(i, 1, new QTableWidgetItem(values[i]));
    }

    m_hardwareInfoTable->resizeColumnsToContents();
}

// Utility methods
void LicenseManagerUI::addLogMessage(const QString& message, const QString& type)
{
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString logEntry = QString("[%1] %2: %3").arg(timestamp).arg(type).arg(message);

    m_logEdit->append(logEntry);

    // Auto-scroll to bottom
    QTextCursor cursor = m_logEdit->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logEdit->setTextCursor(cursor);
}

void LicenseManagerUI::showStatusMessage(const QString& message, int timeout)
{
    m_statusLabel->setText(message);
    if (timeout > 0) {
        QTimer::singleShot(timeout, [this]() {
            m_statusLabel->setText("Ready");
        });
    }
}

void LicenseManagerUI::updateValidationResult(LicenseManager::ValidationResult result)
{
    QString resultText = formatValidationResult(result);
    QColor color = getResultColor(result);

    m_validationResultLabel->setText(resultText);
    m_validationResultLabel->setStyleSheet(QString("QLabel { font-weight: bold; color: %1; }").arg(color.name()));

    QString details;
    switch (result) {
        case LicenseManager::ValidationResult::Valid:
            details = "License is valid and can be installed.";
            break;
        case LicenseManager::ValidationResult::Expired:
            details = "License has expired. Please renew your license.";
            break;
        case LicenseManager::ValidationResult::InvalidFormat:
            details = "License format is invalid. Please check the license key.";
            break;
        case LicenseManager::ValidationResult::Corrupted:
            details = "License data is corrupted or tampered with.";
            break;
        case LicenseManager::ValidationResult::HardwareMismatch:
            details = "License is not valid for this hardware.";
            break;
        case LicenseManager::ValidationResult::NotFound:
            details = "No license found.";
            break;
        default:
            details = "Unknown validation result.";
            break;
    }

    m_validationStatusLabel->setText(details);
}

QString LicenseManagerUI::formatLicenseType(LicenseManager::LicenseType type) const
{
    switch (type) {
        case LicenseManager::LicenseType::Trial: return "Trial";
        case LicenseManager::LicenseType::Standard: return "Standard";
        case LicenseManager::LicenseType::Professional: return "Professional";
        case LicenseManager::LicenseType::Enterprise: return "Enterprise";
        default: return "Unknown";
    }
}

QString LicenseManagerUI::formatValidationResult(LicenseManager::ValidationResult result) const
{
    switch (result) {
        case LicenseManager::ValidationResult::Valid: return "VALID";
        case LicenseManager::ValidationResult::Expired: return "EXPIRED";
        case LicenseManager::ValidationResult::InvalidFormat: return "INVALID FORMAT";
        case LicenseManager::ValidationResult::Corrupted: return "CORRUPTED";
        case LicenseManager::ValidationResult::HardwareMismatch: return "HARDWARE MISMATCH";
        case LicenseManager::ValidationResult::NotFound: return "NOT FOUND";
        default: return "UNKNOWN";
    }
}

QColor LicenseManagerUI::getResultColor(LicenseManager::ValidationResult result) const
{
    switch (result) {
        case LicenseManager::ValidationResult::Valid: return QColor(0, 150, 0);
        case LicenseManager::ValidationResult::Expired: return QColor(255, 140, 0);
        case LicenseManager::ValidationResult::InvalidFormat: return QColor(255, 0, 0);
        case LicenseManager::ValidationResult::Corrupted: return QColor(139, 0, 0);
        case LicenseManager::ValidationResult::HardwareMismatch: return QColor(255, 0, 0);
        case LicenseManager::ValidationResult::NotFound: return QColor(128, 128, 128);
        default: return QColor(0, 0, 0);
    }
}

// Debug and testing methods
void LicenseManagerUI::onTestEncryption()
{
    addLogMessage("=== Starting Encryption Test ===", "DEBUG");

    // Test 1: Generate a simple license and immediately validate it
    QString testCustomer = "Test Customer";
    QString testCompany = "Test Company";
    LicenseManager::LicenseType testType = LicenseManager::LicenseType::Trial;
    QDateTime testExpiry = QDateTime::currentDateTime().addDays(30);
    QStringList testFeatures = {"test_feature"};

    addLogMessage("DEBUG: Generating test license...", "DEBUG");
    QString testLicense = m_licenseManager->generateLicense(testCustomer, testCompany, testType, testExpiry, testFeatures);

    if (testLicense.isEmpty()) {
        addLogMessage("ERROR: Failed to generate test license", "ERROR");
        return;
    }

    addLogMessage(QString("DEBUG: Test license generated, length: %1").arg(testLicense.length()), "DEBUG");
    addLogMessage(QString("DEBUG: Test license preview: %1...").arg(testLicense.left(100)), "DEBUG");

    // Test 2: Validate the generated license
    addLogMessage("DEBUG: Validating test license...", "DEBUG");
    LicenseManager::ValidationResult result = m_licenseManager->validateLicense(testLicense);

    addLogMessage(QString("DEBUG: Validation result: %1").arg(formatValidationResult(result)), "DEBUG");

    if (result == LicenseManager::ValidationResult::Valid) {
        addLogMessage("SUCCESS: Encryption/Decryption test passed!", "SUCCESS");

        // Get license info to verify data integrity
        LicenseManager::LicenseInfo info = m_licenseManager->getCurrentLicenseInfo();
        addLogMessage(QString("DEBUG: Decrypted customer: %1").arg(info.customerName), "DEBUG");
        addLogMessage(QString("DEBUG: Decrypted company: %1").arg(info.companyName), "DEBUG");
        addLogMessage(QString("DEBUG: Decrypted features: %1").arg(info.enabledFeatures.join(", ")), "DEBUG");

        if (info.customerName == testCustomer && info.companyName == testCompany) {
            addLogMessage("SUCCESS: Data integrity verified!", "SUCCESS");
        } else {
            addLogMessage("ERROR: Data integrity check failed!", "ERROR");
        }
    } else {
        addLogMessage("ERROR: Encryption/Decryption test failed!", "ERROR");

        // Additional debugging
        if (result == LicenseManager::ValidationResult::InvalidFormat) {
            addLogMessage("DEBUG: InvalidFormat suggests AES decryption or JSON parsing failed", "DEBUG");
        } else if (result == LicenseManager::ValidationResult::HardwareMismatch) {
            addLogMessage("DEBUG: Hardware fingerprint mismatch", "DEBUG");
        } else if (result == LicenseManager::ValidationResult::Corrupted) {
            addLogMessage("DEBUG: License data corrupted", "DEBUG");
        }
    }

    addLogMessage("=== Encryption Test Complete ===", "DEBUG");

    // Show result in message box
    if (result == LicenseManager::ValidationResult::Valid) {
        QMessageBox::information(this, "Encryption Test",
            "✅ Encryption test PASSED!\n\n"
            "The AES encryption/decryption is working correctly.\n"
            "License generation and validation are functioning properly.");
    } else {
        QMessageBox::warning(this, "Encryption Test",
            QString("❌ Encryption test FAILED!\n\n"
                   "Validation result: %1\n\n"
                   "Check the activity log for detailed debug information.").arg(formatValidationResult(result)));
    }
}

void LicenseManagerUI::onTestQAESEncryption()
{
    addLogMessage("=== Starting QAESEncryption Library Test ===", "DEBUG");

    // Test 1: Direct QAESEncryption library test
    QString testData = "Hello, QAESEncryption World! 🔐";
    QByteArray key = QCryptographicHash::hash("test_key_qaes", QCryptographicHash::Sha256);
    QByteArray iv(16, 0x42); // Test IV

    addLogMessage("DEBUG: Testing QAESEncryption library directly...", "DEBUG");
    addLogMessage(QString("DEBUG: Test data: %1").arg(testData), "DEBUG");
    addLogMessage(QString("DEBUG: Key length: %1 bytes").arg(key.length()), "DEBUG");
    addLogMessage(QString("DEBUG: IV length: %1 bytes").arg(iv.length()), "DEBUG");

    // Test encryption
    QByteArray encrypted = QAESEncryption::Crypt(QAESEncryption::AES_256,
                                                QAESEncryption::CBC,
                                                testData.toUtf8(),
                                                key,
                                                iv,
                                                QAESEncryption::PKCS7);

    if (encrypted.isEmpty()) {
        addLogMessage("ERROR: QAESEncryption failed to encrypt data", "ERROR");
        QMessageBox::critical(this, "QAESEncryption Test", "❌ QAESEncryption encryption failed!");
        return;
    }

    addLogMessage(QString("DEBUG: Encrypted data length: %1 bytes").arg(encrypted.length()), "DEBUG");
    addLogMessage(QString("DEBUG: Encrypted data (hex): %1").arg(QString::fromUtf8(encrypted.toHex())), "DEBUG");

    // Test decryption
    QByteArray decrypted = QAESEncryption::Decrypt(QAESEncryption::AES_256,
                                                  QAESEncryption::CBC,
                                                  encrypted,
                                                  key,
                                                  iv,
                                                  QAESEncryption::PKCS7);

    // Remove padding
    decrypted = QAESEncryption::RemovePadding(decrypted, QAESEncryption::PKCS7);

    QString decryptedText = QString::fromUtf8(decrypted);
    addLogMessage(QString("DEBUG: Decrypted data: %1").arg(decryptedText), "DEBUG");

    bool directTestPassed = (decryptedText == testData);
    if (directTestPassed) {
        addLogMessage("SUCCESS: QAESEncryption direct test passed!", "SUCCESS");
    } else {
        addLogMessage("ERROR: QAESEncryption direct test failed!", "ERROR");
        addLogMessage(QString("Expected: %1").arg(testData), "ERROR");
        addLogMessage(QString("Got: %1").arg(decryptedText), "ERROR");
    }

    // Test 2: License generation with QAESEncryption
    addLogMessage("DEBUG: Testing license generation with QAESEncryption...", "DEBUG");

    QString testCustomer = "QAESEncryption Test Customer";
    QString testCompany = "QAESEncryption Test Company";
    LicenseManager::LicenseType testType = LicenseManager::LicenseType::Professional;
    QDateTime testExpiry = QDateTime::currentDateTime().addDays(90);
    QStringList testFeatures = {"qaes_feature1", "qaes_feature2", "advanced_encryption"};

    QString testLicense = m_licenseManager->generateLicense(testCustomer, testCompany, testType, testExpiry, testFeatures);

    if (testLicense.isEmpty()) {
        addLogMessage("ERROR: Failed to generate license with QAESEncryption", "ERROR");
        QMessageBox::critical(this, "QAESEncryption Test", "❌ License generation with QAESEncryption failed!");
        return;
    }

    addLogMessage(QString("DEBUG: QAESEncryption license generated, length: %1").arg(testLicense.length()), "DEBUG");

    // Test 3: Validate the QAESEncryption license
    addLogMessage("DEBUG: Validating QAESEncryption license...", "DEBUG");
    LicenseManager::ValidationResult result = m_licenseManager->validateLicense(testLicense);

    addLogMessage(QString("DEBUG: QAESEncryption license validation result: %1").arg(formatValidationResult(result)), "DEBUG");

    bool licenseTestPassed = (result == LicenseManager::ValidationResult::Valid);
    if (licenseTestPassed) {
        addLogMessage("SUCCESS: QAESEncryption license test passed!", "SUCCESS");

        // Verify license data integrity
        LicenseManager::LicenseInfo info = m_licenseManager->getCurrentLicenseInfo();
        addLogMessage(QString("DEBUG: Verified customer: %1").arg(info.customerName), "DEBUG");
        addLogMessage(QString("DEBUG: Verified company: %1").arg(info.companyName), "DEBUG");
        addLogMessage(QString("DEBUG: Verified type: %1").arg(formatLicenseType(info.type)), "DEBUG");
        addLogMessage(QString("DEBUG: Verified features: %1").arg(info.enabledFeatures.join(", ")), "DEBUG");

        bool dataIntegrityPassed = (info.customerName == testCustomer &&
                                   info.companyName == testCompany &&
                                   info.type == testType &&
                                   info.enabledFeatures == testFeatures);

        if (dataIntegrityPassed) {
            addLogMessage("SUCCESS: QAESEncryption data integrity verified!", "SUCCESS");
        } else {
            addLogMessage("ERROR: QAESEncryption data integrity check failed!", "ERROR");
            licenseTestPassed = false;
        }
    } else {
        addLogMessage("ERROR: QAESEncryption license test failed!", "ERROR");
    }

    addLogMessage("=== QAESEncryption Test Complete ===", "DEBUG");

    // Show comprehensive result
    bool overallPassed = directTestPassed && licenseTestPassed;
    if (overallPassed) {
        QMessageBox::information(this, "QAESEncryption Test",
            "✅ QAESEncryption test PASSED!\n\n"
            "✓ Direct encryption/decryption: PASSED\n"
            "✓ License generation with QAESEncryption: PASSED\n"
            "✓ License validation with QAESEncryption: PASSED\n"
            "✓ Data integrity verification: PASSED\n\n"
            "The QAESEncryption library integration is working correctly!");
    } else {
        QString details;
        if (!directTestPassed) details += "✗ Direct encryption/decryption: FAILED\n";
        if (!licenseTestPassed) details += "✗ License operations: FAILED\n";

        QMessageBox::warning(this, "QAESEncryption Test",
            QString("❌ QAESEncryption test FAILED!\n\n%1\n"
                   "Check the activity log for detailed debug information.").arg(details));
    }
}
