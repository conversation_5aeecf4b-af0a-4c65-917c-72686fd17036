#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QStandardPaths>
#include <QMessageBox>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>
#include <QSettings>
#include <QTranslator>
#include <QLibraryInfo>
#include <QPainter>

#include "LicenseManagerUI.h"
#include "LicenseManager.h"
#include "glog.h"

// Register the enum type for Qt's meta-object system
Q_DECLARE_METATYPE(LicenseManager::ValidationResult)

void setupApplication(QApplication& app)
{
    // Set application properties
    app.setApplicationName("License Manager UI");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Fuxi Core");
    app.setOrganizationDomain("fuxicore.com");

    // Set application style
    app.setStyle(QStyleFactory::create("Fusion"));

    // Apply dark theme
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    app.setPalette(darkPalette);

    // Set additional stylesheet for better appearance
    QString styleSheet = R"(
        QMainWindow {
            background-color: #353535;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #353535;
        }
        QTabBar::tab {
            background-color: #454545;
            color: white;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #2A82DA;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #454545;
            border: 1px solid #555555;
            padding: 6px 12px;
            border-radius: 3px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #555555;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
        QPushButton:disabled {
            background-color: #2A2A2A;
            color: #666666;
        }
        QLineEdit, QTextEdit, QComboBox {
            background-color: #2A2A2A;
            border: 1px solid #555555;
            padding: 4px;
            border-radius: 3px;
        }
        QTableWidget {
            background-color: #2A2A2A;
            alternate-background-color: #353535;
            gridline-color: #555555;
        }
        QHeaderView::section {
            background-color: #454545;
            padding: 4px;
            border: 1px solid #555555;
            font-weight: bold;
        }
        QListWidget {
            background-color: #2A2A2A;
            border: 1px solid #555555;
        }
        QListWidget::item:selected {
            background-color: #2A82DA;
        }
        QStatusBar {
            background-color: #454545;
            border-top: 1px solid #555555;
        }
        QMenuBar {
            background-color: #454545;
            border-bottom: 1px solid #555555;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
        }
        QMenuBar::item:selected {
            background-color: #2A82DA;
        }
        QMenu {
            background-color: #454545;
            border: 1px solid #555555;
        }
        QMenu::item:selected {
            background-color: #2A82DA;
        }
    )";
    app.setStyleSheet(styleSheet);
}

void initializeLogging()
{
    // Initialize Google logging
    google::InitGoogleLogging("license_manager_ui");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    // Create logs directory if it doesn't exist
    QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
    QDir().mkpath(logDir);
}

QSplashScreen* createSplashScreen()
{
    // Create a simple splash screen
    QPixmap splashPixmap(400, 200);
    splashPixmap.fill(QColor(53, 53, 53));
    
    QPainter painter(&splashPixmap);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 16, QFont::Bold));
    painter.drawText(splashPixmap.rect(), Qt::AlignCenter, 
        "License Manager UI\nLoading...");
    
    QSplashScreen* splash = new QSplashScreen(splashPixmap);
    splash->show();
    
    return splash;
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Register custom types for Qt's meta-object system
    qRegisterMetaType<LicenseManager::ValidationResult>("ValidationResult");
    qRegisterMetaType<LicenseManager::ValidationResult>("LicenseManager::ValidationResult");
    
    // Setup application
    setupApplication(app);
    
    // Initialize logging
    initializeLogging();
    
    // Show splash screen
    QSplashScreen* splash = createSplashScreen();
    splash->showMessage("Initializing License Manager...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    app.processEvents();
    
    // Simulate loading time
    QTimer::singleShot(1500, [splash]() {
        splash->showMessage("Loading UI Components...", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    });
    
    QTimer::singleShot(2000, [splash]() {
        splash->showMessage("Ready!", Qt::AlignBottom | Qt::AlignCenter, Qt::white);
    });
    
    // Create and show main window
    LicenseManagerUI mainWindow;
    
    QTimer::singleShot(2500, [&mainWindow, splash]() {
        splash->finish(&mainWindow);
        mainWindow.show();
        delete splash;
    });
    
    // Check for command line arguments
    QStringList args = app.arguments();
    if (args.size() > 1) {
        QString licenseFile = args[1];
        if (QFile::exists(licenseFile)) {
            QTimer::singleShot(3000, [&mainWindow, licenseFile]() {
                // Auto-load license file if provided as argument
                QMessageBox::information(&mainWindow, "Auto-load License",
                    QString("Loading license file: %1").arg(licenseFile));
            });
        }
    }
    
    return app.exec();
}
