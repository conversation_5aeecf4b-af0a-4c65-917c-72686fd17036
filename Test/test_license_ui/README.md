# License Manager UI

A comprehensive Qt 5.15-based graphical user interface for license generation and validation, built on top of the FuxiCore LicenseManager with AES encryption.

## Features

### 🔐 License Generation
- Generate encrypted licenses with customer information
- Support for multiple license types (Trial, Standard, Professional, Enterprise)
- Configurable expiry dates
- Custom feature enablement
- Hardware fingerprint binding
- Copy to clipboard and save to file functionality

### ✅ License Validation
- Validate license keys with real-time feedback
- Install licenses to the system
- Load licenses from files
- Remove installed licenses
- Detailed validation result reporting

### 📊 License Information
- View current license details
- Hardware fingerprint information
- Enabled features overview
- Export license reports
- Real-time status monitoring

### 📝 Activity Logging
- Comprehensive activity logging
- Export logs to files
- Real-time status updates
- Error tracking and reporting

## Security Features

- **AES-256 Encryption**: All licenses are encrypted using AES-256 with hardware-bound keys
- **Hardware Binding**: Licenses are tied to specific hardware fingerprints
- **Digital Signatures**: RSA signature verification (production builds)
- **Anti-Tampering**: Protection against license modification
- **Secure Random**: Cryptographically secure random number generation

## User Interface

### Main Tabs

1. **License Generation**
   - Customer and company information input
   - License type selection
   - Expiry date configuration
   - Feature management
   - Generated license display

2. **License Validation**
   - License key input
   - Validation status display
   - Installation controls
   - File operations

3. **License Information**
   - Current license details
   - Hardware information
   - Feature status
   - Export functionality

4. **Activity Log**
   - Real-time activity monitoring
   - Log export capabilities
   - Error tracking

## Building and Running

### Prerequisites
- Qt 5.15 or later
- CMake 3.16 or later
- FuxiCore library
- Google glog library
- OpenSSL (for production builds)

### Build Instructions

```bash
# Navigate to the project directory
cd Test/test_license_ui

# Create build directory
mkdir build && cd build

# Configure with CMake
cmake ..

# Build the application
cmake --build .

# Run the application
./test_license_ui
```

### Dependencies
The application depends on the following FuxiCore components:
- `fuxicommon` - Common utilities
- `fuxicore` - Core license management functionality
- `glog` - Logging framework

## Usage Guide

### Generating a License

1. Open the **License Generation** tab
2. Enter customer and company information
3. Select the appropriate license type
4. Set the expiry date
5. Add required features to the list
6. Click **Generate License**
7. Copy the generated license or save to file

### Validating a License

1. Open the **License Validation** tab
2. Paste the license key or load from file
3. Click **Validate License**
4. Review the validation results
5. Click **Install License** if valid

### Viewing License Information

1. Open the **License Information** tab
2. Click **Refresh Information** to update data
3. Review license details, hardware info, and features
4. Export reports as needed

## Configuration

The application stores settings in the system registry (Windows) or configuration files (Linux/macOS):

- Window geometry and state
- Last used directories
- Default customer/company information

## License Types

- **Trial**: Limited time evaluation license
- **Standard**: Basic feature set
- **Professional**: Advanced features
- **Enterprise**: Full feature set with extended support

## File Formats

- **License Files**: `.dat` files containing encrypted license data
- **Log Files**: `.txt` files with activity logs
- **Reports**: `.txt` or `.html` files with license information

## Troubleshooting

### Common Issues

1. **License Generation Fails**
   - Ensure all required fields are filled
   - Check that the expiry date is in the future
   - Verify hardware fingerprint generation

2. **License Validation Fails**
   - Verify the license key is complete and unmodified
   - Check hardware fingerprint compatibility
   - Ensure the license hasn't expired

3. **Installation Issues**
   - Run with appropriate permissions
   - Check file system access
   - Verify license file integrity

### Debug Mode

Enable debug logging by setting the environment variable:
```bash
export GLOG_v=2
```

## API Integration

The UI can be integrated with other applications through:

- Command line arguments for auto-loading license files
- Qt signals and slots for custom integration
- Direct LicenseManager API access

## Security Considerations

- Store private keys securely in production environments
- Use HTTPS for license distribution
- Implement proper key management practices
- Regular security audits recommended

## Support

For technical support and bug reports, please contact the FuxiCore development team.

## Version History

- **v1.0.0**: Initial release with full license management functionality
- AES-256 encryption support
- Qt 5.15 compatibility
- Comprehensive UI with dark theme
- Multi-tab interface design
- Real-time validation and status updates
