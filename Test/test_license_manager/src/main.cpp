#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <cassert>
#include <memory>
#include <string>

#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QDateTime>
#include <QSignalSpy>
#include <QtTest/QSignalSpy>

#include "LicenseManager.h"
#include "glog.h"

using namespace Fuxi::Core;
using namespace std::chrono_literals;

// Register the enum type for Qt's meta-object system
Q_DECLARE_METATYPE(LicenseManager::ValidationResult)

class LicenseManagerTester : public QObject {
    Q_OBJECT

private:
    std::unique_ptr<LicenseManager> licenseManager;
    std::unique_ptr<QTemporaryDir> tempDir;
    QString testLicenseFile;

public:
    LicenseManagerTester() {
        licenseManager = std::make_unique<LicenseManager>();
        tempDir = std::make_unique<QTemporaryDir>();
        
        if (tempDir->isValid()) {
            testLicenseFile = tempDir->path() + "/test_license.dat";
            // Set custom license file path for testing
            licenseManager->setLicenseFilePath(testLicenseFile);
        }
    }

    void testHardwareFingerprint() {
        std::cout << "\n=== Testing Hardware Fingerprint ===" << std::endl;
        
        QString fingerprint = licenseManager->getHardwareFingerprint();
        assert(!fingerprint.isEmpty());
        std::cout << "✓ Hardware fingerprint generated: " << fingerprint.left(16).toStdString() << "..." << std::endl;
        
        // Test consistency - should return same fingerprint
        QString fingerprint2 = licenseManager->getHardwareFingerprint();
        assert(fingerprint == fingerprint2);
        std::cout << "✓ Hardware fingerprint is consistent" << std::endl;
        
        std::cout << "✓ Hardware fingerprint test passed!" << std::endl;
    }

    void testLicenseGeneration() {
        std::cout << "\n=== Testing License Generation ===" << std::endl;
        
        // Generate a trial license
        QString trialLicense = licenseManager->generateLicense(
            "Test Customer",
            "Test Company",
            LicenseManager::LicenseType::Trial,
            QDateTime::currentDateTime().addDays(30),
            {"feature1", "feature2"}
        );
        
        assert(!trialLicense.isEmpty());
        std::cout << "✓ Trial license generated: " << trialLicense.left(20).toStdString() << "..." << std::endl;
        
        // Generate a professional license
        QString professionalLicense = licenseManager->generateLicense(
            "Pro Customer",
            "Pro Company",
            LicenseManager::LicenseType::Professional,
            QDateTime::currentDateTime().addDays(365),
            {"*"}  // All features
        );
        
        assert(!professionalLicense.isEmpty());
        assert(trialLicense != professionalLicense);
        std::cout << "✓ Professional license generated: " << professionalLicense.left(20).toStdString() << "..." << std::endl;
        
        std::cout << "✓ License generation test passed!" << std::endl;
    }

    void testLicenseValidation() {
        std::cout << "\n=== Testing License Validation ===" << std::endl;
        
        // Generate a valid license
        QString validLicense = licenseManager->generateLicense(
            "Valid Customer",
            "Valid Company",
            LicenseManager::LicenseType::Standard,
            QDateTime::currentDateTime().addDays(90),
            {"basic_features"}
        );
        
        // Test validation of valid license
        LicenseManager::ValidationResult result = licenseManager->validateLicense(validLicense);
        assert(result == LicenseManager::ValidationResult::Valid);
        std::cout << "✓ Valid license validation passed" << std::endl;
        
        // Test validation of invalid license
        result = licenseManager->validateLicense("invalid_license_key");
        assert(result != LicenseManager::ValidationResult::Valid);
        std::cout << "✓ Invalid license correctly rejected" << std::endl;
        
        // Test validation of empty license
        result = licenseManager->validateLicense("");
        assert(result != LicenseManager::ValidationResult::Valid);
        std::cout << "✓ Empty license correctly rejected" << std::endl;
        
        std::cout << "✓ License validation test passed!" << std::endl;
    }

    void testLicenseInfo() {
        std::cout << "\n=== Testing License Info ===" << std::endl;
        
        // Generate and install a license
        QString license = licenseManager->generateLicense(
            "Info Customer",
            "Info Company",
            LicenseManager::LicenseType::Enterprise,
            QDateTime::currentDateTime().addDays(180),
            {"feature1", "feature2", "advanced_feature"}
        );
        
        bool installed = licenseManager->installLicense(license);
        assert(installed);
        std::cout << "✓ License installed successfully" << std::endl;
        
        // Get license info
        LicenseManager::LicenseInfo info = licenseManager->getCurrentLicenseInfo();
        assert(info.isValid());
        assert(info.customerName == "Info Customer");
        assert(info.companyName == "Info Company");
        assert(info.type == LicenseManager::LicenseType::Enterprise);
        assert(!info.isExpired());
        
        std::cout << "✓ License info verified: customer=" << info.customerName.toStdString()
                  << ", company=" << info.companyName.toStdString()
                  << ", type=Enterprise" << std::endl;
        
        // Test days remaining
        int daysRemaining = info.daysRemaining();
        assert(daysRemaining > 0 && daysRemaining <= 180);
        std::cout << "✓ Days remaining: " << daysRemaining << std::endl;
        
        std::cout << "✓ License info test passed!" << std::endl;
    }

    void testFeatureAccess() {
        std::cout << "\n=== Testing Feature Access ===" << std::endl;
        
        // Generate license with specific features
        QString license = licenseManager->generateLicense(
            "Feature Customer",
            "Feature Company",
            LicenseManager::LicenseType::Professional,
            QDateTime::currentDateTime().addDays(365),
            {"feature1", "feature2", "premium_feature"}
        );
        
        licenseManager->installLicense(license);
        
        // Test enabled features
        assert(licenseManager->isFeatureEnabled("feature1"));
        assert(licenseManager->isFeatureEnabled("feature2"));
        assert(licenseManager->isFeatureEnabled("premium_feature"));
        std::cout << "✓ Enabled features accessible" << std::endl;
        
        // Test disabled features
        assert(!licenseManager->isFeatureEnabled("disabled_feature"));
        assert(!licenseManager->isFeatureEnabled("enterprise_only"));
        std::cout << "✓ Disabled features correctly blocked" << std::endl;
        
        // Test wildcard license
        QString wildcardLicense = licenseManager->generateLicense(
            "Wildcard Customer",
            "Wildcard Company",
            LicenseManager::LicenseType::Enterprise,
            QDateTime::currentDateTime().addDays(365),
            {"*"}  // All features
        );
        
        licenseManager->installLicense(wildcardLicense);
        
        assert(licenseManager->isFeatureEnabled("any_feature"));
        assert(licenseManager->isFeatureEnabled("enterprise_feature"));
        std::cout << "✓ Wildcard license enables all features" << std::endl;
        
        std::cout << "✓ Feature access test passed!" << std::endl;
    }

    void testLicenseFileOperations() {
        std::cout << "\n=== Testing License File Operations ===" << std::endl;
        
        // Generate a license
        QString license = licenseManager->generateLicense(
            "File Customer",
            "File Company",
            LicenseManager::LicenseType::Standard,
            QDateTime::currentDateTime().addDays(120),
            {"file_feature"}
        );
        
        // Save license to file
        QString licenseFile = tempDir->path() + "/saved_license.dat";
        bool saved = licenseManager->saveLicenseToFile(licenseFile, license);
        assert(saved);
        assert(QFile::exists(licenseFile));
        std::cout << "✓ License saved to file: " << licenseFile.toStdString() << std::endl;
        
        // Load license from file
        QString loadedLicense = licenseManager->loadLicenseFromFile(licenseFile);
        assert(!loadedLicense.isEmpty());
        std::cout << "✓ License loaded from file" << std::endl;
        
        // Validate loaded license
        LicenseManager::ValidationResult result = licenseManager->validateLicense(loadedLicense);
        assert(result == LicenseManager::ValidationResult::Valid);
        std::cout << "✓ Loaded license is valid" << std::endl;
        
        // Test install from file
        bool installedFromFile = licenseManager->installLicenseFromFile(licenseFile);
        assert(installedFromFile);
        std::cout << "✓ License installed from file" << std::endl;
        
        // Verify installed license
        LicenseManager::LicenseInfo info = licenseManager->getCurrentLicenseInfo();
        assert(info.customerName == "File Customer");
        assert(info.companyName == "File Company");
        std::cout << "✓ Installed license verified" << std::endl;
        
        std::cout << "✓ License file operations test passed!" << std::endl;
    }

    void testLicenseRemoval() {
        std::cout << "\n=== Testing License Removal ===" << std::endl;
        
        // Install a license first
        QString license = licenseManager->generateLicense(
            "Remove Customer",
            "Remove Company",
            LicenseManager::LicenseType::Trial,
            QDateTime::currentDateTime().addDays(30),
            {"temp_feature"}
        );
        
        licenseManager->installLicense(license);
        
        // Verify license is installed
        LicenseManager::LicenseInfo info = licenseManager->getCurrentLicenseInfo();
        assert(info.isValid());
        assert(licenseManager->isFeatureEnabled("temp_feature"));
        std::cout << "✓ License installed and verified" << std::endl;
        
        // Remove license
        bool removed = licenseManager->removeLicense();
        assert(removed);
        std::cout << "✓ License removed successfully" << std::endl;
        
        // Verify license is removed
        info = licenseManager->getCurrentLicenseInfo();
        assert(!info.isValid());
        assert(!licenseManager->isFeatureEnabled("temp_feature"));
        std::cout << "✓ License removal verified" << std::endl;
        
        std::cout << "✓ License removal test passed!" << std::endl;
    }

    void testExpiredLicense() {
        std::cout << "\n=== Testing Expired License ===" << std::endl;
        
        // Generate an expired license (1 day ago)
        QString expiredLicense = licenseManager->generateLicense(
            "Expired Customer",
            "Expired Company",
            LicenseManager::LicenseType::Standard,
            QDateTime::currentDateTime().addDays(-1),  // Expired yesterday
            {"expired_feature"}
        );
        
        // Test validation of expired license
        LicenseManager::ValidationResult result = licenseManager->validateLicense(expiredLicense);
        assert(result == LicenseManager::ValidationResult::Expired);
        std::cout << "✓ Expired license correctly identified" << std::endl;
        
        // Try to install expired license (should fail)
        bool installed = licenseManager->installLicense(expiredLicense);
        assert(!installed);
        std::cout << "✓ Expired license installation correctly rejected" << std::endl;
        
        std::cout << "✓ Expired license test passed!" << std::endl;
    }

    void testSignalEmission() {
        std::cout << "\n=== Testing Signal Emission ===" << std::endl;
        
        // Create signal spies
        QSignalSpy validatedSpy(licenseManager.get(), &LicenseManager::licenseValidated);
        QSignalSpy featureAccessDeniedSpy(licenseManager.get(), &LicenseManager::featureAccessDenied);
        
        // Generate and validate a license
        QString license = licenseManager->generateLicense(
            "Signal Customer",
            "Signal Company",
            LicenseManager::LicenseType::Professional,
            QDateTime::currentDateTime().addDays(365),
            {"signal_feature"}
        );
        
        licenseManager->validateLicense(license);
        
        // Check if licenseValidated signal was emitted
        assert(validatedSpy.count() == 1);
        if (validatedSpy.count() > 0) {
            QList<QVariant> arguments = validatedSpy.takeFirst();
            if (arguments.size() > 0) {
                LicenseManager::ValidationResult result = arguments.at(0).value<LicenseManager::ValidationResult>();
                assert(result == LicenseManager::ValidationResult::Valid);
            }
        }
        std::cout << "✓ licenseValidated signal emitted correctly" << std::endl;
        
        // Remove license and test feature access denial
        licenseManager->removeLicense();
        licenseManager->isFeatureEnabled("denied_feature");
        
        // Check if featureAccessDenied signal was emitted
        assert(featureAccessDeniedSpy.count() == 1);
        if (featureAccessDeniedSpy.count() > 0) {
            QList<QVariant> arguments = featureAccessDeniedSpy.takeFirst();
            if (arguments.size() > 0) {
                assert(arguments.at(0).toString() == "denied_feature");
            }
        }
        std::cout << "✓ featureAccessDenied signal emitted correctly" << std::endl;
        
        std::cout << "✓ Signal emission test passed!" << std::endl;
    }
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    // Register custom types for Qt's meta-object system
    qRegisterMetaType<LicenseManager::ValidationResult>("ValidationResult");
    qRegisterMetaType<LicenseManager::ValidationResult>("LicenseManager::ValidationResult");

    // Initialize Google logging
    google::InitGoogleLogging("license_manager_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);

    std::cout << "Starting LicenseManager Tests..." << std::endl;
    
    try {
        LicenseManagerTester tester;
        
        tester.testHardwareFingerprint();
        tester.testLicenseGeneration();
        tester.testLicenseValidation();
        tester.testLicenseInfo();
        tester.testFeatureAccess();
        tester.testLicenseFileOperations();
        tester.testLicenseRemoval();
        tester.testExpiredLicense();
        tester.testSignalEmission();
        
        std::cout << "\n🎉 All LicenseManager tests passed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}

#include "main.moc"
