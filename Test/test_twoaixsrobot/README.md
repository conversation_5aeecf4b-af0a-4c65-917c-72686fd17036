# 🤖 倾倒机器人控制器 (Pouring Robot Controller)

[![C++](https://img.shields.io/badge/C%2B%2B-17-blue.svg)](https://en.cppreference.com/w/cpp/17)
[![RobWork](https://img.shields.io/badge/RobWork-SDK-green.svg)](https://robwork.dk/)
[![HTTP](https://img.shields.io/badge/HTTP-Client-orange.svg)](https://github.com/yhirose/cpp-httplib)

## 📋 项目概述

倾倒机器人控制器是一个基于RobWork SDK开发的两轴机器人控制系统，专门用于精确的液体倾倒操作。该系统通过HTTP通信与外部服务器交互，实现了智能的倾倒动作规划和执行。

## 🚀 主要特性

### 🎯 精确控制
- **两轴运动控制**: 支持直线轴和旋转轴的精确控制
- **倾倒角度补偿**: 自动计算并补偿旋转时的位置偏移
- **关节限制检查**: 自动检查并限制关节运动范围

### 🌐 网络通信
- **HTTP客户端**: 基于httplib的HTTP通信
- **实时数据传输**: 实时发送关节角度到远程服务器
- **连接状态监控**: 自动检测服务器连接状态

### 🔧 操作模式
- **自动倾倒序列**: 预定义的倾倒动作序列
- **手动控制模式**: 交互式手动控制界面
- **测试模式**: 服务器连接测试功能

### 📊 实时监控
- **TCP位姿显示**: 实时显示工具中心点位置和姿态
- **距离计算**: 计算与目标烧杯的距离
- **状态反馈**: 详细的运动状态信息

## 🏗️ 系统架构

```
PouringRobotController
├── 🔧 核心控制
│   ├── WorkCell加载
│   ├── 机器人设备管理
│   └── TCP框架控制
├── 📐 运动规划
│   ├── 关节角度计算
│   ├── 倾倒补偿算法
│   └── 位置优化
├── 🌐 网络通信
│   ├── HTTP客户端
│   ├── 数据传输
│   └── 状态监控
└── 🎮 用户界面
    ├── 自动模式
    ├── 手动控制
    └── 测试功能
```

## 🛠️ 技术栈

### 核心技术
- **C++17**: 现代C++特性
- **RobWork SDK**: 机器人仿真和控制框架
- **httplib**: 轻量级HTTP客户端库

### 依赖库
- **fuxicommon**: 通用工具库
- **fuxicore**: 核心功能库
- **boost**: C++扩展库
- **xerces-c**: XML解析库
- **glog**: Google日志库
- **Qt5**: GUI框架 (Core, Gui, Widgets)

## 📦 安装与构建

### 系统要求
- Windows 11 (推荐)
- Visual Studio 2019/2022
- RobWork SDK
- CMake 3.16+

### 构建步骤

1. **确保依赖已安装**
```bash
# 确保RobWork SDK已正确安装
# 确保所有依赖库可用
```

2. **构建项目**
```bash
cd Test/test_twoaixsrobot
mkdir build && cd build
cmake ..
cmake --build . --config Release
```

3. **运行程序**
```bash
# 使用默认参数
./PouringRobotController.exe

# 指定WorkCell文件和服务器
./PouringRobotController.exe "path/to/workcell.xml" "*************" 8080
```

## 🚦 快速开始

### 1. 基本配置

```cpp
// 创建控制器实例
PouringRobotController controller("TwoAxisRobot.xml", "localhost", 8080);

// 设置烧杯位置
controller.setBeakerPosition(Vector3D<>(0.0, 0.0, 0.1), 0.05);
```

### 2. 自动倾倒序列

程序启动后会自动执行倾倒序列：
1. 移动到烧杯上方 (垂直位置)
2. 开始轻微倾倒 (15度)
3. 继续倾倒 (30度)
4. 最大倾倒角度 (45度)
5. 回到垂直位置

### 3. 手动控制命令

```bash
# 移动到指定位置
move 0.05 30    # 线性位置0.05m，旋转30度

# 执行倾倒动作
pour 45         # 倾倒到45度角

# 回到初始位置
home

# 测试服务器连接
test

# 退出程序
quit
```

## 📐 关键算法

### 倾倒补偿算法

```cpp
// 计算旋转时TCP的偏移量
double angleRad = -pouringAngle * M_PI / 180.0;
double horizontalOffset = tcpOffset * sin(angleRad);

// 调整横向位置以补偿旋转造成的偏移
double adjustedLinearPosition = beakerCenter[0] - horizontalOffset;
```

### 关节限制检查

```cpp
Q qMin = robot->getBounds().first;
Q qMax = robot->getBounds().second;

for (size_t i = 0; i < q.size(); i++) {
    q[i] = std::clamp(q[i], qMin[i], qMax[i]);
}
```

## 🌐 HTTP API

### 发送关节角度
```http
POST /JointAngles
Content-Type: text/plain

0.05,-0.785398  # 线性位置,旋转角度(弧度)
```

### 获取设备列表
```http
GET /DeviceNames
```

## 🔧 配置参数

### 机器人参数
- **TCP偏移距离**: 0.08m (可调整)
- **关节数量**: 2轴 (直线轴 + 旋转轴)
- **运动范围**: 根据WorkCell文件定义

### 烧杯参数
- **默认位置**: (0.0, 0.0, 0.1)
- **默认半径**: 0.05m
- **倾倒高度**: 0.05m

### 网络参数
- **连接超时**: 5秒
- **读取超时**: 10秒
- **默认端口**: 8080

## 🧪 测试

### 单元测试
```bash
# 测试服务器连接
./PouringRobotController.exe
> test

# 测试基本运动
> move 0.0 0.0
> move 0.05 30
> home
```

### 集成测试
1. 启动HTTP服务器
2. 运行倾倒控制器
3. 验证数据传输
4. 检查运动精度

## 📝 使用示例

```cpp
#include "PouringRobotController.cpp"

int main() {
    try {
        // 创建控制器
        PouringRobotController controller(
            "TwoAxisRobot.xml", 
            "*************", 
            8080
        );
        
        // 设置烧杯位置
        controller.setBeakerPosition(
            Vector3D<>(0.03, 0.0, 0.1), 
            0.02
        );
        
        // 执行倾倒序列
        controller.executePouringSequence();
        
        // 进入手动控制
        controller.manualControl();
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

本项目遵循项目根目录的许可证条款。

---

*精确控制，智能倾倒* 🤖💧
