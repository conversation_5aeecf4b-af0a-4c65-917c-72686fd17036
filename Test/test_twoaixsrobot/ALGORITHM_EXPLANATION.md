# 🤖 双轴倾倒机器人算法详解

## 📋 概述

双轴倾倒机器人采用了一种巧妙的运动补偿算法，确保在倾倒过程中工具中心点(TCP)始终保持在烧杯中心线上方，实现精确的液体倾倒控制。

## 🏗️ 机器人结构

### 轴配置
```
双轴机器人结构:
┌─────────────────────────────────┐
│  轴1: 直线轴 (Linear Axis)      │  ← 水平移动
│  - 控制TCP的水平位置            │
│  - 单位: 米 (m)                 │
│  - 范围: [qMin[0], qMax[0]]     │
└─────────────────────────────────┘
           │
           ▼
┌─────────────────────────────────┐
│  轴2: 旋转轴 (Rotation Axis)    │  ← 倾倒角度
│  - 控制倾倒角度                 │
│  - 单位: 弧度 (rad)             │
│  - 范围: [qMin[1], qMax[1]]     │
└─────────────────────────────────┘
```

## 🎯 核心算法

### 1. 基础关节角度计算

```cpp
Q calculateJointAngles(double linearPosition, double rotationAngle) {
    Q q(robot->getDOF());
    
    // 轴1: 直线位置 (米)
    q[0] = linearPosition;
    
    // 轴2: 旋转角度 (度→弧度，反向)
    q[1] = -rotationAngle * M_PI / 180.0;
    
    return q;
}
```

**关键点:**
- `q[0]`: 直线轴位置，直接对应TCP的水平坐标
- `q[1]`: 旋转轴角度，使用负值实现正确的倾倒方向
- 角度转换: 度 → 弧度 (× π/180)

### 2. 倾倒补偿算法 ⭐

这是算法的核心部分，解决了旋转时TCP偏移的问题：

```cpp
Q calculatePouringJointAngles(double pouringAngle) {
    // 关键参数
    double tcpOffset = 0.08;  // TCP到旋转中心距离
    
    // 计算偏移量
    double angleRad = -pouringAngle * M_PI / 180.0;
    double horizontalOffset = tcpOffset * sin(angleRad);
    
    // 位置补偿
    double adjustedLinearPosition = beakerCenter[0] - horizontalOffset;
    
    return calculateJointAngles(adjustedLinearPosition, pouringAngle);
}
```

## 📐 数学原理

### 几何关系图解

```
倾倒前 (垂直状态):
    │
    │ TCP
    │  ●
    │  │ tcpOffset = 0.08m
    │  │
    ●──┴── 旋转中心
    │
    │ 烧杯中心
    ●

倾倒时 (角度θ):
         TCP
          ●
         ╱│
        ╱ │ tcpOffset
       ╱  │
      ╱θ  │
     ●────┴── 旋转中心
     │
     │ 烧杯中心  
     ●
     
水平偏移 = tcpOffset × sin(θ)
```

### 数学公式推导

1. **旋转变换**
   ```
   当机器人旋转角度θ时，TCP相对于旋转中心的位置变化：
   
   x_offset = tcpOffset × sin(θ)
   y_offset = tcpOffset × cos(θ) - tcpOffset
   ```

2. **水平补偿**
   ```
   为保持TCP在烧杯中心线上：
   
   调整后位置 = 烧杯中心位置 - 水平偏移
   adjustedLinearPosition = beakerCenter[0] - horizontalOffset
   ```

3. **角度处理**
   ```
   使用负角度确保正确的倾倒方向：
   angleRad = -pouringAngle × π/180
   ```

## 🔄 倾倒序列执行

### 动作序列
```cpp
void executePouringSequence() {
    // 1. 垂直位置 (0°)
    Q q1 = calculatePouringJointAngles(0.0);
    
    // 2. 轻微倾倒 (15°)
    Q q2 = calculatePouringJointAngles(15.0);
    
    // 3. 继续倾倒 (30°)
    Q q3 = calculatePouringJointAngles(30.0);
    
    // 4. 最大倾倒 (45°)
    Q q4 = calculatePouringJointAngles(45.0);
    
    // 5. 回到垂直 (0°)
    moveToPosition(q1);
}
```

### 位置补偿计算示例

假设参数：
- `tcpOffset = 0.08m`
- `beakerCenter[0] = 0.0m`
- `pouringAngle = 30°`

计算过程：
```
1. 角度转换: angleRad = -30° × π/180 = -0.524 rad
2. 水平偏移: horizontalOffset = 0.08 × sin(-0.524) = -0.04m
3. 调整位置: adjustedLinearPosition = 0.0 - (-0.04) = 0.04m
```

结果：机器人需要向右移动0.04m来补偿旋转造成的偏移。

## 🛡️ 安全机制

### 1. 关节限制检查
```cpp
for (size_t i = 0; i < q.size(); i++) {
    if (q[i] < qMin[i]) {
        q[i] = qMin[i];  // 限制到最小值
    }
    if (q[i] > qMax[i]) {
        q[i] = qMax[i];  // 限制到最大值
    }
}
```

### 2. 边界保护
- 自动检测并限制关节运动范围
- 防止机器人超出物理限制
- 实时警告信息输出

## 📊 算法优势

### 1. 精确性
- **位置补偿**: 确保TCP始终在目标位置上方
- **角度控制**: 精确的倾倒角度控制
- **实时计算**: 每次运动都重新计算最优位置

### 2. 安全性
- **边界检查**: 防止超出机器人工作范围
- **渐进倾倒**: 分步骤执行，避免突然动作
- **状态监控**: 实时反馈运动状态

### 3. 灵活性
- **参数可调**: tcpOffset等参数可根据实际机器人调整
- **多种模式**: 支持自动序列和手动控制
- **扩展性**: 易于添加新的倾倒模式

## 🔧 参数调优

### 关键参数说明

1. **tcpOffset (0.08m)**
   - TCP到旋转中心的距离
   - 需要根据实际机器人结构测量
   - 影响补偿计算的精度

2. **beakerCenter**
   - 烧杯中心位置坐标
   - 决定倾倒的目标位置
   - 可动态调整

3. **pouringHeight (0.05m)**
   - 倾倒时的高度
   - 影响液体流动效果
   - 需要根据液体特性调整

## 🎯 应用场景

### 适用场景
- 实验室自动化液体处理
- 工业生产线液体分配
- 精密化学试剂配制
- 食品饮料自动化生产

### 扩展可能
- 多容器顺序倾倒
- 流量控制集成
- 视觉引导定位
- 力反馈控制

---

*精确算法，智能倾倒* 🤖📐
