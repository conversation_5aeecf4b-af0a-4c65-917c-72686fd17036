#include <rw/rw.hpp>
#include <rw/loaders/WorkCellLoader.hpp>
#include <rw/models/WorkCell.hpp>
#include <rw/models/Device.hpp>
#include <rw/kinematics/State.hpp>
#include <rw/math/Q.hpp>
#include <rw/math/Transform3D.hpp>
#include <rw/math/Vector3D.hpp>
#include <rw/math/RPY.hpp>
#include <iostream>
#include <cmath>
#include <thread>
#include <sstream>
#include <chrono>
#include <math.h>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

#include "httplib.h"

using namespace rw::common;
using namespace rw::loaders;
using namespace rw::models;
using namespace rw::kinematics;
using namespace rw::math;

class PouringRobotController {
private:
    WorkCell::Ptr workcell;
    Device::Ptr robot;
    State state;
    Frame* tcpFrame;

    // 烧杯位置参数
    Vector3D<> beakerCenter;
    double beakerRadius;
    double pouringHeight;

    // HTTP客户端参数
    std::string serverHost;
    
    int serverPort;
    httplib::Client httpClient;
    
public:
    PouringRobotController(const std::string& workcellFile, const std::string& host = "localhost", int port = 8080)
        : serverHost(host), serverPort(port), httpClient(host, port) {
        // 加载WorkCell
        workcell = WorkCellLoader::Factory::load(workcellFile);
        if (workcell == nullptr) {
            throw std::runtime_error("Failed to load WorkCell: " + workcellFile);
        }

        // 获取机器人设备
        robot = workcell->findDevice("TwoAxisRobot");
        if (robot == nullptr) {
            throw std::runtime_error("Robot 'TwoAxisRobot' not found in WorkCell");
        }

        // 获取TCP框架
        tcpFrame = workcell->findFrame("TwoAxisRobot.TCP");
        if (tcpFrame == nullptr) {
            throw std::runtime_error("TCP frame not found");
        }

        // 初始化状态
        state = workcell->getDefaultState();

        // 设置烧杯参数 (可根据实际情况调整)
        beakerCenter = Vector3D<>(0.03, 0.0, 0.1);  // 烧杯中心位置
        beakerRadius = 0.02;  // 烧杯半径 5cm
        pouringHeight = 0.05; // 倒样品时的高度

        // 设置HTTP客户端超时
        httpClient.set_connection_timeout(5, 0); // 5秒连接超时
        httpClient.set_read_timeout(10, 0);      // 10秒读取超时

        std::cout << "Pouring Robot Controller initialized successfully!" << std::endl;
        std::cout << "Robot DOF: " << robot->getDOF() << std::endl;
        std::cout << "HTTP Server: " << serverHost << ":" << serverPort << std::endl;
    }
    
    // 直接设置关节角度 - 适用于两轴机器人
    Q calculateJointAngles(double linearPosition = 0.0, double rotationAngle = 0.0) {
        Q q(robot->getDOF());

        // 轴1 (直线轴): 横向位置 (米)
        q[0] = linearPosition;

        // 轴2 (旋转轴): 旋转角度 (度转弧度) - 反向旋转
        q[1] = -rotationAngle * M_PI / 180.0;

        // 检查关节限制
        Q qMin = robot->getBounds().first;
        Q qMax = robot->getBounds().second;

        for (size_t i = 0; i < q.size(); i++) {
            if (q[i] < qMin[i]) {
                q[i] = qMin[i];
                std::cout << "Warning: Joint " << i << " limited to minimum value: " << qMin[i] << std::endl;
            }
            if (q[i] > qMax[i]) {
                q[i] = qMax[i];
                std::cout << "Warning: Joint " << i << " limited to maximum value: " << qMax[i] << std::endl;
            }
        }

        return q;
    }

    // 倾倒序列的关节角度计算 - 保持TCP在烧杯中心线
    Q calculatePouringJointAngles(double pouringAngle = 0.0) {
        // TCP到旋转中心的距离 (需要根据实际机器人结构调整)
        double tcpOffset = 0.08; // TCP相对于旋转轴的偏移距离

        // 计算旋转时TCP的偏移量
        double angleRad = -pouringAngle * M_PI / 180.0; // 使用负角度
        double horizontalOffset = tcpOffset * sin(angleRad);

        // 调整横向位置以补偿旋转造成的偏移
        double adjustedLinearPosition = beakerCenter[0] - horizontalOffset;

        std::cout << "Pouring angle: " << pouringAngle << "deg, Linear adjustment: "
                  << horizontalOffset << "m, Final position: " << adjustedLinearPosition << "m" << std::endl;

        return calculateJointAngles(adjustedLinearPosition, pouringAngle);
    }
    
    // 执行倾倒动作序列
    void executePouringSequence() {
        std::cout << "\n=== Starting pouring sequence ===" << std::endl;

        // 1. 移动到烧杯上方 (垂直位置)
        std::cout << "1. Moving above beaker (vertical position)..." << std::endl;
        Q q1 = calculatePouringJointAngles(0.0);
        moveToPosition(q1);
        printCurrentPose();
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));

        // 2. 开始倾倒 (15度)
        std::cout << "\n2. Starting slight pour (15 degrees)..." << std::endl;
        Q q2 = calculatePouringJointAngles(15.0);
        moveToPosition(q2);
        printCurrentPose();
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));

        // 3. 继续倾倒 (30度)
        std::cout << "\n3. Continuing pour (30 degrees)..." << std::endl;
        Q q3 = calculatePouringJointAngles(30.0);
        moveToPosition(q3);
        printCurrentPose();
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));

        // 4. 最大倾倒角度 (45度)
        std::cout << "\n4. Maximum pour (45 degrees)..." << std::endl;
        Q q4 = calculatePouringJointAngles(45.0);
        moveToPosition(q4);
        printCurrentPose();
        std::this_thread::sleep_for(std::chrono::milliseconds(2000));

        // 5. 回到垂直位置
        std::cout << "\n5. Returning to vertical position..." << std::endl;
        moveToPosition(q1);
        printCurrentPose();

        std::cout << "\n=== Pouring sequence completed ===" << std::endl;
    }
    
    // 移动到指定关节位置并发送到服务器
    void moveToPosition(const Q& q) {
        robot->setQ(q, state);

        std::cout << "Joint values: [";
        for (size_t i = 0; i < q.size(); i++) {
            if (i == 0) {
                std::cout << "Linear: " << q[i] << "m";
            } else if (i == 1) {
                std::cout << ", Rotation: " << q[i]*180/M_PI << "deg (" << q[i] << "rad)";
            } else {
                std::cout << ", Joint" << i << ": " << q[i];
            }
        }
        std::cout << "]" << std::endl;

        // 发送关节角度到服务器
        sendJointAnglesToServer(q);
    }

    // 发送关节角度到HTTP服务器
    void sendJointAnglesToServer(const Q& q) {
        try {
            // 构建关节角度字符串
            std::stringstream ss;
            for (size_t i = 0; i < q.size(); i++) {
                if (i > 0) ss << ",";
                ss << q[i];
            }
            std::string jointAnglesStr = ss.str();

            std::cout << "Sending to server: " << jointAnglesStr << std::endl;

            // 发送POST请求
            auto res = httpClient.Post("/JointAngles", jointAnglesStr, "text/plain");

            if (res) {
                if (res->status == 200) {
                    std::cout << "Server response: " << res->body << std::endl;
                } else {
                    std::cout << "Server error: " << res->status << " - " << res->body << std::endl;
                }
            } else {
                std::cout << "Failed to connect to server at " << serverHost << ":" << serverPort << std::endl;
            }

        } catch (const std::exception& e) {
            std::cout << "HTTP request error: " << e.what() << std::endl;
        }
        Sleep(200);
    }
    
    // 打印当前TCP位姿
    void printCurrentPose() {
        Transform3D<> tcpTransform = tcpFrame->getTransform(state);
        Vector3D<> pos = tcpTransform.P();
        RPY<> rpy(tcpTransform.R());
        
        std::cout << "TCP位置: (" << pos[0] << ", " << pos[1] << ", " << pos[2] << ")" << std::endl;
        std::cout << "TCP姿态: (" << rpy[0]*180/M_PI << "°, " << rpy[1]*180/M_PI << "°, " << rpy[2]*180/M_PI << "°)" << std::endl;
        
        // 计算与烧杯中心的距离
        Vector3D<> diff = pos - beakerCenter;
        diff[2] = 0; // 只考虑水平距离
        double distance = diff.norm2();
        std::cout << "与烧杯中心水平距离: " << distance << " m" << std::endl;
    }
    
    // 设置烧杯位置
    void setBeakerPosition(const Vector3D<>& center, double radius) {
        beakerCenter = center;
        beakerRadius = radius;
        std::cout << "烧杯位置设置为: (" << center[0] << ", " << center[1] << ", " << center[2] << ")" << std::endl;
        std::cout << "烧杯半径: " << radius << " m" << std::endl;
    }
    
    // 手动控制模式
    void manualControl() {
        std::cout << "\n=== Manual Control Mode ===" << std::endl;
        std::cout << "Available commands:" << std::endl;
        std::cout << "  move <linear_pos> <rotation_angle> - Move to specified linear position (m) and rotation angle (deg)" << std::endl;
        std::cout << "  pour <angle> - Execute pouring action to specified angle (deg)" << std::endl;
        std::cout << "  home - Return to initial position" << std::endl;
        std::cout << "  test - Test server connection" << std::endl;
        std::cout << "  quit - Exit" << std::endl;
        std::cout << "\nJoint limits info:" << std::endl;
        Q qMin = robot->getBounds().first;
        Q qMax = robot->getBounds().second;
        std::cout << "  Joint 1 (linear): [" << qMin[0] << ", " << qMax[0] << "] m" << std::endl;
        std::cout << "  Joint 2 (rotation): [" << qMin[1]*180/M_PI << ", " << qMax[1]*180/M_PI << "] deg" << std::endl;

        std::string command;
        while (std::cin >> command && command != "quit") {
            if (command == "move") {
                double linear_pos, rotation_angle;
                std::cin >> linear_pos >> rotation_angle;
                Q q = calculateJointAngles(linear_pos, rotation_angle);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "pour") {
                double angle;
                std::cin >> angle;
                Q q = calculatePouringJointAngles(angle);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "home") {
                Q q = calculateJointAngles(0.0, 0.0);
                moveToPosition(q);
                printCurrentPose();
            }
            else if (command == "test") {
                testServerConnection();
            }
            else {
                std::cout << "Unknown command: " << command << std::endl;
            }
            std::cout << "\nEnter next command: ";
        }
    }

    // 测试服务器连接
    void testServerConnection() {
        std::cout << "Testing server connection..." << std::endl;

        // 测试获取设备名称
        auto res = httpClient.Get("/DeviceNames");
        if (res) {
            if (res->status == 200) {
                std::cout << "Server connection OK!" << std::endl;
                std::cout << "Available devices: " << res->body << std::endl;
            } else {
                std::cout << "Server error: " << res->status << std::endl;
            }
        } else {
            std::cout << "Failed to connect to server!" << std::endl;
        }
    }
};

int main(int argc, char** argv) {
    try {
        // 解析命令行参数
        std::string workcellFile = "D:/RobWork/RobWork/example/ModelData/TwoAxisRobot.xml";
        std::string serverHost = "localhost";
        int serverPort = 8080;

        if (argc > 1) {
            workcellFile = argv[1];
        }
        if (argc > 2) {
            serverHost = argv[2];
        }
        if (argc > 3) {
            serverPort = std::stoi(argv[3]);
        }

        std::cout << "Loading WorkCell file: " << workcellFile << std::endl;
        std::cout << "Target server: " << serverHost << ":" << serverPort << std::endl;

        // 创建控制器
        PouringRobotController controller(workcellFile, serverHost, serverPort);

        // 设置烧杯位置 (可根据实际情况调整)
        controller.setBeakerPosition(Vector3D<>(0.0, 0.0, 0.1), 0.05);

        // 测试服务器连接
        controller.testServerConnection();

        // 执行倾倒序列演示
        std::cout << "\nStarting automatic pouring sequence in 3 seconds..." << std::endl;
        std::this_thread::sleep_for(std::chrono::seconds(3));
        controller.executePouringSequence();

        // 进入手动控制模式
        controller.manualControl();

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return -1;
    }

    return 0;
}
