#include <iostream>
#include <cassert>
#include <vector>
#include <string>

#include "sqlite3.h"
#include "glog.h"

class SQLite3Tester {
private:
    sqlite3* db = nullptr;
    std::string dbPath = "test_database.db";

public:
    ~SQLite3Tester() {
        if (db) {
            sqlite3_close(db);
        }
    }

    void testDatabaseCreation() {
        std::cout << "\n=== Testing Database Creation ===" << std::endl;

        // Delete any existing test database
        remove(dbPath.c_str());

        int rc = sqlite3_open(dbPath.c_str(), &db);

        if (rc) {
            std::cerr << "Can't open database: " << sqlite3_errmsg(db) << std::endl;
            assert(false);
        } else {
            std::cout << "Database created successfully" << std::endl;
        }

        assert(db != nullptr);
        std::cout << "Database creation test passed!" << std::endl;
    }
    
    void testTableCreation() {
        std::cout << "\n=== Testing Table Creation ===" << std::endl;

        const char* sql = R"(
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                age INTEGER,
                email TEXT UNIQUE
            );
        )";

        char* errMsg = nullptr;
        int rc = sqlite3_exec(db, sql, nullptr, nullptr, &errMsg);

        if (rc != SQLITE_OK) {
            std::cerr << "SQL error: " << errMsg << std::endl;
            sqlite3_free(errMsg);
            assert(false);
        } else {
            std::cout << "Table created successfully" << std::endl;
        }

        std::cout << "Table creation test passed!" << std::endl;
    }
    
    void testDataInsertion() {
        std::cout << "\n=== Testing Data Insertion ===" << std::endl;

        // Prepare insert statement
        const char* sql = "INSERT INTO users (name, age, email) VALUES (?, ?, ?);";
        sqlite3_stmt* stmt;

        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        // Test data
        struct User {
            std::string name;
            int age;
            std::string email;
        };

        std::vector<User> testUsers = {
            {"Alice", 25, "<EMAIL>"},
            {"Bob", 30, "<EMAIL>"},
            {"Charlie", 35, "<EMAIL>"}
        };

        int insertedCount = 0;
        for (const auto& user : testUsers) {
            // Bind parameters
            sqlite3_bind_text(stmt, 1, user.name.c_str(), -1, SQLITE_STATIC);
            sqlite3_bind_int(stmt, 2, user.age);
            sqlite3_bind_text(stmt, 3, user.email.c_str(), -1, SQLITE_STATIC);

            // Execute insert
            rc = sqlite3_step(stmt);
            if (rc == SQLITE_DONE) {
                insertedCount++;
                std::cout << "Inserted user: " << user.name << std::endl;
            } else {
                std::cerr << "Insert failed for user: " << user.name << std::endl;
            }

            // Reset statement for next use
            sqlite3_reset(stmt);
        }

        sqlite3_finalize(stmt);

        assert(insertedCount == 3);
        std::cout << "Data insertion test passed! Inserted " << insertedCount << " records" << std::endl;
    }
    
    void testDataQuery() {
        std::cout << "\n=== Testing Data Query ===" << std::endl;

        const char* sql = "SELECT id, name, age, email FROM users ORDER BY age;";
        sqlite3_stmt* stmt;

        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        int recordCount = 0;
        std::cout << "Query results:" << std::endl;
        std::cout << "ID\tName\t\tAge\tEmail" << std::endl;
        std::cout << "----------------------------------------" << std::endl;

        while ((rc = sqlite3_step(stmt)) == SQLITE_ROW) {
            int id = sqlite3_column_int(stmt, 0);
            const char* name = (const char*)sqlite3_column_text(stmt, 1);
            int age = sqlite3_column_int(stmt, 2);
            const char* email = (const char*)sqlite3_column_text(stmt, 3);

            std::cout << id << "\t" << name << "\t\t" << age << "\t" << email << std::endl;
            recordCount++;
        }

        sqlite3_finalize(stmt);

        assert(recordCount == 3);
        std::cout << "Data query test passed! Retrieved " << recordCount << " records" << std::endl;
    }
    
    void testDataUpdate() {
        std::cout << "\n=== Testing Data Update ===" << std::endl;

        // Update Alice's age
        const char* sql = "UPDATE users SET age = ? WHERE name = ?;";
        sqlite3_stmt* stmt;

        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        sqlite3_bind_int(stmt, 1, 26);  // New age
        sqlite3_bind_text(stmt, 2, "Alice", -1, SQLITE_STATIC);

        rc = sqlite3_step(stmt);
        assert(rc == SQLITE_DONE);

        int changedRows = sqlite3_changes(db);
        std::cout << "Updated " << changedRows << " row(s)" << std::endl;

        sqlite3_finalize(stmt);

        // Verify update
        const char* verifySql = "SELECT age FROM users WHERE name = 'Alice';";
        sqlite3_stmt* verifyStmt;

        rc = sqlite3_prepare_v2(db, verifySql, -1, &verifyStmt, nullptr);
        assert(rc == SQLITE_OK);

        rc = sqlite3_step(verifyStmt);
        assert(rc == SQLITE_ROW);

        int newAge = sqlite3_column_int(verifyStmt, 0);
        assert(newAge == 26);

        sqlite3_finalize(verifyStmt);

        std::cout << "Data update test passed! Alice's age updated to " << newAge << std::endl;
    }
    
    void testDataDeletion() {
        std::cout << "\n=== Testing Data Deletion ===" << std::endl;

        // Delete Charlie
        const char* sql = "DELETE FROM users WHERE name = ?;";
        sqlite3_stmt* stmt;

        int rc = sqlite3_prepare_v2(db, sql, -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        sqlite3_bind_text(stmt, 1, "Charlie", -1, SQLITE_STATIC);

        rc = sqlite3_step(stmt);
        assert(rc == SQLITE_DONE);

        int deletedRows = sqlite3_changes(db);
        std::cout << "Deleted " << deletedRows << " row(s)" << std::endl;

        sqlite3_finalize(stmt);

        // Verify deletion
        const char* countSql = "SELECT COUNT(*) FROM users;";
        sqlite3_stmt* countStmt;

        rc = sqlite3_prepare_v2(db, countSql, -1, &countStmt, nullptr);
        assert(rc == SQLITE_OK);

        rc = sqlite3_step(countStmt);
        assert(rc == SQLITE_ROW);

        int remainingCount = sqlite3_column_int(countStmt, 0);
        assert(remainingCount == 2);

        sqlite3_finalize(countStmt);

        std::cout << "Data deletion test passed! " << remainingCount << " records remaining" << std::endl;
    }
    
    void testTransactions() {
        std::cout << "\n=== Testing Transactions ===" << std::endl;

        // Begin transaction
        char* errMsg = nullptr;
        int rc = sqlite3_exec(db, "BEGIN TRANSACTION;", nullptr, nullptr, &errMsg);
        assert(rc == SQLITE_OK);

        // Insert some test data
        const char* insertSql = "INSERT INTO users (name, age, email) VALUES ('David', 40, '<EMAIL>');";
        rc = sqlite3_exec(db, insertSql, nullptr, nullptr, &errMsg);
        assert(rc == SQLITE_OK);

        // Check if data exists (within transaction)
        const char* checkSql = "SELECT COUNT(*) FROM users WHERE name = 'David';";
        sqlite3_stmt* stmt;

        rc = sqlite3_prepare_v2(db, checkSql, -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        rc = sqlite3_step(stmt);
        assert(rc == SQLITE_ROW);

        int count = sqlite3_column_int(stmt, 0);
        assert(count == 1);

        sqlite3_finalize(stmt);

        // Rollback transaction
        rc = sqlite3_exec(db, "ROLLBACK;", nullptr, nullptr, &errMsg);
        assert(rc == SQLITE_OK);

        // Verify data has been rolled back
        rc = sqlite3_prepare_v2(db, checkSql, -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        rc = sqlite3_step(stmt);
        assert(rc == SQLITE_ROW);

        count = sqlite3_column_int(stmt, 0);
        assert(count == 0);

        sqlite3_finalize(stmt);

        std::cout << "Transaction test passed! Rollback worked correctly" << std::endl;
    }
    
    void testSQLiteInfo() {
        std::cout << "\n=== Testing SQLite Info ===" << std::endl;

        std::cout << "SQLite version: " << sqlite3_libversion() << std::endl;
        std::cout << "SQLite version number: " << sqlite3_libversion_number() << std::endl;

        // Get database file size
        const char* pragmaSql = "PRAGMA page_count; PRAGMA page_size;";
        sqlite3_stmt* stmt;

        // Get page count
        int rc = sqlite3_prepare_v2(db, "PRAGMA page_count;", -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        rc = sqlite3_step(stmt);
        if (rc == SQLITE_ROW) {
            int pageCount = sqlite3_column_int(stmt, 0);
            std::cout << "Database page count: " << pageCount << std::endl;
        }
        sqlite3_finalize(stmt);

        // Get page size
        rc = sqlite3_prepare_v2(db, "PRAGMA page_size;", -1, &stmt, nullptr);
        assert(rc == SQLITE_OK);

        rc = sqlite3_step(stmt);
        if (rc == SQLITE_ROW) {
            int pageSize = sqlite3_column_int(stmt, 0);
            std::cout << "Database page size: " << pageSize << " bytes" << std::endl;
        }
        sqlite3_finalize(stmt);

        std::cout << "SQLite info test passed!" << std::endl;
    }
};

int main() {
    // Initialize Google logging
    google::InitGoogleLogging("sqlite_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting SQLite3 Tests..." << std::endl;
    
    try {
        SQLite3Tester tester;

        // Run all tests
        tester.testDatabaseCreation();
        tester.testTableCreation();
        tester.testDataInsertion();
        tester.testDataQuery();
        tester.testDataUpdate();
        tester.testDataDeletion();
        tester.testTransactions();
        tester.testSQLiteInfo();
        
        std::cout << "\nAll SQLite3 tests passed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
