#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <cassert>
#include <memory>
#include <string>
#include <map>

#include "EventListenerSupport.h"
#include "glog.h"

using namespace Fuxi::Common;
using namespace std::chrono_literals;

// 测试用的事件数据类型
struct TestEvent {
    int id;
    std::string message;
    
    TestEvent() : id(0), message("") {}
    TestEvent(int i, const std::string& msg) : id(i), message(msg) {}
    
    bool operator==(const TestEvent& other) const {
        return id == other.id && message == other.message;
    }
};

// 测试用的事件类型枚举
enum class TestEventType {
    INFO,
    WARNING,
    EventERROR,
    CRITICAL
};

// 将事件类型转换为字符串，用于输出
std::string eventTypeToString(TestEventType type) {
    switch (type) {
        case TestEventType::INFO: return "INFO";
        case TestEventType::WARNING: return "WARNING";
        case TestEventType::EventERROR: return "ERROR";
        case TestEventType::CRITICAL: return "CRITICAL";
        default: return "UNKNOWN";
    }
}

class EventListenerTester {
private:
    using TestEventData = EventData<TestEvent, TestEventType>;
    EventListenerSupport<TestEvent, TestEventType> eventSupport;
    std::atomic<int> eventCounter{0};
    std::atomic<int> infoCounter{0};
    std::atomic<int> warningCounter{0};
    std::atomic<int> errorCounter{0};
    std::atomic<int> criticalCounter{0};

public:
    void testBasicEventNotification() {
        std::cout << "\n=== Testing Basic Event Notification ===" << std::endl;
        
        eventCounter = 0;
        
        // 添加一个简单的事件监听器
        eventSupport.addEventListener([this](TestEventData data) {
            eventCounter++;
            std::cout << "Event received: ID=" << data._event.id 
                      << ", Message='" << data._event.message 
                      << "', Type=" << eventTypeToString(data._eventType) << std::endl;
        });
        
        // 发送测试事件
        TestEvent event1(1, "Test event 1");
        eventSupport.notifyEvent(event1, TestEventType::INFO);
        
        // 等待事件处理完成
        std::this_thread::sleep_for(100ms);
        
        assert(eventCounter.load() == 1);
        std::cout << "✓ Basic event notification test passed!" << std::endl;
    }
    
    void testMultipleListeners() {
        std::cout << "\n=== Testing Multiple Listeners ===" << std::endl;
        
        eventCounter = 0;
        
        // 添加第二个监听器
        eventSupport.addEventListener([this](TestEventData data) {
            eventCounter++;
            std::cout << "Second listener received event: ID=" << data._event.id << std::endl;
        });
        
        // 发送测试事件
        TestEvent event2(2, "Test event 2");
        eventSupport.notifyEvent(event2, TestEventType::WARNING);
        
        // 等待事件处理完成
        std::this_thread::sleep_for(100ms);
        
        // 应该有两个监听器各收到一个事件，总计2个事件计数
        assert(eventCounter.load() == 2);
        std::cout << "✓ Multiple listeners test passed!" << std::endl;
    }
    
    void testEventTypeFiltering() {
        std::cout << "\n=== Testing Event Type Filtering ===" << std::endl;
        
        // 重置计数器
        infoCounter = 0;
        warningCounter = 0;
        errorCounter = 0;
        criticalCounter = 0;
        
        // 创建新的事件支持对象，避免与之前的测试干扰
        EventListenerSupport<TestEvent, TestEventType> filteredEventSupport;
        
        // 添加按类型过滤的监听器
        filteredEventSupport.addEventListener([this](TestEventData data) {
            switch (data._eventType) {
                case TestEventType::INFO:
                    infoCounter++;
                    break;
                case TestEventType::WARNING:
                    warningCounter++;
                    break;
                case TestEventType::EventERROR:
                    errorCounter++;
                    break;
                case TestEventType::CRITICAL:
                    criticalCounter++;
                    break;
            }
        });
        
        // 发送不同类型的事件
        TestEvent event(3, "Test event");
        filteredEventSupport.notifyEvent(event, TestEventType::INFO);
        filteredEventSupport.notifyEvent(event, TestEventType::WARNING);
        filteredEventSupport.notifyEvent(event, TestEventType::EventERROR);
        filteredEventSupport.notifyEvent(event, TestEventType::CRITICAL);
        filteredEventSupport.notifyEvent(event, TestEventType::INFO);  // 再发一个INFO
        
        // 等待事件处理完成
        std::this_thread::sleep_for(100ms);
        
        assert(infoCounter.load() == 2);
        assert(warningCounter.load() == 1);
        assert(errorCounter.load() == 1);
        assert(criticalCounter.load() == 1);
        
        std::cout << "✓ Event counts by type: INFO=" << infoCounter.load()
                  << ", WARNING=" << warningCounter.load()
                  << ", ERROR=" << errorCounter.load()
                  << ", CRITICAL=" << criticalCounter.load() << std::endl;
        std::cout << "✓ Event type filtering test passed!" << std::endl;
    }
    
    void testConcurrentEventNotification() {
        std::cout << "\n=== Testing Concurrent Event Notification ===" << std::endl;
        
        // 重置计数器
        eventCounter = 0;
        
        // 创建新的事件支持对象
        EventListenerSupport<TestEvent, TestEventType> concurrentEventSupport;
        
        // 添加监听器
        concurrentEventSupport.addEventListener([this](TestEventData data) {
            eventCounter++;
            // 模拟处理时间
            std::this_thread::sleep_for(10ms);
            std::cout << "Processed event ID: " << data._event.id << std::endl;
        });
        
        // 并发发送多个事件
        const int numEvents = 10;
        std::vector<std::thread> threads;
        
        for (int i = 0; i < numEvents; ++i) {
            threads.emplace_back([&concurrentEventSupport, i]() {
                TestEvent event(100 + i, "Concurrent event " + std::to_string(i));
                concurrentEventSupport.notifyEvent(event, TestEventType::INFO);
            });
        }
        
        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }
        
        // 等待事件处理完成
        std::this_thread::sleep_for(500ms);
        
        assert(eventCounter.load() == numEvents);
        std::cout << "✓ Processed " << eventCounter.load() << " concurrent events" << std::endl;
        std::cout << "✓ Concurrent event notification test passed!" << std::endl;
    }
    
    void testEventDataConstructor() {
        std::cout << "\n=== Testing EventData Constructor ===" << std::endl;
        
        // 测试EventData构造函数
        TestEvent event(42, "Constructor test");
        TestEventData eventData(event, TestEventType::EventERROR);
        
        assert(eventData._event.id == 42);
        assert(eventData._event.message == "Constructor test");
        assert(eventData._eventType == TestEventType::EventERROR);
        
        std::cout << "✓ EventData constructor test passed!" << std::endl;
    }
    
    void testCommonEventTypes() {
        std::cout << "\n=== Testing Common Event Types ===" << std::endl;
        
        // 测试预定义的通用事件类型
        std::map<CommonType, std::string> typeNames = {
            {CommonType::ADD, "ADD"},
            {CommonType::DEL, "DELETE"},
            {CommonType::UPDATE, "UPDATE"}
        };
        
        // 重置计数器
        std::map<CommonType, int> typeCounts;
        typeCounts[CommonType::ADD] = 0;
        typeCounts[CommonType::DEL] = 0;
        typeCounts[CommonType::UPDATE] = 0;
        
        // 创建使用CommonType的事件支持
        EventListenerSupport<std::string, CommonType> commonEventSupport;
        
        // 添加监听器
        commonEventSupport.addEventListener([&typeCounts](EventData<std::string, CommonType> data) {
            typeCounts[data._eventType]++;
            std::cout << "Common event: " << data._event << ", Type: " 
                      << (data._eventType == CommonType::ADD ? "ADD" : 
                          data._eventType == CommonType::DEL ? "DELETE" : "UPDATE") 
                      << std::endl;
        });
        
        // 发送各种类型的事件
        commonEventSupport.notifyEvent("Item 1 added", CommonType::ADD);
        commonEventSupport.notifyEvent("Item 2 deleted", CommonType::DEL);
        commonEventSupport.notifyEvent("Item 3 updated", CommonType::UPDATE);
        commonEventSupport.notifyEvent("Item 4 added", CommonType::ADD);
        
        // 等待事件处理完成
        std::this_thread::sleep_for(100ms);
        
        assert(typeCounts[CommonType::ADD] == 2);
        assert(typeCounts[CommonType::DEL] == 1);
        assert(typeCounts[CommonType::UPDATE] == 1);
        
        std::cout << "✓ Common event types test passed!" << std::endl;
    }
};

int main() {
    // 初始化Google日志
    google::InitGoogleLogging("event_listener_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting EventListenerSupport Tests..." << std::endl;
    
    try {
        EventListenerTester tester;
        
        tester.testBasicEventNotification();
        tester.testMultipleListeners();
        tester.testEventTypeFiltering();
        tester.testConcurrentEventNotification();
        tester.testEventDataConstructor();
        tester.testCommonEventTypes();
        
        std::cout << "\n🎉 All EventListenerSupport tests passed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
