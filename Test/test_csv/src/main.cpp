#include <iostream>
#include <cassert>
#include <string>
#include <fstream>
#include <cstdio>
#include <direct.h>
#include <algorithm>

#include "parser/CSVParser.h"
#include "parser/FileIOOperation.h"
#include "parser/StringTransform.h"
#include "glog.h"

class CSVTester {
private:
    std::string testDir = "test_csv_data";
    std::string simpleCSV = "simple.csv";
    std::string complexCSV = "complex.csv";
    std::string emptyCSV = "empty.csv";
    std::string malformedCSV = "malformed.csv";
    
public:
    void setupTestEnvironment() {
        std::cout << "\n=== Setting up CSV Test Environment ===" << std::endl;
        
        // Create test directory
        _mkdir(testDir.c_str());
        
        // Create simple CSV file
        std::string simpleCSVPath = testDir + "/" + simpleCSV;
        std::ofstream simpleFile(simpleCSVPath);
        simpleFile << "Name,Age,City\n";
        simpleFile << "Alice,25,New York\n";
        simpleFile << "Bob,30,Los Angeles\n";
        simpleFile << "Charlie,35,Chicago\n";
        simpleFile.close();
        
        // Create complex CSV file with various data types
        std::string complexCSVPath = testDir + "/" + complexCSV;
        std::ofstream complexFile(complexCSVPath);
        complexFile << "ID,Name,Age,Salary,IsActive,Department\n";
        complexFile << "1,Alice Johnson,28,75000.50,true,Engineering\n";
        complexFile << "2,Bob Smith,32,68000.00,false,Marketing\n";
        complexFile << "3,Charlie Brown,29,82000.75,true,Sales\n";
        complexFile << "4,Diana Prince,35,95000.00,true,Management\n";
        complexFile.close();
        
        // Create empty CSV file
        std::string emptyCSVPath = testDir + "/" + emptyCSV;
        std::ofstream emptyFile(emptyCSVPath);
        emptyFile.close();
        
        // Create malformed CSV file
        std::string malformedCSVPath = testDir + "/" + malformedCSV;
        std::ofstream malformedFile(malformedCSVPath);
        malformedFile << "Name,Age,City\n";
        malformedFile << "Alice,25\n";  // Missing field
        malformedFile << "Bob,30,Los Angeles,Extra Field\n";  // Extra field
        malformedFile << "Charlie,,Chicago\n";  // Empty field
        malformedFile.close();
        
        std::cout << "CSV test environment setup completed!" << std::endl;
    }
    
    void cleanupTestEnvironment() {
        std::cout << "\n=== Cleaning up CSV Test Environment ===" << std::endl;
        
        try {
            // Remove test files
            std::remove((testDir + "/" + simpleCSV).c_str());
            std::remove((testDir + "/" + complexCSV).c_str());
            std::remove((testDir + "/" + emptyCSV).c_str());
            std::remove((testDir + "/" + malformedCSV).c_str());
            // Remove test directory
            _rmdir(testDir.c_str());
            std::cout << "CSV test directory cleaned up!" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Cleanup error: " << e.what() << std::endl;
        }
    }
    
    void testSimpleCSVParsing() {
        std::cout << "\n=== Testing Simple CSV Parsing ===" << std::endl;
        
        boost::shared_ptr<CSVParser> parser = CSVParser::create();
        std::string csvPath = testDir + "/" + simpleCSV;
        
        std::vector<std::vector<std::string>> result = parser->parseCSV(csvPath);
        
        std::cout << "Parsed " << result.size() << " rows" << std::endl;
        
        // Verify header row
        assert(result.size() == 4);  // Header + 3 data rows
        assert(result[0].size() == 3);  // 3 columns
        assert(result[0][0] == "Name");
        assert(result[0][1] == "Age");
        assert(result[0][2] == "City");
        
        // Verify first data row
        assert(result[1][0] == "Alice");
        assert(result[1][1] == "25");
        assert(result[1][2] == "New York");
        
        // Print parsed data
        for (size_t i = 0; i < result.size(); ++i) {
            for (size_t j = 0; j < result[i].size(); ++j) {
                std::cout << result[i][j];
                if (j < result[i].size() - 1) std::cout << " | ";
            }
            std::cout << std::endl;
        }
        
        std::cout << "Simple CSV parsing test passed!" << std::endl;
    }
    
    void testComplexCSVParsing() {
        std::cout << "\n=== Testing Complex CSV Parsing ===" << std::endl;
        
        boost::shared_ptr<CSVParser> parser = CSVParser::create();
        std::string csvPath = testDir + "/" + complexCSV;
        
        std::vector<std::vector<std::string>> result = parser->parseCSV(csvPath);
        
        std::cout << "Parsed " << result.size() << " rows" << std::endl;
        
        // Verify structure
        assert(result.size() == 5);  // Header + 4 data rows
        assert(result[0].size() == 6);  // 6 columns
        
        // Verify header
        assert(result[0][0] == "ID");
        assert(result[0][1] == "Name");
        assert(result[0][2] == "Age");
        assert(result[0][3] == "Salary");
        assert(result[0][4] == "IsActive");
        assert(result[0][5] == "Department");
        
        // Test data type parsing using StringTransform
        std::cout << "Testing data type conversions:" << std::endl;
        
        // Parse ID as integer
        int id = StringTransform::parseInt(result[1][0]);
        assert(id == 1);
        std::cout << "ID: " << id << std::endl;
        
        // Parse Age as integer
        int age = StringTransform::parseInt(result[1][2]);
        assert(age == 28);
        std::cout << "Age: " << age << std::endl;
        
        // Parse Salary as double
        double salary = StringTransform::parseDouble(result[1][3]);
        assert(salary == 75000.50);
        std::cout << "Salary: " << salary << std::endl;
        
        // Parse IsActive as boolean
        bool isActive = StringTransform::parseBoolean(result[1][4]);
        assert(isActive == true);
        std::cout << "IsActive: " << (isActive ? "true" : "false") << std::endl;
        
        std::cout << "Complex CSV parsing test passed!" << std::endl;
    }
    
    void testEmptyCSVParsing() {
        std::cout << "\n=== Testing Empty CSV Parsing ===" << std::endl;
        
        boost::shared_ptr<CSVParser> parser = CSVParser::create();
        std::string csvPath = testDir + "/" + emptyCSV;
        
        std::vector<std::vector<std::string>> result = parser->parseCSV(csvPath);
        
        std::cout << "Empty CSV result size: " << result.size() << std::endl;
        
        // Empty file should return empty result
        assert(result.empty());
        
        std::cout << "Empty CSV parsing test passed!" << std::endl;
    }
    
    void testMalformedCSVParsing() {
        std::cout << "\n=== Testing Malformed CSV Parsing ===" << std::endl;
        
        boost::shared_ptr<CSVParser> parser = CSVParser::create();
        std::string csvPath = testDir + "/" + malformedCSV;
        
        std::vector<std::vector<std::string>> result = parser->parseCSV(csvPath);
        
        std::cout << "Malformed CSV result size: " << result.size() << std::endl;
        
        // Should still parse, but with inconsistent column counts
        assert(result.size() == 4);  // Header + 3 data rows
        
        // Print malformed data to show how parser handles it
        for (size_t i = 0; i < result.size(); ++i) {
            std::cout << "Row " << i << " (" << result[i].size() << " columns): ";
            for (size_t j = 0; j < result[i].size(); ++j) {
                std::cout << "'" << result[i][j] << "'";
                if (j < result[i].size() - 1) std::cout << " | ";
            }
            std::cout << std::endl;
        }
        
        std::cout << "Malformed CSV parsing test completed!" << std::endl;
    }
    
    void testNonExistentFile() {
        std::cout << "\n=== Testing Non-existent File ===" << std::endl;
        
        boost::shared_ptr<CSVParser> parser = CSVParser::create();
        std::string nonExistentPath = "non_existent_file.csv";
        
        std::vector<std::vector<std::string>> result = parser->parseCSV(nonExistentPath);
        
        std::cout << "Non-existent file result size: " << result.size() << std::endl;
        
        // Should return empty result for non-existent file
        assert(result.empty());
        
        std::cout << "Non-existent file test passed!" << std::endl;
    }
    
    void testFileIOOperations() {
        std::cout << "\n=== Testing FileIOOperation ===" << std::endl;
        
        // Test reading lines
        std::string csvPath = testDir + "/" + simpleCSV;
        std::vector<std::string> lines = FileIOOperation::readLines(csvPath);
        
        std::cout << "Read " << lines.size() << " lines from file" << std::endl;
        
        // Should read all lines including header
        assert(lines.size() >= 4);
        
        // Print first few lines
        size_t maxLines = lines.size() < 3 ? lines.size() : 3;
        for (size_t i = 0; i < maxLines; ++i) {
            std::cout << "Line " << i << ": " << lines[i] << std::endl;
        }
        
        // Test writing lines
        std::string outputPath = testDir + "/output_test.csv";
        std::vector<std::string> testLines = {
            "Product,Price,Stock",
            "Laptop,999.99,50",
            "Mouse,29.99,100",
            "Keyboard,79.99,75"
        };
        
        int writeResult = FileIOOperation::writeLines(outputPath, testLines);
        std::cout << "Write operation result: " << writeResult << std::endl;
        
        // Verify written file
        std::vector<std::string> readBackLines = FileIOOperation::readLines(outputPath);
        std::cout << "Read back " << readBackLines.size() << " lines" << std::endl;

        // Filter out empty lines that might be added by the reader
        std::vector<std::string> nonEmptyLines;
        for (const auto& line : readBackLines) {
            if (!line.empty()) {
                nonEmptyLines.push_back(line);
            }
        }

        std::cout << "Non-empty lines: " << nonEmptyLines.size() << std::endl;
        assert(nonEmptyLines.size() == testLines.size());
        
        // Clean up output file
        std::remove(outputPath.c_str());
        
        std::cout << "FileIOOperation test passed!" << std::endl;
    }
    
    void testStringTransformUtilities() {
        std::cout << "\n=== Testing StringTransform Utilities ===" << std::endl;
        
        // Test string splitting
        std::string testString = "apple;banana;cherry;date";
        std::vector<std::string> parts = StringTransform::split(testString, ";");
        
        std::cout << "Split '" << testString << "' into " << parts.size() << " parts:" << std::endl;
        assert(parts.size() == 4);
        assert(parts[0] == "apple");
        assert(parts[1] == "banana");
        assert(parts[2] == "cherry");
        assert(parts[3] == "date");
        
        for (const auto& part : parts) {
            std::cout << "  - " << part << std::endl;
        }
        
        // Test number parsing with defaults
        int validInt = StringTransform::parseInt("123", 0);
        int invalidInt = StringTransform::parseInt("invalid", 999);
        
        assert(validInt == 123);
        assert(invalidInt == 999);
        
        std::cout << "Valid int: " << validInt << ", Invalid int (default): " << invalidInt << std::endl;
        
        // Test float parsing
        float validFloat = StringTransform::parseFloat("123.45", 0.0f);
        float invalidFloat = StringTransform::parseFloat("invalid", 999.0f);
        
        assert(validFloat == 123.45f);
        assert(invalidFloat == 999.0f);
        
        std::cout << "Valid float: " << validFloat << ", Invalid float (default): " << invalidFloat << std::endl;
        
        // Test boolean parsing
        bool trueValue = StringTransform::parseBoolean("true", false);
        bool falseValue = StringTransform::parseBoolean("false", true);
        bool invalidBool = StringTransform::parseBoolean("invalid", true);
        
        assert(trueValue == true);
        assert(falseValue == false);
        assert(invalidBool == true);  // Should use default
        
        std::cout << "Boolean parsing: true=" << trueValue << ", false=" << falseValue 
                  << ", invalid(default)=" << invalidBool << std::endl;
        
        std::cout << "StringTransform utilities test passed!" << std::endl;
    }
};

int main() {
    // Initialize Google logging
    google::InitGoogleLogging("csv_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting CSV Parser Tests..." << std::endl;
    
    try {
        CSVTester tester;
        
        // Setup test environment
        tester.setupTestEnvironment();
        
        // Run all tests
        tester.testSimpleCSVParsing();
        tester.testComplexCSVParsing();
        tester.testEmptyCSVParsing();
        tester.testMalformedCSVParsing();
        tester.testNonExistentFile();
        tester.testFileIOOperations();
        tester.testStringTransformUtilities();
        
        // Cleanup
        tester.cleanupTestEnvironment();
        
        std::cout << "\nAll CSV Parser tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
