#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <atomic>
#include <cassert>
#include <memory>
#include <string>

#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QJsonObject>
#include <QJsonDocument>
#include <QTemporaryDir>
#include <QFileInfo>
#include <QSettings>

#include "ConfigManager.h"
#include "glog.h"

using namespace Fuxi::Core;
using namespace std::chrono_literals;

class ConfigManagerTester {
private:
    std::unique_ptr<ConfigManager> configManager;
    std::unique_ptr<QTemporaryDir> tempDir;
    QString testConfigFile;
    QString testIniFile;

public:
    ConfigManagerTester() {
        configManager = std::make_unique<ConfigManager>();
        tempDir = std::make_unique<QTemporaryDir>();
        
        if (tempDir->isValid()) {
            testConfigFile = tempDir->path() + "/test_config.json";
            testIniFile = tempDir->path() + "/test_config.ini";
            createTestConfigFiles();
        }
    }

    void createTestConfigFiles() {
        // Create JSON test config
        QJsonObject config;
        config["version"] = "1.0";
        config["appName"] = "TestApp";
        config["debug"] = true;
        config["maxConnections"] = 100;
        
        QJsonObject database;
        database["host"] = "localhost";
        database["port"] = 5432;
        database["name"] = "testdb";
        config["database"] = database;
        
        QJsonObject logging;
        logging["level"] = "INFO";
        logging["file"] = "app.log";
        config["logging"] = logging;
        
        QJsonDocument doc(config);
        QFile file(testConfigFile);
        if (file.open(QIODevice::WriteOnly)) {
            file.write(doc.toJson());
            file.close();
        }
        
        // Create INI test config
        QSettings iniSettings(testIniFile, QSettings::IniFormat);
        iniSettings.setValue("version", "1.0");
        iniSettings.setValue("appName", "TestApp");
        iniSettings.setValue("debug", true);
        iniSettings.setValue("maxConnections", 100);
        iniSettings.sync();
    }

    void testBasicConfigLoading() {
        std::cout << "\n=== Testing Basic Config Loading ===" << std::endl;
        
        // Test JSON loading
        bool loaded = configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        assert(loaded);
        std::cout << "✓ JSON config loaded successfully" << std::endl;
        
        // Verify loaded values
        assert(configManager->getValue("version").toString() == "1.0");
        assert(configManager->getValue("appName").toString() == "TestApp");
        assert(configManager->getValue("debug").toBool() == true);
        assert(configManager->getValue("maxConnections").toInt() == 100);
        
        std::cout << "✓ Basic values verified: version=" << configManager->getValue("version").toString().toStdString()
                  << ", appName=" << configManager->getValue("appName").toString().toStdString() << std::endl;
        
        std::cout << "✓ Basic config loading test passed!" << std::endl;
    }

    void testNestedValueAccess() {
        std::cout << "\n=== Testing Nested Value Access ===" << std::endl;
        
        configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        
        // Test nested values
        assert(configManager->getValue("database.host").toString() == "localhost");
        assert(configManager->getValue("database.port").toInt() == 5432);
        assert(configManager->getValue("database.name").toString() == "testdb");
        assert(configManager->getValue("logging.level").toString() == "INFO");
        
        std::cout << "✓ Nested values verified: database.host=" 
                  << configManager->getValue("database.host").toString().toStdString()
                  << ", database.port=" << configManager->getValue("database.port").toInt() << std::endl;
        
        std::cout << "✓ Nested value access test passed!" << std::endl;
    }

    void testValueModification() {
        std::cout << "\n=== Testing Value Modification ===" << std::endl;
        
        configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        
        // Test setValue
        bool setResult = configManager->setValue("appName", "ModifiedApp");
        assert(setResult);
        assert(configManager->getValue("appName").toString() == "ModifiedApp");
        std::cout << "✓ Value modified: appName=" << configManager->getValue("appName").toString().toStdString() << std::endl;
        
        // Test nested setValue
        setResult = configManager->setValue("database.host", "remote.server.com");
        assert(setResult);
        assert(configManager->getValue("database.host").toString() == "remote.server.com");
        std::cout << "✓ Nested value modified: database.host=" 
                  << configManager->getValue("database.host").toString().toStdString() << std::endl;
        
        // Test new value creation
        setResult = configManager->setValue("newValue", "test");
        assert(setResult);
        assert(configManager->getValue("newValue").toString() == "test");
        std::cout << "✓ New value created: newValue=" << configManager->getValue("newValue").toString().toStdString() << std::endl;
        
        // Test nested new value
        setResult = configManager->setValue("new.nested.value", 42);
        assert(setResult);
        assert(configManager->getValue("new.nested.value").toInt() == 42);
        std::cout << "✓ New nested value created: new.nested.value=" << configManager->getValue("new.nested.value").toInt() << std::endl;
        
        std::cout << "✓ Value modification test passed!" << std::endl;
    }

    void testDefaultValues() {
        std::cout << "\n=== Testing Default Values ===" << std::endl;
        
        configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        
        // Test getValue with default for non-existent key
        QString defaultValue = configManager->getValue("nonexistent", "default").toString();
        assert(defaultValue == "default");
        std::cout << "✓ Default value returned for non-existent key: " << defaultValue.toStdString() << std::endl;
        
        // Test getValue with default for existing key (should return existing value)
        int existingValue = configManager->getValue("maxConnections", 50).toInt();
        assert(existingValue == 100);  // Should return existing value, not default
        std::cout << "✓ Existing value returned instead of default: " << existingValue << std::endl;
        
        // Test setDefaultValue and getDefaultValue
        configManager->setDefaultValue("timeout", 30);
        QVariant defaultTimeout = configManager->getDefaultValue("timeout");
        assert(defaultTimeout.toInt() == 30);
        std::cout << "✓ Default value set and retrieved: timeout=" << defaultTimeout.toInt() << std::endl;
        
        std::cout << "✓ Default values test passed!" << std::endl;
    }

    void testConfigSaving() {
        std::cout << "\n=== Testing Config Saving ===" << std::endl;
        
        configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        
        // Modify some values
        configManager->setValue("appName", "SavedApp");
        configManager->setValue("maxConnections", 200);
        configManager->setValue("database.host", "saved.server.com");
        
        // Save config
        QString saveFile = tempDir->path() + "/saved_config.json";
        bool saved = configManager->saveConfig(saveFile, ConfigManager::ConfigFormat::JSON);
        assert(saved);
        assert(QFile::exists(saveFile));
        std::cout << "✓ Config saved to: " << saveFile.toStdString() << std::endl;
        
        // Load saved config and verify
        auto verifyManager = std::make_unique<ConfigManager>();
        bool loaded = verifyManager->loadConfig(saveFile, ConfigManager::ConfigFormat::JSON);
        assert(loaded);
        
        assert(verifyManager->getValue("appName").toString() == "SavedApp");
        assert(verifyManager->getValue("maxConnections").toInt() == 200);
        assert(verifyManager->getValue("database.host").toString() == "saved.server.com");
        
        std::cout << "✓ Saved config verified: appName=" << verifyManager->getValue("appName").toString().toStdString()
                  << ", maxConnections=" << verifyManager->getValue("maxConnections").toInt() << std::endl;
        
        std::cout << "✓ Config saving test passed!" << std::endl;
    }

    void testConfigInfo() {
        std::cout << "\n=== Testing Config Info ===" << std::endl;
        
        configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        
        ConfigManager::ConfigInfo info = configManager->getConfigInfo();
        assert(info.filePath == testConfigFile);
        assert(info.format == ConfigManager::ConfigFormat::JSON);
        assert(info.isValid);
        assert(info.lastLoaded.isValid());
        assert(!info.checksum.isEmpty());
        
        std::cout << "✓ Config info verified: filePath=" << info.filePath.toStdString()
                  << ", format=JSON, isValid=" << (info.isValid ? "true" : "false") << std::endl;
        
        // Test checksum
        QString checksum = configManager->getConfigChecksum();
        assert(!checksum.isEmpty());
        std::cout << "✓ Config checksum: " << checksum.left(8).toStdString() << "..." << std::endl;
        
        // Test modification flag
        assert(!configManager->isConfigModified());  // Should not be modified initially
        configManager->setValue("testModification", true);
        assert(configManager->isConfigModified());   // Should be modified after setValue
        std::cout << "✓ Modification flag works correctly" << std::endl;
        
        std::cout << "✓ Config info test passed!" << std::endl;
    }

    void testBackupAndRestore() {
        std::cout << "\n=== Testing Backup and Restore ===" << std::endl;
        
        configManager->loadConfig(testConfigFile, ConfigManager::ConfigFormat::JSON);
        
        // Create backup
        QString backupPath = tempDir->path() + "/backup_config.json";
        bool backupCreated = configManager->createBackup(backupPath);
        assert(backupCreated);
        assert(QFile::exists(backupPath));
        std::cout << "✓ Backup created: " << backupPath.toStdString() << std::endl;
        
        // Modify original config
        configManager->setValue("modified", true);
        configManager->setValue("backupTest", "modified");
        assert(configManager->hasValue("modified"));
        assert(configManager->getValue("backupTest").toString() == "modified");
        std::cout << "✓ Config modified after backup" << std::endl;
        
        // Restore from backup
        bool restored = configManager->restoreFromBackup(backupPath);
        assert(restored);
        assert(!configManager->hasValue("modified"));
        assert(!configManager->hasValue("backupTest"));
        std::cout << "✓ Config restored from backup" << std::endl;
        
        // Verify original values are back
        assert(configManager->getValue("appName").toString() == "TestApp");
        assert(configManager->getValue("maxConnections").toInt() == 100);
        std::cout << "✓ Original values verified after restore" << std::endl;
        
        std::cout << "✓ Backup and restore test passed!" << std::endl;
    }

    void testINIFormat() {
        std::cout << "\n=== Testing INI Format ===" << std::endl;
        
        // Test INI loading
        bool loaded = configManager->loadConfig(testIniFile, ConfigManager::ConfigFormat::INI);
        assert(loaded);
        std::cout << "✓ INI config loaded successfully" << std::endl;
        
        // Verify INI values
        assert(configManager->getValue("version").toString() == "1.0");
        assert(configManager->getValue("appName").toString() == "TestApp");
        assert(configManager->getValue("debug").toBool() == true);
        assert(configManager->getValue("maxConnections").toInt() == 100);
        
        std::cout << "✓ INI values verified: version=" << configManager->getValue("version").toString().toStdString()
                  << ", debug=" << (configManager->getValue("debug").toBool() ? "true" : "false") << std::endl;
        
        std::cout << "✓ INI format test passed!" << std::endl;
    }
};

int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);
    
    // Initialize Google logging
    google::InitGoogleLogging("config_manager_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting ConfigManager Tests..." << std::endl;
    
    try {
        ConfigManagerTester tester;
        
        tester.testBasicConfigLoading();
        tester.testNestedValueAccess();
        tester.testValueModification();
        tester.testDefaultValues();
        tester.testConfigSaving();
        tester.testConfigInfo();
        tester.testBackupAndRestore();
        tester.testINIFormat();
        
        std::cout << "\n🎉 All ConfigManager tests passed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
