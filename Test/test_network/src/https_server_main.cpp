#include "httplib.h"
#include <iostream>
#include <string>
#include "industrial_https_server.h"

using namespace httplib;

int main() {
    std::cout << "🔒 Starting Industrial Automation HTTPS Server..." << std::endl;
    
    // 创建SSL服务器
    SSLServer server("cert.pem", "key.pem");
    
    if (!server.is_valid()) {
        std::cerr << "❌ Failed to load SSL certificates!" << std::endl;
        std::cerr << "Please ensure cert.pem and key.pem are in the current directory." << std::endl;
        return -1;
    }
    
    std::cout << "✅ SSL certificates loaded successfully" << std::endl;
    
    // 创建服务器实例并设置路由
    IndustrialServer industrialServer;
    industrialServer.setupRoutes(server);
    
    std::cout << "🚀 HTTPS Server starting on port 8443..." << std::endl;
    std::cout << "🌐 Access the server at: https://localhost:8443" << std::endl;
    std::cout << "📋 API Documentation: https://localhost:8443/" << std::endl;
    std::cout << "🏥 Health Check: https://localhost:8443/api/health" << std::endl;
    std::cout << "📊 System Status: https://localhost:8443/api/system/status" << std::endl;
    std::cout << "\n⚠️  Note: You may see a security warning due to self-signed certificate" << std::endl;
    std::cout << "   Click 'Advanced' -> 'Proceed to localhost (unsafe)' to continue" << std::endl;
    std::cout << "\n🛑 Press Ctrl+C to stop the server" << std::endl;
    
    // 启动服务器
    if (!server.listen("0.0.0.0", 8443)) {
        std::cerr << "❌ Failed to start HTTPS server on port 8443" << std::endl;
        return -1;
    }
    
    return 0;
}