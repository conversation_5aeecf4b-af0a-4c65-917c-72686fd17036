#ifndef INDUSTRIAL_HTTPS_SERVER_H
#define INDUSTRIAL_HTTPS_SERVER_H

#include "httplib.h"
#include <string>
#include <map>
#include <vector>
#include <chrono>
#include <thread>
#include <mutex>
#include <random>
#include <iomanip>
#include <sstream>
#include <memory>
#include <atomic>

using namespace httplib;

// 全局任务状态管理
struct TaskStatus {
    int taskId;
    std::string action;
    std::string status;
    std::string message;
    std::string updateTime;
    std::map<std::string, std::string> data;
};

class IndustrialServer {
private:
    std::map<int, TaskStatus> taskMap;
    std::mutex taskMutex;
    std::atomic<int> nextTaskId{1000};
    
    // 模拟设备状态
    std::string sampleEntryStatus = "IDLE";
    std::string robotStatus = "IDLE";
    std::string frameStatus = "UNLOCKED";
    std::string repoStatus = "HOMED";
    
    std::string getCurrentTime();
    
    std::string createJsonResponse(int taskId, const std::string& action, 
                                 const std::string& status, const std::string& message,
                                 const std::map<std::string, std::string>& data = {});
    
    std::string createStatusResponse(const std::string& name, const std::string& description,
                                   const std::string& status, const std::string& message,
                                   const std::map<std::string, std::string>& data = {});
    
    // 模拟异步任务处理
    void processAsyncTask(int taskId, const std::string& action);
    
public:
    void setupRoutes(SSLServer& server);
};

#endif // INDUSTRIAL_HTTPS_SERVER_H