# 🏭 Industrial Automation HTTPS Server

一个基于C++和httplib库开发的工业自动化设备控制HTTPS服务器，提供安全的RESTful API接口用于控制各种工业设备。

## 📋 项目概述

本项目实现了一个完整的工业自动化控制系统的HTTP服务器端，支持多种工业设备的远程控制和状态监控。服务器采用HTTPS协议确保通信安全，支持异步任务处理和实时状态查询。

## 🚀 主要特性

- **🔒 HTTPS安全通信**: 使用SSL/TLS加密保护数据传输
- **🎯 RESTful API设计**: 标准化的API接口，易于集成
- **⚡ 异步任务处理**: 支持长时间运行的设备操作
- **📊 实时状态监控**: 设备状态实时查询和更新
- **🌐 CORS支持**: 支持跨域请求，便于Web前端集成
- **🏥 健康检查**: 内置系统健康监控接口
- **📱 响应式Web界面**: 内置API文档和状态展示页面

## 🛠️ 技术栈

- **语言**: C++17
- **HTTP库**: httplib (支持HTTPS)
- **并发处理**: std::thread, std::mutex
- **数据格式**: JSON
- **协议**: HTTPS (TLS 1.2+)

## 📁 项目结构

```
test_network/
├── src/
│   ├── https_server_main.cpp          # 主程序入口
│   ├── industrial_https_server.h       # 服务器类头文件
│   └── industrial_https_server.cpp     # 服务器实现文件
├── cert.pem                            # SSL证书文件
├── key.pem                             # SSL私钥文件
└── README.md                           # 项目文档
```

## 🔧 编译和运行

### 前置要求

1. C++17兼容的编译器
2. httplib库
3. OpenSSL库（用于HTTPS支持）
4. SSL证书文件（cert.pem和key.pem）

### 编译

```bash
# 使用CMake编译
mkdir build && cd build
cmake ..
make
```

### SSL证书生成

如果没有SSL证书，可以生成自签名证书用于测试：

```bash
# 生成私钥
openssl genrsa -out key.pem 2048

# 生成自签名证书
openssl req -new -x509 -key key.pem -out cert.pem -days 365
```

### 运行服务器

```bash
# 确保证书文件在当前目录
./https_server_main
```

服务器启动后将监听8443端口，访问地址：`https://localhost:8443`

## 🌐 API接口文档

### 系统管理接口

#### 健康检查
```http
GET /api/health
```
返回服务器健康状态。

#### 系统状态
```http
GET /api/system/status
```
返回系统整体状态和所有设备状态概览。

### 设备控制接口

#### 1. 进样传送装置

**来样检测**
```http
GET /api/sampleEntry/query?taskId=1000
```

**扫码识别和开合盖操作**
```http
POST /api/sampleEntry/operation
Content-Type: application/json

{"action": "SCAN_TAG"}     # 扫码识别
{"action": "OPEN_CAP"}     # 开盖操作
{"action": "CLOSE_CAP"}    # 关盖操作
```

#### 2. 出样传送装置

**传送带状态检测**
```http
GET /api/sampleExit/query?taskId=1000
```

#### 3. 容器货架装置

**获取设备状态**
```http
GET /api/repo/status
```

**货架操作**
```http
POST /api/repo/operation
Content-Type: application/json

{"action": "POS_REDIRECT"}   # 点位重定向
{"action": "RESET"}         # 复位操作
{"action": "TURN_LEFT"}     # 左转
{"action": "TURN_RIGHT"}    # 右转
{"action": "PLACE_STIRBAR"} # 放置搅拌子
```

#### 4. 工作台机械臂

**获取设备状态**
```http
GET /api/robot/status
```

**提交取放指令**
```http
POST /api/robot/operation
Content-Type: application/json

{"action": "PICK_PLACE"}
```

**查询任务结果**
```http
GET /api/robot/query?taskId=1001
```

#### 5. 倒样称量装置

**称量操作**
```http
POST /api/sampleBalance/operation
Content-Type: application/json

{"action": "RESET"}  # 天平复位清零
{"action": "WEIGH"}  # 倒样称量
```

**查询称量结果**
```http
GET /api/sampleBalance/query?taskId=1000
```

#### 6. 通用称量模块

**提交称量指令**
```http
POST /api/balance/operation
Content-Type: application/json

{"action": "WEIGH"}
```

**查询称量结果**
```http
GET /api/balance/query?taskId=1000
```

#### 7. 加液装置

**加液操作**
```http
POST /api/dosing/operation
Content-Type: application/json

{"action": "DOSING"}
```

**查询加液结果**
```http
GET /api/dosing/query?taskId=1000
```

#### 8. 定容装置

**定容操作**
```http
POST /api/volume/operation
Content-Type: application/json

{"action": "VOLUME"}
```

**查询定容结果**
```http
GET /api/volume/query?taskId=1000
```

#### 9. 过滤装置

**过滤操作**
```http
POST /api/filter/operation
Content-Type: application/json

{"action": "INSTALL_FILTER"}   # 安装过滤头
{"action": "UNINSTALL_FILTER"} # 卸载过滤头
{"action": "FILTER"}           # 执行过滤
```

**查询过滤结果**
```http
GET /api/filter/query?taskId=1000
```

#### 10. 搅拌装置

**搅拌控制**
```http
POST /api/stir/operation
Content-Type: application/json

{"action": "START_STIR"}  # 开启搅拌
{"action": "STOP_STIR"}   # 停止搅拌
```

#### 11. 搅拌加热装置

**加热搅拌控制**
```http
POST /api/heater/operation
Content-Type: application/json

{"action": "START"}  # 开启加热搅拌
{"action": "STOP"}   # 停止加热搅拌
```

#### 12. ICP进样装置

**ICP进样控制**
```http
POST /api/icpEntry/operation
Content-Type: application/json

{"action": "START"}  # 开启ICP进样
{"action": "STOP"}   # 停止ICP进样
```

**ICP进样状态查询**
```http
GET /api/icpEntry/query?taskId=1000
```

#### 13. 框架装置

**查询锁状态**
```http
GET /api/frame/status
```

**门锁和急停控制**
```http
POST /api/frame/operation
Content-Type: application/json

{"action": "UNLOCK"}      # 门框解锁
{"action": "LOCK"}        # 门框锁定
{"action": "URGENT_STOP"} # 急停操作
```

## 📊 响应格式

### 标准任务响应
```json
{
  "taskId": 1001,
  "action": "PICK_PLACE",
  "status": "SUCCESS",
  "message": "任务执行成功",
  "data": {
    "key": "value"
  },
  "updateTime": "2024-01-15 10:30:45"
}
```

### 设备状态响应
```json
{
  "name": "工作台1机械臂",
  "description": "描述设备信息",
  "status": "IDLE",
  "message": "设备空闲可用",
  "data": {
    "x": "100.5",
    "y": "200.3",
    "z": "150.0"
  },
  "updateTime": "2024-01-15 10:30:45"
}
```

### 状态码说明

- **SUCCESS**: 操作成功
- **FAILED**: 操作失败
- **SUBMITTED**: 任务已提交
- **IDLE**: 设备空闲
- **RUNNING**: 设备运行中
- **HOMED**: 设备已复位
- **LOCKED/UNLOCKED**: 门锁状态

## 🔒 安全特性

1. **HTTPS加密**: 所有通信都通过TLS加密
2. **CORS配置**: 支持跨域请求控制
3. **输入验证**: 对所有输入参数进行验证
4. **错误处理**: 完善的错误处理和状态码返回

## 🧪 测试

### 使用curl测试

```bash
# 健康检查
curl -k https://localhost:8443/api/health

# 系统状态
curl -k https://localhost:8443/api/system/status

# 机械臂状态
curl -k https://localhost:8443/api/robot/status

# 提交机械臂任务
curl -k -X POST https://localhost:8443/api/robot/operation \
     -H "Content-Type: application/json" \
     -d '{"action": "PICK_PLACE"}'
```

### 使用Postman测试

1. 导入服务器地址：`https://localhost:8443`
2. 设置忽略SSL证书验证（自签名证书）
3. 设置请求头：`Content-Type: application/json`
4. 测试各个API接口

## 🚨 注意事项

1. **SSL证书**: 生产环境请使用正式的SSL证书
2. **端口配置**: 确保8443端口未被占用
3. **防火墙**: 确保防火墙允许8443端口通信
4. **内存管理**: 长时间运行时注意监控内存使用
5. **日志记录**: 建议添加详细的日志记录功能

## 🔧 配置选项

可以通过修改源代码来调整以下配置：

- **端口号**: 在`https_server_main.cpp`中修改端口
- **SSL证书路径**: 修改证书文件路径
- **CORS设置**: 调整跨域访问策略
- **超时设置**: 调整任务超时时间

## 📈 性能优化

1. **连接池**: 考虑实现连接池以提高并发性能
2. **缓存机制**: 对频繁查询的状态信息实现缓存
3. **异步处理**: 优化异步任务处理机制
4. **内存优化**: 优化JSON生成和内存使用

## 🤝 贡献指南

1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

## 🙏 致谢

- [httplib](https://github.com/yhirose/cpp-httplib) - 优秀的C++ HTTP库
- OpenSSL - 提供SSL/TLS支持
- 所有贡献者和测试人员

---

**⚡ 快速开始**: 访问 `https://localhost:8443` 查看完整的API文档和在线测试界面！