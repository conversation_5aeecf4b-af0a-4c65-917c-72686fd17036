#include <iostream>
#include <cassert>
#include <string>
#include <fstream>
#include <cstdio>
#include <direct.h>

#include "FileUtil.h"
#include "glog.h"

using namespace Fuxi::Common;

class FileUtilTester {
private:
    std::string testDir = "test_fileutil_data";
    std::string testFile = "test_file.txt";
    std::string testFilePath;

public:
    FileUtilTester() {
        testFilePath = testDir + "/" + testFile;
    }

    void setupTestEnvironment() {
        std::cout << "\n=== Setting up Test Environment ===" << std::endl;

        // Create test directory using Windows API
        _mkdir(testDir.c_str());

        // Create a test file
        std::ofstream file(testFilePath);
        file << "This is a test file for FileUtil testing.\n";
        file << "It contains multiple lines of text.\n";
        file << "Line 3 with some data.\n";
        file.close();

        std::cout << "Test environment setup completed!" << std::endl;
    }

    void cleanupTestEnvironment() {
        std::cout << "\n=== Cleaning up Test Environment ===" << std::endl;

        try {
            // Remove test file
            std::remove(testFilePath.c_str());
            // Remove test directory
            _rmdir(testDir.c_str());
            std::cout << "Test directory cleaned up!" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Cleanup error: " << e.what() << std::endl;
        }
    }
    
    void testFileDownload() {
        std::cout << "\n=== Testing File Download ===" << std::endl;
        
        // Test downloading a small file from a public URL
        std::string testUrl = "https://httpbin.org/robots.txt";
        std::string downloadPath = testDir + "/downloaded_robots.txt";
        
        std::cout << "Attempting to download from: " << testUrl << std::endl;
        std::cout << "Download path: " << downloadPath << std::endl;
        
        bool success = FileUtil::downloadFile(testUrl.c_str(), downloadPath.c_str());
        
        if (success) {
            std::cout << "Download successful!" << std::endl;
            
            // Verify file exists and has content
            std::ifstream checkFile(downloadPath);
            if (checkFile.good()) {
                std::string content((std::istreambuf_iterator<char>(checkFile)),
                                   std::istreambuf_iterator<char>());
                checkFile.close();

                std::cout << "Downloaded file size: " << content.length() << " bytes" << std::endl;
                if (!content.empty()) {
                    std::cout << "File download test passed!" << std::endl;
                } else {
                    std::cout << "Warning: Downloaded file is empty" << std::endl;
                }
            } else {
                std::cout << "Warning: Downloaded file not found" << std::endl;
            }
        } else {
            std::cout << "Download failed - this might be due to network issues or URL accessibility" << std::endl;
            std::cout << "File download test completed (network dependent)" << std::endl;
        }
    }
    
    void testFileDownloadInvalidUrl() {
        std::cout << "\n=== Testing File Download with Invalid URL ===" << std::endl;
        
        std::string invalidUrl = "https://invalid-url-that-does-not-exist.com/file.txt";
        std::string downloadPath = testDir + "/invalid_download.txt";
        
        std::cout << "Testing download from invalid URL: " << invalidUrl << std::endl;
        
        bool success = FileUtil::downloadFile(invalidUrl.c_str(), downloadPath.c_str());
        
        if (!success) {
            std::cout << "Download correctly failed for invalid URL!" << std::endl;
            std::cout << "Invalid URL handling test passed!" << std::endl;
        } else {
            std::cout << "Warning: Download unexpectedly succeeded for invalid URL" << std::endl;
        }
    }
    
    void testFileUpload() {
        std::cout << "\n=== Testing File Upload ===" << std::endl;
        
        // Note: File upload requires a server endpoint
        // We'll test the function call but expect it to fail gracefully
        std::string uploadUrl = "https://httpbin.org/post";
        std::string license = "test-license-key";
        
        std::cout << "Testing upload to: " << uploadUrl << std::endl;
        std::cout << "Upload file: " << testFilePath << std::endl;
        
        bool success = FileUtil::uploadFile(uploadUrl, testFilePath, license);
        
        if (success) {
            std::cout << "Upload successful!" << std::endl;
            std::cout << "File upload test passed!" << std::endl;
        } else {
            std::cout << "Upload failed - this is expected for test endpoint" << std::endl;
            std::cout << "File upload test completed (endpoint dependent)" << std::endl;
        }
    }
    
    void testFileUploadInvalidFile() {
        std::cout << "\n=== Testing File Upload with Invalid File ===" << std::endl;
        
        std::string uploadUrl = "https://httpbin.org/post";
        std::string invalidFile = "non_existent_file.txt";
        
        std::cout << "Testing upload of non-existent file: " << invalidFile << std::endl;
        
        bool success = FileUtil::uploadFile(uploadUrl, invalidFile);
        
        if (!success) {
            std::cout << "Upload correctly failed for non-existent file!" << std::endl;
            std::cout << "Invalid file handling test passed!" << std::endl;
        } else {
            std::cout << "Warning: Upload unexpectedly succeeded for non-existent file" << std::endl;
        }
    }
    
    void testFileUploadInvalidUrl() {
        std::cout << "\n=== Testing File Upload with Invalid URL ===" << std::endl;
        
        std::string invalidUrl = "invalid-url-format";
        
        std::cout << "Testing upload to invalid URL: " << invalidUrl << std::endl;
        
        bool success = FileUtil::uploadFile(invalidUrl, testFilePath);
        
        if (!success) {
            std::cout << "Upload correctly failed for invalid URL!" << std::endl;
            std::cout << "Invalid URL upload test passed!" << std::endl;
        } else {
            std::cout << "Warning: Upload unexpectedly succeeded for invalid URL" << std::endl;
        }
    }
    
    void testEdgeCases() {
        std::cout << "\n=== Testing Edge Cases ===" << std::endl;
        
        // Test with empty strings
        std::cout << "Testing with empty URL..." << std::endl;
        bool emptyUrlResult = FileUtil::downloadFile("", "test_empty.txt");
        std::cout << "Empty URL result: " << (emptyUrlResult ? "Success" : "Failed") << std::endl;
        
        // Test with empty file path
        std::cout << "Testing with empty file path..." << std::endl;
        bool emptyPathResult = FileUtil::downloadFile("https://httpbin.org/robots.txt", "");
        std::cout << "Empty path result: " << (emptyPathResult ? "Success" : "Failed") << std::endl;
        
        // Test with null pointers (if the function handles them)
        std::cout << "Testing with null URL..." << std::endl;
        bool nullUrlResult = FileUtil::downloadFile(nullptr, "test_null.txt");
        std::cout << "Null URL result: " << (nullUrlResult ? "Success" : "Failed") << std::endl;
        
        std::cout << "Edge cases testing completed!" << std::endl;
    }
};

int main() {
    // Initialize Google logging
    google::InitGoogleLogging("fileutil_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting FileUtil Tests..." << std::endl;
    std::cout << "Note: Some tests depend on network connectivity and may fail in offline environments." << std::endl;
    
    try {
        FileUtilTester tester;
        
        // Setup test environment
        tester.setupTestEnvironment();
        
        // Run all tests
        tester.testFileDownload();
        tester.testFileDownloadInvalidUrl();
        tester.testFileUpload();
        tester.testFileUploadInvalidFile();
        tester.testFileUploadInvalidUrl();
        tester.testEdgeCases();
        
        // Cleanup
        tester.cleanupTestEnvironment();
        
        std::cout << "\nAll FileUtil tests completed!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
