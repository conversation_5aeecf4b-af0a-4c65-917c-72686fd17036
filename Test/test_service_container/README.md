# Service Container Tests

This directory contains comprehensive unit tests for the dependency injection service container.

## Test Coverage

The tests cover the following functionality:

### ServiceContainer Tests
- Service registration (singleton and transient)
- Service resolution with type safety
- Factory pattern support
- Thread safety
- Error handling and edge cases
- Service lifecycle management

### ServiceFactory Tests
- Factory registration and creation
- Parameterized factory creation
- Factory registry management
- Complex object creation scenarios

### Integration Tests
- Service container builder
- Real-world usage scenarios
- Performance characteristics

## Running Tests

To run the tests:

```bash
cd build
make test_service_container
./Test/test_service_container/test_service_container
```

## Test Structure

- `main.cpp` - Main test runner with comprehensive test cases
- Tests use assertion-based validation following the project's testing patterns
- Each test method focuses on a specific aspect of the DI container functionality