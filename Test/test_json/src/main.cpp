#include <iostream>
#include <cassert>
#include <string>
#include <vector>

#include "JSON.h"
#include "glog.h"
#include "rttr/registration.h"

using namespace Fuxi::Common;

// Test data structures
struct Person {
    std::string name;
    int age;
    std::string email;
    bool isActive;
    
    Person() = default;
    Person(const std::string& n, int a, const std::string& e, bool active)
        : name(n), age(a), email(e), isActive(active) {}
};

struct Company {
    std::string companyName;
    std::vector<Person> employees;
    double revenue;
    
    Company() = default;
    Company(const std::string& name, double rev) : companyName(name), revenue(rev) {}
};

// RTTR registration for reflection
RTTR_REGISTRATION {
    rttr::registration::class_<Person>("Person")
        .constructor<>()
        .constructor<const std::string&, int, const std::string&, bool>()
        .property("name", &Person::name)
        .property("age", &Person::age)
        .property("email", &Person::email)
        .property("isActive", &Person::isActive);
        
    rttr::registration::class_<Company>("Company")
        .constructor<>()
        .constructor<const std::string&, double>()
        .property("companyName", &Company::companyName)
        .property("employees", &Company::employees)
        .property("revenue", &Company::revenue);
}

class JSONTester {
public:
    void testBasicSerialization() {
        std::cout << "\n=== Testing Basic JSON Serialization ===" << std::endl;
        
        Person person("Alice Johnson", 28, "<EMAIL>", true);
        
        std::string jsonStr = JSON::toJSONString(person);
        std::cout << "Serialized JSON: " << jsonStr << std::endl;
        
        // Basic validation - check if key fields are present
        assert(jsonStr.find("Alice Johnson") != std::string::npos);
        assert(jsonStr.find("28") != std::string::npos);
        assert(jsonStr.find("<EMAIL>") != std::string::npos);
        assert(jsonStr.find("true") != std::string::npos);
        
        std::cout << "Basic JSON serialization test passed!" << std::endl;
    }
    
    void testBasicDeserialization() {
        std::cout << "\n=== Testing Basic JSON Deserialization ===" << std::endl;
        
        std::string jsonStr = R"({
            "name": "Bob Smith",
            "age": 35,
            "email": "<EMAIL>",
            "isActive": false
        })";
        
        Person person;
        bool success = JSON::parseJSON(jsonStr, person);
        
        assert(success);
        assert(person.name == "Bob Smith");
        assert(person.age == 35);
        assert(person.email == "<EMAIL>");
        assert(person.isActive == false);
        
        std::cout << "Parsed Person: " << person.name << ", Age: " << person.age 
                  << ", Email: " << person.email << ", Active: " << person.isActive << std::endl;
        std::cout << "Basic JSON deserialization test passed!" << std::endl;
    }
    
    void testRoundTripSerialization() {
        std::cout << "\n=== Testing Round-trip JSON Serialization ===" << std::endl;
        
        Person originalPerson("Charlie Brown", 42, "<EMAIL>", true);
        
        // Serialize to JSON
        std::string jsonStr = JSON::toJSONString(originalPerson);
        std::cout << "Original JSON: " << jsonStr << std::endl;
        
        // Deserialize back to object
        Person deserializedPerson;
        bool success = JSON::parseJSON(jsonStr, deserializedPerson);
        
        assert(success);
        assert(originalPerson.name == deserializedPerson.name);
        assert(originalPerson.age == deserializedPerson.age);
        assert(originalPerson.email == deserializedPerson.email);
        assert(originalPerson.isActive == deserializedPerson.isActive);
        
        std::cout << "Round-trip serialization test passed!" << std::endl;
    }
    
    void testComplexObjectSerialization() {
        std::cout << "\n=== Testing Complex Object JSON Serialization ===" << std::endl;
        
        Company company("Tech Corp", 1500000.50);
        company.employees.push_back(Person("Alice", 28, "<EMAIL>", true));
        company.employees.push_back(Person("Bob", 35, "<EMAIL>", false));
        company.employees.push_back(Person("Charlie", 42, "<EMAIL>", true));
        
        std::string jsonStr = JSON::toJSONString(company);
        std::cout << "Complex JSON: " << jsonStr << std::endl;
        
        // Basic validation
        assert(jsonStr.find("Tech Corp") != std::string::npos);
        assert(jsonStr.find("1500000.5") != std::string::npos);
        assert(jsonStr.find("Alice") != std::string::npos);
        assert(jsonStr.find("Bob") != std::string::npos);
        assert(jsonStr.find("Charlie") != std::string::npos);
        
        std::cout << "Complex object serialization test passed!" << std::endl;
    }
    
    void testComplexObjectDeserialization() {
        std::cout << "\n=== Testing Complex Object JSON Deserialization ===" << std::endl;
        
        std::string complexJson = R"({
            "companyName": "Innovation Labs",
            "revenue": 2500000.75,
            "employees": [
                {
                    "name": "David Wilson",
                    "age": 30,
                    "email": "<EMAIL>",
                    "isActive": true
                },
                {
                    "name": "Emma Davis",
                    "age": 27,
                    "email": "<EMAIL>",
                    "isActive": true
                }
            ]
        })";
        
        Company company;
        bool success = JSON::parseJSON(complexJson, company);
        
        assert(success);
        assert(company.companyName == "Innovation Labs");
        assert(company.revenue == 2500000.75);
        assert(company.employees.size() == 2);
        
        if (company.employees.size() >= 2) {
            assert(company.employees[0].name == "David Wilson");
            assert(company.employees[0].age == 30);
            assert(company.employees[1].name == "Emma Davis");
            assert(company.employees[1].age == 27);
        }
        
        std::cout << "Company: " << company.companyName << ", Revenue: " << company.revenue << std::endl;
        std::cout << "Employees count: " << company.employees.size() << std::endl;
        for (const auto& emp : company.employees) {
            std::cout << "  - " << emp.name << " (" << emp.age << ")" << std::endl;
        }
        
        std::cout << "Complex object deserialization test passed!" << std::endl;
    }
    
    void testErrorHandling() {
        std::cout << "\n=== Testing JSON Error Handling ===" << std::endl;
        
        // Test invalid JSON
        std::string invalidJson = R"({
            "name": "Invalid Person",
            "age": "not_a_number",
            "email": "<EMAIL>"
            // missing closing brace
        )";
        
        Person person;
        bool success = JSON::parseJSON(invalidJson, person);
        
        // Should handle error gracefully
        std::cout << "Invalid JSON parsing result: " << (success ? "Success" : "Failed") << std::endl;
        
        // Test empty JSON
        std::string emptyJson = "{}";
        Person emptyPerson;
        bool emptySuccess = JSON::parseJSON(emptyJson, emptyPerson);
        
        std::cout << "Empty JSON parsing result: " << (emptySuccess ? "Success" : "Failed") << std::endl;
        
        std::cout << "Error handling test completed!" << std::endl;
    }
};

int main() {
    // Initialize Google logging
    google::InitGoogleLogging("json_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting JSON Tests..." << std::endl;
    
    try {
        JSONTester tester;
        
        // Run all tests
        tester.testBasicSerialization();
        tester.testBasicDeserialization();
        tester.testRoundTripSerialization();
        tester.testComplexObjectSerialization();
        tester.testComplexObjectDeserialization();
        tester.testErrorHandling();
        
        std::cout << "\nAll JSON tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
