#include <iostream>
#include <cassert>
#include <string>
#include <fstream>
#include <cstdio>
#include <direct.h>

#include "tinyxml2.h"
#include "glog.h"

// Note: Not using 'using namespace tinyxml2' to avoid conflicts with Windows msxml.h

class XMLTester {
private:
    std::string testDir = "test_xml_data";
    std::string simpleXML = "simple.xml";
    std::string complexXML = "complex.xml";
    std::string malformedXML = "malformed.xml";
    
public:
    void setupTestEnvironment() {
        std::cout << "\n=== Setting up XML Test Environment ===" << std::endl;
        
        // Create test directory
        _mkdir(testDir.c_str());
        
        // Create simple XML file
        std::string simpleXMLPath = testDir + "/" + simpleXML;
        std::ofstream simpleFile(simpleXMLPath);
        simpleFile << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        simpleFile << "<person>\n";
        simpleFile << "  <name><PERSON></name>\n";
        simpleFile << "  <age>30</age>\n";
        simpleFile << "  <email><EMAIL></email>\n";
        simpleFile << "</person>\n";
        simpleFile.close();
        
        // Create complex XML file
        std::string complexXMLPath = testDir + "/" + complexXML;
        std::ofstream complexFile(complexXMLPath);
        complexFile << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        complexFile << "<company>\n";
        complexFile << "  <name>Tech Solutions Inc</name>\n";
        complexFile << "  <address>123 Tech Street, Silicon Valley</address>\n";
        complexFile << "  <employees>\n";
        complexFile << "    <employee>\n";
        complexFile << "      <name>Alice Johnson</name>\n";
        complexFile << "      <age>28</age>\n";
        complexFile << "      <email><EMAIL></email>\n";
        complexFile << "    </employee>\n";
        complexFile << "    <employee>\n";
        complexFile << "      <name>Bob Smith</name>\n";
        complexFile << "      <age>35</age>\n";
        complexFile << "      <email><EMAIL></email>\n";
        complexFile << "    </employee>\n";
        complexFile << "  </employees>\n";
        complexFile << "</company>\n";
        complexFile.close();
        
        // Create malformed XML file
        std::string malformedXMLPath = testDir + "/" + malformedXML;
        std::ofstream malformedFile(malformedXMLPath);
        malformedFile << "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
        malformedFile << "<person>\n";
        malformedFile << "  <name>Invalid Person\n";  // Missing closing tag
        malformedFile << "  <age>25</age>\n";
        malformedFile << "</person>\n";
        malformedFile.close();
        
        std::cout << "XML test environment setup completed!" << std::endl;
    }
    
    void cleanupTestEnvironment() {
        std::cout << "\n=== Cleaning up XML Test Environment ===" << std::endl;
        
        try {
            // Remove test files
            std::remove((testDir + "/" + simpleXML).c_str());
            std::remove((testDir + "/" + complexXML).c_str());
            std::remove((testDir + "/" + malformedXML).c_str());
            // Remove test directory
            _rmdir(testDir.c_str());
            std::cout << "XML test directory cleaned up!" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Cleanup error: " << e.what() << std::endl;
        }
    }
    
    void testTinyXML2BasicParsing() {
        std::cout << "\n=== Testing TinyXML2 Basic Parsing ===" << std::endl;

        tinyxml2::XMLDocument doc;
        std::string xmlPath = testDir + "/" + simpleXML;

        tinyxml2::XMLError result = doc.LoadFile(xmlPath.c_str());

        if (result == tinyxml2::XML_SUCCESS) {
            std::cout << "XML file loaded successfully!" << std::endl;

            tinyxml2::XMLElement* root = doc.RootElement();
            if (root) {
                std::cout << "Root element: " << root->Name() << std::endl;

                // Extract person data
                tinyxml2::XMLElement* nameElem = root->FirstChildElement("name");
                tinyxml2::XMLElement* ageElem = root->FirstChildElement("age");
                tinyxml2::XMLElement* emailElem = root->FirstChildElement("email");
                
                if (nameElem && ageElem && emailElem) {
                    std::string name = nameElem->GetText() ? nameElem->GetText() : "";
                    int age = ageElem->IntText();
                    std::string email = emailElem->GetText() ? emailElem->GetText() : "";
                    
                    std::cout << "Parsed Person:" << std::endl;
                    std::cout << "  Name: " << name << std::endl;
                    std::cout << "  Age: " << age << std::endl;
                    std::cout << "  Email: " << email << std::endl;
                    
                    assert(name == "John Doe");
                    assert(age == 30);
                    assert(email == "<EMAIL>");
                    
                    std::cout << "TinyXML2 basic parsing test passed!" << std::endl;
                } else {
                    std::cout << "Warning: Could not find all expected elements" << std::endl;
                }
            } else {
                std::cout << "Warning: No root element found" << std::endl;
            }
        } else {
            std::cout << "Failed to load XML file. Error: " << result << std::endl;
        }
    }
    
    void testTinyXML2ComplexParsing() {
        std::cout << "\n=== Testing TinyXML2 Complex Parsing ===" << std::endl;

        tinyxml2::XMLDocument doc;
        std::string xmlPath = testDir + "/" + complexXML;

        tinyxml2::XMLError result = doc.LoadFile(xmlPath.c_str());

        if (result == tinyxml2::XML_SUCCESS) {
            std::cout << "Complex XML file loaded successfully!" << std::endl;

            tinyxml2::XMLElement* root = doc.RootElement();
            if (root) {
                // Extract company data
                tinyxml2::XMLElement* nameElem = root->FirstChildElement("name");
                tinyxml2::XMLElement* addressElem = root->FirstChildElement("address");
                tinyxml2::XMLElement* employeesElem = root->FirstChildElement("employees");
                
                if (nameElem && addressElem && employeesElem) {
                    std::string companyName = nameElem->GetText() ? nameElem->GetText() : "";
                    std::string address = addressElem->GetText() ? addressElem->GetText() : "";
                    
                    std::cout << "Company: " << companyName << std::endl;
                    std::cout << "Address: " << address << std::endl;
                    std::cout << "Employees:" << std::endl;
                    
                    int employeeCount = 0;
                    for (tinyxml2::XMLElement* empElem = employeesElem->FirstChildElement("employee");
                         empElem != nullptr;
                         empElem = empElem->NextSiblingElement("employee")) {

                        tinyxml2::XMLElement* empName = empElem->FirstChildElement("name");
                        tinyxml2::XMLElement* empAge = empElem->FirstChildElement("age");
                        tinyxml2::XMLElement* empEmail = empElem->FirstChildElement("email");
                        
                        if (empName && empAge && empEmail) {
                            std::cout << "  Employee " << (employeeCount + 1) << ":" << std::endl;
                            std::cout << "    Name: " << (empName->GetText() ? empName->GetText() : "") << std::endl;
                            std::cout << "    Age: " << empAge->IntText() << std::endl;
                            std::cout << "    Email: " << (empEmail->GetText() ? empEmail->GetText() : "") << std::endl;
                            employeeCount++;
                        }
                    }
                    
                    assert(employeeCount == 2);
                    std::cout << "TinyXML2 complex parsing test passed!" << std::endl;
                } else {
                    std::cout << "Warning: Could not find all expected company elements" << std::endl;
                }
            }
        } else {
            std::cout << "Failed to load complex XML file. Error: " << result << std::endl;
        }
    }
    
    void testTinyXML2Creation() {
        std::cout << "\n=== Testing TinyXML2 XML Creation ===" << std::endl;

        tinyxml2::XMLDocument doc;

        // Create XML declaration
        tinyxml2::XMLDeclaration* decl = doc.NewDeclaration("xml version=\"1.0\" encoding=\"UTF-8\"");
        doc.InsertFirstChild(decl);

        // Create root element
        tinyxml2::XMLElement* root = doc.NewElement("testPerson");
        doc.InsertEndChild(root);

        // Add child elements
        tinyxml2::XMLElement* nameElem = doc.NewElement("name");
        nameElem->SetText("Test User");
        root->InsertEndChild(nameElem);

        tinyxml2::XMLElement* ageElem = doc.NewElement("age");
        ageElem->SetText(25);
        root->InsertEndChild(ageElem);

        tinyxml2::XMLElement* emailElem = doc.NewElement("email");
        emailElem->SetText("<EMAIL>");
        root->InsertEndChild(emailElem);

        // Save to file
        std::string outputPath = testDir + "/created_test.xml";
        tinyxml2::XMLError saveResult = doc.SaveFile(outputPath.c_str());
        
        if (saveResult == tinyxml2::XML_SUCCESS) {
            std::cout << "XML file created successfully!" << std::endl;

            // Verify by reading back
            tinyxml2::XMLDocument verifyDoc;
            tinyxml2::XMLError loadResult = verifyDoc.LoadFile(outputPath.c_str());

            if (loadResult == tinyxml2::XML_SUCCESS) {
                tinyxml2::XMLElement* verifyRoot = verifyDoc.RootElement();
                if (verifyRoot) {
                    tinyxml2::XMLElement* verifyName = verifyRoot->FirstChildElement("name");
                    if (verifyName && verifyName->GetText()) {
                        std::cout << "Verified created XML - Name: " << verifyName->GetText() << std::endl;
                        assert(std::string(verifyName->GetText()) == "Test User");
                    }
                }
                std::cout << "TinyXML2 creation test passed!" << std::endl;
            } else {
                std::cout << "Failed to verify created XML file" << std::endl;
            }
            
            // Clean up created file
            std::remove(outputPath.c_str());
        } else {
            std::cout << "Failed to save XML file. Error: " << saveResult << std::endl;
        }
    }
    
    void testTinyXML2ErrorHandling() {
        std::cout << "\n=== Testing TinyXML2 Error Handling ===" << std::endl;

        tinyxml2::XMLDocument doc;
        std::string malformedPath = testDir + "/" + malformedXML;

        tinyxml2::XMLError result = doc.LoadFile(malformedPath.c_str());

        if (result != tinyxml2::XML_SUCCESS) {
            std::cout << "Correctly detected malformed XML. Error code: " << result << std::endl;
            std::cout << "Error description: " << doc.ErrorStr() << std::endl;
            std::cout << "Error line: " << doc.ErrorLineNum() << std::endl;
            std::cout << "TinyXML2 error handling test passed!" << std::endl;
        } else {
            std::cout << "Warning: Malformed XML was parsed successfully (unexpected)" << std::endl;
        }

        // Test non-existent file
        tinyxml2::XMLDocument doc2;
        tinyxml2::XMLError result2 = doc2.LoadFile("non_existent_file.xml");

        if (result2 != tinyxml2::XML_SUCCESS) {
            std::cout << "Correctly handled non-existent file. Error code: " << result2 << std::endl;
            std::cout << "Non-existent file handling test passed!" << std::endl;
        } else {
            std::cout << "Warning: Non-existent file was handled unexpectedly" << std::endl;
        }
    }
    
    void testXMLStringParsing() {
        std::cout << "\n=== Testing XML String Parsing ===" << std::endl;

        std::string xmlString = R"(<?xml version="1.0" encoding="UTF-8"?>
<book>
    <title>XML Processing Guide</title>
    <author>John Smith</author>
    <year>2023</year>
    <price>29.99</price>
</book>)";

        tinyxml2::XMLDocument doc;
        tinyxml2::XMLError result = doc.Parse(xmlString.c_str());

        if (result == tinyxml2::XML_SUCCESS) {
            std::cout << "XML string parsed successfully!" << std::endl;

            tinyxml2::XMLElement* root = doc.RootElement();
            if (root) {
                tinyxml2::XMLElement* titleElem = root->FirstChildElement("title");
                tinyxml2::XMLElement* authorElem = root->FirstChildElement("author");
                tinyxml2::XMLElement* yearElem = root->FirstChildElement("year");
                tinyxml2::XMLElement* priceElem = root->FirstChildElement("price");
                
                if (titleElem && authorElem && yearElem && priceElem) {
                    std::cout << "Book Information:" << std::endl;
                    std::cout << "  Title: " << (titleElem->GetText() ? titleElem->GetText() : "") << std::endl;
                    std::cout << "  Author: " << (authorElem->GetText() ? authorElem->GetText() : "") << std::endl;
                    std::cout << "  Year: " << yearElem->IntText() << std::endl;
                    std::cout << "  Price: " << priceElem->FloatText() << std::endl;
                    
                    assert(yearElem->IntText() == 2023);
                    assert(priceElem->FloatText() == 29.99f);
                    
                    std::cout << "XML string parsing test passed!" << std::endl;
                }
            }
        } else {
            std::cout << "Failed to parse XML string. Error: " << result << std::endl;
        }
    }
};

int main() {
    // Initialize Google logging
    google::InitGoogleLogging("xml_test");
    google::SetLogDestination(google::GLOG_INFO, "");
    google::SetStderrLogging(google::GLOG_INFO);
    
    std::cout << "Starting XML Processing Tests..." << std::endl;
    std::cout << "Note: Testing TinyXML2 functionality (Xerces-C++ requires more complex setup)" << std::endl;
    
    try {
        XMLTester tester;
        
        // Setup test environment
        tester.setupTestEnvironment();
        
        // Run TinyXML2 tests
        tester.testTinyXML2BasicParsing();
        tester.testTinyXML2ComplexParsing();
        tester.testTinyXML2Creation();
        tester.testTinyXML2ErrorHandling();
        tester.testXMLStringParsing();
        
        // Cleanup
        tester.cleanupTestEnvironment();
        
        std::cout << "\nAll XML processing tests completed successfully!" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test failed with exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Test failed with unknown exception" << std::endl;
        return 1;
    }
    
    return 0;
}
