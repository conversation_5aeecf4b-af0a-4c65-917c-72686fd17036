#!/usr/bin/env python3
"""
优化版Qt GUI - 解决卡顿问题
"""

import sys
import os
from pathlib import Path

# 添加父目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from PyQt5.QtWidgets import (
        QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, 
        QPushButton, QTextEdit, QLineEdit, QGridLayout, QGroupBox,
        QHBoxLayout, QProgressBar, QMessageBox, QSplashScreen
    )
    from PyQt5.QtCore import QTimer, QThread, pyqtSignal, Qt, QPropertyAnimation, QEasingCurve
    from PyQt5.QtGui import QFont, QPixmap, QPalette, QColor
    import requests
except ImportError as e:
    print(f"导入错误: {e}")
    print("请安装PyQt5: pip install PyQt5")
    sys.exit(1)

class ApiWorker(QThread):
    """API请求工作线程 - 避免UI卡顿"""
    
    # 信号定义
    status_updated = pyqtSignal(bool)
    email_generated = pyqtSignal(dict)
    code_queried = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    
    def __init__(self, api_base):
        super().__init__()
        self.api_base = api_base
        self.task_queue = []
        self.running = True
        
    def add_task(self, task_type, **kwargs):
        """添加任务到队列"""
        self.task_queue.append((task_type, kwargs))
        
    def run(self):
        """工作线程主循环"""
        while self.running:
            if self.task_queue:
                task_type, kwargs = self.task_queue.pop(0)
                
                try:
                    if task_type == "check_status":
                        self.check_api_status()
                    elif task_type == "generate_email":
                        self.generate_email(**kwargs)
                    elif task_type == "query_code":
                        self.query_code(**kwargs)
                except Exception as e:
                    self.error_occurred.emit(str(e))
                    
            self.msleep(100)  # 避免CPU占用过高
            
    def check_api_status(self):
        """检查API状态"""
        try:
            response = requests.get(f"{self.api_base}/health", timeout=2)
            self.status_updated.emit(response.status_code == 200)
        except:
            self.status_updated.emit(False)
            
    def generate_email(self, app_name, prefix=None):
        """生成邮箱"""
        try:
            data = {"app_name": app_name}
            if prefix:
                data["prefix"] = prefix
                
            response = requests.post(f"{self.api_base}/api/generate-email", json=data, timeout=10)
            result = response.json()
            self.email_generated.emit(result)
        except Exception as e:
            self.error_occurred.emit(f"生成邮箱失败: {str(e)}")
            
    def query_code(self, email_id):
        """查询验证码"""
        try:
            response = requests.get(f"{self.api_base}/api/get-code/{email_id}", timeout=10)
            result = response.json()
            self.code_queried.emit(result)
        except Exception as e:
            self.error_occurred.emit(f"查询验证码失败: {str(e)}")
            
    def stop(self):
        """停止工作线程"""
        self.running = False
        self.wait()

class OptimizedEmailUI(QMainWindow):
    """优化版邮箱验证码UI"""
    
    def __init__(self):
        super().__init__()
        self.api_base = "http://localhost:8000"
        self.init_worker()
        self.init_ui()
        self.setup_timers()
        self.apply_styles()
        
    def init_worker(self):
        """初始化工作线程"""
        self.worker = ApiWorker(self.api_base)
        
        # 连接信号
        self.worker.status_updated.connect(self.update_status)
        self.worker.email_generated.connect(self.on_email_generated)
        self.worker.code_queried.connect(self.on_code_queried)
        self.worker.error_occurred.connect(self.on_error)
        
        # 启动工作线程
        self.worker.start()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("邮箱验证码管理系统 - 616866.xyz")
        self.setGeometry(300, 300, 700, 600)
        self.setMinimumSize(600, 500)
        
        # 中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题和状态
        self.create_header(layout)
        
        # 主要功能区域
        self.create_main_content(layout)
        
        central_widget.setLayout(layout)
        
    def create_header(self, layout):
        """创建头部区域"""
        # 标题
        title = QLabel("📧 邮箱验证码管理系统")
        title.setFont(QFont("Microsoft YaHei", 18, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # 状态栏
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("🔴 检查API连接中...")
        self.status_label.setFont(QFont("Microsoft YaHei", 10))
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        domain_label = QLabel("🌐 域名: 616866.xyz")
        domain_label.setFont(QFont("Microsoft YaHei", 10))
        status_layout.addWidget(domain_label)
        
        layout.addLayout(status_layout)
        
        # 分隔线
        line = QLabel()
        line.setFixedHeight(2)
        line.setStyleSheet("background-color: #e0e0e0; margin: 10px 0;")
        layout.addWidget(line)
        
    def create_main_content(self, layout):
        """创建主要内容区域"""
        # 使用水平布局分为两列
        main_layout = QHBoxLayout()
        
        # 左列 - 操作区域
        left_layout = QVBoxLayout()
        
        # 生成邮箱组
        self.create_generate_group(left_layout)
        
        # 验证码查询组
        self.create_query_group(left_layout)
        
        left_layout.addStretch()
        
        # 右列 - 结果显示区域
        right_layout = QVBoxLayout()
        
        # 结果显示
        self.create_result_area(right_layout)
        
        # 添加到主布局
        main_layout.addLayout(left_layout, 1)
        main_layout.addLayout(right_layout, 1)
        
        layout.addLayout(main_layout)
        
    def create_generate_group(self, layout):
        """创建邮箱生成组"""
        generate_group = QGroupBox("🔄 生成邮箱")
        generate_group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        generate_layout = QGridLayout()
        generate_layout.setSpacing(10)
        
        # 应用名称
        generate_layout.addWidget(QLabel("应用名称:"), 0, 0)
        self.app_name_input = QLineEdit()
        self.app_name_input.setPlaceholderText("例如: wechat, alipay, taobao")
        self.app_name_input.setFont(QFont("Microsoft YaHei", 10))
        generate_layout.addWidget(self.app_name_input, 0, 1)
        
        # 前缀
        generate_layout.addWidget(QLabel("前缀 (可选):"), 1, 0)
        self.prefix_input = QLineEdit()
        self.prefix_input.setPlaceholderText("例如: verify, test, demo")
        self.prefix_input.setFont(QFont("Microsoft YaHei", 10))
        generate_layout.addWidget(self.prefix_input, 1, 1)
        
        # 生成按钮
        self.generate_btn = QPushButton("🔄 生成邮箱")
        self.generate_btn.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        self.generate_btn.clicked.connect(self.generate_email)
        self.generate_btn.setMinimumHeight(40)
        generate_layout.addWidget(self.generate_btn, 2, 0, 1, 2)
        
        generate_group.setLayout(generate_layout)
        layout.addWidget(generate_group)
        
    def create_query_group(self, layout):
        """创建验证码查询组"""
        query_group = QGroupBox("🔍 验证码查询")
        query_group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        query_layout = QGridLayout()
        query_layout.setSpacing(10)
        
        # 邮箱ID
        query_layout.addWidget(QLabel("邮箱ID:"), 0, 0)
        self.email_id_input = QLineEdit()
        self.email_id_input.setPlaceholderText("输入邮箱ID")
        self.email_id_input.setFont(QFont("Microsoft YaHei", 10))
        query_layout.addWidget(self.email_id_input, 0, 1)
        
        # 查询按钮
        self.query_btn = QPushButton("🔍 查询验证码")
        self.query_btn.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        self.query_btn.clicked.connect(self.query_code)
        self.query_btn.setMinimumHeight(40)
        query_layout.addWidget(self.query_btn, 1, 0, 1, 2)
        
        query_group.setLayout(query_layout)
        layout.addWidget(query_group)
        
    def create_result_area(self, layout):
        """创建结果显示区域"""
        # 邮箱生成结果
        result_group = QGroupBox("📧 邮箱生成结果")
        result_group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        result_layout = QVBoxLayout()
        
        self.result_text = QTextEdit()
        self.result_text.setMaximumHeight(180)
        self.result_text.setFont(QFont("Consolas", 10))
        self.result_text.setReadOnly(True)
        result_layout.addWidget(self.result_text)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        # 验证码结果
        code_group = QGroupBox("🔐 验证码信息")
        code_group.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))
        code_layout = QVBoxLayout()
        
        self.code_text = QTextEdit()
        self.code_text.setMaximumHeight(180)
        self.code_text.setFont(QFont("Consolas", 10))
        self.code_text.setReadOnly(True)
        code_layout.addWidget(self.code_text)
        
        code_group.setLayout(code_layout)
        layout.addWidget(code_group)
        
    def setup_timers(self):
        """设置定时器"""
        # 状态检查定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.check_status)
        self.status_timer.start(5000)  # 每5秒检查一次
        
        # 初始状态检查
        self.check_status()
        
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 15px;
                background-color: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                color: #495057;
                background-color: white;
            }
            
            QPushButton {
                background-color: #007bff;
                border: none;
                color: white;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #0056b3;
            }
            
            QPushButton:pressed {
                background-color: #004085;
            }
            
            QPushButton:disabled {
                background-color: #6c757d;
            }
            
            QLineEdit {
                border: 2px solid #ced4da;
                border-radius: 6px;
                padding: 8px 12px;
                font-size: 12px;
                background-color: white;
            }
            
            QLineEdit:focus {
                border-color: #007bff;
                outline: none;
            }
            
            QTextEdit {
                border: 2px solid #ced4da;
                border-radius: 6px;
                padding: 10px;
                background-color: white;
                font-family: 'Consolas', 'Monaco', monospace;
            }
            
            QLabel {
                color: #495057;
                font-weight: normal;
            }
        """)
        
    def check_status(self):
        """检查API状态"""
        self.worker.add_task("check_status")
        
    def generate_email(self):
        """生成邮箱"""
        app_name = self.app_name_input.text().strip()
        if not app_name:
            QMessageBox.warning(self, "警告", "请输入应用名称")
            return
            
        prefix = self.prefix_input.text().strip() or None
        
        # 禁用按钮防止重复点击
        self.generate_btn.setEnabled(False)
        self.generate_btn.setText("🔄 生成中...")
        
        # 添加任务到工作线程
        self.worker.add_task("generate_email", app_name=app_name, prefix=prefix)
        
    def query_code(self):
        """查询验证码"""
        email_id = self.email_id_input.text().strip()
        if not email_id:
            QMessageBox.warning(self, "警告", "请输入邮箱ID")
            return
            
        try:
            email_id = int(email_id)
        except ValueError:
            QMessageBox.warning(self, "警告", "邮箱ID必须是数字")
            return
            
        # 禁用按钮防止重复点击
        self.query_btn.setEnabled(False)
        self.query_btn.setText("🔍 查询中...")
        
        # 添加任务到工作线程
        self.worker.add_task("query_code", email_id=email_id)
        
    def update_status(self, connected):
        """更新连接状态"""
        if connected:
            self.status_label.setText("🟢 API服务器: 已连接")
            self.status_label.setStyleSheet("color: #28a745; font-weight: bold;")
        else:
            self.status_label.setText("🔴 API服务器: 未连接")
            self.status_label.setStyleSheet("color: #dc3545; font-weight: bold;")
            
    def on_email_generated(self, result):
        """处理邮箱生成结果"""
        # 恢复按钮状态
        self.generate_btn.setEnabled(True)
        self.generate_btn.setText("🔄 生成邮箱")
        
        if result.get("success"):
            self.result_text.setHtml(f"""
            <div style="color: #28a745; font-weight: bold; font-size: 14px;">
                ✅ 邮箱生成成功！
            </div>
            <br>
            <div style="font-family: 'Consolas', monospace;">
                <strong>邮箱地址:</strong><br>
                <span style="color: #007bff; font-size: 13px; background: #f8f9fa; padding: 4px; border-radius: 4px;">
                    {result.get('email_address')}
                </span>
            </div>
            <br>
            <div>
                <strong>邮箱ID:</strong> <span style="color: #dc3545; font-weight: bold;">{result.get('email_id')}</span><br>
                <strong>应用名称:</strong> {result.get('app_name')}<br>
                <strong>创建时间:</strong> {result.get('created_at', '').replace('T', ' ')[:19] if result.get('created_at') else '未知'}
            </div>
            <br>
            <div style="color: #6c757d; font-size: 12px;">
                💡 请将此邮箱地址用于接收验证码
            </div>
            """)
            
            # 自动填入邮箱ID
            self.email_id_input.setText(str(result.get('email_id', '')))
            
            # 清空输入框
            self.app_name_input.clear()
            self.prefix_input.clear()
            
        else:
            self.result_text.setHtml(f"""
            <div style="color: #dc3545; font-weight: bold; font-size: 14px;">
                ❌ 生成失败
            </div>
            <br>
            <div style="color: #6c757d;">
                {result.get('message', '未知错误')}
            </div>
            """)
            
    def on_code_queried(self, result):
        """处理验证码查询结果"""
        # 恢复按钮状态
        self.query_btn.setEnabled(True)
        self.query_btn.setText("🔍 查询验证码")
        
        if result.get("success"):
            self.code_text.setHtml(f"""
            <div style="color: #28a745; font-weight: bold; font-size: 14px;">
                ✅ 验证码获取成功！
            </div>
            <br>
            <div style="text-align: center; margin: 15px 0;">
                <span style="color: #dc3545; font-size: 28px; font-weight: bold; 
                           background: #f8f9fa; padding: 10px 20px; border-radius: 8px; 
                           border: 2px dashed #dc3545;">
                    {result.get('code')}
                </span>
            </div>
            <div>
                <strong>发件人:</strong> {result.get('sender_email', '未知')}<br>
                <strong>邮件主题:</strong> {result.get('subject', '无主题')}<br>
                <strong>提取时间:</strong> {result.get('extracted_at', '').replace('T', ' ')[:19] if result.get('extracted_at') else '未知'}<br>
                <strong>使用状态:</strong> <span style="color: {'#dc3545' if result.get('is_used') else '#28a745'};">
                    {'已使用' if result.get('is_used') else '未使用'}
                </span>
            </div>
            """)
        else:
            self.code_text.setHtml(f"""
            <div style="color: #ffc107; font-weight: bold; font-size: 14px;">
                ℹ️ 暂无验证码
            </div>
            <br>
            <div style="color: #6c757d;">
                {result.get('message', '未找到验证码')}
            </div>
            <br>
            <div style="color: #6c757d; font-size: 12px;">
                💡 请确保已发送邮件到生成的邮箱地址，并等待邮件转发和处理
            </div>
            """)
            
    def on_error(self, error_msg):
        """处理错误"""
        # 恢复按钮状态
        self.generate_btn.setEnabled(True)
        self.generate_btn.setText("🔄 生成邮箱")
        self.query_btn.setEnabled(True)
        self.query_btn.setText("🔍 查询验证码")
        
        QMessageBox.critical(self, "错误", error_msg)
        
    def closeEvent(self, event):
        """关闭事件"""
        self.worker.stop()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("邮箱验证码管理系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("616866.xyz")
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建主窗口
    window = OptimizedEmailUI()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
