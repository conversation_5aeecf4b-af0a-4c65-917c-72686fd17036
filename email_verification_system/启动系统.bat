@echo off
chcp 65001 >nul
title 邮箱验证码管理系统 - 一键启动

echo.
echo ========================================
echo 🚀 邮箱验证码管理系统 - 一键启动
echo ========================================
echo.

cd /d "%~dp0"

echo 📁 当前目录: %CD%
echo 🌐 域名: 616866.xyz
echo 📧 转发邮箱: <EMAIL>
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.6+并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 🚀 启动系统...
python start_all.py

if errorlevel 1 (
    echo.
    echo ❌ 系统启动失败
    echo 请检查错误信息并重试
    pause
    exit /b 1
)

echo.
echo ✅ 系统已退出
pause
