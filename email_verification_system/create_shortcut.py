#!/usr/bin/env python3
"""
创建桌面快捷方式
"""

import os
import sys
from pathlib import Path

def create_desktop_shortcut():
    """创建桌面快捷方式"""
    try:
        import winshell
        from win32com.client import Dispatch
    except ImportError:
        print("❌ 需要安装 pywin32 和 winshell")
        print("运行: pip install pywin32 winshell")
        return False
    
    try:
        # 获取桌面路径
        desktop = winshell.desktop()
        
        # 项目路径
        project_path = Path(__file__).parent
        bat_file = project_path / "启动系统.bat"
        
        # 快捷方式路径
        shortcut_path = os.path.join(desktop, "邮箱验证码系统.lnk")
        
        # 创建快捷方式
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = str(bat_file)
        shortcut.WorkingDirectory = str(project_path)
        shortcut.IconLocation = str(bat_file)
        shortcut.Description = "邮箱验证码管理系统 - 一键启动"
        shortcut.save()
        
        print(f"✅ 桌面快捷方式创建成功: {shortcut_path}")
        return True
        
    except Exception as e:
        print(f"❌ 创建快捷方式失败: {e}")
        return False

def main():
    """主函数"""
    print("🔗 创建桌面快捷方式...")
    
    if create_desktop_shortcut():
        print("🎉 现在可以通过桌面快捷方式启动系统了！")
    else:
        print("⚠️  手动启动方式:")
        print(f"   双击: {Path(__file__).parent / '启动系统.bat'}")

if __name__ == "__main__":
    main()
