#!/usr/bin/env python3
"""
邮箱验证码系统 - 一键启动脚本
"""

import os
import sys
import time
import subprocess
import threading
import requests
from pathlib import Path

class SystemLauncher:
    """系统启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.processes = {}
        self.api_ready = False
        
    def print_banner(self):
        """打印启动横幅"""
        print("=" * 60)
        print("🚀 邮箱验证码管理系统 - 一键启动")
        print("=" * 60)
        print(f"📁 项目目录: {self.project_root}")
        print(f"🌐 域名: 616866.xyz")
        print(f"📧 转发邮箱: <EMAIL>")
        print("=" * 60)
        
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 6):
            print("❌ Python版本过低，需要3.6+")
            return False
            
        # 检查必要的包
        required_packages = ['fastapi', 'uvicorn', 'PyQt5', 'requests', 'sqlalchemy']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.lower().replace('-', '_'))
                print(f"✅ {package}")
            except ImportError:
                missing_packages.append(package)
                print(f"❌ {package}")
        
        if missing_packages:
            print(f"\n📦 安装缺失的包...")
            try:
                subprocess.run([
                    sys.executable, "-m", "pip", "install"
                ] + missing_packages, check=True)
                print("✅ 依赖安装完成")
            except subprocess.CalledProcessError:
                print("❌ 依赖安装失败")
                return False
                
        return True
        
    def start_api_server(self):
        """启动API服务器"""
        print("🌐 启动API服务器...")
        
        try:
            # 检查端口是否被占用
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                print("⚠️  API服务器已在运行")
                self.api_ready = True
                return True
            except:
                pass
            
            # 启动API服务器
            api_cmd = [sys.executable, "src/api_server.py"]
            process = subprocess.Popen(
                api_cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            
            self.processes['api'] = process
            
            # 等待API服务器启动
            for i in range(30):  # 最多等待30秒
                try:
                    response = requests.get("http://localhost:8000/health", timeout=1)
                    if response.status_code == 200:
                        print("✅ API服务器启动成功")
                        self.api_ready = True
                        return True
                except:
                    pass
                time.sleep(1)
                print(f"⏳ 等待API服务器启动... ({i+1}/30)")
            
            print("❌ API服务器启动超时")
            return False
            
        except Exception as e:
            print(f"❌ API服务器启动失败: {e}")
            return False
            
    def start_gmail_monitor(self):
        """启动Gmail监听服务"""
        print("📧 启动Gmail监听服务...")
        
        # 检查Gmail凭据文件
        credentials_file = self.project_root / "config" / "gmail_credentials.json"
        if not credentials_file.exists():
            print("⚠️  Gmail凭据文件不存在，跳过Gmail监听服务")
            print("   请参考 GMAIL_SETUP.md 配置Gmail API")
            return True
            
        try:
            monitor_cmd = [sys.executable, "src/gmail_monitor.py"]
            process = subprocess.Popen(
                monitor_cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            
            self.processes['gmail'] = process
            print("✅ Gmail监听服务启动成功")
            return True
            
        except Exception as e:
            print(f"❌ Gmail监听服务启动失败: {e}")
            return False
            
    def start_gui(self):
        """启动GUI界面"""
        print("🖥️  启动GUI界面...")
        
        if not self.api_ready:
            print("⚠️  API服务器未就绪，GUI可能无法正常工作")
            
        try:
            gui_cmd = [sys.executable, "qt_ui/optimized_gui.py"]
            process = subprocess.Popen(
                gui_cmd,
                cwd=self.project_root,
                creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
            )
            
            self.processes['gui'] = process
            print("✅ GUI界面启动成功")
            return True
            
        except Exception as e:
            print(f"❌ GUI界面启动失败: {e}")
            return False
            
    def wait_for_exit(self):
        """等待用户退出"""
        print("\n" + "=" * 60)
        print("🎉 系统启动完成！")
        print("\n📋 运行中的服务:")
        
        if 'api' in self.processes:
            print("   🌐 API服务器: http://localhost:8000")
            print("   📚 API文档: http://localhost:8000/docs")
            
        if 'gmail' in self.processes:
            print("   📧 Gmail监听服务")
            
        if 'gui' in self.processes:
            print("   🖥️  Qt GUI界面")
            
        print("\n💡 使用说明:")
        print("   1. 使用Qt GUI界面进行操作")
        print("   2. 生成邮箱地址用于接收验证码")
        print("   3. 查询验证码")
        print("\n⚠️  注意事项:")
        print("   - 确保域名邮箱转发已配置")
        print("   - Gmail API需要单独配置")
        print("\n按 Ctrl+C 退出所有服务")
        print("=" * 60)
        
        try:
            # 等待GUI进程结束
            if 'gui' in self.processes:
                self.processes['gui'].wait()
        except KeyboardInterrupt:
            print("\n🛑 收到退出信号...")
            
        self.cleanup()
        
    def cleanup(self):
        """清理进程"""
        print("🧹 清理进程...")
        
        for name, process in self.processes.items():
            try:
                if process.poll() is None:  # 进程还在运行
                    print(f"   停止 {name} 服务...")
                    process.terminate()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                        
            except Exception as e:
                print(f"   清理 {name} 服务时出错: {e}")
                
        print("✅ 清理完成")
        
    def run(self):
        """运行启动流程"""
        self.print_banner()
        
        # 1. 检查依赖
        if not self.check_dependencies():
            print("❌ 依赖检查失败")
            return 1
            
        # 2. 启动API服务器
        if not self.start_api_server():
            print("❌ API服务器启动失败")
            return 1
            
        # 3. 启动Gmail监听服务
        self.start_gmail_monitor()
        
        # 4. 启动GUI界面
        if not self.start_gui():
            print("❌ GUI界面启动失败")
            return 1
            
        # 5. 等待退出
        self.wait_for_exit()
        
        return 0

def main():
    """主函数"""
    launcher = SystemLauncher()
    try:
        exit_code = launcher.run()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 用户中断启动")
        launcher.cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 启动过程中发生异常: {e}")
        launcher.cleanup()
        sys.exit(1)

if __name__ == "__main__":
    main()
