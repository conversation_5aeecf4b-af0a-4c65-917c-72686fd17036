# 📱 邮箱验证码系统 - 一键启动说明

## 🚀 快速启动

### 方式1: 双击批处理文件（推荐）
```
双击: 启动系统.bat
```

### 方式2: 命令行启动
```bash
python start_all.py
```

### 方式3: 创建桌面快捷方式
```bash
python create_shortcut.py
```

## ✨ 优化改进

### 🔧 解决的问题

1. **UI卡顿问题**
   - ✅ 使用多线程处理API请求
   - ✅ 避免UI主线程阻塞
   - ✅ 异步状态更新

2. **启动复杂性**
   - ✅ 一键启动所有服务
   - ✅ 自动依赖检查和安装
   - ✅ 智能服务管理

3. **用户体验**
   - ✅ 现代化UI设计
   - ✅ 实时状态反馈
   - ✅ 友好的错误提示

### 🎯 新功能特性

#### 📊 智能启动器
- **自动依赖检查**: 检测并安装缺失的Python包
- **服务状态检测**: 避免重复启动已运行的服务
- **进程管理**: 统一管理所有子进程
- **优雅退出**: Ctrl+C安全关闭所有服务

#### 🖥️ 优化版GUI
- **多线程架构**: API请求在后台线程执行
- **响应式设计**: 界面布局自适应
- **实时状态**: 连接状态实时更新
- **现代样式**: 美观的界面设计

#### 🔄 自动化流程
- **一键启动**: 启动所有必要服务
- **状态监控**: 实时监控服务状态
- **错误处理**: 智能错误恢复
- **资源清理**: 退出时自动清理资源

## 📋 启动流程

### 1. 系统检查阶段
```
🔍 检查系统依赖...
✅ fastapi
✅ uvicorn  
✅ PyQt5
✅ requests
✅ sqlalchemy
```

### 2. 服务启动阶段
```
🌐 启动API服务器...
📧 启动Gmail监听服务...
🖥️  启动GUI界面...
```

### 3. 运行监控阶段
```
📋 运行中的服务:
   🌐 API服务器: http://localhost:8000
   📚 API文档: http://localhost:8000/docs
   📧 Gmail监听服务
   🖥️  Qt GUI界面
```

## 🎮 使用指南

### 📧 生成邮箱
1. 在GUI中输入"应用名称"（如：wechat）
2. 可选输入"前缀"（如：verify）
3. 点击"🔄 生成邮箱"按钮
4. 复制生成的邮箱地址用于注册

### 🔍 查询验证码
1. 输入邮箱ID（生成邮箱时显示）
2. 点击"🔍 查询验证码"按钮
3. 查看验证码信息

### 📊 监控状态
- **绿色圆点** 🟢: 服务正常
- **红色圆点** 🔴: 服务异常
- 自动每5秒检查一次

## ⚙️ 配置要求

### 系统要求
- **操作系统**: Windows 10/11
- **Python**: 3.6+ 
- **内存**: 最少512MB可用内存
- **网络**: 能访问Gmail API

### 必要配置
1. **域名邮箱转发**: `*@616866.xyz` → `<EMAIL>`
2. **Gmail API**: 配置OAuth2认证（可选）

## 🔧 故障排除

### Q: 启动失败
**A**: 检查以下项目：
- Python是否正确安装
- 网络连接是否正常
- 端口8000是否被占用

### Q: GUI无响应
**A**: 
- 关闭程序重新启动
- 检查API服务器状态
- 查看控制台错误信息

### Q: 无法生成邮箱
**A**:
- 确认API服务器正在运行
- 检查网络连接
- 验证输入参数

### Q: 查询不到验证码
**A**:
- 确认邮箱ID正确
- 检查是否已发送邮件
- 验证Gmail监听服务状态
- 确认域名转发配置

## 📁 文件说明

### 核心文件
- `start_all.py` - 一键启动脚本
- `启动系统.bat` - Windows批处理启动
- `qt_ui/optimized_gui.py` - 优化版GUI

### 配置文件
- `config/settings.py` - 系统配置
- `config/gmail_credentials.json` - Gmail API凭据

### 工具文件
- `create_shortcut.py` - 创建桌面快捷方式
- `test_system.py` - 系统功能测试

## 🎯 高级功能

### 🔄 服务管理
```bash
# 只启动API服务器
python src/api_server.py

# 只启动Gmail监听
python src/gmail_monitor.py

# 只启动GUI
python qt_ui/optimized_gui.py
```

### 📊 系统测试
```bash
# 运行完整测试
python test_system.py

# 测试Gmail API
python quick_gmail_test.py

# 测试邮箱转发
python test_email_forwarding.py
```

### 🌐 Web界面
```bash
# 访问API文档
http://localhost:8000/docs

# 访问Web界面
打开: web/index.html
```

## 💡 使用技巧

1. **批量生成邮箱**: 为不同应用生成专用邮箱
2. **验证码管理**: 及时查询避免过期
3. **状态监控**: 关注连接状态指示
4. **日志查看**: 查看logs目录下的日志文件

## 🔒 安全注意

1. **Gmail凭据**: 妥善保管API凭据文件
2. **邮箱隐私**: 定期清理过期验证码
3. **网络安全**: 在安全网络环境下使用
4. **数据备份**: 定期备份重要配置

## 📞 技术支持

如遇问题请：
1. 查看控制台输出信息
2. 检查logs目录下的日志
3. 运行系统测试脚本
4. 参考项目README.md文档
