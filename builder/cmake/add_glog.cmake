# Google glog 日志库配置
set(glog_INCLUDE_DIRS ${OSS_PREFIX_PATH}/glog/include)

# 检查可用的glog库文件
if(EXISTS "${OSS_PREFIX_PATH}/glog/lib/glog.lib")
    set(glog_LIBRARIES
        debug ${OSS_PREFIX_PATH}/glog/lib/glogd.lib
        optimized ${OSS_PREFIX_PATH}/glog/lib/glogd.lib
    )
else()
    # 如果只有debug版本，则在所有配置下都使用debug版本
    set(glog_LIBRARIES ${OSS_PREFIX_PATH}/glog/lib/glogd.lib)
    message(WARNING "Only debug version of glog found, using glogd.lib for all configurations")
endif()

include_directories(${glog_INCLUDE_DIRS})
message(STATUS "GLOG_INCLUDE_DIRS: ${glog_INCLUDE_DIRS}")
message(STATUS "GLOG_LIBRARIES: ${glog_LIBRARIES}")