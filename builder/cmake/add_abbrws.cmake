# ABB RWS库配置
set(abbrws_INCLUDE_DIR ${OSS_PREFIX_PATH}/abbrws/include)
set(poco_INCLUDE_DIR ${OSS_PREFIX_PATH}/poco/x64-Debug/include)

set(abbrws_LIBRARIES
    debug ${OSS_PREFIX_PATH}/poco/x64-Debug/lib/PocoFoundationd.lib
    debug ${OSS_PREFIX_PATH}/poco/x64-Debug/lib/PocoXMLd.lib
    debug ${OSS_PREFIX_PATH}/poco/x64-Debug/lib/PocoNetd.lib
    optimized ${OSS_PREFIX_PATH}/poco/x64-Debug/lib/PocoFoundation.lib
    optimized ${OSS_PREFIX_PATH}/poco/x64-Debug/lib/PocoXML.lib
    optimized ${OSS_PREFIX_PATH}/poco/x64-Debug/lib/PocoNet.lib
    ${OSS_PREFIX_PATH}/abbrws/lib/abb_librws.lib
)

include_directories(${abbrws_INCLUDE_DIR})
include_directories(${poco_INCLUDE_DIR})
message(STATUS "abbrws_INCLUDE_DIR: ${abbrws_INCLUDE_DIR}")
message(STATUS "poco_INCLUDE_DIR: ${poco_INCLUDE_DIR}")

