# LibEvent事件库配置
set(libevent_INCLUDE_DIRS ${OSS_PREFIX_PATH}/libevent/include)
set(libevent_LIBRARIES
    debug ${OSS_PREFIX_PATH}/libevent/lib/eventd.lib
    debug ${OSS_PREFIX_PATH}/libevent/lib/event_cored.lib
    debug ${OSS_PREFIX_PATH}/libevent/lib/event_extrad.lib
    debug ${OSS_PREFIX_PATH}/libevent/lib/event_openssld.lib
    optimized ${OSS_PREFIX_PATH}/libevent/lib/event.lib
    optimized ${OSS_PREFIX_PATH}/libevent/lib/event_core.lib
    optimized ${OSS_PREFIX_PATH}/libevent/lib/event_extra.lib
    optimized ${OSS_PREFIX_PATH}/libevent/lib/event_openssl.lib
)

include_directories(${libevent_INCLUDE_DIRS})
message(STATUS "libevent_INCLUDE_DIRS: ${libevent_INCLUDE_DIRS}")
message(STATUS "libevent_LIBRARIES: ${libevent_LIBRARIES}")