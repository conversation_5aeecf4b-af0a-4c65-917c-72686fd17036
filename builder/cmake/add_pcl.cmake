# PCL点云处理库配置
# 设置PCL查找路径
if(EXISTS "${PCL_ROOT}")
    set(PCL_DIR "${PCL_ROOT}/cmake")
    list(APPEND CMAKE_PREFIX_PATH "${PCL_ROOT}")
endif()

# 查找PCL库
find_package(PCL QUIET)

if(PCL_FOUND)
    include_directories(${PCL_INCLUDE_DIRS})
    message(STATUS "PCL_INCLUDE_DIRS: ${PCL_INCLUDE_DIRS}")
    message(STATUS "PCL_LIBRARY_DIRS: ${PCL_LIBRARY_DIRS}")
    message(STATUS "PCL_VERSION: ${PCL_VERSION}")

    # 设置PCL库变量
    set(pcl_LIBRARIES ${PCL_LIBRARIES})
    set(pcl_INCLUDE_DIRS ${PCL_INCLUDE_DIRS})

    # FLANN库配置（通常包含在PCL中）
    if(DEFINED FLANN_INCLUDE_DIRS)
        include_directories(${FLANN_INCLUDE_DIRS})
        message(STATUS "FLANN_INCLUDE_DIRS: ${FLANN_INCLUDE_DIRS}")
    endif()
else()
    message(WARNING "PCL库未找到，请确保PCL已正确安装并设置PCL_ROOT路径")
endif()