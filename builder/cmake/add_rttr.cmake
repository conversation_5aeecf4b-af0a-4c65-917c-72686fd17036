# RTTR反射库配置
# 支持多种RTTR安装路径

# 设置RTTR可能的安装路径
set(RTTR_POSSIBLE_ROOTS
    "${OSS_PREFIX_PATH}/rttr/cmake"
    "${OSS_PREFIX_PATH}/rttr"
    "${OSS_PREFIX_PATH}/RTTR/cmake"
    "${OSS_PREFIX_PATH}/RTTR"
)

# 查找可用的RTTR安装
foreach(RTTR_PATH ${RTTR_POSSIBLE_ROOTS})
    if(EXISTS "${RTTR_PATH}")
        if(EXISTS "${RTTR_PATH}/cmake" OR "${RTTR_PATH}" MATCHES "cmake$")
            set(RTTR_ROOT "${RTTR_PATH}")
        else()
            set(RTTR_ROOT "${RTTR_PATH}/cmake")
        endif()
        break()
    endif()
endforeach()

# 尝试查找RTTR
find_package(RTTR CONFIG QUIET COMPONENTS Core)

if(RTTR_FOUND)
    set(rttr_INCLUDE_DIR ${OSS_PREFIX_PATH}/rttr/include)
    include_directories(${rttr_INCLUDE_DIR})
    set(rttr_LIBRARIES RTTR::Core)
    message(STATUS "rttr_INCLUDE_DIR: ${rttr_INCLUDE_DIR}")
    message(STATUS "rttr_LIBRARIES: ${rttr_LIBRARIES}")
else()
    # 如果find_package失败，尝试手动设置
    set(rttr_INCLUDE_DIR ${OSS_PREFIX_PATH}/rttr/include)
    set(rttr_LIBRARIES
        debug ${OSS_PREFIX_PATH}/rttr/lib/rttr_core_d.lib
        optimized ${OSS_PREFIX_PATH}/rttr/lib/rttr_core.lib
    )
    include_directories(${rttr_INCLUDE_DIR})
    message(STATUS "RTTR手动配置 - rttr_INCLUDE_DIR: ${rttr_INCLUDE_DIR}")
    message(STATUS "RTTR手动配置 - rttr_LIBRARIES: ${rttr_LIBRARIES}")
endif()