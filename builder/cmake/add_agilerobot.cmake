# Agile Robot Diana API配置
set(agilerobot_INCLUDE_DIRS ${OSS_PREFIX_PATH}/Diana<PERSON><PERSON>/include)
set(agilerobot_LIBRARIES
    debug ${OSS_PREFIX_PATH}/Diana<PERSON><PERSON>/lib/DianaApid.lib
    debug ${OSS_PREFIX_PATH}/DianaApi/lib/GenericAlgorithmd.lib
    debug ${OSS_PREFIX_PATH}/DianaApi/lib/VersionApid.lib
    optimized ${OSS_PREFIX_PATH}/DianaApi/lib/DianaA<PERSON>.lib
    optimized ${OSS_PREFIX_PATH}/DianaApi/lib/GenericAlgorithm.lib
    optimized ${OSS_PREFIX_PATH}/DianaApi/lib/VersionApi.lib
)

include_directories(${agilerobot_INCLUDE_DIRS})
message(STATUS "agilerobot_INCLUDE_DIRS: ${agilerobot_INCLUDE_DIRS}")
message(STATUS "agilerobot_LIBRARIES: ${agilerobot_LIBRARIES}")