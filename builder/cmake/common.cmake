# Compiler-specific definitions
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" OR CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
    add_definitions("-fext-numeric-literals")
endif()
if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/data)
    file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/data/ DESTINATION ${CMAKE_BINARY_DIR}/../install/x64-install/dev/data/)
endif ()
#CMP0087 NEW用于指定ExternalProject_Add 默认不更新任何git子模块，必须依赖3.15版本。
file(RELATIVE_PATH PROJECT_PATH ${PROJECT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR})
string(REPLACE "/" "" CURRENT_PROJECT_NAME ${PROJECT_PATH})
project(${CURRENT_PROJECT_NAME})
message(STATUS "current project name is ${PROJECT_NAME}")
#获取当前项目的目录文件名
cmake_policy(SET CMP0097 NEW)


#编译的库和进程的输出目录

if (DEFINED API_DIR)
    message(STATUS "create API_DIR ${API_DIR}")
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/../install/x64-install/dev/lib/${API_DIR})
else ()
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/../install/x64-install/dev/lib/)
endif ()
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/../install/x64-install/dev/bin)
set(CMAKE_PLUGIN_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/../install/x64-install/dev/plugins)

#开源代码的库路径和头文件路径
set(OSS_PREFIX_LIB_PATH ${OSS_PREFIX_PATH}/lib)
set(OSS_PREFIX_INC_PATH ${OSS_PREFIX_PATH}/include)
set(CMAKE_MODULE_PATH ${CMAKE_CURRENT_LIST_DIR}/Modules ${CMAKE_MODULE_PATH})







#编译的库和进程的输出目录
#set(CMAKE_INSTALL_LIBDIR ${DEV_PREFIX_PATH}/lib/)
#set(CMAKE_INSTALL_BINDIR ${DEV_PREFIX_PATH}/bin/)
set(CMAKE_INSTALL_INCLUDEDIR ${DEV_PREFIX_PATH}/)

set(prefix ${CMAKE_BINARY_DIR}/../install/x64-install/dev)
set(exec_prefix "${CMAKE_RUNTIME_OUTPUT_DIRECTORY}")
set(libdir "${CMAKE_LIBRARY_OUTPUT_DIRECTORY}")
set(includedir "${CMAKE_INSTALL_INCLUDEDIR}")
set(LIB_NAMES -l${PROJECT_NAME})

# Qt路径配置已在ThirdPartyConfig.cmake中设置
# 这里不再重复设置，避免硬编码路径

#添加当前项目的include
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
file(GLOB_RECURSE ${PROJECT_NAME}_SRCS ${CMAKE_CURRENT_SOURCE_DIR}/src/*)
file(GLOB_RECURSE ${PROJECT_NAME}_INCS ${CMAKE_CURRENT_SOURCE_DIR}/include/*)
list(LENGTH ${PROJECT_NAME}_INCS INCS_LENGTH)
list(LENGTH ${PROJECT_NAME}_SRCS SRCS_LENGTH)
set(${PROJECT_NAME}_VERSION "3")

set(${PROJECT_NAME}_DEP_LIBRARIES)
set(${PROJECT_NAME}_DEP_TARGETS)
set(${PROJECT_NAME}_DEP_LIBRARY_DIRS)
set(${PROJECT_NAME}_DEP_INCLUDE_DIRS)


#遍历依赖关系，如果不是源码编译目录或者是pkgconfig找到的依赖，那么就会调用add_xxx.cmake添加依赖，并将这些库依赖关系加到对应的变量中区。

    foreach (_dep IN ITEMS ${DEPENDS})
        string(LENGTH "${_dep}" _dep_len)
        if ("${_dep_len}" GREATER 0)
            string(REPLACE "." "/" _dep_path ${_dep})
            if (IS_DIRECTORY ${CMAKE_SOURCE_DIR}/${_dep} AND EXISTS ${CMAKE_SOURCE_DIR}/${_dep}/CMakeLists.txt)
                message(STATUS "FOUND ${_dep} as target ")
                set(${PROJECT_NAME}_DEP_LIBRARIES ${_dep} ${${PROJECT_NAME}_DEP_LIBRARIES})
            elseif (IS_DIRECTORY ${CMAKE_SOURCE_DIR}/${_dep_path} AND EXISTS ${CMAKE_SOURCE_DIR}/${_dep_path}/CMakeLists.txt)
                string(REPLACE "." "" _dep_lib ${_dep})
                set(${PROJECT_NAME}_DEP_LIBRARIES ${_dep_lib} ${${PROJECT_NAME}_DEP_LIBRARIES})
            else ()
                 message(STATUS "include ${CMAKE_CURRENT_LIST_DIR}/add_${_dep}.cmake")
                 include(add_${_dep})
                 list(APPEND ${PROJECT_NAME}_DEP_TARGETS ${_dep})
                 set(${PROJECT_NAME}_DEP_LIBRARIES ${${_dep}_LIBRARIES} ${${PROJECT_NAME}_DEP_LIBRARIES})
                 set(${PROJECT_NAME}_DEP_INCLUDE_DIRS ${${_dep}_INCLUDE_DIRS} ${${PROJECT_NAME}_DEP_INCLUDE_DIRS})
            endif ()
        endif ()
    endforeach ()


#添加依赖头文件路径
include_directories(${${PROJECT_NAME}_DEP_INCLUDE_DIRS})

# 函数：添加依赖库，避免重复添加
function(add_unique_libraries TARGET MODE)
    set(current_lib "")
    set(current_libs "")
    set(PROCESSED_LIBRARIES "")
    
    # 首先处理CMake关键字，如optimized、debug等
    foreach(item ${ARGN})
        if("${item}" STREQUAL "optimized" OR "${item}" STREQUAL "debug" OR "${item}" STREQUAL "general")
            # 如果有之前的库，先处理它
            if(NOT "${current_lib}" STREQUAL "")
                if(NOT "${current_lib}" IN_LIST PROCESSED_LIBRARIES)
                    list(APPEND PROCESSED_LIBRARIES "${current_lib}")
                    list(APPEND current_libs "${current_lib}")
                endif()
            endif()
            # 设置新的关键字
            set(current_lib "${item}")
        else()
            # 这是一个库路径
            if("${current_lib}" STREQUAL "")
                # 没有关键字，直接处理
                if(NOT "${item}" IN_LIST PROCESSED_LIBRARIES)
                    list(APPEND PROCESSED_LIBRARIES "${item}")
                    list(APPEND current_libs "${item}")
                endif()
            else()
                # 有关键字，作为一对处理
                if(NOT "${item}" IN_LIST PROCESSED_LIBRARIES)
                    list(APPEND PROCESSED_LIBRARIES "${item}")
                    list(APPEND current_libs "${current_lib}" "${item}")
                endif()
                set(current_lib "")
            endif()
        endif()
    endforeach()
    
    # 处理最后一个项
    if(NOT "${current_lib}" STREQUAL "")
        message(WARNING "关键字 '${current_lib}' 后没有跟随库路径")
    endif()
    
    # 只有当有库需要添加时才链接
    if(NOT "${current_libs}" STREQUAL "")
        target_link_libraries(${TARGET} ${MODE} ${current_libs})
        message(STATUS "为 ${TARGET} 添加依赖库(${MODE}): ${current_libs}")
    endif()
endfunction()

# 添加用于诊断依赖的函数
function(print_target_dependencies TARGET)
    get_target_property(libs ${TARGET} LINK_LIBRARIES)
    message(STATUS "${TARGET} 直接依赖库: ${libs}")
    
    get_target_property(interface_libs ${TARGET} INTERFACE_LINK_LIBRARIES)
    message(STATUS "${TARGET} 接口依赖库: ${interface_libs}")
    
    get_target_property(incs ${TARGET} INCLUDE_DIRECTORIES)
    message(STATUS "${TARGET} 包含目录: ${incs}")
    
    get_target_property(interface_incs ${TARGET} INTERFACE_INCLUDE_DIRECTORIES)
    message(STATUS "${TARGET} 接口包含目录: ${interface_incs}")
endfunction()
