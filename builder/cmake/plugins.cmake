include(${CMAKE_CURRENT_LIST_DIR}/common.cmake)

message(STATUS "plugins project ${PROJECT_NAME}         ${CMAKE_CURRENT_SOURCE_DIR}")
FILE(GLOB children RELATIVE ${CMAKE_CURRENT_SOURCE_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/*)
SET(plugins "")
FOREACH (child ${children})
    IF (IS_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/${child})
        LIST(APPEND plugins ${child})
    ENDIF ()
ENDFOREACH ()
message(STATUS "scan plugins  ${PROJECT_NAME}  ${plugins}")

# 创建一个变量存储已处理的依赖库
set(PROCESSED_LIBRARIES "")

foreach (plugin ${plugins})
    file(GLOB_RECURSE ${plugin}_SRCS ${CMAKE_CURRENT_SOURCE_DIR}/${plugin}/*.cpp ${CMAKE_CURRENT_SOURCE_DIR}/${plugin}/*.hpp ${CMAKE_CURRENT_SOURCE_DIR}/${plugin}/*.h)
    message(STATUS "${plugin}           ${${plugin}_SRCS}")

    list(LENGTH ${plugin}_SRCS _tool_dir_length)
    if (${_tool_dir_length} GREATER 0)
        message(STATUS "创建插件 ${PROJECT_NAME}${plugin}")
        list(LENGTH QT_LIBS QT_LIBS_LEN)
        #如果发现QT依赖使用 automoc autouic编译。
        if (${QT_LIBS_LEN} GREATER 0)
            find_package(Qt5Core REQUIRED)

            ADD_DEFINITIONS("-DQT_DISABLE_DEPRECATED_BEFORE=0")
            ADD_DEFINITIONS("-DQT_NO_DEBUG")
            ADD_DEFINITIONS("-DQT_WIDGETS_LIB")
            ADD_DEFINITIONS("-DQT_GUI_LIB")
            ADD_DEFINITIONS("-DQT_CORE_LIB")
            set(CMAKE_AUTOMOC ON)
            set(CMAKE_AUTOUIC ON)

            file(GLOB_RECURSE QRC src/*.qrc)
            qt5_add_resources(QRCS ${QRC})
            message(STATUS "创建Qt插件 ${PROJECT_NAME}${plugin}")
            add_library(${PROJECT_NAME}${plugin} SHARED ${${PROJECT_NAME}_SRCS} ${${PROJECT_NAME}_INCS} ${QRCS})
            
            # 使用优化后的依赖添加函数
            add_unique_libraries(${PROJECT_NAME}${plugin} PUBLIC ${${PROJECT_NAME}_DEP_LIBRARIES})
            
            foreach (_qt_lib IN ITEMS ${QT_LIBS})
                find_package(${_qt_lib} REQUIRED)
                include_directories(${${_qt_lib}_INCLUDE_DIRS})
                add_unique_libraries(${PROJECT_NAME}${plugin} PUBLIC ${${_qt_lib}_LIBRARIES})
            endforeach ()
            
            set_target_properties(${PROJECT_NAME}${plugin} PROPERTIES
                    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_PLUGIN_OUTPUT_DIRECTORY}/${PLUGIN_NAME})
        else ()
            message(STATUS "创建插件 ${PROJECT_NAME}${plugin}")
            add_library(${PROJECT_NAME}${plugin} SHARED ${${plugin}_SRCS})
            
            # 使用优化后的依赖添加函数
            add_unique_libraries(${PROJECT_NAME}${plugin} PUBLIC ${${PROJECT_NAME}_DEP_LIBRARIES})
        endif ()
        
        # 设置目标属性，使依赖关系能够传递
        target_include_directories(${PROJECT_NAME}${plugin} PUBLIC
            "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/${plugin}/include>"
            "$<INSTALL_INTERFACE:${CMAKE_INSTALL_PREFIX}/${CMAKE_INSTALL_INCLUDEDIR}>")

        # 确保所有依赖的头文件也能被包含
        target_include_directories(${PROJECT_NAME}${plugin} PUBLIC ${${PROJECT_NAME}_DEP_INCLUDE_DIRS})
        
        set_target_properties(${PROJECT_NAME}${plugin} PROPERTIES
                LIBRARY_OUTPUT_DIRECTORY ${CMAKE_PLUGIN_OUTPUT_DIRECTORY}/${PLUGIN_NAME})
        foreach (_target IN ITEMS ${${PROJECT_NAME}_DEP_TARGETS})
            if (TARGET ${_target})
                add_dependencies(${PROJECT_NAME}${plugin} ${_target})
            endif ()
        endforeach ()
    endif ()
endforeach ()