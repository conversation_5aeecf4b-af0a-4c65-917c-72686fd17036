# JsonCpp JSON解析库配置
set(jsoncpp_INCLUDE_DIRS ${OSS_PREFIX_PATH}/jsoncpp/include)
set(jsoncpp_LIBRARIES
    debug ${OSS_PREFIX_PATH}/jsoncpp/lib/jsoncpp.lib
    debug ${OSS_PREFIX_PATH}/jsoncpp/lib/jsoncpp_static.lib
    optimized ${OSS_PREFIX_PATH}/jsoncpp/lib/jsoncpp.lib
    optimized ${OSS_PREFIX_PATH}/jsoncpp/lib/jsoncpp_static.lib
)

include_directories(${jsoncpp_INCLUDE_DIRS})
message(STATUS "jsoncpp_INCLUDE_DIRS: ${jsoncpp_INCLUDE_DIRS}")
message(STATUS "jsoncpp_LIBRARIES: ${jsoncpp_LIBRARIES}")