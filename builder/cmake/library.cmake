include(${CMAKE_CURRENT_LIST_DIR}/common.cmake)
#如果有源码文件就编译成动态库，否则不创建动态库
if ("${SRCS_LENGTH}" GREATER 0)
    message(STATUS "创建静态库 ${PROJECT_NAME}")
    list(LENGTH QT_LIBS QT_LIBS_LEN)
    
    # 创建一个变量存储已处理的依赖库
    set(PROCESSED_LIBRARIES "")
    
    # 函数：添加依赖库，避免重复添加
    function(add_unique_libraries TARGET MODE)
        set(current_lib "")
        set(current_libs "")
        
        # 首先处理CMake关键字，如optimized、debug等
        foreach(item ${ARGN})
            if("${item}" STREQUAL "optimized" OR "${item}" STREQUAL "debug" OR "${item}" STREQUAL "general")
                # 如果有之前的库，先处理它
                if(NOT "${current_lib}" STREQUAL "")
                    if(NOT "${current_lib}" IN_LIST PROCESSED_LIBRARIES)
                        list(APPEND PROCESSED_LIBRARIES "${current_lib}")
                        list(APPEND current_libs "${current_lib}")
                    endif()
                endif()
                # 设置新的关键字
                set(current_lib "${item}")
            else()
                # 这是一个库路径
                if("${current_lib}" STREQUAL "")
                    # 没有关键字，直接处理
                    if(NOT "${item}" IN_LIST PROCESSED_LIBRARIES)
                        list(APPEND PROCESSED_LIBRARIES "${item}")
                        list(APPEND current_libs "${item}")
                    endif()
                else()
                    # 有关键字，作为一对处理
                    if(NOT "${item}" IN_LIST PROCESSED_LIBRARIES)
                        list(APPEND PROCESSED_LIBRARIES "${item}")
                        list(APPEND current_libs "${current_lib}" "${item}")
                    endif()
                    set(current_lib "")
                endif()
            endif()
        endforeach()
        
        # 处理最后一个项
        if(NOT "${current_lib}" STREQUAL "")
            message(WARNING "关键字 '${current_lib}' 后没有跟随库路径")
        endif()
        
        # 只有当有库需要添加时才链接
        if(NOT "${current_libs}" STREQUAL "")
            target_link_libraries(${TARGET} ${MODE} ${current_libs})
            message(STATUS "为 ${TARGET} 添加依赖库(${MODE}): ${current_libs}")
        endif()
        
        set(PROCESSED_LIBRARIES ${PROCESSED_LIBRARIES} PARENT_SCOPE)
    endfunction()
    
    #如果发现QT依赖使用 automoc autouic编译。
    if (${QT_LIBS_LEN} GREATER 0)
        find_package(Qt5Core REQUIRED)

        ADD_DEFINITIONS("-DQT_DISABLE_DEPRECATED_BEFORE=0")
        ADD_DEFINITIONS("-DQT_NO_DEBUG")
        ADD_DEFINITIONS("-DQT_WIDGETS_LIB")
        ADD_DEFINITIONS("-DQT_GUI_LIB")
        ADD_DEFINITIONS("-DQT_CORE_LIB")
        set(CMAKE_AUTOMOC ON)
        set(CMAKE_AUTOUIC ON)

        file(GLOB_RECURSE QRC src/*.qrc)
        qt5_add_resources(QRCS ${QRC})
        message(STATUS "创建Qt静态库 ${PROJECT_NAME}")

        add_library(${PROJECT_NAME} STATIC ${${PROJECT_NAME}_SRCS} ${${PROJECT_NAME}_INCS} ${QRCS})
        # 使用PUBLIC关键字，确保依赖传递
        add_unique_libraries(${PROJECT_NAME} PUBLIC ${${PROJECT_NAME}_DEP_LIBRARIES})
        
        message(STATUS "_target ${${PROJECT_NAME}_DEP_LIBRARIES}")
        foreach (_qt_lib IN ITEMS ${QT_LIBS})
            find_package(${_qt_lib} REQUIRED)
            include_directories(${${_qt_lib}_INCLUDE_DIRS})
            add_unique_libraries(${PROJECT_NAME} PUBLIC ${${_qt_lib}_LIBRARIES})
        endforeach ()
    else ()
        message(STATUS "创建静态库 ${PROJECT_NAME}")
        add_library(${PROJECT_NAME} STATIC ${${PROJECT_NAME}_SRCS} ${${PROJECT_NAME}_INCS})
        # 使用PUBLIC关键字，确保依赖传递
        add_unique_libraries(${PROJECT_NAME} PUBLIC ${${PROJECT_NAME}_DEP_LIBRARIES})
    endif ()
    
    foreach (_target IN ITEMS ${${PROJECT_NAME}_DEP_TARGETS})
        if (TARGET ${_target})
            add_dependencies(${PROJECT_NAME} ${_target})
        endif ()
    endforeach ()

    # 设置目标属性，使依赖关系能够传递
    target_include_directories(${PROJECT_NAME} PUBLIC
        "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
        "$<INSTALL_INTERFACE:${CMAKE_INSTALL_PREFIX}/${CMAKE_INSTALL_INCLUDEDIR}>")

    # 确保所有依赖的头文件也能被包含
    target_include_directories(${PROJECT_NAME} PUBLIC ${${PROJECT_NAME}_DEP_INCLUDE_DIRS})

    message(STATUS "CMAKE_CURRENT_SOURCE_DIR library ${CMAKE_CURRENT_SOURCE_DIR}")

    if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/include)
        install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include
                DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}" COMPONENT include)
    endif ()
    
    install(FILES ${CMAKE_BINARY_DIR}/${PROJECT_NAME}.pc DESTINATION ${CMAKE_LIBRARY_OUTPUT_DIRECTORY}/pkgconfig/)
else ()
    message(STATUS "创建仅头文件库 ${PROJECT_NAME}")
    add_library(${PROJECT_NAME} INTERFACE)
    
    # 对于接口库，使用INTERFACE关键字
    target_include_directories(${PROJECT_NAME} INTERFACE 
        "$<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>"
        "$<INSTALL_INTERFACE:${CMAKE_INSTALL_PREFIX}/${CMAKE_INSTALL_INCLUDEDIR}>")
    
    # 确保接口库也传递其依赖
    if(NOT "${${PROJECT_NAME}_DEP_LIBRARIES}" STREQUAL "")
        target_link_libraries(${PROJECT_NAME} INTERFACE ${${PROJECT_NAME}_DEP_LIBRARIES})
    endif()
    
    # 确保接口库也传递其依赖的头文件
    if(NOT "${${PROJECT_NAME}_DEP_INCLUDE_DIRS}" STREQUAL "")
        target_include_directories(${PROJECT_NAME} INTERFACE ${${PROJECT_NAME}_DEP_INCLUDE_DIRS})
    endif()
    
    install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include
            DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}" COMPONENT include)
endif ()

