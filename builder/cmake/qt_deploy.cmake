# Qt部署配置
# 自动复制Qt运行时依赖到输出目录

function(qt_deploy_runtime TARGET_NAME)
    # 获取Qt安装路径
    get_target_property(QT5_QMAKE_EXECUTABLE Qt5::qmake IMPORTED_LOCATION)
    get_filename_component(QT5_WINDEPLOYQT_EXECUTABLE ${QT5_QMAKE_EXECUTABLE} PATH)
    set(QT5_WINDEPLOYQT_EXECUTABLE "${QT5_WINDEPLOYQT_EXECUTABLE}/windeployqt.exe")
    
    # 暂时禁用windeployqt，使用手动部署方式
    message(STATUS "Using manual deployment for ${TARGET_NAME}")
    qt_manual_deploy(${TARGET_NAME})
    return()

    # 如果windeployqt不存在，使用手动复制方式
    if(NOT EXISTS ${QT5_WINDEPLOYQT_EXECUTABLE})
        message(STATUS "windeployqt not found, using manual deployment for ${TARGET_NAME}")
        qt_manual_deploy(${TARGET_NAME})
        return()
    endif()

    # 使用windeployqt自动部署
    message(STATUS "Using windeployqt for ${TARGET_NAME}")

    # 添加自定义命令在构建后运行windeployqt
    add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
        COMMAND ${QT5_WINDEPLOYQT_EXECUTABLE} --debug --release --sql --charts --network $<TARGET_FILE:${TARGET_NAME}>
        COMMENT "Deploying Qt libraries for ${TARGET_NAME}")
endfunction()

function(qt_manual_deploy TARGET_NAME)
    message(STATUS "Manual Qt deployment for ${TARGET_NAME}")
    
    # 获取Qt路径
    if(NOT DEFINED QT_ROOT)
        get_target_property(QT5_CORE_LOCATION Qt5::Core LOCATION)
        get_filename_component(QT_BIN_DIR ${QT5_CORE_LOCATION} DIRECTORY)
        get_filename_component(QT_ROOT ${QT_BIN_DIR} DIRECTORY)
    else()
        set(QT_BIN_DIR "${QT_ROOT}/bin")
    endif()
    
    set(QT_PLUGINS_DIR "${QT_ROOT}/plugins")
    
    # 获取目标输出目录
    get_target_property(TARGET_OUTPUT_DIR ${TARGET_NAME} RUNTIME_OUTPUT_DIRECTORY)
    if(NOT TARGET_OUTPUT_DIR)
        set(TARGET_OUTPUT_DIR ${CMAKE_RUNTIME_OUTPUT_DIRECTORY})
    endif()
    
    # 根据配置类型设置输出目录
    if(CMAKE_CONFIGURATION_TYPES)
        set(TARGET_OUTPUT_DIR "${TARGET_OUTPUT_DIR}/$<CONFIG>")
    endif()
    
    message(STATUS "Qt deployment target directory: ${TARGET_OUTPUT_DIR}")
    
    # 定义需要复制的Qt DLL列表
    set(QT_DLLS
        Qt5Core.dll
        Qt5Gui.dll
        Qt5Widgets.dll
        Qt5Sql.dll
        Qt5Charts.dll
        Qt5Network.dll
    )
    
    # 复制Qt DLL文件
    foreach(QT_DLL ${QT_DLLS})
        add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${QT_BIN_DIR}/${QT_DLL}"
                "${TARGET_OUTPUT_DIR}/${QT_DLL}"
            COMMENT "Copying ${QT_DLL}")
    endforeach()
    
    # 创建plugins目录并复制插件
    add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "${TARGET_OUTPUT_DIR}/platforms"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${QT_PLUGINS_DIR}/platforms/qwindows.dll"
            "${TARGET_OUTPUT_DIR}/platforms/qwindows.dll"
        COMMENT "Copying Qt platform plugins")
    
    add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "${TARGET_OUTPUT_DIR}/sqldrivers"
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${QT_PLUGINS_DIR}/sqldrivers/qsqlite.dll"
            "${TARGET_OUTPUT_DIR}/sqldrivers/qsqlite.dll"
        COMMENT "Copying Qt SQL drivers")
    
    # 复制其他可能需要的插件
    set(QT_IMAGEFORMATS
        qgif.dll
        qico.dll
        qjpeg.dll
        qsvg.dll
        qtiff.dll
        qwbmp.dll
        qwebp.dll
    )
    
    add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E make_directory "${TARGET_OUTPUT_DIR}/imageformats")
    
    foreach(IMAGE_FORMAT ${QT_IMAGEFORMATS})
        add_custom_command(TARGET ${TARGET_NAME} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
                "${QT_PLUGINS_DIR}/imageformats/${IMAGE_FORMAT}"
                "${TARGET_OUTPUT_DIR}/imageformats/${IMAGE_FORMAT}"
            COMMENT "Copying ${IMAGE_FORMAT}")
    endforeach()
endfunction()
