# VTK可视化工具包配置
# 设置VTK可能的安装路径
set(VTK_POSSIBLE_PATHS
    "${PCL_ROOT}/3rdParty/VTK/lib/cmake/vtk-9.1"
    "${PCL_ROOT}/3rdParty/VTK/lib/cmake/vtk-9.0"
    "${OSS_PREFIX_PATH}/VTK/lib/cmake/vtk-9.1"
    "${OSS_PREFIX_PATH}/VTK/lib/cmake/vtk-9.0"
    "${OSS_PREFIX_PATH}/vtk/lib/cmake/vtk-9.1"
    "${OSS_PREFIX_PATH}/vtk/lib/cmake/vtk-9.0"
)

# 查找可用的VTK安装
foreach(VTK_PATH ${VTK_POSSIBLE_PATHS})
    if(EXISTS "${VTK_PATH}")
        set(VTK_DIR "${VTK_PATH}" CACHE PATH "The directory containing VTKConfig.cmake")
        break()
    endif()
endforeach()

# 查找VTK
find_package(VTK ${QUIET_} COMPONENTS ${PCL_VTK_COMPONENTS})

if(VTK_FOUND)
    include_directories(${VTK_INCLUDE_DIRS})
    set(vtk_LIBRARIES ${VTK_LIBRARIES})
    set(vtk_INCLUDE_DIRS ${VTK_INCLUDE_DIRS})

    message(STATUS "VTK_INCLUDE_DIRS: ${VTK_INCLUDE_DIRS}")
    message(STATUS "VTK_VERSION: ${VTK_VERSION}")
else()
    message(WARNING "VTK库未找到，请确保VTK已正确安装")
endif()