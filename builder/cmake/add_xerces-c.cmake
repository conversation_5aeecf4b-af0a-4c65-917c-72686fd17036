# Xerces-C XML解析库配置
set(xerces-c_INCLUDE_DIR ${OSS_PREFIX_PATH}/xerces-c/include)
set(xerces-c_LIBRARIES
    debug ${OSS_PREFIX_PATH}/xerces-c/lib/xerces-c_3D.lib
    optimized ${OSS_PREFIX_PATH}/xerces-c/lib/xerces-c_3D.lib
)

# 设置变量以保持兼容性
set(xerces-c ${xerces-c_LIBRARIES})

include_directories(${xerces-c_INCLUDE_DIR})
message(STATUS "xerces-c_INCLUDE_DIR: ${xerces-c_INCLUDE_DIR}")
message(STATUS "xerces-c_LIBRARIES: ${xerces-c_LIBRARIES}")