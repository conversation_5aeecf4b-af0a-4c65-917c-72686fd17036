# Boost库配置
# 优先使用PCL自带的Boost，如果不存在则查找系统安装的Boost

# 设置Boost查找路径
set(BOOST_POSSIBLE_ROOTS
    "${PCL_ROOT}/3rdParty/Boost"
    "${OSS_PREFIX_PATH}/boost"
    "${OSS_PREFIX_PATH}/Boost"
)

# 查找可用的Boost安装
foreach(BOOST_PATH ${BOOST_POSSIBLE_ROOTS})
    if(EXISTS "${BOOST_PATH}")
        set(Boost_ROOT "${BOOST_PATH}")
        break()
    endif()
endforeach()

# 如果找到了PCL的Boost，设置cmake路径
if(EXISTS "${PCL_ROOT}/3rdParty/Boost/lib/cmake")
    set(Boost_ROOT "${PCL_ROOT}/3rdParty/Boost/lib/cmake")
endif()

set(Boost_ADDITIONAL_VERSIONS
        "1.80.0" "1.80" "1.78.0" "1.78"
        "1.78.0" "1.78" "1.77.0" "1.77" "1.76.0" "1.76" "1.75.0" "1.75"
        "1.74.0" "1.74" "1.73.0" "1.73" "1.72.0" "1.72" "1.71.0" "1.71" "1.70.0" "1.70"
        "1.69.0" "1.69" "1.68.0" "1.68" "1.67.0" "1.67" "1.66.0" "1.66" "1.65.1" "1.65.0" "1.65")

set(Boost_USE_DEBUG_LIBS ON)
set(Boost_USE_RELEASE_LIBS OFF)

find_package(Boost 1.65.0 ${QUIET_} COMPONENTS system filesystem date_time iostreams serialization thread program_options )

if(Boost_FOUND)
    set(boost_INCLUDE_DIRS ${Boost_INCLUDE_DIRS})
    set(boost_LIBRARY_DIRS "${Boost_LIBRARY_DIRS}")
    set(boost_LIBRARIES ${Boost_LIBRARIES})

    include_directories(${boost_INCLUDE_DIRS})
    message(STATUS "boost_INCLUDE_DIRS: ${boost_INCLUDE_DIRS}")
    message(STATUS "boost_LIBRARIES: ${boost_LIBRARIES}")
else()
    message(WARNING "Boost库未找到，请确保Boost已正确安装")
endif()
