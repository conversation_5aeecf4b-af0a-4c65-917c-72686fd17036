# RobWork机器人工作空间库配置
# 设置RobWork可能的安装路径
set(ROBWORK_POSSIBLE_PATHS
    "${OSS_PREFIX_PATH}/robwork-21.12/robwork-21.12/cmake"
    "${OSS_PREFIX_PATH}/robwork/cmake"
    "${OSS_PREFIX_PATH}/RobWork/cmake"
)

# 查找可用的RobWork安装
foreach(ROBWORK_PATH ${ROBWORK_POSSIBLE_PATHS})
    if(EXISTS "${ROBWORK_PATH}")
        list(APPEND CMAKE_PREFIX_PATH "${ROBWORK_PATH}")
        break()
    endif()
endforeach()

# 查找RobWork
find_package(RobWork QUIET)

if(RobWork_FOUND OR ROBWORK_FOUND)
    include_directories(${ROBWORK_INCLUDE_DIRS})
    set(robwork_LIBRARIES ${ROBWORK_LIBRARIES})
    set(robwork_INCLUDE_DIRS ${ROBWORK_INCLUDE_DIRS})

    message(STATUS "ROBWORK_LIBRARIES: ${ROBWORK_LIBRARIES}")
    message(STATUS "ROBWORK_INCLUDE_DIRS: ${ROBWORK_INCLUDE_DIRS}")
else()
    message(WARNING "RobWork库未找到，请确保RobWork已正确安装")
endif()


