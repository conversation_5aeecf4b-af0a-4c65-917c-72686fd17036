# LaserControlFrame SDK configuration
# Support multiple LaserControlFrame installation paths

# Set possible LaserControlFrame installation paths
set(LASERCONTROLFRAME_POSSIBLE_ROOTS
    "${OSS_PREFIX_PATH}/laserControlFrame"
    "${OSS_PREFIX_PATH}/LaserControlFrame"
    "${OSS_PREFIX_PATH}/laser_control_frame"
)

# Find available LaserControlFrame installation
foreach(LASERCONTROLFRAME_PATH ${LASERCONTROLFRAME_POSSIBLE_ROOTS})
    if(EXISTS "${LASERCONTROLFRAME_PATH}")
        set(LASERCONTROLFRAME_ROOT "${LASERCONTROLFRAME_PATH}")
        break()
    endif()
endforeach()

# Check if LaserControlFrame is found
if(EXISTS "${LASERCONTROLFRAME_ROOT}")
    set(laserControlFrame_INCLUDE_DIRS ${LASERCONTROLFRAME_ROOT}/include)

    # Set library files, support Debug and Release versions
    if(EXISTS "${LASERCONTROLFRAME_ROOT}/lib/LaserControlFrameSDK.lib")
        # 检查是否有debug版本的库
        if(EXISTS "${LASERCONTROLFRAME_ROOT}/lib/LaserControlFrameSDKd.lib")
            set(laserControlFrame_LIBRARIES
                debug ${LASERCONTROLFRAME_ROOT}/lib/LaserControlFrameSDKd.lib
                optimized ${LASERCONTROLFRAME_ROOT}/lib/LaserControlFrameSDK.lib
            )
        else()
            # 只有一个库文件，在所有配置下都使用它
            set(laserControlFrame_LIBRARIES ${LASERCONTROLFRAME_ROOT}/lib/LaserControlFrameSDK.lib)
            message(STATUS "Using single LaserControlFrame library for all configurations")
        endif()
    else()
        # Fallback to release version only
        set(laserControlFrame_LIBRARIES ${LASERCONTROLFRAME_ROOT}/lib/LaserControlFrameSDK.lib)
    endif()

    include_directories(${laserControlFrame_INCLUDE_DIRS})

    message(STATUS "LaserControlFrame found at: ${LASERCONTROLFRAME_ROOT}")
    message(STATUS "laserControlFrame_INCLUDE_DIRS: ${laserControlFrame_INCLUDE_DIRS}")
    message(STATUS "laserControlFrame_LIBRARIES: ${laserControlFrame_LIBRARIES}")
else()
    message(WARNING "LaserControlFrame SDK not found. Please ensure LaserControlFrame is properly installed in ${OSS_PREFIX_PATH}")
    # Set empty variables to avoid CMake errors
    set(laserControlFrame_INCLUDE_DIRS "")
    set(laserControlFrame_LIBRARIES "")
endif()