# LibFairino机器人库配置
set(libfairino_INCLUDE_DIR ${OSS_PREFIX_PATH}/libfairino/include)

# 设置库文件路径，注意路径中的空格
set(FAIRINO_LIB_PATH "${OSS_PREFIX_PATH}/libfairino/lib/vs2017 x86-64")
set(libfairino_LIBRARIES
    debug "${FAIRINO_LIB_PATH}/Debug/fairinod.lib"
    optimized "${FAIRINO_LIB_PATH}/Release/fairino.lib"
)

include_directories(${libfairino_INCLUDE_DIR})
message(STATUS "libfairino_INCLUDE_DIR: ${libfairino_INCLUDE_DIR}")
message(STATUS "libfairino_LIBRARIES: ${libfairino_LIBRARIES}")
