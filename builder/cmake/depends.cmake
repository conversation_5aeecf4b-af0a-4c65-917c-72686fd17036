#解析子目录的CMakeLists文件里定义的DEPENDS，根据依赖调用add_subdirectory()
#file(GLOB_RECURSE ${PROJECT_NAME}_SRCS ${CMAKE_CURRENT_SOURCE_DIR}/*/CMakeLists.txt)
set(SUBDIRS)
file(GLOB_RECURSE CMAKES RELATIVE ${PROJECT_SOURCE_DIR}/ ${CMAKE_CURRENT_SOURCE_DIR}/CMakeLists.txt)
foreach (_dir IN ITEMS ${CMAKES})
    if (${_dir} MATCHES "^builder" OR ${_dir} MATCHES "^cmake-build-debug"
            OR ${_dir} MATCHES "^cmake-build-release"
            OR ${_dir} MATCHES "^build"
            OR ${_dir} MATCHES "^install")
        message(STATUS "skip directory ${_dir}")
    else ()
        get_filename_component(sub_dir ${_dir} DIRECTORY)
        message(STATUS "${sub_dir}")
        list(APPEND SUBDIRS ${sub_dir})
    endif ()
endforeach ()

set(SUB_DIRECOTRIES)
message(STATUS " SUBDIRS ${SUBDIRS}")
foreach (_dir IN ITEMS ${SUBDIRS})
    if (IS_DIRECTORY ${PROJECT_SOURCE_DIR}/${_dir} AND EXISTS ${PROJECT_SOURCE_DIR}/${_dir}/CMakeLists.txt)
        list(APPEND SUB_DIRECOTRIES ${_dir})
        set(${_dir}_DEP_SUBDIRS)
        FILE(READ "${PROJECT_SOURCE_DIR}/${_dir}/CMakeLists.txt" contents)
        string(FIND ${contents} "set(DEPENDS" out)
        string(LENGTH ${contents} _len)
        if (NOT ${out} EQUAL -1)
            math(EXPR _length "${_len}-${out}")
            math(EXPR out "${out}+11")
            string(SUBSTRING ${contents} ${out} ${_length} tempStr)
            string(FIND ${tempStr} ")" out)
            string(SUBSTRING ${tempStr} 0 ${out} tempStr)
            string(REPLACE " " ";" _list "${tempStr}")

            foreach (_item IN ITEMS ${_list})
                string(REPLACE "." "/" PATH ${_item})
                if (IS_DIRECTORY ${PROJECT_SOURCE_DIR}/${PATH} AND EXISTS ${PROJECT_SOURCE_DIR}/${PATH}/CMakeLists.txt)
                    list(APPEND ${_dir}_DEP_SUBDIRS ${PATH})
                endif ()
            endforeach ()
        endif ()
    endif ()
endforeach ()

function(addDir _dir)
    string(REPLACE "/" "" _target ${_dir})
    if (NOT TARGET ${_target})
        message(STATUS "add_subdirectory ${_dir}")
        add_subdirectory(${_dir})
    endif ()
endfunction()

foreach (_com IN ITEMS ${SUB_DIRECOTRIES})
    foreach (_dir IN ITEMS ${${_com}_DEP_SUBDIRS})
        addDir(${_dir})
    endforeach ()
    addDir(${_com})
endforeach ()