# OpenSSL加密库配置
set(openssl_INCLUDE_DIRS ${OSS_PREFIX_PATH}/openssl/include)

# 启用OpenSSL支持
add_definitions(-DCPPHTTPLIB_OPENSSL_SUPPORT)

# 设置OpenSSL库文件，支持Debug和Release版本
set(openssl_LIBRARIES
    ${OSS_PREFIX_PATH}/openssl/lib/libssl.lib
    ${OSS_PREFIX_PATH}/openssl/lib/libcrypto.lib
    ${OSS_PREFIX_PATH}/openssl/lib/jsoncpp.lib
    ${OSS_PREFIX_PATH}/openssl/lib/libcurl.lib
    ${OSS_PREFIX_PATH}/openssl/lib/zlib.lib
)

# 确保正确链接OpenSSL库
if(WIN32)
    # 添加Windows系统库，这些是OpenSSL在Windows上需要的
    set(OPENSSL_SYSTEM_LIBS ws2_32 crypt32 advapi32 user32)
    list(APPEND openssl_LIBRARIES ${OPENSSL_SYSTEM_LIBS})
endif()

include_directories(${openssl_INCLUDE_DIRS})
message(STATUS "openssl_INCLUDE_DIRS: ${openssl_INCLUDE_DIRS}")
message(STATUS "openssl_LIBRARIES: ${openssl_LIBRARIES}")