# 第三方库路径配置文件
# 这个文件用于配置所有第三方库的路径，支持相对路径和环境变量

# 获取项目根目录
get_filename_component(PROJECT_ROOT_DIR "${CMAKE_SOURCE_DIR}" ABSOLUTE)

# 默认第三方库根目录 - 可以通过环境变量或CMake变量覆盖
if(DEFINED ENV{THIRD_PARTY_ROOT})
    set(THIRD_PARTY_ROOT "$ENV{THIRD_PARTY_ROOT}")
elseif(NOT DEFINED THIRD_PARTY_ROOT)
    # 默认使用项目根目录下的third_party目录
    set(THIRD_PARTY_ROOT "${PROJECT_ROOT_DIR}/third_party")
endif()

# 如果third_party目录不存在，尝试使用C:/opt作为后备
if(NOT EXISTS "${THIRD_PARTY_ROOT}")
    if(EXISTS "C:/opt")
        set(THIRD_PARTY_ROOT "C:/opt")
        message(WARNING "第三方库目录 ${PROJECT_ROOT_DIR}/third_party 不存在，使用后备路径: ${THIRD_PARTY_ROOT}")
    else()
        message(FATAL_ERROR "第三方库目录不存在: ${THIRD_PARTY_ROOT}")
    endif()
endif()

message(STATUS "第三方库根目录: ${THIRD_PARTY_ROOT}")

# 设置各个第三方库的路径
set(OSS_PREFIX_PATH "${THIRD_PARTY_ROOT}")
set(PCL_ROOT "${OSS_PREFIX_PATH}/PCL")

# Qt路径配置 - 支持环境变量覆盖
if(DEFINED ENV{QT_ROOT})
    set(QT_ROOT "$ENV{QT_ROOT}")
elseif(NOT DEFINED QT_ROOT)
    # 尝试常见的Qt安装路径
    set(QT_POSSIBLE_PATHS
        "${THIRD_PARTY_ROOT}/Qt/Qt5.14.2/5.14.2/msvc2017_64"
        "C:/Qt/Qt5.14.2/5.14.2/msvc2017_64"
        "C:/Qt/5.14.2/msvc2017_64"
        "${PROJECT_ROOT_DIR}/third_party/Qt/5.14.2/msvc2017_64"
    )
    
    foreach(QT_PATH ${QT_POSSIBLE_PATHS})
        if(EXISTS "${QT_PATH}")
            set(QT_ROOT "${QT_PATH}")
            break()
        endif()
    endforeach()
    
    if(NOT DEFINED QT_ROOT)
        message(FATAL_ERROR "找不到Qt安装目录，请设置环境变量QT_ROOT或将Qt安装到以下路径之一: ${QT_POSSIBLE_PATHS}")
    endif()
endif()

message(STATUS "Qt根目录: ${QT_ROOT}")

# 验证关键路径是否存在
if(NOT EXISTS "${OSS_PREFIX_PATH}")
    message(WARNING "第三方库路径不存在: ${OSS_PREFIX_PATH}")
endif()

if(NOT EXISTS "${QT_ROOT}")
    message(WARNING "Qt路径不存在: ${QT_ROOT}")
endif()

# 设置Qt相关路径
set(Qt5Core_DIR "${QT_ROOT}/lib/cmake/Qt5Core")
set(Qt5_DIR "${QT_ROOT}/lib/cmake/Qt5")
set(QT_QMAKE_EXECUTABLE "${QT_ROOT}/bin/qmake")
set(CMAKE_PREFIX_PATH "${QT_ROOT}/lib/cmake" ${CMAKE_PREFIX_PATH})

# 设置CMake策略
if(POLICY CMP0167)
    cmake_policy(SET CMP0167 OLD)  # 保持使用FindBoost模块
endif()

if(POLICY CMP0144)
    cmake_policy(SET CMP0144 OLD)  # 兼容旧版本的find_package行为
endif()

# 输出配置信息
message(STATUS "=== 第三方库配置 ===")
message(STATUS "OSS_PREFIX_PATH: ${OSS_PREFIX_PATH}")
message(STATUS "PCL_ROOT: ${PCL_ROOT}")
message(STATUS "QT_ROOT: ${QT_ROOT}")
message(STATUS "========================")
