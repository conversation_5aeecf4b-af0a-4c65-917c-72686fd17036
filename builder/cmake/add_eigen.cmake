# Eigen数学库配置
# 支持多种Eigen安装路径

# 设置Eigen可能的安装路径
set(EIGEN_POSSIBLE_PATHS
    "${PCL_ROOT}/3rdParty/Eigen/eigen3"
    "${OSS_PREFIX_PATH}/eigen3"
    "${OSS_PREFIX_PATH}/Eigen/eigen3"
    "${OSS_PREFIX_PATH}/eigen"
    "${OSS_PREFIX_PATH}/Eigen"
)

# 查找可用的Eigen安装
foreach(EIGEN_PATH ${EIGEN_POSSIBLE_PATHS})
    if(EXISTS "${EIGEN_PATH}")
        set(EIGEN_INCLUDE_DIRS "${EIGEN_PATH}")
        break()
    endif()
endforeach()

# 如果没有找到，尝试使用find_package
if(NOT DEFINED EIGEN_INCLUDE_DIRS)
    find_package(Eigen3 QUIET)
    if(Eigen3_FOUND)
        set(EIGEN_INCLUDE_DIRS ${EIGEN3_INCLUDE_DIRS})
    endif()
endif()

if(DEFINED EIGEN_INCLUDE_DIRS)
    include_directories(${EIGEN_INCLUDE_DIRS})
    message(STATUS "EIGEN_INCLUDE_DIRS: ${EIGEN_INCLUDE_DIRS}")
else()
    message(WARNING "Eigen库未找到，请确保Eigen已正确安装")
endif()