# Taskflow并行计算库配置
# 支持多种Taskflow安装路径

# 设置Taskflow可能的安装路径
set(TASKFLOW_POSSIBLE_PATHS
    "C:/opt/taskflow-3.10.0"
    "${OSS_PREFIX_PATH}/taskflow"
    "${OSS_PREFIX_PATH}/Taskflow"
    "${OSS_PREFIX_PATH}/taskflow-3.10.0"
)

# 查找可用的Taskflow安装
foreach(TASKFLOW_PATH ${TASKFLOW_POSSIBLE_PATHS})
    if(EXISTS "${TASKFLOW_PATH}")
        set(TASKFLOW_ROOT "${TASKFLOW_PATH}")
        set(TASKFLOW_INCLUDE_DIRS "${TASKFLOW_PATH}/include")
        break()
    endif()
endforeach()

# 如果没有找到，尝试使用find_package
if(NOT DEFINED TASKFLOW_INCLUDE_DIRS)
    find_package(Taskflow QUIET PATHS "C:/opt/taskflow-3.10.0/lib/cmake/Taskflow")
    if(Taskflow_FOUND)
        get_target_property(TASKFLOW_INCLUDE_DIRS Taskflow::Taskflow INTERFACE_INCLUDE_DIRECTORIES)
    endif()
endif()

if(DEFINED TASKFLOW_INCLUDE_DIRS)
    include_directories(${TASKFLOW_INCLUDE_DIRS})
    message(STATUS "TASKFLOW_INCLUDE_DIRS: ${TASKFLOW_INCLUDE_DIRS}")
    message(STATUS "TASKFLOW_ROOT: ${TASKFLOW_ROOT}")
else()
    message(WARNING "Taskflow库未找到，请确保Taskflow已正确安装")
endif()
