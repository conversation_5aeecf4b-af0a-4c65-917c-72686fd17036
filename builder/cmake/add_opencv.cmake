# OpenCV库配置
# 支持多种OpenCV安装路径

# 设置OpenCV可能的安装路径
set(OPENCV_POSSIBLE_ROOTS
    "${OSS_PREFIX_PATH}/opencv/build"
    "${OSS_PREFIX_PATH}/opencv"
    "${OSS_PREFIX_PATH}/OpenCV/build"
    "${OSS_PREFIX_PATH}/OpenCV"
)

# 查找可用的OpenCV安装
foreach(OPENCV_PATH ${OPENCV_POSSIBLE_ROOTS})
    if(EXISTS "${OPENCV_PATH}")
        set(OpenCV_ROOT "${OPENCV_PATH}")
        break()
    endif()
endforeach()

find_package(OpenCV REQUIRED)

if(OpenCV_FOUND)
    include_directories(${OpenCV_INCLUDE_DIRS})

    # 设置OpenCV库文件，支持Debug和Release版本
    set(opencv_LIBRARIES
        debug ${OSS_PREFIX_PATH}/opencv/build/x64/vc16/lib/opencv_world4110d.lib
        debug ${OSS_PREFIX_PATH}/opencv/build/x64/vc16/lib/opencv_img_hash4110d.lib
        optimized ${OSS_PREFIX_PATH}/opencv/build/x64/vc16/lib/opencv_world4110.lib
        optimized ${OSS_PREFIX_PATH}/opencv/build/x64/vc16/lib/opencv_img_hash4110.lib
    )

    message(STATUS "OpenCV_INCLUDE_DIRS: ${OpenCV_INCLUDE_DIRS}")
    message(STATUS "OpenCV_LIBRARY_DIRS: ${OpenCV_LIBRARY_DIRS}")
    message(STATUS "OpenCV_VERSION: ${OpenCV_VERSION}")
else()
    message(WARNING "OpenCV库未找到，请确保OpenCV已正确安装")
endif()