include(${CMAKE_CURRENT_LIST_DIR}/common.cmake)

if ("${SRCS_LENGTH}" GREATER 0)
    message(STATUS "创建可执行文件 ${PROJECT_NAME}")
    list(LENGTH QT_LIBS QT_LIBS_LEN)
    
    # 创建一个变量存储已处理的依赖库
    set(PROCESSED_LIBRARIES "")
    
    if (${QT_LIBS_LEN} GREATER 0)
        find_package(Qt5Core REQUIRED)

        ADD_DEFINITIONS("-DQT_DISABLE_DEPRECATED_BEFORE=0")
        ADD_DEFINITIONS("-DQT_NO_DEBUG")
        ADD_DEFINITIONS("-DQT_WIDGETS_LIB")
        ADD_DEFINITIONS("-DQT_GUI_LIB")
        ADD_DEFINITIONS("-DQT_CORE_LIB")
        set(CMAKE_AUTOMOC ON)
        set(CMAKE_AUTOUIC ON)

        file(GLOB_RECURSE QRC src/*.qrc)
        qt5_add_resources(QRCS ${QRC})
        if ("${INCS_LENGTH}" GREATER 0)
            add_executable(${PROJECT_NAME} ${GUI_TYPE} ${${PROJECT_NAME}_SRCS} ${${PROJECT_NAME}_INCS} ${QRCS})
        else ()
            add_executable(${PROJECT_NAME} ${GUI_TYPE} ${${PROJECT_NAME}_SRCS} ${QRCS})
        endif ()
        
        # 使用新函数添加依赖库
        add_unique_libraries(${PROJECT_NAME} PUBLIC ${${PROJECT_NAME}_DEP_LIBRARIES})
        
        foreach (_qt_lib IN ITEMS ${QT_LIBS})
            find_package(${_qt_lib} REQUIRED)
            include_directories(${${_qt_lib}_INCLUDE_DIRS})
            add_unique_libraries(${PROJECT_NAME} PUBLIC ${${_qt_lib}_LIBRARIES})
        endforeach ()

        if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/include)
            install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include
                    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}" COMPONENT include)
        endif ()

        # 添加Windows系统库依赖，确保使用正确的x64版本
        if(WIN32)
            # 确保链接器使用正确的x64库路径
            target_link_libraries(${PROJECT_NAME} PRIVATE
                ws2_32
                kernel32
                user32
                gdi32
                winspool
                shell32
                ole32
                oleaut32
                uuid
                comdlg32
                advapi32
            )

            # 设置链接器标志确保使用x64架构
            set_target_properties(${PROJECT_NAME} PROPERTIES
                LINK_FLAGS "/machine:x64"
            )
        endif()

        # Qt部署配置 - 仅对Qt应用程序
        include(${CMAKE_CURRENT_LIST_DIR}/qt_deploy.cmake)
        qt_deploy_runtime(${PROJECT_NAME})
    else ()
        if ("${INCS_LENGTH}" GREATER 0)
            add_executable(${PROJECT_NAME} ${GUI_TYPE} ${${PROJECT_NAME}_SRCS} ${${PROJECT_NAME}_INCS})
        else ()
            add_executable(${PROJECT_NAME} ${GUI_TYPE} ${${PROJECT_NAME}_SRCS})
        endif ()
        
        # 使用新函数添加依赖库
        add_unique_libraries(${PROJECT_NAME} PUBLIC ${${PROJECT_NAME}_DEP_LIBRARIES})

        if (EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/include)
            install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include
                    DESTINATION "${CMAKE_INSTALL_INCLUDEDIR}" COMPONENT include)
        endif ()
    endif ()
    
    # 确保所有依赖的头文件也能被包含
    target_include_directories(${PROJECT_NAME} PRIVATE ${${PROJECT_NAME}_DEP_INCLUDE_DIRS})
    
    foreach (_target IN ITEMS ${${PROJECT_NAME}_DEP_TARGETS})
        if (TARGET ${_target})
            add_dependencies(${PROJECT_NAME} ${_target})
        endif ()
    endforeach ()

endif ()
