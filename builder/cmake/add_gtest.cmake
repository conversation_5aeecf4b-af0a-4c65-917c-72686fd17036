# Google Test 测试框架配置
set(gtest_INCLUDE_DIRS C:/opt/gtest/include)

# 检查可用的gtest库文件
if(EXISTS "C:/opt/gtest/lib/Release/gtest.lib")
    set(gtest_LIBRARIES
        debug C:/opt/gtest/lib/Debug/gtest.lib
        optimized C:/opt/gtest/lib/Release/gtest.lib
    )
else()
    # 如果只有debug版本，则在所有配置下都使用debug版本
    set(gtest_LIBRARIES C:/opt/gtest/lib/Debug/gtest.lib)
    message(WARNING "Only debug version of gtest found, using Debug/gtest.lib for all configurations")
endif()

# 检查可用的gtest_main库文件
if(EXISTS "C:/opt/gtest/lib/Release/gtest_main.lib")
    set(gtest_main_LIBRARIES
        debug C:/opt/gtest/lib/Debug/gtest_main.lib
        optimized C:/opt/gtest/lib/Release/gtest_main.lib
    )
else()
    # 如果只有debug版本，则在所有配置下都使用debug版本
    set(gtest_main_LIBRARIES C:/opt/gtest/lib/Debug/gtest_main.lib)
    message(WARNING "Only debug version of gtest_main found, using Debug/gtest_main.lib for all configurations")
endif()

include_directories(${gtest_INCLUDE_DIRS})
message(STATUS "GTEST_INCLUDE_DIRS: ${gtest_INCLUDE_DIRS}")
message(STATUS "GTEST_LIBRARIES: ${gtest_LIBRARIES}")
message(STATUS "GTEST_MAIN_LIBRARIES: ${gtest_main_LIBRARIES}")
