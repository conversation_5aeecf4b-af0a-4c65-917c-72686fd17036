# Aubo ARCS机器人SDK配置
set(auboarcs_INCLUDE_DIRS ${OSS_PREFIX_PATH}/auboarcs/include)
set(auboarcs_LIBRARIES
    debug ${OSS_PREFIX_PATH}/auboarcs/lib/aubo_sdkd.lib
    debug ${OSS_PREFIX_PATH}/auboarcs/lib/robot_proxyd.lib
    optimized ${OSS_PREFIX_PATH}/auboarcs/lib/aubo_sdk.lib
    optimized ${OSS_PREFIX_PATH}/auboarcs/lib/robot_proxy.lib
)

include_directories(${auboarcs_INCLUDE_DIRS})
message(STATUS "auboarcs_INCLUDE_DIRS: ${auboarcs_INCLUDE_DIRS}")
message(STATUS "auboarcs_LIBRARIES: ${auboarcs_LIBRARIES}")