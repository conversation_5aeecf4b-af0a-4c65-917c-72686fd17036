#pragma once

#include <QMainWindow>
#include <QTimer>
#include <QLabel>
#include <QTableWidget>
#include <QPushButton>
#include <QGroupBox>
#include <QListWidget>
#include <QProcess>
#include <QThread>
#include <memory>
#include "Executor.h"
#include "control_system.h"
#include "system_status.h"
#include <atomic>
#include <map>
#include <mutex>
#include <chrono>
#include <QTabWidget>
#include <QDateTime>
#include <QCheckBox>

// 前向声明
class MainWindow;

// 在类定义之前声明元类型
Q_DECLARE_METATYPE(SystemStatus)

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onStartStopClicked();
    void onEmergencyStopClicked();
    void handleVirtualMesStateChanged(int state);

    void startVirtualMes();

    void stopVirtualMes();

    void handleVirtualMesError(QProcess::ProcessError error);
    void handleVirtualMesFinished(int exitCode, QProcess::ExitStatus exitStatus);

    void flushLogBuffer();

    void cleanupLogFiles();
private:
    void setupUI();
    void setupStatusCallbacks();
    void resetUIState();
    
    // 添加这些缺失的方法声明
    void createStatusGroup();
    void createHeartbeatGroup();
    void createTaskGroup();
    void createFeederGroup();
    void createAreaGroup();
    
    void updateStatus(const std::string& status);
    void updateSystemStatus(bool autoMode, bool error);
    void updateHeartbeatStatus(bool mesConnected, bool plcConnected);
    void updateTaskStatus(const std::shared_ptr<TaskStatus>& status);
    void updateFeederTable(const std::vector<std::shared_ptr<FeederStatus>>& feederStatus);
    void updateAreaTable(const std::map<char, std::shared_ptr<AreaData>>& areas);

    // UI组件
    QLabel *modeLabel;
    QLabel *errorLabel;
    QLabel *mesHeartbeatLabel;
    QLabel *plcHeartbeatLabel;
    QLabel *taskReadyLabel;
    QLabel *taskDumpingLabel;
    QLabel *taskCompletedLabel;
    QLabel *remainingWeightLabel;
    QTableWidget *feederTable;
    QTableWidget *areaTable;
    QPushButton *startStopButton;
    // QPushButton *emergencyStopButton;
    QListWidget *logList;
    
    QGroupBox *systemStatusGroup;
    QGroupBox *heartbeatGroup;
    QGroupBox *taskGroup;

    // 核心组件
    std::shared_ptr<ControlSystem> controlSystem;
    std::shared_ptr<Fuxi::Common::Executor> executor;

    QTimer* updateTimer;
    std::atomic<bool> needUpdate;
    
    struct UICache {
        bool autoMode{false};
        bool error{false};
        bool mesConnected{false};
        bool plcConnected{false};
        std::shared_ptr<TaskStatus> taskStatus;
        std::vector<std::shared_ptr<FeederStatus>> feederStatus;
        std::map<char, std::shared_ptr<AreaData>> areas;

        // 添加哈希值用于检测变化
        size_t feederHash{0};
        size_t areaHash{0};
        size_t taskHash{0};
        
        // 添加上次更新时间戳
        std::chrono::steady_clock::time_point lastUpdate;
        
        bool hasChanged() const {
            return std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now() - lastUpdate).count() >= 1000;
        }
    } uiCache;
    
    void updateUI();
    
    // 添加表格项缓存池
    class ItemPool {
    public:
        QTableWidgetItem* getItem() {
            if (items.empty()) {
                return new QTableWidgetItem();
            }
            auto item = items.back();
            items.pop_back();
            return item;
        }
        
        void recycleItem(QTableWidgetItem* item) {
            if (item) {
                item->setText("");
                item->setBackground(Qt::white);
                items.push_back(item);
            }
        }
        
    private:
        std::vector<QTableWidgetItem*> items;
    };
    
    ItemPool feederItemPool;
    ItemPool areaItemPool;
    
    // 添加节流控制
    std::chrono::steady_clock::time_point lastUpdateTime;
    static constexpr int MIN_UPDATE_INTERVAL = 1000; // ms

    // 添加新的UI组
    QTabWidget* tabWidget;
    QTableWidget* currentSandTypeTable;  // 当前料号表格
    QTableWidget* sandTypeHistoryTable;  // 料号变化历史表格
    
    // 添加新的方法
    void createCurrentSandTypeTab();
    void createSandTypeHistoryTab();
    void updateCurrentSandTypeTable(const std::vector<std::shared_ptr<FeederStatus>>& feederStatus);
    void updateSandTypeHistoryTable(const std::string& feederName, int binId, 
                                  const std::string& oldType, const std::string& newType);

    // 添加历史记录存储
    struct SandTypeChange {
        QDateTime timestamp;
        std::string feederName;
        int binId;
        std::string oldType;
        std::string newType;
    };
    std::vector<SandTypeChange> sandTypeHistory;
    static constexpr int MAX_HISTORY_ITEMS = 100;  // 最大历史记录数

    QCheckBox* virtualMesCheckBox;
    QProcess* virtualMesProcess;

    struct LogEntry {
        QString message;
        QString type;  // "系统" 或 "任务"
        QDateTime timestamp;
    };
    std::vector<LogEntry> logBuffer;
    std::mutex logMutex;
    QTimer* logUpdateTimer;
    static constexpr int LOG_UPDATE_INTERVAL = 500;  // 500ms更新一次
    QTimer *logCleanupTimer;

}; 
