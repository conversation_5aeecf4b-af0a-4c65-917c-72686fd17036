#pragma once

#include <modbus/modbus.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include "Executor.h"

#define MAX_CONNECTIONS 10  // 最大并发连接数

// 定义区域数据结构
struct AreaData {
    std::string sandType;  // 区砂类别
    struct Bucket {
        std::string batchNumber;  // 石英砂批号
        float weight;             // 重量
    };
    std::vector<Bucket> buckets;  // 4个桶的数据
};

// 定义投料机数据结构
struct FeederData {
    std::string changeTime;  // 更换生产图号时间
    std::vector<std::string> sandTypes;  // 6个料仓的砂类别
};

// 定义一个新的结构体来存储发送数据
struct SendData {
    std::string batchNumber;     // 机器人当次所取料砂批号
    std::string dumpTime;        // 当次倒料时间
    int feederNumber;            // 当次倒入投料机号码(1-6)
    int binNumber;               // 当次倒入料仓号(1-5)
    float dumpWeight;            // 当次倒入料砂重量
    float minWeight;             // 最小起加量
    float maxWeight;             // 最大加入量
    std::string taskBatchNumber;    // 任务批号
    int taskFeederNumber;           // 任务投料机
    int taskBinNumber;              // 任务料仓
};

class MesServer {
public:
    MesServer(const std::string& ip = "0.0.0.0", int port = 5020);
    ~MesServer();

    bool start();
    void stop();
    void run();

    void processModbusQuery(uint8_t *query, int rc);

    const std::map<char, std::shared_ptr<AreaData>>& getAreas() const {
        return areas;
    }
    
    void updateSendData(std::shared_ptr<SendData> data);
    bool isConnected() const { return m_connected; }
    bool isHeartbeatOK() const { return heartbeatOK; }

    std::vector<std::shared_ptr<FeederData>> getFeeders() {return feeders;};
private:
    void handleReceivedData(int address, uint16_t* data, int count);
    void heartbeatLoop();
    bool reconnectModbus();
    
    // 工具函数
    void parseStringFromRegisters(const uint16_t* regs, int count, std::string& result);
    void parseFloatFromRegisters(const uint16_t* regs, float& result);
    void stringToRegisters(const std::string& str, uint16_t* regs, int maxRegs);
    void floatToRegisters(float value, uint16_t* regs);

    modbus_t* ctx;
    std::shared_ptr<modbus_mapping_t> mbMapping;
    int serverSocket;
    bool running;
    
    std::vector<std::shared_ptr<FeederData>> feeders;
    std::map<char, std::shared_ptr<AreaData>> areas;
    
    bool heartbeatOK=false;
    bool m_connected=false;
    
    std::shared_ptr<Fuxi::Common::Executor> executor1;
    std::shared_ptr<Fuxi::Common::Executor> executor2;
    std::shared_ptr<Fuxi::Common::Executor> executor3;
    
    const int MAX_HEARTBEAT_FAILS = 3;
    std::atomic<int> heartbeatFailCount{0};
    
    bool initModbus();
    
    std::string _ip;
    int _port;
    
    // 添加心跳计时器
    struct HeartbeatTimer {
        std::atomic<std::chrono::steady_clock::time_point> lastHeartbeat;
        std::atomic<bool> heartbeatOK{false};
        static constexpr int HEARTBEAT_TIMEOUT_MS = 3000;
    } heartbeatTimer;
    
    // 添加连接状态检查方法
    bool checkConnection();
    
    struct RegisterData {
        int address;
        std::string dataType;  // "string", "float", "int"
        std::string value;
    };
    
    std::string csvFilePath = "modbus_registers.csv";
    void saveRegisterToCSV(const RegisterData& data);
    void loadRegistersFromCSV();
    void updateRegisterValue(const RegisterData& data);
    
public:
    // 优化心跳方法
    void updateHeartbeat();
}; 
