#pragma once

#include "abb_plc_client.h"
#include "mes_server.h"
#include <memory>
#include <functional>
#include "Executor.h"

// 添加前向声明
struct SystemStatus;

class ControlSystem {
public:
    // 修改构造函数,不再需要外部传入 modbus 上下文
    ControlSystem();
    ~ControlSystem();

    bool start();
    void stop();
    void emergencyStop();
    
    // 状态查询
    bool isAutoMode() const;
    bool hasError() const;
    bool isMesConnected() const;
    bool isPlcConnected() const;
    
    // 获取状态信息
    std::shared_ptr<TaskStatus> getTaskStatus() const;
    std::vector<std::shared_ptr<FeederStatus>> getFeederStatus() const;
    
    // 获取当前任务
    std::shared_ptr<TaskInfo> getCurrentTask() const;
    
    bool isRunning() const { return running; }
    const MesServer& getMesServer() const { return *mesServer; }
    bool isAreaEnabled(char areaId) const;
    
    // 添加状态回调相关
    using StatusCallback = std::function<void(const std::string&)>;
    void setStatusCallback(StatusCallback callback) { statusCallback = callback; }

    // 添加状态回调类型定义
    using SystemStatusCallback = std::function<void(const SystemStatus&)>;
    
    // 添加设置回调的方法
    void setSystemStatusCallback(SystemStatusCallback callback) { 
        systemStatusCallback = callback; 
    }

    // 添加新的回调类型定义
    using SystemModeCallback = std::function<void(bool autoMode, bool error)>;
    using HeartbeatCallback = std::function<void(bool mesConnected, bool plcConnected)>;
    using TaskStatusCallback = std::function<void(const std::shared_ptr<TaskStatus>& status)>;
    using FeederStatusCallback = std::function<void(const std::vector<std::shared_ptr<FeederStatus>>& status)>;
    using AreaStatusCallback = std::function<void(const std::map<char, std::shared_ptr<AreaData>>& areas)>;

    // 添加新的回调设置方法
    void setSystemModeCallback(SystemModeCallback callback) {
        systemModeCallback = callback;
    }
    
    void setHeartbeatCallback(HeartbeatCallback callback) {
        heartbeatCallback = callback;
    }
    
    void setTaskStatusCallback(TaskStatusCallback callback) {
        taskStatusCallback = callback;
    }
    
    void setFeederStatusCallback(FeederStatusCallback callback) {
        feederStatusCallback = callback;
    }
    
    void setAreaStatusCallback(AreaStatusCallback callback) {
        areaStatusCallback = callback;
    }

    // 任务日志回调
    using TaskLogCallback = std::function<void(const std::string& logMsg)>;
    void setTaskLogCallback(TaskLogCallback callback) {
        taskLogCallback = callback;
    }

private:
    void run();
    void notifyStatus(const std::string& status);
    
    std::shared_ptr<MesServer> mesServer;
    std::shared_ptr<ABBPLCClient> abbClient;
    std::shared_ptr<Fuxi::Common::Executor> executor;
    bool running{false};
    bool emergencyStopFlag{false};
    
    StatusCallback statusCallback;
    SystemStatusCallback systemStatusCallback;
    
    // 添加新的回调成员
    SystemModeCallback systemModeCallback;
    HeartbeatCallback heartbeatCallback;
    TaskStatusCallback taskStatusCallback;
    FeederStatusCallback feederStatusCallback;
    AreaStatusCallback areaStatusCallback;
    
    // 添加新的通知方法
    void notifySystemMode(bool autoMode, bool error) {
        if(systemModeCallback) {
            systemModeCallback(autoMode, error);
        }
    }
    
    void notifyHeartbeat(bool mesConnected, bool plcConnected) {
        if(heartbeatCallback) {
            heartbeatCallback(mesConnected, plcConnected);
        }
    }
    
    void notifyTaskStatus(const std::shared_ptr<TaskStatus>& status) {
        if(taskStatusCallback) {
            taskStatusCallback(status);
        }
    }
    
    void notifyFeederStatus(const std::vector<std::shared_ptr<FeederStatus>>& status) {
        if(feederStatusCallback) {
            feederStatusCallback(status);
        }
    }
    
    void notifyAreaStatus(const std::map<char, std::shared_ptr<AreaData>>& areas) {
        if(areaStatusCallback) {
            areaStatusCallback(areas);
        }
    }

    // 任务日志回调
    TaskLogCallback taskLogCallback;

    // 通知任务日志
    void notifyTaskLog(const std::string& logMsg) {
        if(taskLogCallback) {
            taskLogCallback(logMsg);
        }
    }
}; 
