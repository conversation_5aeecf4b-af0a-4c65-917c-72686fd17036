#pragma once

#include <string>
#include <filesystem>
#include <vector>
#include <chrono>
#include <memory>

class LogManager : public std::enable_shared_from_this<LogManager> {
public:
    static std::shared_ptr<LogManager> create() {
        return std::shared_ptr<LogManager>(new LogManager());
    }
    
    static void checkAndCleanLogs(const std::string& logPath, size_t maxLines = 1000);

private:
    LogManager() = default;  // 构造函数设为私有
    
    static std::vector<std::filesystem::path> getLogFiles(const std::string& logPath);
    static size_t countTotalLines(const std::vector<std::filesystem::path>& logFiles);
    static void removeOldestLogs(std::vector<std::filesystem::path>& logFiles);
}; 