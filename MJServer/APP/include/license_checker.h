#pragma once

#include <string>
#include <vector>
#include <memory>

class LicenseChecker : public std::enable_shared_from_this<LicenseChecker> {
public:
    static std::shared_ptr<LicenseChecker> create() {
        return std::shared_ptr<LicenseChecker>(new LicenseChecker());
    }
    
    static bool isLicensed();
    
private:
    LicenseChecker() = default;  // 构造函数设为私有
    
    static std::vector<std::string> getAllowedMacs();
    static std::vector<std::string> getSystemMacs();
    static bool checkMacAddress(const std::vector<std::string>& allowedMacs,
                              const std::vector<std::string>& systemMacs);
}; 