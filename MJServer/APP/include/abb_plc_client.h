#pragma once

#include "mes_server.h"
#include <string>
#include <memory>
#include <array>
#include <map>
#include <vector>
#include <ctime>
#include "Executor.h"
#include "modbusTcp.h"
#include <mutex>
#include <atomic>
#include <chrono>
#include <cmath>
#include <commonData.h>
#include <shared_mutex>
#include <glog.h>
#include <functional>

// 状态缓存结构
struct StatusCache {
    std::vector<std::shared_ptr<FeederStatus>> feederStatus;
    std::shared_ptr<TaskStatus> taskStatus;
    bool isAuto;
    bool hasError;
    std::chrono::steady_clock::time_point lastUpdate;
    
    bool isValid() const {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            now - lastUpdate).count() < 20000; // 500ms更新周期
    }
    
    void update() {
        lastUpdate = std::chrono::steady_clock::now();
    }
};

// 在 StatusCache 结构体后添加新的缓存结构
struct ModbusCache {
    std::vector<uint16_t> registers;  // 存储所有寄存器数据
    std::chrono::steady_clock::time_point lastUpdate;
    static constexpr int CACHE_VALID_MS = 3000;  // 缓存有效期100ms
    
    bool isValid() const {
        auto now = std::chrono::steady_clock::now();
        return std::chrono::duration_cast<std::chrono::milliseconds>(
            now - lastUpdate).count() < CACHE_VALID_MS;
    }
    
    void update() {
        lastUpdate = std::chrono::steady_clock::now();
    }
};

class ABBPLCClient : public std::enable_shared_from_this<ABBPLCClient> {
public:
    ABBPLCClient(const std::string& ip = "*************", int port = 5011);
    ~ABBPLCClient();
    
    bool start();
    void stop();
    
    void setMesServer(std::shared_ptr<MesServer> server);
    
    // 任务管理
    std::shared_ptr<TaskInfo> findNextTask();
    bool startTask(std::shared_ptr<TaskInfo> task);
    std::shared_ptr<TaskStatus> getTaskStatus() const;
    
    // 状态查询
    std::vector<std::shared_ptr<FeederStatus>> getFeederStatus();
    bool isSystemAuto() const;
    bool hasError() const;
    bool isAreaEnabled(char areaId) const;
    bool isFeederEnabled(int feederId) const;
    bool isBinEnabled(int feederId, int binId) const;
    bool isHeartbeatOK() const { return heartbeatOK; }

    bool reconnectModbus() const;
    int safeModbusRead(int addr, int nb, uint16_t* dest, int maxRetries = 3) const;
    int safeModbusWrite(int addr, int nb, const uint16_t* src, int maxRetries = 3) const;
    bool checkTaskConditions(std::shared_ptr<TaskInfo> task);

    const MesServer& getMesServer() const { 
        if (!mesServer) {
            throw std::runtime_error("MES server not initialized");
        }
        return *mesServer; 
    }

    // 写入区域物料类型
    bool writeAreaMaterialTypes();

    std::map<char, std::shared_ptr<AreaData>> getAllPLCAreaData();

    bool writeInt(int addr, int16_t value) const;
    bool writeString1(int addr, char value) const;

    // 任务日志回调函数类型
    using TaskLogCallback = std::function<void(const std::string& logMsg)>;

    // 设置任务日志回调
    void setTaskLogCallback(TaskLogCallback callback) {
        taskLogCallback = callback;
    }

private:
    void heartbeatLoop();
    void run();
    void writeAreaDataToPLC();
    void readTaskFromPLC();
    void writeTaskToABB(std::shared_ptr<TaskInfo> task);
    std::string readSandType(int feederId, int binId) const;
    void updateLocalCache();
    bool useLocalCache() const;
    bool initModbus();
    
    // 新增的优化方法
    bool readSystemStatus(bool& isAuto, bool& hasErr) const;

    // 通知任务日志
    void notifyTaskLog(const std::string& logMsg) const {
        if(taskLogCallback) {
            taskLogCallback(logMsg);
        }
    }

    std::unique_ptr<modbus> m_ctx;
    std::shared_ptr<MesServer> mesServer;
    
    bool running=false;
    bool heartbeatOK=false;
    
    std::shared_ptr<Fuxi::Common::Executor> executor1;
    std::shared_ptr<Fuxi::Common::Executor> executor2;
    
    // 缓存相关
    std::shared_ptr<StatusCache> statusCache;
    mutable std::shared_ptr<ModbusCache> modbusCache;
    mutable std::mutex cacheMutex;
    mutable std::shared_mutex modbusCacheMutex;
    mutable std::mutex modbusMutex;
    std::shared_ptr<TaskInfo> current_task;
    // 本地缓存结构
    struct LocalCache {
        std::map<char, std::shared_ptr<AreaData>> areas;
        std::vector<std::shared_ptr<FeederData>> feeders;
        time_t lastUpdateTime;
        
        void update(const std::map<char, std::shared_ptr<AreaData>>& newAreas) {
            areas = newAreas;
            lastUpdateTime = time(nullptr);
        }
        
        bool isValid() const {
            return (time(nullptr) - lastUpdateTime) < 3600;
        }
    };
    
    std::shared_ptr<LocalCache> localCache;
    
    // 系统常量定义
    const int AREAS_COUNT = 22;           // A-V,22个区域
    const int FEEDERS_COUNT = 6;          // 6个投料机
    const int BINS_PER_FEEDER = 6;        // 每个投料机6个料仓
    const int BUCKETS_PER_AREA = 4;       // 每个区域4个桶位
    const int MATERIAL_TYPES_COUNT = 23;   // 23种物料类型
    const int MATERIAL_TYPE_SIZE = 17;     // 每个物料类型17个寄存器
    const int AREA_DATA_SIZE = 68;         // 每个区域68个寄存器
    const int BUCKET_DATA_SIZE = 17;       // 每个桶17个寄存器(16个物料号+1个重量)
    const int WEIGHT_REGS_PER_BIN = 6;    // 每个料仓6个寄存器(3个float值*2)



        // PLC地址常量
    const int ADDR_HEARTBEAT = 0;            // MES通讯心跳
    const int ADDR_BATCH_NUMBER = 1;         // 机器人当次所取料砂批号(16个寄存器)
    const int ADDR_DUMP_TIME = 18;           // 当次倒料时间(10个寄存器)
    const int ADDR_FEEDER_NUMBER = 29;       // 当次倒入投料机号码
    const int ADDR_BIN_NUMBER = 30;          // 当次倒入上述投料机料仓号
    const int ADDR_DUMP_WEIGHT = 31;         // 当次倒入料砂重量(2个寄存器,float)

    // 区域数据相关地址
    const int ADDR_AREA_START = 33;          // 区域托盘物料号开始地址
                                            // 每个区域68个寄存器:
                                            // - 4个桶位
                                            // - 每个桶位17个寄存器(16个物料号+1个重量)

    // 区域桶数量地址
    const int ADDR_BUCKET_COUNT_START = 1529; // 区域桶数量开始地址(22个区域)

    // 区域桶重量地址
    const int ADDR_BUCKET_WEIGHT_START = 1551; // 区域桶重量开始地址
                                              // 每个区域8个寄存器(4个桶位*2个寄存器float)

    // 区域空桶状态地址
    const int ADDR_EMPTY_BUCKET_START = 1727; // 区域空桶状态开始地址(22个区域)

    // 设备状态地址
    const int ADDR_FEEDER_BLOCK_START = 1749; // 设备屏蔽状态开始地址
                                             // 每个设备7个寄存器(1个设备+6个料仓)
    const int ADDR_BIN_BLOCK_START = 1750;    // 料仓屏蔽状态开始地址
                                             // 紧跟在设备屏蔽状态后面
                                             // 每个设备6个料仓

    // ROTO相关地址
    const int ADDR_ROTO_POSITION = 1792;    // 取桶位
    const int ADDR_ROTO_FEEDER = 1793;      // 倒料设备
    const int ADDR_ROTO_BIN = 1794;         // 料口编号
    const int ADDR_ROTO_AREA = 1795;        // 取桶区域(高字节)
    const int ADDR_TASK_READY = 1799;       // 准备好倒料
    const int ADDR_TASK_DUMPING = 1800;     // 设备运行中
    const int ADDR_TASK_COMPLETED = 1801;   // 倒料完成
    const int ADDR_REMAINING_WEIGHT = 1802;  // 当前桶剩余重量(2个寄存器,float)

    // 设备料仓重量地址
    const int ADDR_CURRENT_WEIGHT_START = 1804; // 当前重量开始地址(6设备*6料仓*2寄存器)
    const int ADDR_START_WEIGHT_START = 1876;   // 起加重量开始地址(6设备*6料仓*2寄存器)
    const int ADDR_TARGET_WEIGHT_START = 1948;  // 目标重量开始地址(6设备*6料仓*2寄存器)

    // 系统状态地址
    const int ADDR_AUTO_MODE = 2020;         // 自动状态
    const int ADDR_ERROR_STATE = 2021;       // 设备故障

    // MES读取状态地址
    const int ADDR_MES_READ_START = 2022;    // MES读取状态开始地址(22个区域)

    // 区域启用状态地址
    const int ADDR_AREA_ENABLE_START = 2044; // 区域启用状态开始地址(22个区域)

    // 物料类型地址
    const int ADDR_MATERIAL_TYPE_START = 2066; // 物料类型开始地址
                                              // 23个物料类型*17个寄存器

    // 总寄存器数量
    static constexpr int TOTAL_REGISTERS = 2457; // 更新为正确的总数

    const int FEEDER_DATA_SIZE = 108;  // 更新为6个料仓的大小
    const int SANDTYPE_OFFSET = 10;
    const int SANDTYPE_SIZE = 16;

    std::string _ip;
    int _port;



    // 原 TaskManager 的成员变量
    float minWeight{-10.0f};
    float maxWeight{1000.0f};
    std::vector<std::shared_ptr<FeederStatus>> lastFeederStatus;

    // 原 TaskManager 的私有方法
    bool checkWeightConditions(float bucketWeight, 
                             float binCurrentWeight,
                             float binMinStartWeight, 
                             float binTargetWeight) const;
    bool checkMaterialMatch(const std::string& bucketBatchNumber,
                          const std::string& binSandType) const;

    std::string registersToString(const uint16_t *regs, int count) const;

    // 添加新的私有成员
    bool updateModbusCache() const;

    std::shared_ptr<AreaData> getPLCAreaData(char areaId);

private:
    // 添加跟踪当前料机和等待时间的成员变量
    int currentFeederId{0};  // 当前正在投料的料机ID (0表示未投料)
    std::map<int, std::chrono::steady_clock::time_point> feederWaitTimes;  // 记录各料机的等待时间
    
    // 更新料机等待时间
    void updateFeederWaitTime(int feederId);
    // 获取等待时间最长的料机
    int getLongestWaitingFeeder();

    static std::chrono::steady_clock::time_point lastUpdateTime;
    static std::vector<std::string> lastMaterials;

    // STRING 类型转换方法
    std::string parseString32(const uint16_t* regs) const {
        uint8_t maxLen = (regs[0] >> 8) & 0xFF;
        uint8_t actualLen = regs[0] & 0xFF;
        
        if (maxLen != 32 || actualLen > 32) {
            // LOG(ERROR) << "无效的STRING[32]长度: max=" << (int)maxLen
            //           << ", actual=" << (int)actualLen;
            return "";
        }

        std::string result;
        result.reserve(actualLen);
        
        for (size_t i = 0; i < actualLen; i++) {
            uint16_t reg = regs[1 + i/2];
            char ch = (i % 2 == 0) ? ((reg >> 8) & 0xFF) : (reg & 0xFF);
            if (ch != 0) {
                result += ch;
            }
        }
        return result;
    }

    std::string parseString1(const uint16_t* regs) const {
        uint8_t maxLen = (regs[0] >> 8) & 0xFF;
        uint8_t actualLen = regs[0] & 0xFF;
        
        if (maxLen != 1 || actualLen != 1) {
            LOG(ERROR) << "无效的STRING[1]长度: max=" << (int)maxLen 
                      << ", actual=" << (int)actualLen;
            return "";
        }

        char ch = (regs[1] >> 8) & 0xFF;
        return std::string(1, ch);
    }

    // STRING 类型打包方法
    void packString32(const std::string& str, uint16_t* regs) const {
        size_t str_len = std::min(str.length(), size_t(32));
        regs[0] = (32 << 8) | (str_len & 0xFF);

        memset(regs + 1, 0, 16 * sizeof(uint16_t));
        for (size_t i = 0; i < str_len; i++) {
            int reg_index = 1 + i/2;
            if (i % 2 == 0) {
                regs[reg_index] = (str[i] << 8);
            } else {
                regs[reg_index] |= (str[i] & 0xFF);
            }
        }
    }

    void packString1(char ch, uint16_t* regs) const {
        regs[0] = (1 << 8) | 1;
        regs[1] = (ch << 8);
    }

    // 数值类型转换方法
    float parseReal(const uint16_t* regs) const {
        uint32_t raw = ((uint32_t)regs[0] << 16) | regs[1];
        float result;
        memcpy(&result, &raw, sizeof(float));
        
        if (std::isnan(result) || std::isinf(result) ||
            result > maxWeight || result < minWeight) {
            result = 0.0f;
        }
        return result;
    }

    void packReal(float value, uint16_t* regs) const {
        uint32_t raw;
        memcpy(&raw, &value, sizeof(float));
        regs[0] = (raw >> 16) & 0xFFFF;
        regs[1] = raw & 0xFFFF;
    }

    int16_t parseInt(const uint16_t* reg) const {
        return static_cast<int16_t>(*reg);
    }

    void packInt(int16_t value, uint16_t* reg) const {
        *reg = static_cast<uint16_t>(value);
    }

    TaskLogCallback taskLogCallback;

};
