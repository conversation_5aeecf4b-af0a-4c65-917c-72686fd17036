//
// Created by xu on 2024/11/29.
//

#ifndef COMMONDATA_H
#define COMMONDATA_H

#include <array>
#include <optional>
#include <memory>
#include <string>
#include <vector>



// 料仓状态
struct BinStatus {
    float currentWeight;      // 当前重量
    float minStartWeight;     // 起加重量(下限值)
    float targetWeight;       // 目标加入量
    bool isBlocked;          // 是否屏蔽
    std::string sandType;    // 砂类型
};

// 投料机状态
struct FeederStatus {
    bool isBlocked;                  // 是否屏蔽
    std::array<BinStatus, 5> bins;   // 5个料仓状态
    std::string changeTime;  // 更换生产图号时间
    std::vector<std::string> sandTypes;  // 5个料仓的砂类别
};

// 任务状态
struct TaskStatus {
    bool isReady;           // 是否准备好 (地址1813)
    bool isDumping;         // 是否在倒料 (地址1814)
    bool isCompleted;       // 是否完成 (地址1815)
    float remainingWeight;  // 当前桶剩余重量 (地址1816)
};

// 任务信息
struct TaskInfo {
    int feederId;           // 投料机号(1-6)
    int binId;             // 料仓号(1-5)
    char areaId;           // 取料区域(A-V)
    int bucketId;          // 桶号(1-4)
    std::string batchNumber; // 批号
    float weight;          // 重量
    float minWeight;             // 最小起加量
    float maxWeight;             // 最大加入量
};






#endif //COMMONDATA_H
