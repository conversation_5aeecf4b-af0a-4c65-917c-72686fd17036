#pragma once

#include "commonData.h"
#include <map>
#include <memory>
#include <vector>

struct SystemStatus {
    bool systemAutoMode{false};
    bool systemError{false}; 
    bool mesConnected{false};
    bool plcConnected{false};
    std::shared_ptr<TaskStatus> taskStatus;
    std::vector<std::shared_ptr<FeederStatus>> feederStatus;
    std::map<char, std::shared_ptr<AreaData>> areas;
    
    std::map<char, bool> areaEnabled;  // 区域启用状态
    bool systemRunning{false};         // 设备启动状态
}; 