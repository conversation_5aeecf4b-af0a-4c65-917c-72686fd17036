#include "main_window.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QHeaderView>
#include <QDateTime>
#include <QMessageBox>
#include <iomanip>
#include <QApplication>
#include <QStatusBar>
#include <QProcess>
#include <glog.h>
#include <QFile>
#include <mutex>
#include <QDir>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent),
      modeLabel(new QLabel(this)),
      errorLabel(new QLabel(this)),
      mesHeartbeatLabel(new QLabel(this)),
      plcHeartbeatLabel(new QLabel(this)),
      taskReadyLabel(new QLabel(this)),
      taskDumpingLabel(new QLabel(this)),
      taskCompletedLabel(new QLabel(this)),
      remainingWeightLabel(new QLabel(this)),
      feederTable(new QTableWidget(this)),
      areaTable(new QTableWidget(this)),
      startStopButton(new QPushButton("启动", this)),
      // emergencyStopButton(new QPushButton("紧急停止", this)),
      logList(new QListWidget(this)),
      virtualMesCheckBox(new QCheckBox("启用虚拟MES", this)),
      virtualMesProcess(nullptr) {
    // 初始化UI组件
    setupUI();

    // 创建执行器
    executor = std::make_shared<Fuxi::Common::Executor>(10);

    // 创建并配置更新定时器
    updateTimer = new QTimer(this);
    updateTimer->setInterval(100); // 100ms更新一次
    connect(updateTimer, &QTimer::timeout, this, &MainWindow::updateUI);
    updateTimer->start();

    // 初始化日志更新定时器
    logUpdateTimer = new QTimer(this);
    logUpdateTimer->setInterval(LOG_UPDATE_INTERVAL);
    connect(logUpdateTimer, &QTimer::timeout, this, &MainWindow::flushLogBuffer);
    logUpdateTimer->start();

    // 添加日志清理定时器
    logCleanupTimer = new QTimer(this);
    logCleanupTimer->setInterval(3600000); // 每小时检查一次
    connect(logCleanupTimer, &QTimer::timeout, this, &MainWindow::cleanupLogFiles);
    logCleanupTimer->start();

    // 初始运行时也执行一次清理
    cleanupLogFiles();
}

MainWindow::~MainWindow() {
    exit(100);
    if (controlSystem) {
        controlSystem->stop();
        controlSystem.reset();
    }

    // 停止定时器
    updateTimer->stop();
    delete updateTimer;

    // 清理表格项
    for (int i = 0; i < feederTable->rowCount(); ++i) {
        for (int j = 0; j < feederTable->columnCount(); ++j) {
            delete feederTable->item(i, j);
        }
    }

    for (int i = 0; i < areaTable->rowCount(); ++i) {
        for (int j = 0; j < areaTable->columnCount(); ++j) {
            delete areaTable->item(i, j);
        }
    }

    stopVirtualMes();
    delete virtualMesProcess;

    logUpdateTimer->stop();
    delete logUpdateTimer;

    logUpdateTimer->stop();
    delete logUpdateTimer;

    logCleanupTimer->stop();
    delete logCleanupTimer;
}

void MainWindow::cleanupLogFiles() {
    QString logPath = "C:\\fuxios\\install\\x64-install\\dev\\data\\";
    QDir logDir(logPath);

    // 获取所有日志文件并按修改时间排序
    QFileInfoList files = logDir.entryInfoList(
        QStringList() << "*.log" << "*.INFO" << "*.WARNING" << "*.ERROR",
        QDir::Files | QDir::NoDotAndDotDot,
        QDir::Time);

    // 如果文件数量超过100个，删除最旧的文件
    if (files.size() > 100) {
        for (int i = 100; i < files.size(); ++i) {
            QFile file(files[i].absoluteFilePath());
            if (file.remove()) {
                LOG(INFO) << "已删除旧日志文件: " << files[i].fileName().toStdString();
            } else {
                LOG(WARNING) << "删除日志文件失败: " << files[i].fileName().toStdString();
            }
        }
    }
}

void MainWindow::setupUI() {
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);

    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);

    // 添加标题和图标
    QHBoxLayout *titleLayout = new QHBoxLayout();

    // 添加左侧图标
    QLabel *leftIconLabel = new QLabel(this);
    QPixmap leftPixmap("C:\\fuxios\\install\\x64-install\\dev\\data\\image\\image1.png");
    leftIconLabel->setPixmap(leftPixmap.scaled(156, 156, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    titleLayout->addWidget(leftIconLabel);

    // 添加右侧图标
    QLabel *rightIconLabel = new QLabel(this);
    QPixmap rightPixmap("C:\\fuxios\\install\\x64-install\\dev\\data\\image\\image2.png");
    rightIconLabel->setPixmap(rightPixmap.scaled(128, 128, Qt::KeepAspectRatio, Qt::SmoothTransformation));
    titleLayout->addWidget(rightIconLabel);

    mainLayout->addLayout(titleLayout);

    // 添加标题
    QLabel *titleLabel = new QLabel("MJG石英砂上料机调度系统", this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(16);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLayout->addWidget(titleLabel, 1, Qt::AlignCenter);


    // 创各个组件
    createStatusGroup();
    createHeartbeatGroup();
    createTaskGroup();
    createFeederGroup();
    createAreaGroup();

    // 添加到主布局
    QHBoxLayout *topLayout = new QHBoxLayout();
    topLayout->addWidget(systemStatusGroup);
    topLayout->addWidget(heartbeatGroup);
    topLayout->addWidget(taskGroup);
    mainLayout->addLayout(topLayout);

    // mainLayout->addWidget(taskGroup);
    mainLayout->addWidget(feederTable);
    mainLayout->addWidget(areaTable);

    // 添加日志组
    QGroupBox *logGroup = new QGroupBox("系统日志", this);
    logGroup->setFixedHeight(100);
    QVBoxLayout *logLayout = new QVBoxLayout(logGroup);
    logList = new QListWidget(this);
    logLayout->addWidget(logList);
    mainLayout->addWidget(logGroup);

    // 添加打开日志文件夹按钮
    QPushButton *openLogButton = new QPushButton("打开日志文件夹", this);

    // 在按钮布局之前添加虚拟MES选项
    mainLayout->addWidget(virtualMesCheckBox);
    connect(virtualMesCheckBox, &QCheckBox::stateChanged,
            this, &MainWindow::handleVirtualMesStateChanged);

    // 修改按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->addWidget(virtualMesCheckBox); // 添加复选框
    buttonLayout->addWidget(startStopButton);
    buttonLayout->addWidget(openLogButton);
    mainLayout->addLayout(buttonLayout);

    // 创建标签页控件
    tabWidget = new QTabWidget(this);

    // 创建原有内容的容器widget
    QWidget *mainWidget = new QWidget();
    mainWidget->setLayout(mainLayout);

    // 添加原有页面
    tabWidget->addTab(mainWidget, "系统状态");

    // 创建新的页面
    createCurrentSandTypeTab();
    createSandTypeHistoryTab();

    // 设置中心部件
    setCentralWidget(tabWidget);

    // 连接信号
    connect(startStopButton, &QPushButton::clicked, this, &MainWindow::onStartStopClicked);
    // connect(emergencyStopButton, &QPushButton::clicked, this, &MainWindow::onEmergencyStopClicked);
    connect(openLogButton, &QPushButton::clicked, this, [this]() {
        QString logPath = "C:\\fuxios\\install\\x64-install\\dev\\data\\";
#ifdef _WIN32
        QProcess::startDetached("explorer", QStringList() << logPath);
#else
        QProcess::startDetached("xdg-open", QStringList() << logPath);
#endif
    });


    // 设置窗口属性
    setWindowTitle("MJG石英砂上料机调度系统");
    showMaximized(); // 使用这行代替 showMaximized()
}

void MainWindow::createStatusGroup() {
    systemStatusGroup = new QGroupBox("系统状态", this);
    QVBoxLayout *layout = new QVBoxLayout(systemStatusGroup);

    layout->addWidget(modeLabel);
    layout->addWidget(errorLabel);
}

void MainWindow::createHeartbeatGroup() {
    heartbeatGroup = new QGroupBox("心跳状态", this);
    QVBoxLayout *layout = new QVBoxLayout(heartbeatGroup);

    layout->addWidget(mesHeartbeatLabel);
    layout->addWidget(plcHeartbeatLabel);
}

void MainWindow::createTaskGroup() {
    taskGroup = new QGroupBox("任务状态", this);
    QGridLayout *layout = new QGridLayout(taskGroup);

    layout->addWidget(taskReadyLabel, 0, 0);
    layout->addWidget(taskDumpingLabel, 0, 1);
    layout->addWidget(taskCompletedLabel, 1, 0);
    layout->addWidget(remainingWeightLabel, 1, 1);
}

void MainWindow::createFeederGroup() {
    feederTable = new QTableWidget(this);
    feederTable->setColumnCount(8);  // 修改为8列（1列投料机编号 + 1列状态 + 6列料仓）
    feederTable->setRowCount(6);

    QStringList headers;
    headers << "投料机" << "状态" << "料仓1" << "料仓2" << "料仓3" << "料仓4" << "料仓5" << "料仓6";  // 添加料仓6
    feederTable->setHorizontalHeaderLabels(headers);

    feederTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    feederTable->verticalHeader()->setVisible(false);

    feederTable->setWordWrap(true); // 启用自动换行
    feederTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents); // 自动整行高
}

void MainWindow::createAreaGroup() {
    areaTable = new QTableWidget(this);
    areaTable->setColumnCount(6);
    areaTable->setRowCount(22); // A-V，22个区域

    QStringList headers;
    headers << "区域" << "状态" << "桶1" << "桶2" << "桶3" << "桶4";
    areaTable->setHorizontalHeaderLabels(headers);

    QStringList rowLabels;
    for (int c = 1; c <= 22; c++) {
        rowLabels << QString::number(c);
    }
    areaTable->setVerticalHeaderLabels(rowLabels);

    areaTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    areaTable->setWordWrap(true); // 启用自动换行
    areaTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents); // 自动调整行高
}

void MainWindow::setupStatusCallbacks() {
    if (!controlSystem) return;

    controlSystem->setSystemModeCallback(
        [this](bool autoMode, bool error) {
            {
                uiCache.autoMode = autoMode;
                uiCache.error = error;
            }
            needUpdate = true;
        }
    );

    controlSystem->setFeederStatusCallback(
        [this](const std::vector<std::shared_ptr<FeederStatus> > &status) {
            {
                uiCache.feederStatus = status;
            }
            needUpdate = true;
        }
    );

    controlSystem->setHeartbeatCallback(
        [this](bool mesConnected, bool plcConnected) {
            {
                uiCache.mesConnected = mesConnected;
                uiCache.plcConnected = plcConnected;
            }
            needUpdate = true;
        }
    );

    controlSystem->setTaskStatusCallback(
        [this](const std::shared_ptr<TaskStatus> &status) {
            {
                uiCache.taskStatus = status;
            }
            needUpdate = true;
        }
    );

    controlSystem->setFeederStatusCallback(
        [this](const std::vector<std::shared_ptr<FeederStatus> > &status) {
            {
                uiCache.feederStatus = status;
            }
            needUpdate = true;
        }
    );

    controlSystem->setAreaStatusCallback(
        [this](const std::map<char, std::shared_ptr<AreaData> > &areas) {
            {
                uiCache.areas = areas;
            }
            needUpdate = true;
        }
    );

    // 修改任务日志回调，将任务日志整合到系统日志中
    controlSystem->setTaskLogCallback([this](const std::string &logMsg) {
        LogEntry entry{
            QString::fromStdString(logMsg),
            "任务",
            QDateTime::currentDateTime()
        };

        {
            std::lock_guard<std::mutex> lock(logMutex);
            logBuffer.push_back(entry);
        }
    });
}

void MainWindow::onStartStopClicked() {
    executor->postTask([this]() {
        try {
            if (controlSystem && controlSystem->isRunning()) {
                // 停止系统时也停止虚拟MES
                stopVirtualMes();
                controlSystem->stop();
                controlSystem.reset(); // 销毁控制系统

                QMetaObject::invokeMethod(this, [this]() {
                    startStopButton->setText("启动");
                    updateStatus("系统已停止");
                    resetUIState(); // 重置UI状态
                }, Qt::AutoConnection);
            } else {
                // 创建新的制系统实例
                controlSystem = std::make_shared<ControlSystem>();
                // 设置状态回调
                setupStatusCallbacks();


                if (controlSystem->start()) {
                    // 如果虚拟MES被选中，则启动它
                    if (virtualMesCheckBox->isChecked()) {
                        QMetaObject::invokeMethod(this, [this]() {
                            startVirtualMes();
                        }, Qt::QueuedConnection);
                    }

                    QMetaObject::invokeMethod(this, [this]() {
                        startStopButton->setText("停止");
                        startStopButton->hide();
                        virtualMesCheckBox->hide();

                        updateStatus("系统已启动");
                    }, Qt::AutoConnection);
                } else {
                    controlSystem.reset(); // 启动失败时销毁实例
                    QMetaObject::invokeMethod(this, [this]() {
                        QMessageBox::warning(this, "警告", "系统启动失败");
                        updateStatus("系统启动失败");
                        resetUIState(); // 重置UI状态
                    }, Qt::AutoConnection);
                }
            }
        } catch (const std::exception &e) {
            controlSystem.reset(); // 发生异常时销毁实例
            QMetaObject::invokeMethod(this, [this, error = std::string(e.what())]() {
                QMessageBox::critical(this, "错误", QString::fromStdString(error));
                updateStatus("操作失败: " + error);
                resetUIState(); // 重置UI状态
            }, Qt::AutoConnection);
        }
    });
}

void MainWindow::onEmergencyStopClicked() {
    executor->postTask([this]() {
        try {
            if (controlSystem) {
                controlSystem->emergencyStop();
                controlSystem.reset(); // 销毁控制系统
            }
            QMetaObject::invokeMethod(this, [this]() {
                updateStatus("系统紧急停止");
                QMessageBox::warning(this, "警告", "系统已紧急停止");
                startStopButton->setText("启动");
                resetUIState(); // 重置UI状态
            }, Qt::AutoConnection);
        } catch (const std::exception &e) {
            QMetaObject::invokeMethod(this, [this, error = std::string(e.what())]() {
                QMessageBox::critical(this, "错误", QString::fromStdString(error));
                updateStatus("紧急停止失败: " + error);
            }, Qt::AutoConnection);
        }
    });
}

void MainWindow::updateStatus(const std::string &status) {
    // 创建日志条目
    LogEntry entry{
        QString::fromStdString(status),
        "系统",
        QDateTime::currentDateTime()
    };

    // 添加到缓冲区
    {
        std::lock_guard<std::mutex> lock(logMutex);
        logBuffer.push_back(entry);
    }

    // 输出到glog
    LOG(INFO) << status;
}

void MainWindow::updateSystemStatus(bool autoMode, bool error) {
    if (QThread::currentThread() != QApplication::instance()->thread()) {
        QMetaObject::invokeMethod(this, [this, autoMode, error]() {
            updateSystemStatus(autoMode, error);
        }, Qt::AutoConnection);
        return;
    }

    modeLabel->setText(QString("自动模式: %1").arg(autoMode ? "开启" : "关闭"));
    modeLabel->setStyleSheet(autoMode ? "color: green;" : "color: gray;");

    errorLabel->setText(QString("系统错误: %1").arg(error ? "是" : "否"));
    errorLabel->setStyleSheet(error ? "color: red;" : "color: green;");
}

void MainWindow::updateHeartbeatStatus(bool mesConnected, bool plcConnected) {
    if (QThread::currentThread() != QApplication::instance()->thread()) {
        QMetaObject::invokeMethod(this, [this, mesConnected, plcConnected]() {
            updateHeartbeatStatus(mesConnected, plcConnected);
        }, Qt::AutoConnection);
        return;
    }

    mesHeartbeatLabel->setText(QString("MES心跳: %1").arg(mesConnected ? "正常" : "断开"));
    mesHeartbeatLabel->setStyleSheet(mesConnected ? "color: green;" : "color: red;");

    plcHeartbeatLabel->setText(QString("PLC心跳: %1").arg(plcConnected ? "正常" : "断开"));
    plcHeartbeatLabel->setStyleSheet(plcConnected ? "color: green;" : "color: red;");
}

void MainWindow::updateTaskStatus(const std::shared_ptr<TaskStatus> &status) {
    if (QThread::currentThread() != QApplication::instance()->thread()) {
        QMetaObject::invokeMethod(this, [this, status]() {
            updateTaskStatus(status);
        }, Qt::AutoConnection);
        return;
    }

    if (!status) return;

    taskReadyLabel->setText(QString("准备状态: %1").arg(
        status->isReady ? "就绪" : "未就绪"));
    taskDumpingLabel->setText(QString("倒料状态: %1").arg(
        status->isDumping ? "倒料中" : "空闲"));
    taskCompletedLabel->setText(QString("完成状态: %1").arg(
        status->isCompleted ? "已完成" : "未完成"));
    remainingWeightLabel->setText(QString("剩余重量: %1kg").arg(
        QString::number(status->remainingWeight, 'f', 2)));
}

void MainWindow::resetUIState() {
    // 重置系统状态
    updateSystemStatus(false, false);

    // 重置心跳状态
    updateHeartbeatStatus(false, false);

    // 重置任务态
    taskReadyLabel->setText("准备状态: 未就绪");
    taskDumpingLabel->setText("倒料状态: 空闲");
    taskCompletedLabel->setText("完成状态: 未完成");
    remainingWeightLabel->setText("剩余重量: 0.00kg");

    // 清空投料机表格
    for (int i = 0; i < feederTable->rowCount(); i++) {
        for (int j = 0; j < feederTable->columnCount(); j++) {
            if (auto item = feederTable->item(i, j)) {
                item->setText("");
                item->setBackground(Qt::white);
            }
        }
    }

    // 清空区域表格
    for (int i = 0; i < areaTable->rowCount(); i++) {
        for (int j = 0; j < areaTable->columnCount(); j++) {
            if (auto item = areaTable->item(i, j)) {
                item->setText("");
                item->setBackground(Qt::white);
            }
        }
    }
}

void MainWindow::updateFeederTable(const std::vector<std::shared_ptr<FeederStatus> > &feederStatus) {
    // 暂停表格更新
    feederTable->setUpdatesEnabled(false);

    // 回收所有现有的表格项
    for (int i = 0; i < feederTable->rowCount(); ++i) {
        for (int j = 0; j < feederTable->columnCount(); ++j) {
            if (auto item = feederTable->takeItem(i, j)) {
                feederItemPool.recycleItem(item);
            }
        }
    }

    // 使用缓存池中的项更新表格
    for (size_t i = 0; i < feederStatus.size() && i < 6; ++i) {
        const auto &feeder = feederStatus[i];
        if (!feeder) continue;

        // 获取并设置表格项
        auto numItem = feederItemPool.getItem();
        numItem->setText(QString::number(i + 1));
        numItem->setTextAlignment(Qt::AlignCenter);
        feederTable->setItem(i, 0, numItem);

        auto statusItem = new QTableWidgetItem(feeder->isBlocked ? "已屏蔽" : "正常");
        statusItem->setBackground(feeder->isBlocked ? Qt::red : Qt::green);
        statusItem->setTextAlignment(Qt::AlignCenter);
        feederTable->setItem(i, 1, statusItem);

        for (size_t j = 0; j < feeder->bins.size() && j < 6; ++j) {  // 修改为j < 6
            const auto &bin = feeder->bins[j];
            QString binStatus = QString("重量: %1kg\n类型: %2")
                    .arg(bin.currentWeight, 0, 'f', 2)
                    .arg(QString::fromStdString(bin.sandType));

            auto binItem = new QTableWidgetItem(binStatus);
            binItem->setBackground(bin.isBlocked
                                       ? Qt::red
                                       : (bin.currentWeight < bin.minStartWeight ? Qt::yellow : Qt::white));
            binItem->setTextAlignment(Qt::AlignCenter);
            feederTable->setItem(i, j + 2, binItem);
        }
    }

    // 恢复表格更新
    feederTable->setUpdatesEnabled(true);
}

void MainWindow::updateAreaTable(const std::map<char, std::shared_ptr<AreaData>>& areas) {
    areaTable->setUpdatesEnabled(false);

    // 回收现有项
    for (int i = 0; i < areaTable->rowCount(); ++i) {
        for (int j = 0; j < areaTable->columnCount(); ++j) {
            if (auto item = areaTable->takeItem(i, j)) {
                areaItemPool.recycleItem(item);
            }
        }
    }

    // 使用缓存池中的项更新表格
    int row = 0;
    for (char areaId = 'A'; areaId <= 'V'; areaId++, row++) {
        // 区域编号
        auto idItem = areaItemPool.getItem();
        idItem->setText(QString::number(row+1));
        idItem->setTextAlignment(Qt::AlignCenter);
        areaTable->setItem(row, 0, idItem);

        // 区域状态
        bool enabled = controlSystem->isAreaEnabled(areaId);
        auto statusItem = new QTableWidgetItem(enabled ? "启用" : "禁用");
        statusItem->setBackground(enabled ? Qt::green : Qt::red);
        statusItem->setTextAlignment(Qt::AlignCenter);
        areaTable->setItem(row, 1, statusItem);

        // 桶信息
        auto it = areas.find(areaId);
        if (it != areas.end() && it->second) {
            const auto& area = it->second;
            
            // 确保桶的数量正确
            size_t numBuckets = std::min(area->buckets.size(), size_t(4));
            
            // 按顺序显示每个桶的信息
            for (size_t i = 0; i < numBuckets; i++) {
                const auto& bucket = area->buckets[i];
                
                // 处理料号字符串
                QString batchNumber;
                if (!bucket.batchNumber.empty()) {
                    std::string cleanBatchNumber;
                    for (char c : bucket.batchNumber) {
                        if (c >= 32 && c <= 126) { // 只保留可打印ASCII字符
                            cleanBatchNumber += c;
                        }
                    }
                    batchNumber = QString::fromUtf8(cleanBatchNumber.c_str());
                } else {
                    batchNumber = "无";
                }

                // 创建桶信息显示字符串
                QString bucketInfo = QString("桶%1\n批号: %2\n重量: %3kg")
                    .arg(i + 1)  // 添加桶号显示
                    .arg(batchNumber)
                    .arg(bucket.weight, 0, 'f', 2);

                auto bucketItem = new QTableWidgetItem(bucketInfo);
                bucketItem->setBackground(bucket.weight > 0 ? Qt::cyan : Qt::white);
                bucketItem->setTextAlignment(Qt::AlignCenter);
                
                // 确保桶的显示位置正确
                areaTable->setItem(row, i + 2, bucketItem);
                
                // LOG(INFO) << "区域 " << areaId << " 桶" << (i + 1)
                //          << " 显示位置=" << (i + 2)
                //          << " 批号=" << bucket.batchNumber
                //          << " 重量=" << bucket.weight;
            }

            // 清空未使用的桶位置
            for (size_t i = numBuckets; i < 4; i++) {
                auto emptyItem = new QTableWidgetItem("空");
                emptyItem->setBackground(Qt::white);
                emptyItem->setTextAlignment(Qt::AlignCenter);
                areaTable->setItem(row, i + 2, emptyItem);
            }
        }
    }

    areaTable->setUpdatesEnabled(true);
}

void MainWindow::updateUI() {
    if (!needUpdate) {
        VLOG(2) << "无需更新UI";
        return;
    }

    // LOG(INFO) << "开始更新UI";

    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - lastUpdateTime).count();
    if (elapsed < MIN_UPDATE_INTERVAL) {
        return;
    }
    lastUpdateTime = now;

    if (!uiCache.hasChanged()) {
        return;
    }

    // 更新系统状态
    updateSystemStatus(uiCache.autoMode, uiCache.error);

    // 更新心跳状态
    updateHeartbeatStatus(uiCache.mesConnected, uiCache.plcConnected);

    // 更新任务状态
    if (uiCache.taskStatus) {
        size_t newTaskHash = 0;
        newTaskHash ^= std::hash<bool>()(uiCache.taskStatus->isReady);
        newTaskHash ^= std::hash<bool>()(uiCache.taskStatus->isDumping);
        newTaskHash ^= std::hash<bool>()(uiCache.taskStatus->isCompleted);
        newTaskHash ^= std::hash<float>()(uiCache.taskStatus->remainingWeight);

        // if (newTaskHash != uiCache.taskHash) {
        updateTaskStatus(uiCache.taskStatus);
        uiCache.taskHash = newTaskHash;
        // }
    }

    // 更新投料机状态
    size_t newFeederHash = 0;
    if (!uiCache.feederStatus.empty()) {
        for (const auto &feeder: uiCache.feederStatus) {
            if (feeder) {
                newFeederHash ^= std::hash<bool>()(feeder->isBlocked);
                for (const auto &bin: feeder->bins) {
                    newFeederHash ^= std::hash<float>()(bin.currentWeight);
                    newFeederHash ^= std::hash<std::string>()(bin.sandType);
                }
            }
        }

        // if (newFeederHash != uiCache.feederHash) {
        updateFeederTable(uiCache.feederStatus);
        updateCurrentSandTypeTable(uiCache.feederStatus); // 添加这行
        uiCache.feederHash = newFeederHash;
        // }
    }

    // 更新区域状态
    size_t newAreaHash = 0;
    if (!uiCache.areas.empty()) {
        for (const auto &[areaId, area]: uiCache.areas) {
            if (area) {
                newAreaHash ^= std::hash<char>()(areaId);
                for (const auto &bucket: area->buckets) {
                    newAreaHash ^= std::hash<std::string>()(bucket.batchNumber);
                    newAreaHash ^= std::hash<float>()(bucket.weight);
                }
            }
        }

        // if (newAreaHash != uiCache.areaHash) {
        updateAreaTable(uiCache.areas);
        uiCache.areaHash = newAreaHash;
        // }
    }

    uiCache.lastUpdate = now;
    // LOG(INFO) << "UI更新完成";
    needUpdate = false;
}


void MainWindow::updateCurrentSandTypeTable(const std::vector<std::shared_ptr<FeederStatus> > &feederStatus) {
    try {
        for (size_t i = 0; i < feederStatus.size() && i < 6; i++) {
            const auto &feeder = feederStatus.at(i);
            if (!feeder) continue;

            for (int j = 0; j < 6; j++) {  // 修改为j < 6
                QString sandType = QString::fromStdString(feeder->sandTypes.at(j));

                // 检查是否需要更新历史记录
                QTableWidgetItem *currentItem = currentSandTypeTable->item(i, j);
                if (currentItem) {
                    QString oldType = currentItem->text();
                    if (oldType != sandType && !oldType.isEmpty()) {
                        updateSandTypeHistoryTable(
                            QString("投料机%1").arg(i + 1).toStdString(),
                            j + 1,
                            oldType.toStdString(),
                            sandType.toStdString()
                        );
                    } else if (oldType != sandType && oldType.isEmpty()) {
                        updateSandTypeHistoryTable(
                            QString("投料机%1").arg(i + 1).toStdString(),
                            j + 1,
                            oldType.toStdString(),
                            sandType.toStdString()
                        );
                    }
                }

                // 更新当前料号表格
                auto *item = new QTableWidgetItem(sandType);
                item->setTextAlignment(Qt::AlignCenter);
                currentSandTypeTable->setItem(i, j, item);
            }
        }
    }catch (const std::exception &e) {
        LOG(ERROR) << "Exception in updateCurrentSandTypeTable: " << e.what();
    }
}

void MainWindow::updateSandTypeHistoryTable(const std::string &feederName, int binId,
                                            const std::string &oldType, const std::string &newType) {
    // 添加新记录
    SandTypeChange change{
        QDateTime::currentDateTime(),
        feederName,
        binId,
        oldType,
        newType
    };
    sandTypeHistory.insert(sandTypeHistory.begin(), change);

    // 限制历史记录数量
    if (sandTypeHistory.size() > MAX_HISTORY_ITEMS) {
        sandTypeHistory.pop_back();
    }

    // 更新表格显示
    sandTypeHistoryTable->setRowCount(sandTypeHistory.size());

    for (size_t i = 0; i < sandTypeHistory.size(); i++) {
        const auto &record = sandTypeHistory[i];

        sandTypeHistoryTable->setItem(i, 0, new QTableWidgetItem(
                                          record.timestamp.toString("yyyy-MM-dd hh:mm:ss")));
        sandTypeHistoryTable->setItem(i, 1, new QTableWidgetItem(
                                          QString::fromStdString(record.feederName)));
        sandTypeHistoryTable->setItem(i, 2, new QTableWidgetItem(
                                          QString::number(record.binId)));
        sandTypeHistoryTable->setItem(i, 3, new QTableWidgetItem(
                                          QString::fromStdString(record.oldType)));
        sandTypeHistoryTable->setItem(i, 4, new QTableWidgetItem(
                                          QString::fromStdString(record.newType)));
    }
}

void MainWindow::createSandTypeHistoryTab() {
    sandTypeHistoryTable = new QTableWidget(this);
    sandTypeHistoryTable->setColumnCount(5);

    // 设置表头
    QStringList headers;
    headers << "时间" << "投料机" << "料口" << "原类型" << "新类型";
    sandTypeHistoryTable->setHorizontalHeaderLabels(headers);

    // 调整表格样式
    sandTypeHistoryTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    tabWidget->addTab(sandTypeHistoryTable, "类型变化记录");
}

void MainWindow::createCurrentSandTypeTab() {
    currentSandTypeTable = new QTableWidget(this);
    currentSandTypeTable->setColumnCount(6); // 修改为6个料口
    currentSandTypeTable->setRowCount(6); // 6个投料机

    // 设置表头
    QStringList headers;
    headers << "料口1" << "料口2" << "料口3" << "料口4" << "料口5" << "料口6";  // 添加料口6
    currentSandTypeTable->setHorizontalHeaderLabels(headers);

    QStringList rowLabels;
    for (int i = 1; i <= 6; i++) {
        rowLabels << QString("投料机%1").arg(i);
    }
    currentSandTypeTable->setVerticalHeaderLabels(rowLabels);

    // 调整表格样式
    currentSandTypeTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    currentSandTypeTable->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    tabWidget->addTab(currentSandTypeTable, "当前类型");
}

// 添加新的槽函数实现
void MainWindow::handleVirtualMesStateChanged(int state) {
    if (state == Qt::Checked) {
        if (!virtualMesProcess) {
            virtualMesProcess = new QProcess(this);

            // 连接信号
            connect(virtualMesProcess, &QProcess::errorOccurred,
                    this, &MainWindow::handleVirtualMesError);
            connect(virtualMesProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                    this, &MainWindow::handleVirtualMesFinished);
        }

        if (controlSystem && controlSystem->isRunning()) {
            startVirtualMes();
        }
    } else {
        stopVirtualMes();
    }
}

void MainWindow::startVirtualMes() {
    if (!virtualMesProcess) return;

    QString program = QCoreApplication::applicationDirPath() + "/MJServertest_mesClientall";

    if (!QFile::exists(program)) {
        QMessageBox::warning(this, "错误", "找不到虚拟MES程序");
        virtualMesCheckBox->setChecked(false);
        return;
    }

    virtualMesProcess->start(program);
    if (!virtualMesProcess->waitForStarted()) {
        QMessageBox::warning(this, "错误", "启动虚拟MES失败");
        virtualMesCheckBox->setChecked(false);
    } else {
        updateStatus("虚拟MES已启动");
    }
}

void MainWindow::stopVirtualMes() {
    if (virtualMesProcess && virtualMesProcess->state() == QProcess::Running) {
        virtualMesProcess->terminate();
        if (!virtualMesProcess->waitForFinished(3000)) {
            virtualMesProcess->kill();
        }
        updateStatus("虚拟MES已停止");
    }
}

void MainWindow::handleVirtualMesError(QProcess::ProcessError error) {
    QString errorMsg = "虚拟MES错误: ";
    switch (error) {
        case QProcess::FailedToStart:
            errorMsg += "启动失败";
            break;
        case QProcess::Crashed:
            errorMsg += "程序崩溃";
            break;
        default:
            errorMsg += "未知错误";
    }
    updateStatus(errorMsg.toStdString());
}

void MainWindow::handleVirtualMesFinished(int exitCode, QProcess::ExitStatus exitStatus) {
    if (exitStatus == QProcess::CrashExit) {
        updateStatus("虚拟MES异常退出");
    } else {
        updateStatus("虚拟MES已退出，退出码: " + std::to_string(exitCode));
    }
}

void MainWindow::flushLogBuffer() {
    std::vector<LogEntry> entries;
    {
        std::lock_guard<std::mutex> lock(logMutex);
        if (logBuffer.empty()) {
            return;
        }
        entries.swap(logBuffer);
    }

    // 批量更新UI
    logList->setUpdatesEnabled(false);

    // 限制日志条目数量
    const int MAX_LOG_ENTRIES = 1000;
    while (logList->count() + entries.size() > MAX_LOG_ENTRIES) {
        delete logList->takeItem(0);
    }

    // 添加新的日志条目
    for (const auto& entry : entries) {
        QString logEntry = QString("[%1] [%2] %3")
            .arg(entry.timestamp.toString("yyyy-MM-dd hh:mm:ss"))
            .arg(entry.type)
            .arg(entry.message);
        logList->addItem(logEntry);
    }

    logList->scrollToBottom();
    logList->setUpdatesEnabled(true);
}
