#include "mes_server.h"
#include <cstring>
#include <glog.h>
#include <fstream>
#include <sstream>
#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#define CLOSE_SOCKET closesocket
#define GET_SOCKET_ERROR() WSAGetLastError()
#else
#include <unistd.h>
#include <sys/socket.h>
#include <errno.h>
#define CLOSE_SOCKET close
#define GET_SOCKET_ERROR() errno
#endif

MesServer::MesServer(const std::string &ip, int port) : _ip(ip),
                                                        _port(port),
                                                        serverSocket(-1),
                                                        running(false)
{
#ifdef _WIN32
    // Initialize Winsock on Windows
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        throw std::runtime_error("WSAStartup failed: " + std::to_string(result));
    }
#endif

    if (!initModbus())
    {
        throw std::runtime_error("Modbus initialization failed");
    }

    executor1 = std::make_shared<Fuxi::Common::Executor>(1);
    executor2 = std::make_shared<Fuxi::Common::Executor>(10);
    executor3 = std::make_shared<Fuxi::Common::Executor>(1);

    // 初始化区域数据
    for (char c = 'A'; c <= 'V'; c++)
    {
        areas[c] = std::make_shared<AreaData>();
        areas[c]->buckets.resize(4);
    }

    // 初始化投料机数据
    feeders.resize(6);
    for (auto &feeder : feeders)
    {
        feeder = std::make_shared<FeederData>();
        feeder->sandTypes.resize(6);
    }

    // 加载保存的寄存器数据
    loadRegistersFromCSV();

    LOG(INFO) << "MES服务器初始化完成";
}

MesServer::~MesServer()
{
    stop();
#ifdef _WIN32
    // Cleanup Winsock on Windows
    WSACleanup();
#endif
    // 不再释放 ctx,由外部管理
}

bool MesServer::start()
{
    serverSocket = modbus_tcp_listen(ctx, MAX_CONNECTIONS);
    if (serverSocket == -1)
    {
        LOG(ERROR) << "MES服务器监听失败: " << modbus_strerror(errno);
        return false;
    }

    // 设置非阻塞模式
    modbus_set_socket(ctx, -1);

    running = true;

    // 启动心跳检测
    executor1->postTask([this]()
                        { heartbeatLoop(); });

    // 启动主循环
    executor2->postTask([this]()
                        { run(); });

    LOG(INFO) << "MES服务器启动成功";
    return true;
}

void MesServer::stop()
{
    running = false;
    m_connected = false;

    if (serverSocket != -1)
    {
        CLOSE_SOCKET(serverSocket);
        serverSocket = -1;
    }

    LOG(INFO) << "MES服务器已停止";
}

void MesServer::run()
{
    uint8_t query[MODBUS_TCP_MAX_ADU_LENGTH];
    fd_set refset;
    fd_set rdset;
    int fdmax;

    // 初始化文件描述符集合
    FD_ZERO(&refset);
    FD_SET(serverSocket, &refset);
    fdmax = serverSocket;

    while (running)
    {
        try
        {
            if (!ctx)
            {
                LOG(ERROR) << "Modbus context is null";
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            rdset = refset;
            struct timeval timeout = {10, 0}; // 1秒超时

            if (select(fdmax + 1, &rdset, NULL, NULL, &timeout) == -1)
            {
#ifdef _WIN32
                LOG(ERROR) << "Select failed: " << WSAGetLastError();
#else
                LOG(ERROR) << "Select failed: " << strerror(errno);
#endif
                continue;
            }

            // 检查新连接
            if (FD_ISSET(serverSocket, &rdset))
            {
                int new_socket = modbus_tcp_accept(ctx, &serverSocket);
                if (new_socket != -1)
                {
                    FD_SET(new_socket, &refset);
                    if (new_socket > fdmax)
                    {
                        fdmax = new_socket;
                    }
                    // LOG(INFO) << "新客户端连接";
                    m_connected = true;
                }
            }

            // 处理现有连接
            for (int master_socket = serverSocket + 1; master_socket <= fdmax; master_socket++)
            {
                if (!FD_ISSET(master_socket, &rdset))
                    continue;

                modbus_set_socket(ctx, master_socket);
                int rc = modbus_receive(ctx, query);

                if (rc > 0)
                {
                    processModbusQuery(query, rc);
                }
                else
                {
                    // 连接关闭或错误
                    FD_CLR(master_socket, &refset);
                    CLOSE_SOCKET(master_socket);
                    // LOG(INFO) << "客户端断开连接";
                    // m_connected = false;
                }
            }
        }
        catch (const std::exception &e)
        {
            // LOG(ERROR) << "MES服务器运行异常: " << e.what();
            // m_connected = false;
            std::this_thread::sleep_for(std::chrono::milliseconds(300));
        }
    }
}

void MesServer::processModbusQuery(uint8_t *query, int rc)
{
    int function = query[7];
    int address = (query[8] << 8) | query[9];

    if (function == 0x10)
    { // 写多个寄存器
        int count = (query[10] << 8) | query[11];
        std::vector<uint16_t> data(count);

        for (int i = 0; i < count; i++)
        {
            data[i] = (query[13 + i * 2] << 8) | query[14 + i * 2];
        }

        handleReceivedData(address, data.data(), count);
        // LOG(INFO) << "处理写入请求: 地址=" << address << " 数量=" << count;
    }

    if (modbus_reply(ctx, query, rc, mbMapping.get()) == -1)
    {
        LOG(ERROR) << "Reply failed: " << modbus_strerror(errno);
    }
}

void MesServer::heartbeatLoop()
{
    while (running)
    {
        try
        {
            if (!ctx || !mbMapping)
            {
                LOG(ERROR) << "Modbus context or mapping is null";
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            // 更新心跳变量(如果等于0则置为1)
            if (mbMapping->tab_registers[0] == 0 || mbMapping->tab_registers[0] == 1)
            {
                heartbeatOK = true;
                m_connected = true;
            }

            VLOG(2) << "MES心跳正常";
        }
        catch (const std::exception &e)
        {
            LOG(ERROR) << "MES心跳异常: " << e.what();
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

bool MesServer::reconnectModbus()
{
    if (!ctx)
        return false;

    modbus_close(ctx);
    serverSocket = modbus_tcp_listen(ctx, 1);
    if (serverSocket == -1)
    {
        LOG(ERROR) << "MES服务器重连失败: " << modbus_strerror(errno);
        return false;
    }
    LOG(INFO) << "MES服务器重连成功";
    return true;
}

void MesServer::handleReceivedData(int address, uint16_t *data, int count)
{
    // 处理区域数据
    if (address >= 12288 && address < 14288)
    {
        int areaOffset = address - 12288;
        char areaId = 'A' + (areaOffset / 88);
        int fieldOffset = areaOffset % 88;

        if (areaId >= 'A' && areaId <= 'V')
        {
            auto &area = areas[areaId];

            if (fieldOffset == 1)
            {
                // 更新区砂类别
                std::string sandType;
                parseStringFromRegisters(data, count, sandType);
                area->sandType = sandType;
                // 保存到CSV
                saveRegisterToCSV({address, "string", sandType});
                LOG(INFO) << "区域 " << areaId << " 砂类别更新为: " << sandType;
            }
            else
            {
                // 处理批号和重量数据
                int dataOffset = fieldOffset - 17;
                int position = dataOffset / 18;

                if (position >= 0 && position < 4)
                {
                    int itemOffset = dataOffset % 18;
                    if (itemOffset == 0)
                    {
                        // 更新批号
                        std::string batchNumber;
                        parseStringFromRegisters(data, count, batchNumber);
                        area->buckets[position].batchNumber = batchNumber;
                        // 保存到CSV
                        saveRegisterToCSV({address, "string", batchNumber});
                        LOG(INFO) << "区域 " << areaId << " 位置 " << position + 1 
                                 << " 批号更新为: " << batchNumber;
                    }
                    else if (itemOffset == 16)
                    {
                        // 更新重量
                        float weight;
                        parseFloatFromRegisters(data, weight);
                        area->buckets[position].weight = weight;
                        // 保存到CSV
                        saveRegisterToCSV({address, "float", std::to_string(weight)});
                        LOG(INFO) << "区域 " << areaId << " 位置 " << position + 1 
                                 << " 重量更新为: " << weight << "kg";
                    }
                }
            }
        }
    }

    // 处理投料机数据 (1-5号料仓)
    else if (address >= 14288 && address < 14828)
    {
        int feederOffset = address - 14288;
        int feederId = feederOffset / 90;
        int fieldOffset = feederOffset % 90;

        if (feederId >= 0 && feederId < 6)
        {
            auto &feeder = feeders[feederId];

            if (fieldOffset == 0)
            {
                // 更新更换时间
                std::string changeTime;
                parseStringFromRegisters(data, count, changeTime);
                feeder->changeTime = changeTime;
                // 保存到CSV
                saveRegisterToCSV({address, "string", changeTime});
                LOG(INFO) << "投料机 " << feederId + 1 << " 更换时间更新为: " << changeTime;
            }
            else if (fieldOffset >= 10)
            {
                // 更新砂类别 (1-5号料仓)
                int sandTypeIndex = (fieldOffset - 10) / 16;
                if (sandTypeIndex >= 0 && sandTypeIndex < 5)
                {
                    std::string sandType;
                    parseStringFromRegisters(data, count, sandType);
                    feeder->sandTypes[sandTypeIndex] = sandType;
                    // 保存到CSV
                    saveRegisterToCSV({address, "string", sandType});
                    LOG(INFO) << "投料机 " << feederId + 1 << " 料仓 " << sandTypeIndex + 1 
                             << " 砂类别更新为: " << sandType;
                }
            }
        }
    }
    // 处理投料机 6号料仓数据 (连续存放在地址14828-14924)
    else if (address >= 14828 && address < 14924)
    {
        // 计算是哪个投料机的6号料仓
        int index = (address - 14828) / 16;
        if (index >= 0 && index < 6)
        {
            int feederId = index;
            std::string sandType;
            parseStringFromRegisters(data, count, sandType);
            feeders[feederId]->sandTypes[5] = sandType;
            // 保存到CSV
            saveRegisterToCSV({address, "string", sandType});
            LOG(INFO) << "投料机 " << feederId + 1 << " 料仓 6 砂类别更新为: " << sandType;
        }
    }
}

void MesServer::updateSendData(std::shared_ptr<SendData> data)
{
    // 更新心跳变量(0和1之间交替)
    static bool heartbeatToggle = false;
    heartbeatToggle = !heartbeatToggle;
    mbMapping->tab_registers[16288] = heartbeatToggle ? 1 : 0;

    // 更新机器人当次所取料砂批号 (String[32])
    stringToRegisters(data->batchNumber, mbMapping->tab_registers + 16289, 32);

    // 更新当次倒料时间 (String[20])
    stringToRegisters(data->dumpTime, mbMapping->tab_registers + 16305, 20);

    // 更新当次倒入投料机号码(1-6)
    mbMapping->tab_registers[16315] = data->feederNumber;

    // 更新当次倒入料仓号(1-5)
    mbMapping->tab_registers[16316] = data->binNumber;

    // 更新当次倒入料砂重量
    floatToRegisters(data->dumpWeight, mbMapping->tab_registers + 16317);

    // 更新最小起加量
    floatToRegisters(data->minWeight, mbMapping->tab_registers + 16319);

    // 更新最大加入量
    floatToRegisters(data->maxWeight, mbMapping->tab_registers + 16321);

    // 更新任务批号 (String[32])
    stringToRegisters(data->taskBatchNumber, mbMapping->tab_registers + 16323, 32);

    // 更新任务投料机
    mbMapping->tab_registers[16339] = data->taskFeederNumber;

    // 更新任务料仓
    mbMapping->tab_registers[16340] = data->taskBinNumber;

    // 添加验证日志
    LOG(INFO) << "发送数据验证:";
    LOG(INFO) << "批号: " << data->batchNumber;
    LOG(INFO) << "倒料时间: " << data->dumpTime;
    LOG(INFO) << "投料机号: " << data->feederNumber;
    LOG(INFO) << "料仓号: " << data->binNumber;
    LOG(INFO) << "倒入重量: " << data->dumpWeight;
    LOG(INFO) << "最小起加量: " << data->minWeight;
    LOG(INFO) << "最大加入量: " << data->maxWeight;
    LOG(INFO) << "任务批号: " << data->taskBatchNumber;
    LOG(INFO) << "任务投料机: " << data->taskFeederNumber;
    LOG(INFO) << "任务料仓: " << data->taskBinNumber;

    // 验证写入是否成功
    std::string verifyBatchNumber;
    std::string verifyDumpTime;
    std::string verifyTaskBatchNumber;

    // 分别验证每个字段，使用正确的偏移量和长度
    parseStringFromRegisters(mbMapping->tab_registers + 16289, 16, verifyBatchNumber); // 批号验证使用16个寄存器
    parseStringFromRegisters(mbMapping->tab_registers + 16305, 10, verifyDumpTime);    // 时间验证使用10个寄存器
    parseStringFromRegisters(mbMapping->tab_registers + 16323, 16, verifyTaskBatchNumber);

    float verifyDumpWeight, verifyMinWeight, verifyMaxWeight;
    parseFloatFromRegisters(mbMapping->tab_registers + 16317, verifyDumpWeight);
    parseFloatFromRegisters(mbMapping->tab_registers + 16319, verifyMinWeight);
    parseFloatFromRegisters(mbMapping->tab_registers + 16321, verifyMaxWeight);

    // 添加详细的验证日志
    bool verificationPassed = true;
    std::stringstream errorMsg;

    if (verifyBatchNumber != data->batchNumber)
    {
        verificationPassed = false;
        errorMsg << "批号不匹配: 期望='" << data->batchNumber << "' 实际='" << verifyBatchNumber << "'\n";
    }
    if (verifyDumpTime != data->dumpTime)
    {
        verificationPassed = false;
        errorMsg << "时间不匹配: 期望='" << data->dumpTime << "' 实际='" << verifyDumpTime << "'\n";
    }
    if (mbMapping->tab_registers[16315] != data->feederNumber)
    {
        verificationPassed = false;
        errorMsg << "投料机号不匹配: 期望=" << data->feederNumber << " 实际=" << mbMapping->tab_registers[16315] << "\n";
    }
    if (mbMapping->tab_registers[16316] != data->binNumber)
    {
        verificationPassed = false;
        errorMsg << "料仓号不匹配: 期望=" << data->binNumber << " 实际=" << mbMapping->tab_registers[16316] << "\n";
    }
    if (std::abs(verifyDumpWeight - data->dumpWeight) > 0.001f)
    {
        verificationPassed = false;
        errorMsg << "倒入重量不匹配: 期望=" << data->dumpWeight << " 实际=" << verifyDumpWeight << "\n";
    }
    if (std::abs(verifyMinWeight - data->minWeight) > 0.001f)
    {
        verificationPassed = false;
        errorMsg << "最小起加量不匹配: 期望=" << data->minWeight << " 实际=" << verifyMinWeight << "\n";
    }
    if (std::abs(verifyMaxWeight - data->maxWeight) > 0.001f)
    {
        verificationPassed = false;
        errorMsg << "最大加入量不匹配: 期望=" << data->maxWeight << " 实际=" << verifyMaxWeight << "\n";
    }
    if (verifyTaskBatchNumber != data->taskBatchNumber)
    {
        verificationPassed = false;
        errorMsg << "任务批号不匹配: 期望='" << data->taskBatchNumber << "' 实际='" << verifyTaskBatchNumber << "'\n";
    }
    if (mbMapping->tab_registers[16339] != data->taskFeederNumber)
    {
        verificationPassed = false;
        errorMsg << "任务投料机不匹配: 期望=" << data->taskFeederNumber << " 实际=" << mbMapping->tab_registers[16339] << "\n";
    }
    if (mbMapping->tab_registers[16340] != data->taskBinNumber)
    {
        verificationPassed = false;
        errorMsg << "任务料仓不匹配: 期望=" << data->taskBinNumber << " 实际=" << mbMapping->tab_registers[16340] << "\n";
    }

    if (!verificationPassed)
    {
        LOG(ERROR) << "数据写入验证失败！\n"
                   << errorMsg.str();
    }
    else
    {
        LOG(INFO) << "数据写入验证成功";
    }
}

// 工具函数实现...
void MesServer::parseStringFromRegisters(const uint16_t *regs, int count, std::string &result)
{
    result.clear();
    result.reserve(count * 2);

    std::string temp;
    for (int i = 0; i < count; i++)
    {
        char low = regs[i] & 0xFF;
        char high = (regs[i] >> 8) & 0xFF;

        if (low != 0)
            temp += low;
        if (high != 0)
            temp += high;
    }

    // 移除尾部空格
    while (!temp.empty() && temp.back() == ' ')
    {
        temp.pop_back();
    }

    result = temp;
    VLOG(2) << "Parsed string: '" << result << "' (length: " << result.length() << ")";
}

void MesServer::parseFloatFromRegisters(const uint16_t *regs, float &result)
{
    uint32_t bits = (uint32_t)regs[1] << 16 | regs[0];
    memcpy(&result, &bits, sizeof(float));
}
//todo 去掉空格填充
void MesServer::stringToRegisters(const std::string &str, uint16_t *regs, int maxRegs)
{
    // 清空所有寄存器
    memset(regs, 0, maxRegs * sizeof(uint16_t));

    // 处理字符串，不使用空格填充
    std::string processedStr = str;
    size_t maxLength = maxRegs * 2;

    // 移除尾部空格
    while (!processedStr.empty() && processedStr.back() == ' ')
    {
        processedStr.pop_back();
    }

    // 如果字符串超过最大长度，则截断
    if (processedStr.length() > maxLength)
    {
        processedStr = processedStr.substr(0, maxLength);
    }

    VLOG(2) << "Writing string: '" << str << "' -> '" << processedStr
            << "' (length: " << processedStr.length() << "/" << maxLength << ")";

    // 每两个字符组成一个寄存器
    for (size_t i = 0; i < processedStr.length(); i += 2)
    {
        uint16_t word = (uint8_t)processedStr[i];
        if (i + 1 < processedStr.length())
        {
            word |= ((uint16_t)(uint8_t)processedStr[i + 1] << 8);
        }
        regs[i / 2] = word;
        VLOG(3) << "Register " << i / 2 << ": 0x" << std::hex << word << std::dec;
    }
}

void MesServer::floatToRegisters(float value, uint16_t *regs)
{
    uint32_t bits;
    memcpy(&bits, &value, sizeof(float));
    regs[0] = bits & 0xFFFF;
    regs[1] = bits >> 16;
}

bool MesServer::initModbus()
{
    ctx = modbus_new_tcp(_ip.c_str(), _port);
    if (!ctx)
    {
        LOG(ERROR) << "Failed to create modbus context";
        return false;
    }

    mbMapping = std::shared_ptr<modbus_mapping_t>(
        modbus_mapping_new(0, 0, 20000, 0),
        modbus_mapping_free);

    if (!mbMapping)
    {
        LOG(ERROR) << "Failed to create modbus mapping";
        modbus_free(ctx);
        ctx = nullptr;
        return false;
    }

    return true;
}

void MesServer::saveRegisterToCSV(const RegisterData& data)
{
    // 使用executor3异步执行保存操作
    executor3->postTask([this, data]() {
        std::vector<RegisterData> registers;
        bool found = false;
        
        // 读取现有数据
        std::ifstream inFile(csvFilePath);
        if (inFile.is_open()) {
            std::string line;
            while (std::getline(inFile, line)) {
                std::stringstream ss(line);
                std::string addressStr, dataType, value;
                
                std::getline(ss, addressStr, ',');
                std::getline(ss, dataType, ',');
                std::getline(ss, value, ',');
                
                int address = std::stoi(addressStr);
                
                // 如果找到相同地址的寄存器，更新其值
                if (address == data.address) {
                    registers.push_back(data);
                    found = true;
                } else {
                    registers.push_back({address, dataType, value});
                }
            }
            inFile.close();
        }
        
        // 如果没有找到相同地址的寄存器，添加新的
        if (!found) {
            registers.push_back(data);
        }
        
        // 写回文件
        std::ofstream outFile(csvFilePath);
        if (!outFile.is_open()) {
            LOG(ERROR) << "无法打开CSV文件进行写入: " << csvFilePath;
            return;
        }
        
        // 按地址排序
        std::sort(registers.begin(), registers.end(), 
            [](const RegisterData& a, const RegisterData& b) {
                return a.address < b.address;
            });
        
        // 写入所有数据
        for (const auto& reg : registers) {
            outFile << reg.address << "," << reg.dataType << "," << reg.value << "\n";
        }
        outFile.close();
        
        VLOG(2) << "已" << (found ? "更新" : "添加") << "寄存器数据 - 地址: " 
                << data.address << ", 类型: " << data.dataType << ", 值: " << data.value;
    });
}

void MesServer::loadRegistersFromCSV()
{
    std::ifstream file(csvFilePath);
    if (!file.is_open()) {
        LOG(INFO) << "未找到寄存器数据文件，将创建新文件";
        return;
    }

    std::string line;
    while (std::getline(file, line)) {
        std::stringstream ss(line);
        std::string addressStr, dataType, value;
        
        // 解析CSV行
        std::getline(ss, addressStr, ',');
        std::getline(ss, dataType, ',');
        std::getline(ss, value, ',');
        
        int address = std::stoi(addressStr);
        
        // 处理区域数据 (12288-14287)
        if (address >= 12288 && address < 14288) {
            int areaOffset = address - 12288;
            char areaId = 'A' + (areaOffset / 88);
            int fieldOffset = areaOffset % 88;

            if (areaId >= 'A' && areaId <= 'V') {
                auto &area = areas[areaId];
                
                if (fieldOffset == 1) {
                    // 区砂类别
                    area->sandType = value;
                } else {
                    // 处理批号和重量数据
                    int dataOffset = fieldOffset - 17;
                    int position = dataOffset / 18;

                    if (position >= 0 && position < 4) {
                        int itemOffset = dataOffset % 18;
                        if (itemOffset == 0) {
                            // 批号
                            area->buckets[position].batchNumber = value;
                        } else if (itemOffset == 16 && dataType == "float") {
                            // 重量
                            area->buckets[position].weight = std::stof(value);
                        }
                    }
                }
            }
        }
        // 处理投料机数据 (1-5号料仓) (14288-14827)
        else if (address >= 14288 && address < 14828) {
            int feederOffset = address - 14288;
            int feederId = feederOffset / 90;
            int fieldOffset = feederOffset % 90;

            if (feederId >= 0 && feederId < 6) {
                auto &feeder = feeders[feederId];
                
                if (fieldOffset == 0) {
                    // 更换时间
                    feeder->changeTime = value;
                } else if (fieldOffset >= 10) {
                    // 砂类别 (1-5号料仓)
                    int sandTypeIndex = (fieldOffset - 10) / 16;
                    if (sandTypeIndex >= 0 && sandTypeIndex < 5) {
                        feeder->sandTypes[sandTypeIndex] = value;
                    }
                }
            }
        }
        // 处理投料机 6号料仓数据 (14828-14923)
        else if (address >= 14828 && address < 14924) {
            // 计算是哪个投料机的6号料仓
            int index = (address - 14828) / 16;
            if (index >= 0 && index < 6) {
                int feederId = index;
                feeders[feederId]->sandTypes[5] = value;
            }
        }

        // 更新Modbus寄存器
        updateRegisterValue({address, dataType, value});
    }
    
    file.close();
    LOG(INFO) << "已从CSV文件加载寄存器数据";
}

void MesServer::updateRegisterValue(const RegisterData& data)
{
    if (!mbMapping) {
        LOG(ERROR) << "Modbus mapping is null";
        return;
    }

    try {
        if (data.dataType == "string") {
            // 字符串类型数据写入
            stringToRegisters(data.value, mbMapping->tab_registers + data.address, 32);
        }
        else if (data.dataType == "float") {
            // 浮点数类型数据写入
            float value = std::stof(data.value);
            floatToRegisters(value, mbMapping->tab_registers + data.address);
        }
        else if (data.dataType == "int") {
            // 整数类型数据写入
            mbMapping->tab_registers[data.address] = std::stoi(data.value);
        }
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "更新寄存器值失败: " << e.what();
    }
}
