#include "log_manager.h"
#include <fstream>
#include <algorithm>
#include <glog.h>

void LogManager::checkAndCleanLogs(const std::string& logPath, size_t maxLines) {
    try {
        auto logFiles = getLogFiles(logPath);
        if (logFiles.empty()) {
            return;
        }

        // 按修改时间排序
        std::sort(logFiles.begin(), logFiles.end(), 
            [](const std::filesystem::path& a, const std::filesystem::path& b) {
                return std::filesystem::last_write_time(a) > std::filesystem::last_write_time(b);
            });

        size_t totalLines = countTotalLines(logFiles);
        
        // 如果总行数超过限制，删除最旧的日志文件
        while (totalLines > maxLines && !logFiles.empty()) {
            auto oldestFile = logFiles.back();
            size_t fileLines = 0;
            
            // 计算文件行数
            std::ifstream file(oldestFile);
            std::string line;
            while (std::getline(file, line)) {
                fileLines++;
            }
            
            // 删除文件
            std::filesystem::remove(oldestFile);
            LOG(INFO) << "删除旧日志文件: " << oldestFile.string() 
                     << " (行数: " << fileLines << ")";
            
            totalLines -= fileLines;
            logFiles.pop_back();
        }
    } catch (const std::exception& e) {
        LOG(ERROR) << "清理日志文件时发生错误: " << e.what();
    }
}

std::vector<std::filesystem::path> LogManager::getLogFiles(const std::string& logPath) {
    std::vector<std::filesystem::path> logFiles;
    
    for (const auto& entry : std::filesystem::directory_iterator(logPath)) {
        if (entry.path().extension() == ".log") {
            logFiles.push_back(entry.path());
        }
    }
    
    return logFiles;
}

size_t LogManager::countTotalLines(const std::vector<std::filesystem::path>& logFiles) {
    size_t totalLines = 0;
    
    for (const auto& file : logFiles) {
        std::ifstream in(file);
        std::string line;
        while (std::getline(in, line)) {
            totalLines++;
        }
    }
    
    return totalLines;
} 