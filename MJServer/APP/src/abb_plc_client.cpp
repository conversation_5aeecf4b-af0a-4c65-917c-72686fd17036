#include "abb_plc_client.h"
#include <cstring>
#include <iostream>
#include <glog.h>
#include <algorithm>  // 为了使用 std::clamp
#include <array>
#include <chrono>
#include <cmath> // 添加这个头文件


ABBPLCClient::ABBPLCClient(const std::string& ip, int port) : _ip(ip),
                                                              _port(port),
                                                              mesServer(nullptr),
                                                              running(false),
                                                              heartbeatOK(false),
                                                              minWeight(0.0f),
                                                              maxWeight(1000.0f)
{
    // 初始化本地缓存
    localCache = std::make_shared<LocalCache>();
    localCache->feeders.resize(6);
    for (auto& feeder : localCache->feeders)
    {
        feeder = std::make_shared<FeederData>();
        feeder->sandTypes.resize(6);
    }

    // 初始化Modbus缓存
    modbusCache = std::make_shared<ModbusCache>();
    modbusCache->registers.resize(TOTAL_REGISTERS);

    // 初始化默认的投料机状态
    for (int i = 0; i < 6; i++)
    {
        auto feeder = std::make_shared<FeederStatus>();
        feeder->isBlocked = false;

        // 初始化6个料仓
        for (int j = 0; j < 6; j++)
        {
            feeder->bins[j].currentWeight = 0.0f;
            feeder->bins[j].minStartWeight = 100.0f;
            feeder->bins[j].targetWeight = 800.0f;
            feeder->bins[j].isBlocked = false;
            feeder->bins[j].sandType = "";
        }

        lastFeederStatus.push_back(feeder);
    }

       // 初始化所有料机的等待时间
       auto now = std::chrono::steady_clock::now();
       for (int i = 1; i <= 6; i++) {
           feederWaitTimes[i] = now;
       }


    executor1 = std::make_shared<Fuxi::Common::Executor>(6);
    executor2 = std::make_shared<Fuxi::Common::Executor>(1);
}

ABBPLCClient::~ABBPLCClient()
{
    stop();
}

bool ABBPLCClient::start()
{
    running = true;

    if (!initModbus())
    {
        LOG(WARNING) << "Modbus initialization failed, will retry later";
        return false;
    }

    // 启动定期更新缓存的任务
    executor1->postTask([this]()
    {
        while (running)
        {
            updateModbusCache();
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    });

    // 启动主循环
    executor2->postTask([this]() { run(); });

    return true;
}

void ABBPLCClient::stop()
{
    running = false;
}


void ABBPLCClient::run()
{
    while (running)
    {
        try
        {
          //  LOG(INFO) << "run--------------------";
            if (!m_ctx)
            {
                LOG(ERROR) << "Modbus context is null";
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }


            try
            {
                if (!m_ctx)
                {
                    LOG(ERROR) << "Modbus context is null";
                    heartbeatOK = false;
                    std::this_thread::sleep_for(std::chrono::seconds(1));
                    continue;
                }

                uint16_t heartbeat;
                // 从缓存读取心跳值
                if (safeModbusRead(ADDR_HEARTBEAT, 1, &heartbeat) == 1)
                {
                    if (parseInt(&heartbeat) != 0)
                    {
                        uint16_t response = 0;
                        // 写入响应时仍需要直接与PLC通信
                        std::lock_guard<std::mutex> lock(modbusMutex);
                        if (m_ctx->modbus_write_register(ADDR_HEARTBEAT, response) == 0)
                        {
                            heartbeatOK = true;
                          //  LOG(INFO) << "心跳正常";
                        }
                        else
                        {
                            heartbeatOK = false;
                            LOG(ERROR) << "心跳响应写入失败: " << m_ctx->error_msg;
                        }
                    }
                }
                else
                {
                    heartbeatOK = false;
                    LOG(ERROR) << "心跳读取失败";
                }
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "心跳处理异常: " << e.what();
                heartbeatOK = false;
            }

            writeAreaDataToPLC();
            // 使用一次性读取获取系统状态
            bool systemError = false;
            bool isAuto = false;
            if (!readSystemStatus(isAuto, systemError))
            {
                std::this_thread::sleep_for(std::chrono::seconds(1));
                continue;
            }

            // 自动模式且无故障时执行任务处理
            if (isAuto && !systemError)
            {
                try
                {
                    auto status = getTaskStatus();
                    if (status)
                    {
                        if (status->isReady)
                        {
                            // 前任务完成，寻找下一个任务
                            if (auto nextTask = findNextTask())
                            {
                                if (startTask(nextTask))
                                {
                                    LOG(INFO) << "成功启动新任务";
                                }
                                else
                                {
                                    LOG(WARNING) << "启动新任务失败";
                                }
                            }
                            else
                            {
                                VLOG(1) << "没有找到合适的新任务";
                            }
                        }
                        else
                        {
                            VLOG(2) << "当前任务未完成，状态: "
                                   << "就绪=" << status->isReady
                                   << ", 倒料中=" << status->isDumping
                                   << ", 完成=" << status->isCompleted
                                   << ", 剩余重量=" << status->remainingWeight;
                        }
                    }
                }
                catch (const std::exception& e)
                {
                    LOG(ERROR) << "任务处理异常: " << e.what();
                }
            }
            else
            {
                VLOG(1) << "系统状态: "
                        << (isAuto ? "自动" : "手动")
                        << (systemError ? ", 有故障" : ", 无故障");
            }

            // MES数据同步
            try
            {
                if (mesServer->isConnected() && !systemError)
                {
                    // 批量处理数据更新
                    bool dataUpdated = false;


                    dataUpdated = true;
                    // LOG(INFO) << "区域数据更新成功";

                    // if (writeAreaMaterialTypes()) {
                    //     dataUpdated = true;
                    //     LOG(INFO) << "物料类型更新成功";
                    // }

                    readTaskFromPLC();
                    updateLocalCache();

                    if (dataUpdated)
                    {
                        // LOG(INFO) << "MES数据同步完成";
                    }
                }
            }
            catch (const std::exception& e)
            {
                LOG(ERROR) << "MES数据同步异常: " << e.what();
            }
        }
        catch (const std::exception& e)
        {
            LOG(ERROR) << "主循环异常: " << e.what();
        }

        // 主循环延时，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void ABBPLCClient::writeAreaDataToPLC()
{

  try
    {
        const int BATCH_SIZE = 32; // 每批写入的最大寄存器数量
        const int DELAY_MS = 50; // 每批写入后的延时(毫秒)

        const auto& areas = mesServer->getAreas();
        for (const auto& [areaId, area] : areas)
        {
            // 检查MES读取标志
            uint16_t mesFlag;
            if (safeModbusRead(ADDR_MES_READ_START + (areaId - 'A'), 1, &mesFlag) != 1)
            {
                LOG(WARNING) << "读取区域 " << areaId << " 的MES标志失败";
                continue;
            }

            // 如果MES标志为0，表示不需要更新
            if (parseInt(&mesFlag) == 0)
            {
                VLOG(2) << "区域 " << areaId << " 无需更新数据";
                continue;
            }

            // 如果区域数据为空或没有桶数据，写入标志2表示需要继续读取
            if (!area || area->buckets.empty())
            {
                uint16_t retryFlag = 2;
                if (safeModbusWrite(ADDR_MES_READ_START + (areaId - 'A'), 1, &retryFlag) != 1)
                {
                    LOG(WARNING) << "写入区域 " << areaId << " 的重试标志失败";
                }
                LOG(INFO) << "区域 " << areaId << " 数据为空，设置重试标志";
                continue;
            }

            bool writeSuccess = true;

            // 写入区域砂型
            uint16_t sandTypeRegs[17];
            packString32(area->sandType, sandTypeRegs);
            if (safeModbusWrite(ADDR_MATERIAL_TYPE_START + (areaId - 'A') * 17, 17, sandTypeRegs) != 17)
            {
                LOG(ERROR) << "区域 " << areaId << " 写入砂型失败: " << area->sandType;
                writeSuccess = false;
            }
            else
            {
                // LOG(INFO) << "区域 " << areaId << " 写入砂型成功: " << area->sandType;
            }

            if (!writeSuccess) continue;

            // 以下是原有的数据准备和写入逻辑
            std::vector<uint16_t> materialRegs;
            std::vector<uint16_t> weightRegs;
            uint16_t bucketCount = area->buckets.size();

            // 处理每个桶的数据
            for (const auto& bucket : area->buckets)
            {
                // 准备物料数据
                uint16_t materialData[17];
                packString32(bucket.batchNumber, materialData);
                materialRegs.insert(materialRegs.end(), materialData, materialData + 17);

                // 准备重量数据
                uint16_t weightData[2];
                packReal(bucket.weight, weightData);
                weightRegs.insert(weightRegs.end(), weightData, weightData + 2);
            }

            // 写入物料号数据
            int areaOffset = (areaId - 'A') * (4 * 17);
            for (size_t offset = 0; offset < materialRegs.size(); offset += BATCH_SIZE)
            {
                size_t batch_size = std::min(BATCH_SIZE, static_cast<int>(materialRegs.size() - offset));
                if (safeModbusWrite(ADDR_AREA_START + areaOffset + offset, batch_size, &materialRegs[offset]) !=
                    batch_size)
                {
                    LOG(ERROR) << "区域 " << areaId << " 批量写入物料号数据失败";
                    writeSuccess = false;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(DELAY_MS));
            }

            if (!writeSuccess) continue;

            // 写入重量数据
            for (size_t offset = 0; offset < weightRegs.size(); offset += BATCH_SIZE)
            {
                size_t batch_size = std::min(BATCH_SIZE, static_cast<int>(weightRegs.size() - offset));
                if (safeModbusWrite(ADDR_BUCKET_WEIGHT_START + (areaId - 'A') * 8 + offset, batch_size,
                                    &weightRegs[offset]) != batch_size)
                {
                    LOG(ERROR) << "区域 " << areaId << " 批量写入重量数据失败";
                    writeSuccess = false;
                    break;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(DELAY_MS));
            }

            if (!writeSuccess) continue;

            // 写入桶数量
            uint16_t countReg;
            packInt(bucketCount, &countReg);
            if (safeModbusWrite(ADDR_BUCKET_COUNT_START + (areaId - 'A'), 1, &countReg) != 1)
            {
                LOG(ERROR) << "区域 " << areaId << " 写入桶数量失败";
                continue;
            }

            // 所有数据写入成功后，检查并清除MES读取标志
            if (parseInt(&mesFlag) != 2) {  // 只有当标志不为2时才清除
                uint16_t clearFlag = 0;
                if (safeModbusWrite(ADDR_MES_READ_START + (areaId - 'A'), 1, &clearFlag) != 1)
                {
                    LOG(WARNING) << "清除区域 " << areaId << " 的MES标志失败";
                }
                else
                {
                    // LOG(INFO) << "区域 " << areaId << " 数据更新完成: "
                    //     << "物料号寄存器=" << materialRegs.size()
                    //     << ", 重量寄存器=" << weightRegs.size()
                    //     << ", 桶数量=" << bucketCount;
                }
            }
        }
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "批量写入区域数据异常: " << e.what();
    }

    // try
    // {
    //     const int BATCH_SIZE = 32; // 每批写入的最大寄存器数量
    //     const int DELAY_MS = 50; // 每批写入后的延时(毫秒)
    //     //LOG(WARNING) << "writeAreaDataToPLC ";
    //     const auto& areas = mesServer->getAreas();
    //     for (const auto& [areaId, area] : areas)
    //     {
    //         if (!area || area->buckets.empty())
    //         {
    //             LOG(WARNING) << "!area || area->buckets.empty()------------- ";
    //             continue;
    //         }
    //
    //         // 检查MES读取标志
    //         uint16_t mesFlag;
    //         if (safeModbusRead(ADDR_MES_READ_START + (areaId - 'A'), 1, &mesFlag) != 1)
    //         {
    //             LOG(WARNING) << "读取区域 " << areaId << " 的MES标志失败";
    //             continue;
    //         }
    //
    //         // 如果MES标志为0，表示不需要更新
    //         if (parseInt(&mesFlag) == 0)
    //         {
    //             VLOG(2) << "区域 " << areaId << " 无需更新数据";
    //             continue;
    //         }
    //
    //         bool writeSuccess = true;
    //
    //         // 写入区域砂型
    //         uint16_t sandTypeRegs[17];
    //         packString32(area->sandType, sandTypeRegs);
    //         if (safeModbusWrite(ADDR_MATERIAL_TYPE_START + (areaId - 'A') * 17, 17, sandTypeRegs) != 17)
    //         {
    //             LOG(ERROR) << "区域 " << areaId << " 写入砂型失败: " << area->sandType;
    //             writeSuccess = false;
    //         }
    //         else
    //         {
    //             // LOG(INFO) << "区域 " << areaId << " 写入砂型成功: " << area->sandType;
    //         }
    //
    //         if (!writeSuccess) continue;
    //
    //         // 以下是原有的数据准备和写入逻辑
    //         std::vector<uint16_t> materialRegs;
    //         std::vector<uint16_t> weightRegs;
    //         uint16_t bucketCount = area->buckets.size();
    //
    //         // 处理每个桶的数据
    //         for (const auto& bucket : area->buckets)
    //         {
    //
    //
    //             // 准备物料数据
    //             uint16_t materialData[17];
    //             packString32(bucket.batchNumber, materialData);
    //             materialRegs.insert(materialRegs.end(), materialData, materialData + 17);
    //
    //             // 准备重量数据
    //             uint16_t weightData[2];
    //             packReal(bucket.weight, weightData);
    //             weightRegs.insert(weightRegs.end(), weightData, weightData + 2);
    //         }
    //
    //         // 写入物料号数据
    //         int areaOffset = (areaId - 'A') * (4 * 17);
    //         for (size_t offset = 0; offset < materialRegs.size(); offset += BATCH_SIZE)
    //         {
    //             size_t batch_size = std::min(BATCH_SIZE, static_cast<int>(materialRegs.size() - offset));
    //             if (safeModbusWrite(ADDR_AREA_START + areaOffset + offset, batch_size, &materialRegs[offset]) !=
    //                 batch_size)
    //             {
    //                 LOG(ERROR) << "区域 " << areaId << " 批量写入物料号数据失败";
    //                 writeSuccess = false;
    //                 break;
    //             }
    //             std::this_thread::sleep_for(std::chrono::milliseconds(DELAY_MS));
    //         }
    //
    //         if (!writeSuccess) continue;
    //
    //         // 写入重量数据
    //         for (size_t offset = 0; offset < weightRegs.size(); offset += BATCH_SIZE)
    //         {
    //             size_t batch_size = std::min(BATCH_SIZE, static_cast<int>(weightRegs.size() - offset));
    //             if (safeModbusWrite(ADDR_BUCKET_WEIGHT_START + (areaId - 'A') * 8 + offset, batch_size,
    //                                 &weightRegs[offset]) != batch_size)
    //             {
    //                 LOG(ERROR) << "区域 " << areaId << " 批量写入重量数据失败";
    //                 writeSuccess = false;
    //                 break;
    //             }
    //             std::this_thread::sleep_for(std::chrono::milliseconds(DELAY_MS));
    //         }
    //
    //         if (!writeSuccess) continue;
    //
    //         // 写入桶数量
    //         uint16_t countReg;
    //         packInt(bucketCount, &countReg);
    //         if (safeModbusWrite(ADDR_BUCKET_COUNT_START + (areaId - 'A'), 1, &countReg) != 1)
    //         {
    //             LOG(ERROR) << "区域 " << areaId << " 写入桶数量失败";
    //             continue;
    //         }
    //
    //         // 所有数据写入成功后，清除MES读取标志
    //         uint16_t clearFlag = 0;
    //         if (safeModbusWrite(ADDR_MES_READ_START + (areaId - 'A'), 1, &clearFlag) != 1)
    //         {
    //             LOG(WARNING) << "清除区域 " << areaId << " 的MES标志失败";
    //         }
    //         else
    //         {
    //             // LOG(INFO) << "区域 " << areaId << " 数据更新完成: "
    //             //     << "物料号寄存器=" << materialRegs.size()
    //             //     << ", 重量寄存器=" << weightRegs.size()
    //             //     << ", 桶数量=" << bucketCount;
    //         }
    //     }
    // }
    // catch (const std::exception& e)
    // {
    //     LOG(ERROR) << "批量写入区域数据异常: " << e.what();
    // }
}

void ABBPLCClient::readTaskFromPLC()
{
    auto taskData = std::make_shared<SendData>();
    try
    {
        if (current_task)
        {
            // 设置批号
            taskData->batchNumber = current_task->batchNumber;

            // 设置当前时间作为倒料时间
            time_t now = time(NULL);
            struct tm* tm_now = localtime(&now);
            char time_str[20];
            strftime(time_str, sizeof(time_str), "%Y-%m-%d %H:%M:%S", tm_now);
            // 解析倒料时间(STRING[20])
            taskData->dumpTime = time_str;

            // 设置投料机和料仓信息 - 使用逻辑顺序
            taskData->feederNumber = current_task->feederId;
            taskData->binNumber = current_task->binId;

            // 设置重量相关参数
            taskData->dumpWeight = current_task->weight;
            taskData->minWeight = current_task->minWeight;
            taskData->maxWeight = current_task->maxWeight;

            // 设置任务相关信息
            taskData->taskBatchNumber = current_task->batchNumber;
            taskData->taskFeederNumber = current_task->feederId;
            taskData->taskBinNumber = current_task->binId;

            // 更新到MES服务器
            if (mesServer)
            {
                mesServer->updateSendData(taskData);
            }

            LOG(INFO) << "成功读取任务数据: "
                     << "批号=" << taskData->batchNumber
                     << ", 时间=" << taskData->dumpTime
                     << ", 投料机=" << taskData->feederNumber
                     << ", 料仓=" << taskData->binNumber
                     << ", 重量=" << taskData->dumpWeight
                     << ", 最小起加量=" << taskData->minWeight
                     << ", 最大加入量=" << taskData->maxWeight
                     << ", 任务批号=" << taskData->taskBatchNumber
                     << ", 任务投料机=" << taskData->taskFeederNumber
                     << ", 任务料仓=" << taskData->taskBinNumber;
        }
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "读取任务数据异常: " << e.what();
    }
}

bool ABBPLCClient::isSystemAuto() const
{
    uint16_t autoStatus;
    if (safeModbusRead(ADDR_AUTO_MODE, 1, &autoStatus) == 1)
    {
        return (autoStatus == 1);
    }
    return false;
}

bool ABBPLCClient::hasError() const
{
    uint16_t errorStatus;
    if (safeModbusRead(ADDR_ERROR_STATE, 1, &errorStatus) == 1)
    {
        return (errorStatus == 1);
    }
    return false;
}

bool ABBPLCClient::startTask(std::shared_ptr<TaskInfo> task)
{
    if (!task || !running)
    {
        notifyTaskLog("任务启动失败: " + std::string(!task ? "任务为空" : "系统未运行"));
        return false;
    }

    try
    {
        std::stringstream logMsg;
        logMsg << "开始启动任务: "
               << "投料机=" << task->feederId
               << " 料仓=" << task->binId 
               << " 区域=" << task->areaId
               << " 桶号=" << task->bucketId
               << " 批号=" << task->batchNumber
               << " 重量=" << task->weight;
        notifyTaskLog(logMsg.str());

        // 从PLC读取料口的起加量和加入量
        uint16_t startWeightRegs[2], targetWeightRegs[2];
        int feederOffset = (task->feederId - 1) * 10 + (task->binId - 1) * 2;
        
        if (safeModbusRead(ADDR_START_WEIGHT_START + feederOffset, 2, startWeightRegs) == 2 &&
            safeModbusRead(ADDR_TARGET_WEIGHT_START + feederOffset, 2, targetWeightRegs) == 2) {
            
            task->minWeight = parseReal(startWeightRegs);
            task->maxWeight = parseReal(startWeightRegs)+parseReal(targetWeightRegs);
            
            LOG(INFO) << "获取料口参数: 起加量=" << task->minWeight 
                     << ", 加入量=" << task->maxWeight;
        } else {
            LOG(ERROR) << "读取料口参数失败";
            return false;
        }

        // 检查任务条件
        if (!checkTaskConditions(task))
        {
            notifyTaskLog("任务条件检查失败，取消启动");
            return false;
        }
        notifyTaskLog("任务条件检查通过");

        // 写入任务数据到PLC
        LOG(INFO) << "开始写入任务数据到PLC...";
        writeTaskToABB(task);

        // 等待任务开始执行
        notifyTaskLog("等待任务开始执行...");
        uint16_t taskReady;
        for (int retry = 0; retry < 50; retry++)
        {
            if (safeModbusRead(ADDR_TASK_READY, 1, &taskReady) == 1)
            {
                if (parseInt(&taskReady) == 1)
                {
                    notifyTaskLog("任务成功开始执行 (重试次数=" + std::to_string(retry) + ")");
                    current_task = task;
                    currentFeederId = task->feederId;  // 更新当前料机ID
                    return true;
                }
                notifyTaskLog("等待PLC响应... (重试次数=" + std::to_string(retry) + 
                            ", 任务就绪状态=" + std::to_string(parseInt(&taskReady)) + ")");
            }
            else
            {
                LOG(WARNING) << "读取任务就绪状态失败 (重试次数=" << retry << ")";
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }

        notifyTaskLog("等待任务开始超时 (5秒)");
        return false;
    }
    catch (const std::exception& e)
    {
        std::stringstream errMsg;
        errMsg << "启动任务异常: " << e.what() << "\n"
               << "任务信息: "
               << "投料机=" << task->feederId
               << " 料仓=" << task->binId
               << " 区域=" << task->areaId
               << " 桶号=" << task->bucketId
               << " 批号=" << task->batchNumber
               << " 重量=" << task->weight;
        notifyTaskLog(errMsg.str());
        return false;
    }
}

// 获取等待时间最长的料机
int ABBPLCClient::getLongestWaitingFeeder() {
    int longestWaitingFeeder = 0;
    std::chrono::steady_clock::time_point oldestTime;
    
    for (const auto& [feederId, waitTime] : feederWaitTimes) {
        // 根据逻辑顺序检查料机是否启用
        int logicalFeederId = feederId - 1; // 转换为0-based索引
        
        if (!isFeederEnabled(logicalFeederId)) {
            continue;
        }
        
        if (longestWaitingFeeder == 0 || waitTime < oldestTime) {
            longestWaitingFeeder = feederId;
            oldestTime = waitTime;
        }
    }
    
    return longestWaitingFeeder;
}

// 更新料机等待时间
void ABBPLCClient::updateFeederWaitTime(int feederId) {
    if (feederId > 0 && feederId <= 6) {
        feederWaitTimes[feederId] = std::chrono::steady_clock::now();
    }
}

void ABBPLCClient::writeTaskToABB(std::shared_ptr<TaskInfo> task)
{
    try
    {
        LOG(INFO) << "开始写入任务参数:";

        // 写入取桶位置(INT)
        LOG(INFO) << "  写入取桶位置: " << task->bucketId;
        if (!writeInt(ADDR_ROTO_POSITION, task->bucketId))
        {
            LOG(ERROR) << "  写入取桶位置失败";
            return;
        }

        // 转换为倒置的投料机ID (物理上的顺序是倒置的)
        int invertedFeederId = 5 - task->feederId; // 将0-5倒置为5-0

        // 写入倒料设备号(INT)
        LOG(INFO) << "  写入倒料设备号: " << task->feederId << " (倒置后: " << invertedFeederId << ")";
        if (!writeInt(ADDR_ROTO_FEEDER, invertedFeederId))
        {
            LOG(ERROR) << "  写入倒料设备号失败";
            return;
        }

        // 写入料口编号(INT)
        LOG(INFO) << "  写入料口编号: " << task->binId;
        if (!writeInt(ADDR_ROTO_BIN, task->binId))
        {
            LOG(ERROR) << "  写入料口编号失败";
            return;
        }

        // 写入区域ID (STRING[1])
        LOG(INFO) << "  写入区域ID: " << task->areaId;
        if (!writeString1(ADDR_ROTO_AREA, task->areaId))
        {
            LOG(ERROR) << "  写入区域ID失败";
            return;
        }

        LOG(INFO) << "任务参数写入完成: "
            << "桶=" << task->bucketId
            << ", 投料机=" << task->feederId << " (倒置后: " << invertedFeederId << ")"
            << ", 料仓=" << task->binId
            << ", 区域=" << task->areaId;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "写入任务数据异常: " << e.what() << "\n"
            << "任务信息: "
            << "投料机=" << task->feederId
            << " 料仓=" << task->binId
            << " 区域=" << task->areaId
            << " 桶号=" << task->bucketId
            << " 批号=" << task->batchNumber
            << " 重量=" << task->weight;
    }
}

std::shared_ptr<TaskStatus> ABBPLCClient::getTaskStatus() const
{
    auto status = std::make_shared<TaskStatus>();

    try
    {
        // 一次性读取所有任务状态寄存器
        const int TOTAL_READ = 4; // 准备+运行+完成+重量(2个寄存器)
        uint16_t registers[TOTAL_READ];

        if (safeModbusRead(ADDR_TASK_READY, TOTAL_READ, registers) == TOTAL_READ)
        {
            status->isReady = (parseInt(&registers[0]) == 1); // ADDR_TASK_READY
            status->isDumping = (parseInt(&registers[1]) == 1); // ADDR_TASK_DUMPING
            status->isCompleted = true; // ADDR_TASK_COMPLETED
            status->remainingWeight = parseReal(&registers[2]); // ADDR_REMAINING_WEIGHT

            // LOG(INFO) << "任务状态: "
            //     << "就绪=" << status->isReady
            //     << ", 倒料中=" << status->isDumping
            //     << ", 完成=" << status->isCompleted
            //     << ", 剩余重量=" << status->remainingWeight;
        }
        else
        {
            LOG(ERROR) << "读取任务状态失败";
        }
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "读取任务状态异常: " << e.what();
    }

    return status;
}


bool ABBPLCClient::isAreaEnabled(char areaId) const
{
    if (areaId < 'A' || areaId > 'V')
    {
        return false;
    }

    int areaIndex = areaId - 'A';
    uint16_t value;
    if (safeModbusRead(ADDR_AREA_ENABLE_START + areaIndex, 1, &value) == 1)
    {
        return (parseInt(&value) == 0); // 0表示启用,1表示屏蔽
    }
    return false;
}

std::string ABBPLCClient::readSandType(int feederId, int binId) const
{
    if (!running)
    {
        return {};
    }

    // 参数检查
    if (feederId < 0 || feederId >= 6 || binId < 0 || binId >= 5)
    {
        LOG(ERROR) << "无效的投料机或料仓编号: feeder=" << feederId << ", bin=" << binId;
        return "";
    }

    try
    {
        // 从MES服务器获取投料数据
        if (mesServer )
        {
            auto feeders = mesServer->getFeeders();
            if (feederId < feeders.size() && binId < feeders[feederId]->sandTypes.size())
            {
                std::string sandType = feeders[feederId]->sandTypes[binId];
                if (!sandType.empty())
                {
                    VLOG(2) << "读取砂类型成功: feeder=" << feederId
                            << ", bin=" << binId
                            << ", type=" << sandType;
                }
                return sandType;
            }
        }
        else
        {
            LOG(WARNING) << "MES服务器未连接或无效";
        }
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "读取砂类型异常: feeder=" << feederId
            << ", bin=" << binId
            << ", error=" << e.what();
    }

    return "";
}

void ABBPLCClient::updateLocalCache()
{
    if (mesServer && mesServer->isConnected())
    {
        if (localCache)
        {
            localCache->update(mesServer->getAreas());
        }
    }
}

bool ABBPLCClient::useLocalCache() const
{
    return localCache && (!mesServer->isConnected() || hasError()) && localCache->isValid();
}

std::shared_ptr<TaskInfo> ABBPLCClient::findNextTask()
{
    if (!running)
    {
        LOG(WARNING) << "系统未运行";
        notifyTaskLog("系统未运行");
        return nullptr;
    }

    // 创建一个空的区域数据映射
    std::map<char, std::shared_ptr<AreaData>> areas;

    // 遍历所有区域(A-V)
    for (char areaId = 'A'; areaId <= 'V'; areaId++)
    {
        // 从PLC读取该区域数据
        auto plcArea = getPLCAreaData(areaId);
        if (plcArea)
        {
            areas[areaId] = plcArea;
            // LOG(INFO) << "区域 " << areaId << " 数据读取成功: "
            //     << "砂型=" << plcArea->sandType
            //     << ", 桶数量=" << plcArea->buckets.size();
        }
    }

    auto feederStatus = getFeederStatus();
    if (feederStatus.empty())
    {
        LOG(WARNING) << "获取投料机状态失败，使用上次状态";
        notifyTaskLog("获取投料机状态失败，使用上次状态");
        feederStatus = lastFeederStatus;
    }
    lastFeederStatus = feederStatus;

    // 创建一个优先级队列，按照以下规则排序：
    // 1. 当前正在投料的料机优先
    // 2. 等待时间最长的料机优先
    std::vector<int> prioritizedFeeders;
    
    // 如果当前有正在投料的料机，优先处理它
    if (currentFeederId > 0 && currentFeederId <= 6) {
        prioritizedFeeders.push_back(currentFeederId - 1);  // 转换为0-based索引
        LOG(INFO) << "优先处理当前投料机 " << currentFeederId;
        notifyTaskLog("优先处理当前投料机 " + std::to_string(currentFeederId));
    }
    
    // 获取等待时间最长的料机
    int longestWaitingFeeder = getLongestWaitingFeeder();
    
    // 添加其他料机，按等待时间排序
    for (size_t i = 0; i < feederStatus.size(); i++) {
        // 跳过当前料机(已添加)和不可用的料机
        if ((i + 1) == currentFeederId || !isFeederEnabled(i)) {
            continue;
        }
        
        // 等待时间最长的料机优先
        if ((i + 1) == longestWaitingFeeder) {
            prioritizedFeeders.insert(prioritizedFeeders.begin() + (currentFeederId > 0 ? 1 : 0), i);
        } else {
            prioritizedFeeders.push_back(i);
        }
    }
    
    // 按优先级顺序遍历料机
    for (int feederId : prioritizedFeeders) {
        const auto& feeder = feederStatus[feederId];
        if (!feeder || feeder->isBlocked)
        {
            LOG(INFO) << "投料机 " << feederId + 1 << " "
                << (!feeder ? "无效" : "已屏") << ",跳过";
            notifyTaskLog("投料机 " + std::to_string(feederId + 1) + " "
                + (!feeder ? "无效" : "已屏") + ",跳过");
            continue;
        }

        LOG(INFO) << "检查投料机 " << feederId + 1 << " 状态:";
        for (int binId = 0; binId < 5; binId++)
        {


            const auto& bin = feeder->bins[binId];
            if (bin.isBlocked)
            {
                LOG(INFO) << "  料仓 " << binId + 1 << " 已屏蔽,跳过";
                notifyTaskLog("  料仓 " + std::to_string(binId + 1) + " 已屏蔽,跳过");
                continue;
            }

            // 修改这里：允许补料，只要当前重量小于目标重量就继续找料
            if (bin.currentWeight < bin.minStartWeight)
            {
                LOG(INFO) << "  料仓 " << binId + 1 << " 需要补料:"
                    << " 当前重量=" << bin.currentWeight
                    << " 目标重量=" << bin.targetWeight
                    << " 砂型=" << bin.sandType;
                notifyTaskLog("  料仓 " + std::to_string(binId + 1) + " 需要补料");

                // 遍历所有区域寻找匹配的物料
                for (const auto& [areaId, area] : areas)
                {
                    // 添加投料机与区域的匹配限制
                    if ((feederId < 3 && (areaId < 'A' || areaId > 'K')) ||
                        (feederId >= 3 && (areaId < 'L' || areaId > 'V')))
                    {
                        // LOG(INFO) << "    区域 " << areaId << " 不匹配投料机 " << feederId + 1 << " 的区域范围,跳过";
                        // notifyTaskLog("    区域 " + std::string(1, areaId) + " 不匹配投料机 " + std::to_string(feederId + 1) + " 的区域范围,跳过");
                        continue;
                    }

                    if (!isAreaEnabled(areaId))
                    {
                        LOG(INFO) << "    区域 " << areaId << " 未启用,跳过";
                        notifyTaskLog("    区域 " + std::string(1, areaId) + " 未启用,跳过");
                        continue;
                    }

                    LOG(INFO) << "    检查区域 " << areaId << ":"
                        << " 区域砂型=" << area->sandType;

                    // 遍历区域内的所有桶
                    for (size_t bucketId = 0; bucketId < area->buckets.size(); bucketId++)
                    {
                        const auto& bucket = area->buckets[bucketId];
                        LOG(INFO) << "      检查桶 " << bucketId + 1 << ":"
                            << " 批号=" << bucket.batchNumber
                            << " 重量=" << bucket.weight;

                        bool materialMatched = checkMaterialMatch(area->sandType, bin.sandType);
                        bool weightOK = checkWeightConditions(bucket.weight,
                                                              bin.currentWeight,
                                                              bin.minStartWeight,
                                                              bin.targetWeight);

                        LOG(INFO) << "      匹配结果: 物料匹配="
                            << (materialMatched ? "是" : "否")
                            << " 重量条件="
                            << (weightOK ? "满足" : "不满足");

                        if (materialMatched && weightOK)
                        {
                            auto task = std::make_shared<TaskInfo>();
                            task->feederId = feederId + 1;
                            task->binId = binId + 1;
                            task->areaId = areaId;
                            task->bucketId = bucketId + 1;
                            task->batchNumber = bucket.batchNumber;
                            task->weight = bin.targetWeight;

                            if (checkTaskConditions(task))
                            {
                                LOG(INFO) << "找到匹配任务: "
                                    << "投料机=" << task->feederId
                                    << " 料仓=" << task->binId
                                    << " 区域=" << task->areaId
                                    << " 桶号=" << task->bucketId
                                    << " 批号=" << task->batchNumber
                                    << " 重量=" << task->weight;
                                notifyTaskLog("找到匹配任务: "
                                    "投料机=" + std::to_string(task->feederId) +
                                    " 料仓=" + std::to_string(task->binId) +
                                    " 区域=" + std::string(1, task->areaId) +
                                    " 桶号=" + std::to_string(task->bucketId) +
                                    " 批号=" + task->batchNumber +
                                    " 重量=" + std::to_string(task->weight));
                                
                                // 更新该料机的等待时间
                                updateFeederWaitTime(task->feederId);
                                
                                return task;
                            }
                            else
                            {
                                LOG(INFO) << "      任务条件检查未通过";
                                notifyTaskLog("      任务条件检查未通过");
                            }
                        }
                    }
                }
            }
            else
            {
                LOG(INFO) << "  料仓 " << binId + 1 << " 不需要补料: "
                    << "当前=" << bin.currentWeight
                    << " >= 目标=" << bin.targetWeight;
            }
        }
    }

    LOG(INFO) << "未找到匹配的任务";
    notifyTaskLog("未找到匹配的任务");
    return nullptr;
}

bool ABBPLCClient::checkTaskConditions(std::shared_ptr<TaskInfo> task)
{
    if (!task)
    {
        LOG(ERROR) << "任务指针为空";
        notifyTaskLog("任务指针为空");
        return false;
    }

    // // 检查基本条件
    // if (!isBinEnabled(task->feederId - 1, task->binId - 1) ||
    //     !isAreaEnabled(task->areaId))
    // {
    //     LOG(WARNING) << "任务基本条件检查失败";
    //     notifyTaskLog("任务基本条件检查失败");
    //     return false;
    // }

    // 检查任务参数有效性
    if (task->feederId < 1 || task->feederId > 6 ||
        task->binId < 1 || task->binId > 5 ||
        task->areaId < 'A' || task->areaId > 'V' ||
        task->bucketId < 1 || task->bucketId > 4)
    {
        LOG(WARNING) << "任务参数无效";
        notifyTaskLog("任务参数无效");
        return false;
    }

    // 检查重量是否在有效范围内
    if (task->weight < minWeight || task->weight > maxWeight)
    {
        LOG(WARNING) << "任务重量超出范围: " << task->weight;
        notifyTaskLog("任务重量超出范围: " + std::to_string(task->weight));
        return false;
    }

    return true;
}

std::vector<std::shared_ptr<FeederStatus>> ABBPLCClient::getFeederStatus()
{
    std::vector<std::shared_ptr<FeederStatus>> feeders;
    feeders.resize(FEEDERS_COUNT);

    try
    {
        // 一次性读取所有投料机和料仓的屏蔽状态
        const int BLOCK_REGS = FEEDERS_COUNT * 6;
        uint16_t blockRegs[BLOCK_REGS];
        if (safeModbusRead(ADDR_FEEDER_BLOCK_START, BLOCK_REGS, blockRegs) != BLOCK_REGS)
        {
            LOG(ERROR) << "读取设备和料仓屏蔽状态失败";
            return lastFeederStatus;
        }

        // 一次性读取所有重量数据
        const int WEIGHT_REGS = FEEDERS_COUNT * BINS_PER_FEEDER * 6;
        uint16_t weightRegs[WEIGHT_REGS];
        if (safeModbusRead(ADDR_CURRENT_WEIGHT_START, WEIGHT_REGS, weightRegs) != WEIGHT_REGS)
        {
            LOG(ERROR) << "读取重量数据失败";
            return lastFeederStatus;
        }

        for (int feederId = 0; feederId < FEEDERS_COUNT; feederId++)
        {
            // 计算倒置后的投料机ID (物理上的顺序是倒置的)
            int invertedFeederId = FEEDERS_COUNT - 1 - feederId; // 从0-5映射到5-0
            
            // 使用逻辑上的顺序存储(0-5对应逻辑上的1-6)
            feeders[feederId] = std::make_shared<FeederStatus>();
            // 从物理倒置的顺序读取屏蔽状态
            feeders[feederId]->isBlocked = (parseInt(&blockRegs[invertedFeederId * 6]) == 1);

            // 从MES服务器获取投料机数据
            if (mesServer && mesServer->isConnected())
            {
                auto mesFeeders = mesServer->getFeeders();
                if (feederId < mesFeeders.size())
                {
                    feeders[feederId]->sandTypes = mesFeeders[feederId]->sandTypes;
                    feeders[feederId]->changeTime = mesFeeders[feederId]->changeTime;
                }
            }

            for (int binId = 0; binId < BINS_PER_FEEDER; binId++)
            {
                auto& bin = feeders[feederId]->bins[binId];
                // 从物理倒置的顺序读取料仓屏蔽状态
                bin.isBlocked = (parseInt(&blockRegs[invertedFeederId * 6 + binId + 1]) == 1);

                // 修正重量数据的偏移量计算 - 使用倒置的投料机ID读取
                // 每个投料机有6个料仓，每个料仓有2个寄存器 (修正乘数为12而不是10)
                int currentWeightOffset = (invertedFeederId * 12 + binId * 2);
                int startWeightOffset = (invertedFeederId * 12 + binId * 2);
                int targetWeightOffset = (invertedFeederId * 12 + binId * 2);

                bin.currentWeight = parseReal(&weightRegs[currentWeightOffset]);
                bin.minStartWeight = parseReal(&weightRegs[BINS_PER_FEEDER * FEEDERS_COUNT * 2 + startWeightOffset]);
                bin.targetWeight = parseReal(&weightRegs[BINS_PER_FEEDER * FEEDERS_COUNT * 4 + targetWeightOffset]);

                // 读取砂类型 - 使用逻辑顺序
                bin.sandType = readSandType(feederId, binId);
            }
        }

        lastFeederStatus = feeders;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "读取投料机状态异常: " << e.what();
        return lastFeederStatus;
    }

    return feeders;
}

bool ABBPLCClient::isFeederEnabled(int feederId) const
{
    if (feederId < 0 || feederId >= 6)
    {
        return false;
    }

    // 转换为倒置的投料机ID (物理上的顺序是倒置的)
    int invertedFeederId = FEEDERS_COUNT - 1 - feederId; // 从0-5映射到5-0

    uint16_t value;
    if (safeModbusRead(ADDR_FEEDER_BLOCK_START + invertedFeederId, 1, &value) == 1)
    {
        return (parseInt(&value) == 0); // 0表示启用,1表示屏蔽
    }
    return false;
}

bool ABBPLCClient::isBinEnabled(int feederId, int binId) const
{
    if (feederId < 0 || feederId >= 6 || binId < 0 || binId >= 5)
    {
        return false;
    }

    // 转换为倒置的投料机ID (物理上的顺序是倒置的)
    int invertedFeederId = FEEDERS_COUNT - 1 - feederId; // 从0-5映射到5-0

    uint16_t value;
    if (safeModbusRead(ADDR_BIN_BLOCK_START + invertedFeederId * 6 + binId, 1, &value) == 1)
    {
        return (parseInt(&value) == 0); // 0表示启用,1表示屏蔽
    }
    return false;
}

bool ABBPLCClient::reconnectModbus() const
{
    if (!m_ctx) return false;

    m_ctx->modbus_close();

    // 连接
    for (int retry = 0; retry < 5; retry++)
    {
        if (m_ctx->modbus_connect())
        {
            LOG(INFO) << "PLC重连成功 (尝试" << retry + 1 << "次)";
            return true;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(200 * (retry + 1)));
    }

    LOG(ERROR) << "PLC重连失败: " << m_ctx->error_msg;
    return false;
}

int ABBPLCClient::safeModbusRead(int addr, int nb, uint16_t* dest, int maxRetries) const
{
    if (!running)
    {
        LOG(WARNING) << addr << "PLC客户端未运行,跳过读取操作";
        return -1;
    }

    // 检查缓存是否有效
    if (!modbusCache || !modbusCache->isValid())
    {
        // if (!updateModbusCache()) {
        //     return -1;
        // }
    }

    // 从缓存中获取数据
    if (addr + nb <= TOTAL_REGISTERS)
    {
        // std::lock_guard<std::mutex> lock(modbusCacheMutex);
        std::copy(modbusCache->registers.begin() + addr,
                  modbusCache->registers.begin() + addr + nb,
                  dest);
        return nb;
    }

    LOG(ERROR) << "请求的地址范围超出缓存范围: addr=" << addr << ", nb=" << nb;
    return -1;
}

int ABBPLCClient::safeModbusWrite(int addr, int nb, const uint16_t* src, int maxRetries) const
{
    if (!running) return -1;

    for (int retry = 0; retry < maxRetries; retry++)
    {
        if (!m_ctx) return -1;

        int result;
        {
            // Modbus写操作需要独占锁
            std::lock_guard<std::mutex> lock(modbusMutex);
            result = m_ctx->modbus_write_registers(addr, nb, src);
        }

        if (result == 0) return nb;

        if (result == BAD_CON)
        {
            if (reconnectModbus()) continue;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }
    return -1;
}

void ABBPLCClient::heartbeatLoop()
{
    while (running)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    }
}

bool ABBPLCClient::initModbus()
{
    try
    {
        m_ctx = std::make_unique<modbus>(_ip, _port);
        if (!m_ctx)
        {
            LOG(ERROR) << "Failed to create modbus context";
            return false;
        }

        if (!m_ctx->modbus_connect())
        {
            LOG(WARNING) << "Initial connection failed: " << m_ctx->error_msg;
            return false;
        }

        return true;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "Modbus initialization failed: " << e.what();
        return false;
    }
}

bool ABBPLCClient::readSystemStatus(bool& isAuto, bool& hasErr) const
{
    try
    {
        uint16_t autoStatus, errorStatus;

        // 读取自动状态
        if (safeModbusRead(ADDR_AUTO_MODE, 1, &autoStatus) != 1)
        {
            LOG(ERROR) << "读取自动状态失败";
            return false;
        }
        isAuto = (parseInt(&autoStatus) == 1);

        // 读取故障状态
        if (safeModbusRead(ADDR_ERROR_STATE, 1, &errorStatus) != 1)
        {
            LOG(ERROR) << "读取故障状态失败";
            return false;
        }
        hasErr = (parseInt(&errorStatus) == 1);

        return true;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "读取系统状态异常: " << e.what();
        return false;
    }
}


void ABBPLCClient::setMesServer(std::shared_ptr<MesServer> server)
{
    mesServer = server;
}

bool ABBPLCClient::checkWeightConditions(float bucketWeight,
                                       float binCurrentWeight,
                                       float binMinStartWeight,
                                       float binTargetWeight) const
{
    if (bucketWeight <= 0 || bucketWeight < minWeight)
    {
       LOG(INFO) << "料桶重量不足: " << bucketWeight;
        return false;
    }

    // // 修改这里：只要当前重量小于目标重量就允许继续加料
    // if (binCurrentWeight >= binTargetWeight)
    // {
    //     LOG(INFO) << "料仓已达到目标重量: 当前=" << binCurrentWeight
    //             << ", 目标=" << binTargetWeight;
    //     return false;
    // }



    return true;
}

bool ABBPLCClient::checkMaterialMatch(const std::string& bucketBatchNumber,
                                      const std::string& binSandType) const
{
    bool matched = bucketBatchNumber == binSandType;
    if (!matched)
    {
        LOG(INFO) << "料号不匹配: 料桶=" << bucketBatchNumber
            << ",料仓=" << binSandType;
    }
    return matched;
}

std::string ABBPLCClient::registersToString(const uint16_t* regs, int count) const
{
    std::string result;
    for (int i = 0; i < count; i++)
    {
        char high = (regs[i] >> 8) & 0xFF;
        char low = regs[i] & 0xFF;
        if (isprint(high)) result += high;
        if (isprint(low)) result += low;
    }
    // 移除末尾的空字符
    result.erase(std::remove(result.begin(), result.end(), '\0'), result.end());
    return result;
}

bool ABBPLCClient::writeAreaMaterialTypes()
{
    static std::chrono::steady_clock::time_point lastUpdateTime;
    static std::vector<std::string> lastMaterials;

    // 检查距离上次更新是否超过10秒
    auto now = std::chrono::steady_clock::now();
    if (std::chrono::duration_cast<std::chrono::seconds>(now - lastUpdateTime).count() < 10)
    {
        VLOG(2) << "物料类型更新间隔未到";
        return false;
    }

    if (!mesServer)
    {
        LOG(ERROR) << "MES服务器未连接";
        return false;
    }

    try
    {
        const auto& areas = mesServer->getAreas();

        // 检查是否所有区域都有数据
        bool allAreasHaveData = true;
        for (char areaId = 'A'; areaId <= 'V'; areaId++)
        {
            auto it = areas.find(areaId);
            if (it == areas.end() || !it->second || it->second->buckets.empty())
            {
                LOG(WARNING) << "区域 " << areaId << " 数据不完整或为空";
                allAreasHaveData = false;
                break;
            }
        }

        if (!allAreasHaveData)
        {
            LOG(WARNING) << "某些区域数据不完整,跳过更新";
            return false;
        }

        std::vector<std::string> uniqueMaterials;
        // 收集所有唯一的物料类型
        for (const auto& [areaId, areaData] : areas)
        {
            for (const auto& bucket : areaData->buckets)
            {
                if (!bucket.batchNumber.empty())
                {
                    if (std::find(uniqueMaterials.begin(), uniqueMaterials.end(),
                                  bucket.batchNumber) == uniqueMaterials.end())
                    {
                        uniqueMaterials.push_back(bucket.batchNumber);
                    }
                }
            }
        }

        if (uniqueMaterials.empty())
        {
            return false;
        }

        // 检查物料类型是否有变化
        bool materialsChanged = (lastMaterials != uniqueMaterials);
        if (!materialsChanged)
        {
            VLOG(2) << "物料类型未发生变化";
            return false;
        }

        // 计算需要的总寄存器数量
        // 每个STRING[32]需要17个寄存器，最多MATERIAL_TYPES_COUNT个物料类型
        const int TOTAL_REGS = MATERIAL_TYPES_COUNT * 17;
        std::vector<uint16_t> allRegisters(TOTAL_REGS, 0);

        // 准备所有物料类型的数据
        int numTypes = std::min(static_cast<int>(uniqueMaterials.size()), MATERIAL_TYPES_COUNT);
        for (int i = 0; i < numTypes; i++)
        {
            // 为每个STRING[32]准备17个寄存器
            packString32(uniqueMaterials[i], &allRegisters[i * 17]);
        }

        // 一次性写入所有数据
        if (safeModbusWrite(ADDR_MATERIAL_TYPE_START, TOTAL_REGS, allRegisters.data()) != TOTAL_REGS)
        {
            LOG(ERROR) << "批量写入物料类型失败";
            return false;
        }

        // 更新缓存和时间戳
        lastMaterials = uniqueMaterials;
        lastUpdateTime = now;

        LOG(INFO) << "成功批量写入" << numTypes << "个物料类型";
        return true;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "写入物料类型异常: " << e.what();
        return false;
    }
}

bool ABBPLCClient::updateModbusCache() const
{
    if (!m_ctx)
    {
        LOG(ERROR) << "Modbus context is null";
        return false;
    }

    if (!modbusCache)
    {
        modbusCache = std::make_shared<ModbusCache>();
        modbusCache->registers.resize(TOTAL_REGISTERS);
    }

    auto startTime = std::chrono::steady_clock::now();
    bool success = true;

    try
    {
        const int BATCH_SIZE = 125; // Modbus TCP最大读取长度
        const int DELAY_MS = 25; // 每批读取后的延时

        // 按批次读取所有寄存器
        for (int addr = 0; addr < TOTAL_REGISTERS; addr += BATCH_SIZE)
        {
            int count = std::min(BATCH_SIZE, TOTAL_REGISTERS - addr);

            {
                std::lock_guard<std::mutex> lock(modbusMutex);
                if (m_ctx->modbus_read_holding_registers(
                    addr,
                    count,
                    modbusCache->registers.data() + addr) != 0)
                {
                    LOG(ERROR) << "批量读取寄存器失败: "
                        << "起始地址=" << addr
                        << ", 数量=" << count;
                    success = false;
                    break;
                }
            }

            // 添加延时避免PLC负载过高
            std::this_thread::sleep_for(std::chrono::milliseconds(DELAY_MS));
        }

        if (success)
        {
            modbusCache->update();
            auto endTime = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                endTime - startTime).count();
            // LOG(INFO) << "Modbus缓存完整更新完成，耗时: " << duration << "ms";
        }
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "更新Modbus缓存异常: " << e.what();
        success = false;
    }

    return success;
}

// 新增方法：从PLC读取区域数据
std::shared_ptr<AreaData> ABBPLCClient::getPLCAreaData(char areaId)
{
    try
    {
        // 检查区域是否启用
        if (!isAreaEnabled(areaId))
        {
            return nullptr;
        }

        // 计算区域基地址
        int baseAddr = ADDR_AREA_START + (areaId - 'A') * (4 * 17);  // 每个区域4个桶，每个桶17个寄存器
        int baseWeightAddr = ADDR_BUCKET_WEIGHT_START + (areaId - 'A') * (4 * 2);  // 每个区域4个桶，每个桶2个寄存器(float)
        int materialTypeAddr = ADDR_MATERIAL_TYPE_START + (areaId - 'A') * 17;

        auto areaData = std::make_shared<AreaData>();

        // 读取桶数量
        uint16_t bucketCount = 0;
        if (safeModbusRead(ADDR_BUCKET_COUNT_START + (areaId - 'A'), 1, &bucketCount) != 1)
        {
            LOG(WARNING) << "读取区域" << areaId << "桶数量失败";
            return nullptr;
        }

        // 限制桶数量在合理范围内(最多4个桶)
        bucketCount = std::min(bucketCount, static_cast<uint16_t>(4));

        int startBucketIndex = 4 - bucketCount;

        // 读取区域物料类型
        uint16_t materialTypeRegs[17];
        if (safeModbusRead(materialTypeAddr, 17, materialTypeRegs) == 17)
        {
            areaData->sandType = parseString32(materialTypeRegs);
            VLOG(2) << "区域" << areaId << "物料类型: " << areaData->sandType;
        }
        else
        {
            LOG(WARNING) << "读取区域" << areaId << "物料类型失败";
        }

        // 添加详细的地址日志
        // LOG(INFO) << "区域 " << areaId << " 地址信息:"
        //           << "\n  基地址(批号)=" << baseAddr
        //           << "\n  重量地址=" << baseWeightAddr
        //           << "\n  物料类型地址=" << materialTypeAddr
        //           << "\n  桶数量=" << bucketCount;

        // 一次性读取所有桶的数据（只读取需要的桶）
        std::vector<uint16_t> batchRegs(bucketCount * 17);
        std::vector<uint16_t> weightRegs(bucketCount * 2);

        // 批量读取批号数据（从指定位置开始）
        if (safeModbusRead(baseAddr + (startBucketIndex * 17), bucketCount * 17, batchRegs.data()) != bucketCount * 17)
        {
            LOG(WARNING) << "批量读取区域" << areaId << "批号数据失败";
            return nullptr;
        }

        // 批量读取重量数据（从指定位置开始）
        if (safeModbusRead(baseWeightAddr + (startBucketIndex * 2), bucketCount * 2, weightRegs.data()) != bucketCount * 2)
        {
            LOG(WARNING) << "批量读取区域" << areaId << "重量数据失败";
            return nullptr;
        }

        // 打印原始数据
        // LOG(INFO) << "区域 " << areaId << " 原始数据:";
        for (uint16_t i = 0; i < bucketCount; i++)
        {
            // LOG(INFO) << "  桶" << (startBucketIndex + i + 1) << ":"  // 显示实际桶号
            //          << "\n    批号地址=" << (baseAddr + (startBucketIndex + i) * 17)
            //          << "\n    重量地址=" << (baseWeightAddr + (startBucketIndex + i) * 2)
            //          << "\n    批号寄存器[0]=" << batchRegs[i * 17]
            //          << "\n    重量寄存器[0,1]=" << weightRegs[i * 2] << "," << weightRegs[i * 2 + 1];
        }
        for (int i = 0; i < startBucketIndex; i++)
        {
            AreaData::Bucket bucket;
            bucket.batchNumber="";
            bucket.weight=0.0;
            areaData->buckets.push_back(bucket);
        }

        // 按顺序处理每个桶的数据
        for (uint16_t i = 0; i < bucketCount; i++)
        {
            AreaData::Bucket bucket;

            // 解析批号
            bucket.batchNumber = parseString32(&batchRegs[i * 17]);

            // 解析重量
            bucket.weight = parseReal(&weightRegs[i * 2]);

            // 检查数据有效性
            if (bucket.weight < 0 || bucket.weight > maxWeight)
            {
                LOG(WARNING) << "区域" << areaId << "桶" << (i + 1)
                            << "重量异常: " << bucket.weight;
                bucket.weight = 0;
            }

            areaData->buckets.push_back(bucket);

            // LOG(INFO) << "区域" << areaId << "桶" << (i + 1)
            //          << " 批号=" << bucket.batchNumber
            //          << " 重量=" << bucket.weight;
        }

        return areaData;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "从PLC读取区域" << areaId << "数据异常: " << e.what();
        return nullptr;
    }
}

// 新增方法：从PLC读取所有区域数据
std::map<char, std::shared_ptr<AreaData>> ABBPLCClient::getAllPLCAreaData()
{
    std::map<char, std::shared_ptr<AreaData>> areas;

    try
    {
        // 遍历所有区域(A-V)
        for (char areaId = 'A'; areaId <= 'V'; areaId++)
        {
            auto areaData = getPLCAreaData(areaId);
            if (areaData)
            {
                areas[areaId] = areaData;
            }
        }
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "获取所有区域数据异常: " << e.what();
    }

    return areas;
}

// 优化 STRING[1] 写入方法
bool ABBPLCClient::writeString1(int addr, char value) const
{
    try
    {
        uint16_t string1_registers[2];
        packString1(value, string1_registers);

        if (safeModbusWrite(addr, 2, string1_registers) != 2)
        {
            LOG(ERROR) << "写入STRING[1]失败: " << value;
            return false;
        }
        return true;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "写入STRING[1]异常: " << e.what();
        return false;
    }
}


// 写入 INT 类型
bool ABBPLCClient::writeInt(int addr, int16_t value) const
{
    try
    {
        uint16_t int_register;
        packInt(value, &int_register);

        if (safeModbusWrite(addr, 1, &int_register) != 1)
        {
            LOG(ERROR) << "写入INT失败: " << value;
            return false;
        }
        return true;
    }
    catch (const std::exception& e)
    {
        LOG(ERROR) << "写入INT异常: " << e.what();
        return false;
    }
}
