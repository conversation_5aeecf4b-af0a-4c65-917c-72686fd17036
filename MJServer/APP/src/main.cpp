#include <QApplication>
#include <QMessageBox>
#include <glog.h>
#include "main_window.h"
#include "license_checker.h"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // 初始化 glog
    google::InitGoogleLogging(argv[0]);
    FLAGS_log_dir = "C:\\fuxios\\install\\x64-install\\dev\\data\\";
    FLAGS_alsologtostderr = true;
    FLAGS_colorlogtostderr = true;
    
    // 检查许可证
    if (!LicenseChecker::isLicensed()) {
        QMessageBox::critical(nullptr, "错误", 
            "许可证验证失败!\n"
            "请确保您使用的是授权设备。\n"
            "如需帮助，请联系技术支持。");
        google::ShutdownGoogleLogging();
        return 1;
    }
    
    MainWindow window;
    window.show();
    
    int ret = app.exec();
    
    google::ShutdownGoogleLogging();
    return ret;
} 