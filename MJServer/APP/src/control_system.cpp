#include "control_system.h"
#include "system_status.h"
#include <iostream>
#include <chrono>
#include <glog.h>

ControlSystem::ControlSystem() : running(false), emergencyStopFlag(false) {
    try {
        mesServer = std::make_shared<MesServer>();
        abbClient = std::make_shared<ABBPLCClient>();
        executor = std::make_shared<Fuxi::Common::Executor>(6);
        
        abbClient->setMesServer(mesServer);
        
    } catch (const std::exception& e) {
        notifyStatus("系统初始化失败: " + std::string(e.what()));
        throw;
    }
}

ControlSystem::~ControlSystem() {
    stop();
}

bool ControlSystem::start() {
    if (running) {
        return true;
    }
    
    try {
        // 先启动MES服务器
        if (!mesServer->start()) {
            notifyStatus("MES服务器启动失败");
            return false;
        }
        notifyStatus("MES服务器启动成功");

        
        if (!abbClient->start()) {
            running = false;
            notifyStatus("ABB上料机启动失败");
            return false;
        }
        running = true;
        notifyStatus("ABB上料机启动成功");

        // 设置任务日志回调
        abbClient->setTaskLogCallback([this](const std::string& logMsg) {
            notifyTaskLog(logMsg);
        });

        
        emergencyStopFlag = false;
        executor->postTask([this]() {
            run();
        });
        return true;
    } catch (const std::exception& e) {
        running = false;
        notifyStatus("系统启动失败: " + std::string(e.what()));
        return false;
    }
}

void ControlSystem::stop() {
    running = false;
    notifyStatus("系统已停止");
}

void ControlSystem::emergencyStop() {
    emergencyStopFlag = true;
    notifyStatus("系统紧急停止");
    stop();
}

bool ControlSystem::isMesConnected() const {
    return mesServer && mesServer->isConnected() && mesServer->isHeartbeatOK();
}

bool ControlSystem::isPlcConnected() const {
    return  abbClient->isHeartbeatOK();
}

bool ControlSystem::hasError() const {
    return  abbClient->hasError();
}

bool ControlSystem::isAutoMode() const {
    return  abbClient->isSystemAuto();
}

std::shared_ptr<TaskStatus> ControlSystem::getTaskStatus() const {
    return abbClient->getTaskStatus();
}

std::vector<std::shared_ptr<FeederStatus>> ControlSystem::getFeederStatus() const {
    return abbClient->getFeederStatus();
}

bool ControlSystem::isAreaEnabled(char areaId) const {
    return abbClient->isAreaEnabled(areaId);
}

void ControlSystem::run() {
    while(running && !emergencyStopFlag) {
        try {
            // 使用executor异步执行每个状态的获取和通知
            
            // 系统模式状态
            executor->postTask([this]() {
                try {
                    bool autoMode = isAutoMode();
                    bool error = hasError();
                    notifySystemMode(autoMode, error);
                } catch(const std::exception& e) {
                    LOG(ERROR) << "获取系统模式状态异常: " << e.what();
                }
            });
            
            // 心跳状态
            executor->postTask([this]() {
                try {
                    bool mesConn = isMesConnected();
                    bool plcConn = isPlcConnected();
                    notifyHeartbeat(mesConn, plcConn);
                } catch(const std::exception& e) {
                    LOG(ERROR) << "获取心跳状态异常: " << e.what();
                }
            });
            
            // 任务状态
            executor->postTask([this]() {
                try {
                    if(auto taskStatus = getTaskStatus()) {
                        notifyTaskStatus(taskStatus);
                    }
                } catch(const std::exception& e) {
                    LOG(ERROR) << "获��任务状态异常: " << e.what();
                }
            });
            
            // 投料机状态
            executor->postTask([this]() {
                try {
                    auto feederStatus = getFeederStatus();
                    if(!feederStatus.empty()) {
                        notifyFeederStatus(feederStatus);
                    }
                } catch(const std::exception& e) {
                    LOG(ERROR) << "获取投料机状态异常: " << e.what();
                }
            });
            
            // 区域状态
            executor->postTask([this]() {
                try {
                    if(mesServer) {
                        notifyAreaStatus(abbClient->getAllPLCAreaData());
                        // notifyAreaStatus(mesServer->getAreas());
                    }
                } catch(const std::exception& e) {
                    LOG(ERROR) << "获取区域状态异常: " << e.what();
                }
            });
            
        } catch(const std::exception& e) {
            LOG(ERROR) << "运行主循环异常: " << e.what();
        }
        
        // 主循环延时，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(10000));
    }
}

void ControlSystem::notifyStatus(const std::string& status) {
    if (statusCallback) {
        statusCallback(status);
    }
    LOG(INFO) << status;
}

std::shared_ptr<TaskInfo> ControlSystem::getCurrentTask() const {
    return std::make_shared<TaskInfo>();  // 返回空任务
}
