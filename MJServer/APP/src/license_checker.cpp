#include "license_checker.h"
#include <glog.h>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cstring>
#include <iomanip>
#ifdef _WIN32
#include <winsock2.h>
#include <iphlpapi.h>
#include <windows.h>
#pragma comment(lib, "iphlpapi.lib")
#pragma comment(lib, "ws2_32.lib")
#else
#include <net/if.h>
#include <sys/ioctl.h>
#include <unistd.h>
#include <netinet/in.h>
#include <dirent.h>
#endif

bool LicenseChecker::isLicensed() {
    try {
        auto allowedMacs = getAllowedMacs();
        auto systemMacs = getSystemMacs();
        
        return checkMacAddress(allowedMacs, systemMacs);
    } catch (const std::exception& e) {
        LOG(ERROR) << "许可证检查失败: " << e.what();
        return false;
    }
}

std::vector<std::string> LicenseChecker::getAllowedMacs() {
    // 这里存放允许的MAC地址列表
    return {
        "34:23:87:d6:96:a6",  // 添加允许的MAC地址
        "00:e0:1d:a4:80:9f",   // 可以添加多个MAC地址
        "00:e0:1d:a4:80:9e",
        "00:e0:20:5e:c0:c1",
        "00:e0:20:5e:c0:c0"
    };
}

std::vector<std::string> LicenseChecker::getSystemMacs() {
    std::vector<std::string> macs;
    
#ifdef _WIN32
    // Windows implementation using IP Helper API
    ULONG bufferSize = 0;
    DWORD result = GetAdaptersInfo(nullptr, &bufferSize);
    
    if (result == ERROR_BUFFER_OVERFLOW) {
        std::vector<BYTE> buffer(bufferSize);
        PIP_ADAPTER_INFO adapterInfo = reinterpret_cast<PIP_ADAPTER_INFO>(buffer.data());
        
        result = GetAdaptersInfo(adapterInfo, &bufferSize);
        if (result == NO_ERROR) {
            PIP_ADAPTER_INFO adapter = adapterInfo;
            while (adapter) {
                if (adapter->Type == MIB_IF_TYPE_ETHERNET || adapter->Type == IF_TYPE_IEEE80211) {
                    std::stringstream ss;
                    for (UINT i = 0; i < adapter->AddressLength; i++) {
                        if (i > 0) ss << ":";
                        ss << std::hex << std::setfill('0') << std::setw(2) 
                           << static_cast<int>(adapter->Address[i]);
                    }
                    std::string mac = ss.str();
                    std::transform(mac.begin(), mac.end(), mac.begin(), ::toupper);
                    macs.push_back(mac);
                    LOG(INFO) << "发现网络接口: " << adapter->AdapterName << ", MAC: " << mac;
                }
                adapter = adapter->Next;
            }
        } else {
            LOG(ERROR) << "获取网络适配器信息失败: " << result;
        }
    } else {
        LOG(ERROR) << "获取缓冲区大小失败: " << result;
    }
#else
    // Linux implementation
    DIR *dir;
    struct dirent *ent;
    dir = opendir("/sys/class/net");
    if (dir != nullptr) {
        while ((ent = readdir(dir)) != nullptr) {
            if (ent->d_name[0] != '.') {
                std::string ifname = ent->d_name;
                std::string path = "/sys/class/net/" + ifname + "/address";
                
                std::ifstream mac_file(path);
                std::string mac;
                if (mac_file.good() && std::getline(mac_file, mac)) {
                    std::transform(mac.begin(), mac.end(), mac.begin(), ::toupper);
                    
                    std::string type_path = "/sys/class/net/" + ifname + "/type";
                    std::ifstream type_file(type_path);
                    std::string type;
                    std::getline(type_file, type);
                    
                    if (type != "772") {
                        macs.push_back(mac);
                        LOG(INFO) << "发现网络接口: " << ifname << ", MAC: " << mac;
                    }
                }
            }
        }
        closedir(dir);
    } else {
        LOG(ERROR) << "无法打开 /sys/class/net 目录: " << strerror(errno);
    }
#endif
    
    LOG(INFO) << "系统中发现 " << macs.size() << " 个有效MAC地址";
    return macs;
}

bool LicenseChecker::checkMacAddress(const std::vector<std::string>& allowedMacs,
                                   const std::vector<std::string>& systemMacs) {
    for (const auto& systemMac : systemMacs) {
        for (const auto& allowedMac : allowedMacs) {
#ifdef _WIN32
            if (_stricmp(systemMac.c_str(), allowedMac.c_str()) == 0) {
#else
            if (strcasecmp(systemMac.c_str(), allowedMac.c_str()) == 0) {
#endif
                LOG(INFO) << "找到匹配的MAC地址";
                return true;
            }
        }
    }
    
    LOG(WARNING) << "未找到匹配的MAC地址";
    return false;
} 