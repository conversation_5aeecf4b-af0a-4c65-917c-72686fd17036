PLC--上位机,,地址,,
Static,,,,
MES通讯心跳,Int,0,,
机器人当次所取料砂批号,String[32],1,,
当次倒料时间_1,String[20],18,,
当次倒入投料机号码,Int,29,,
当次倒入上述投料机料仓号,Int,30,,
当次倒入料砂重量_1,Real,31,,
A区域托盘物料号_1,String[32],33,,
A区域托盘物料号_2,String[32],50,,
A区域托盘物料号_3,String[32],67,,
A区域托盘物料号_4,String[32],84,,
B区域托盘物料号_1,String[32],101,,
B区域托盘物料号_2,String[32],118,,
B区域托盘物料号_3,String[32],135,,
B区域托盘物料号_4,String[32],152,,
C区域托盘物料号_1,String[32],169,,
C区域托盘物料号_2,String[32],186,,
C区域托盘物料号_3,String[32],203,,
C区域托盘物料号_4,String[32],220,,
D区域托盘物料号_1,String[32],237,,
D区域托盘物料号_2,String[32],254,,
D区域托盘物料号_3,String[32],271,,
D区域托盘物料号_4,String[32],288,,
E区域托盘物料号_1,String[32],305,,
E区域托盘物料号_2,String[32],322,,
E区域托盘物料号_3,String[32],339,,
E区域托盘物料号_4,String[32],356,,
F区域托盘物料号_1,String[32],373,,
F区域托盘物料号_2,String[32],390,,
F区域托盘物料号_3,String[32],407,,
F区域托盘物料号_4,String[32],424,,
G区域托盘物料号_1,String[32],441,,
G区域托盘物料号_2,String[32],458,,
G区域托盘物料号_3,String[32],475,,
G区域托盘物料号_4,String[32],492,,
H区域托盘物料号_1,String[32],509,,
H区域托盘物料号_2,String[32],526,,
H区域托盘物料号_3,String[32],543,,
H区域托盘物料号_4,String[32],560,,
I区域托盘物料号_1,String[32],577,,
I区域托盘物料号_2,String[32],594,,
I区域托盘物料号_3,String[32],611,,
I区域托盘物料号_4,String[32],628,,
J区域托盘物料号_1,String[32],645,,
J区域托盘物料号_2,String[32],662,,
J区域托盘物料号_3,String[32],679,,
J区域托盘物料号_4,String[32],696,,
K区域托盘物料号_1,String[32],713,,
K区域托盘物料号_2,String[32],730,,
K区域托盘物料号_3,String[32],747,,
K区域托盘物料号_4,String[32],764,,
L区域托盘物料号_1,String[32],781,,
L区域托盘物料号_2,String[32],798,,
L区域托盘物料号_3,String[32],815,,
L区域托盘物料号_4,String[32],832,,
M区域托盘物料号_1,String[32],849,,
M区域托盘物料号_2,String[32],866,,
M区域托盘物料号_3,String[32],883,,
M区域托盘物料号_4,String[32],900,,
N区域托盘物料号_1,String[32],917,,
N区域托盘物料号_2,String[32],934,,
N区域托盘物料号_3,String[32],951,,
N区域托盘物料号_4,String[32],968,,
O区域托盘物料号_1,String[32],985,,
O区域托盘物料号_2,String[32],1002,,
O区域托盘物料号_3,String[32],1019,,
O区域托盘物料号_4,String[32],1036,,
P区域托盘物料号_1,String[32],1053,,
P区域托盘物料号_2,String[32],1070,,
P区域托盘物料号_3,String[32],1087,,
P区域托盘物料号_4,String[32],1104,,
Q区域托盘物料号_1,String[32],1121,,
Q区域托盘物料号_2,String[32],1138,,
Q区域托盘物料号_3,String[32],1155,,
Q区域托盘物料号_4,String[32],1172,,
R区域托盘物料号_1,String[32],1189,,
R区域托盘物料号_2,String[32],1206,,
R区域托盘物料号_3,String[32],1223,,
R区域托盘物料号_4,String[32],1240,,
S区域托盘物料号_1,String[32],1257,,
S区域托盘物料号_2,String[32],1274,,
S区域托盘物料号_3,String[32],1291,,
S区域托盘物料号_4,String[32],1308,,
T区域托盘物料号_1,String[32],1325,,
T区域托盘物料号_2,String[32],1342,,
T区域托盘物料号_3,String[32],1359,,
T区域托盘物料号_4,String[32],1376,,
U区域托盘物料号_1,String[32],1393,,
U区域托盘物料号_2,String[32],1410,,
U区域托盘物料号_3,String[32],1427,,
U区域托盘物料号_4,String[32],1444,,
V区域托盘物料号_1,String[32],1461,,
V区域托盘物料号_2,String[32],1478,,
V区域托盘物料号_3,String[32],1495,,
V区域托盘物料号_4,String[32],1512,,
A区域桶数量,Int,1529,,
B区域桶数量,Int,1530,,
C区域桶数量,Int,1531,,
D区域桶数量,Int,1532,,
E区域桶数量,Int,1533,,
F区域桶数量,Int,1534,,
G区域桶数量,Int,1535,,
H区域桶数量,Int,1536,,
I区域桶数量,Int,1537,,
J区域桶数量,Int,1538,,
K区域桶数量,Int,1539,,
L区域桶数量,Int,1540,,
M区域桶数量,Int,1541,,
N区域桶数量,Int,1542,,
O区域桶数量,Int,1543,,
P区域桶数量,Int,1544,,
Q区域桶数量,Int,1545,,
R区域桶数量,Int,1546,,
S区域桶数量,Int,1547,,
T区域桶数量,Int,1548,,
U区域桶数量,Int,1549,,
V区域桶数量,Int,1550,,
A区域桶_1重量,Real,1551,,
A区域桶_2重量,Real,1553,,
A区域桶_3重量,Real,1555,,
A区域桶_4重量,Real,1557,,
B区域桶_1重量,Real,1559,,
B区域桶_2重量,Real,1561,,
B区域桶_3重量,Real,1563,,
B区域桶_4重量,Real,1565,,
C区域桶_1重量,Real,1567,,
C区域桶_2重量,Real,1569,,
C区域桶_3重量,Real,1571,,
C区域桶_4重量,Real,1573,,
D区域桶_1重量,Real,1575,,
D区域桶_2重量,Real,1577,,
D区域桶_3重量,Real,1579,,
D区域桶_4重量,Real,1581,,
E区域桶_1重量,Real,1583,,
E区域桶_2重量,Real,1585,,
E区域桶_3重量,Real,1587,,
E区域桶_4重量,Real,1589,,
F区域桶_1重量,Real,1591,,
F区域桶_2重量,Real,1593,,
F区域桶_3重量,Real,1595,,
F区域桶_4重量,Real,1597,,
G区域桶_1重量,Real,1599,,
G区域桶_2重量,Real,1601,,
G区域桶_3重量,Real,1603,,
G区域桶_4重量,Real,1605,,
H区域桶_1重量,Real,1607,,
H区域桶_2重量,Real,1609,,
H区域桶_3重量,Real,1611,,
H区域桶_4重量,Real,1613,,
I区域桶_1重量,Real,1615,,
I区域桶_2重量,Real,1617,,
I区域桶_3重量,Real,1619,,
I区域桶_4重量,Real,1621,,
J区域桶_1重量,Real,1623,,
J区域桶_2重量,Real,1625,,
J区域桶_3重量,Real,1627,,
J区域桶_4重量,Real,1629,,
K区域桶_1重量,Real,1631,,
K区域桶_2重量,Real,1633,,
K区域桶_3重量,Real,1635,,
K区域桶_4重量,Real,1637,,
L区域桶_1重量,Real,1639,,
L区域桶_2重量,Real,1641,,
L区域桶_3重量,Real,1643,,
L区域桶_4重量,Real,1645,,
M区域桶_1重量,Real,1647,,
M区域桶_2重量,Real,1649,,
M区域桶_3重量,Real,1651,,
M区域桶_4重量,Real,1653,,
N区域桶_1重量,Real,1655,,
N区域桶_2重量,Real,1657,,
N区域桶_3重量,Real,1659,,
N区域桶_4重量,Real,1661,,
O区域桶_1重量,Real,1663,,
O区域桶_2重量,Real,1665,,
O区域桶_3重量,Real,1667,,
O区域桶_4重量,Real,1669,,
P区域桶_1重量,Real,1671,,
P区域桶_2重量,Real,1673,,
P区域桶_3重量,Real,1675,,
P区域桶_4重量,Real,1677,,
Q区域桶_1重量,Real,1679,,
Q区域桶_2重量,Real,1681,,
Q区域桶_3重量,Real,1683,,
Q区域桶_4重量,Real,1685,,
R区域桶_1重量,Real,1687,,
R区域桶_2重量,Real,1689,,
R区域桶_3重量,Real,1691,,
R区域桶_4重量,Real,1693,,
S区域桶_1重量,Real,1695,,
S区域桶_2重量,Real,1697,,
S区域桶_3重量,Real,1699,,
S区域桶_4重量,Real,1701,,
T区域桶_1重量,Real,1703,,
T区域桶_2重量,Real,1705,,
T区域桶_3重量,Real,1707,,
T区域桶_4重量,Real,1709,,
U区域桶_1重量,Real,1711,,
U区域桶_2重量,Real,1713,,
U区域桶_3重量,Real,1715,,
U区域桶_4重量,Real,1717,,
V区域桶_1重量,Real,1719,,
V区域桶_2重量,Real,1721,,
V区域桶_3重量,Real,1723,,
V区域桶_4重量,Real,1725,,
A区域是否为空桶区,Int,1727,,
B区域是否为空桶区,Int,1728,,
C区域是否为空桶区,Int,1729,,
D区域是否为空桶区,Int,1730,,
E区域是否为空桶区,Int,1731,,
F区域是否为空桶区,Int,1732,,
G区域是否为空桶区,Int,1733,,
H区域是否为空桶区,Int,1734,,
I区域是否为空桶区,Int,1735,,
J区域是否为空桶区,Int,1736,,
K区域是否为空桶区,Int,1737,,
L区域是否为空桶区,Int,1738,,
M区域是否为空桶区,Int,1739,,
N区域是否为空桶区,Int,1740,,
O区域是否为空桶区,Int,1741,,
P区域是否为空桶区,Int,1742,,
Q区域是否为空桶区,Int,1743,,
R区域是否为空桶区,Int,1744,,
S区域是否为空桶区,Int,1745,,
T区域是否为空桶区,Int,1746,,
U区域是否为空桶区,Int,1747,,
V区域是否为空桶区,Int,1748,,
投料机1是否屏蔽,Int,1749,,
投料机1料仓1是否屏蔽,Int,1750,,
投料机1料仓2是否屏蔽,Int,1751,,
投料机1料仓3是否屏蔽,Int,1752,,
投料机1料仓4是否屏蔽,Int,1753,,
投料机1料仓5是否屏蔽,Int,1754,,
投料机2是否屏蔽,Int,1755,,
投料机2料仓1是否屏蔽,Int,1756,,
投料机2料仓2是否屏蔽,Int,1757,,
投料机2料仓3是否屏蔽,Int,1758,,
投料机2料仓4是否屏蔽,Int,1759,,
投料机2料仓5是否屏蔽,Int,1760,,
投料机3是否屏蔽,Int,1761,,
投料机3料仓1是否屏蔽,Int,1762,,
投料机3料仓2是否屏蔽,Int,1763,,
投料机3料仓3是否屏蔽,Int,1764,,
投料机3料仓4是否屏蔽,Int,1765,,
投料机3料仓5是否屏蔽,Int,1766,,
投料机4是否屏蔽,Int,1767,,
投料机4料仓1是否屏蔽,Int,1768,,
投料机4料仓2是否屏蔽,Int,1769,,
投料机4料仓3是否屏蔽,Int,1770,,
投料机4料仓4是否屏蔽,Int,1771,,
投料机4料仓5是否屏蔽,Int,1772,,
投料机5是否屏蔽,Int,1773,,
投料机5料仓1是否屏蔽,Int,1774,,
投料机5料仓2是否屏蔽,Int,1775,,
投料机5料仓3是否屏蔽,Int,1776,,
投料机5料仓4是否屏蔽,Int,1777,,
投料机5料仓5是否屏蔽,Int,1778,,
投料机6是否屏蔽,Int,1779,,
投料机6料仓1是否屏蔽,Int,1780,,
投料机6料仓2是否屏蔽,Int,1781,,
投料机6料仓3是否屏蔽,Int,1782,,
投料机6料仓4是否屏蔽,Int,1783,,
投料机6料仓5是否屏蔽,Int,1784,,
备用36,Int,1785,,
备用26,Int,1786,,
备用27,Int,1787,,
备用28,Int,1788,,
备用30,Int,1789,,
备用31,Int,1790,,
备用32,Int,1791,,
备用33,Int,1792,,
备用34,Int,1793,,
备用29,Int,1794,,
备用30_1,Int,1795,,
备用31_1,Int,1796,,
备用32_1,Int,1797,,
备用33_1,Int,1798,,
备用34_1,Int,1799,,
备用35,Int,1800,,
备用24,Int,1801,,
备用2_1,Int,1802,,
备用2_2,Int,1803,,
备用2_3,Int,1804,,
备用2_4,Int,1805,,
备用3_1,Int,1806,,
备用4_1,Int,1807,,
备用5_1,Int,1808,,
ROTO_投料机,Int,1809,,取值：1 到 6
ROTO_料口编号,Int,1810,,取值：1到 22
ROTO_取桶区域,String[1],1811,,取值：A到 V
倒料准备好,Int,1813,,1 准备好 0 没有准备好
倒料中,Int,1814,,1 倒料中 
ROTO_倒料完成,Int,1815,,1 倒料完成
ROTO_当前桶剩余重量,Real,1816,,
1_1号口当前重量,Real,1818,,
1_2号口当前重量,Real,1820,,
1_3号口当前重量,Real,1822,,
1_4号口当前重量,Real,1824,,
1_5号口当前重量,Real,1826,,
2_1号口当前重量,Real,1828,,
2_2号口当前重量,Real,1830,,
2_3号口当前重量,Real,1832,,
2_4号口当前重量,Real,1834,,
2_5号口当前重量,Real,1836,,
3_1号口当前重量,Real,1838,,
3_2号口当前重量,Real,1840,,
3_3号口当前重量,Real,1842,,
3_4号口当前重量,Real,1844,,
3_5号口当前重量,Real,1846,,
4_1号口当前重量,Real,1848,,
4_2号口当前重量,Real,1850,,
4_3号口当前重量,Real,1852,,
4_4号口当前重量,Real,1854,,
4_5号口当前重量,Real,1856,,
5_1号口当前重量,Real,1858,,
5_2号口当前重量,Real,1860,,
5_3号口当前重量,Real,1862,,
5_4号口当前重量,Real,1864,,
5_5号口当前重量,Real,1866,,
6_1号口当前重量,Real,1868,,
6_2号口当前重量,Real,1870,,
6_3号口当前重量,Real,1872,,
6_4号口当前重量,Real,1874,,
6_5号口当前重量,Real,1876,,
投料机1起加重量料仓1,Real,1878,,
投料机1起加重量料仓2,Real,1880,,
投料机1起加重量料仓3,Real,1882,,
投料机1起加重量料仓4,Real,1884,,
投料机1起加重量料仓5,Real,1886,,
投料机2起加重量料仓1,Real,1888,,
投料机2起加重量料仓2,Real,1890,,
投料机2起加重量料仓3,Real,1892,,
投料机2起加重量料仓4,Real,1894,,
投料机2起加重量料仓5,Real,1896,,
投料机3起加重量料仓1,Real,1898,,
投料机3起加重量料仓2,Real,1900,,
投料机3起加重量料仓3,Real,1902,,
投料机3起加重量料仓4,Real,1904,,
投料机3起加重量料仓5,Real,1906,,
投料机4起加重量料仓1,Real,1908,,
投料机4起加重量料仓2,Real,1910,,
投料机4起加重量料仓3,Real,1912,,
投料机4起加重量料仓4,Real,1914,,
投料机4起加重量料仓5,Real,1916,,
投料机5起加重量料仓1,Real,1918,,
投料机5起加重量料仓2,Real,1920,,
投料机5起加重量料仓3,Real,1922,,
投料机5起加重量料仓4,Real,1924,,
投料机5起加重量料仓5,Real,1926,,
投料机6起加重量料仓1,Real,1928,,
投料机6起加重量料仓2,Real,1930,,
投料机6起加重量料仓3,Real,1932,,
投料机6起加重量料仓4,Real,1934,,
投料机6起加重量料仓5,Real,1936,,
投料机1料仓加入量设定值1,Real,1938,,
投料机1料仓加入量设定值2,Real,1940,,
投料机1料仓加入量设定值3,Real,1942,,
投料机1料仓加入量设定值4,Real,1944,,
投料机1料仓加入量设定值5,Real,1946,,
投料机2料仓加入量设定值1,Real,1948,,
投料机2料仓加入量设定值2,Real,1950,,
投料机2料仓加入量设定值3,Real,1952,,
投料机2料仓加入量设定值4,Real,1954,,
投料机2料仓加入量设定值5,Real,1956,,
投料机3料仓加入量设定值1,Real,1958,,
投料机3料仓加入量设定值2,Real,1960,,
投料机3料仓加入量设定值3,Real,1962,,
投料机3料仓加入量设定值4,Real,1964,,
投料机3料仓加入量设定值5,Real,1966,,
投料机4料仓加入量设定值1,Real,1968,,
投料机4料仓加入量设定值2,Real,1970,,
投料机4料仓加入量设定值3,Real,1972,,
投料机4料仓加入量设定值4,Real,1974,,
投料机4料仓加入量设定值5,Real,1976,,
投料机5料仓加入量设定值1,Real,1978,,
投料机5料仓加入量设定值2,Real,1980,,
投料机5料仓加入量设定值3,Real,1982,,
投料机5料仓加入量设定值4,Real,1984,,
投料机5料仓加入量设定值5,Real,1986,,
投料机6料仓加入量设定值1,Real,1988,,
投料机6料仓加入量设定值2,Real,1990,,
投料机6料仓加入量设定值3,Real,1992,,
投料机6料仓加入量设定值4,Real,1994,,
投料机6料仓加入量设定值5,Real,1996,,
自动状态,Int,1998,,
是否故障,Int,1999,,
A区域托盘是否启用,Int,2000,,
B区域托盘是否启用,Int,2001,,
C区域托盘是否启用,Int,2002,,
D区域托盘是否启用,Int,2003,,
E区域托盘是否启用,Int,2004,,
F区域托盘是否启用,Int,2005,,
G区域托盘是否启用,Int,2006,,
H区域托盘是否启用,Int,2007,,
I区域托盘是否启用,Int,2008,,
J区域托盘是否启用,Int,2009,,
K区域托盘是否启用,Int,2010,,
L区域托盘是否启用,Int,2011,,
M区域托盘是否启用,Int,2012,,
N区域托盘是否启用,Int,2013,,
O区域托盘是否启用,Int,2014,,
P区域托盘是否启用,Int,2015,,
Q区域托盘是否启用,Int,2016,,
L区域托盘是否启用,Int,2017,,
S区域托盘是否启用,Int,2018,,
T区域托盘是否启用,Int,2019,,
U区域托盘是否启用,Int,2020,,
V区域托盘是否启用,Int,2021,,
