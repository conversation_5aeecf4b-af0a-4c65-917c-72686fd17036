MODULE TestModule
    !***********************************************************
    ! MJServer投料系统 - ABB机器人简化测试脚本
    ! 版本: 2.0
    ! 描述: 简化的通讯测试程序，适配80字符限制
    !***********************************************************
    
    ! 测试常量
    CONST string TEST_SERVER_IP := "*************";
    CONST num TEST_SERVER_PORT := 1025;
    
    ! 测试变量
    VAR socketdev test_socket;
    
    !***********************************************************
    ! 简化测试主程序
    !***********************************************************
    PROC TestMain()
        TPWrite "=== Simple Communication Test ===";
        
        ! 测试1: 连接测试
        TestConnection;
        
        ! 测试2: 命令测试
        TestCommands;
        
        ! 测试3: 位置测试
        TestMovement;
        
        TPWrite "=== Test Completed ===";
    ENDPROC
    
    !***********************************************************
    ! 测试1: 连接测试（机器人作为客户端）
    !***********************************************************
    PROC TestConnection()
        TPWrite "Test 1: Client Connection";

        TRY
            SocketCreate test_socket;
            SocketConnect test_socket, TEST_SERVER_IP, TEST_SERVER_PORT;
            TPWrite "✓ Connected to MJServer";

            ! 发送机器人就绪消息
            SocketSend test_socket \Str := "0#";
            TPWrite "✓ Ready message sent";

            SocketClose test_socket;

        CATCH
            TPWrite "✗ Failed: " + NumToStr(ERRNO, 0);
        ENDTRY

        WaitTime 1;
    ENDPROC
    
    !***********************************************************
    ! 测试2: 命令测试
    !***********************************************************
    PROC TestCommands()
        TPWrite "Test 2: Commands";
        
        TRY
            SocketCreate test_socket;
            SocketConnect test_socket, TEST_SERVER_IP, TEST_SERVER_PORT;
            
            ! 测试心跳
            SocketSend test_socket \Str := "8#";
            TPWrite "✓ Heartbeat sent";
            WaitTime 0.5;
            
            ! 测试状态查询
            SocketSend test_socket \Str := "3#";
            TPWrite "✓ Status query sent";
            WaitTime 0.5;
            
            ! 测试投料命令
            SocketSend test_socket \Str := "1,1,2#";
            TPWrite "✓ Feeding command sent";
            
            SocketClose test_socket;
            TPWrite "✓ Commands test completed";
            
        CATCH
            TPWrite "✗ Commands failed: " + NumToStr(ERRNO, 0);
        ENDTRY
        
        WaitTime 1;
    ENDPROC
    
    !***********************************************************
    ! 测试3: 位置移动
    !***********************************************************
    PROC TestMovement()
        TPWrite "Test 3: Movement";
        
        TRY
            ! 定义测试位置
            VAR robtarget pos1 := [[500, 0, 600], [1, 0, 0, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
            VAR robtarget pos2 := [[600, 100, 500], [1, 0, 0, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
            
            ! 移动测试
            TPWrite "Moving to pos1...";
            MoveJ pos1, v1000, z100, tool0;
            TPWrite "✓ Reached pos1";
            
            TPWrite "Moving to pos2...";
            MoveJ pos2, v500, z50, tool0;
            TPWrite "✓ Reached pos2";
            
            ! 返回
            TPWrite "Returning home...";
            MoveJ pos1, v1000, z100, tool0;
            TPWrite "✓ Movement test completed";
            
        CATCH
            TPWrite "✗ Movement failed: " + NumToStr(ERRNO, 0);
        ENDTRY
        
        WaitTime 1;
    ENDPROC
    
    !***********************************************************
    ! 快速连接测试（客户端模式）
    !***********************************************************
    PROC QuickTest()
        TPWrite "=== Quick Client Test ===";

        TRY
            SocketCreate test_socket;
            SocketConnect test_socket, TEST_SERVER_IP, TEST_SERVER_PORT;
            TPWrite "✓ Connected to server";

            ! 发送心跳
            SocketSend test_socket \Str := "8#";
            TPWrite "✓ Heartbeat sent";

            SocketClose test_socket;
            TPWrite "✓ Quick test completed";

        CATCH
            TPWrite "✗ Quick test failed";
        ENDTRY
    ENDPROC

ENDMODULE
