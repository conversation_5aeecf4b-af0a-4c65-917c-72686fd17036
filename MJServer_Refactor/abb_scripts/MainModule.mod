MODULE MainModule
    !***********************************************************
    ! MJServer投料系统 - ABB机器人简化通讯脚本
    ! 版本: 2.0
    ! 描述: 采用简化的Socket通讯方式，类似RoboticLaserMarking
    !***********************************************************

    ! 常量定义
    CONST num SERVER_PORT := 1025;
    CONST string SERVER_IP := "*************";

    ! 通讯变量
    VAR socketdev client_socket;
    VAR bool connected := FALSE;
    VAR bool system_running := TRUE;

    ! 命令和状态变量
    VAR string received_cmd := "";
    VAR string robot_status := "IDLE";
    VAR num task_progress := 0;
    VAR bool emergency_stop := FALSE;

    ! 任务参数
    VAR num feeder_id := 0;
    VAR num bin_id := 0;

    ! 位置定义
    CONST robtarget HOME_POS := [[500, 0, 600], [1, 0, 0, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];

    ! 投料位置数组
    VAR robtarget feeder_pos{6, 5};
    
    !***********************************************************
    ! 主程序入口
    !***********************************************************
    PROC main()
        ! 初始化
        Initialize;

        ! 主循环
        WHILE system_running DO
            ! 连接检查
            IF NOT connected THEN
                ConnectToServer;
            ENDIF

            ! 处理命令
            IF connected THEN
                ReceiveCommand;
                ProcessCommand;
                SendStatus;
            ENDIF

            ! 检查紧急停止
            CheckEmergencyStop;

            WaitTime 0.1;
        ENDWHILE

        ! 清理
        Cleanup;
    ENDPROC
    
    !***********************************************************
    ! 初始化
    !***********************************************************
    PROC Initialize()
        ! 初始化位置
        InitPositions;

        ! 移动到初始位置
        MoveJ HOME_POS, v1000, z100, tool0;

        ! 初始化状态
        robot_status := "IDLE";
        task_progress := 0;
        emergency_stop := FALSE;

        TPWrite "Robot initialized";
    ENDPROC
    
    !***********************************************************
    ! 初始化位置
    !***********************************************************
    PROC InitPositions()
        ! 投料机1位置
        feeder_pos{1, 1} := [[800, -400, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{1, 2} := [[800, -300, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{1, 3} := [[800, -200, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{1, 4} := [[800, -100, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{1, 5} := [[800, 0, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];

        ! 投料机2位置
        feeder_pos{2, 1} := [[600, -400, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{2, 2} := [[600, -300, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{2, 3} := [[600, -200, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{2, 4} := [[600, -100, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];
        feeder_pos{2, 5} := [[600, 0, 300], [0, 0.707, 0.707, 0], [0, 0, 0, 0], [9E+09, 9E+09, 9E+09, 9E+09, 9E+09, 9E+09]];

        TPWrite "Positions initialized";
    ENDPROC
    
    !***********************************************************
    ! 连接到服务器（机器人作为客户端）
    !***********************************************************
    PROC ConnectToServer()
        TRY
            SocketCreate client_socket;
            SocketConnect client_socket, SERVER_IP, SERVER_PORT;
            connected := TRUE;
            TPWrite "Connected to MJServer at " + SERVER_IP;

            ! 发送机器人就绪消息
            SocketSend client_socket \Str := "0#";
            TPWrite "Robot ready message sent";

        CATCH
            connected := FALSE;
            TPWrite "Connection failed: " + NumToStr(ERRNO, 0);
            WaitTime 2;
        ENDTRY
    ENDPROC

    !***********************************************************
    ! 接收命令
    !***********************************************************
    PROC ReceiveCommand()
        VAR string buffer;
        VAR bool data_available;

        TRY
            SocketGetStatus client_socket \Data := data_available;
            IF data_available THEN
                SocketReceive client_socket \Str := buffer \Time := 1;
                received_cmd := buffer;
            ENDIF
        CATCH
            connected := FALSE;
        ENDTRY
    ENDPROC

    !***********************************************************
    ! 处理命令
    !***********************************************************
    PROC ProcessCommand()
        VAR string cmd_type;
        VAR num feeder_num;
        VAR num bin_num;
        VAR num weight_val;

        IF StrLen(received_cmd) > 0 THEN
            ! 解析命令 - 格式: "命令码,参数1,参数2,参数3#"
            cmd_type := ExtractCmdType(received_cmd);

            TEST cmd_type
                CASE "1":  ! 开始投料
                    ParseTaskParams received_cmd, feeder_num, bin_num;
                    StartFeedingTask feeder_num, bin_num;
                CASE "2":  ! 停止投料
                    StopCurrentTask;
                CASE "3":  ! 状态查询
                    ! 状态查询在SendStatus中处理
                CASE "4":  ! 紧急停止
                    EmergencyStopTask;
                CASE "5":  ! 复位
                    ResetRobot;
                DEFAULT:
                    ! 未知命令
            ENDTEST

            received_cmd := "";  ! 清空命令
        ENDIF
    ENDPROC
    
    !***********************************************************
    ! 发送连接消息
    !***********************************************************
    PROC SendConnectionMessage()
        VAR string message;
        message := "{\"type\":\"ROBOT_CONNECTED\",\"timestamp\":\"" + CTime() + "\",\"robot_id\":\"ABB_Robot_1\"}";
        SendMessage message;
    ENDPROC
    
    !***********************************************************
    ! 处理通讯
    !***********************************************************
    PROC HandleCommunication()
        VAR string received_message;
        VAR bool message_available;
        
        TRY
            ! 检查是否有消息
            message_available := SocketGetStatus(server_socket);
            
            IF message_available THEN
                SocketReceive server_socket \Str := received_message;
                ProcessReceivedMessage received_message;
            ENDIF
            
        CATCH
            TPWrite "Communication error occurred";
            is_connected := FALSE;
        ENDTRY
    ENDPROC
    
    !***********************************************************
    ! 处理接收到的消息
    !***********************************************************
    PROC ProcessReceivedMessage(string message)
        VAR string msg_type;
        
        ! 解析消息类型 (简化的JSON解析)
        msg_type := ExtractMessageType(message);
        
        TEST msg_type
            CASE "START_TASK":
                HandleStartTaskMessage message;
            CASE "STOP_TASK":
                HandleStopTaskMessage;
            CASE "EMERGENCY_STOP":
                HandleEmergencyStopMessage;
            CASE "GET_STATUS":
                SendStatusMessage;
            CASE "HEARTBEAT_REQUEST":
                SendHeartbeatResponse;
            DEFAULT:
                TPWrite "Unknown message type: " + msg_type;
        ENDTEST
    ENDPROC
    
    !***********************************************************
    ! 提取消息类型
    !***********************************************************
    FUNC string ExtractMessageType(string message)
        VAR string result := "";
        VAR num start_pos;
        VAR num end_pos;
        
        ! 查找 "type":"xxx" 模式
        start_pos := StrFind(message, 1, "\"type\":\"");
        IF start_pos > 0 THEN
            start_pos := start_pos + 8;  ! 跳过 "type":"
            end_pos := StrFind(message, start_pos, "\"");
            IF end_pos > start_pos THEN
                result := StrPart(message, start_pos, end_pos - start_pos);
            ENDIF
        ENDIF
        
        RETURN result;
    ENDFUNC
    
    !***********************************************************
    ! 处理开始任务消息
    !***********************************************************
    PROC HandleStartTaskMessage(string message)
        ! 解析任务参数
        current_task_id := ExtractStringValue(message, "task_id");
        target_feeder_id := ExtractNumValue(message, "feeder_id");
        target_bin_id := ExtractNumValue(message, "bin_id");
        target_weight := ExtractNumValue(message, "weight");
        target_area := ExtractStringValue(message, "area_id");
        
        ! 设置任务状态为执行中
        current_task_status := 1;
        
        TPWrite "Task started: " + current_task_id + " Feeder:" + NumToStr(target_feeder_id, 0) + " Bin:" + NumToStr(target_bin_id, 0);
        
        ! 发送任务开始确认
        SendTaskStartedMessage;
    ENDPROC
    
    !***********************************************************
    ! 处理停止任务消息
    !***********************************************************
    PROC HandleStopTaskMessage()
        current_task_status := 0;
        current_task_id := "";
        TPWrite "Task stopped by server";

        ! 停止当前运动并返回安全位置
        StopMove;
        MoveJ home_position, v1000, z100, tool0;

        SendTaskStoppedMessage;
    ENDPROC

    !***********************************************************
    ! 处理紧急停止消息
    !***********************************************************
    PROC HandleEmergencyStopMessage()
        emergency_stop := TRUE;
        current_task_status := 3;
        TPWrite "EMERGENCY STOP activated by server";

        ! 立即停止所有运动
        StopMove \Quick;

        SendEmergencyStopAckMessage;
    ENDPROC

    !***********************************************************
    ! 执行当前任务
    !***********************************************************
    PROC ExecuteCurrentTask()
        IF current_task_status = 1 AND NOT emergency_stop THEN
            TRY
                ! 执行投料任务
                PerformFeedingTask;

                ! 任务完成
                current_task_status := 2;
                SendTaskCompletedMessage;

            CATCH
                ! 任务执行错误
                current_task_status := 3;
                last_error := "Task execution failed: " + NumToStr(ERRNO, 0);
                TPWrite last_error;
                SendTaskErrorMessage;
            ENDTRY
        ENDIF
    ENDPROC

    !***********************************************************
    ! 执行投料任务
    !***********************************************************
    PROC PerformFeedingTask()
        VAR robtarget approach_pos;
        VAR robtarget feeding_pos;

        ! 获取目标位置
        feeding_pos := feeder_positions{target_feeder_id, target_bin_id};
        approach_pos := feeding_pos;
        approach_pos.trans.z := approach_pos.trans.z + 100;  ! 接近位置高100mm

        ! 发送状态更新
        SendTaskStatusMessage "MOVING_TO_POSITION";

        ! 移动到接近位置
        MoveJ approach_pos, v1000, z50, tool0;

        ! 发送状态更新
        SendTaskStatusMessage "AT_FEEDING_POSITION";

        ! 移动到投料位置
        MoveL feeding_pos, v200, z10, tool0;

        ! 发送状态更新
        SendTaskStatusMessage "FEEDING";

        ! 模拟投料动作 (根据实际需要调整)
        WaitTime 2;  ! 投料等待时间

        ! 发送状态更新
        SendTaskStatusMessage "RETURNING_BUCKET";

        ! 返回接近位置
        MoveL approach_pos, v200, z10, tool0;

        ! 返回初始位置
        MoveJ home_position, v1000, z100, tool0;

        TPWrite "Feeding task completed for Feeder " + NumToStr(target_feeder_id, 0) + " Bin " + NumToStr(target_bin_id, 0);
    ENDPROC

    !***********************************************************
    ! 发送心跳
    !***********************************************************
    PROC SendHeartbeat()
        STATIC clock heartbeat_timer;

        IF ClkRead(heartbeat_timer) > HEARTBEAT_INTERVAL THEN
            SendHeartbeatMessage;
            ClkReset heartbeat_timer;
        ENDIF
    ENDPROC

    !***********************************************************
    ! 检查紧急停止
    !***********************************************************
    PROC CheckEmergencyStop()
        ! 检查硬件紧急停止按钮
        IF DInput(diEmergencyStop) = 1 THEN
            IF NOT emergency_stop THEN
                emergency_stop := TRUE;
                current_task_status := 3;
                TPWrite "Hardware emergency stop activated";
                SendEmergencyStopMessage;
            ENDIF
        ENDIF
    ENDPROC

    !***********************************************************
    ! 消息发送函数
    !***********************************************************
    PROC SendHeartbeatMessage()
        VAR string message;
        message := "{\"type\":\"HEARTBEAT\",\"timestamp\":\"" + CTime() + "\",\"status\":" + NumToStr(current_task_status, 0) + ",\"emergency_stop\":" + BoolToStr(emergency_stop) + "}";
        SendMessage message;
    ENDPROC

    PROC SendTaskStartedMessage()
        VAR string message;
        message := "{\"type\":\"TASK_STARTED\",\"task_id\":\"" + current_task_id + "\",\"timestamp\":\"" + CTime() + "\"}";
        SendMessage message;
    ENDPROC

    PROC SendTaskCompletedMessage()
        VAR string message;
        message := "{\"type\":\"TASK_COMPLETED\",\"task_id\":\"" + current_task_id + "\",\"timestamp\":\"" + CTime() + "\"}";
        SendMessage message;
    ENDPROC

    PROC SendTaskErrorMessage()
        VAR string message;
        message := "{\"type\":\"TASK_ERROR\",\"task_id\":\"" + current_task_id + "\",\"error\":\"" + last_error + "\",\"timestamp\":\"" + CTime() + "\"}";
        SendMessage message;
    ENDPROC

    PROC SendTaskStatusMessage(string step)
        VAR string message;
        message := "{\"type\":\"TASK_STATUS\",\"task_id\":\"" + current_task_id + "\",\"step\":\"" + step + "\",\"timestamp\":\"" + CTime() + "\"}";
        SendMessage message;
    ENDPROC

    PROC SendStatusMessage()
        VAR string message;
        message := "{\"type\":\"STATUS_RESPONSE\",\"task_status\":" + NumToStr(current_task_status, 0) + ",\"current_task\":\"" + current_task_id + "\",\"emergency_stop\":" + BoolToStr(emergency_stop) + ",\"timestamp\":\"" + CTime() + "\"}";
        SendMessage message;
    ENDPROC

    !***********************************************************
    ! 辅助函数
    !***********************************************************
    FUNC string ExtractStringValue(string message, string key)
        VAR string result := "";
        VAR string search_pattern;
        VAR num start_pos;
        VAR num end_pos;

        search_pattern := "\"" + key + "\":\"";
        start_pos := StrFind(message, 1, search_pattern);
        IF start_pos > 0 THEN
            start_pos := start_pos + StrLen(search_pattern);
            end_pos := StrFind(message, start_pos, "\"");
            IF end_pos > start_pos THEN
                result := StrPart(message, start_pos, end_pos - start_pos);
            ENDIF
        ENDIF

        RETURN result;
    ENDFUNC

    FUNC num ExtractNumValue(string message, string key)
        VAR num result := 0;
        VAR string search_pattern;
        VAR string value_str;
        VAR num start_pos;
        VAR num end_pos;

        search_pattern := "\"" + key + "\":";
        start_pos := StrFind(message, 1, search_pattern);
        IF start_pos > 0 THEN
            start_pos := start_pos + StrLen(search_pattern);
            end_pos := StrFind(message, start_pos, ",");
            IF end_pos = 0 THEN
                end_pos := StrFind(message, start_pos, "}");
            ENDIF
            IF end_pos > start_pos THEN
                value_str := StrPart(message, start_pos, end_pos - start_pos);
                result := StrToNum(value_str);
            ENDIF
        ENDIF

        RETURN result;
    ENDFUNC

    FUNC string BoolToStr(bool value)
        IF value THEN
            RETURN "true";
        ELSE
            RETURN "false";
        ENDIF
    ENDFUNC

    !***********************************************************
    ! 发送消息
    !***********************************************************
    PROC SendMessage(string message)
        TRY
            IF is_connected THEN
                SocketSend server_socket \Str := message;
            ENDIF
        CATCH
            TPWrite "Failed to send message";
            is_connected := FALSE;
        ENDTRY
    ENDPROC

    !***********************************************************
    ! 清理系统
    !***********************************************************
    PROC CleanupSystem()
        TRY
            IF is_connected THEN
                SocketClose server_socket;
            ENDIF
        CATCH
            ! 忽略清理错误
        ENDTRY

        TPWrite "System cleanup completed";
    ENDPROC

    !***********************************************************
    ! 发送状态
    !***********************************************************
    PROC SendStatus()
        VAR string status_msg;

        TRY
            IF connected THEN
                ! 构建状态消息 - 格式: "状态码,进度#"
                status_msg := GetStatusCode() + "," + NumToStr(task_progress, 0) + "#";
                SocketSend client_socket \Str := status_msg;
            ENDIF
        CATCH
            connected := FALSE;
        ENDTRY
    ENDPROC

    !***********************************************************
    ! 检查紧急停止
    !***********************************************************
    PROC CheckEmergencyStop()
        ! 检查硬件紧急停止
        IF DInput(diEmergencyStop) = 1 THEN
            emergency_stop := TRUE;
            robot_status := "ERROR";
            StopMove \Quick;
        ENDIF
    ENDPROC

    !***********************************************************
    ! 辅助函数
    !***********************************************************
    FUNC string ExtractCmdType(string cmd)
        VAR string result := "";
        VAR num comma_pos;

        comma_pos := StrFind(cmd, 1, ",");
        IF comma_pos > 0 THEN
            result := StrPart(cmd, 1, comma_pos - 1);
        ELSE
            ! 没有逗号，查找#
            VAR num hash_pos;
            hash_pos := StrFind(cmd, 1, "#");
            IF hash_pos > 0 THEN
                result := StrPart(cmd, 1, hash_pos - 1);
            ENDIF
        ENDIF

        RETURN result;
    ENDFUNC

    PROC ParseTaskParams(string cmd, INOUT num feeder_id, INOUT num bin_id)
        VAR string params;
        VAR num start_pos;
        VAR num end_pos;

        ! 跳过命令码
        start_pos := StrFind(cmd, 1, ",") + 1;
        end_pos := StrFind(cmd, start_pos, "#");

        IF start_pos > 1 AND end_pos > start_pos THEN
            params := StrPart(cmd, start_pos, end_pos - start_pos);

            ! 解析参数: feeder_id,bin_id
            VAR num comma1;
            comma1 := StrFind(params, 1, ",");

            IF comma1 > 0 THEN
                feeder_id := StrToNum(StrPart(params, 1, comma1 - 1));
                bin_id := StrToNum(StrPart(params, comma1 + 1, StrLen(params) - comma1));
            ENDIF
        ENDIF
    ENDPROC

    FUNC string GetStatusCode()
        TEST robot_status
            CASE "IDLE":
                RETURN "0";
            CASE "RUNNING":
                RETURN "1";
            CASE "COMPLETE":
                RETURN "2";
            CASE "ERROR":
                RETURN "3";
            DEFAULT:
                RETURN "0";
        ENDTEST
    ENDFUNC

    !***********************************************************
    ! 任务执行函数
    !***********************************************************
    PROC StartFeedingTask(num feeder_num, num bin_num)
        feeder_id := feeder_num;
        bin_id := bin_num;
        robot_status := "RUNNING";
        task_progress := 0;

        TPWrite "Starting feeding: F" + NumToStr(feeder_id, 0) + " B" + NumToStr(bin_id, 0);

        ! 执行投料动作
        ExecuteFeedingSequence;
    ENDPROC

    PROC ExecuteFeedingSequence()
        TRY
            ! 移动到投料位置
            task_progress := 20;
            MoveJ feeder_pos{feeder_id, bin_id}, v1000, z100, tool0;

            ! 到达投料位置，发送开始投料信号给投料机
            task_progress := 40;
            SetDO doFeederActive{feeder_id}, 1;  ! 启动投料机
            TPWrite "Feeder " + NumToStr(feeder_id, 0) + " started";

            ! 等待投料完成（通过输入信号检测）
            task_progress := 60;
            WaitDI diFeederComplete{feeder_id}, 1 \MaxTime := 30;  ! 等待投料机完成信号，最多30秒

            ! 停止投料机
            task_progress := 80;
            SetDO doFeederActive{feeder_id}, 0;  ! 停止投料机
            TPWrite "Feeder " + NumToStr(feeder_id, 0) + " stopped";

            ! 返回初始位置
            task_progress := 90;
            MoveJ HOME_POS, v1000, z100, tool0;

            ! 投料完成
            task_progress := 100;
            robot_status := "COMPLETE";

            TPWrite "Feeding completed";

        CATCH
            robot_status := "ERROR";
            task_progress := 0;
            SetDO doFeederActive{feeder_id}, 0;  ! 确保停止投料机
            TPWrite "Feeding failed: " + NumToStr(ERRNO, 0);
        ENDTRY
    ENDPROC

    PROC StopCurrentTask()
        robot_status := "IDLE";
        task_progress := 0;

        ! 停止所有投料机
        SetDO doFeeder1Active, 0;
        SetDO doFeeder2Active, 0;
        SetDO doFeeder3Active, 0;
        SetDO doFeeder4Active, 0;
        SetDO doFeeder5Active, 0;
        SetDO doFeeder6Active, 0;

        StopMove;
        MoveJ HOME_POS, v1000, z100, tool0;
        TPWrite "Feeding stopped";
    ENDPROC

    PROC EmergencyStopTask()
        emergency_stop := TRUE;
        robot_status := "ERROR";
        StopMove \Quick;
        TPWrite "Emergency stop activated";
    ENDPROC

    PROC ResetRobot()
        emergency_stop := FALSE;
        robot_status := "IDLE";
        task_progress := 0;
        MoveJ HOME_POS, v1000, z100, tool0;
        TPWrite "Robot reset";
    ENDPROC

ENDMODULE
