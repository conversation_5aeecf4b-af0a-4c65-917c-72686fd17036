#include "simple_feeder_client.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <modbus.h>

// libmodbus包装类
class modbus {
public:
    std::string error_msg;

    modbus(const std::string& ip, int port) : ip_(ip), port_(port), ctx_(nullptr) {}

    ~modbus() {
        if (ctx_) {
            ::modbus_close(ctx_);
            ::modbus_free(ctx_);
        }
    }

    bool modbus_connect() {
        if (ctx_) {
            ::modbus_close(ctx_);
            ::modbus_free(ctx_);
        }

        ctx_ = ::modbus_new_tcp(ip_.c_str(), port_);
        if (!ctx_) {
            error_msg = "Failed to create modbus context";
            return false;
        }

        if (::modbus_connect(ctx_) == -1) {
            error_msg = ::modbus_strerror(errno);
            ::modbus_free(ctx_);
            ctx_ = nullptr;
            return false;
        }

        return true;
    }

    void modbus_close() {
        if (ctx_) {
            ::modbus_close(ctx_);
            ::modbus_free(ctx_);
            ctx_ = nullptr;
        }
    }

    int modbus_read_holding_registers(int address, int count, uint16_t* buffer) {
        if (!ctx_) {
            error_msg = "Not connected";
            return -1;
        }

        int result = ::modbus_read_registers(ctx_, address, count, buffer);
        if (result == -1) {
            error_msg = ::modbus_strerror(errno);
        }
        return result == count ? 0 : -1;
    }

    int modbus_write_registers(int address, int count, const uint16_t* buffer) {
        if (!ctx_) {
            error_msg = "Not connected";
            return -1;
        }

        int result = ::modbus_write_registers(ctx_, address, count, buffer);
        if (result == -1) {
            error_msg = ::modbus_strerror(errno);
        }
        return result == count ? 0 : -1;
    }

private:
    std::string ip_;
    int port_;
    modbus_t* ctx_;
};

SimpleFeederClient::SimpleFeederClient(const FeederConfig& config)
    : config(config), connected(false) {
    clearError();
}

SimpleFeederClient::~SimpleFeederClient() {
    disconnect();
}

bool SimpleFeederClient::connect() {
    if (connected) {
        return true;
    }
    
    try {
        // 创建modbus客户端
        modbusClient = std::make_unique<modbus>(config.ip, config.port);
        
        // 尝试连接
        if (!modbusClient->modbus_connect()) {
            setError("Failed to connect to feeder " + std::to_string(config.id) + 
                    " at " + config.ip + ":" + std::to_string(config.port) + 
                    " - " + modbusClient->error_msg);
            return false;
        }
        
        connected = true;
        clearError();
        return true;
        
    } catch (const std::exception& e) {
        setError("Exception during connection: " + std::string(e.what()));
        return false;
    }
}

void SimpleFeederClient::disconnect() {
    if (modbusClient && connected) {
        modbusClient->modbus_close();
    }
    connected = false;
    modbusClient.reset();
}

bool SimpleFeederClient::readHeartbeat(uint16_t& heartbeat) {
    if (!connected || !modbusClient) {
        setError("Not connected to feeder");
        return false;
    }
    
    return readSingleRegister(Constants::ADDR_HEARTBEAT_READ, heartbeat);
}

bool SimpleFeederClient::readBinWeights(std::vector<float>& weights) {
    if (!connected || !modbusClient) {
        setError("Not connected to feeder");
        return false;
    }
    
    weights.clear();
    weights.resize(Constants::BINS_PER_FEEDER);
    
    // 读取5个料仓的重量，每个重量占用2个寄存器(REAL类型)
    uint16_t buffer[Constants::BINS_PER_FEEDER * 2];
    if (!readRegisters(Constants::ADDR_BIN_WEIGHT_START, Constants::BINS_PER_FEEDER * 2, buffer)) {
        return false;
    }
    
    // 转换为浮点数
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        weights[i] = registersToFloat(buffer[i * 2], buffer[i * 2 + 1]);
    }
    
    return true;
}

bool SimpleFeederClient::readBinBlockStatus(std::vector<bool>& blockStatus) {
    if (!connected || !modbusClient) {
        setError("Not connected to feeder");
        return false;
    }
    
    uint16_t blockReg;
    if (!readSingleRegister(Constants::ADDR_BIN_BLOCK, blockReg)) {
        return false;
    }
    
    blockStatus.clear();
    blockStatus.resize(Constants::BINS_PER_FEEDER);
    
    // 解析位状态
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        blockStatus[i] = (blockReg & (1 << i)) != 0;
    }
    
    return true;
}

bool SimpleFeederClient::writeHeartbeat(uint16_t heartbeat) {
    if (!connected || !modbusClient) {
        setError("Not connected to feeder");
        return false;
    }
    
    return writeSingleRegister(Constants::ADDR_HEARTBEAT_WRITE, heartbeat);
}

bool SimpleFeederClient::writeBinFeedingStatus(const std::vector<bool>& feedingStatus) {
    if (!connected || !modbusClient) {
        setError("Not connected to feeder");
        return false;
    }
    
    if (feedingStatus.size() != Constants::BINS_PER_FEEDER) {
        setError("Invalid feeding status array size");
        return false;
    }
    
    // 将布尔数组转换为位字段
    uint16_t statusReg = 0;
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        if (feedingStatus[i]) {
            statusReg |= (1 << i);
        }
    }
    
    return writeSingleRegister(Constants::ADDR_BIN_FEEDING, statusReg);
}

bool SimpleFeederClient::writeBinTargetWeights(const std::vector<float>& targetWeights) {
    if (!connected || !modbusClient) {
        setError("Not connected to feeder");
        return false;
    }
    
    if (targetWeights.size() != Constants::BINS_PER_FEEDER) {
        setError("Invalid target weights array size");
        return false;
    }
    
    // 转换浮点数为寄存器值
    uint16_t buffer[Constants::BINS_PER_FEEDER * 2];
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        floatToRegisters(targetWeights[i], buffer[i * 2], buffer[i * 2 + 1]);
    }
    
    return writeRegisters(Constants::ADDR_BIN_TARGET_WEIGHT_START, Constants::BINS_PER_FEEDER * 2, buffer);
}

bool SimpleFeederClient::readRegisters(int address, int count, uint16_t* buffer) {
    try {
        int result = modbusClient->modbus_read_holding_registers(address, count, buffer);
        if (result != 0) {
            setError("Failed to read registers at address " + std::to_string(address) + 
                    ": " + modbusClient->error_msg);
            return false;
        }
        return true;
    } catch (const std::exception& e) {
        setError("Exception reading registers: " + std::string(e.what()));
        return false;
    }
}

bool SimpleFeederClient::writeRegisters(int address, int count, const uint16_t* buffer) {
    try {
        int result = modbusClient->modbus_write_registers(address, count, buffer);
        if (result != 0) {
            setError("Failed to write registers at address " + std::to_string(address) + 
                    ": " + modbusClient->error_msg);
            return false;
        }
        return true;
    } catch (const std::exception& e) {
        setError("Exception writing registers: " + std::string(e.what()));
        return false;
    }
}

bool SimpleFeederClient::readSingleRegister(int address, uint16_t& value) {
    return readRegisters(address, 1, &value);
}

bool SimpleFeederClient::writeSingleRegister(int address, uint16_t value) {
    return writeRegisters(address, 1, &value);
}

float SimpleFeederClient::registersToFloat(uint16_t reg1, uint16_t reg2) {
    // 假设高位在前，低位在后的IEEE 754格式
    uint32_t combined = (static_cast<uint32_t>(reg1) << 16) | reg2;
    return *reinterpret_cast<float*>(&combined);
}

void SimpleFeederClient::floatToRegisters(float value, uint16_t& reg1, uint16_t& reg2) {
    uint32_t combined = *reinterpret_cast<uint32_t*>(&value);
    reg1 = static_cast<uint16_t>(combined >> 16);
    reg2 = static_cast<uint16_t>(combined & 0xFFFF);
}

void SimpleFeederClient::setError(const std::string& error) {
    lastError = error;
    std::cerr << "[FeederClient " << config.id << "] Error: " << error << std::endl;
}

void SimpleFeederClient::clearError() {
    lastError.clear();
}

// FeederTester实现
bool FeederTester::testConnection(const FeederConfig& config) {
    SimpleFeederClient client(config);
    bool result = client.connect();
    printTestResult("Connection Test", result, client.getLastError());
    return result;
}

bool FeederTester::testHeartbeat(SimpleFeederClient& client) {
    uint16_t heartbeat;
    bool readResult = client.readHeartbeat(heartbeat);
    if (!readResult) {
        printTestResult("Heartbeat Read Test", false, client.getLastError());
        return false;
    }
    
    // 尝试写入心跳
    uint16_t testHeartbeat = (heartbeat == 0) ? 1 : 0;
    bool writeResult = client.writeHeartbeat(testHeartbeat);
    
    printTestResult("Heartbeat Test", readResult && writeResult, 
                   "Read: " + std::to_string(heartbeat) + ", Write: " + std::to_string(testHeartbeat));
    return readResult && writeResult;
}

bool FeederTester::testDataRead(SimpleFeederClient& client) {
    std::vector<float> weights;
    std::vector<bool> blockStatus;
    
    bool weightsResult = client.readBinWeights(weights);
    bool blockResult = client.readBinBlockStatus(blockStatus);
    
    if (weightsResult && blockResult) {
        std::cout << "Bin weights: ";
        for (size_t i = 0; i < weights.size(); ++i) {
            std::cout << weights[i] << " ";
        }
        std::cout << std::endl;
        
        std::cout << "Block status: ";
        for (size_t i = 0; i < blockStatus.size(); ++i) {
            std::cout << (blockStatus[i] ? "1" : "0") << " ";
        }
        std::cout << std::endl;
    }
    
    printTestResult("Data Read Test", weightsResult && blockResult, client.getLastError());
    return weightsResult && blockResult;
}

bool FeederTester::testDataWrite(SimpleFeederClient& client) {
    // 测试写入目标重量
    std::vector<float> targetWeights = {100.0f, 200.0f, 300.0f, 400.0f, 500.0f};
    bool weightsResult = client.writeBinTargetWeights(targetWeights);
    
    // 测试写入投料状态
    std::vector<bool> feedingStatus = {false, true, false, true, false};
    bool statusResult = client.writeBinFeedingStatus(feedingStatus);
    
    printTestResult("Data Write Test", weightsResult && statusResult, client.getLastError());
    return weightsResult && statusResult;
}

bool FeederTester::runFullTest(const FeederConfig& config) {
    std::cout << "=== Testing Feeder " << config.id << " at " << config.ip << ":" << config.port << " ===" << std::endl;
    
    SimpleFeederClient client(config);
    
    bool connectionResult = testConnection(config);
    if (!connectionResult) {
        return false;
    }
    
    bool heartbeatResult = testHeartbeat(client);
    bool readResult = testDataRead(client);
    bool writeResult = testDataWrite(client);
    
    bool overallResult = connectionResult && heartbeatResult && readResult && writeResult;
    
    std::cout << "=== Overall Test Result: " << (overallResult ? "PASS" : "FAIL") << " ===" << std::endl;
    std::cout << std::endl;
    
    return overallResult;
}

void FeederTester::printTestResult(const std::string& testName, bool result, const std::string& details) {
    std::cout << "[" << (result ? "PASS" : "FAIL") << "] " << testName;
    if (!details.empty()) {
        std::cout << " - " << details;
    }
    std::cout << std::endl;
}

// 测试主函数
int main() {
    std::cout << "=== LibModbus 测试程序 ===" << std::endl;

    // 创建投料机配置
    FeederConfig config;
    config.id = 1;
    config.ip = "127.0.0.1";  // 本地测试
    config.port = 502;
    config.enabled = true;

    std::cout << "创建投料机客户端..." << std::endl;
    SimpleFeederClient client(config);

    std::cout << "尝试连接到 " << config.ip << ":" << config.port << std::endl;

    // 尝试连接（这会失败，因为没有实际的Modbus服务器，但可以测试库是否正常加载）
    bool connected = client.connect();

    if (connected) {
        std::cout << "连接成功！" << std::endl;

        // 测试读取心跳
        uint16_t heartbeat;
        if (client.readHeartbeat(heartbeat)) {
            std::cout << "读取心跳成功：" << heartbeat << std::endl;
        } else {
            std::cout << "读取心跳失败：" << client.getLastError() << std::endl;
        }

        // 测试读取料仓重量
        std::vector<float> weights;
        if (client.readBinWeights(weights)) {
            std::cout << "读取料仓重量成功，数量：" << weights.size() << std::endl;
        } else {
            std::cout << "读取料仓重量失败：" << client.getLastError() << std::endl;
        }

        client.disconnect();
    } else {
        std::cout << "连接失败：" << client.getLastError() << std::endl;
        std::cout << "这是正常的，因为没有运行Modbus服务器。" << std::endl;
    }

    std::cout << "LibModbus库加载和调用测试完成。" << std::endl;

    // 等待用户输入
    std::cout << "按回车键退出..." << std::endl;
    std::cin.get();

    return 0;
}
