#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <chrono>
#include "data_structures.h"

// 前向声明
class modbus;

/**
 * 简单投料机客户端
 * 用于Phase 1的基础连通验证
 * 基于现有的modbusTcp.cpp实现
 */
class SimpleFeederClient {
public:
    explicit SimpleFeederClient(const FeederConfig& config);
    ~SimpleFeederClient();
    
    // 基础连接管理
    bool connect();
    void disconnect();
    bool isConnected() const { return connected; }
    
    // 基础数据读取
    bool readHeartbeat(uint16_t& heartbeat);
    bool readBinWeights(std::vector<float>& weights);
    bool readBinBlockStatus(std::vector<bool>& blockStatus);
    
    // 基础数据写入
    bool writeHeartbeat(uint16_t heartbeat);
    bool writeBinFeedingStatus(const std::vector<bool>& feedingStatus);
    bool writeBinTargetWeights(const std::vector<float>& targetWeights);
    
    // 获取配置信息
    const FeederConfig& getConfig() const { return config; }
    int getFeederId() const { return config.id; }
    
    // 错误信息
    std::string getLastError() const { return lastError; }
    
private:
    FeederConfig config;
    std::unique_ptr<modbus> modbusClient;
    std::atomic<bool> connected;
    std::string lastError;
    
    // 内部辅助方法
    bool readRegisters(int address, int count, uint16_t* buffer);
    bool writeRegisters(int address, int count, const uint16_t* buffer);
    bool readSingleRegister(int address, uint16_t& value);
    bool writeSingleRegister(int address, uint16_t value);
    
    // 数据转换辅助方法
    float registersToFloat(uint16_t reg1, uint16_t reg2);
    void floatToRegisters(float value, uint16_t& reg1, uint16_t& reg2);
    
    // 错误处理
    void setError(const std::string& error);
    void clearError();
};

/**
 * 投料机测试工具类
 * 用于验证投料机连接和基础功能
 */
class FeederTester {
public:
    static bool testConnection(const FeederConfig& config);
    static bool testHeartbeat(SimpleFeederClient& client);
    static bool testDataRead(SimpleFeederClient& client);
    static bool testDataWrite(SimpleFeederClient& client);
    static bool runFullTest(const FeederConfig& config);
    
private:
    static void printTestResult(const std::string& testName, bool result, const std::string& details = "");
};
