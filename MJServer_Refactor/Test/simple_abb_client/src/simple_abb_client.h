#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <queue>
#include "data_structures.h"
#include "abb_protocol.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
using SOCKET_TYPE = SOCKET;
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
using SOCKET_TYPE = int;
#define INVALID_SOCKET -1
#define SOCKET_ERROR -1
#endif

/**
 * 简单ABB客户端
 * 用于Phase 1的基础连通验证
 * 基于Socket实现与ABB机器人的直接通信
 */
class SimpleABBClient {
public:
    explicit SimpleABBClient(const ABBConfig& config);
    ~SimpleABBClient();
    
    // 基础连接管理
    bool connect();
    void disconnect();
    bool isConnected() const { return connected; }
    
    // 基础通信功能
    bool sendCommand(const std::string& command);
    bool sendMessage(const std::string& message);
    bool receiveResponse(std::string& response, int timeoutMs = 5000);
    
    // 协议相关功能
    bool sendTaskCommand(const TaskInfo& task);
    bool sendHeartbeat();
    bool sendStatusQuery();
    bool sendStopCommand();
    bool sendEmergencyStop();
    
    // 获取配置信息
    const ABBConfig& getConfig() const { return config; }
    
    // 错误信息
    std::string getLastError() const { return lastError; }
    
    // 统计信息
    struct Statistics {
        int messagesSent;
        int messagesReceived;
        int connectionAttempts;
        int errors;
        
        Statistics() : messagesSent(0), messagesReceived(0), connectionAttempts(0), errors(0) {}
    };
    
    const Statistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = Statistics(); }
    
private:
    ABBConfig config;
    SOCKET_TYPE socket;
    std::atomic<bool> connected;
    std::string lastError;
    Statistics stats;
    std::mutex socketMutex;
    
    // 内部辅助方法
    bool initializeSocket();
    void cleanupSocket();
    bool connectSocket();
    int sendData(const char* data, int length);
    int receiveData(char* buffer, int bufferSize, int timeoutMs);
    
    // 错误处理
    void setError(const std::string& error);
    void clearError();
    
    // 平台相关的socket操作
    void closeSocket();
    int getLastSocketError();
    std::string getSocketErrorString(int errorCode);
};

/**
 * ABB客户端测试工具类
 * 用于验证ABB机器人连接和基础功能
 */
class ABBTester {
public:
    static bool testConnection(const ABBConfig& config);
    static bool testBasicCommunication(SimpleABBClient& client);
    static bool testProtocolCommands(SimpleABBClient& client);
    static bool testTaskSending(SimpleABBClient& client);
    static bool runFullTest(const ABBConfig& config);
    
private:
    static void printTestResult(const std::string& testName, bool result, const std::string& details = "");
    static TaskInfo createTestTask();
};
