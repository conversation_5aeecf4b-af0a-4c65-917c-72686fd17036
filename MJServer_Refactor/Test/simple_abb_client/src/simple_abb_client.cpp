#include "simple_abb_client.h"
#include <iostream>
#include <chrono>
#include <cstring>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#endif

SimpleABBClient::SimpleABBClient(const ABBConfig& config)
    : config(config), socket(INVALID_SOCKET), connected(false) {
    clearError();
    
#ifdef _WIN32
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        setError("Failed to initialize Winsock");
    }
#endif
}

SimpleABBClient::~SimpleABBClient() {
    disconnect();
    
#ifdef _WIN32
    WSACleanup();
#endif
}

bool SimpleABBClient::connect() {
    if (connected) {
        return true;
    }
    
    stats.connectionAttempts++;
    
    if (!initializeSocket()) {
        return false;
    }
    
    if (!connectSocket()) {
        cleanupSocket();
        return false;
    }
    
    connected = true;
    clearError();
    return true;
}

void SimpleABBClient::disconnect() {
    if (connected) {
        connected = false;
        cleanupSocket();
    }
}

bool SimpleABBClient::sendCommand(const std::string& command) {
    if (!ABBProtocol::ProtocolHelper::validateCommand(command)) {
        setError("Invalid command format: " + command);
        return false;
    }
    
    return sendMessage(command);
}

bool SimpleABBClient::sendMessage(const std::string& message) {
    if (!connected) {
        setError("Not connected to ABB robot");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(socketMutex);
    
    int bytesSent = sendData(message.c_str(), static_cast<int>(message.length()));
    if (bytesSent <= 0) {
        setError("Failed to send message: " + getSocketErrorString(getLastSocketError()));
        stats.errors++;
        return false;
    }
    
    stats.messagesSent++;
    return true;
}

bool SimpleABBClient::receiveResponse(std::string& response, int timeoutMs) {
    if (!connected) {
        setError("Not connected to ABB robot");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(socketMutex);
    
    char buffer[1024];
    int bytesReceived = receiveData(buffer, sizeof(buffer) - 1, timeoutMs);
    
    if (bytesReceived <= 0) {
        setError("Failed to receive response: " + getSocketErrorString(getLastSocketError()));
        stats.errors++;
        return false;
    }
    
    buffer[bytesReceived] = '\0';
    response = std::string(buffer);
    stats.messagesReceived++;
    return true;
}

bool SimpleABBClient::sendTaskCommand(const TaskInfo& task) {
    std::string command = ABBProtocol::ProtocolHelper::buildTaskCommand(task);
    return sendCommand(command);
}

bool SimpleABBClient::sendHeartbeat() {
    std::string command = ABBProtocol::ProtocolHelper::buildHeartbeat();
    return sendCommand(command);
}

bool SimpleABBClient::sendStatusQuery() {
    std::string command = ABBProtocol::ProtocolHelper::buildStatusQuery();
    return sendCommand(command);
}

bool SimpleABBClient::sendStopCommand() {
    std::string command = ABBProtocol::ProtocolHelper::buildStopCommand();
    return sendCommand(command);
}

bool SimpleABBClient::sendEmergencyStop() {
    std::string command = ABBProtocol::ProtocolHelper::buildEmergencyStop();
    return sendCommand(command);
}

bool SimpleABBClient::initializeSocket() {
    socket = ::socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (socket == INVALID_SOCKET) {
        setError("Failed to create socket: " + getSocketErrorString(getLastSocketError()));
        return false;
    }
    
    // 设置socket选项
    int optval = 1;
#ifdef _WIN32
    setsockopt(socket, SOL_SOCKET, SO_REUSEADDR, (const char*)&optval, sizeof(optval));
#else
    setsockopt(socket, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));
#endif
    
    return true;
}

void SimpleABBClient::cleanupSocket() {
    if (socket != INVALID_SOCKET) {
        closeSocket();
        socket = INVALID_SOCKET;
    }
}

bool SimpleABBClient::connectSocket() {
    sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(static_cast<uint16_t>(config.port));
    
#ifdef _WIN32
    serverAddr.sin_addr.s_addr = inet_addr(config.ip.c_str());
    if (serverAddr.sin_addr.s_addr == INADDR_NONE) {
#else
    if (inet_pton(AF_INET, config.ip.c_str(), &serverAddr.sin_addr) <= 0) {
#endif
        setError("Invalid IP address: " + config.ip);
        return false;
    }
    
    if (::connect(socket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        setError("Failed to connect to " + config.ip + ":" + std::to_string(config.port) + 
                " - " + getSocketErrorString(getLastSocketError()));
        return false;
    }
    
    return true;
}

int SimpleABBClient::sendData(const char* data, int length) {
#ifdef _WIN32
    return send(socket, data, length, 0);
#else
    return send(socket, data, length, MSG_NOSIGNAL);
#endif
}

int SimpleABBClient::receiveData(char* buffer, int bufferSize, int timeoutMs) {
    // 设置接收超时
#ifdef _WIN32
    DWORD timeout = timeoutMs;
    setsockopt(socket, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
#else
    struct timeval timeout;
    timeout.tv_sec = timeoutMs / 1000;
    timeout.tv_usec = (timeoutMs % 1000) * 1000;
    setsockopt(socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
#endif
    
    return recv(socket, buffer, bufferSize, 0);
}

void SimpleABBClient::setError(const std::string& error) {
    lastError = error;
    std::cerr << "[ABBClient] Error: " << error << std::endl;
}

void SimpleABBClient::clearError() {
    lastError.clear();
}

void SimpleABBClient::closeSocket() {
#ifdef _WIN32
    closesocket(socket);
#else
    close(socket);
#endif
}

int SimpleABBClient::getLastSocketError() {
#ifdef _WIN32
    return WSAGetLastError();
#else
    return errno;
#endif
}

std::string SimpleABBClient::getSocketErrorString(int errorCode) {
#ifdef _WIN32
    char* errorMsg = nullptr;
    FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM,
                   nullptr, errorCode, 0, (LPSTR)&errorMsg, 0, nullptr);
    std::string result = errorMsg ? errorMsg : "Unknown error";
    if (errorMsg) LocalFree(errorMsg);
    return result;
#else
    return strerror(errorCode);
#endif
}

// ABBTester实现
bool ABBTester::testConnection(const ABBConfig& config) {
    SimpleABBClient client(config);
    bool result = client.connect();
    printTestResult("Connection Test", result, client.getLastError());
    return result;
}

bool ABBTester::testBasicCommunication(SimpleABBClient& client) {
    // 发送简单的心跳命令
    bool sendResult = client.sendHeartbeat();
    if (!sendResult) {
        printTestResult("Basic Communication Test", false, "Failed to send heartbeat: " + client.getLastError());
        return false;
    }
    
    // 尝试接收响应
    std::string response;
    bool receiveResult = client.receiveResponse(response, 3000);
    
    printTestResult("Basic Communication Test", sendResult && receiveResult, 
                   "Sent heartbeat, Response: " + (receiveResult ? response : "No response"));
    return sendResult && receiveResult;
}

bool ABBTester::testProtocolCommands(SimpleABBClient& client) {
    bool heartbeatResult = client.sendHeartbeat();
    bool statusResult = client.sendStatusQuery();
    bool stopResult = client.sendStopCommand();
    
    bool overallResult = heartbeatResult && statusResult && stopResult;
    
    printTestResult("Protocol Commands Test", overallResult, 
                   "Heartbeat: " + std::to_string(heartbeatResult) + 
                   ", Status: " + std::to_string(statusResult) + 
                   ", Stop: " + std::to_string(stopResult));
    return overallResult;
}

bool ABBTester::testTaskSending(SimpleABBClient& client) {
    TaskInfo testTask = createTestTask();
    bool result = client.sendTaskCommand(testTask);
    
    printTestResult("Task Sending Test", result, 
                   "Task ID: " + testTask.taskId + ", Feeder: " + std::to_string(testTask.feederId));
    return result;
}

bool ABBTester::runFullTest(const ABBConfig& config) {
    std::cout << "=== Testing ABB Robot at " << config.ip << ":" << config.port << " ===" << std::endl;
    
    SimpleABBClient client(config);
    
    bool connectionResult = testConnection(config);
    if (!connectionResult) {
        return false;
    }
    
    bool communicationResult = testBasicCommunication(client);
    bool protocolResult = testProtocolCommands(client);
    bool taskResult = testTaskSending(client);
    
    bool overallResult = connectionResult && communicationResult && protocolResult && taskResult;
    
    // 显示统计信息
    auto stats = client.getStatistics();
    std::cout << "Statistics - Sent: " << stats.messagesSent 
              << ", Received: " << stats.messagesReceived 
              << ", Errors: " << stats.errors << std::endl;
    
    std::cout << "=== Overall Test Result: " << (overallResult ? "PASS" : "FAIL") << " ===" << std::endl;
    std::cout << std::endl;
    
    return overallResult;
}

void ABBTester::printTestResult(const std::string& testName, bool result, const std::string& details) {
    std::cout << "[" << (result ? "PASS" : "FAIL") << "] " << testName;
    if (!details.empty()) {
        std::cout << " - " << details;
    }
    std::cout << std::endl;
}

TaskInfo ABBTester::createTestTask() {
    TaskInfo task;
    task.taskId = "TEST_001";
    task.batchNumber = "BATCH_001";
    task.feederId = 1;
    task.binId = 2;
    task.bucketId = 3;
    task.areaId = 'A';
    task.weight = 100.5f;
    task.minWeight = 90.0f;
    task.maxWeight = 110.0f;
    task.createTime = "2024-01-01 12:00:00";
    return task;
}
