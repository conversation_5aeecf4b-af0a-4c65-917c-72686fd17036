# 系统配置功能使用说明

## 概述

系统配置功能为MJServer_RefactorApp提供了完整的配置管理界面，用户可以通过图形界面方便地管理系统的各项配置参数，包括投料机设置、ABB机器人参数、MES服务器配置等。

## 功能特性

### ✅ 已实现功能

1. **基本系统配置**
   - 直连模式开关
   - 运行模式切换

2. **投料机配置管理**
   - 支持5台投料机的配置
   - IP地址、端口、超时时间设置
   - 心跳检测开关
   - 实时配置更新

3. **ABB机器人配置**
   - IP地址和端口设置
   - 连接超时时间配置
   - 直连模式开关

4. **MES服务器配置**
   - 监听IP和端口设置
   - 数据自动保存开关
   - 历史记录数量限制

5. **配置文件操作**
   - 保存配置到文件
   - 重新加载配置
   - 恢复默认设置
   - 导出/导入配置文件

6. **数据持久化**
   - 自动保存配置更改
   - JSON格式配置文件
   - 配置备份和恢复

## 界面布局

### 系统配置Tab页面

系统配置功能位于主界面的"系统配置"Tab页面中，包含以下几个主要区域：

#### 1. 基本系统配置
- **直连模式开关**：启用/禁用直连模式
- **运行模式显示**：当前系统运行模式状态

#### 2. 投料机配置表格
| 投料机ID | IP地址 | 端口 | 超时(ms) | 启用心跳 |
|---------|--------|------|----------|----------|
| 1 | ************* | 502 | 5000 | ✓ |
| 2 | ************* | 502 | 5000 | ✓ |
| 3 | ************* | 502 | 5000 | ✓ |
| 4 | ************* | 502 | 5000 | ✓ |
| 5 | ************* | 502 | 5000 | ✓ |

#### 3. ABB机器人配置
- **IP地址**：机器人控制器IP地址
- **端口**：通信端口号
- **超时时间**：连接超时设置
- **连接模式**：直连/标准模式选择

#### 4. MES服务器配置
- **监听IP**：MES服务器监听地址
- **监听端口**：MES服务器端口
- **数据持久化**：自动保存数据开关
- **历史记录数**：最大保存的历史记录数量

#### 5. 配置操作按钮
- **保存配置**：将当前配置保存到文件
- **重新加载**：从文件重新加载配置
- **恢复默认**：恢复系统默认配置
- **导出配置**：导出配置到指定文件
- **导入配置**：从文件导入配置

## 使用方法

### 基本配置操作

1. **修改投料机配置**
   - 在投料机配置表格中直接编辑IP地址、端口、超时时间
   - 勾选/取消心跳检测开关
   - 配置会自动保存

2. **调整ABB机器人参数**
   - 修改IP地址和端口设置
   - 调整超时时间
   - 切换连接模式

3. **设置MES服务器**
   - 配置监听地址和端口
   - 开启/关闭数据自动保存
   - 设置历史记录保存数量

### 配置文件管理

1. **保存当前配置**
   ```
   点击"保存配置"按钮 → 配置保存到 config/direct_mode.json
   ```

2. **重新加载配置**
   ```
   点击"重新加载"按钮 → 从配置文件重新读取设置
   ```

3. **恢复默认设置**
   ```
   点击"恢复默认"按钮 → 确认对话框 → 恢复系统默认配置
   ```

4. **导出配置**
   ```
   点击"导出配置"按钮 → 选择保存位置 → 导出JSON配置文件
   ```

5. **导入配置**
   ```
   点击"导入配置"按钮 → 选择配置文件 → 确认导入 → 配置生效
   ```

## 配置文件格式

系统使用JSON格式存储配置，主要配置文件：

- **主配置文件**：`config/direct_mode.json`
- **MES数据配置**：`config/mes_data.json`
- **屏蔽配置**：包含在主配置文件中

### 配置文件结构示例

```json
{
  "enable_direct_mode": false,
  "feeders": [
    {
      "id": 1,
      "ip": "*************",
      "port": 502,
      "timeout": 5000,
      "enable_heartbeat": true
    }
  ],
  "abb_robot": {
    "ip": "*************",
    "port": 7000,
    "enable_direct": false,
    "timeout": 10000
  },
  "mes_config": {
    "server_config": {
      "ip": "0.0.0.0",
      "port": 502,
      "auto_save": true,
      "max_send_data_history": 1000
    }
  }
}
```

## 实时反馈

系统配置功能提供实时的操作反馈：

1. **操作日志**：所有配置操作都会在日志区域显示
2. **状态提示**：配置保存、加载状态实时显示
3. **错误处理**：配置错误时显示详细错误信息
4. **确认对话框**：重要操作前提供确认提示

## 安全特性

1. **配置验证**：自动验证配置参数的有效性
2. **备份机制**：重要操作前自动备份当前配置
3. **错误恢复**：配置错误时可快速恢复默认设置
4. **权限控制**：配置修改操作记录在日志中

## 注意事项

1. **配置生效**：某些配置更改可能需要重启系统才能完全生效
2. **网络设置**：修改IP地址和端口时请确保网络连通性
3. **备份重要**：重要配置修改前建议先导出备份
4. **兼容性**：导入配置时请确保配置文件格式正确

## 故障排除

### 常见问题

1. **配置保存失败**
   - 检查config目录是否存在
   - 确认文件写入权限
   - 查看错误日志信息

2. **配置导入失败**
   - 验证JSON文件格式
   - 检查配置参数有效性
   - 确认文件路径正确

3. **网络连接问题**
   - 验证IP地址和端口设置
   - 检查网络连通性
   - 确认防火墙设置

### 技术支持

如遇到问题，请查看：
- 系统日志区域的错误信息
- 配置文件的JSON格式
- 网络连接状态

---

**版本信息**
- 功能版本：v1.0
- 兼容性：MJServer_RefactorApp v2.0+
- 更新日期：2025-07-18
