{"enable_direct_mode": false, "feeders": [{"id": 1, "ip": "*************", "port": 502, "timeout": 5000, "enable_heartbeat": true}, {"id": 2, "ip": "*************", "port": 502, "timeout": 5000, "enable_heartbeat": true}, {"id": 3, "ip": "*************", "port": 502, "timeout": 5000, "enable_heartbeat": true}, {"id": 4, "ip": "*************", "port": 502, "timeout": 5000, "enable_heartbeat": true}, {"id": 5, "ip": "*************", "port": 502, "timeout": 5000, "enable_heartbeat": true}], "abb_robot": {"listen_ip": "0.0.0.0", "listen_port": 7000, "timeout": 10000, "max_connections": 1, "enable_heartbeat": true, "heartbeat_interval": 2000}, "block_config": {"feeder_blocked": {"1": false, "2": true, "3": false, "4": false, "5": false}, "bin_blocked": {"1_1": false, "1_2": false, "1_3": false, "1_4": false, "1_5": false, "2_1": true, "2_2": true, "2_3": true, "2_4": true, "2_5": true, "3_1": false, "3_2": true, "3_3": false, "3_4": false, "3_5": false, "4_1": false, "4_2": false, "4_3": false, "4_4": false, "4_5": false, "5_1": false, "5_2": false, "5_3": false, "5_4": false, "5_5": false}, "area_blocked": {"A": false, "B": false, "C": true, "D": false, "E": false, "F": false, "G": false, "H": false, "I": false, "J": false, "K": false, "L": false, "M": false, "N": false, "O": false, "P": false, "Q": false, "R": false, "S": false, "T": false, "U": false, "V": false}, "bucket_blocked": {"A_1": false, "A_2": false, "A_3": false, "A_4": false, "B_1": false, "B_2": true, "B_3": false, "B_4": false, "C_1": true, "C_2": true, "C_3": true, "C_4": true, "D_1": false, "D_2": false, "D_3": true, "D_4": false}}}