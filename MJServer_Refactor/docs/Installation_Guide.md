# MJServer_Refactor 安装配置指南

## 系统要求

### 硬件要求
- **CPU**: Intel i5 或 AMD 同等级别以上
- **内存**: 8GB RAM 最小，16GB 推荐
- **存储**: 500GB 可用空间
- **网络**: 千兆以太网接口

### 软件要求
- **操作系统**: Windows 10/11 (64位)
- **编译器**: Visual Studio 2019+ 或 MinGW-w64
- **Qt**: 5.15 或更高版本
- **CMake**: 3.16 或更高版本

## 依赖库安装

### 1. Qt 5.15 安装

```bash
# 下载Qt在线安装器
# 选择Qt 5.15.x版本
# 确保包含以下组件：
# - Qt 5.15.x MSVC 2019 64-bit
# - Qt Creator
# - CMake
```

### 2. jsoncpp 库

```bash
# 使用vcpkg安装
vcpkg install jsoncpp:x64-windows

# 或者手动编译
git clone https://github.com/open-source-parsers/jsoncpp.git
cd jsoncpp
mkdir build
cd build
cmake -DCMAKE_BUILD_TYPE=Release ..
cmake --build . --config Release
```

### 3. 其他依赖

```bash
# Windows Socket库 (系统自带)
# 链接 ws2_32.lib 和 wsock32.lib
```

## 项目编译

### 1. 克隆项目

```bash
git clone <repository-url>
cd MJServer_Refactor
```

### 2. 配置CMake

```bash
mkdir build
cd build

# 配置项目
cmake -DCMAKE_BUILD_TYPE=Release \
      -DCMAKE_PREFIX_PATH="C:/Qt/5.15.2/msvc2019_64" \
      -DVCPKG_TARGET_TRIPLET=x64-windows \
      ..
```

### 3. 编译项目

```bash
# 编译
cmake --build . --config Release

# 或者使用Visual Studio
# 打开生成的 .sln 文件进行编译
```

## 配置文件设置

### 1. 系统配置 (config/direct_mode.json)

```json
{
  "enable_direct_mode": true,
  "feeders": [
    {
      "id": 1,
      "ip": "***********01",
      "port": 502,
      "enabled": true,
      "timeout": 5000,
      "enable_heartbeat": true,
      "bin_min_weights": [100.0, 100.0, 100.0, 100.0, 100.0],
      "bin_max_weights": [1000.0, 1000.0, 1000.0, 1000.0, 1000.0]
    }
  ],
  "abb_robot": {
    "ip": "*************",
    "port": 1025,
    "enable_direct": true,
    "timeout": 3000
  },
  "block_config": {
    "feeder_blocked": {},
    "bin_blocked": {},
    "area_blocked": {},
    "bucket_blocked": {}
  }
}
```

### 2. MES配置 (config/mes_config.json)

```json
{
  "server_config": {
    "port": 8080,
    "ip": "0.0.0.0",
    "auto_save": true,
    "max_send_data_history": 1000
  },
  "area_data": {
    "A": {
      "sand_type": "细砂",
      "is_blocked": false,
      "buckets": [
        {
          "batch_number": "BATCH_A001",
          "weight": 0.0,
          "is_blocked": false
        }
      ]
    }
  },
  "feeder_data": {},
  "send_data_history": []
}
```

## ABB机器人配置

### 1. 网络设置

在ABB机器人控制器中配置网络：

```
控制面板 → 配置 → 通讯 → TCP/IP
- IP地址: *************
- 子网掩码: *************
- 网关: ***********
```

### 2. 加载RAPID程序

1. 将 `abb_scripts/MainModule.mod` 复制到机器人控制器
2. 在RobotStudio中加载模块：
   ```
   程序编辑器 → 模块 → 加载模块 → 选择MainModule.mod
   ```

3. 配置I/O信号：
   ```
   配置 → I/O系统 → 导入 → 选择IOSignals.sys
   ```

### 3. 位置校准

根据实际布局调整投料位置：

```rapid
! 在MainModule.mod中修改位置数据
feeder_positions{1, 1} := [[X, Y, Z], [Q1, Q2, Q3, Q4], ...];
```

## 网络配置

### 1. 防火墙设置

```bash
# Windows防火墙添加例外
# 入站规则 → 新建规则 → 端口
# TCP端口: 1025, 8080, 502
```

### 2. 网络拓扑

```
[MJServer PC]     [ABB Robot]     [Feeder 1-6]
***********00 ←→ ************* ←→ ***********01-106
     ↓
[Switch/Router]
***********
```

## 启动顺序

### 1. 系统启动

```bash
# 1. 启动投料机设备
# 2. 启动ABB机器人控制器
# 3. 加载并运行RAPID程序
# 4. 启动MJServer应用程序
```

### 2. 验证连接

```bash
# 检查网络连通性
ping *************  # ABB机器人
ping ***********01  # 投料机1

# 检查端口开放
telnet ************* 1025  # ABB通讯端口
telnet ***********01 502   # Modbus端口
```

## 故障排除

### 1. 编译错误

**Qt路径错误**:
```bash
# 确保CMAKE_PREFIX_PATH正确指向Qt安装目录
cmake -DCMAKE_PREFIX_PATH="C:/Qt/5.15.2/msvc2019_64" ..
```

**依赖库缺失**:
```bash
# 检查vcpkg集成
vcpkg integrate install
```

### 2. 运行时错误

**配置文件错误**:
- 检查JSON格式是否正确
- 验证IP地址和端口设置
- 确认文件路径存在

**网络连接失败**:
- 检查防火墙设置
- 验证网络连通性
- 确认设备IP配置

### 3. ABB机器人问题

**RAPID程序错误**:
- 检查语法错误
- 验证I/O信号配置
- 确认位置数据有效

**通讯中断**:
- 检查网络连接
- 验证端口配置
- 查看机器人事件日志

## 性能优化

### 1. 系统优化

```bash
# Windows系统优化
# - 关闭不必要的服务
# - 设置高性能电源模式
# - 优化网络设置
```

### 2. 应用优化

```cpp
// 线程池大小调整
executor = std::make_shared<Fuxi::Common::Executor>(4);

// 心跳间隔优化
CONST num HEARTBEAT_INTERVAL := 1;  // 1秒间隔
```

## 维护建议

### 1. 定期维护

- **每日**: 检查系统日志
- **每周**: 备份配置文件
- **每月**: 更新系统补丁
- **每季度**: 性能评估

### 2. 监控指标

- CPU使用率 < 80%
- 内存使用率 < 70%
- 网络延迟 < 10ms
- 任务成功率 > 95%

## 技术支持

如遇到问题，请提供以下信息：

1. 系统版本和配置
2. 错误日志文件
3. 网络拓扑图
4. 问题复现步骤

联系方式：
- 邮箱: <EMAIL>
- 电话: +86-xxx-xxxx-xxxx
