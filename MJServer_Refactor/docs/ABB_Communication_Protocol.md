# ABB机器人简化通讯协议文档

## 概述

本文档描述了MJServer_Refactor系统与ABB机器人之间的简化TCP通讯协议。**MJServer软件作为服务器，ABB机器人作为客户端**主动连接。采用简短的文本格式，适配ABB Socket 80字符限制。

## 连接架构

- **服务器**: MJServer软件 (监听连接)
- **客户端**: ABB机器人 (主动连接)
- **协议**: TCP
- **端口**: 1025 (可配置)
- **消息格式**: 简化文本格式
- **编码**: ASCII
- **最大长度**: 80字符
- **心跳间隔**: 2秒

## 消息结构

所有消息采用简化格式：`命令码[,参数1,参数2,...]#`

- **命令码**: 1-2位数字
- **参数**: 用逗号分隔
- **结束符**: `#`
- **最大长度**: 80字符

## 命令类型

### 1. 连接流程

#### 连接建立
1. **MJServer启动服务器**: 监听端口1025
2. **机器人连接**: 作为客户端连接到MJServer
3. **机器人就绪**: 连接成功后发送就绪消息

#### 机器人就绪 (0)

**方向**: 机器人 → MJServer
**格式**: `0#`
**说明**: 机器人连接成功后发送，表示准备接收命令

### 2. 投料命令

#### 开始投料 (1)

**方向**: MJServer → 机器人
**格式**: `1,投料机ID,料仓ID#`
**示例**: `1,1,3#`
**说明**:
- 投料机ID: 1-6
- 料仓ID: 1-5
- 机器人负责启停投料机，重量由上位机和投料机直接处理

#### 停止投料 (2)

**方向**: MJServer → 机器人
**格式**: `2#`
**说明**: 停止当前投料动作

### 3. 状态命令

#### 状态查询 (3)

**方向**: MJServer → 机器人
**格式**: `3#`
**说明**: 查询机器人当前状态

#### 状态响应

**方向**: 机器人 → MJServer
**格式**: `状态码,进度#`
**示例**: `1,75#`
**状态码说明**:
- 0: 空闲
- 1: 执行中
- 2: 完成
- 3: 错误

### 4. 控制命令

#### 紧急停止 (4)

**方向**: MJServer → 机器人 或 机器人 → MJServer
**格式**: `4#`
**说明**: 立即停止所有运动

#### 复位 (5)

**方向**: MJServer → 机器人
**格式**: `5#`
**说明**: 复位机器人状态，清除错误

### 5. 心跳命令

#### 心跳 (8)

**方向**: MJServer ↔ 机器人
**格式**: `8#`
**说明**: 保持连接活跃，双向发送

## 通讯示例

### 典型投料执行流程

1. **服务器启动**
   ```
   MJServer启动服务器，监听端口1025
   ```

2. **机器人连接**
   ```
   机器人作为客户端连接到MJServer
   机器人 → MJServer: 0#  (就绪消息)
   ```

3. **开始投料**
   ```
   MJServer → 机器人: 1,1,3#  (投料机1，料仓3)
   ```

3. **状态查询**
   ```
   MJServer → 机器人: 3#
   机器人 → MJServer: 1,40#  (执行中，进度40%)
   ```

4. **投料进行中**
   ```
   MJServer → 机器人: 3#
   机器人 → MJServer: 1,80#  (执行中，进度80%)
   ```

5. **投料完成**
   ```
   MJServer → 机器人: 3#
   机器人 → MJServer: 2,100# (完成，进度100%)
   ```

### 连接断开处理

```
机器人断开连接 → MJServer检测到断开
MJServer等待机器人重新连接
机器人重新连接 → 发送就绪消息: 0#
```

### 紧急停止流程

```
MJServer → 机器人: 4#
机器人 → MJServer: 3,0#  (错误状态)
```

## 错误处理

### 连接错误
- 连接超时: 3秒
- 重试次数: 3次
- 重试间隔: 2秒

### 消息错误
- 格式错误: 忽略消息
- 未知命令: 返回错误状态
- 参数错误: 返回错误状态

### 通讯中断
- 心跳超时: 5秒
- 自动重连: 是
- 重连间隔: 2秒

## 实现注意事项

### MJServer端
1. 使用异步Socket处理多个连接
2. 实现消息队列避免阻塞
3. 定期检查连接状态
4. 记录所有通讯日志

### ABB机器人端
1. 使用RAPID的Socket指令
2. 实现消息解析函数
3. 处理网络异常情况
4. 维护连接状态

## 配置示例

### MJServer配置
```json
{
  "abb_robot": {
    "ip": "*************",
    "port": 1025,
    "enable_direct": true,
    "timeout": 3000
  }
}
```

### ABB机器人配置
```rapid
CONST string SERVER_IP := "*************";
CONST num SERVER_PORT := 1025;
```

## 测试工具

可以使用以下工具测试通讯协议：

1. **Telnet**: 基本连接测试
   ```bash
   telnet ************* 1025
   8#  # 发送心跳
   ```

2. **ABB RobotStudio**: 机器人仿真测试
3. **TestModule.mod**: ABB机器人测试脚本

## 版本历史

- v2.0: 简化协议，适配ABB Socket限制
- v1.0: 初始JSON协议版本
