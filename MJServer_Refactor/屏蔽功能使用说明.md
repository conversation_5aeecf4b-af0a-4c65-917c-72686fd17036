# MJServer_RefactorApp 屏蔽功能使用说明

## 功能概述

MJServer_RefactorApp 新增了完整的屏蔽管理功能，允许用户屏蔽投料机、投料机料仓、上料区域及上料区域桶。屏蔽后的设备将不参与任务调度，确保系统安全运行。

## 功能特点

### 1. 多层级屏蔽支持
- **投料机整机屏蔽**：屏蔽整台投料机及其所有料仓
- **投料机料仓屏蔽**：单独屏蔽特定投料机的特定料仓
- **上料区域整区屏蔽**：屏蔽整个上料区域及其所有桶
- **上料区域桶屏蔽**：单独屏蔽特定区域的特定桶

### 2. 直观的视觉反馈
- **颜色编码**：
  - 🔴 深红色：完全屏蔽
  - 🟡 黄色：部分屏蔽
  - 🟢 绿色：正常状态
  - 🔴 浅红色：设备离线

### 3. 多种操作方式
- **专用屏蔽管理页面**：集中管理所有屏蔽设置
- **右键菜单**：在主表格中快速屏蔽/解除屏蔽
- **菜单栏快捷入口**：配置 → 屏蔽管理

## 使用方法

### 方法一：屏蔽管理页面

1. **打开屏蔽管理页面**
   - 点击菜单栏 "配置" → "屏蔽管理"
   - 或切换到 "屏蔽管理" Tab页

2. **投料机屏蔽操作**
   - 勾选 "整机屏蔽" 复选框：屏蔽整台投料机
   - 勾选特定料仓复选框：屏蔽单个料仓
   - 支持投料机1-6，每台投料机有5个料仓

3. **上料区域屏蔽操作**
   - 勾选 "整区屏蔽" 复选框：屏蔽整个区域
   - 勾选特定桶复选框：屏蔽单个桶
   - 支持区域A-V（22个区域），每个区域有4个桶

4. **批量操作**
   - 点击 "清除所有屏蔽"：一键清除所有屏蔽状态
   - 点击 "刷新状态"：更新显示状态

### 方法二：右键菜单快速操作

1. **投料机表格右键菜单**
   - 在投料机表格中右键点击任意行
   - 选择 "屏蔽投料机" 或 "解除投料机屏蔽"
   - 选择 "屏蔽料仓X" 或 "解除屏蔽料仓X"

2. **上料区域表格右键菜单**
   - 在区域表格中右键点击任意行
   - 选择 "屏蔽区域" 或 "解除区域屏蔽"
   - 选择 "屏蔽桶X" 或 "解除屏蔽桶X"

## 状态显示说明

### 投料机状态显示
- **已屏蔽**：整台投料机被屏蔽
- **在线**：投料机正常在线
- **离线**：投料机连接断开
- **在线 (心跳异常)**：连接正常但心跳异常
- **在线 (部分屏蔽:料仓1,料仓2)**：部分料仓被屏蔽

### 上料区域状态显示
- **已屏蔽**：整个区域被屏蔽
- **空闲**：区域空闲可用
- **占用**：区域被占用
- **空闲 (部分屏蔽:桶1,桶2)**：部分桶被屏蔽
- **占用 (部分屏蔽:桶3,桶4)**：占用状态下部分桶被屏蔽

## 任务调度影响

### 屏蔽规则
1. **投料机屏蔽**：
   - 整机屏蔽：该投料机不会被分配任何任务
   - 料仓屏蔽：该料仓不会被分配任务，其他料仓正常

2. **上料区域屏蔽**：
   - 整区屏蔽：该区域不会被选择为目标位置
   - 桶屏蔽：该桶不会被选择，其他桶正常

3. **级联屏蔽**：
   - 屏蔽整机时，自动屏蔽所有料仓
   - 屏蔽整区时，自动屏蔽所有桶

### 调度优先级
任务调度系统会自动过滤屏蔽的设备：
1. 首先检查投料机是否被屏蔽
2. 检查目标料仓是否被屏蔽
3. 检查目标区域是否被屏蔽
4. 检查目标桶是否被屏蔽
5. 只有通过所有检查的设备才会参与调度

## 日志记录

所有屏蔽操作都会记录在系统日志中：
- `[BLOCK] 屏蔽投料机1`
- `[BLOCK] 解除屏蔽投料机1料仓2`
- `[BLOCK] 屏蔽区域A`
- `[BLOCK] 解除屏蔽区域B桶3`
- `[BLOCK] 已清除所有屏蔽状态`

## 注意事项

1. **屏蔽状态持久化**：✅ 屏蔽状态会自动保存到配置文件中，重启后自动恢复
2. **配置文件位置**：屏蔽配置保存在 `config/system_config.json` 文件中
3. **自动保存**：每次屏蔽操作都会自动保存到配置文件，无需手动保存
4. **手动保存**：屏蔽管理页面提供"保存配置"按钮，可手动触发保存
5. **安全考虑**：屏蔽功能主要用于设备维护、故障隔离等场景
6. **性能影响**：屏蔽状态检查对系统性能影响极小
7. **操作权限**：所有用户都可以进行屏蔽操作，建议在生产环境中加入权限控制

## 技术实现

### 数据结构扩展
- `AreaData` 结构增加 `isBlocked` 字段
- `AreaData::Bucket` 结构增加 `isBlocked` 字段
- `SystemConfig` 中添加 `BlockConfig` 结构用于持久化存储
- UI层维护屏蔽状态映射表

### UI组件
- 专用屏蔽管理Tab页面
- 投料机和区域表格右键菜单
- 实时状态更新和颜色编码

### 调度集成
- 任务调度前进行屏蔽状态检查
- 自动过滤被屏蔽的设备和位置

### 配置持久化
- JSON格式存储屏蔽配置到 `config/system_config.json`
- 自动保存：每次屏蔽操作后自动保存
- 自动加载：程序启动时自动加载屏蔽配置
- 配置管理器集成：通过ConfigManager统一管理

## 版本信息

- **功能版本**：v1.1 (新增配置持久化)
- **兼容性**：MJServer_RefactorApp v2.0+
- **更新日期**：2025-07-18
