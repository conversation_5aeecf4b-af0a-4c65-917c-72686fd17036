#pragma once

#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QMessageBox>
#include <QtCore/QTimer>
#include <QtCore/QDateTime>
#include <memory>
#include <string>
#include <functional>
#include "data_structures.h"
#include "config_manager.h"
#pragma execution_character_set("utf-8")
// 前向声明
class ControlSystem;

/**
 * 主窗口类
 * 保持与原有UI完全兼容，仅底层数据源切换
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    
    // 初始化
    bool initialize();
    void setControlSystem(std::shared_ptr<ControlSystem> controlSystem);
    void setConfigManager(std::shared_ptr<ConfigManager> configMgr);
private slots:
    // 按钮事件
    void onStartSystemClicked();
    void onStopSystemClicked();
    void onEmergencyStopClicked();
    void onResetSystemClicked();
    void onModeToggleClicked();
    
    // 投料机控制
    void onStartFeedingClicked();
    void onStopFeedingClicked();
    void onStopAllFeedingClicked();
    
    // 定时更新
    void updateFeederStatus();
    void updateAreaStatus();
    void updateSystemStatus();
    void updateTaskStatus();
    
    // 配置管理
    void onConfigClicked();
    void onDirectModeToggled(bool enabled);

    // 新增的槽函数（仿照原始MJServer）
    void onVirtualMesToggled(int state);
    void onStartStopClicked();
    void onOpenLogClicked();

    // 屏蔽管理槽函数
    void onFeederBlockToggled();
    void onFeederBinBlockToggled();
    void onAreaBlockToggled();
    void onAreaBucketBlockToggled();
    void onBlockManagementClicked();

private:
    // UI组件
    QWidget *centralWidget;
    QVBoxLayout *mainLayout;

    // 标题区域（仿照原始MJServer）
    QHBoxLayout *titleLayout;
    QLabel *leftIconLabel;
    QLabel *rightIconLabel;
    QLabel *titleLabel;

    // 状态组区域（仿照原始MJServer）
    QGroupBox *systemStatusGroup;
    QGroupBox *heartbeatGroup;
    QGroupBox *taskGroup;
    QLabel *modeLabel;
    QLabel *errorLabel;
    QLabel *mesHeartbeatLabel;
    QLabel *plcHeartbeatLabel;
    QLabel *taskReadyLabel;
    QLabel *taskDumpingLabel;
    QLabel *taskCompletedLabel;
    QLabel *remainingWeightLabel;

    // 表格区域
    QTableWidget *feederTable;
    QTableWidget *areaTable;

    // 日志区域
    QGroupBox *logGroup;
    QListWidget *logList;

    // Tab控件和相关页面
    QTabWidget *tabWidget;
    QTableWidget *currentSandTypeTable;  // 当前料号表格
    QTableWidget *sandTypeHistoryTable;  // 料号变化历史表格
    QWidget *blockManagementTab;         // 屏蔽管理页面
    QWidget *systemConfigTab;            // 系统配置页面

    // 底部按钮区域
    QCheckBox *virtualMesCheckBox;
    QPushButton *startStopButton;
    QPushButton *openLogButton;

    // 系统控制区域（保留原有的）
    QGroupBox *systemControlGroup;
    QHBoxLayout *systemControlLayout;
    QPushButton *startButton;
    QPushButton *stopButton;
    QPushButton *emergencyButton;
    QPushButton *resetButton;
    QPushButton *modeToggleButton;
    QPushButton *configButton;
    
    // 投料机状态显示区域
    QGroupBox *feederStatusGroup;
    QGridLayout *feederStatusLayout;
    std::vector<QWidget*> feederWidgets;
    std::vector<QLabel*> feederStatusLabels;
    std::vector<QLabel*> feederWeightLabels;
    std::vector<QPushButton*> feederControlButtons;
    
    // 区域状态显示区域
    QGroupBox *areaStatusGroup;
    QGridLayout *areaStatusLayout;
    std::map<char, QWidget*> areaWidgets;
    std::map<char, QLabel*> areaLabels;
    std::map<char, std::vector<QLabel*>> bucketLabels;
    
    // 任务管理区域
    QVBoxLayout *taskLayout;
    QTableWidget *taskTable;
    QTextEdit *taskLogText;
    
    // 系统状态区域
    QGroupBox *statusGroup;
    QVBoxLayout *statusLayout;
    QLabel *systemStatusLabel;
    QLabel *connectionStatusLabel;
    QLabel *heartbeatStatusLabel;
    QTextEdit *systemLogText;
    
    // 定时器
    QTimer *updateTimer;
    QTimer *statusTimer;
    
    // 控制系统
    std::shared_ptr<ControlSystem> controlSystem;

    // 配置管理器（用于屏蔽状态持久化）
    std::shared_ptr<ConfigManager> configManager;
    
    // 状态变量
    bool systemRunning;
    bool autoMode;
    bool directMode;

    // 屏蔽状态管理
    std::map<int, bool> feederBlockStatus;           // 投料机屏蔽状态 (feederId -> blocked)
    std::map<std::pair<int, int>, bool> binBlockStatus;  // 料仓屏蔽状态 (feederId, binId -> blocked)
    std::map<char, bool> areaBlockStatus;            // 区域屏蔽状态 (areaId -> blocked)
    std::map<std::pair<char, int>, bool> bucketBlockStatus; // 桶屏蔽状态 (areaId, bucketId -> blocked)

    // 砂类型变化记录
    struct SandTypeChange {
        QDateTime timestamp;
        std::string feederName;
        int binId;
        std::string oldType;
        std::string newType;
    };
    std::vector<SandTypeChange> sandTypeHistory;
    static constexpr int MAX_HISTORY_ITEMS = 100;  // 最大历史记录数
    
    // UI初始化方法
    void setupUI();
    void setupTitleArea();
    void setupStatusGroups();
    void setupFeederTable();
    void setupAreaTable();
    void setupLogArea();
    void setupBottomButtons();
    void setupSystemControlArea();
    void setupFeederStatusArea();
    void setupAreaStatusArea();
    void setupTaskArea();
    void setupStatusArea();
    void setupMenuBar();
    void setupStatusBar();
    void setupTimers();

    // Tab页面设置方法
    void createCurrentSandTypeTab();
    void createSandTypeHistoryTab();
    void createBlockManagementTab();
    void createSystemConfigTab();
    void createMESDataDisplayTab();
    
    // UI更新方法
    void updateFeederWidget(int feederId, std::shared_ptr<FeederStatus> status);
    void updateAreaWidget(char areaId, std::shared_ptr<AreaData> data);
    void updateFeederTableData(const std::vector<std::shared_ptr<FeederStatus>>& feederStatus);
    void updateAreaTableData();
    void updateTaskStatusDisplay();
    void updateSystemStatusDisplay();
    void updateConnectionStatus();
    void updateHeartbeatStatus();
    void updateConnectionStatusAsync();
    void updateHeartbeatStatusAsync();
    void updateCurrentSandTypeTableAsync();
    void updateCurrentSandTypeTable(const std::vector<std::shared_ptr<FeederStatus>>& feederStatus);
    void updateSandTypeHistoryTable(const std::string& feederName, int binId,
                                  const std::string& oldType, const std::string& newType);

    // 屏蔽管理方法
    void updateBlockStatusDisplay();
    void setFeederBlocked(int feederId, bool blocked);
    void setBinBlocked(int feederId, int binId, bool blocked);
    void setAreaBlocked(char areaId, bool blocked);
    void setBucketBlocked(char areaId, int bucketId, bool blocked);
    bool isFeederBlocked(int feederId) const;
    bool isBinBlocked(int feederId, int binId) const;
    bool isAreaBlocked(char areaId) const;
    bool isBucketBlocked(char areaId, int bucketId) const;

    // 屏蔽状态持久化
    void loadBlockConfigFromFile();
    void saveBlockConfigToFile();

    // 系统配置管理
    void onSaveSystemConfigClicked();
    void onLoadSystemConfigClicked();
    void onResetSystemConfigClicked();
    void onExportSystemConfigClicked();
    void onImportSystemConfigClicked();
    void onDirectModeConfigToggled(bool enabled);
    void onFeederConfigChanged();
    void onWeightLimitChanged();
    void onABBConfigChanged();
    void onMESConfigChanged();
    void updateSystemConfigDisplay();

    // MES数据显示相关
    void onRefreshMESDataClicked();
    void onClearMESHistoryClicked();
    void onMESHistoryCountChanged(int count);
    void updateMESDataDisplay();

    // 配置热更新
    void onConfigurationChanged(const std::string& section, const std::string& key);

    // 事件处理
    void handleSystemError(const std::string& error);
    void handleTaskLog(const std::string& message);
    void handleStatusUpdate(const std::string& status);
    
    // 辅助方法
    QString formatWeight(float weight);
    QString formatTime(const std::string& timeStr);
    QColor getStatusColor(bool isOK);
    void addLogMessage(const QString& message, QTextEdit* logWidget);
    void showErrorMessage(const QString& title, const QString& message);
    void showInfoMessage(const QString& title, const QString& message);
    void adjustTableHeight(QTableWidget* table, int maxHeight = -1);  // 新增：自动调整表格高度

    // 配置验证方法
    bool validateIPAddress(const QString& ip, QString& errorMessage);
    bool validatePort(int port, QString& errorMessage);
    bool validateTimeout(int timeout, QString& errorMessage);
    bool validateWeight(float weight, QString& errorMessage);
    void showValidationError(QWidget* widget, const QString& message);
    void clearValidationError(QWidget* widget);
    void showValidationError(QTableWidgetItem* item, const QString& message);
    void clearValidationError(QTableWidgetItem* item);
    
    // 样式设置
    void applyStyles();
    QString getButtonStyle(const QString& color);
    QString getLabelStyle(const QString& color);
    QString getGroupBoxStyle();
    QString getCheckBoxStyle();
};
