#include "main_window.h"
#include "control_system.h"
#include "config_manager.h"
#include <QtWidgets/QApplication>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QMenu>
#include <QtWidgets/QAction>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QListWidget>
#include <QtWidgets/QTableWidget>
#include <QtWidgets/QTableWidgetItem>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QFormLayout>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QFileDialog>
#include <QtCore/QDateTime>
#include <QtCore/QTimer>
#include <QtCore/QMetaObject>
#include <iostream>
#include <set>
#include <string>
#include <functional>
#include <thread>
#pragma execution_character_set("utf-8")
MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), systemRunning(false), autoMode(true), directMode(false) {
    
    // 设置窗口属性
    setWindowTitle("MJServer Refactor - 美晶石英砂上料系统");
    setMinimumSize(1200, 800);
    resize(1400, 900);
    
    // 初始化UI
    setupUI();
    setupTimers();
    applyStyles();

    // 暂时跳过屏蔽配置加载以避免JSON解析崩溃
    loadBlockConfigFromFile();
}

MainWindow::~MainWindow() {
    // 停止所有定时器
    if (updateTimer) {
        updateTimer->stop();
    }
    if (statusTimer) {
        statusTimer->stop();
    }

    // 停止MES数据更新定时器
    QTimer *mesUpdateTimer = findChild<QTimer*>("mesUpdateTimer");
    if (mesUpdateTimer) {
        mesUpdateTimer->stop();
    }

    // 清除配置变更回调，避免在析构过程中被调用
    if (configManager) {
        configManager->setConfigChangeCallback(nullptr);
    }
}

bool MainWindow::initialize() {
    try {
        // 初始化UI组件状态
        updateSystemStatusDisplay();
        updateConnectionStatus();
        updateHeartbeatStatus();

        // 添加初始化完成日志
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] 系统界面初始化完成").arg(timestamp));
        }

        std::cout << "系统界面初始化完成" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "界面初始化失败: " << e.what() << std::endl;
        return false;
    }
}

void MainWindow::setControlSystem(std::shared_ptr<ControlSystem> controlSystem) {
    this->controlSystem = controlSystem;

    if (controlSystem) {
        // 设置状态回调
        controlSystem->setStatusCallback([this](const std::string& status) {
            QMetaObject::invokeMethod(this, [this, status]() {
                handleStatusUpdate(status);
            }, Qt::QueuedConnection);
        });

        controlSystem->setTaskLogCallback([this](const std::string& message) {
            QMetaObject::invokeMethod(this, [this, message]() {
                handleTaskLog(message);
            }, Qt::QueuedConnection);
        });

        controlSystem->setFeederStatusCallback([this](const std::vector<std::shared_ptr<FeederStatus>>& status) {
            QMetaObject::invokeMethod(this, [this, status]() {
                updateFeederTableData(status);
            }, Qt::QueuedConnection);
        });

        // 设置定时器来更新任务状态
        QTimer::singleShot(1000, this, [this]() {
            updateTaskStatusDisplay();
        });

        std::cout << "Control system set successfully (all callbacks enabled)" << std::endl;

        // 更新直连模式状态
        directMode = controlSystem->isDirectModeEnabled();
        updateSystemStatusDisplay();

        // 显示基础服务器状态
        if (logList && controlSystem->areBaseServersRunning()) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] ABB和MES服务器已启动，点击'连接设备'按钮连接其他设备").arg(timestamp));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::setupUI() {
    // 创建Tab控件
    tabWidget = new QTabWidget(this);
    setCentralWidget(tabWidget);

    // 创建主页面的容器widget
    QWidget *mainWidget = new QWidget();
    mainLayout = new QVBoxLayout(mainWidget);
    mainLayout->setSpacing(10);
    mainLayout->setContentsMargins(10, 10, 10, 10);

    // 逐步恢复UI组件到主页面
    setupTitleArea();
    setupStatusGroups();
    setupFeederTable();
    setupAreaTable();
    setupLogArea();
    setupBottomButtons();

    // 添加主页面到Tab控件
    tabWidget->addTab(mainWidget, "系统状态");

    // 创建新的Tab页面
    createCurrentSandTypeTab();
    createSandTypeHistoryTab();
    createBlockManagementTab();
    createSystemConfigTab();
    createMESDataDisplayTab();

    setupMenuBar();
    setupStatusBar();
}

void MainWindow::setupTitleArea() {
    // 创建标题布局（仿照原始MJServer）
    titleLayout = new QHBoxLayout();

    // 简化版本：只添加标题，暂时不加载图片
    titleLabel = new QLabel("MJServer Refactor - 美晶石英砂上料系统", this);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(16);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLayout->addWidget(titleLabel, 1);

    mainLayout->addLayout(titleLayout);
}

void MainWindow::setupStatusGroups() {
    // 创建三个状态组的水平布局（仿照原始MJServer）
    QHBoxLayout *topLayout = new QHBoxLayout();

    // 系统状态组
    systemStatusGroup = new QGroupBox("系统状态", this);
    QVBoxLayout *statusLayout = new QVBoxLayout(systemStatusGroup);

    modeLabel = new QLabel("模式: 自动", this);
    errorLabel = new QLabel("错误: 无", this);
    statusLayout->addWidget(modeLabel);
    statusLayout->addWidget(errorLabel);

    // 心跳状态组
    heartbeatGroup = new QGroupBox("心跳状态", this);
    QVBoxLayout *heartbeatLayout = new QVBoxLayout(heartbeatGroup);

    mesHeartbeatLabel = new QLabel("MES心跳: 正常", this);
    plcHeartbeatLabel = new QLabel("PLC心跳: 正常", this);
    heartbeatLayout->addWidget(mesHeartbeatLabel);
    heartbeatLayout->addWidget(plcHeartbeatLabel);

    // 任务状态组
    taskGroup = new QGroupBox("任务状态", this);
    QGridLayout *taskLayout = new QGridLayout(taskGroup);

    taskReadyLabel = new QLabel("就绪任务: 0", this);
    taskDumpingLabel = new QLabel("执行任务: 0", this);
    taskCompletedLabel = new QLabel("完成任务: 0", this);
    remainingWeightLabel = new QLabel("剩余重量: 0kg", this);

    taskLayout->addWidget(taskReadyLabel, 0, 0);
    taskLayout->addWidget(taskDumpingLabel, 0, 1);
    taskLayout->addWidget(taskCompletedLabel, 1, 0);
    taskLayout->addWidget(remainingWeightLabel, 1, 1);

    // 添加到水平布局
    topLayout->addWidget(systemStatusGroup);
    topLayout->addWidget(heartbeatGroup);
    topLayout->addWidget(taskGroup);

    mainLayout->addLayout(topLayout);
}

void MainWindow::setupSystemControlArea() {
    systemControlGroup = new QGroupBox("系统控制", this);
    systemControlLayout = new QHBoxLayout(systemControlGroup);
    
    startButton = new QPushButton("启动系统", this);
    stopButton = new QPushButton("停止系统", this);
    emergencyButton = new QPushButton("紧急停止", this);
    resetButton = new QPushButton("系统复位", this);
    modeToggleButton = new QPushButton("切换模式", this);
    configButton = new QPushButton("配置管理", this);
    
    // 设置按钮样式
    emergencyButton->setStyleSheet(getButtonStyle("red"));
    startButton->setStyleSheet(getButtonStyle("green"));
    stopButton->setStyleSheet(getButtonStyle("orange"));
    
    systemControlLayout->addWidget(startButton);
    systemControlLayout->addWidget(stopButton);
    systemControlLayout->addWidget(emergencyButton);
    systemControlLayout->addWidget(resetButton);
    systemControlLayout->addWidget(modeToggleButton);
    systemControlLayout->addWidget(configButton);
    systemControlLayout->addStretch();
    
    // 连接信号
    connect(startButton, &QPushButton::clicked, this, &MainWindow::onStartSystemClicked);
    connect(stopButton, &QPushButton::clicked, this, &MainWindow::onStopSystemClicked);
    connect(emergencyButton, &QPushButton::clicked, this, &MainWindow::onEmergencyStopClicked);
    connect(resetButton, &QPushButton::clicked, this, &MainWindow::onResetSystemClicked);
    connect(modeToggleButton, &QPushButton::clicked, this, &MainWindow::onModeToggleClicked);
    connect(configButton, &QPushButton::clicked, this, &MainWindow::onConfigClicked);
    
    mainLayout->addWidget(systemControlGroup);
}

void MainWindow::setupFeederStatusArea() {
    feederStatusGroup = new QGroupBox("投料机状态", this);
    feederStatusLayout = new QGridLayout(feederStatusGroup);
    
    // 创建5台投料机的状态显示
    for (int i = 0; i < Constants::FEEDERS_COUNT; ++i) {
        QWidget* feederWidget = new QWidget(this);
        QVBoxLayout* layout = new QVBoxLayout(feederWidget);
        
        QLabel* titleLabel = new QLabel(QString("投料机 %1").arg(i + 1), this);
        titleLabel->setAlignment(Qt::AlignCenter);
        titleLabel->setStyleSheet("font-weight: bold; font-size: 14px;");
        
        QLabel* statusLabel = new QLabel("未连接", this);
        statusLabel->setAlignment(Qt::AlignCenter);
        statusLabel->setStyleSheet(getLabelStyle("red"));
        
        QLabel* weightLabel = new QLabel("重量: 0.0 kg", this);
        weightLabel->setAlignment(Qt::AlignCenter);
        
        QPushButton* controlButton = new QPushButton("控制", this);
        
        layout->addWidget(titleLabel);
        layout->addWidget(statusLabel);
        layout->addWidget(weightLabel);
        layout->addWidget(controlButton);
        
        feederWidgets.push_back(feederWidget);
        feederStatusLabels.push_back(statusLabel);
        feederWeightLabels.push_back(weightLabel);
        feederControlButtons.push_back(controlButton);
        
        feederStatusLayout->addWidget(feederWidget, i / 3, i % 3);
    }
    
    mainLayout->addWidget(feederStatusGroup);
}

void MainWindow::setupFeederTable() {
    // 创建投料机表格（仿照原始MJServer）
    feederTable = new QTableWidget(this);
    feederTable->setColumnCount(8);  // 8列（投料机编号 + 状态 + 6个料仓）
    feederTable->setRowCount(6);     // 6行投料机

    QStringList headers;
    headers << "投料机" << "状态" << "料仓1" << "料仓2" << "料仓3" << "料仓4" << "料仓5" << "料仓6";
    feederTable->setHorizontalHeaderLabels(headers);

    feederTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    feederTable->verticalHeader()->setVisible(false);
    feederTable->setWordWrap(true);
    feederTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);

    // 设置右键菜单
    feederTable->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(feederTable, &QTableWidget::customContextMenuRequested, [this](const QPoint& pos) {
        QTableWidgetItem* item = feederTable->itemAt(pos);
        if (!item) return;

        int row = item->row();
        int feederId = row + 1;

        QMenu contextMenu(this);

        // 投料机屏蔽选项
        bool feederBlocked = isFeederBlocked(feederId);
        QAction* feederAction = contextMenu.addAction(feederBlocked ? "解除投料机屏蔽" : "屏蔽投料机");
        connect(feederAction, &QAction::triggered, [this, feederId, feederBlocked]() {
            setFeederBlocked(feederId, !feederBlocked);
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                QString action = feederBlocked ? "解除屏蔽" : "屏蔽";
                logList->addItem(QString("[%1] [BLOCK] %2投料机%3").arg(timestamp, action).arg(feederId));
                logList->scrollToBottom();
            }
        });

        contextMenu.addSeparator();

        // 料仓屏蔽选项
        for (int binId = 1; binId <= 5; ++binId) {
            bool binBlocked = isBinBlocked(feederId, binId);
            QAction* binAction = contextMenu.addAction(
                QString("%1料仓%2").arg(binBlocked ? "解除屏蔽" : "屏蔽").arg(binId));
            connect(binAction, &QAction::triggered, [this, feederId, binId, binBlocked]() {
                setBinBlocked(feederId, binId, !binBlocked);
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    QString action = binBlocked ? "解除屏蔽" : "屏蔽";
                    logList->addItem(QString("[%1] [BLOCK] %2投料机%3料仓%4").arg(timestamp, action).arg(feederId).arg(binId));
                    logList->scrollToBottom();
                }
            });
        }

        contextMenu.exec(feederTable->mapToGlobal(pos));
    });

    // 初始化表格数据
    for (int row = 0; row < 6; ++row) {
        feederTable->setItem(row, 0, new QTableWidgetItem(QString("投料机%1").arg(row + 1)));
        feederTable->setItem(row, 1, new QTableWidgetItem("离线"));
        for (int col = 2; col < 8; ++col) {
            feederTable->setItem(row, col, new QTableWidgetItem("0.0kg"));
        }
    }

    mainLayout->addWidget(feederTable);
}

void MainWindow::setupAreaTable() {
    // 创建区域表格（仿照原始MJServer）
    areaTable = new QTableWidget(this);
    areaTable->setColumnCount(6);   // 6列（区域 + 状态 + 4个桶）
    areaTable->setRowCount(22);     // 22个区域

    QStringList headers;
    headers << "区域" << "状态" << "桶1" << "桶2" << "桶3" << "桶4";
    areaTable->setHorizontalHeaderLabels(headers);

    QStringList rowLabels;
    for (int c = 1; c <= 22; c++) {
        rowLabels << QString::number(c);
    }
    areaTable->setVerticalHeaderLabels(rowLabels);

    areaTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    areaTable->setWordWrap(true);
    areaTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);

    // 设置右键菜单
    areaTable->setContextMenuPolicy(Qt::CustomContextMenu);
    connect(areaTable, &QTableWidget::customContextMenuRequested, [this](const QPoint& pos) {
        QTableWidgetItem* item = areaTable->itemAt(pos);
        if (!item) return;

        int row = item->row();
        char areaId = 'A' + row;

        QMenu contextMenu(this);

        // 区域屏蔽选项
        bool areaBlocked = isAreaBlocked(areaId);
        QAction* areaAction = contextMenu.addAction(areaBlocked ? "解除区域屏蔽" : "屏蔽区域");
        connect(areaAction, &QAction::triggered, [this, areaId, areaBlocked]() {
            setAreaBlocked(areaId, !areaBlocked);
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                QString action = areaBlocked ? "解除屏蔽" : "屏蔽";
                logList->addItem(QString("[%1] [BLOCK] %2区域%3").arg(timestamp, action).arg(areaId));
                logList->scrollToBottom();
            }
        });

        contextMenu.addSeparator();

        // 桶屏蔽选项
        for (int bucketId = 1; bucketId <= 4; ++bucketId) {
            bool bucketBlocked = isBucketBlocked(areaId, bucketId);
            QAction* bucketAction = contextMenu.addAction(
                QString("%1桶%2").arg(bucketBlocked ? "解除屏蔽" : "屏蔽").arg(bucketId));
            connect(bucketAction, &QAction::triggered, [this, areaId, bucketId, bucketBlocked]() {
                setBucketBlocked(areaId, bucketId, !bucketBlocked);
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    QString action = bucketBlocked ? "解除屏蔽" : "屏蔽";
                    logList->addItem(QString("[%1] [BLOCK] %2区域%3桶%4").arg(timestamp, action).arg(areaId).arg(bucketId));
                    logList->scrollToBottom();
                }
            });
        }

        contextMenu.exec(areaTable->mapToGlobal(pos));
    });

    // 初始化表格数据
    for (int row = 0; row < 22; ++row) {
        areaTable->setItem(row, 0, new QTableWidgetItem(QString("区域%1").arg(row + 1)));
        areaTable->setItem(row, 1, new QTableWidgetItem("空闲"));
        for (int col = 2; col < 6; ++col) {
            areaTable->setItem(row, col, new QTableWidgetItem("空"));
        }
    }

    mainLayout->addWidget(areaTable);
}

void MainWindow::setupAreaStatusArea() {
    areaStatusGroup = new QGroupBox("区域状态 (A-V)", this);
    areaStatusLayout = new QGridLayout(areaStatusGroup);
    
    // 创建A-V区域的状态显示（简化版本，只显示部分区域）
    int row = 0, col = 0;
    for (char c = 'A'; c <= 'V' && row < 4; ++c) {
        QWidget* areaWidget = new QWidget(this);
        QVBoxLayout* layout = new QVBoxLayout(areaWidget);
        
        QLabel* areaLabel = new QLabel(QString("区域 %1").arg(c), this);
        areaLabel->setAlignment(Qt::AlignCenter);
        areaLabel->setStyleSheet("font-weight: bold;");
        
        layout->addWidget(areaLabel);
        
        areaWidgets[c] = areaWidget;
        areaLabels[c] = areaLabel;
        
        areaStatusLayout->addWidget(areaWidget, row, col);
        
        col++;
        if (col >= 6) {
            col = 0;
            row++;
        }
    }
    
    mainLayout->addWidget(areaStatusGroup);
}

void MainWindow::setupTaskArea() {
    taskGroup = new QGroupBox("任务管理", this);
    taskLayout = new QVBoxLayout(taskGroup);
    
    // 任务表格
    taskTable = new QTableWidget(0, 6, this);
    taskTable->setHorizontalHeaderLabels({"任务ID", "投料机", "料仓", "重量", "状态", "时间"});
    taskTable->horizontalHeader()->setStretchLastSection(true);
    taskTable->setMaximumHeight(150);
    
    // 任务日志
    taskLogText = new QTextEdit(this);
    taskLogText->setMaximumHeight(100);
    taskLogText->setReadOnly(true);
    
    taskLayout->addWidget(new QLabel("任务列表:", this));
    taskLayout->addWidget(taskTable);
    taskLayout->addWidget(new QLabel("任务日志:", this));
    taskLayout->addWidget(taskLogText);
    
    mainLayout->addWidget(taskGroup);
}

void MainWindow::setupStatusArea() {
    statusGroup = new QGroupBox("系统状态", this);
    statusLayout = new QVBoxLayout(statusGroup);
    
    systemStatusLabel = new QLabel("系统状态: 停止", this);
    connectionStatusLabel = new QLabel("连接状态: 未连接", this);
    heartbeatStatusLabel = new QLabel("心跳状态: 正常", this);
    
    systemLogText = new QTextEdit(this);
    systemLogText->setMaximumHeight(100);
    systemLogText->setReadOnly(true);
    
    statusLayout->addWidget(systemStatusLabel);
    statusLayout->addWidget(connectionStatusLabel);
    statusLayout->addWidget(heartbeatStatusLabel);
    statusLayout->addWidget(new QLabel("系统日志:", this));
    statusLayout->addWidget(systemLogText);
    
    mainLayout->addWidget(statusGroup);
}

void MainWindow::setupLogArea() {
    // 创建日志区域（仿照原始MJServer）
    logGroup = new QGroupBox("系统日志", this);
    logGroup->setFixedHeight(100);
    QVBoxLayout *logLayout = new QVBoxLayout(logGroup);

    logList = new QListWidget(this);
    logLayout->addWidget(logList);

    mainLayout->addWidget(logGroup);
}

void MainWindow::setupBottomButtons() {
    // 创建底部按钮区域（仿照原始MJServer）
    virtualMesCheckBox = new QCheckBox("启用虚拟MES", this);
    startStopButton = new QPushButton("连接设备", this);  // 修改按钮文本
    openLogButton = new QPushButton("打开日志文件夹", this);

    // 设置按钮样式
    startStopButton->setStyleSheet(getButtonStyle("green"));
    openLogButton->setStyleSheet(getButtonStyle(""));

    // 设置checkbox样式 - 使用更大的尺寸便于点击
    virtualMesCheckBox->setStyleSheet(getCheckBoxStyle());

    // 创建按钮布局
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(15); // 增加间距
    buttonLayout->addWidget(virtualMesCheckBox);
    buttonLayout->addWidget(startStopButton);
    buttonLayout->addWidget(openLogButton);
    buttonLayout->addStretch(); // 添加弹性空间

    mainLayout->addLayout(buttonLayout);

    // 连接信号
    connect(virtualMesCheckBox, &QCheckBox::stateChanged, this, &MainWindow::onVirtualMesToggled);
    connect(startStopButton, &QPushButton::clicked, this, &MainWindow::onStartStopClicked);
    connect(openLogButton, &QPushButton::clicked, this, &MainWindow::onOpenLogClicked);
}

void MainWindow::setupMenuBar() {
    QMenuBar* menuBar = this->menuBar();
    
    // 系统菜单
    QMenu* systemMenu = menuBar->addMenu("系统");
    systemMenu->addAction("启动", this, &MainWindow::onStartSystemClicked);
    systemMenu->addAction("停止", this, &MainWindow::onStopSystemClicked);
    systemMenu->addSeparator();
    systemMenu->addAction("退出", this, &QWidget::close);
    
    // 配置菜单
    QMenu* configMenu = menuBar->addMenu("配置");
    configMenu->addAction("系统配置", this, &MainWindow::onConfigClicked);
    configMenu->addSeparator();
    configMenu->addAction("屏蔽管理", this, &MainWindow::onBlockManagementClicked);
    configMenu->addSeparator();

    QAction* directModeAction = configMenu->addAction("直连模式");
    directModeAction->setCheckable(true);
    directModeAction->setChecked(directMode);
    connect(directModeAction, &QAction::toggled, this, &MainWindow::onDirectModeToggled);
    
    // 帮助菜单
    QMenu* helpMenu = menuBar->addMenu("帮助");
    helpMenu->addAction("关于", [this]() {
        QMessageBox::about(this, "关于", "MJServer Refactor v2.0\n美晶石英砂上料系统");
    });
}

void MainWindow::setupStatusBar() {
    statusBar()->showMessage("系统就绪");
}

void MainWindow::setupTimers() {
    // 状态更新定时器
    updateTimer = new QTimer(this);

    // 将所有更新操作异步化，避免阻塞UI线程
    connect(updateTimer, &QTimer::timeout, this, [this]() {
        // 使用QTimer::singleShot在下一个事件循环中异步执行
        QTimer::singleShot(0, this, [this]() {
            updateAreaTableData();
        });

        QTimer::singleShot(10, this, [this]() {
            updateTaskStatusDisplay();
        });

        QTimer::singleShot(20, this, [this]() {
            updateSystemStatusDisplay();
        });

        QTimer::singleShot(30, this, [this]() {
            updateConnectionStatusAsync();
        });

        QTimer::singleShot(40, this, [this]() {
            updateHeartbeatStatusAsync();
        });

        QTimer::singleShot(50, this, [this]() {
            updateBlockStatusDisplay();
        });

        QTimer::singleShot(60, this, [this]() {
            updateCurrentSandTypeTableAsync();
        });
    });

    // 启动定时器，增加间隔减少UI负担
    updateTimer->start(3000);  // 3秒更新一次，减少频率
}

void MainWindow::onStartSystemClicked() {
    if (controlSystem && !systemRunning) {
        if (controlSystem->start()) {
            systemRunning = true;
            startButton->setEnabled(false);
            stopButton->setEnabled(true);
            updateSystemStatusDisplay();

            // 添加到日志列表
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [SYSTEM] 系统启动成功").arg(timestamp));
                logList->scrollToBottom();
            }
            statusBar()->showMessage("系统运行中");
        } else {
            QMessageBox::critical(this, "启动失败", "无法启动控制系统");
        }
    }
}

void MainWindow::onStopSystemClicked() {
    if (controlSystem && systemRunning) {
        controlSystem->stop();
        systemRunning = false;
        startButton->setEnabled(true);
        stopButton->setEnabled(false);
        updateSystemStatusDisplay();

        // 添加到日志列表
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [SYSTEM] 系统已停止").arg(timestamp));
            logList->scrollToBottom();
        }
        statusBar()->showMessage("系统已停止");
    }
}

void MainWindow::onEmergencyStopClicked() {
    if (controlSystem) {
        controlSystem->emergencyStop();

        // 添加到日志列表
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [EMERGENCY] 紧急停止已激活").arg(timestamp));
            logList->scrollToBottom();
        }
        statusBar()->showMessage("紧急停止");

        // 更新系统状态
        systemRunning = false;
        startButton->setEnabled(true);
        stopButton->setEnabled(false);
        updateSystemStatusDisplay();
    }
}

void MainWindow::onResetSystemClicked() {
    if (controlSystem) {
        controlSystem->reset();

        // 添加到日志列表
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [SYSTEM] 系统复位完成").arg(timestamp));
            logList->scrollToBottom();
        }
        statusBar()->showMessage("系统复位");

        // 恢复UI状态
        systemRunning = false;
        startButton->setEnabled(true);
        stopButton->setEnabled(false);
        updateSystemStatusDisplay();
    }
}

void MainWindow::onModeToggleClicked() {
    autoMode = !autoMode;
    if (controlSystem) {
        controlSystem->setSystemMode(autoMode);
    }

    modeToggleButton->setText(autoMode ? "手动模式" : "自动模式");

    // 添加到日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] [MODE] 切换到%2模式").arg(timestamp, autoMode ? "自动" : "手动"));
        logList->scrollToBottom();
    }
}

void MainWindow::onConfigClicked() {
    // 切换到系统配置Tab页面
    if (tabWidget) {
        // 查找系统配置Tab的索引
        for (int i = 0; i < tabWidget->count(); ++i) {
            if (tabWidget->tabText(i) == "系统配置") {
                tabWidget->setCurrentIndex(i);
                break;
            }
        }
    }

    // 刷新配置显示
    updateSystemConfigDisplay();

    // 添加到日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] [CONFIG] 打开系统配置界面").arg(timestamp));
        logList->scrollToBottom();
    }
}

void MainWindow::onDirectModeToggled(bool enabled) {
    if (controlSystem) {
        controlSystem->enableDirectMode(enabled);
        directMode = enabled;

        // 添加到日志列表
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [MODE] 直连模式%2").arg(timestamp, enabled ? "启用" : "禁用"));
            logList->scrollToBottom();
        }
        updateSystemStatusDisplay();
    }
}

void MainWindow::updateFeederStatus() {
    if (!controlSystem) {
        // 如果没有控制系统，显示所有投料机为未连接状态
        for (int i = 1; i <= Constants::FEEDERS_COUNT; ++i) {
            updateFeederWidget(i, nullptr);
        }
        return;
    }

    auto feederStatus = controlSystem->getFeederStatus();

    // 创建一个集合来跟踪哪些投料机有数据
    std::set<int> feedersWithData;

    for (const auto& status : feederStatus) {
        if (status) {
            updateFeederWidget(status->feederId, status);
            feedersWithData.insert(status->feederId);
        }
    }

    // 对于没有数据的投料机，显示为未连接
    for (int i = 1; i <= Constants::FEEDERS_COUNT; ++i) {
        if (feedersWithData.find(i) == feedersWithData.end()) {
            updateFeederWidget(i, nullptr);
        }
    }
}

void MainWindow::updateAreaStatus() {
    if (!controlSystem) return;
    
    auto areaStatus = controlSystem->getAreaStatus();
    for (const auto& pair : areaStatus) {
        updateAreaWidget(pair.first, pair.second);
    }
}

void MainWindow::updateSystemStatus() {
    updateSystemStatusDisplay();
    updateConnectionStatus();
    updateHeartbeatStatus();
}

void MainWindow::updateTaskStatus() {
    if (!controlSystem) return;
    
    auto taskStatus = controlSystem->getTaskStatus();
    if (taskStatus) {
        // 更新任务表格（简化实现）
        // 实际项目中应该维护完整的任务列表
    }
}

void MainWindow::updateFeederWidget(int feederId, std::shared_ptr<FeederStatus> status) {
    if (feederId < 0 || feederId >= feederStatusLabels.size()) return;

    // 检查是否被屏蔽 (注意：这里的feederId可能是1基索引，需要确认)
    bool blocked = isFeederBlocked(feederId);

    if (blocked) {
        feederStatusLabels[feederId - 1]->setText("已屏蔽");
        feederStatusLabels[feederId - 1]->setStyleSheet(getLabelStyle("orange"));
        feederWeightLabels[feederId - 1]->setText("重量: -- kg");
    } else if (status) {
        feederStatusLabels[feederId - 1]->setText(status->isConnected ? "已连接" : "未连接");
        feederStatusLabels[feederId - 1]->setStyleSheet(getLabelStyle(status->isConnected ? "green" : "red"));

        // 计算总重量
        float totalWeight = 0.0f;
        for (const auto& bin : status->bins) {
            totalWeight += bin.currentWeight;
        }
        feederWeightLabels[feederId - 1]->setText(QString("重量: %1 kg").arg(totalWeight, 0, 'f', 1));
    } else {
        // 没有状态数据时显示未连接
        feederStatusLabels[feederId - 1]->setText("未连接");
        feederStatusLabels[feederId - 1]->setStyleSheet(getLabelStyle("red"));
        feederWeightLabels[feederId - 1]->setText("重量: 0.0 kg");
    }
}

void MainWindow::updateAreaWidget(char areaId, std::shared_ptr<AreaData> data) {
    auto it = areaLabels.find(areaId);
    if (it != areaLabels.end() && data) {
        // 更新区域显示（简化实现）
        it->second->setToolTip(QString("砂类型: %1").arg(QString::fromStdString(data->sandType)));
    }
}

void MainWindow::updateSystemStatusDisplay() {
    // 更新模式标签（使用新UI布局中的组件）
    if (modeLabel) {
        QString statusText;
        if (controlSystem) {
            bool baseServersRunning = controlSystem->areBaseServersRunning();
            bool allDevicesConnected = controlSystem->isRunning();

            if (allDevicesConnected) {
                statusText = "全部运行";
            } else if (baseServersRunning) {
                statusText = "基础服务器运行";
            } else {
                statusText = "停止";
            }
        } else {
            statusText = "未初始化";
        }

        modeLabel->setText(QString("模式: %1 (%2)")
            .arg(statusText)
            .arg(directMode ? "直连" : "PLC"));
    }

    // 更新错误标签
    if (errorLabel) {
        bool hasError = controlSystem ? controlSystem->hasError() : false;
        errorLabel->setText(QString("错误: %1").arg(hasError ? "有错误" : "无"));
        errorLabel->setStyleSheet(hasError ? "color: red;" : "color: green;");
    }
}

void MainWindow::updateConnectionStatus() {
    // 保留原方法用于兼容性，但标记为已弃用
    updateConnectionStatusAsync();
}

void MainWindow::updateConnectionStatusAsync() {
    // 异步获取连接状态，避免阻塞UI
    if (!controlSystem || !errorLabel) {
        return;
    }

    // 在后台线程中获取状态
    std::thread([this]() {
        bool connected = false;
        QString statusText = "未连接";

        try {
            // 检查控制系统是否有错误
            if (!controlSystem->hasError()) {
                // 进一步检查是否真正连接（可能阻塞的操作）
                auto feederStatusList = controlSystem->getFeederStatus();
                bool hasConnectedFeeders = false;
                for (const auto& status : feederStatusList) {
                    if (status && status->isConnected) {
                        hasConnectedFeeders = true;
                        break;
                    }
                }

                if (hasConnectedFeeders) {
                    connected = true;
                    statusText = "已连接";
                } else {
                    statusText = "设备离线";
                }
            } else {
                statusText = "连接异常";
            }
        } catch (...) {
            // 异常情况下保持未连接状态
            statusText = "连接异常";
        }

        // 在主线程中更新UI
        QMetaObject::invokeMethod(this, [this, connected, statusText]() {
            if (errorLabel) {
                errorLabel->setText(QString("连接: %1").arg(statusText));
                errorLabel->setStyleSheet(connected ? "color: green;" : "color: red;");
            }
        }, Qt::QueuedConnection);
    }).detach();
}

void MainWindow::updateHeartbeatStatus() {
    // 保留原方法用于兼容性
    updateHeartbeatStatusAsync();
}

void MainWindow::updateHeartbeatStatusAsync() {
    // 异步获取心跳状态，避免阻塞UI
    if (!controlSystem) {
        return;
    }

    // 在后台线程中获取状态
    std::thread([this]() {
        bool mesOK = false;
        bool plcOK = false;

        try {
            // 可能阻塞的心跳检查操作
            mesOK = controlSystem->isHeartbeatOK();
            plcOK = controlSystem->isHeartbeatOK(); // 假设MES和PLC使用相同的心跳检查
        } catch (...) {
            // 异常情况下保持false状态
        }

        // 在主线程中更新UI
        QMetaObject::invokeMethod(this, [this, mesOK, plcOK]() {
            if (mesHeartbeatLabel) {
                mesHeartbeatLabel->setText(QString("MES心跳: %1").arg(mesOK ? "正常" : "异常"));
                mesHeartbeatLabel->setStyleSheet(mesOK ? "color: green;" : "color: red;");
            }

            if (plcHeartbeatLabel) {
                plcHeartbeatLabel->setText(QString("PLC心跳: %1").arg(plcOK ? "正常" : "异常"));
                plcHeartbeatLabel->setStyleSheet(plcOK ? "color: green;" : "color: red;");
            }
        }, Qt::QueuedConnection);
    }).detach();
}

void MainWindow::handleSystemError(const std::string& error) {
    // 添加到新UI布局中的日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] [ERROR] 系统错误: %2").arg(timestamp, QString::fromStdString(error)));
        logList->scrollToBottom();
    }

    // 同时显示错误对话框
    QMessageBox::critical(this, "系统错误", QString::fromStdString(error));
}

void MainWindow::handleTaskLog(const std::string& message) {
    // 添加到新UI布局中的日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] [TASK] %2").arg(timestamp, QString::fromStdString(message)));
        logList->scrollToBottom();
    }
}

void MainWindow::handleStatusUpdate(const std::string& status) {
    // 添加到新UI布局中的日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] [STATUS] %2").arg(timestamp, QString::fromStdString(status)));
        logList->scrollToBottom();
    }

    // 更新状态栏
    statusBar()->showMessage(QString::fromStdString(status));
}

void MainWindow::updateFeederTableData(const std::vector<std::shared_ptr<FeederStatus>>& feederStatus) {
    if (!feederTable) return;

    for (const auto& status : feederStatus) {
        if (!status || status->feederId < 1 || status->feederId > 6) continue;

        int row = status->feederId - 1; // 转换为0基索引

        // 更新投料机状态
        QString statusText;
        if (isFeederBlocked(status->feederId)) {
            statusText = "已屏蔽";
        } else if (!status->isConnected) {
            statusText = "离线";
        } else {
            statusText = "在线";
            if (!status->heartbeatOK) statusText += " (心跳异常)";

            // 检查料仓屏蔽状态
            QStringList blockedBins;
            for (int binId = 1; binId <= 5; ++binId) {
                if (isBinBlocked(status->feederId, binId)) {
                    blockedBins << QString("料仓%1").arg(binId);
                }
            }
            if (!blockedBins.isEmpty()) {
                statusText += " (部分屏蔽:" + blockedBins.join(",") + ")";
            }
        }
        feederTable->setItem(row, 1, new QTableWidgetItem(statusText));

        // 更新料仓重量（使用bins数组）
        for (int i = 0; i < 6 && i < status->bins.size(); ++i) {
            feederTable->setItem(row, i + 2, new QTableWidgetItem(
                QString("%1kg").arg(status->bins[i].currentWeight, 0, 'f', 1)));
        }

        // 设置行颜色
        QColor rowColor;
        if (isFeederBlocked(status->feederId)) {
            rowColor = QColor(255, 150, 150); // 深红色表示屏蔽
        } else if (!status->isConnected) {
            rowColor = QColor(255, 200, 200); // 浅红色表示离线
        } else {
            // 检查是否有料仓被屏蔽
            bool hasBinBlocked = false;
            for (int binId = 1; binId <= 5; ++binId) {
                if (isBinBlocked(status->feederId, binId)) {
                    hasBinBlocked = true;
                    break;
                }
            }
            rowColor = hasBinBlocked ? QColor(255, 255, 150) : QColor(200, 255, 200); // 黄色表示部分屏蔽，绿色表示正常
        }

        for (int col = 0; col < feederTable->columnCount(); ++col) {
            if (feederTable->item(row, col)) {
                feederTable->item(row, col)->setBackgroundColor(rowColor);
            }
        }
    }
}

void MainWindow::updateTaskStatusDisplay() {
    if (!controlSystem) return;

    // 获取当前任务状态
    auto taskStatus = controlSystem->getTaskStatus();
    if (taskStatus) {
        // 更新任务状态标签（简化版本）
        if (taskReadyLabel) {
            taskReadyLabel->setText(QString("当前任务: %1").arg(QString::fromStdString(taskStatus->taskId)));
        }
        if (taskDumpingLabel) {
            QString statusText;
            switch (taskStatus->status) {
                case TaskStatus::PENDING: statusText = "等待中"; break;
                case TaskStatus::RUNNING: statusText = "执行中"; break;
                case TaskStatus::COMPLETED: statusText = "已完成"; break;
                case TaskStatus::FAILED: statusText = "失败"; break;
                case TaskStatus::CANCELLED: statusText = "已取消"; break;
                default: statusText = "未知"; break;
            }
            taskDumpingLabel->setText(QString("状态: %1").arg(statusText));
        }
        if (taskCompletedLabel) {
            taskCompletedLabel->setText(QString("进度: %1%").arg(taskStatus->progress * 100, 0, 'f', 1));
        }
        if (remainingWeightLabel) {
            remainingWeightLabel->setText(QString("消息: %1").arg(QString::fromStdString(taskStatus->message)));
        }
    } else {
        // 没有任务时显示默认信息
        if (taskReadyLabel) taskReadyLabel->setText("当前任务: 无");
        if (taskDumpingLabel) taskDumpingLabel->setText("状态: 空闲");
        if (taskCompletedLabel) taskCompletedLabel->setText("进度: 0%");
        if (remainingWeightLabel) remainingWeightLabel->setText("消息: 系统就绪");
    }
}

void MainWindow::updateAreaTableData() {
    if (!areaTable || !controlSystem) return;

    // 获取区域状态数据
    auto areaStatusMap = controlSystem->getAreaStatus();

    for (int row = 0; row < areaTable->rowCount(); ++row) {
        char areaId = 'A' + row; // 假设区域ID从A开始

        // 查找对应的区域数据
        auto it = areaStatusMap.find(areaId);

        if (it != areaStatusMap.end()) {
            auto data = it->second;

            // 更新区域状态
            QString statusText;
            if (isAreaBlocked(areaId)) {
                statusText = "已屏蔽";
            } else {
                bool isOccupied = !data->sandType.empty();
                statusText = isOccupied ? "占用" : "空闲";

                // 检查桶屏蔽状态
                QStringList blockedBuckets;
                for (int bucketId = 1; bucketId <= 4; ++bucketId) {
                    if (isBucketBlocked(areaId, bucketId)) {
                        blockedBuckets << QString("桶%1").arg(bucketId);
                    }
                }
                if (!blockedBuckets.isEmpty()) {
                    statusText += " (部分屏蔽:" + blockedBuckets.join(",") + ")";
                }
            }
            areaTable->setItem(row, 1, new QTableWidgetItem(statusText));

            // 更新桶状态
            for (int col = 2; col < 6 && (col - 2) < data->buckets.size(); ++col) {
                const auto& bucket = data->buckets[col - 2];
                QString bucketText = (bucket.weight > 0.0f) ?
                    QString("%1 (%2kg)").arg(QString::fromStdString(bucket.batchNumber))
                                        .arg(bucket.weight, 0, 'f', 1) : "空";
                areaTable->setItem(row, col, new QTableWidgetItem(bucketText));
            }

            // 设置行颜色
            QColor rowColor;
            if (isAreaBlocked(areaId)) {
                rowColor = QColor(255, 150, 150); // 深红色表示屏蔽
            } else {
                bool isOccupied = !data->sandType.empty();
                // 检查是否有桶被屏蔽
                bool hasBucketBlocked = false;
                for (int bucketId = 1; bucketId <= 4; ++bucketId) {
                    if (isBucketBlocked(areaId, bucketId)) {
                        hasBucketBlocked = true;
                        break;
                    }
                }

                if (hasBucketBlocked) {
                    rowColor = QColor(255, 255, 150); // 黄色表示部分屏蔽
                } else if (isOccupied) {
                    rowColor = QColor(255, 255, 200); // 浅黄色表示占用
                } else {
                    rowColor = QColor(200, 255, 200); // 绿色表示空闲
                }
            }

            for (int col = 0; col < areaTable->columnCount(); ++col) {
                if (areaTable->item(row, col)) {
                    areaTable->item(row, col)->setBackgroundColor(rowColor);
                }
            }
        } else {
            // 如果没有数据，显示为空闲
            areaTable->setItem(row, 1, new QTableWidgetItem("空闲"));
            for (int col = 2; col < 6; ++col) {
                areaTable->setItem(row, col, new QTableWidgetItem("空"));
            }
        }
    }
}

QString MainWindow::formatWeight(float weight) {
    return QString::number(weight, 'f', 1) + " kg";
}

QString MainWindow::formatTime(const std::string& timeStr) {
    // 简化的时间格式化
    return QString::fromStdString(timeStr);
}

QColor MainWindow::getStatusColor(bool isOK) {
    return isOK ? QColor(0, 255, 0) : QColor(255, 0, 0);
}

void MainWindow::addLogMessage(const QString& message, QTextEdit* logWidget) {
    if (logWidget) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logWidget->append(QString("[%1] %2").arg(timestamp, message));
        
        // 限制日志行数
        QTextDocument* doc = logWidget->document();
        if (doc->blockCount() > 100) {
            QTextCursor cursor = logWidget->textCursor();
            cursor.movePosition(QTextCursor::Start);
            cursor.select(QTextCursor::BlockUnderCursor);
            cursor.removeSelectedText();
        }
    }
}

void MainWindow::showErrorMessage(const QString& title, const QString& message) {
    QMessageBox::critical(this, title, message);
}

void MainWindow::showInfoMessage(const QString& title, const QString& message) {
    QMessageBox::information(this, title, message);
}

void MainWindow::applyStyles() {
    setStyleSheet(R"(
        /* 主窗口 - 蓝白科技风格 */
        QMainWindow {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f8f9fa, stop: 1 #e9ecef);
            color: #212529;
            font-size: 14px;
        }

        /* 组框 - 蓝白科技风格 */
        QGroupBox {
            font-weight: bold;
            font-size: 16px;
            border: 2px solid #0d6efd;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 15px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffffff, stop: 1 #f8f9fa);
            color: #0d6efd;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background-color: #ffffff;
            border-radius: 4px;
            font-size: 16px;
        }

        /* 标签 - 蓝白科技风格 */
        QLabel {
            color: #495057;
            font-size: 15px;
            font-weight: 500;
        }

        /* 按钮 - 蓝白科技风格 */
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0d6efd, stop: 1 #0b5ed7);
            color: white;
            font-weight: bold;
            font-size: 15px;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            min-height: 25px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0b5ed7, stop: 1 #0a58ca);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0a58ca, stop: 1 #084298);
        }

        /* CheckBox - 更大更容易点击的蓝白科技风格 */
        QCheckBox {
            font-size: 16px;
            font-weight: bold;
            color: #495057;
            spacing: 10px;
            padding: 6px;
        }
        QCheckBox::indicator {
            width: 24px;
            height: 24px;
            border: 2px solid #0d6efd;
            border-radius: 4px;
            background-color: #ffffff;
        }
        QCheckBox::indicator:hover {
            border: 2px solid #0b5ed7;
            background-color: #e7f1ff;
        }
        QCheckBox::indicator:checked {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0d6efd, stop: 1 #0b5ed7);
            border: 2px solid #0b5ed7;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjg1NCA0LjE0NkwxNC4xNDYgNC44NTRMNi41IDEyLjVMMi4xNDYgOC4xNDZMMi44NTQgNy44NTRMNi41IDExLjVMMTMuODU0IDQuMTQ2WiIgZmlsbD0id2hpdGUiLz4KPHN2Zz4K);
        }
        QCheckBox::indicator:checked:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0b5ed7, stop: 1 #0a58ca);
        }

        /* 表格 - 蓝白科技风格 */
        QTableWidget {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            gridline-color: #dee2e6;
            selection-background-color: #e7f1ff;
            font-size: 14px;
        }
        QTableWidget::item {
            padding: 10px;
            border-bottom: 1px solid #f8f9fa;
            font-size: 14px;
        }
        QTableWidget::item:selected {
            background-color: #e7f1ff;
            color: #0d6efd;
        }
        QHeaderView::section {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0d6efd, stop: 1 #0b5ed7);
            color: white;
            font-weight: bold;
            font-size: 15px;
            padding: 12px;
            border: none;
            border-right: 1px solid #0a58ca;
            min-height: 25px;
        }

        /* 列表控件 - 蓝白科技风格 */
        QListWidget {
            background-color: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            selection-background-color: #e7f1ff;
            font-size: 14px;
        }
        QListWidget::item {
            padding: 10px;
            border-bottom: 1px solid #f8f9fa;
            font-size: 14px;
            min-height: 20px;
        }
        QListWidget::item:selected {
            background-color: #e7f1ff;
            color: #0d6efd;
        }

        /* Tab控件 - 蓝白科技风格 */
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            background-color: #ffffff;
            border-radius: 6px;
            font-size: 14px;
        }
        QTabBar::tab {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #f8f9fa, stop: 1 #e9ecef);
            color: #6c757d;
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            font-weight: bold;
            font-size: 15px;
            min-width: 80px;
        }
        QTabBar::tab:selected {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0d6efd, stop: 1 #0b5ed7);
            color: white;
        }
        QTabBar::tab:hover:!selected {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #e7f1ff, stop: 1 #cfe2ff);
            color: #0d6efd;
        }
    )");
}

QString MainWindow::getButtonStyle(const QString& color) {
    if (color == "red") {
        return R"(
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #dc3545, stop: 1 #c82333);
                color: white;
                font-weight: bold;
                font-size: 15px;
                padding: 12px 24px;
                border: none;
                border-radius: 6px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #c82333, stop: 1 #bd2130);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #bd2130, stop: 1 #b21f2d);
            }
        )";
    } else if (color == "green") {
        return R"(
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #198754, stop: 1 #157347);
                color: white;
                font-weight: bold;
                font-size: 15px;
                padding: 12px 24px;
                border: none;
                border-radius: 6px;
                min-height: 25px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #157347, stop: 1 #146c43);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #146c43, stop: 1 #13653f);
            }
        )";
    } else if (color == "orange") {
        return R"(
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #fd7e14, stop: 1 #e8681c);
                color: white;
                font-weight: bold;
                font-size: 13px;
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                min-height: 20px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #e8681c, stop: 1 #dc5f1a);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #dc5f1a, stop: 1 #d05718);
            }
        )";
    }
    return R"(
        QPushButton {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #6c757d, stop: 1 #5c636a);
            color: white;
            font-weight: bold;
            font-size: 13px;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            min-height: 20px;
        }
        QPushButton:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #5c636a, stop: 1 #545b62);
        }
        QPushButton:pressed {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #545b62, stop: 1 #4e555b);
        }
    )";
}

QString MainWindow::getLabelStyle(const QString& color) {
    if (color == "red") {
        return "QLabel { color: #dc3545; font-weight: bold; font-size: 16px; }";
    } else if (color == "green") {
        return "QLabel { color: #198754; font-weight: bold; font-size: 16px; }";
    } else if (color == "orange") {
        return "QLabel { color: #fd7e14; font-weight: bold; font-size: 16px; }";
    } else if (color == "blue") {
        return "QLabel { color: #0d6efd; font-weight: bold; font-size: 16px; }";
    }
    return "QLabel { color: #495057; font-weight: bold; font-size: 16px; }";
}

QString MainWindow::getGroupBoxStyle() {
    return R"(
        QGroupBox {
            font-weight: bold;
            font-size: 16px;
            border: 2px solid #0d6efd;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 15px;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #ffffff, stop: 1 #f8f9fa);
            color: #0d6efd;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            background-color: #ffffff;
            border-radius: 4px;
            font-size: 16px;
        }
    )";
}

QString MainWindow::getCheckBoxStyle() {
    return R"(
        QCheckBox {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            spacing: 15px;
            padding: 10px;
            min-height: 35px;
        }
        QCheckBox::indicator {
            width: 28px;
            height: 28px;
            border: 3px solid #0d6efd;
            border-radius: 6px;
            background-color: #ffffff;
        }
        QCheckBox::indicator:hover {
            border: 3px solid #0b5ed7;
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #e7f1ff, stop: 1 #cfe2ff);
        }
        QCheckBox::indicator:checked {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0d6efd, stop: 1 #0b5ed7);
            border: 3px solid #0b5ed7;
        }
        QCheckBox::indicator:checked:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #0b5ed7, stop: 1 #0a58ca);
            border: 3px solid #0a58ca;
        }
        QCheckBox::indicator:checked {
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE3LjMxNzUgNS4xODI1TDE3LjY4MjUgNS44MTc1TDguMTI1IDE1LjM3NUwyLjY4MjUgOS45MzI1TDMuMzE3NSA5LjMxNzVMOC4xMjUgMTQuMTI1TDE3LjMxNzUgNS4xODI1WiIgZmlsbD0id2hpdGUiLz4KPHN2Zz4K);
        }
    )";
}

void MainWindow::adjustTableHeight(QTableWidget* table, int maxHeight) {
    if (!table) return;

    // 计算表格内容的实际高度
    int headerHeight = table->horizontalHeader()->height();
    int rowHeight = 0;

    // 计算所有行的高度
    for (int i = 0; i < table->rowCount(); ++i) {
        rowHeight += table->rowHeight(i);
    }

    // 计算总高度（表头 + 所有行 + 一些边距）
    int totalHeight = headerHeight + rowHeight + 20; // 20像素边距

    // 设置最小高度，确保至少能显示表头和一些空间
    int defaultRowHeight = 25; // 默认行高
    int minHeight = headerHeight + defaultRowHeight * 3 + 20; // 至少显示3行的空间

    if (table->rowCount() == 0 || totalHeight < minHeight) {
        totalHeight = minHeight;
    }

    // 如果指定了最大高度，则限制在最大高度内
    if (maxHeight > 0 && totalHeight > maxHeight) {
        totalHeight = maxHeight;
        // 当超过最大高度时，启用垂直滚动条
        table->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    } else {
        // 当内容能完全显示时，隐藏垂直滚动条
        table->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    }

    // 应用高度
    table->setFixedHeight(totalHeight);
}

// 其他槽函数的简化实现
void MainWindow::onStartFeedingClicked() {
    addLogMessage("开始投料", taskLogText);
}

void MainWindow::onStopFeedingClicked() {
    addLogMessage("停止投料", taskLogText);
}

void MainWindow::onStopAllFeedingClicked() {
    addLogMessage("停止所有投料", taskLogText);
}

// 新增的槽函数实现（仿照原始MJServer）
void MainWindow::onVirtualMesToggled(int state) {
    bool enabled = (state == Qt::Checked);
    QString message = enabled ? "启用虚拟MES" : "禁用虚拟MES";

    // 添加到日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] %2").arg(timestamp, message));
        logList->scrollToBottom();
    }
}

void MainWindow::onStartStopClicked() {
    if (systemRunning) {
        // 停止系统
        if (controlSystem) {
            controlSystem->stop();
        }

        systemRunning = false;
        startStopButton->setText("连接设备");
        startStopButton->setStyleSheet(getButtonStyle("green"));

        QString message = "系统已停止";
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] %2").arg(timestamp, message));
            logList->scrollToBottom();
        }

        // 更新状态标签
        if (modeLabel) {
            modeLabel->setText("模式: 停止");
        }
    } else {
        // 启动系统 - 连接其他设备
        if (controlSystem) {
            // 检查基础服务器是否已运行
            if (!controlSystem->areBaseServersRunning()) {
                QString errorMsg = "基础服务器未运行，请先启动ABB和MES服务器";
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] [ERROR] %2").arg(timestamp, errorMsg));
                    logList->scrollToBottom();
                }
                QMessageBox::warning(this, "启动失败", errorMsg);
                return;
            }

            // 连接其他设备
            if (controlSystem->connectOtherDevices()) {
                systemRunning = true;
                startStopButton->setText("断开设备");
                startStopButton->setStyleSheet(getButtonStyle("red"));

                QString message = "系统已启动，所有设备已连接";
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] %2").arg(timestamp, message));
                    logList->scrollToBottom();
                }

                // 更新状态标签
                if (modeLabel) {
                    modeLabel->setText(autoMode ? "模式: 自动" : "模式: 手动");
                }
            } else {
                QString errorMsg = "连接设备失败";
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] [ERROR] %2").arg(timestamp, errorMsg));
                    logList->scrollToBottom();
                }
                QMessageBox::critical(this, "启动失败", errorMsg);
            }
        } else {
            QString errorMsg = "控制系统未初始化";
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] %2").arg(timestamp, errorMsg));
                logList->scrollToBottom();
            }
            QMessageBox::critical(this, "启动失败", errorMsg);
        }
    }
}

void MainWindow::onOpenLogClicked() {
    QString message = "打开日志文件夹";

    // 添加到日志列表
    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] %2").arg(timestamp, message));
        logList->scrollToBottom();
    }

    // 这里可以添加实际打开日志文件夹的代码
    // QDesktopServices::openUrl(QUrl::fromLocalFile(logPath));
}

// Tab页面创建方法
void MainWindow::createCurrentSandTypeTab() {
    currentSandTypeTable = new QTableWidget(this);
    currentSandTypeTable->setColumnCount(6); // 6个料口
    currentSandTypeTable->setRowCount(6); // 6个投料机

    // 设置表头
    QStringList headers;
    headers << "料口1" << "料口2" << "料口3" << "料口4" << "料口5" << "料口6";
    currentSandTypeTable->setHorizontalHeaderLabels(headers);

    QStringList rowLabels;
    for (int i = 1; i <= 6; i++) {
        rowLabels << QString("投料机%1").arg(i);
    }
    currentSandTypeTable->setVerticalHeaderLabels(rowLabels);

    // 调整表格样式
    currentSandTypeTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    currentSandTypeTable->verticalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 初始化表格数据
    for (int row = 0; row < 6; ++row) {
        for (int col = 0; col < 6; ++col) {
            auto *item = new QTableWidgetItem("");
            item->setTextAlignment(Qt::AlignCenter);
            currentSandTypeTable->setItem(row, col, item);
        }
    }

    tabWidget->addTab(currentSandTypeTable, "当前类型");
}

void MainWindow::createSandTypeHistoryTab() {
    sandTypeHistoryTable = new QTableWidget(this);
    sandTypeHistoryTable->setColumnCount(5);

    // 设置表头
    QStringList headers;
    headers << "时间" << "投料机" << "料口" << "原类型" << "新类型";
    sandTypeHistoryTable->setHorizontalHeaderLabels(headers);

    // 调整表格样式
    sandTypeHistoryTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    tabWidget->addTab(sandTypeHistoryTable, "类型变化记录");
}

// 砂类型表格更新方法
void MainWindow::updateCurrentSandTypeTable(const std::vector<std::shared_ptr<FeederStatus>>& feederStatus) {
    if (!currentSandTypeTable) return;

    try {
        for (size_t i = 0; i < feederStatus.size() && i < 6; i++) {
            const auto &feeder = feederStatus.at(i);
            if (!feeder) continue;

            for (int j = 0; j < 6; j++) {
                QString sandType = "";

                // 从FeederStatus获取砂类型信息（需要根据实际数据结构调整）
                if (j < feeder->bins.size()) {
                    // 这里假设bins中有砂类型信息，实际需要根据数据结构调整
                    sandType = QString("类型%1").arg(j + 1); // 临时示例
                }

                // 检查是否需要更新历史记录
                QTableWidgetItem *currentItem = currentSandTypeTable->item(i, j);
                if (currentItem) {
                    QString oldType = currentItem->text();
                    if (oldType != sandType && !oldType.isEmpty()) {
                        updateSandTypeHistoryTable(
                            QString("投料机%1").arg(i + 1).toStdString(),
                            j + 1,
                            oldType.toStdString(),
                            sandType.toStdString()
                        );
                    }
                }

                // 更新当前料号表格
                auto *item = new QTableWidgetItem(sandType);
                item->setTextAlignment(Qt::AlignCenter);
                currentSandTypeTable->setItem(i, j, item);
            }
        }
    } catch (const std::exception &e) {
        // 记录错误但不中断程序
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新砂类型表格失败: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::updateCurrentSandTypeTableAsync() {
    // 异步获取投料机状态并更新砂类型表格
    if (!controlSystem) {
        return;
    }

    // 在后台线程中获取状态
    std::thread([this]() {
        std::vector<std::shared_ptr<FeederStatus>> feederStatus;

        try {
            // 可能阻塞的操作
            feederStatus = controlSystem->getFeederStatus();
        } catch (...) {
            // 异常情况下使用空状态
        }

        // 在主线程中更新UI
        QMetaObject::invokeMethod(this, [this, feederStatus]() {
            updateCurrentSandTypeTable(feederStatus);
        }, Qt::QueuedConnection);
    }).detach();
}

void MainWindow::updateSandTypeHistoryTable(const std::string &feederName, int binId,
                                            const std::string &oldType, const std::string &newType) {
    // 添加新记录
    SandTypeChange change{
        QDateTime::currentDateTime(),
        feederName,
        binId,
        oldType,
        newType
    };
    sandTypeHistory.insert(sandTypeHistory.begin(), change);

    // 限制历史记录数量
    if (sandTypeHistory.size() > MAX_HISTORY_ITEMS) {
        sandTypeHistory.pop_back();
    }

    // 更新表格显示
    if (sandTypeHistoryTable) {
        sandTypeHistoryTable->setRowCount(sandTypeHistory.size());

        for (size_t i = 0; i < sandTypeHistory.size(); i++) {
            const auto &record = sandTypeHistory[i];

            sandTypeHistoryTable->setItem(i, 0, new QTableWidgetItem(
                                              record.timestamp.toString("yyyy-MM-dd hh:mm:ss")));
            sandTypeHistoryTable->setItem(i, 1, new QTableWidgetItem(
                                              QString::fromStdString(record.feederName)));
            sandTypeHistoryTable->setItem(i, 2, new QTableWidgetItem(
                                              QString::number(record.binId)));
            sandTypeHistoryTable->setItem(i, 3, new QTableWidgetItem(
                                              QString::fromStdString(record.oldType)));
            sandTypeHistoryTable->setItem(i, 4, new QTableWidgetItem(
                                              QString::fromStdString(record.newType)));
        }
    }
}

// 创建屏蔽管理Tab页面
void MainWindow::createBlockManagementTab() {
    blockManagementTab = new QWidget(this);
    QVBoxLayout *mainLayout = new QVBoxLayout(blockManagementTab);

    // 创建说明标签
    QLabel *infoLabel = new QLabel("屏蔽管理 - 屏蔽后的设备将不参与任务调度", this);
    infoLabel->setStyleSheet("font-weight: bold; color: #666; padding: 10px;");
    mainLayout->addWidget(infoLabel);

    // 创建水平布局容器
    QHBoxLayout *contentLayout = new QHBoxLayout();

    // 左侧：投料机屏蔽管理
    QGroupBox *feederGroup = new QGroupBox("投料机屏蔽管理", this);
    QVBoxLayout *feederLayout = new QVBoxLayout(feederGroup);

    // 投料机屏蔽表格
    QTableWidget *feederBlockTable = new QTableWidget(6, 7, this); // 6行7列（投料机+6个料仓）
    QStringList feederHeaders;
    feederHeaders << "投料机" << "整机屏蔽" << "料仓1" << "料仓2" << "料仓3" << "料仓4" << "料仓5";
    feederBlockTable->setHorizontalHeaderLabels(feederHeaders);
    feederBlockTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    feederBlockTable->verticalHeader()->setVisible(false);

    // 初始化投料机屏蔽表格
    for (int row = 0; row < 6; ++row) {
        // 投料机名称
        feederBlockTable->setItem(row, 0, new QTableWidgetItem(QString("投料机%1").arg(row + 1)));

        // 整机屏蔽复选框
        QCheckBox *feederCheckBox = new QCheckBox(this);
        feederCheckBox->setProperty("feederId", row + 1);
        feederCheckBox->setStyleSheet(getCheckBoxStyle());
        connect(feederCheckBox, &QCheckBox::toggled, this, &MainWindow::onFeederBlockToggled);
        feederBlockTable->setCellWidget(row, 1, feederCheckBox);

        // 料仓屏蔽复选框
        for (int col = 2; col < 7; ++col) {
            QCheckBox *binCheckBox = new QCheckBox(this);
            binCheckBox->setProperty("feederId", row + 1);
            binCheckBox->setProperty("binId", col - 1);
            binCheckBox->setStyleSheet(getCheckBoxStyle());
            connect(binCheckBox, &QCheckBox::toggled, this, &MainWindow::onFeederBinBlockToggled);
            feederBlockTable->setCellWidget(row, col, binCheckBox);
        }
    }

    feederLayout->addWidget(feederBlockTable);
    contentLayout->addWidget(feederGroup);

    // 右侧：上料区域屏蔽管理
    QGroupBox *areaGroup = new QGroupBox("上料区域屏蔽管理", this);
    QVBoxLayout *areaLayout = new QVBoxLayout(areaGroup);

    // 区域屏蔽表格
    QTableWidget *areaBlockTable = new QTableWidget(22, 6, this); // 22行6列（区域+整区屏蔽+4个桶）
    QStringList areaHeaders;
    areaHeaders << "区域" << "整区屏蔽" << "桶1" << "桶2" << "桶3" << "桶4";
    areaBlockTable->setHorizontalHeaderLabels(areaHeaders);
    areaBlockTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    areaBlockTable->verticalHeader()->setVisible(false);

    // 初始化区域屏蔽表格
    for (int row = 0; row < 22; ++row) {
        char areaId = 'A' + row;

        // 区域名称
        areaBlockTable->setItem(row, 0, new QTableWidgetItem(QString("区域%1").arg(areaId)));

        // 整区屏蔽复选框
        QCheckBox *areaCheckBox = new QCheckBox(this);
        areaCheckBox->setProperty("areaId", areaId);
        areaCheckBox->setStyleSheet(getCheckBoxStyle());
        connect(areaCheckBox, &QCheckBox::toggled, this, &MainWindow::onAreaBlockToggled);
        areaBlockTable->setCellWidget(row, 1, areaCheckBox);

        // 桶屏蔽复选框
        for (int col = 2; col < 6; ++col) {
            QCheckBox *bucketCheckBox = new QCheckBox(this);
            bucketCheckBox->setProperty("areaId", areaId);
            bucketCheckBox->setProperty("bucketId", col - 1);
            bucketCheckBox->setStyleSheet(getCheckBoxStyle());
            connect(bucketCheckBox, &QCheckBox::toggled, this, &MainWindow::onAreaBucketBlockToggled);
            areaBlockTable->setCellWidget(row, col, bucketCheckBox);
        }
    }

    areaLayout->addWidget(areaBlockTable);
    contentLayout->addWidget(areaGroup);

    mainLayout->addLayout(contentLayout);

    // 底部操作按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout();
    QPushButton *clearAllButton = new QPushButton("清除所有屏蔽", this);
    QPushButton *saveConfigButton = new QPushButton("保存配置", this);
    QPushButton *refreshButton = new QPushButton("刷新状态", this);

    clearAllButton->setStyleSheet(getButtonStyle("orange"));
    saveConfigButton->setStyleSheet(getButtonStyle("blue"));
    refreshButton->setStyleSheet(getButtonStyle("green"));

    connect(clearAllButton, &QPushButton::clicked, [this]() {
        // 清除所有屏蔽状态
        feederBlockStatus.clear();
        binBlockStatus.clear();
        areaBlockStatus.clear();
        bucketBlockStatus.clear();
        updateBlockStatusDisplay();
        saveBlockConfigToFile(); // 保存到配置文件

        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [BLOCK] 已清除所有屏蔽状态并保存到配置文件").arg(timestamp));
            logList->scrollToBottom();
        }
    });

    connect(saveConfigButton, &QPushButton::clicked, [this]() {
        saveBlockConfigToFile();
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [CONFIG] 手动保存屏蔽配置").arg(timestamp));
            logList->scrollToBottom();
        }
    });

    connect(refreshButton, &QPushButton::clicked, this, &MainWindow::updateBlockStatusDisplay);

    buttonLayout->addWidget(clearAllButton);
    buttonLayout->addWidget(saveConfigButton);
    buttonLayout->addWidget(refreshButton);
    buttonLayout->addStretch();

    mainLayout->addLayout(buttonLayout);

    // 添加到Tab控件
    tabWidget->addTab(blockManagementTab, "屏蔽管理");
}

// 屏蔽管理槽函数实现
void MainWindow::onFeederBlockToggled() {
    QCheckBox *checkBox = qobject_cast<QCheckBox*>(sender());
    if (!checkBox) return;

    int feederId = checkBox->property("feederId").toInt();
    bool blocked = checkBox->isChecked();

    setFeederBlocked(feederId, blocked);

    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        QString action = blocked ? "屏蔽" : "解除屏蔽";
        logList->addItem(QString("[%1] [BLOCK] %2投料机%3").arg(timestamp, action).arg(feederId));
        logList->scrollToBottom();
    }
}

void MainWindow::onFeederBinBlockToggled() {
    QCheckBox *checkBox = qobject_cast<QCheckBox*>(sender());
    if (!checkBox) return;

    int feederId = checkBox->property("feederId").toInt();
    int binId = checkBox->property("binId").toInt();
    bool blocked = checkBox->isChecked();

    setBinBlocked(feederId, binId, blocked);

    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        QString action = blocked ? "屏蔽" : "解除屏蔽";
        logList->addItem(QString("[%1] [BLOCK] %2投料机%3料仓%4").arg(timestamp, action).arg(feederId).arg(binId));
        logList->scrollToBottom();
    }
}

void MainWindow::onAreaBlockToggled() {
    QCheckBox *checkBox = qobject_cast<QCheckBox*>(sender());
    if (!checkBox) return;

    char areaId = checkBox->property("areaId").toChar().toLatin1();
    bool blocked = checkBox->isChecked();

    setAreaBlocked(areaId, blocked);

    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        QString action = blocked ? "屏蔽" : "解除屏蔽";
        logList->addItem(QString("[%1] [BLOCK] %2区域%3").arg(timestamp, action).arg(areaId));
        logList->scrollToBottom();
    }
}

void MainWindow::onAreaBucketBlockToggled() {
    QCheckBox *checkBox = qobject_cast<QCheckBox*>(sender());
    if (!checkBox) return;

    char areaId = checkBox->property("areaId").toChar().toLatin1();
    int bucketId = checkBox->property("bucketId").toInt();
    bool blocked = checkBox->isChecked();

    setBucketBlocked(areaId, bucketId, blocked);

    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        QString action = blocked ? "屏蔽" : "解除屏蔽";
        logList->addItem(QString("[%1] [BLOCK] %2区域%3桶%4").arg(timestamp, action).arg(areaId).arg(bucketId));
        logList->scrollToBottom();
    }
}

void MainWindow::onBlockManagementClicked() {
    // 切换到屏蔽管理Tab页面
    if (tabWidget) {
        for (int i = 0; i < tabWidget->count(); ++i) {
            if (tabWidget->tabText(i) == "屏蔽管理") {
                tabWidget->setCurrentIndex(i);
                break;
            }
        }
    }
}

// 屏蔽状态管理方法实现
void MainWindow::updateBlockStatusDisplay() {
    // 更新投料机表格的屏蔽状态显示
    if (feederTable) {
        for (int row = 0; row < feederTable->rowCount(); ++row) {
            int feederId = row + 1;

            // 检查投料机是否被屏蔽
            bool feederBlocked = isFeederBlocked(feederId);
            QColor rowColor;

            if (feederBlocked) {
                rowColor = QColor(255, 200, 200); // 红色表示屏蔽
            } else {
                // 检查是否有料仓被屏蔽
                bool hasBinBlocked = false;
                for (int binId = 1; binId <= 5; ++binId) {
                    if (isBinBlocked(feederId, binId)) {
                        hasBinBlocked = true;
                        break;
                    }
                }
                rowColor = hasBinBlocked ? QColor(255, 255, 200) : QColor(200, 255, 200); // 黄色表示部分屏蔽，绿色表示正常
            }

            // 设置行颜色
            for (int col = 0; col < feederTable->columnCount(); ++col) {
                if (feederTable->item(row, col)) {
                    feederTable->item(row, col)->setBackgroundColor(rowColor);
                }
            }

            // 更新状态文本 - 只有在没有实际数据时才使用默认状态
            // 检查是否有来自controlSystem的实际状态数据
            bool hasRealData = false;
            QString statusText = "离线"; // 默认为离线状态

            if (controlSystem) {
                auto feederStatusList = controlSystem->getFeederStatus();
                for (const auto& status : feederStatusList) {
                    if (status && status->feederId == feederId) {
                        hasRealData = true;
                        if (feederBlocked) {
                            statusText = "已屏蔽";
                        } else if (!status->isConnected) {
                            statusText = "离线";
                        } else {
                            statusText = "在线";
                            if (!status->heartbeatOK) statusText += " (心跳异常)";

                            // 检查料仓屏蔽状态
                            QStringList blockedBins;
                            for (int binId = 1; binId <= 5; ++binId) {
                                if (isBinBlocked(feederId, binId)) {
                                    blockedBins << QString("料仓%1").arg(binId);
                                }
                            }
                            if (!blockedBins.isEmpty()) {
                                statusText += " (部分屏蔽:" + blockedBins.join(",") + ")";
                            }
                        }
                        break;
                    }
                }
            }

            // 如果没有实际数据，只显示屏蔽状态
            if (!hasRealData) {
                if (feederBlocked) {
                    statusText = "已屏蔽";
                } else {
                    statusText = "离线"; // 没有连接数据时默认为离线
                }
            }

            if (feederTable->item(row, 1)) {
                feederTable->item(row, 1)->setText(statusText);
            }
        }
    }

    // 更新区域表格的屏蔽状态显示
    if (areaTable) {
        for (int row = 0; row < areaTable->rowCount(); ++row) {
            char areaId = 'A' + row;

            // 检查区域是否被屏蔽
            bool areaBlocked = isAreaBlocked(areaId);
            QColor rowColor;

            if (areaBlocked) {
                rowColor = QColor(255, 200, 200); // 红色表示屏蔽
            } else {
                // 检查是否有桶被屏蔽
                bool hasBucketBlocked = false;
                for (int bucketId = 1; bucketId <= 4; ++bucketId) {
                    if (isBucketBlocked(areaId, bucketId)) {
                        hasBucketBlocked = true;
                        break;
                    }
                }
                rowColor = hasBucketBlocked ? QColor(255, 255, 200) : QColor(200, 255, 200); // 黄色表示部分屏蔽，绿色表示正常
            }

            // 设置行颜色
            for (int col = 0; col < areaTable->columnCount(); ++col) {
                if (areaTable->item(row, col)) {
                    areaTable->item(row, col)->setBackgroundColor(rowColor);
                }
            }

            // 更新状态文本 - 基于实际数据
            QString statusText = "空闲";

            if (areaBlocked) {
                statusText = "已屏蔽";
            } else {
                // 检查是否有实际的区域数据
                bool hasRealData = false;
                if (controlSystem) {
                    auto areaStatusMap = controlSystem->getAreaStatus();
                    auto it = areaStatusMap.find(areaId);
                    if (it != areaStatusMap.end()) {
                        hasRealData = true;
                        auto data = it->second;
                        bool isOccupied = !data->sandType.empty();
                        statusText = isOccupied ? "占用" : "空闲";
                    }
                }

                // 检查桶屏蔽状态
                QStringList blockedBuckets;
                for (int bucketId = 1; bucketId <= 4; ++bucketId) {
                    if (isBucketBlocked(areaId, bucketId)) {
                        blockedBuckets << QString("桶%1").arg(bucketId);
                    }
                }
                if (!blockedBuckets.isEmpty()) {
                    statusText += " (部分屏蔽:" + blockedBuckets.join(",") + ")";
                }
            }

            if (areaTable->item(row, 1)) {
                areaTable->item(row, 1)->setText(statusText);
            }
        }
    }
}

void MainWindow::setFeederBlocked(int feederId, bool blocked) {
    feederBlockStatus[feederId] = blocked;

    // 如果屏蔽整个投料机，同时屏蔽所有料仓
    if (blocked) {
        for (int binId = 1; binId <= 5; ++binId) {
            binBlockStatus[{feederId, binId}] = true;
        }
    }

    updateBlockStatusDisplay();
    saveBlockConfigToFile(); // 自动保存到配置文件
}

void MainWindow::setBinBlocked(int feederId, int binId, bool blocked) {
    binBlockStatus[{feederId, binId}] = blocked;
    updateBlockStatusDisplay();
    saveBlockConfigToFile(); // 自动保存到配置文件
}

void MainWindow::setAreaBlocked(char areaId, bool blocked) {
    areaBlockStatus[areaId] = blocked;

    // 如果屏蔽整个区域，同时屏蔽所有桶
    if (blocked) {
        for (int bucketId = 1; bucketId <= 4; ++bucketId) {
            bucketBlockStatus[{areaId, bucketId}] = true;
        }
    }

    updateBlockStatusDisplay();
    saveBlockConfigToFile(); // 自动保存到配置文件
}

void MainWindow::setBucketBlocked(char areaId, int bucketId, bool blocked) {
    bucketBlockStatus[{areaId, bucketId}] = blocked;
    updateBlockStatusDisplay();
    saveBlockConfigToFile(); // 自动保存到配置文件
}

bool MainWindow::isFeederBlocked(int feederId) const {
    auto it = feederBlockStatus.find(feederId);
    return it != feederBlockStatus.end() && it->second;
}

bool MainWindow::isBinBlocked(int feederId, int binId) const {
    // 如果整个投料机被屏蔽，则料仓也被屏蔽
    if (isFeederBlocked(feederId)) {
        return true;
    }

    auto it = binBlockStatus.find({feederId, binId});
    return it != binBlockStatus.end() && it->second;
}

bool MainWindow::isAreaBlocked(char areaId) const {
    auto it = areaBlockStatus.find(areaId);
    return it != areaBlockStatus.end() && it->second;
}

bool MainWindow::isBucketBlocked(char areaId, int bucketId) const {
    // 如果整个区域被屏蔽，则桶也被屏蔽
    if (isAreaBlocked(areaId)) {
        return true;
    }

    auto it = bucketBlockStatus.find({areaId, bucketId});
    return it != bucketBlockStatus.end() && it->second;
}

// 屏蔽状态持久化方法实现
void MainWindow::setConfigManager(std::shared_ptr<ConfigManager> configMgr) {
    configManager = configMgr;

    // 设置配置变更回调，实现热更新
    if (configManager) {
        // 使用弱引用避免循环引用
        std::weak_ptr<ConfigManager> weakConfigManager = configManager;
        configManager->setConfigChangeCallback([this, weakConfigManager](const std::string& section, const std::string& key) {
            // 检查ConfigManager是否仍然有效
            if (auto strongConfigManager = weakConfigManager.lock()) {
                QMetaObject::invokeMethod(this, [this, section, key]() {
                    onConfigurationChanged(section, key);
                }, Qt::QueuedConnection);
            }
        });
    }

    // 设置配置管理器后立即加载屏蔽配置
    loadBlockConfigFromFile();
}

void MainWindow::loadBlockConfigFromFile() {
    if (!configManager) {
        // 如果没有配置管理器，尝试创建一个临时的
        configManager = std::make_shared<ConfigManager>();
        if (!configManager->loadConfig()) {
            // 如果加载失败，使用默认配置
            return;
        }
    }

    try {
        const SystemConfig& config = configManager->getSystemConfig();
        const BlockConfig& blockConfig = config.blockConfig;

        // 加载投料机屏蔽状态
        feederBlockStatus = blockConfig.feederBlocked;

        // 加载料仓屏蔽状态
        binBlockStatus = blockConfig.binBlocked;

        // 加载区域屏蔽状态
        areaBlockStatus = blockConfig.areaBlocked;

        // 加载桶屏蔽状态
        bucketBlockStatus = blockConfig.bucketBlocked;

        // 更新UI显示
        updateBlockStatusDisplay();

        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            int totalBlocked = feederBlockStatus.size() + binBlockStatus.size() +
                              areaBlockStatus.size() + bucketBlockStatus.size();
            logList->addItem(QString("[%1] [CONFIG] 已加载屏蔽配置，共%2项屏蔽设置").arg(timestamp).arg(totalBlocked));
            logList->scrollToBottom();
        }

    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 加载屏蔽配置失败: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::saveBlockConfigToFile() {
    if (!configManager) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 无法保存屏蔽配置：配置管理器未初始化").arg(timestamp));
            logList->scrollToBottom();
        }
        return;
    }

    try {
        // 获取当前系统配置
        SystemConfig config = configManager->getSystemConfig();

        // 更新屏蔽配置
        config.blockConfig.feederBlocked = feederBlockStatus;
        config.blockConfig.binBlocked = binBlockStatus;
        config.blockConfig.areaBlocked = areaBlockStatus;
        config.blockConfig.bucketBlocked = bucketBlockStatus;

        // 设置更新后的配置
        configManager->setSystemConfig(config);

        // 通知控制系统配置已更新
        if (controlSystem) {
            controlSystem->setConfig(configManager->getSystemConfig());
        }
        // 保存到文件
        if (configManager->saveConfig()) {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                int totalBlocked = feederBlockStatus.size() + binBlockStatus.size() +
                                  areaBlockStatus.size() + bucketBlockStatus.size();
                logList->addItem(QString("[%1] [CONFIG] 已保存屏蔽配置，共%2项屏蔽设置").arg(timestamp).arg(totalBlocked));
                logList->scrollToBottom();
            }
        } else {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] 保存屏蔽配置到文件失败").arg(timestamp));
                logList->scrollToBottom();
            }
        }

    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 保存屏蔽配置失败: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

// 创建系统配置Tab页面
void MainWindow::createSystemConfigTab() {
    systemConfigTab = new QWidget(this);
    QVBoxLayout *mainLayout = new QVBoxLayout(systemConfigTab);

    // 创建滚动区域
    QScrollArea *scrollArea = new QScrollArea(this);
    QWidget *scrollWidget = new QWidget();
    QVBoxLayout *scrollLayout = new QVBoxLayout(scrollWidget);

    // 标题和说明
    QLabel *titleLabel = new QLabel("系统配置管理", this);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px 0;");
    scrollLayout->addWidget(titleLabel);

    QLabel *infoLabel = new QLabel("配置系统运行参数、投料机设置、ABB机器人参数和MES服务器设置", this);
    infoLabel->setStyleSheet("color: #7f8c8d; margin-bottom: 20px;");
    scrollLayout->addWidget(infoLabel);

    // 基本系统配置组
    QGroupBox *basicConfigGroup = new QGroupBox("基本系统配置", this);
    QFormLayout *basicLayout = new QFormLayout(basicConfigGroup);

    // 直连模式开关
    QCheckBox *directModeCheckBox = new QCheckBox("启用直连模式", this);
    directModeCheckBox->setObjectName("directModeCheckBox");
    directModeCheckBox->setStyleSheet(getCheckBoxStyle());
    connect(directModeCheckBox, &QCheckBox::toggled, this, &MainWindow::onDirectModeConfigToggled);
    basicLayout->addRow("运行模式:", directModeCheckBox);

    scrollLayout->addWidget(basicConfigGroup);

    // 投料机配置组
    QGroupBox *feederConfigGroup = new QGroupBox("投料机配置", this);
    QVBoxLayout *feederLayout = new QVBoxLayout(feederConfigGroup);

    // 投料机配置表格
    QTableWidget *feederConfigTable = new QTableWidget(5, 5, this);
    feederConfigTable->setObjectName("feederConfigTable");
    QStringList feederHeaders = {"投料机ID", "IP地址", "端口", "超时(ms)", "启用心跳"};
    feederConfigTable->setHorizontalHeaderLabels(feederHeaders);
    feederConfigTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 设置表格自适应高度
    feederConfigTable->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Minimum);
    feederConfigTable->setMinimumHeight(150);
    feederConfigTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);

    // 填充投料机配置数据
    for (int i = 0; i < 5; ++i) {
        feederConfigTable->setItem(i, 0, new QTableWidgetItem(QString::number(i + 1)));
        feederConfigTable->setItem(i, 1, new QTableWidgetItem(QString("192.168.1.%1").arg(101 + i)));
        feederConfigTable->setItem(i, 2, new QTableWidgetItem("502"));
        feederConfigTable->setItem(i, 3, new QTableWidgetItem("5000"));

        QCheckBox *heartbeatCheckBox = new QCheckBox();
        heartbeatCheckBox->setChecked(true);
        heartbeatCheckBox->setStyleSheet(getCheckBoxStyle());
        feederConfigTable->setCellWidget(i, 4, heartbeatCheckBox);

        // 设置前两列为只读
        feederConfigTable->item(i, 0)->setFlags(feederConfigTable->item(i, 0)->flags() & ~Qt::ItemIsEditable);
    }

    connect(feederConfigTable, &QTableWidget::itemChanged, this, &MainWindow::onFeederConfigChanged);

    // 调整表格高度以显示所有内容
    adjustTableHeight(feederConfigTable, 300);

    feederLayout->addWidget(feederConfigTable);

    // 投料机重量上下限配置表格
    QLabel *weightLimitLabel = new QLabel("投料机重量上下限配置 (kg)", this);
    weightLimitLabel->setStyleSheet("font-weight: bold; margin-top: 10px;");
    feederLayout->addWidget(weightLimitLabel);

    QTableWidget *weightLimitTable = new QTableWidget(5, 11, this);  // 5行投料机，11列（ID + 5个料仓的上下限）
    weightLimitTable->setObjectName("weightLimitTable");
    QStringList weightHeaders = {"投料机ID", "料仓1下限", "料仓1上限", "料仓2下限", "料仓2上限",
                                "料仓3下限", "料仓3上限", "料仓4下限", "料仓4上限", "料仓5下限", "料仓5上限"};
    weightLimitTable->setHorizontalHeaderLabels(weightHeaders);
    weightLimitTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 设置表格自适应高度
    weightLimitTable->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Minimum);
    weightLimitTable->setMinimumHeight(150);
    weightLimitTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);

    // 填充重量上下限配置数据
    for (int i = 0; i < 5; ++i) {
        // 投料机ID列（只读）
        weightLimitTable->setItem(i, 0, new QTableWidgetItem(QString::number(i + 1)));
        weightLimitTable->item(i, 0)->setFlags(weightLimitTable->item(i, 0)->flags() & ~Qt::ItemIsEditable);
        weightLimitTable->item(i, 0)->setBackground(QColor(240, 240, 240));

        // 为每个料仓设置默认的重量上下限
        for (int j = 0; j < 5; ++j) {
            // 下限列
            QTableWidgetItem *minItem = new QTableWidgetItem("100.0");
            minItem->setTextAlignment(Qt::AlignCenter);
            weightLimitTable->setItem(i, j * 2 + 1, minItem);

            // 上限列
            QTableWidgetItem *maxItem = new QTableWidgetItem("1000.0");
            maxItem->setTextAlignment(Qt::AlignCenter);
            weightLimitTable->setItem(i, j * 2 + 2, maxItem);
        }
    }

    connect(weightLimitTable, &QTableWidget::itemChanged, this, &MainWindow::onWeightLimitChanged);

    // 调整表格高度以显示所有内容
    adjustTableHeight(weightLimitTable, 300);

    feederLayout->addWidget(weightLimitTable);

    scrollLayout->addWidget(feederConfigGroup);

    // ABB服务器配置组
    QGroupBox *abbConfigGroup = new QGroupBox("ABB服务器配置", this);
    QFormLayout *abbLayout = new QFormLayout(abbConfigGroup);

    // 说明标签
    QLabel *abbInfoLabel = new QLabel("ABB机器人将作为客户端连接到此服务器", this);
    abbInfoLabel->setStyleSheet("color: #7f8c8d; font-style: italic; margin-bottom: 10px;");
    abbLayout->addRow(abbInfoLabel);

    QLineEdit *abbListenIpEdit = new QLineEdit("0.0.0.0", this);
    abbListenIpEdit->setObjectName("abbListenIpEdit");
    connect(abbListenIpEdit, &QLineEdit::textChanged, this, &MainWindow::onABBConfigChanged);
    abbLayout->addRow("监听IP:", abbListenIpEdit);

    QSpinBox *abbListenPortSpinBox = new QSpinBox(this);
    abbListenPortSpinBox->setObjectName("abbListenPortSpinBox");
    abbListenPortSpinBox->setRange(1, 65535);
    abbListenPortSpinBox->setValue(7000);
    connect(abbListenPortSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onABBConfigChanged);
    abbListenPortSpinBox->setToolTip("ABB机器人连接到此端口");
    abbLayout->addRow("监听端口:", abbListenPortSpinBox);

    QSpinBox *abbTimeoutSpinBox = new QSpinBox(this);
    abbTimeoutSpinBox->setObjectName("abbTimeoutSpinBox");
    abbTimeoutSpinBox->setRange(1000, 60000);
    abbTimeoutSpinBox->setValue(10000);
    abbTimeoutSpinBox->setSuffix(" ms");
    connect(abbTimeoutSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onABBConfigChanged);
    abbLayout->addRow("连接超时:", abbTimeoutSpinBox);

    QSpinBox *abbMaxConnectionsSpinBox = new QSpinBox(this);
    abbMaxConnectionsSpinBox->setObjectName("abbMaxConnectionsSpinBox");
    abbMaxConnectionsSpinBox->setRange(1, 10);
    abbMaxConnectionsSpinBox->setValue(1);
    connect(abbMaxConnectionsSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onABBConfigChanged);
    abbMaxConnectionsSpinBox->setToolTip("允许的最大ABB机器人连接数");
    abbLayout->addRow("最大连接数:", abbMaxConnectionsSpinBox);

    QCheckBox *abbHeartbeatCheckBox = new QCheckBox("启用心跳检测", this);
    abbHeartbeatCheckBox->setObjectName("abbHeartbeatCheckBox");
    abbHeartbeatCheckBox->setChecked(true);
    abbHeartbeatCheckBox->setStyleSheet(getCheckBoxStyle());
    connect(abbHeartbeatCheckBox, &QCheckBox::toggled, this, &MainWindow::onABBConfigChanged);
    abbLayout->addRow("心跳检测:", abbHeartbeatCheckBox);

    scrollLayout->addWidget(abbConfigGroup);

    // MES服务器配置组
    QGroupBox *mesConfigGroup = new QGroupBox("MES服务器配置", this);
    QFormLayout *mesLayout = new QFormLayout(mesConfigGroup);

    QLineEdit *mesIpEdit = new QLineEdit("0.0.0.0", this);
    mesIpEdit->setObjectName("mesIpEdit");
    connect(mesIpEdit, &QLineEdit::textChanged, this, &MainWindow::onMESConfigChanged);
    mesLayout->addRow("监听IP:", mesIpEdit);

    QSpinBox *mesPortSpinBox = new QSpinBox(this);
    mesPortSpinBox->setObjectName("mesPortSpinBox");
    mesPortSpinBox->setRange(1, 65535);
    mesPortSpinBox->setValue(502);
    connect(mesPortSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onMESConfigChanged);
    mesLayout->addRow("监听端口:", mesPortSpinBox);

    QCheckBox *mesAutoSaveCheckBox = new QCheckBox("自动保存数据", this);
    mesAutoSaveCheckBox->setObjectName("mesAutoSaveCheckBox");
    mesAutoSaveCheckBox->setChecked(true);
    mesAutoSaveCheckBox->setStyleSheet(getCheckBoxStyle());
    connect(mesAutoSaveCheckBox, &QCheckBox::toggled, this, &MainWindow::onMESConfigChanged);
    mesLayout->addRow("数据持久化:", mesAutoSaveCheckBox);

    QSpinBox *mesHistorySpinBox = new QSpinBox(this);
    mesHistorySpinBox->setObjectName("mesHistorySpinBox");
    mesHistorySpinBox->setRange(100, 10000);
    mesHistorySpinBox->setValue(1000);
    connect(mesHistorySpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onMESConfigChanged);
    mesLayout->addRow("历史记录数:", mesHistorySpinBox);

    scrollLayout->addWidget(mesConfigGroup);

    // 配置操作按钮组
    QGroupBox *operationGroup = new QGroupBox("配置操作", this);
    QHBoxLayout *operationLayout = new QHBoxLayout(operationGroup);

    QPushButton *saveConfigButton = new QPushButton("保存配置", this);
    saveConfigButton->setStyleSheet("QPushButton { background-color: #27ae60; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #2ecc71; }");
    connect(saveConfigButton, &QPushButton::clicked, this, &MainWindow::onSaveSystemConfigClicked);
    operationLayout->addWidget(saveConfigButton);

    QPushButton *loadConfigButton = new QPushButton("重新加载", this);
    loadConfigButton->setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #5dade2; }");
    connect(loadConfigButton, &QPushButton::clicked, this, &MainWindow::onLoadSystemConfigClicked);
    operationLayout->addWidget(loadConfigButton);

    QPushButton *resetConfigButton = new QPushButton("恢复默认", this);
    resetConfigButton->setStyleSheet("QPushButton { background-color: #f39c12; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #f4d03f; }");
    connect(resetConfigButton, &QPushButton::clicked, this, &MainWindow::onResetSystemConfigClicked);
    operationLayout->addWidget(resetConfigButton);

    operationLayout->addStretch();

    QPushButton *exportConfigButton = new QPushButton("导出配置", this);
    exportConfigButton->setStyleSheet("QPushButton { background-color: #8e44ad; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #a569bd; }");
    connect(exportConfigButton, &QPushButton::clicked, this, &MainWindow::onExportSystemConfigClicked);
    operationLayout->addWidget(exportConfigButton);

    QPushButton *importConfigButton = new QPushButton("导入配置", this);
    importConfigButton->setStyleSheet("QPushButton { background-color: #e67e22; color: white; padding: 8px 16px; border: none; border-radius: 4px; } QPushButton:hover { background-color: #f39c12; }");
    connect(importConfigButton, &QPushButton::clicked, this, &MainWindow::onImportSystemConfigClicked);
    operationLayout->addWidget(importConfigButton);

    scrollLayout->addWidget(operationGroup);

    // 不添加stretch，让表格能够充分利用空间

    // 设置滚动区域
    scrollArea->setWidget(scrollWidget);
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 设置滚动区域的大小策略，让它能够扩展
    scrollArea->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    mainLayout->addWidget(scrollArea);

    // 添加到Tab控件
    tabWidget->addTab(systemConfigTab, "系统配置");

    // 初始化显示
    updateSystemConfigDisplay();
}

// 系统配置槽函数实现
void MainWindow::onSaveSystemConfigClicked() {
    if (!configManager) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 配置管理器未初始化").arg(timestamp));
            logList->scrollToBottom();
        }
        return;
    }

    try {
        // 从UI收集配置数据
        SystemConfig config = configManager->getSystemConfig();

        // 更新基本配置
        QCheckBox *directModeCheckBox = systemConfigTab->findChild<QCheckBox*>("directModeCheckBox");
        if (directModeCheckBox) {
            config.enableDirectMode = directModeCheckBox->isChecked();
        }

        // 更新投料机配置
        QTableWidget *feederTable = systemConfigTab->findChild<QTableWidget*>("feederConfigTable");
        if (feederTable) {
            for (int row = 0; row < feederTable->rowCount() && row < config.feeders.size(); ++row) {
                FeederConfig& feederConfig = config.feeders[row];

                QTableWidgetItem *ipItem = feederTable->item(row, 1);
                if (ipItem) feederConfig.ip = ipItem->text().toStdString();

                QTableWidgetItem *portItem = feederTable->item(row, 2);
                if (portItem) feederConfig.port = portItem->text().toInt();

                QTableWidgetItem *timeoutItem = feederTable->item(row, 3);
                if (timeoutItem) feederConfig.timeout = timeoutItem->text().toInt();

                QCheckBox *heartbeatCheckBox = qobject_cast<QCheckBox*>(feederTable->cellWidget(row, 4));
                if (heartbeatCheckBox) feederConfig.enableHeartbeat = heartbeatCheckBox->isChecked();
            }
        }

        // 更新料仓重量限制配置
        QTableWidget *weightLimitTable = systemConfigTab->findChild<QTableWidget*>("weightLimitTable");
        if (weightLimitTable) {
            for (int row = 0; row < weightLimitTable->rowCount() && row < config.feeders.size(); ++row) {
                FeederConfig& feederConfig = config.feeders[row];

                // 确保料仓重量数组有正确的大小
                feederConfig.binMinWeights.resize(5, 100.0f);
                feederConfig.binMaxWeights.resize(5, 1000.0f);

                for (int col = 1; col < weightLimitTable->columnCount() && col <= 5; ++col) {
                    QTableWidgetItem *minItem = weightLimitTable->item(row * 2, col);
                    QTableWidgetItem *maxItem = weightLimitTable->item(row * 2 + 1, col);

                    if (minItem) {
                        feederConfig.binMinWeights[col - 1] = minItem->text().toFloat();
                    }
                    if (maxItem) {
                        feederConfig.binMaxWeights[col - 1] = maxItem->text().toFloat();
                    }
                }
            }
        }

        // 更新ABB服务器配置
        QLineEdit *abbListenIpEdit = systemConfigTab->findChild<QLineEdit*>("abbListenIpEdit");
        if (abbListenIpEdit) {
            config.abbRobot.listenIp = abbListenIpEdit->text().toStdString();
        }

        QSpinBox *abbListenPortSpinBox = systemConfigTab->findChild<QSpinBox*>("abbListenPortSpinBox");
        if (abbListenPortSpinBox) {
            config.abbRobot.listenPort = abbListenPortSpinBox->value();
        }

        QSpinBox *abbTimeoutSpinBox = systemConfigTab->findChild<QSpinBox*>("abbTimeoutSpinBox");
        if (abbTimeoutSpinBox) {
            config.abbRobot.timeout = abbTimeoutSpinBox->value();
        }

        QSpinBox *abbMaxConnectionsSpinBox = systemConfigTab->findChild<QSpinBox*>("abbMaxConnectionsSpinBox");
        if (abbMaxConnectionsSpinBox) {
            config.abbRobot.maxConnections = abbMaxConnectionsSpinBox->value();
        }

        QCheckBox *abbHeartbeatCheckBox = systemConfigTab->findChild<QCheckBox*>("abbHeartbeatCheckBox");
        if (abbHeartbeatCheckBox) {
            config.abbRobot.enableHeartbeat = abbHeartbeatCheckBox->isChecked();
        }

        // 验证配置
        std::string errorMessage;
        if (!configManager->validateConfig(config, errorMessage)) {
            QMessageBox::warning(this, "配置验证失败", QString("配置验证失败: %1").arg(QString::fromStdString(errorMessage)));
            return;
        }

        // 设置配置
        configManager->setSystemConfig(config);

        // 保存配置到文件
        if (configManager->saveConfig()) {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] 系统配置保存成功").arg(timestamp));
                logList->scrollToBottom();
            }

            // 通知控制系统配置已更新
            if (controlSystem) {
                controlSystem->setConfig(configManager->getSystemConfig());
            }

            QMessageBox::information(this, "保存成功", "系统配置已保存");
        } else {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] 系统配置保存失败").arg(timestamp));
                logList->scrollToBottom();
            }
            QMessageBox::warning(this, "保存失败", "无法保存系统配置");
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 保存系统配置异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
        QMessageBox::critical(this, "保存异常", QString("保存配置时发生异常: %1").arg(e.what()));
    }
}

void MainWindow::onLoadSystemConfigClicked() {
    if (!configManager) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 配置管理器未初始化").arg(timestamp));
            logList->scrollToBottom();
        }
        return;
    }

    try {
        // 重新加载配置
        if (configManager->reloadConfig()) {
            updateSystemConfigDisplay();
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] 系统配置重新加载成功").arg(timestamp));
                logList->scrollToBottom();
            }
        } else {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] 系统配置重新加载失败").arg(timestamp));
                logList->scrollToBottom();
            }
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 重新加载系统配置异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::onResetSystemConfigClicked() {
    if (!configManager) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 配置管理器未初始化").arg(timestamp));
            logList->scrollToBottom();
        }
        return;
    }

    // 确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(this,
        "确认重置", "确定要恢复默认配置吗？这将覆盖当前所有设置。",
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        try {
            // 创建默认配置
            SystemConfig defaultConfig = configManager->createDefaultConfig();
            configManager->setSystemConfig(defaultConfig);

            // 更新显示
            updateSystemConfigDisplay();

            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] 系统配置已恢复为默认设置").arg(timestamp));
                logList->scrollToBottom();
            }
        } catch (const std::exception& e) {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] 恢复默认配置异常: %2").arg(timestamp, e.what()));
                logList->scrollToBottom();
            }
        }
    }
}

void MainWindow::onExportSystemConfigClicked() {
    if (!configManager) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 配置管理器未初始化").arg(timestamp));
            logList->scrollToBottom();
        }
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
        "导出系统配置", "system_config_export.json", "JSON Files (*.json)");

    if (!fileName.isEmpty()) {
        try {
            if (configManager->exportConfig(fileName.toStdString(), "json")) {
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] [INFO] 系统配置导出成功: %2").arg(timestamp, fileName));
                    logList->scrollToBottom();
                }
            } else {
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] [ERROR] 系统配置导出失败").arg(timestamp));
                    logList->scrollToBottom();
                }
            }
        } catch (const std::exception& e) {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] 导出系统配置异常: %2").arg(timestamp, e.what()));
                logList->scrollToBottom();
            }
        }
    }
}

void MainWindow::onImportSystemConfigClicked() {
    if (!configManager) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 配置管理器未初始化").arg(timestamp));
            logList->scrollToBottom();
        }
        return;
    }

    QString fileName = QFileDialog::getOpenFileName(this,
        "导入系统配置", "", "JSON Files (*.json)");

    if (!fileName.isEmpty()) {
        // 确认对话框
        QMessageBox::StandardButton reply = QMessageBox::question(this,
            "确认导入", "导入配置将覆盖当前设置，确定继续吗？",
            QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

        if (reply == QMessageBox::Yes) {
            try {
                if (configManager->importConfig(fileName.toStdString(), "json")) {
                    updateSystemConfigDisplay();
                    if (logList) {
                        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                        logList->addItem(QString("[%1] [INFO] 系统配置导入成功: %2").arg(timestamp, fileName));
                        logList->scrollToBottom();
                    }
                } else {
                    if (logList) {
                        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                        logList->addItem(QString("[%1] [ERROR] 系统配置导入失败").arg(timestamp));
                        logList->scrollToBottom();
                    }
                }
            } catch (const std::exception& e) {
                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] [ERROR] 导入系统配置异常: %2").arg(timestamp, e.what()));
                    logList->scrollToBottom();
                }
            }
        }
    }
}

void MainWindow::onDirectModeConfigToggled(bool enabled) {
    if (!configManager) return;

    try {
        configManager->setDirectModeEnabled(enabled);

        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            QString mode = enabled ? "直连模式" : "标准模式";
            logList->addItem(QString("[%1] [INFO] 运行模式切换为: %2").arg(timestamp, mode));
            logList->scrollToBottom();
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 切换运行模式异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::onFeederConfigChanged() {
    if (!configManager) return;

    try {
        QTableWidget *table = systemConfigTab->findChild<QTableWidget*>("feederConfigTable");
        if (!table) return;

        // 更新投料机配置
        auto feederConfigs = configManager->getFeederConfigs();
        bool hasValidationError = false;

        for (int row = 0; row < table->rowCount() && row < feederConfigs.size(); ++row) {
            int feederId = row + 1;
            FeederConfig config = configManager->getFeederConfig(feederId);

            // 验证并更新IP地址
            if (table->item(row, 1)) {
                QString ip = table->item(row, 1)->text();
                QString errorMessage;

                if (validateIPAddress(ip, errorMessage)) {
                    config.ip = ip.toStdString();
                    clearValidationError(table->item(row, 1));
                } else {
                    showValidationError(table->item(row, 1), errorMessage);
                    hasValidationError = true;
                    continue; // 跳过此行的其他验证
                }
            }

            // 验证并更新端口
            if (table->item(row, 2)) {
                bool ok;
                int port = table->item(row, 2)->text().toInt(&ok);
                QString errorMessage;

                if (ok && validatePort(port, errorMessage)) {
                    config.port = port;
                    clearValidationError(table->item(row, 2));
                } else {
                    showValidationError(table->item(row, 2), errorMessage);
                    hasValidationError = true;
                    continue;
                }
            }

            // 验证并更新超时
            if (table->item(row, 3)) {
                bool ok;
                int timeout = table->item(row, 3)->text().toInt(&ok);
                QString errorMessage;

                if (ok && validateTimeout(timeout, errorMessage)) {
                    config.timeout = timeout;
                    clearValidationError(table->item(row, 3));
                } else {
                    showValidationError(table->item(row, 3), errorMessage);
                    hasValidationError = true;
                    continue;
                }
            }

            // 更新心跳设置
            QCheckBox *heartbeatCheckBox = qobject_cast<QCheckBox*>(table->cellWidget(row, 4));
            if (heartbeatCheckBox) {
                config.enableHeartbeat = heartbeatCheckBox->isChecked();
            }

            // 只有在没有验证错误时才更新配置
            if (!hasValidationError) {
                configManager->updateFeederConfig(feederId, config);
            }
        }

        if (!hasValidationError) {
            // 自动保存配置
            configManager->saveConfig();

            // 通知控制系统配置已更新
            if (controlSystem) {
                controlSystem->setConfig(configManager->getSystemConfig());
            }

            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] 投料机配置已更新并保存").arg(timestamp));
                logList->scrollToBottom();
            }
        } else {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [WARNING] 投料机配置包含错误，未保存").arg(timestamp));
                logList->scrollToBottom();
            }
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新投料机配置异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::onWeightLimitChanged() {
    if (!configManager) return;

    try {
        QTableWidget *table = systemConfigTab->findChild<QTableWidget*>("weightLimitTable");
        if (!table) return;

        // 更新投料机重量上下限配置
        auto feederConfigs = configManager->getFeederConfigs();

        for (int row = 0; row < table->rowCount() && row < feederConfigs.size(); ++row) {
            int feederId = row + 1;
            FeederConfig config = configManager->getFeederConfig(feederId);

            // 更新每个料仓的重量上下限
            for (int binIndex = 0; binIndex < 5; ++binIndex) {
                // 下限列 (奇数列: 1, 3, 5, 7, 9)
                int minCol = binIndex * 2 + 1;
                if (table->item(row, minCol)) {
                    bool ok;
                    float minWeight = table->item(row, minCol)->text().toFloat(&ok);
                    if (ok && minWeight >= 0) {
                        if (binIndex < config.binMinWeights.size()) {
                            config.binMinWeights[binIndex] = minWeight;
                        }
                    }
                }

                // 上限列 (偶数列: 2, 4, 6, 8, 10)
                int maxCol = binIndex * 2 + 2;
                if (table->item(row, maxCol)) {
                    bool ok;
                    float maxWeight = table->item(row, maxCol)->text().toFloat(&ok);
                    if (ok && maxWeight >= 0) {
                        if (binIndex < config.binMaxWeights.size()) {
                            config.binMaxWeights[binIndex] = maxWeight;
                        }
                    }
                }
            }

            // 验证上下限的合理性
            for (int binIndex = 0; binIndex < 5; ++binIndex) {
                if (binIndex < config.binMinWeights.size() && binIndex < config.binMaxWeights.size()) {
                    if (config.binMinWeights[binIndex] > config.binMaxWeights[binIndex]) {
                        // 如果下限大于上限，交换它们
                        std::swap(config.binMinWeights[binIndex], config.binMaxWeights[binIndex]);

                        // 更新界面显示
                        int minCol = binIndex * 2 + 1;
                        int maxCol = binIndex * 2 + 2;
                        if (table->item(row, minCol)) {
                            table->item(row, minCol)->setText(QString::number(config.binMinWeights[binIndex], 'f', 1));
                        }
                        if (table->item(row, maxCol)) {
                            table->item(row, maxCol)->setText(QString::number(config.binMaxWeights[binIndex], 'f', 1));
                        }
                    }
                }
            }

            configManager->updateFeederConfig(feederId, config);
        }

        // 自动保存配置
        configManager->saveConfig();

        // 通知控制系统配置已更新
        if (controlSystem) {
            controlSystem->setConfig(configManager->getSystemConfig());
        }

        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [INFO] 投料机重量上下限配置已更新并保存").arg(timestamp));
            logList->scrollToBottom();
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新重量上下限配置异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::onABBConfigChanged() {
    if (!configManager) return;

    try {
        ABBConfig abbConfig = configManager->getABBConfig();
        bool hasValidationError = false;

        // 验证并更新监听IP地址
        QLineEdit *listenIpEdit = systemConfigTab->findChild<QLineEdit*>("abbListenIpEdit");
        if (listenIpEdit) {
            QString ip = listenIpEdit->text();
            QString errorMessage;

            if (validateIPAddress(ip, errorMessage)) {
                abbConfig.listenIp = ip.toStdString();
                clearValidationError(listenIpEdit);
            } else {
                showValidationError(listenIpEdit, errorMessage);
                hasValidationError = true;
            }
        }

        // 验证并更新监听端口
        QSpinBox *listenPortSpinBox = systemConfigTab->findChild<QSpinBox*>("abbListenPortSpinBox");
        if (listenPortSpinBox) {
            int port = listenPortSpinBox->value();
            QString errorMessage;

            if (validatePort(port, errorMessage)) {
                abbConfig.listenPort = port;
                clearValidationError(listenPortSpinBox);
            } else {
                showValidationError(listenPortSpinBox, errorMessage);
                hasValidationError = true;
            }
        }

        // 验证并更新超时
        QSpinBox *timeoutSpinBox = systemConfigTab->findChild<QSpinBox*>("abbTimeoutSpinBox");
        if (timeoutSpinBox) {
            int timeout = timeoutSpinBox->value();
            QString errorMessage;

            if (validateTimeout(timeout, errorMessage)) {
                abbConfig.timeout = timeout;
                clearValidationError(timeoutSpinBox);
            } else {
                showValidationError(timeoutSpinBox, errorMessage);
                hasValidationError = true;
            }
        }

        // 更新最大连接数
        QSpinBox *maxConnectionsSpinBox = systemConfigTab->findChild<QSpinBox*>("abbMaxConnectionsSpinBox");
        if (maxConnectionsSpinBox) {
            abbConfig.maxConnections = maxConnectionsSpinBox->value();
        }

        // 更新心跳检测
        QCheckBox *heartbeatCheckBox = systemConfigTab->findChild<QCheckBox*>("abbHeartbeatCheckBox");
        if (heartbeatCheckBox) {
            abbConfig.enableHeartbeat = heartbeatCheckBox->isChecked();
        }

        // 只有在没有验证错误时才更新配置
        if (!hasValidationError) {
            configManager->setABBConfig(abbConfig);

            // 自动保存配置
            configManager->saveConfig();

            // 通知控制系统配置已更新
            if (controlSystem) {
                controlSystem->setConfig(configManager->getSystemConfig());
            }

            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] ABB服务器配置已更新并保存").arg(timestamp));
                logList->scrollToBottom();
            }
        } else {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [WARNING] ABB配置包含错误，未保存").arg(timestamp));
                logList->scrollToBottom();
            }
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新ABB配置异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::onMESConfigChanged() {
    if (!configManager) return;

    try {
        MESDataConfig mesConfig = configManager->getMESConfig();

        // 更新IP地址
        QLineEdit *ipEdit = systemConfigTab->findChild<QLineEdit*>("mesIpEdit");
        if (ipEdit) {
            mesConfig.serverConfig.ip = ipEdit->text().toStdString();
        }

        // 更新端口
        QSpinBox *portSpinBox = systemConfigTab->findChild<QSpinBox*>("mesPortSpinBox");
        if (portSpinBox) {
            mesConfig.serverConfig.port = portSpinBox->value();
        }

        // 更新自动保存设置
        QCheckBox *autoSaveCheckBox = systemConfigTab->findChild<QCheckBox*>("mesAutoSaveCheckBox");
        if (autoSaveCheckBox) {
            mesConfig.serverConfig.autoSave = autoSaveCheckBox->isChecked();
        }

        // 更新历史记录数
        QSpinBox *historySpinBox = systemConfigTab->findChild<QSpinBox*>("mesHistorySpinBox");
        if (historySpinBox) {
            mesConfig.serverConfig.maxSendDataHistory = historySpinBox->value();
        }

        configManager->setMESConfig(mesConfig);

        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [INFO] MES服务器配置已更新").arg(timestamp));
            logList->scrollToBottom();
        }
    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新MES配置异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

void MainWindow::updateSystemConfigDisplay() {
    if (!configManager || !systemConfigTab) return;

    try {
        const SystemConfig& config = configManager->getSystemConfig();

        // 更新直连模式开关
        QCheckBox *directModeCheckBox = systemConfigTab->findChild<QCheckBox*>("directModeCheckBox");
        if (directModeCheckBox) {
            directModeCheckBox->setChecked(config.enableDirectMode);
        }

        // 更新投料机配置表格
        QTableWidget *feederTable = systemConfigTab->findChild<QTableWidget*>("feederConfigTable");
        if (feederTable) {
            for (int i = 0; i < feederTable->rowCount() && i < config.feeders.size(); ++i) {
                const FeederConfig& feederConfig = config.feeders[i];

                if (feederTable->item(i, 1)) {
                    feederTable->item(i, 1)->setText(QString::fromStdString(feederConfig.ip));
                }
                if (feederTable->item(i, 2)) {
                    feederTable->item(i, 2)->setText(QString::number(feederConfig.port));
                }
                if (feederTable->item(i, 3)) {
                    feederTable->item(i, 3)->setText(QString::number(feederConfig.timeout));
                }

                QCheckBox *heartbeatCheckBox = qobject_cast<QCheckBox*>(feederTable->cellWidget(i, 4));
                if (heartbeatCheckBox) {
                    heartbeatCheckBox->setChecked(feederConfig.enableHeartbeat);
                }
            }
        }

        // 更新ABB服务器配置
        QLineEdit *abbListenIpEdit = systemConfigTab->findChild<QLineEdit*>("abbListenIpEdit");
        if (abbListenIpEdit) {
            abbListenIpEdit->setText(QString::fromStdString(config.abbRobot.listenIp));
        }

        QSpinBox *abbListenPortSpinBox = systemConfigTab->findChild<QSpinBox*>("abbListenPortSpinBox");
        if (abbListenPortSpinBox) {
            abbListenPortSpinBox->setValue(config.abbRobot.listenPort);
        }

        QSpinBox *abbTimeoutSpinBox = systemConfigTab->findChild<QSpinBox*>("abbTimeoutSpinBox");
        if (abbTimeoutSpinBox) {
            abbTimeoutSpinBox->setValue(config.abbRobot.timeout);
        }

        QSpinBox *abbMaxConnectionsSpinBox = systemConfigTab->findChild<QSpinBox*>("abbMaxConnectionsSpinBox");
        if (abbMaxConnectionsSpinBox) {
            abbMaxConnectionsSpinBox->setValue(config.abbRobot.maxConnections);
        }

        QCheckBox *abbHeartbeatCheckBox = systemConfigTab->findChild<QCheckBox*>("abbHeartbeatCheckBox");
        if (abbHeartbeatCheckBox) {
            abbHeartbeatCheckBox->setChecked(config.abbRobot.enableHeartbeat);
        }

        // 更新MES配置
        QLineEdit *mesIpEdit = systemConfigTab->findChild<QLineEdit*>("mesIpEdit");
        if (mesIpEdit) {
            mesIpEdit->setText(QString::fromStdString(config.mesConfig.serverConfig.ip));
        }

        QSpinBox *mesPortSpinBox = systemConfigTab->findChild<QSpinBox*>("mesPortSpinBox");
        if (mesPortSpinBox) {
            mesPortSpinBox->setValue(config.mesConfig.serverConfig.port);
        }

        QCheckBox *mesAutoSaveCheckBox = systemConfigTab->findChild<QCheckBox*>("mesAutoSaveCheckBox");
        if (mesAutoSaveCheckBox) {
            mesAutoSaveCheckBox->setChecked(config.mesConfig.serverConfig.autoSave);
        }

        QSpinBox *mesHistorySpinBox = systemConfigTab->findChild<QSpinBox*>("mesHistorySpinBox");
        if (mesHistorySpinBox) {
            mesHistorySpinBox->setValue(config.mesConfig.serverConfig.maxSendDataHistory);
        }

        // 更新投料机重量上下限配置表格
        QTableWidget *weightLimitTable = systemConfigTab->findChild<QTableWidget*>("weightLimitTable");
        if (weightLimitTable) {
            for (int i = 0; i < weightLimitTable->rowCount() && i < config.feeders.size(); ++i) {
                const FeederConfig& feederConfig = config.feeders[i];

                // 更新每个料仓的重量上下限
                for (int binIndex = 0; binIndex < 5; ++binIndex) {
                    // 下限列 (奇数列: 1, 3, 5, 7, 9)
                    int minCol = binIndex * 2 + 1;
                    if (weightLimitTable->item(i, minCol) && binIndex < feederConfig.binMinWeights.size()) {
                        weightLimitTable->item(i, minCol)->setText(
                            QString::number(feederConfig.binMinWeights[binIndex], 'f', 1));
                    }

                    // 上限列 (偶数列: 2, 4, 6, 8, 10)
                    int maxCol = binIndex * 2 + 2;
                    if (weightLimitTable->item(i, maxCol) && binIndex < feederConfig.binMaxWeights.size()) {
                        weightLimitTable->item(i, maxCol)->setText(
                            QString::number(feederConfig.binMaxWeights[binIndex], 'f', 1));
                    }
                }
            }
        }

    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新系统配置显示异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

// 创建MES数据显示Tab页面
void MainWindow::createMESDataDisplayTab() {
    QWidget *mesDataTab = new QWidget(this);
    QVBoxLayout *mainLayout = new QVBoxLayout(mesDataTab);

    // 创建滚动区域
    QScrollArea *scrollArea = new QScrollArea(this);
    QWidget *scrollWidget = new QWidget();
    QVBoxLayout *scrollLayout = new QVBoxLayout(scrollWidget);

    // 标题和说明
    QLabel *titleLabel = new QLabel("MES数据显示", this);
    titleLabel->setStyleSheet("font-size: 16px; font-weight: bold; color: #2c3e50; margin: 10px 0;");
    scrollLayout->addWidget(titleLabel);

    QLabel *infoLabel = new QLabel("实时显示MES服务器的区域数据、投料机数据和发送数据历史", this);
    infoLabel->setStyleSheet("color: #7f8c8d; margin-bottom: 20px;");
    scrollLayout->addWidget(infoLabel);

    // 区域数据显示组
    QGroupBox *areaDataGroup = new QGroupBox("区域数据状态", this);
    QVBoxLayout *areaLayout = new QVBoxLayout(areaDataGroup);

    // 区域数据表格
    QTableWidget *areaDataTable = new QTableWidget(0, 4, this);
    areaDataTable->setObjectName("areaDataTable");
    areaDataTable->setHorizontalHeaderLabels({"区域ID", "料号", "是否屏蔽", "桶数量"});
    areaDataTable->horizontalHeader()->setStretchLastSection(true);
    areaDataTable->setAlternatingRowColors(true);
    areaDataTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    areaDataTable->setEditTriggers(QAbstractItemView::NoEditTriggers);

    // 设置表格自适应高度，显示更多内容
    areaDataTable->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    areaDataTable->setMinimumHeight(200);
    areaDataTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    areaDataTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 初始调整表格高度
    adjustTableHeight(areaDataTable, 400);

    areaLayout->addWidget(areaDataTable);
    scrollLayout->addWidget(areaDataGroup);

    // 投料机数据显示组
    QGroupBox *feederDataGroup = new QGroupBox("投料机数据状态", this);
    QVBoxLayout *feederLayout = new QVBoxLayout(feederDataGroup);

    // 投料机数据表格
    QTableWidget *feederDataTable = new QTableWidget(0, 8, this);
    feederDataTable->setObjectName("feederDataTable");
    feederDataTable->setHorizontalHeaderLabels({"投料机ID", "变更时间", "料仓1", "料仓2", "料仓3", "料仓4", "料仓5", "状态"});
    feederDataTable->horizontalHeader()->setStretchLastSection(true);
    feederDataTable->setAlternatingRowColors(true);
    feederDataTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    feederDataTable->setEditTriggers(QAbstractItemView::NoEditTriggers);

    // 设置表格自适应高度，显示更多内容
    feederDataTable->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    feederDataTable->setMinimumHeight(200);
    feederDataTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    feederDataTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 初始调整表格高度
    adjustTableHeight(feederDataTable, 300);

    feederLayout->addWidget(feederDataTable);
    scrollLayout->addWidget(feederDataGroup);

    // 发送数据历史显示组
    QGroupBox *sendDataGroup = new QGroupBox("发送数据历史", this);
    QVBoxLayout *sendDataLayout = new QVBoxLayout(sendDataGroup);

    // 历史数据控制
    QHBoxLayout *historyControlLayout = new QHBoxLayout();
    QLabel *historyLabel = new QLabel("显示最近:", this);
    QSpinBox *historyCountSpinBox = new QSpinBox(this);
    historyCountSpinBox->setObjectName("historyCountSpinBox");
    historyCountSpinBox->setRange(10, 1000);
    historyCountSpinBox->setValue(100);
    historyCountSpinBox->setSuffix(" 条记录");

    QPushButton *refreshButton = new QPushButton("刷新数据", this);
    refreshButton->setStyleSheet("QPushButton { background-color: #3498db; color: white; padding: 5px 10px; border: none; border-radius: 3px; } QPushButton:hover { background-color: #2980b9; }");

    QPushButton *clearHistoryButton = new QPushButton("清空历史", this);
    clearHistoryButton->setStyleSheet("QPushButton { background-color: #e74c3c; color: white; padding: 5px 10px; border: none; border-radius: 3px; } QPushButton:hover { background-color: #c0392b; }");

    historyControlLayout->addWidget(historyLabel);
    historyControlLayout->addWidget(historyCountSpinBox);
    historyControlLayout->addStretch();
    historyControlLayout->addWidget(refreshButton);
    historyControlLayout->addWidget(clearHistoryButton);
    sendDataLayout->addLayout(historyControlLayout);

    // 发送数据历史表格
    QTableWidget *sendDataTable = new QTableWidget(0, 10, this);
    sendDataTable->setObjectName("sendDataTable");
    sendDataTable->setHorizontalHeaderLabels({"批次号", "倾倒时间", "投料机号", "料仓号", "倾倒重量",
                                             "最小重量", "最大重量", "任务批次", "任务投料机", "任务料仓"});
    sendDataTable->horizontalHeader()->setStretchLastSection(true);
    sendDataTable->setAlternatingRowColors(true);
    sendDataTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    sendDataTable->setEditTriggers(QAbstractItemView::NoEditTriggers);

    // 设置表格自适应高度，显示更多历史数据
    sendDataTable->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    sendDataTable->setMinimumHeight(300);  // 历史数据表格设置更大的最小高度
    sendDataTable->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    sendDataTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);

    // 初始调整表格高度
    adjustTableHeight(sendDataTable, 500);

    sendDataLayout->addWidget(sendDataTable);
    scrollLayout->addWidget(sendDataGroup);

    // 不添加stretch，让表格能够充分利用空间

    // 设置滚动区域
    scrollArea->setWidget(scrollWidget);
    scrollArea->setWidgetResizable(true);
    scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);

    // 设置滚动区域的大小策略，让它能够扩展
    scrollArea->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    mainLayout->addWidget(scrollArea);

    // 添加到Tab控件
    tabWidget->addTab(mesDataTab, "MES数据");

    // 连接信号
    connect(refreshButton, &QPushButton::clicked, this, &MainWindow::onRefreshMESDataClicked);
    connect(clearHistoryButton, &QPushButton::clicked, this, &MainWindow::onClearMESHistoryClicked);
    connect(historyCountSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &MainWindow::onMESHistoryCountChanged);

    // 启动定时器定期更新MES数据显示
    QTimer *mesUpdateTimer = new QTimer(this);
    mesUpdateTimer->setObjectName("mesUpdateTimer");
    connect(mesUpdateTimer, &QTimer::timeout, this, &MainWindow::updateMESDataDisplay);
    mesUpdateTimer->start(5000); // 每5秒更新一次
}

// MES数据显示相关槽函数实现
void MainWindow::onRefreshMESDataClicked() {
    updateMESDataDisplay();

    if (logList) {
        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
        logList->addItem(QString("[%1] [INFO] MES数据已刷新").arg(timestamp));
        logList->scrollToBottom();
    }
}

void MainWindow::onClearMESHistoryClicked() {
    if (!configManager) return;

    // 确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(this,
        "确认清空", "确定要清空MES发送数据历史吗？此操作不可撤销。",
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (reply == QMessageBox::Yes) {
        try {
            if (configManager->clearMESSendDataHistory()) {
                updateMESDataDisplay();

                if (logList) {
                    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                    logList->addItem(QString("[%1] [INFO] MES发送数据历史已清空").arg(timestamp));
                    logList->scrollToBottom();
                }

                QMessageBox::information(this, "清空成功", "MES发送数据历史已清空");
            } else {
                QMessageBox::warning(this, "清空失败", "无法清空MES发送数据历史");
            }
        } catch (const std::exception& e) {
            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [ERROR] 清空MES历史异常: %2").arg(timestamp, e.what()));
                logList->scrollToBottom();
            }
            QMessageBox::critical(this, "清空异常", QString("清空MES历史时发生异常: %1").arg(e.what()));
        }
    }
}

void MainWindow::onMESHistoryCountChanged(int count) {
    // 当历史记录数量改变时，重新更新显示
    updateMESDataDisplay();
}

void MainWindow::updateMESDataDisplay() {
    if (!configManager) return;

    try {
        // 更新区域数据表格
        QTableWidget *areaDataTable = findChild<QTableWidget*>("areaDataTable");
        if (areaDataTable) {
            auto areaDataMap = configManager->loadAllMESAreaData();

            areaDataTable->setRowCount(areaDataMap.size());
            int row = 0;

            for (const auto& pair : areaDataMap) {
                char areaId = pair.first;
                auto areaData = pair.second;

                if (areaData) {
                    areaDataTable->setItem(row, 0, new QTableWidgetItem(QString(areaId)));
                    areaDataTable->setItem(row, 1, new QTableWidgetItem(QString::fromStdString(areaData->sandType)));
                    areaDataTable->setItem(row, 2, new QTableWidgetItem(areaData->isBlocked ? "是" : "否"));
                    areaDataTable->setItem(row, 3, new QTableWidgetItem(QString::number(areaData->buckets.size())));

                    // 设置行颜色
                    QColor rowColor = areaData->isBlocked ? QColor(255, 200, 200) : QColor(200, 255, 200);
                    for (int col = 0; col < areaDataTable->columnCount(); ++col) {
                        if (areaDataTable->item(row, col)) {
                            areaDataTable->item(row, col)->setBackground(rowColor);
                        }
                    }
                }
                row++;
            }
        }

        // 更新投料机数据表格
        QTableWidget *feederDataTable = findChild<QTableWidget*>("feederDataTable");
        if (feederDataTable) {
            auto feederDataMap = configManager->loadAllMESFeederData();

            feederDataTable->setRowCount(feederDataMap.size());
            int row = 0;

            for (const auto& pair : feederDataMap) {
                int feederId = pair.first;
                auto feederData = pair.second;

                if (feederData) {
                    feederDataTable->setItem(row, 0, new QTableWidgetItem(QString::number(feederId)));
                    feederDataTable->setItem(row, 1, new QTableWidgetItem(QString::fromStdString(feederData->changeTime)));

                    // 显示各料仓的料号
                    for (int binIndex = 0; binIndex < 5 && binIndex < feederData->sandTypes.size(); ++binIndex) {
                        feederDataTable->setItem(row, binIndex + 2,
                            new QTableWidgetItem(QString::fromStdString(feederData->sandTypes[binIndex])));
                    }

                    feederDataTable->setItem(row, 7, new QTableWidgetItem("正常"));
                }
                row++;
            }
        }

        // 更新发送数据历史表格
        QTableWidget *sendDataTable = findChild<QTableWidget*>("sendDataTable");
        QSpinBox *historyCountSpinBox = findChild<QSpinBox*>("historyCountSpinBox");
        if (sendDataTable && historyCountSpinBox) {
            int maxCount = historyCountSpinBox->value();
            auto sendDataHistory = configManager->loadMESSendDataHistory(maxCount);

            sendDataTable->setRowCount(sendDataHistory.size());

            for (int row = 0; row < sendDataHistory.size(); ++row) {
                auto sendData = sendDataHistory[row];
                if (sendData) {
                    sendDataTable->setItem(row, 0, new QTableWidgetItem(QString::fromStdString(sendData->batchNumber)));
                    sendDataTable->setItem(row, 1, new QTableWidgetItem(QString::fromStdString(sendData->dumpTime)));
                    sendDataTable->setItem(row, 2, new QTableWidgetItem(QString::number(sendData->feederNumber)));
                    sendDataTable->setItem(row, 3, new QTableWidgetItem(QString::number(sendData->binNumber)));
                    sendDataTable->setItem(row, 4, new QTableWidgetItem(QString::number(sendData->dumpWeight, 'f', 2)));
                    sendDataTable->setItem(row, 5, new QTableWidgetItem(QString::number(sendData->minWeight, 'f', 2)));
                    sendDataTable->setItem(row, 6, new QTableWidgetItem(QString::number(sendData->maxWeight, 'f', 2)));
                    sendDataTable->setItem(row, 7, new QTableWidgetItem(QString::fromStdString(sendData->taskBatchNumber)));
                    sendDataTable->setItem(row, 8, new QTableWidgetItem(QString::number(sendData->taskFeederNumber)));
                    sendDataTable->setItem(row, 9, new QTableWidgetItem(QString::number(sendData->taskBinNumber)));
                }
            }

            // 滚动到最新记录
            if (sendDataTable->rowCount() > 0) {
                sendDataTable->scrollToBottom();
            }
        }

        // 调整所有表格的高度以适应内容
        areaDataTable = findChild<QTableWidget*>("areaDataTable");
        feederDataTable = findChild<QTableWidget*>("feederDataTable");
        sendDataTable = findChild<QTableWidget*>("sendDataTable");

        if (areaDataTable) {
            adjustTableHeight(areaDataTable, 400);  // 区域数据表格最大400像素
        }
        if (feederDataTable) {
            adjustTableHeight(feederDataTable, 300);  // 投料机数据表格最大300像素
        }
        if (sendDataTable) {
            adjustTableHeight(sendDataTable, 500);  // 历史数据表格最大500像素
        }

    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 更新MES数据显示异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}

// 配置验证方法实现
bool MainWindow::validateIPAddress(const QString& ip, QString& errorMessage) {
    if (ip.isEmpty()) {
        errorMessage = "IP地址不能为空";
        return false;
    }

    QStringList parts = ip.split('.');
    if (parts.size() != 4) {
        errorMessage = "IP地址格式错误，应为xxx.xxx.xxx.xxx";
        return false;
    }

    for (const QString& part : parts) {
        bool ok;
        int num = part.toInt(&ok);
        if (!ok || num < 0 || num > 255) {
            errorMessage = "IP地址中的数字必须在0-255之间";
            return false;
        }
    }

    return true;
}

bool MainWindow::validatePort(int port, QString& errorMessage) {
    if (port < 1 || port > 65535) {
        errorMessage = "端口号必须在1-65535之间";
        return false;
    }

    // 检查常用的系统保留端口
    if (port < 1024) {
        errorMessage = "建议使用1024以上的端口号";
        return false;
    }

    return true;
}

bool MainWindow::validateTimeout(int timeout, QString& errorMessage) {
    if (timeout < 1000 || timeout > 60000) {
        errorMessage = "超时时间必须在1000-60000毫秒之间";
        return false;
    }

    return true;
}

bool MainWindow::validateWeight(float weight, QString& errorMessage) {
    if (weight < 0) {
        errorMessage = "重量不能为负数";
        return false;
    }

    if (weight > 10000) {
        errorMessage = "重量不能超过10000kg";
        return false;
    }

    return true;
}

void MainWindow::showValidationError(QWidget* widget, const QString& message) {
    if (!widget) return;

    // 设置错误样式
    widget->setStyleSheet("border: 2px solid red; background-color: #ffe6e6;");

    // 显示工具提示
    widget->setToolTip(message);

    // 显示错误消息
    QMessageBox::warning(this, "输入验证错误", message);
}

void MainWindow::clearValidationError(QWidget* widget) {
    if (!widget) return;

    // 清除错误样式
    widget->setStyleSheet("");

    // 清除工具提示
    widget->setToolTip("");
}

void MainWindow::showValidationError(QTableWidgetItem* item, const QString& message) {
    if (!item) return;

    // 设置错误样式
    item->setBackground(QBrush(QColor(255, 230, 230))); // 浅红色背景
    item->setData(Qt::ToolTipRole, message); // 设置工具提示显示错误信息

    // 显示错误消息
    QMessageBox::warning(this, "输入验证错误", message);
}

void MainWindow::clearValidationError(QTableWidgetItem* item) {
    if (!item) return;

    // 清除错误样式
    item->setBackground(QBrush()); // 恢复默认背景
    item->setData(Qt::ToolTipRole, QVariant()); // 清除工具提示
}

// 配置热更新实现
void MainWindow::onConfigurationChanged(const std::string& section, const std::string& key) {
    if (!configManager) return;

    try {
        QString sectionStr = QString::fromStdString(section);
        QString keyStr = QString::fromStdString(key);

        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [CONFIG] 配置变更: %2.%3").arg(timestamp, sectionStr, keyStr));
            logList->scrollToBottom();
        }

        // 根据配置变更的类型进行相应的热更新
        if (section == "system") {
            if (key == "directMode" || key == "all") {
                // 直连模式变更
                bool newDirectMode = configManager->isDirectModeEnabled();
                if (directMode != newDirectMode) {
                    directMode = newDirectMode;
                    updateSystemStatusDisplay();

                    // 通知控制系统模式变更
                    if (controlSystem) {
                        controlSystem->enableDirectMode(directMode);
                    }

                    if (logList) {
                        QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                        QString mode = directMode ? "直连模式" : "标准模式";
                        logList->addItem(QString("[%1] [INFO] 系统模式热更新为: %2").arg(timestamp, mode));
                        logList->scrollToBottom();
                    }
                }
            }

            if (key == "all" || key == "imported") {
                // 全量配置更新，刷新所有UI显示
                updateSystemConfigDisplay();
                updateMESDataDisplay();

                // 通知控制系统配置已更新
                if (controlSystem) {
                    controlSystem->setConfig(configManager->getSystemConfig());
                }
            }
        }
        else if (section == "feeder") {
            // 投料机配置变更
            updateSystemConfigDisplay();

            // 通知控制系统投料机配置已更新
            if (controlSystem) {
                controlSystem->setConfig(configManager->getSystemConfig());
            }

            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] 投料机%2配置已热更新").arg(timestamp, keyStr));
                logList->scrollToBottom();
            }
        }
        else if (section == "abb") {
            // ABB机器人配置变更
            updateSystemConfigDisplay();

            // 通知控制系统ABB配置已更新
            if (controlSystem) {
                controlSystem->setConfig(configManager->getSystemConfig());
            }

            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] ABB机器人配置已热更新").arg(timestamp));
                logList->scrollToBottom();
            }
        }
        else if (section == "mes" || section == "mes_area" || section == "mes_feeder" || section == "mes_send_data") {
            // MES数据变更
            updateMESDataDisplay();

            if (logList) {
                QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
                logList->addItem(QString("[%1] [INFO] MES数据已热更新").arg(timestamp));
                logList->scrollToBottom();
            }
        }

    } catch (const std::exception& e) {
        if (logList) {
            QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
            logList->addItem(QString("[%1] [ERROR] 配置热更新异常: %2").arg(timestamp, e.what()));
            logList->scrollToBottom();
        }
    }
}
