#include <QtWidgets/QApplication>
#include <QtCore/QDir>
#include <QtCore/QStandardPaths>
#include <iostream>
#include <memory>

#include "main_window.h"
#include "control_system.h"
#include "config_manager.h"
#pragma execution_character_set("utf-8")
// 全局变量
std::shared_ptr<ControlSystem> g_controlSystem;
std::shared_ptr<ConfigManager> g_configManager;

// 信号处理
#ifdef _WIN32
#include <windows.h>
BOOL WINAPI ConsoleHandler(DWORD signal) {
    if (signal == CTRL_C_EVENT || signal == CTRL_CLOSE_EVENT) {
        std::cout << "Shutting down system..." << std::endl;
        if (g_controlSystem) {
            g_controlSystem->stop();
        }
        return TRUE;
    }
    return FALSE;
}
#else
#include <signal.h>
void signalHandler(int signal) {
    if (signal == SIGINT || signal == SIGTERM) {
        std::cout << "Shutting down system..." << std::endl;
        if (g_controlSystem) {
            g_controlSystem->stop();
        }
        exit(0);
    }
}
#endif

// 初始化应用程序目录
bool initializeAppDirectories() {
    QDir appDir = QDir::current();
    
    // 创建必要的目录
    QStringList dirs = {"config", "logs", "backup", "data"};
    
    for (const QString& dir : dirs) {
        if (!appDir.exists(dir)) {
            if (!appDir.mkpath(dir)) {
                std::cerr << "Failed to create directory: " << dir.toStdString() << std::endl;
                return false;
            }
        }
    }
    
    return true;
}

// 初始化日志系统
bool initializeLogging() {
    // 这里可以初始化glog或其他日志系统
    // 目前使用简单的cout输出
    std::cout << "MJServer Refactor starting..." << std::endl;
    std::cout << "Working directory: " << QDir::currentPath().toStdString() << std::endl;
    return true;
}

// 初始化配置管理器
bool initializeConfigManager() {
    try {
        g_configManager = std::make_shared<ConfigManager>();

        std::cout << "Attempting to load configuration file..." << std::endl;

        // 尝试加载INI配置文件
        bool configLoaded = false;
        try {
            configLoaded = g_configManager->loadConfig("direct_mode.ini");
        } catch (const std::exception& e) {
            std::cerr << "Config file loading failed: " << e.what() << std::endl;
            configLoaded = false;
        }

        if (!configLoaded) {
            std::cout << "Config file not found or invalid, creating default configuration..." << std::endl;

            // 创建默认配置
            SystemConfig defaultConfig = g_configManager->createDefaultConfig();
            g_configManager->setSystemConfig(defaultConfig);

            // 保存默认配置为INI文件
            try {
                g_configManager->saveConfig("direct_mode.ini");
                std::cout << "Default configuration saved to INI file" << std::endl;
            } catch (const std::exception& e) {
                std::cerr << "Failed to save default configuration: " << e.what() << std::endl;
            }
        } else {
            std::cout << "Configuration loaded from INI file successfully" << std::endl;
        }

        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize config manager: " << e.what() << std::endl;
        return false;
    }
}

// 初始化控制系统（仅初始化，不启动）
bool initializeControlSystem() {
    try {
        g_controlSystem = std::make_shared<ControlSystem>();

        // 设置配置
        if (g_configManager) {
            g_controlSystem->setConfig(g_configManager->getSystemConfig());
        }

        // 设置回调
        g_controlSystem->setStatusCallback([](const std::string& status) {
            std::cout << "[STATUS] " << status << std::endl;
        });

        g_controlSystem->setTaskLogCallback([](const std::string& message) {
            std::cout << "[TASK] " << message << std::endl;
        });

        std::cout << "Control system initialized successfully (not started yet)" << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to initialize control system: " << e.what() << std::endl;
        return false;
    }
}

// 启动基础服务器（ABB和MES）
bool startBaseServers() {
    try {
        if (!g_controlSystem) {
            std::cerr << "Control system not initialized" << std::endl;
            return false;
        }

        // 只启动ABB服务器和MES服务器，不连接其他设备
        if (!g_controlSystem->startBaseServers()) {
            std::cerr << "Failed to start base servers (ABB & MES)" << std::endl;
            return false;
        }

        std::cout << "Base servers (ABB & MES) started successfully" << std::endl;
        std::cout << "Waiting for user to click start button to connect other devices..." << std::endl;
        return true;

    } catch (const std::exception& e) {
        std::cerr << "Failed to start base servers: " << e.what() << std::endl;
        return false;
    }
}

// 清理资源
void cleanup() {
    std::cout << "Cleaning up resources..." << std::endl;
    
    if (g_controlSystem) {
        g_controlSystem->stop();
        g_controlSystem.reset();
    }
    
    if (g_configManager) {
        g_configManager.reset();
    }
    
    std::cout << "Cleanup completed" << std::endl;
}

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("MJServer Refactor");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("MeiJing");
    app.setOrganizationDomain("meijing.com");
    
    // 设置信号处理
#ifdef _WIN32
    SetConsoleCtrlHandler(ConsoleHandler, TRUE);
#else
    signal(SIGINT, signalHandler);
    signal(SIGTERM, signalHandler);
#endif
    
    try {
        // 初始化应用程序目录
        if (!initializeAppDirectories()) {
            std::cerr << "Failed to initialize application directories" << std::endl;
            return 1;
        }
        
        // 初始化日志系统
        if (!initializeLogging()) {
            std::cerr << "Failed to initialize logging system" << std::endl;
            return 1;
        }
        
        // 初始化配置管理器
        if (!initializeConfigManager()) {
            std::cerr << "Failed to initialize configuration manager" << std::endl;
            return 1;
        }
        
        // 初始化控制系统
        if (!initializeControlSystem()) {
            std::cerr << "Failed to initialize control system" << std::endl;
            return 1;
        }
        
        // 创建主窗口
        MainWindow window;
        if (!window.initialize()) {
            std::cerr << "Failed to initialize main window" << std::endl;
            return 1;
        }
        
        // 设置控制系统和配置管理器
        window.setControlSystem(g_controlSystem);
        window.setConfigManager(g_configManager);

        std::cout << "About to show window..." << std::endl;

        try {
            // 显示主窗口
            window.show();
            std::cout << "Window shown successfully" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "Exception during window.show(): " << e.what() << std::endl;
            return 1;
        } catch (...) {
            std::cerr << "Unknown exception during window.show()" << std::endl;
            return 1;
        }
        
        std::cout << "Application started successfully" << std::endl;
        std::cout << "Direct mode: " << (g_configManager->isDirectModeEnabled() ? "Enabled" : "Disabled") << std::endl;

        // 启动基础服务器（ABB和MES），等待用户点击启动按钮连接其他设备
        if (!startBaseServers()) {
            std::cerr << "Failed to start base servers" << std::endl;
            return 1;
        }
        
        // 运行应用程序
        int result = app.exec();
        
        // 清理资源
        cleanup();
        
        return result;
        
    } catch (const std::exception& e) {
        std::cerr << "Unhandled exception: " << e.what() << std::endl;
        cleanup();
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        cleanup();
        return 1;
    }
}
