#pragma once

#include <string>
#include <vector>
#include <memory>
#include <atomic>
#include <map>
#include <functional>

// 前向声明
namespace Fuxi {
namespace Common {
class Executor;
}
}

// 投料机配置结构
struct FeederConfig {
    int id;
    std::string ip;
    int port;
    bool enabled;
    int timeout;           // 超时时间(毫秒)
    bool enableHeartbeat;  // 启用心跳检测

    // 每个料仓的重量上下限配置 (5个料仓)
    std::vector<float> binMinWeights;  // 料仓重量下限
    std::vector<float> binMaxWeights;  // 料仓重量上限

    FeederConfig() : id(0), port(502), enabled(true), timeout(5000), enableHeartbeat(true) {
        // 初始化5个料仓的重量上下限，默认值：下限100kg，上限1000kg
        binMinWeights.resize(5, 100.0f);
        binMaxWeights.resize(5, 1000.0f);
    }

    FeederConfig(int _id, const std::string& _ip, int _port = 502, bool _enabled = true, int _timeout = 5000, bool _heartbeat = true)
        : id(_id), ip(_ip), port(_port), enabled(_enabled), timeout(_timeout), enableHeartbeat(_heartbeat) {
        // 初始化5个料仓的重量上下限，默认值：下限100kg，上限1000kg
        binMinWeights.resize(5, 100.0f);
        binMaxWeights.resize(5, 1000.0f);
    }
};

// 料仓状态结构
struct BinStatus {
    int binId;
    float currentWeight;    // 当前重量
    float targetWeight;     // 目标重量
    float minWeight;        // 最小重量
    float maxWeight;        // 最大重量
    bool isBlocked;         // 是否被禁止
    bool isFeeding;         // 是否正在投料
    std::string sandType;   // 砂类型
    
    BinStatus() : binId(0), currentWeight(0.0f), targetWeight(0.0f), 
                  minWeight(0.0f), maxWeight(0.0f), isBlocked(false), isFeeding(false) {}
};

// 投料机状态结构
struct FeederStatus {
    int feederId;
    bool isConnected;
    bool heartbeatOK;
    std::string changeTime;
    std::vector<BinStatus> bins;
    std::vector<std::string> sandTypes;
    bool isBlocked;
    
    FeederStatus() : feederId(0), isConnected(false), heartbeatOK(false), isBlocked(false) {
        bins.resize(5);  // 5个料仓
        sandTypes.resize(6);  // 6种砂类型
        for (int i = 0; i < 5; ++i) {
            bins[i].binId = i;
        }
    }
};

// 任务信息结构
struct TaskInfo {
    std::string taskId;
    std::string batchNumber;
    int feederId;
    int binId;
    int bucketId;
    char areaId;
    float weight;
    float minWeight;
    float maxWeight;
    std::string createTime;
    
    TaskInfo() : feederId(0), binId(0), bucketId(0), areaId('A'), 
                 weight(0.0f), minWeight(0.0f), maxWeight(0.0f) {}
};

// 任务状态结构
struct TaskStatus {
    std::string taskId;
    enum Status {
        PENDING,    // 等待中
        RUNNING,    // 执行中
        COMPLETED,  // 已完成
        FAILED,     // 失败
        CANCELLED   // 已取消
    } status;

    float progress;         // 进度 0.0-1.0
    std::string message;    // 状态消息
    std::string currentStep; // 当前执行步骤
    std::string errorMessage; // 错误消息
    std::string startTime;
    std::string endTime;

    // 任务统计字段
    int readyTasks;         // 准备就绪的任务数
    int executingTasks;     // 正在执行的任务数
    int completedTasks;     // 已完成的任务数
    float remainingWeight;  // 剩余重量

    TaskStatus() : status(PENDING), progress(0.0f), readyTasks(0), executingTasks(0), completedTasks(0), remainingWeight(0.0f) {}
};

// 区域数据结构
struct AreaData {
    std::string sandType;  // 区砂类别
    bool isBlocked;        // 是否被屏蔽
    struct Bucket {
        std::string batchNumber;  // 石英砂批号
        float weight;             // 重量
        bool isBlocked;           // 桶是否被屏蔽

        Bucket() : weight(0.0f), isBlocked(false) {}
    };
    std::vector<Bucket> buckets;  // 4个桶的数据

    AreaData() : isBlocked(false) {
        buckets.resize(4);
    }
};

// 投料机数据结构
struct FeederData {
    std::string changeTime;  // 更换生产图号时间
    std::vector<std::string> sandTypes;  // 6个料仓的砂类别
    
    FeederData() {
        sandTypes.resize(6);
    }
};

// 发送数据结构
struct SendData {
    std::string batchNumber;     // 机器人当次所取料砂批号
    std::string dumpTime;        // 当次倒料时间
    int feederNumber;            // 当次倒入投料机号码(1-6)
    int binNumber;               // 当次倒入料仓号(1-5)
    float dumpWeight;            // 当次倒入料砂重量
    float minWeight;             // 最小起加量
    float maxWeight;             // 最大加入量
    std::string taskBatchNumber;    // 任务批号
    int taskFeederNumber;           // 任务投料机
    int taskBinNumber;              // 任务料仓
    
    SendData() : feederNumber(0), binNumber(0), dumpWeight(0.0f), 
                 minWeight(0.0f), maxWeight(0.0f), taskFeederNumber(0), taskBinNumber(0) {}
};

// ABB服务器配置（ABB机器人作为客户端连接）
struct ABBConfig {
    std::string listenIp;       // 服务器监听IP
    int listenPort;             // 服务器监听端口
    int timeout;                // 连接超时时间(毫秒)
    int maxConnections;         // 最大连接数
    bool enableHeartbeat;       // 启用心跳检测
    int heartbeatInterval;      // 心跳间隔(毫秒)

    ABBConfig() : listenIp("0.0.0.0"), listenPort(7000), timeout(10000),
                  maxConnections(1), enableHeartbeat(true), heartbeatInterval(2000) {}
};

// 屏蔽配置结构
struct BlockConfig {
    std::map<int, bool> feederBlocked;                    // 投料机屏蔽状态 (feederId -> blocked)
    std::map<std::pair<int, int>, bool> binBlocked;       // 料仓屏蔽状态 (feederId, binId -> blocked)
    std::map<char, bool> areaBlocked;                     // 区域屏蔽状态 (areaId -> blocked)
    std::map<std::pair<char, int>, bool> bucketBlocked;   // 桶屏蔽状态 (areaId, bucketId -> blocked)

    BlockConfig() = default;
};

// MES数据配置
struct MESDataConfig {
    // 区域数据持久化
    std::map<char, std::shared_ptr<AreaData>> areaData;

    // 投料机数据持久化
    std::map<int, std::shared_ptr<FeederData>> feederData;

    // 最近的发送数据记录
    std::vector<std::shared_ptr<SendData>> recentSendData;

    // MES服务器配置
    struct ServerConfig {
        int port;
        std::string ip;
        bool autoSave;
        int maxSendDataHistory;  // 最大保存的发送数据历史记录数

        ServerConfig() : port(502), ip("0.0.0.0"), autoSave(true), maxSendDataHistory(1000) {}
    } serverConfig;

    MESDataConfig() {
        // 初始化区域数据 (A-V)
        for (char c = 'A'; c <= 'V'; ++c) {
            areaData[c] = std::make_shared<AreaData>();
        }

        // 初始化投料机数据 (1-6)
        for (int i = 1; i <= 6; ++i) {
            feederData[i] = std::make_shared<FeederData>();
        }
    }
};

// 系统配置
struct SystemConfig {
    bool enableDirectMode;
    std::vector<FeederConfig> feeders;
    ABBConfig abbRobot;
    BlockConfig blockConfig;  // 屏蔽配置
    MESDataConfig mesConfig;  // MES数据配置
    std::string logFilePath;  // 日志文件路径

    SystemConfig() : enableDirectMode(false), logFilePath("system.log") {
        // 默认5台投料机配置
        for (int i = 1; i <= 5; ++i) {
            feeders.emplace_back(i, "192.168.1." + std::to_string(100 + i));
        }
    }

    // 拷贝构造函数
    SystemConfig(const SystemConfig& other)
        : enableDirectMode(other.enableDirectMode)
        , feeders(other.feeders)
        , abbRobot(other.abbRobot)
        , blockConfig(other.blockConfig)
        , logFilePath(other.logFilePath) {
        // 安全地拷贝MES配置
        try {
            mesConfig = other.mesConfig;
        } catch (...) {
            // 如果拷贝失败，使用默认配置
            mesConfig = MESDataConfig();
        }
    }

    // 赋值操作符
    SystemConfig& operator=(const SystemConfig& other) {
        if (this != &other) {
            enableDirectMode = other.enableDirectMode;
            feeders = other.feeders;
            abbRobot = other.abbRobot;
            blockConfig = other.blockConfig;
            logFilePath = other.logFilePath;

            // 安全地拷贝MES配置
            try {
                mesConfig = other.mesConfig;
            } catch (...) {
                // 如果拷贝失败，使用默认配置
                mesConfig = MESDataConfig();
            }
        }
        return *this;
    }
};

// 回调函数类型定义
using StatusCallback = std::function<void(const std::string&)>;
using TaskLogCallback = std::function<void(const std::string&)>;
using FeederStatusCallback = std::function<void(const std::vector<std::shared_ptr<FeederStatus>>&)>;
using AreaStatusCallback = std::function<void(const std::map<char, std::shared_ptr<AreaData>>&)>;

// 常量定义
namespace Constants {
    // Modbus寄存器地址
    constexpr int ADDR_HEARTBEAT_READ = 40069;      // 投料机心跳变量(读取)
    constexpr int ADDR_BIN_BLOCK = 40070;           // 料仓禁止倒料
    constexpr int ADDR_BIN_WEIGHT_START = 40071;    // 料仓实际重量起始地址
    constexpr int ADDR_HEARTBEAT_WRITE = 40100;     // 倒料机心跳变量(写入)
    constexpr int ADDR_BIN_FEEDING = 40101;         // 料仓倒料运行反馈
    constexpr int ADDR_BIN_MIN_WEIGHT_START = 40102; // 料仓缺料下限值起始地址
    constexpr int ADDR_BIN_TARGET_WEIGHT_START = 40112; // 料仓加料目标值起始地址
    
    // 系统常量
    constexpr int FEEDERS_COUNT = 5;                // 投料机数量
    constexpr int BINS_PER_FEEDER = 5;              // 每台投料机的料仓数量
    constexpr int SAND_TYPES_COUNT = 6;             // 砂类型数量
    constexpr int AREAS_COUNT = 22;                 // 区域数量 (A-V)
    constexpr int BUCKETS_PER_AREA = 4;             // 每个区域的桶数量
    
    // 通信参数
    constexpr int MODBUS_TIMEOUT = 5000;            // Modbus超时时间(ms)
    constexpr int SOCKET_TIMEOUT = 5000;            // Socket超时时间(ms)
    constexpr int HEARTBEAT_INTERVAL = 200;         // 心跳间隔(ms)
    constexpr int STATUS_UPDATE_INTERVAL = 1000;    // 状态更新间隔(ms)
}

