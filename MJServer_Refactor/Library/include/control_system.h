#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <chrono>
#include <functional>
#include <mutex>
#include <queue>

#include "data_structures.h"

// 前向声明
class SystemAdapter;
class MesServer;
namespace Fuxi {
namespace Common {
class Executor;
}
}

/**
 * 重构后的控制系统
 * 使用SystemAdapter来保持接口兼容性
 * 同时支持直连模式和传统PLC模式
 */
class ControlSystem {
public:
    ControlSystem();
    ~ControlSystem();
    
    // 系统控制
    bool start();
    bool startBaseServers();  // 新增：仅启动ABB和MES服务器
    bool connectOtherDevices(); // 新增：连接其他设备（投料机等）
    void stop();
    bool isRunning() const { return running; }
    bool areBaseServersRunning() const { return baseServersRunning; }
    
    // 配置管理
    bool loadConfig(const std::string& configFile = "config/direct_mode.json");
    bool saveConfig(const std::string& configFile = "config/direct_mode.json");
    const SystemConfig& getConfig() const { return config; }
    void setConfig(const SystemConfig& newConfig);
    
    // 模式切换
    bool enableDirectMode(bool enable);
    bool isDirectModeEnabled() const { return config.enableDirectMode; }
    
    // 兼容原有接口的方法
    std::shared_ptr<TaskInfo> findNextTask();
    bool startTask(std::shared_ptr<TaskInfo> task);
    std::shared_ptr<TaskStatus> getTaskStatus() const;
    
    // 状态查询接口
    std::vector<std::shared_ptr<FeederStatus>> getFeederStatus();
    std::map<char, std::shared_ptr<AreaData>> getAreaStatus();
    bool isSystemAuto() const;
    bool hasError() const;
    bool isHeartbeatOK() const;
    
    // 控制接口
    bool emergencyStop();
    bool reset();
    bool setSystemMode(bool autoMode);
    
    // 投料机控制
    bool isFeederEnabled(int feederId) const;
    bool isBinEnabled(int feederId, int binId) const;
    bool startFeeding(int feederId, int binId);
    bool stopFeeding(int feederId, int binId);
    bool stopAllFeeding();
    
    // 重量管理
    bool setBinTargetWeight(int feederId, int binId, float targetWeight);
    bool getBinWeight(int feederId, int binId, float& weight);
    
    // 回调设置（保持与原系统兼容）
    using StatusCallback = std::function<void(const std::string&)>;
    using TaskLogCallback = std::function<void(const std::string&)>;
    using FeederStatusCallback = std::function<void(const std::vector<std::shared_ptr<FeederStatus>>&)>;
    using AreaStatusCallback = std::function<void(const std::map<char, std::shared_ptr<AreaData>>&)>;
    
    void setStatusCallback(StatusCallback callback) { statusCallback = callback; }
    void setTaskLogCallback(TaskLogCallback callback) { taskLogCallback = callback; }
    void setFeederStatusCallback(FeederStatusCallback callback) { feederStatusCallback = callback; }
    void setAreaStatusCallback(AreaStatusCallback callback) { areaStatusCallback = callback; }
    
    // 统计信息
    struct SystemStatistics {
        int tasksCompleted;
        int tasksFailed;
        int systemErrors;
        int modeChanges;
        int feedingOperations;
        int failedOperations;
        int connectionAttempts;
        int successfulConnections;
        std::chrono::steady_clock::time_point startTime;
        std::chrono::steady_clock::time_point lastTaskTime;

        SystemStatistics() : tasksCompleted(0), tasksFailed(0), systemErrors(0), modeChanges(0),
                           feedingOperations(0), failedOperations(0), connectionAttempts(0), successfulConnections(0) {}
    };
    
    const SystemStatistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = SystemStatistics(); }
    
    // 诊断和维护
    std::vector<std::string> getDiagnosticInfo();
    bool performSystemCheck();
    
private:
    SystemConfig config;
    std::atomic<bool> running;
    std::atomic<bool> baseServersRunning;  // 新增：基础服务器运行状态
    std::atomic<bool> emergencyStopFlag;
    
    // 核心组件
    std::shared_ptr<SystemAdapter> systemAdapter;
    std::shared_ptr<MesServer> mesServer;
    std::shared_ptr<Fuxi::Common::Executor> executor;
    
    // 统计信息
    SystemStatistics stats;
    
    // 回调函数
    StatusCallback statusCallback;
    TaskLogCallback taskLogCallback;
    FeederStatusCallback feederStatusCallback;
    AreaStatusCallback areaStatusCallback;
    
    // 内部工作线程
    void run();
    void monitorSystem();
    
    // 初始化方法
    bool initializeComponents();
    bool initializeDirectMode();
    bool initializeLegacyMode();

    // 分阶段启动方法
    bool initializeBaseServersDirectMode();
    bool initializeBaseServersLegacyMode();
    bool connectDevicesDirectMode();
    bool connectDevicesLegacyMode();
    
    // 配置管理
    bool loadConfigFromFile(const std::string& filename);
    bool saveConfigToFile(const std::string& filename);
    SystemConfig createDefaultConfig();
    
    // 状态通知
    void notifyStatus(const std::string& status);
    void notifyTaskLog(const std::string& message);
    void notifyFeederStatus(const std::vector<std::shared_ptr<FeederStatus>>& status);
    void notifyAreaStatus(const std::map<char, std::shared_ptr<AreaData>>& status);
    
    // 错误处理
    void handleSystemError(const std::string& error);
    void handleTaskError(const std::string& taskId, const std::string& error);
    
    // 任务管理
    std::queue<std::shared_ptr<TaskInfo>> taskQueue;
    mutable std::mutex taskQueueMutex;
    std::shared_ptr<TaskStatus> currentTask;
    mutable std::mutex currentTaskMutex;

    void processTaskQueue();
    bool validateTask(std::shared_ptr<TaskInfo> task);
    void updateTaskStatistics(std::shared_ptr<TaskStatus> status);

    // 任务执行辅助方法
    bool isSystemReadyForTask(std::shared_ptr<TaskInfo> task);
    bool executeTask(std::shared_ptr<TaskInfo> task);
    std::string getCurrentTimeString();
};
