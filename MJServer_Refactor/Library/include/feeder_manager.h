#pragma once

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <atomic>
#include <mutex>
#include <functional>
#include "data_structures.h"

// 前向声明
class FeederClient;
namespace Fuxi {
namespace Common {
class Executor;
}
}

/**
 * 投料机管理器
 * 管理多台投料机的连接、状态监控和控制
 */
class FeederManager {
public:
    FeederManager();
    ~FeederManager();
    
    // 初始化和配置
    bool initialize(const std::vector<FeederConfig>& configs);
    bool start();
    void stop();
    bool isRunning() const { return running; }
    
    // 连接管理
    bool connectAllFeeders();
    void disconnectAllFeeders();
    bool connectFeeder(int feederId);
    void disconnectFeeder(int feederId);
    
    // 状态查询
    std::vector<std::shared_ptr<FeederStatus>> getFeederStatus();
    std::shared_ptr<FeederStatus> getFeederStatus(int feederId);
    bool isFeederConnected(int feederId) const;
    bool isFeederEnabled(int feederId) const;
    bool isBinEnabled(int feederId, int binId) const;
    bool isHeartbeatOK() const;
    bool hasError() const;
    
    // 重量管理
    bool getBinWeight(int feederId, int binId, float& weight);
    bool setBinTargetWeight(int feederId, int binId, float targetWeight);
    bool setBinMinWeight(int feederId, int binId, float minWeight);
    
    // 投料控制
    bool startFeeding(int feederId, int binId);
    bool stopFeeding(int feederId, int binId);
    bool stopAllFeeding();
    bool emergencyStop();
    bool reset();
    
    // 批量操作
    bool setAllBinTargetWeights(int feederId, const std::vector<float>& targetWeights);
    bool setAllBinMinWeights(int feederId, const std::vector<float>& minWeights);
    bool getAllBinWeights(int feederId, std::vector<float>& weights);
    
    // 配置管理
    const std::vector<FeederConfig>& getConfigs() const { return configs; }
    void updateConfig(const std::vector<FeederConfig>& newConfigs);
    
    // 回调设置
    using StatusCallback = std::function<void(const std::vector<std::shared_ptr<FeederStatus>>&)>;
    using ErrorCallback = std::function<void(const std::string&)>;
    using FeederEventCallback = std::function<void(int feederId, const std::string& event)>;
    
    void setStatusCallback(StatusCallback callback) { statusCallback = callback; }
    void setErrorCallback(ErrorCallback callback) { errorCallback = callback; }
    void setFeederEventCallback(FeederEventCallback callback) { feederEventCallback = callback; }
    
    // 统计信息
    struct ManagerStatistics {
        int totalFeeders;
        int connectedFeeders;
        int activeFeeders;
        int totalErrors;
        int totalReconnects;
        std::chrono::steady_clock::time_point startTime;
        
        ManagerStatistics() : totalFeeders(0), connectedFeeders(0), activeFeeders(0),
                             totalErrors(0), totalReconnects(0) {}
    };
    
    const ManagerStatistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = ManagerStatistics(); }
    
    // 诊断和维护
    bool performHealthCheck();
    std::vector<std::string> getDiagnosticInfo();
    bool calibrateFeeder(int feederId);
    
private:
    std::vector<FeederConfig> configs;
    std::map<int, std::shared_ptr<FeederClient>> feeders;
    std::shared_ptr<Fuxi::Common::Executor> executor;
    
    std::atomic<bool> running;
    mutable std::mutex feedersMutex;
    mutable std::mutex statusMutex;
    
    // 缓存状态
    std::vector<std::shared_ptr<FeederStatus>> cachedStatus;
    std::chrono::steady_clock::time_point lastStatusUpdate;
    
    // 统计信息
    ManagerStatistics stats;
    
    // 回调函数
    StatusCallback statusCallback;
    ErrorCallback errorCallback;
    FeederEventCallback feederEventCallback;
    
    // 内部工作线程
    void statusMonitorLoop();
    void healthCheckLoop();
    
    // 内部辅助方法
    bool createFeederClient(const FeederConfig& config);
    void removeFeederClient(int feederId);
    void updateStatistics();
    void updateStatusCache();
    
    // 错误处理
    void handleFeederError(int feederId, const std::string& error);
    void notifyError(const std::string& error);
    void notifyFeederEvent(int feederId, const std::string& event);
    void notifyStatusUpdate();
    
    // 验证方法
    bool validateFeederId(int feederId) const;
    bool validateBinId(int binId) const;
    bool validateWeight(float weight) const;
    
    // 重连管理
    void attemptReconnection(int feederId);
    bool shouldAttemptReconnect(int feederId) const;
};
