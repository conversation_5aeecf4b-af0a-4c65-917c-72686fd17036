#pragma once

#include <modbus.h>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <atomic>
#include "Executor.h"
#include "data_structures.h"

#define MAX_CONNECTIONS 10  // 最大并发连接数

// 前向声明
class SystemAdapter;

class MesServer {
public:
    MesServer(const std::string& ip = "0.0.0.0", int port = 5020);
    ~MesServer();

    bool start();
    void stop();
    void run();

    void processModbusQuery(uint8_t *query, int rc);

    const std::map<char, std::shared_ptr<AreaData>>& getAreas() const {
        return areas;
    }
    
    void updateSendData(std::shared_ptr<SendData> data);
    bool isConnected() const { return m_connected; }
    bool isHeartbeatOK() const { return heartbeatOK; }

    // 系统适配器设置
    void setSystemAdapter(std::shared_ptr<SystemAdapter> adapter);

    std::vector<std::shared_ptr<FeederData>> getFeeders() {return feeders;};
private:
    void handleReceivedData(int address, uint16_t* data, int count);
    void heartbeatLoop();
    bool reconnectModbus();
    
    // 工具函数
    void parseStringFromRegisters(const uint16_t* regs, int count, std::string& result);
    void parseFloatFromRegisters(const uint16_t* regs, float& result);
    void stringToRegisters(const std::string& str, uint16_t* regs, int maxRegs);
    void floatToRegisters(float value, uint16_t* regs);

    modbus_t* ctx;
    std::shared_ptr<modbus_mapping_t> mbMapping;
    int serverSocket;
    bool running;
    
    std::vector<std::shared_ptr<FeederData>> feeders;
    std::map<char, std::shared_ptr<AreaData>> areas;
    
    bool heartbeatOK=false;
    bool m_connected=false;
    
    std::shared_ptr<Fuxi::Common::Executor> executor1;
    std::shared_ptr<Fuxi::Common::Executor> executor2;
    std::shared_ptr<Fuxi::Common::Executor> executor3;

    // 系统适配器
    std::shared_ptr<SystemAdapter> systemAdapter;
    
    const int MAX_HEARTBEAT_FAILS = 3;
    std::atomic<int> heartbeatFailCount{0};
    
    bool initModbus();
    
    std::string _ip;
    int _port;
    
    // 添加心跳计时器
    struct HeartbeatTimer {
        std::atomic<std::chrono::steady_clock::time_point> lastHeartbeat;
        std::atomic<bool> heartbeatOK{false};
        static constexpr int HEARTBEAT_TIMEOUT_MS = 3000;
    } heartbeatTimer;
    
    // 添加连接状态检查方法
    bool checkConnection();
    
    struct RegisterData {
        int address;
        std::string dataType;  // "string", "float", "int"
        std::string value;
    };
    
    std::string csvFilePath = "modbus_registers.csv";
    void saveRegisterToCSV(const RegisterData& data);
    void loadRegistersFromCSV();
    void updateRegisterValue(const RegisterData& data);
    
public:
    // 优化心跳方法
    void updateHeartbeat();
}; 
