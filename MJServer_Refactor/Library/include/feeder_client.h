#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <chrono>
#include <functional>
#include "Executor.h"
#include "data_structures.h"
#include "modbus.h"




/**
 * 完整的投料机客户端
 * Phase 2实现，包含完整的通信协议和数据处理
 */
class FeederClient {
public:
    explicit FeederClient(const FeederConfig& config);
    ~FeederClient();
    
    // 连接管理
    bool connect();
    void disconnect();
    bool isConnected() const { return connected; }
    bool isHeartbeatOK() const { return heartbeatOK; }
    
    // 启动和停止服务
    bool start();
    void stop();
    bool isRunning() const { return running; }
    
    // 数据读取接口
    std::shared_ptr<FeederStatus> getFeederStatus();
    bool getBinWeight(int binId, float& weight);
    bool getBinBlockStatus(int binId, bool& isBlocked);
    bool getAllBinWeights(std::vector<float>& weights);
    bool getAllBinBlockStatus(std::vector<bool>& blockStatus);
    
    // 数据写入接口
    bool setBinTargetWeight(int binId, float targetWeight);
    bool setBinMinWeight(int binId, float minWeight);
    bool setAllBinTargetWeights(const std::vector<float>& targetWeights);
    bool setAllBinMinWeights(const std::vector<float>& minWeights);
    bool setBinFeedingStatus(int binId, bool isFeeding);
    bool setAllBinFeedingStatus(const std::vector<bool>& feedingStatus);
    
    // 控制接口
    bool startFeeding(int binId);
    bool stopFeeding(int binId);
    bool stopAllFeeding();
    bool emergencyStop();

    // Modbus协议专用接口（根据通讯协议文档）
    bool readFeederHeartbeat(uint16_t& heartbeat);
    bool readBinDisableStatus(uint16_t& status);
    bool readBinActualWeight(int binId, float& weight);
    bool writeClientHeartbeat(uint16_t heartbeat);
    bool writeFeedingStatus(uint16_t status);
    bool writeBinMinWeight(int binId, float weight);
    bool writeBinTargetWeight(int binId, float weight);
    
    // 配置和状态
    const FeederConfig& getConfig() const { return config; }
    int getFeederId() const { return config.id; }
    std::string getLastError() const { return lastError; }
    
    // 回调设置
    using StatusCallback = std::function<void(std::shared_ptr<FeederStatus>)>;
    using ErrorCallback = std::function<void(const std::string&)>;
    
    void setStatusCallback(StatusCallback callback) { statusCallback = callback; }
    void setErrorCallback(ErrorCallback callback) { errorCallback = callback; }
    
    // 统计信息
    struct Statistics {
        int connectAttempts;
        int successfulReads;
        int failedReads;
        int successfulWrites;
        int failedWrites;
        int heartbeatMisses;
        std::chrono::steady_clock::time_point lastConnectTime;
        std::chrono::steady_clock::time_point lastHeartbeatTime;
        
        Statistics() : connectAttempts(0), successfulReads(0), failedReads(0),
                      successfulWrites(0), failedWrites(0), heartbeatMisses(0) {}
    };
    
    const Statistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = Statistics(); }
    
    
private:
    FeederConfig config;
    modbus_t* modbusContext;
    std::shared_ptr<Fuxi::Common::Executor> executor;
    
    std::atomic<bool> connected;
    std::atomic<bool> running;
    std::atomic<bool> heartbeatOK;
    
    std::string lastError;
    Statistics stats;
    
    // 缓存的状态数据
    std::shared_ptr<FeederStatus> cachedStatus;
    std::mutex statusMutex;
    std::chrono::steady_clock::time_point lastStatusUpdate;
    
    // 心跳相关
    uint16_t heartbeatCounter;
    std::chrono::steady_clock::time_point lastHeartbeatSend;
    std::chrono::steady_clock::time_point lastHeartbeatReceive;
    
    // 回调函数
    StatusCallback statusCallback;
    ErrorCallback errorCallback;
    
    // 内部工作线程
    void heartbeatLoop();
    void statusUpdateLoop();
    
    // 内部辅助方法
    bool reconnect();
    bool updateStatus();
    bool sendHeartbeat();
    bool checkHeartbeat();
    
    // Modbus操作封装
    bool readRegisters(int address, int count, uint16_t* buffer);
    bool writeRegisters(int address, int count, const uint16_t* buffer);
    bool readSingleRegister(int address, uint16_t& value);
    bool writeSingleRegister(int address, uint16_t value);
    bool readFloatRegisters(int address, float& value);
    bool writeFloatRegisters(int address, float value);
    
    // 数据转换
    float registersToFloat(uint16_t reg1, uint16_t reg2);
    void floatToRegisters(float value, uint16_t& reg1, uint16_t& reg2);
    
    // 错误处理
    void setError(const std::string& error);
    void clearError();
    void notifyError(const std::string& error);
    void notifyStatus(std::shared_ptr<FeederStatus> status);
    
    // 验证方法
    bool validateBinId(int binId) const;
    bool validateWeight(float weight) const;
};
