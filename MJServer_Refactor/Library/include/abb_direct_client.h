#pragma once

#include <string>
#include <memory>
#include <atomic>
#include <thread>
#include <mutex>
#include <queue>
#include <chrono>
#include <functional>
#include "data_structures.h"
#include "abb_protocol.h"

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
using SOCKET_TYPE = SOCKET;
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
using SOCKET_TYPE = int;
#define INVALID_SOCKET -1
#define SOCKET_ERROR -1
#endif

// 前向声明
namespace Fuxi {
namespace Common {
class Executor;
}
}

/**
 * ABB机器人直连服务器
 * 软件作为服务器，机器人作为客户端连接
 */
class ABBDirectClient {
public:
    explicit ABBDirectClient(const ABBConfig& config);
    ~ABBDirectClient();
    
    // 服务器管理
    bool startServer();
    void stopServer();
    bool isServerRunning() const { return serverRunning; }

    // 客户端连接管理
    bool waitForRobotConnection(int timeoutMs = 30000);
    void disconnectRobot();
    bool isRobotConnected() const { return robotConnected; }
    bool isHeartbeatOK() const { return heartbeatOK; }
    
    // 启动和停止服务
    bool start();
    void stop();
    bool isRunning() const { return running; }
    
    // 任务管理接口
    bool sendTask(const TaskInfo& task);
    bool cancelTask(const std::string& taskId);
    bool cancelAllTasks();
    std::shared_ptr<TaskStatus> getTaskStatus(const std::string& taskId);
    std::shared_ptr<TaskStatus> getCurrentTaskStatus();
    
    // 控制接口
    bool emergencyStop();
    bool reset();
    bool pause();
    bool resume();

    // 扩展控制接口
    bool sendCommand(const std::string& command, const std::string& parameter = "");
    
    // 状态查询接口
    ABBProtocol::RobotStatus getRobotStatus();
    bool isRobotReady();
    bool hasError();
    
    // 配置和错误信息
    const ABBConfig& getConfig() const { return config; }
    std::string getLastError() const { return lastError; }
    
    // 回调设置
    using TaskStatusCallback = std::function<void(std::shared_ptr<TaskStatus>)>;
    using RobotStatusCallback = std::function<void(ABBProtocol::RobotStatus)>;
    using ErrorCallback = std::function<void(const std::string&)>;
    using MessageCallback = std::function<void(const std::string&)>;
    
    void setTaskStatusCallback(TaskStatusCallback callback) { taskStatusCallback = callback; }
    void setRobotStatusCallback(RobotStatusCallback callback) { robotStatusCallback = callback; }
    void setErrorCallback(ErrorCallback callback) { errorCallback = callback; }
    void setMessageCallback(MessageCallback callback) { messageCallback = callback; }
    
    // 统计信息
    struct Statistics {
        int connectAttempts;
        int messagesSent;
        int messagesReceived;
        int tasksCompleted;
        int tasksFailed;
        int heartbeatMisses;
        std::chrono::steady_clock::time_point lastConnectTime;
        std::chrono::steady_clock::time_point lastMessageTime;
        
        Statistics() : connectAttempts(0), messagesSent(0), messagesReceived(0),
                      tasksCompleted(0), tasksFailed(0), heartbeatMisses(0) {}
    };
    
    const Statistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = Statistics(); }
    
private:
    ABBConfig config;
    SOCKET_TYPE serverSocket;      // 服务器监听Socket
    SOCKET_TYPE clientSocket;      // 机器人客户端Socket
    std::shared_ptr<Fuxi::Common::Executor> executor;

    std::atomic<bool> serverRunning;   // 服务器是否运行
    std::atomic<bool> robotConnected;  // 机器人是否连接
    std::atomic<bool> running;
    std::atomic<bool> heartbeatOK;
    
    std::string lastError;
    Statistics stats;
    
    // 当前任务状态
    std::shared_ptr<TaskStatus> currentTask;
    std::mutex taskMutex;
    
    // 机器人状态
    ABBProtocol::RobotStatus robotStatus;
    std::mutex statusMutex;
    std::chrono::steady_clock::time_point lastStatusUpdate;
    
    // 消息队列
    std::queue<std::string> sendQueue;
    std::queue<std::string> receiveQueue;
    std::mutex sendQueueMutex;
    std::mutex receiveQueueMutex;
    
    // 心跳相关
    std::chrono::steady_clock::time_point lastHeartbeatSend;
    std::chrono::steady_clock::time_point lastHeartbeatReceive;
    
    // 回调函数
    TaskStatusCallback taskStatusCallback;
    RobotStatusCallback robotStatusCallback;
    ErrorCallback errorCallback;
    MessageCallback messageCallback;
    
    // 内部工作线程
    void heartbeatLoop();
    void sendLoop();
    void receiveLoop();
    void statusUpdateLoop();
    
    // 内部辅助方法
    bool sendMessage(const std::string& message);
    bool receiveMessage(std::string& message, int timeoutMs = 1000);
    bool processReceivedMessage(const std::string& message);

    // 服务器Socket操作
    bool initializeServerSocket();
    bool acceptClientConnection();
    void cleanupSockets();
    int sendData(const char* data, int length);
    int receiveData(char* buffer, int bufferSize, int timeoutMs);
    
    // 协议处理
    bool sendHeartbeat();
    bool sendStatusQuery();
    bool processTaskResponse(const ABBProtocol::ABBResponse& response);
    bool processStatusResponse(const ABBProtocol::ABBResponse& response);
    
    // 错误处理
    void setError(const std::string& error);
    void clearError();
    void notifyError(const std::string& error);
    void notifyTaskStatus(std::shared_ptr<TaskStatus> status);
    void notifyRobotStatus(ABBProtocol::RobotStatus status);
    void notifyMessage(const std::string& message);
    
    // 平台相关的socket操作
    void closeSocket();
    int getLastSocketError();
    std::string getSocketErrorString(int errorCode);
    
    // 任务管理
    std::string generateTaskId();
    void updateTaskStatus(const std::string& taskId, TaskStatus::Status status, const std::string& message = "");
};
