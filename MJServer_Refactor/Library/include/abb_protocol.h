#pragma once

#include <string>
#include <vector>
#include <sstream>
#include <iomanip>
#include "data_structures.h"

namespace ABBProtocol {

// ABB命令类型枚举
enum class CommandType {
    TASK_START = 1,         // 开始任务
    TASK_STOP = 2,          // 停止任务
    GET_STATUS = 3,         // 获取状态
    EMERGENCY_STOP = 4,     // 紧急停止
    RESET = 5,              // 复位
    SET_POSITION = 6,       // 设置位置
    GET_POSITION = 7,       // 获取位置
    HEARTBEAT = 8           // 心跳
};

// ABB响应类型枚举
enum class ResponseType {
    SUCCESS = 0,            // 成功
    RESPONSE_ERROR = 1,     // 错误
    BUSY = 2,               // 忙碌
    TIMEOUT = 3,            // 超时
    INVALID_COMMAND = 4,    // 无效命令
    SYSTEM_ERROR = 5        // 系统错误
};

// ABB机器人状态枚举
enum class RobotStatus {
    IDLE = 0,               // 空闲
    RUNNING = 1,            // 运行中
    ROBOT_ERROR = 2,        // 错误
    EMERGENCY_STOP = 3,     // 紧急停止
    MAINTENANCE = 4         // 维护模式
};

// ABB消息结构
struct ABBMessage {
    CommandType command;
    std::vector<double> parameters;
    std::string data;
    std::string timestamp;
    
    ABBMessage() : command(CommandType::HEARTBEAT) {}
    
    ABBMessage(CommandType cmd, const std::vector<double>& params = {})
        : command(cmd), parameters(params) {}
};

// ABB响应结构
struct ABBResponse {
    ResponseType type;
    std::string message;
    std::vector<double> data;
    std::string timestamp;
    
    ABBResponse() : type(ResponseType::SUCCESS) {}
    
    ABBResponse(ResponseType t, const std::string& msg = "")
        : type(t), message(msg) {}
};

// 协议工具类 - 简化版本，类似RoboticLaserMarking
class ProtocolHelper {
public:
    // 构建任务命令 - 只包含投料机和料仓信息，不包含重量
    static std::string buildTaskCommand(const TaskInfo& task) {
        std::ostringstream oss;
        oss << static_cast<int>(CommandType::TASK_START) << ","
            << task.feederId << ","
            << task.binId << "#";
        return oss.str();
    }

    // 构建简化命令 - 最大长度不超过80字符
    static std::string buildCommand(CommandType cmdType, const std::vector<double>& params = {}) {
        std::ostringstream oss;
        oss << static_cast<int>(cmdType);

        for (size_t i = 0; i < params.size() && i < 5; ++i) {  // 最多5个参数
            oss << "," << std::fixed << std::setprecision(1) << params[i];
        }

        oss << "#";

        // 确保不超过80字符
        std::string result = oss.str();
        if (result.length() > 80) {
            result = result.substr(0, 79) + "#";
        }

        return result;
    }
    
    // 解析响应 - 简化版本，类似RoboticLaserMarking
    static ABBResponse parseResponse(const std::string& response) {
        ABBResponse result;

        if (response.empty()) {
            result.type = ResponseType::TIMEOUT;
            result.message = "Empty response";
            return result;
        }

        // 查找结束符 '#'
        size_t endPos = response.find('#');
        if (endPos == std::string::npos) {
            result.type = ResponseType::INVALID_COMMAND;
            result.message = "Invalid message format";
            return result;
        }

        std::string msgContent = response.substr(0, endPos);

        // 简化的响应解析 - 第一个数字为状态码
        if (!msgContent.empty() && std::isdigit(msgContent[0])) {
            int statusCode = msgContent[0] - '0';
            switch (statusCode) {
                case 0:
                    result.type = ResponseType::SUCCESS;
                    result.message = "OK";
                    break;
                case 1:
                    result.type = ResponseType::RESPONSE_ERROR;
                    result.message = "ERROR";
                    break;
                case 2:
                    result.type = ResponseType::BUSY;
                    result.message = "BUSY";
                    break;
                default:
                    result.type = ResponseType::INVALID_COMMAND;
                    result.message = "Unknown status: " + std::to_string(statusCode);
                    break;
            }
        } else {
            result.type = ResponseType::INVALID_COMMAND;
            result.message = "Invalid response format";
        }

        return result;
    }
    
    // 构建心跳命令 - 简化格式: "8#"
    static std::string buildHeartbeat() {
        return std::to_string(static_cast<int>(CommandType::HEARTBEAT)) + "#";
    }

    // 构建状态查询命令 - 简化格式: "3#"
    static std::string buildStatusQuery() {
        return std::to_string(static_cast<int>(CommandType::GET_STATUS)) + "#";
    }

    // 构建停止命令 - 简化格式: "2#"
    static std::string buildStopCommand() {
        return std::to_string(static_cast<int>(CommandType::TASK_STOP)) + "#";
    }

    // 构建紧急停止命令 - 简化格式: "4#"
    static std::string buildEmergencyStop() {
        return std::to_string(static_cast<int>(CommandType::EMERGENCY_STOP)) + "#";
    }

    // 构建复位命令 - 简化格式: "5#"
    static std::string buildResetCommand() {
        return std::to_string(static_cast<int>(CommandType::RESET)) + "#";
    }
    
    // 验证命令格式
    static bool validateCommand(const std::string& command) {
        if (command.empty()) return false;
        if (command.back() != '#') return false;
        if (command.find(',') == std::string::npos) return false;
        return true;
    }
    
    // 提取命令类型
    static CommandType extractCommandType(const std::string& command) {
        if (command.length() < 2) return CommandType::HEARTBEAT;
        
        std::string cmdStr = command.substr(0, 2);
        try {
            int cmdInt = std::stoi(cmdStr);
            return static_cast<CommandType>(cmdInt);
        } catch (...) {
            return CommandType::HEARTBEAT;
        }
    }
    
    // 提取参数
    static std::vector<double> extractParameters(const std::string& command) {
        std::vector<double> params;
        
        size_t start = command.find(',');
        if (start == std::string::npos) return params;
        
        start++; // 跳过第一个逗号
        size_t end = command.find('#');
        if (end == std::string::npos) return params;
        
        std::string paramStr = command.substr(start, end - start);
        std::istringstream iss(paramStr);
        std::string token;
        
        while (std::getline(iss, token, ',')) {
            if (!token.empty()) {
                try {
                    params.push_back(std::stod(token));
                } catch (...) {
                    // 忽略无法转换的参数
                }
            }
        }
        
        return params;
    }
};

// 协议常量 - 适配ABB Socket限制
namespace Constants {
    constexpr char COMMAND_TERMINATOR = '#';
    constexpr char PARAMETER_SEPARATOR = ',';
    constexpr int MAX_COMMAND_LENGTH = 80;      // ABB Socket限制
    constexpr int MAX_RESPONSE_LENGTH = 80;     // ABB Socket限制
    constexpr int COMMAND_TIMEOUT_MS = 3000;    // 缩短超时时间
    constexpr int HEARTBEAT_INTERVAL_MS = 2000; // 心跳间隔
}

} // namespace ABBProtocol
