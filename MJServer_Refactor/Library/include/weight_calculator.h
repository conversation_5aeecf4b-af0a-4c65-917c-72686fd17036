#pragma once

#include <chrono>
#include <string>
#include <vector>
#include <map>
#include <memory>
#include <functional>
#include "data_structures.h"

/**
 * 重量计算器
 * 负责投料重量的计算、策略制定和安全检查
 */
class WeightCalculator {
public:
    WeightCalculator();
    ~WeightCalculator();
    
    // 计算策略枚举
    enum class CalculationStrategy {
        SIMPLE,         // 简单策略：直接计算差值
        BALANCED,       // 平衡策略：考虑多料仓负载均衡
        OPTIMIZED,      // 优化策略：最小化投料次数
        SAFETY_FIRST    // 安全优先：保守的投料策略
    };
    
    // 投料建议结构
    struct FeedingSuggestion {
        int feederId;
        int binId;
        float suggestedWeight;      // 建议投料重量
        float currentWeight;        // 当前重量
        float targetWeight;         // 目标重量
        float minWeight;           // 最小投料量
        float maxWeight;           // 最大投料量
        float priority;            // 优先级 (0.0-1.0)
        std::string reason;        // 建议原因
        bool isSafe;              // 是否安全
        
        FeedingSuggestion() : feederId(0), binId(0), suggestedWeight(0.0f),
                             currentWeight(0.0f), targetWeight(0.0f),
                             minWeight(0.0f), maxWeight(0.0f),
                             priority(0.0f), isSafe(true) {}
    };
    
    // 计算参数结构
    struct CalculationParams {
        CalculationStrategy strategy;
        float safetyMargin;         // 安全余量 (0.0-1.0)
        float minFeedingAmount;     // 最小投料量
        float maxFeedingAmount;     // 最大投料量
        float toleranceRange;       // 容差范围
        bool enableLoadBalancing;   // 启用负载均衡
        bool enableSafetyCheck;     // 启用安全检查
        
        CalculationParams() : strategy(CalculationStrategy::SIMPLE),
                             safetyMargin(0.1f), minFeedingAmount(10.0f),
                             maxFeedingAmount(1000.0f), toleranceRange(5.0f),
                             enableLoadBalancing(true), enableSafetyCheck(true) {}
    };
    
    // 主要计算接口
    std::vector<FeedingSuggestion> calculateFeedingSuggestions(
        const std::vector<std::shared_ptr<FeederStatus>>& feederStatus,
        const std::map<int, std::map<int, float>>& targetWeights);
    
    FeedingSuggestion calculateSingleBinFeeding(
        int feederId, int binId,
        float currentWeight, float targetWeight,
        float minWeight = 0.0f, float maxWeight = 1000.0f);
    
    // 重量验证
    bool validateWeight(float weight, float minLimit = 0.0f, float maxLimit = 10000.0f);
    bool validateFeedingAmount(float amount, float currentWeight, float targetWeight);
    bool isSafeToFeed(int feederId, int binId, float amount,
                      const std::shared_ptr<FeederStatus>& status);
    
    // 策略管理
    void setCalculationStrategy(CalculationStrategy strategy) { params.strategy = strategy; }
    CalculationStrategy getCalculationStrategy() const { return params.strategy; }
    void setCalculationParams(const CalculationParams& newParams) { params = newParams; }
    const CalculationParams& getCalculationParams() const { return params; }
    
    // 负载均衡
    std::vector<FeedingSuggestion> balanceFeederLoads(
        const std::vector<std::shared_ptr<FeederStatus>>& feederStatus);
    
    float calculateFeederLoad(const std::shared_ptr<FeederStatus>& status);
    std::vector<int> getOptimalFeedingOrder(
        const std::vector<FeedingSuggestion>& suggestions);
    
    // 安全检查
    struct SafetyCheck {
        bool passed;
        std::string message;
        float riskLevel;    // 0.0-1.0, 0为无风险
        
        SafetyCheck() : passed(true), riskLevel(0.0f) {}
    };
    
    SafetyCheck performSafetyCheck(const FeedingSuggestion& suggestion,
                                  const std::shared_ptr<FeederStatus>& status);
    
    std::vector<SafetyCheck> performBatchSafetyCheck(
        const std::vector<FeedingSuggestion>& suggestions,
        const std::vector<std::shared_ptr<FeederStatus>>& feederStatus);
    
    // 统计和分析
    struct CalculationStatistics {
        int totalCalculations;
        int successfulSuggestions;
        int rejectedSuggestions;
        int safetyViolations;
        float averageAccuracy;
        std::chrono::steady_clock::time_point lastCalculationTime;
        
        CalculationStatistics() : totalCalculations(0), successfulSuggestions(0),
                                 rejectedSuggestions(0), safetyViolations(0),
                                 averageAccuracy(0.0f) {}
    };
    
    const CalculationStatistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = CalculationStatistics(); }
    
    // 历史记录
    struct CalculationRecord {
        std::chrono::steady_clock::time_point timestamp;
        FeedingSuggestion suggestion;
        bool wasExecuted;
        float actualResult;
        std::string notes;
    };
    
    void recordCalculation(const FeedingSuggestion& suggestion, bool executed = false);
    std::vector<CalculationRecord> getCalculationHistory(int maxRecords = 100);
    void clearCalculationHistory();
    
    // 配置和校准
    bool loadCalibrationData(const std::string& filename);
    bool saveCalibrationData(const std::string& filename);
    void calibrateFeeder(int feederId, const std::vector<std::pair<float, float>>& actualVsExpected);
    
private:
    CalculationParams params;
    CalculationStatistics stats;
    std::vector<CalculationRecord> history;
    std::map<int, float> feederCalibrationFactors;  // 投料机校准系数
    
    // 内部计算方法
    FeedingSuggestion calculateSimpleStrategy(int feederId, int binId,
                                            float currentWeight, float targetWeight,
                                            float minWeight, float maxWeight);
    
    FeedingSuggestion calculateBalancedStrategy(int feederId, int binId,
                                              float currentWeight, float targetWeight,
                                              float minWeight, float maxWeight,
                                              const std::vector<std::shared_ptr<FeederStatus>>& allStatus);
    
    FeedingSuggestion calculateOptimizedStrategy(int feederId, int binId,
                                               float currentWeight, float targetWeight,
                                               float minWeight, float maxWeight);
    
    FeedingSuggestion calculateSafetyFirstStrategy(int feederId, int binId,
                                                 float currentWeight, float targetWeight,
                                                 float minWeight, float maxWeight);
    
    // 辅助计算方法
    float applyCalibrationFactor(int feederId, float weight);
    float calculatePriority(float weightDifference, float totalCapacity);
    bool isWithinTolerance(float current, float target, float tolerance);
    float clampWeight(float weight, float minWeight, float maxWeight);
    
    // 安全检查辅助方法
    bool checkWeightLimits(float weight, float minLimit, float maxLimit);
    bool checkFeederCapacity(int feederId, float additionalWeight,
                           const std::shared_ptr<FeederStatus>& status);
    bool checkBinStatus(int feederId, int binId, const std::shared_ptr<FeederStatus>& status);
    
    // 统计更新
    void updateStatistics(const FeedingSuggestion& suggestion, bool successful);
};
