#pragma once

#include <chrono>
#include <string>
#include <memory>
#include <map>
#include <functional>
#include "data_structures.h"
#include <json/json.h>

/**
 * 配置管理器
 * 负责系统配置的加载、保存和管理
 */
class ConfigManager {
public:
    ConfigManager();
    ~ConfigManager();
    
    // 配置文件操作
    bool loadConfig(const std::string& filename = "config/direct_mode.json");
    bool saveConfig(const std::string& filename = "config/direct_mode.json");
    bool reloadConfig();
    
    // 系统配置
    const SystemConfig& getSystemConfig() const { return systemConfig; }
    void setSystemConfig(const SystemConfig& config);
    
    // 投料机配置
    std::vector<FeederConfig> getFeederConfigs() const { return systemConfig.feeders; }
    bool addFeederConfig(const FeederConfig& config);
    bool updateFeederConfig(int feederId, const FeederConfig& config);
    bool removeFeederConfig(int feederId);
    FeederConfig getFeederConfig(int feederId) const;
    
    // ABB配置
    ABBConfig getABBConfig() const { return systemConfig.abbRobot; }
    void setABBConfig(const ABBConfig& config);
    
    // 模式配置
    bool isDirectModeEnabled() const { return systemConfig.enableDirectMode; }
    void setDirectModeEnabled(bool enabled);
    
    // 配置验证
    bool validateConfig(const SystemConfig& config, std::string& errorMessage);
    bool validateFeederConfig(const FeederConfig& config, std::string& errorMessage);
    bool validateABBConfig(const ABBConfig& config, std::string& errorMessage);
    
    // 默认配置
    SystemConfig createDefaultConfig();
    FeederConfig createDefaultFeederConfig(int feederId) const;
    ABBConfig createDefaultABBConfig();

    // MES数据配置
    const MESDataConfig& getMESConfig() const { return systemConfig.mesConfig; }
    void setMESConfig(const MESDataConfig& config);

    // MES区域数据管理
    bool saveMESAreaData(char areaId, std::shared_ptr<AreaData> data);
    std::shared_ptr<AreaData> loadMESAreaData(char areaId);
    std::map<char, std::shared_ptr<AreaData>> loadAllMESAreaData();

    // MES投料机数据管理
    bool saveMESFeederData(int feederId, std::shared_ptr<FeederData> data);
    std::shared_ptr<FeederData> loadMESFeederData(int feederId);
    std::map<int, std::shared_ptr<FeederData>> loadAllMESFeederData();

    // MES发送数据管理
    bool saveMESSendData(std::shared_ptr<SendData> data);
    std::vector<std::shared_ptr<SendData>> loadMESSendDataHistory(int maxCount = 100);
    bool clearMESSendDataHistory();

    // MES数据批量操作
    bool saveMESDataToFile(const std::string& filename = "config/mes_data.json");
    bool loadMESDataFromFile(const std::string& filename = "config/mes_data.json");

    // 配置变更通知
    using ConfigChangeCallback = std::function<void(const std::string& section, const std::string& key)>;
    void setConfigChangeCallback(ConfigChangeCallback callback) { configChangeCallback = callback; }
    
    // 配置备份和恢复
    bool backupConfig(const std::string& backupName = "");
    bool restoreConfig(const std::string& backupName);
    std::vector<std::string> listBackups();
    bool deleteBackup(const std::string& backupName);
    
    // 配置导入导出
    bool exportConfig(const std::string& filename, const std::string& format = "json");
    bool importConfig(const std::string& filename, const std::string& format = "json");
    
    // 运行时配置
    void setRuntimeValue(const std::string& key, const std::string& value);
    std::string getRuntimeValue(const std::string& key, const std::string& defaultValue = "");
    void clearRuntimeValues();
    
    // 配置监控
    struct ConfigStatistics {
        int loadCount;
        int saveCount;
        int errorCount;
        std::chrono::steady_clock::time_point lastLoadTime;
        std::chrono::steady_clock::time_point lastSaveTime;
        std::string lastError;
        
        ConfigStatistics() : loadCount(0), saveCount(0), errorCount(0) {}
    };
    
    const ConfigStatistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = ConfigStatistics(); }
    
private:
    SystemConfig systemConfig;
    std::string currentConfigFile;
    std::map<std::string, std::string> runtimeValues;
    ConfigStatistics stats;
    ConfigChangeCallback configChangeCallback;
    
    // INI处理
    bool loadFromIni(const std::string& filename);
    bool saveToIni(const std::string& filename);

    // JSON处理（保留用于兼容性）
    bool loadFromJson(const std::string& filename);
    bool saveToJson(const std::string& filename);
    std::string configToJson(const SystemConfig& config);
    SystemConfig jsonToConfig(const std::string& jsonStr);

    // MES数据JSON处理
    std::string mesDataToJson(const MESDataConfig& mesConfig);
    MESDataConfig jsonToMESData(const std::string& jsonStr);

    // XML处理（可选）
    bool loadFromXml(const std::string& filename);
    bool saveToXml(const std::string& filename);
    
    // 配置文件路径管理
    std::string getConfigPath(const std::string& filename);
    std::string getBackupPath(const std::string& backupName);
    bool ensureConfigDirectory();
    
    // 错误处理
    void handleError(const std::string& error);
    void notifyConfigChange(const std::string& section, const std::string& key);
    
    // 验证辅助方法
    bool isValidIP(const std::string& ip);
    bool isValidPort(int port);
    bool isValidFeederId(int feederId);
};
