#pragma once

#include <string>
#include <memory>
#include <vector>
#include <map>
#include <functional>
#include <chrono>
#include <mutex>
#include <queue>
#include "data_structures.h"

// 前向声明
class FeederClient;
class ABBDirectClient;
class FeederManager;

/**
 * 系统适配器
 * 用于保持与原有系统接口的兼容性
 * 将新的直连组件适配为原有的接口格式
 */
class SystemAdapter {
public:
    SystemAdapter();
    ~SystemAdapter();
    
    // 初始化和配置
    bool initialize(const SystemConfig& config);
    bool start();
    void stop();
    bool isRunning() const { return running; }
    
    // 兼容原有ABBPLCClient接口的方法
    std::shared_ptr<TaskInfo> findNextTask();
    bool startTask(std::shared_ptr<TaskInfo> task);
    std::shared_ptr<TaskStatus> getTaskStatus() const;
    
    // 投料机状态接口（兼容原有格式）
    std::vector<std::shared_ptr<FeederStatus>> getFeederStatus();
    bool isSystemAuto() const;
    bool hasError() const;
    bool isAreaEnabled(char areaId) const;
    bool isFeederEnabled(int feederId) const;
    bool isBinEnabled(int feederId, int binId) const;
    bool isHeartbeatOK() const;
    
    // 区域数据接口（兼容MES服务器格式）
    std::map<char, std::shared_ptr<AreaData>> getAllPLCAreaData();
    
    // 控制接口
    bool emergencyStop();
    bool reset();
    bool setSystemMode(bool autoMode);

    // 投料控制接口
    bool startFeeding(int feederId, int binId);
    bool stopFeeding(int feederId, int binId);
    bool stopAllFeeding();
    bool setBinTargetWeight(int feederId, int binId, float targetWeight);
    bool getBinWeight(int feederId, int binId, float& weight);
    
    // 配置管理
    const SystemConfig& getConfig() const { return config; }
    void updateConfig(const SystemConfig& newConfig);
    
    // 回调设置（保持与原系统兼容）
    using TaskLogCallback = std::function<void(const std::string&)>;
    using StatusCallback = std::function<void(const std::string&)>;
    using FeederStatusCallback = std::function<void(const std::vector<std::shared_ptr<FeederStatus>>&)>;
    using AreaStatusCallback = std::function<void(const std::map<char, std::shared_ptr<AreaData>>&)>;
    
    void setTaskLogCallback(TaskLogCallback callback) { taskLogCallback = callback; }
    void setStatusCallback(StatusCallback callback) { statusCallback = callback; }
    void setFeederStatusCallback(FeederStatusCallback callback) { feederStatusCallback = callback; }
    void setAreaStatusCallback(AreaStatusCallback callback) { areaStatusCallback = callback; }
    
    // 统计和诊断信息
    struct AdapterStatistics {
        int tasksProcessed;
        int feederUpdates;
        int areaUpdates;
        int errors;
        std::chrono::steady_clock::time_point startTime;
        
        AdapterStatistics() : tasksProcessed(0), feederUpdates(0), areaUpdates(0), errors(0) {}
    };
    
    const AdapterStatistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = AdapterStatistics(); }
    
private:
    SystemConfig config;
    bool running;
    bool systemAuto;
    
    // 核心组件
    std::shared_ptr<FeederManager> feederManager;
    std::shared_ptr<ABBDirectClient> abbClient;
    
    // 缓存数据
    std::shared_ptr<TaskStatus> currentTaskStatus;
    std::vector<std::shared_ptr<FeederStatus>> cachedFeederStatus;
    std::map<char, std::shared_ptr<AreaData>> cachedAreaData;
    
    // 同步控制
    mutable std::mutex taskMutex;
    mutable std::mutex feederMutex;
    mutable std::mutex areaMutex;
    
    // 统计信息
    AdapterStatistics stats;
    
    // 回调函数
    TaskLogCallback taskLogCallback;
    StatusCallback statusCallback;
    FeederStatusCallback feederStatusCallback;
    AreaStatusCallback areaStatusCallback;
    
    // 内部方法
    void initializeAreaData();
    void updateFeederStatusCache();
    void updateAreaDataCache();

    // 区域数据更新辅助方法
    void updateAreaAvailabilityFromFeeder(std::shared_ptr<FeederStatus> feederStatus);
    void applyBlockConfigToAreas();
    void validateAndCleanAreaData();
    
    // 数据转换方法
    std::shared_ptr<FeederStatus> convertToFeederStatus(int feederId);
    std::shared_ptr<AreaData> convertToAreaData(char areaId);
    
    // 任务管理
    std::queue<std::shared_ptr<TaskInfo>> taskQueue;
    std::mutex taskQueueMutex;
    
    // 错误处理
    void handleError(const std::string& error);
    void notifyTaskLog(const std::string& message);
    void notifyStatus(const std::string& status);
    
    // 兼容性辅助方法
    bool validateTaskInfo(std::shared_ptr<TaskInfo> task);
    void updateTaskProgress(std::shared_ptr<TaskInfo> task);

    // 任务进度更新辅助方法
    void updateProgressByExecutionPhase(std::shared_ptr<TaskInfo> task);
    void updateTaskStatistics();
    void checkTaskCompletion(std::shared_ptr<TaskInfo> task);
    std::string getCurrentTimeString();

    // 任务管理辅助方法
    void generateTasksFromFeederStatus();
    std::shared_ptr<TaskInfo> findBestMatchingTask();
    void removeTaskFromQueue(const std::string& taskId);
    int calculateTaskPriority(std::shared_ptr<TaskInfo> task);

    // 投料流程控制方法
    bool executeCompleteFeederTask(std::shared_ptr<TaskInfo> task);
    bool waitForRobotAtPosition(std::shared_ptr<TaskInfo> task);
    bool startFeedingProcess(std::shared_ptr<TaskInfo> task);
    bool monitorFeedingWeight(std::shared_ptr<TaskInfo> task);
    bool completeFeedingTask(std::shared_ptr<TaskInfo> task);
    bool checkBucketWeightAndScheduleNext(std::shared_ptr<TaskInfo> task);

    // 辅助方法
    std::string generateNextBatchNumber(const std::string& currentBatch);
};
