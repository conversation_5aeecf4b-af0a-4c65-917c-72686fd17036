#pragma once

#include <string>
#include <memory>
#include <vector>
#include <queue>
#include <map>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <functional>
#include <chrono>
#include "data_structures.h"

// 前向声明
class FeederClient;
class ABBDirectClient;

/**
 * 连接池管理器
 * 优化连接资源的使用和管理
 */
class ConnectionPool {
public:
    ConnectionPool();
    ~ConnectionPool();
    
    // 连接池配置
    struct PoolConfig {
        int maxConnections;         // 最大连接数
        int minConnections;         // 最小连接数
        int connectionTimeout;      // 连接超时时间(ms)
        int idleTimeout;           // 空闲超时时间(ms)
        int maxRetries;            // 最大重试次数
        int retryInterval;         // 重试间隔(ms)
        bool enableHealthCheck;    // 启用健康检查
        int healthCheckInterval;   // 健康检查间隔(ms)
        
        PoolConfig() : maxConnections(20), minConnections(5),
                      connectionTimeout(5000), idleTimeout(300000),
                      maxRetries(3), retryInterval(1000),
                      enableHealthCheck(true), healthCheckInterval(30000) {}
    };
    
    // 连接状态
    enum class ConnectionState {
        IDLE,           // 空闲
        ACTIVE,         // 活跃
        CONNECTING,     // 连接中
        DISCONNECTED,   // 已断开
        ERROR           // 错误状态
    };
    
    // 连接信息
    struct ConnectionInfo {
        std::string id;
        std::string type;           // "feeder" 或 "abb"
        std::string target;         // 目标地址
        ConnectionState state;
        std::chrono::steady_clock::time_point createTime;
        std::chrono::steady_clock::time_point lastUsedTime;
        std::chrono::steady_clock::time_point lastHealthCheck;
        int useCount;
        int errorCount;
        std::string lastError;
        
        ConnectionInfo() : state(ConnectionState::DISCONNECTED),
                          useCount(0), errorCount(0) {}
    };
    
    // 初始化和配置
    bool initialize(const PoolConfig& config);
    void setConfig(const PoolConfig& config) { this->config = config; }
    const PoolConfig& getConfig() const { return config; }
    
    // 连接池控制
    bool start();
    void stop();
    bool isRunning() const { return running; }
    
    // 投料机连接管理
    std::shared_ptr<FeederClient> getFeederConnection(const FeederConfig& feederConfig);
    void releaseFeederConnection(std::shared_ptr<FeederClient> client);
    bool preCreateFeederConnections(const std::vector<FeederConfig>& configs);
    
    // ABB连接管理
    std::shared_ptr<ABBDirectClient> getABBConnection(const ABBConfig& abbConfig);
    void releaseABBConnection(std::shared_ptr<ABBDirectClient> client);
    bool preCreateABBConnection(const ABBConfig& config);
    
    // 连接状态查询
    std::vector<ConnectionInfo> getAllConnections();
    ConnectionInfo getConnectionInfo(const std::string& connectionId);
    int getActiveConnectionCount() const;
    int getIdleConnectionCount() const;
    int getTotalConnectionCount() const;
    
    // 连接健康检查
    bool performHealthCheck();
    bool checkConnection(const std::string& connectionId);
    void removeUnhealthyConnections();
    
    // 统计信息
    struct PoolStatistics {
        int totalCreated;
        int totalDestroyed;
        int totalRequests;
        int successfulRequests;
        int failedRequests;
        int currentActive;
        int currentIdle;
        int healthCheckCount;
        int reconnectCount;
        std::chrono::steady_clock::time_point startTime;
        
        PoolStatistics() : totalCreated(0), totalDestroyed(0),
                          totalRequests(0), successfulRequests(0), failedRequests(0),
                          currentActive(0), currentIdle(0),
                          healthCheckCount(0), reconnectCount(0) {}
    };
    
    const PoolStatistics& getStatistics() const { return stats; }
    void resetStatistics() { stats = PoolStatistics(); }
    
    // 回调设置
    using ConnectionEventCallback = std::function<void(const std::string& connectionId, const std::string& event)>;
    using ErrorCallback = std::function<void(const std::string& error)>;
    
    void setConnectionEventCallback(ConnectionEventCallback callback) { connectionEventCallback = callback; }
    void setErrorCallback(ErrorCallback callback) { errorCallback = callback; }
    
    // 连接池优化
    void optimizePool();
    void cleanupIdleConnections();
    void balanceConnections();
    
private:
    PoolConfig config;
    std::atomic<bool> running;
    PoolStatistics stats;
    
    // 连接存储
    struct PooledConnection {
        std::string id;
        std::string type;
        std::string target;
        std::shared_ptr<void> connection;  // FeederClient* 或 ABBDirectClient*
        ConnectionInfo info;
        std::mutex connectionMutex;
        
        PooledConnection() = default;
        PooledConnection(const PooledConnection&) = delete;
        PooledConnection& operator=(const PooledConnection&) = delete;
    };
    
    std::map<std::string, std::unique_ptr<PooledConnection>> connections;
    std::queue<std::string> idleConnections;
    std::map<std::string, std::string> activeConnections;  // connectionId -> userId
    
    // 同步控制
    mutable std::mutex poolMutex;
    mutable std::mutex idleMutex;
    mutable std::mutex activeMutex;
    std::condition_variable connectionAvailable;
    
    // 回调函数
    ConnectionEventCallback connectionEventCallback;
    ErrorCallback errorCallback;
    
    // 工作线程
    std::shared_ptr<std::thread> healthCheckThread;
    std::shared_ptr<std::thread> cleanupThread;
    
    void healthCheckLoop();
    void cleanupLoop();
    
    // 连接创建和销毁
    std::string createFeederConnection(const FeederConfig& config);
    std::string createABBConnection(const ABBConfig& config);
    bool destroyConnection(const std::string& connectionId);
    
    // 连接管理辅助方法
    std::string generateConnectionId(const std::string& type, const std::string& target);
    std::string getConnectionTarget(const FeederConfig& config);
    std::string getConnectionTarget(const ABBConfig& config);
    
    bool isConnectionHealthy(const std::string& connectionId);
    bool reconnectConnection(const std::string& connectionId);
    void updateConnectionInfo(const std::string& connectionId, ConnectionState state, const std::string& error = "");
    
    // 资源管理
    void ensureMinimumConnections();
    void removeExcessConnections();
    bool canCreateNewConnection() const;
    
    // 错误处理
    void handleError(const std::string& error);
    void notifyConnectionEvent(const std::string& connectionId, const std::string& event);
    
    // 统计更新
    void updateStatistics(bool success);
    void updateConnectionStatistics();
};
