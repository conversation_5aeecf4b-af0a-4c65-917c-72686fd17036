#include "abb_direct_client.h"
#include "Executor.h"
#include <iostream>
#include <chrono>
#include <cstring>
#include <sstream>
#include <iomanip>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <fcntl.h>
#include <errno.h>
#endif

ABBDirectClient::ABBDirectClient(const ABBConfig& config)
    : config(config), serverSocket(INVALID_SOCKET), clientSocket(INVALID_SOCKET),
      serverRunning(false), robotConnected(false), running(false),
      heartbeatOK(false), robotStatus(ABBProtocol::RobotStatus::IDLE) {

    // 创建执行器
    executor = std::make_shared<Fuxi::Common::Executor>(4);

    // 初始化当前任务状态
    currentTask = std::make_shared<TaskStatus>();

    clearError();
    
#ifdef _WIN32
    // 初始化Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        setError("Failed to initialize Winsock");
    }
#endif
}

ABBDirectClient::~ABBDirectClient() {
    stop();
    stopServer();
    
#ifdef _WIN32
    WSACleanup();
#endif
}

bool ABBDirectClient::startServer() {
    if (serverRunning) {
        return true;
    }

    if (!initializeServerSocket()) {
        return false;
    }

    serverRunning = true;
    clearError();

    return true;
}

void ABBDirectClient::stopServer() {
    if (serverRunning) {
        serverRunning = false;
        disconnectRobot();
        cleanupSockets();
    }
}

bool ABBDirectClient::waitForRobotConnection(int timeoutMs) {
    if (!serverRunning) {
        setError("Server not running");
        return false;
    }

    if (robotConnected) {
        return true;
    }

    return acceptClientConnection();
}

void ABBDirectClient::disconnectRobot() {
    if (robotConnected) {
        robotConnected = false;
        heartbeatOK = false;

        if (clientSocket != INVALID_SOCKET) {
#ifdef _WIN32
            closesocket(clientSocket);
#else
            close(clientSocket);
#endif
            clientSocket = INVALID_SOCKET;
        }
    }
}

bool ABBDirectClient::start() {
    if (running) {
        return true;
    }

    // 确保服务器已启动
    if (!serverRunning && !startServer()) {
        setError("Failed to start server");
        return false;
    }

    running = true;

    // 启动各个工作循环
    executor->postTask([this]() { heartbeatLoop(); });
    executor->postTask([this]() { sendLoop(); });
    executor->postTask([this]() { receiveLoop(); });
    executor->postTask([this]() { statusUpdateLoop(); });

    return true;
}

void ABBDirectClient::stop() {
    running = false;
    // executor会在析构时自动等待任务完成
}

bool ABBDirectClient::sendTask(const TaskInfo& task) {
    if (!robotConnected) {
        setError("Robot not connected");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(taskMutex);
    
    // 更新当前任务状态
    currentTask->taskId = task.taskId;
    currentTask->status = TaskStatus::PENDING;
    currentTask->progress = 0.0f;
    currentTask->message = "Task queued";
    currentTask->startTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
        std::chrono::system_clock::now().time_since_epoch()).count());
    
    // 构建任务命令
    std::string command = ABBProtocol::ProtocolHelper::buildTaskCommand(task);
    
    // 添加到发送队列
    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        sendQueue.push(command);
    }
    
    notifyTaskStatus(currentTask);
    return true;
}

bool ABBDirectClient::cancelTask(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(taskMutex);
    
    if (currentTask && currentTask->taskId == taskId) {
        currentTask->status = TaskStatus::CANCELLED;
        currentTask->message = "Task cancelled by user";
        currentTask->endTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        
        // 发送停止命令
        std::string stopCommand = ABBProtocol::ProtocolHelper::buildStopCommand();
        {
            std::lock_guard<std::mutex> sendLock(sendQueueMutex);
            sendQueue.push(stopCommand);
        }
        
        notifyTaskStatus(currentTask);
        return true;
    }
    
    return false;
}

bool ABBDirectClient::cancelAllTasks() {
    std::lock_guard<std::mutex> lock(taskMutex);
    
    if (currentTask) {
        currentTask->status = TaskStatus::CANCELLED;
        currentTask->message = "All tasks cancelled";
        currentTask->endTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        
        notifyTaskStatus(currentTask);
    }
    
    // 清空发送队列
    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        std::queue<std::string> empty;
        sendQueue.swap(empty);
    }
    
    // 发送停止命令
    std::string stopCommand = ABBProtocol::ProtocolHelper::buildStopCommand();
    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        sendQueue.push(stopCommand);
    }
    
    return true;
}

std::shared_ptr<TaskStatus> ABBDirectClient::getTaskStatus(const std::string& taskId) {
    std::lock_guard<std::mutex> lock(taskMutex);
    
    if (currentTask && currentTask->taskId == taskId) {
        return currentTask;
    }
    
    return nullptr;
}

std::shared_ptr<TaskStatus> ABBDirectClient::getCurrentTaskStatus() {
    std::lock_guard<std::mutex> lock(taskMutex);
    return currentTask;
}

bool ABBDirectClient::emergencyStop() {
    std::string command = ABBProtocol::ProtocolHelper::buildEmergencyStop();
    
    // 紧急停止命令优先发送
    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        // 清空队列，优先发送紧急停止
        std::queue<std::string> empty;
        sendQueue.swap(empty);
        sendQueue.push(command);
    }
    
    // 更新任务状态
    {
        std::lock_guard<std::mutex> lock(taskMutex);
        if (currentTask) {
            currentTask->status = TaskStatus::CANCELLED;
            currentTask->message = "Emergency stop activated";
            notifyTaskStatus(currentTask);
        }
    }
    
    notifyError("Emergency stop activated");
    return true;
}

bool ABBDirectClient::reset() {
    std::string command = ABBProtocol::ProtocolHelper::buildResetCommand();
    
    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        sendQueue.push(command);
    }
    
    return true;
}

ABBProtocol::RobotStatus ABBDirectClient::getRobotStatus() {
    std::lock_guard<std::mutex> lock(statusMutex);
    return robotStatus;
}

bool ABBDirectClient::isRobotReady() {
    return getRobotStatus() == ABBProtocol::RobotStatus::IDLE;
}

bool ABBDirectClient::hasError() {
    return getRobotStatus() == ABBProtocol::RobotStatus::ROBOT_ERROR;
}

void ABBDirectClient::heartbeatLoop() {
    while (running) {
        try {
            if (robotConnected) {
                if (!sendHeartbeat()) {
                    stats.heartbeatMisses++;
                    if (stats.heartbeatMisses > 3) {
                        heartbeatOK = false;
                        notifyError("Heartbeat failed for ABB robot");

                        // 断开连接，等待重新连接
                        disconnectRobot();
                        std::this_thread::sleep_for(std::chrono::seconds(5));
                    }
                } else {
                    stats.heartbeatMisses = 0;
                    heartbeatOK = true;
                }
            }
        } catch (const std::exception& e) {
            setError("Heartbeat loop exception: " + std::string(e.what()));
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(config.heartbeatInterval));
    }
}

void ABBDirectClient::sendLoop() {
    while (running) {
        try {
            std::string message;
            
            // 从队列中获取消息
            {
                std::lock_guard<std::mutex> lock(sendQueueMutex);
                if (!sendQueue.empty()) {
                    message = sendQueue.front();
                    sendQueue.pop();
                }
            }
            
            if (!message.empty() && robotConnected) {
                if (sendMessage(message)) {
                    stats.messagesSent++;
                    stats.lastMessageTime = std::chrono::steady_clock::now();
                    notifyMessage("Sent: " + message);
                } else {
                    notifyError("Failed to send message: " + message);
                }
            }
        } catch (const std::exception& e) {
            setError("Send loop exception: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void ABBDirectClient::receiveLoop() {
    while (running) {
        try {
            if (robotConnected) {
                std::string message;
                if (receiveMessage(message, 100)) {
                    stats.messagesReceived++;

                    // 添加到接收队列
                    {
                        std::lock_guard<std::mutex> lock(receiveQueueMutex);
                        receiveQueue.push(message);
                    }

                    // 处理消息
                    processReceivedMessage(message);
                    notifyMessage("Received: " + message);
                }
            }
        } catch (const std::exception& e) {
            setError("Receive loop exception: " + std::string(e.what()));
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void ABBDirectClient::statusUpdateLoop() {
    while (running) {
        try {
            if (robotConnected && heartbeatOK) {
                // 定期查询状态
                sendStatusQuery();
            }
        } catch (const std::exception& e) {
            setError("Status update loop exception: " + std::string(e.what()));
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(config.heartbeatInterval * 2));
    }
}



bool ABBDirectClient::sendMessage(const std::string& message) {
    if (!robotConnected) {
        setError("Robot not connected");
        return false;
    }

    // 检查消息长度限制
    if (message.length() > ABBProtocol::Constants::MAX_COMMAND_LENGTH) {
        setError("Message too long: " + std::to_string(message.length()) + " > " +
                std::to_string(ABBProtocol::Constants::MAX_COMMAND_LENGTH));
        return false;
    }

    int bytesSent = sendData(message.c_str(), static_cast<int>(message.length()));
    if (bytesSent <= 0) {
        setError("Failed to send message: " + getSocketErrorString(getLastSocketError()));
        return false;
    }

    return true;
}

bool ABBDirectClient::receiveMessage(std::string& message, int timeoutMs) {
    if (!robotConnected) {
        setError("Robot not connected");
        return false;
    }

    char buffer[ABBProtocol::Constants::MAX_RESPONSE_LENGTH + 1];
    int bytesReceived = receiveData(buffer, sizeof(buffer) - 1, timeoutMs);

    if (bytesReceived <= 0) {
        return false; // 超时或错误，但不设置错误信息（正常情况）
    }

    buffer[bytesReceived] = '\0';
    message = std::string(buffer);

    // 查找消息结束符
    size_t endPos = message.find('#');
    if (endPos != std::string::npos) {
        message = message.substr(0, endPos + 1);  // 包含结束符
    }

    return true;
}

bool ABBDirectClient::processReceivedMessage(const std::string& message) {
    try {
        ABBProtocol::ABBResponse response = ABBProtocol::ProtocolHelper::parseResponse(message);

        // 根据响应类型处理
        switch (response.type) {
            case ABBProtocol::ResponseType::SUCCESS:
                processTaskResponse(response);
                break;
            case ABBProtocol::ResponseType::RESPONSE_ERROR:
                notifyError("ABB Robot Error: " + response.message);
                break;
            case ABBProtocol::ResponseType::BUSY:
                // 机器人忙碌，正常状态
                break;
            default:
                break;
        }

        return true;
    } catch (const std::exception& e) {
        setError("Error processing message: " + std::string(e.what()));
        return false;
    }
}

bool ABBDirectClient::initializeServerSocket() {
    serverSocket = ::socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket == INVALID_SOCKET) {
        setError("Failed to create server socket: " + getSocketErrorString(getLastSocketError()));
        return false;
    }

    // 设置socket选项
    int optval = 1;
#ifdef _WIN32
    setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, (const char*)&optval, sizeof(optval));
#else
    setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, &optval, sizeof(optval));
#endif

    // 绑定到端口
    sockaddr_in serverAddr;
    memset(&serverAddr, 0, sizeof(serverAddr));
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(static_cast<uint16_t>(config.listenPort));

    if (bind(serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        setError("Failed to bind server socket: " + getSocketErrorString(getLastSocketError()));
#ifdef _WIN32
        closesocket(serverSocket);
#else
        close(serverSocket);
#endif
        serverSocket = INVALID_SOCKET;
        return false;
    }

    // 开始监听
    if (listen(serverSocket, 1) == SOCKET_ERROR) {
        setError("Failed to listen on server socket: " + getSocketErrorString(getLastSocketError()));
#ifdef _WIN32
        closesocket(serverSocket);
#else
        close(serverSocket);
#endif
        serverSocket = INVALID_SOCKET;
        return false;
    }

    return true;
}

bool ABBDirectClient::acceptClientConnection() {
    if (serverSocket == INVALID_SOCKET) {
        setError("Server socket not initialized");
        return false;
    }

    sockaddr_in clientAddr;
    socklen_t clientAddrLen = sizeof(clientAddr);

    clientSocket = accept(serverSocket, (sockaddr*)&clientAddr, &clientAddrLen);
    if (clientSocket == INVALID_SOCKET) {
        setError("Failed to accept client connection: " + getSocketErrorString(getLastSocketError()));
        return false;
    }

    robotConnected = true;
    stats.lastConnectTime = std::chrono::steady_clock::now();

    // 获取客户端IP地址
    char clientIP[INET_ADDRSTRLEN];
    inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
    notifyMessage("Robot connected from: " + std::string(clientIP));

    return true;
}

void ABBDirectClient::cleanupSockets() {
    if (clientSocket != INVALID_SOCKET) {
#ifdef _WIN32
        closesocket(clientSocket);
#else
        close(clientSocket);
#endif
        clientSocket = INVALID_SOCKET;
    }

    if (serverSocket != INVALID_SOCKET) {
#ifdef _WIN32
        closesocket(serverSocket);
#else
        close(serverSocket);
#endif
        serverSocket = INVALID_SOCKET;
    }
}



int ABBDirectClient::sendData(const char* data, int length) {
    if (clientSocket == INVALID_SOCKET) {
        return -1;
    }

#ifdef _WIN32
    return send(clientSocket, data, length, 0);
#else
    return send(clientSocket, data, length, MSG_NOSIGNAL);
#endif
}

int ABBDirectClient::receiveData(char* buffer, int bufferSize, int timeoutMs) {
    if (clientSocket == INVALID_SOCKET) {
        return -1;
    }

    // 设置接收超时
#ifdef _WIN32
    DWORD timeout = timeoutMs;
    setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof(timeout));
#else
    struct timeval timeout;
    timeout.tv_sec = timeoutMs / 1000;
    timeout.tv_usec = (timeoutMs % 1000) * 1000;
    setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
#endif

    return recv(clientSocket, buffer, bufferSize, 0);
}

bool ABBDirectClient::sendHeartbeat() {
    std::string command = ABBProtocol::ProtocolHelper::buildHeartbeat();
    bool result = sendMessage(command);

    if (result) {
        lastHeartbeatSend = std::chrono::steady_clock::now();
    }

    return result;
}

bool ABBDirectClient::sendStatusQuery() {
    std::string command = ABBProtocol::ProtocolHelper::buildStatusQuery();
    return sendMessage(command);
}

bool ABBDirectClient::processTaskResponse(const ABBProtocol::ABBResponse& response) {
    std::lock_guard<std::mutex> lock(taskMutex);

    if (!currentTask) {
        return false;
    }

    if (response.type == ABBProtocol::ResponseType::SUCCESS) {
        if (currentTask->status == TaskStatus::PENDING) {
            currentTask->status = TaskStatus::RUNNING;
            currentTask->message = "Task started successfully";
            currentTask->progress = 0.1f;
        } else if (currentTask->status == TaskStatus::RUNNING) {
            currentTask->status = TaskStatus::COMPLETED;
            currentTask->message = "Task completed successfully";
            currentTask->progress = 1.0f;
            currentTask->endTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count());
            stats.tasksCompleted++;
        }
    } else {
        currentTask->status = TaskStatus::FAILED;
        currentTask->message = "Task failed: " + response.message;
        currentTask->endTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::system_clock::now().time_since_epoch()).count());
        stats.tasksFailed++;
    }

    notifyTaskStatus(currentTask);
    return true;
}

bool ABBDirectClient::processStatusResponse(const ABBProtocol::ABBResponse& response) {
    std::lock_guard<std::mutex> lock(statusMutex);

    // 简单的状态解析逻辑
    if (response.message.find("IDLE") != std::string::npos) {
        robotStatus = ABBProtocol::RobotStatus::IDLE;
    } else if (response.message.find("RUNNING") != std::string::npos) {
        robotStatus = ABBProtocol::RobotStatus::RUNNING;
    } else if (response.message.find("ERROR") != std::string::npos) {
        robotStatus = ABBProtocol::RobotStatus::ROBOT_ERROR;
    }

    lastStatusUpdate = std::chrono::steady_clock::now();
    notifyRobotStatus(robotStatus);
    return true;
}

void ABBDirectClient::setError(const std::string& error) {
    lastError = error;
    std::cerr << "[ABBDirectClient] Error: " << error << std::endl;
}

void ABBDirectClient::clearError() {
    lastError.clear();
}

void ABBDirectClient::notifyError(const std::string& error) {
    if (errorCallback) {
        errorCallback(error);
    }
}

void ABBDirectClient::notifyTaskStatus(std::shared_ptr<TaskStatus> status) {
    if (taskStatusCallback) {
        taskStatusCallback(status);
    }
}

void ABBDirectClient::notifyRobotStatus(ABBProtocol::RobotStatus status) {
    if (robotStatusCallback) {
        robotStatusCallback(status);
    }
}

void ABBDirectClient::notifyMessage(const std::string& message) {
    if (messageCallback) {
        messageCallback(message);
    }
}

void ABBDirectClient::closeSocket() {
    // 在服务器模式下，使用 cleanupSockets() 方法
    cleanupSockets();
}

int ABBDirectClient::getLastSocketError() {
#ifdef _WIN32
    return WSAGetLastError();
#else
    return errno;
#endif
}

std::string ABBDirectClient::getSocketErrorString(int errorCode) {
#ifdef _WIN32
    char* errorMsg = nullptr;
    FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM,
                   nullptr, errorCode, 0, (LPSTR)&errorMsg, 0, nullptr);
    std::string result = errorMsg ? errorMsg : "Unknown error";
    if (errorMsg) LocalFree(errorMsg);
    return result;
#else
    return strerror(errorCode);
#endif
}

std::string ABBDirectClient::generateTaskId() {
    auto now = std::chrono::system_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

    std::ostringstream oss;
    oss << "TASK_" << timestamp;
    return oss.str();
}

void ABBDirectClient::updateTaskStatus(const std::string& taskId, TaskStatus::Status status, const std::string& message) {
    std::lock_guard<std::mutex> lock(taskMutex);

    if (currentTask && currentTask->taskId == taskId) {
        currentTask->status = status;
        currentTask->message = message;

        if (status == TaskStatus::COMPLETED || status == TaskStatus::FAILED || status == TaskStatus::CANCELLED) {
            currentTask->endTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count());
        }

        notifyTaskStatus(currentTask);
    }
}

bool ABBDirectClient::pause() {
    // 暂停功能的实现
    std::string command = ABBProtocol::ProtocolHelper::buildCommand(ABBProtocol::CommandType::TASK_STOP);

    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        sendQueue.push(command);
    }

    return true;
}

bool ABBDirectClient::resume() {
    // 恢复功能的实现
    std::string command = ABBProtocol::ProtocolHelper::buildCommand(ABBProtocol::CommandType::TASK_START);

    {
        std::lock_guard<std::mutex> sendLock(sendQueueMutex);
        sendQueue.push(command);
    }

    return true;
}

bool ABBDirectClient::sendCommand(const std::string& command, const std::string& parameter) {
    if (!robotConnected) {
        lastError = "Robot not connected";
        return false;
    }

    try {
        // 构建简单的命令字符串
        std::string commandMessage = command;
        if (!parameter.empty()) {
            commandMessage += ":" + parameter;
        }
        commandMessage += "\n";

        // 发送命令
        {
            std::lock_guard<std::mutex> sendLock(sendQueueMutex);
            sendQueue.push(commandMessage);
        }

        // 记录命令发送
        if (taskStatusCallback) {
            auto status = std::make_shared<TaskStatus>();
            status->taskId = "COMMAND_" + command;
            status->status = TaskStatus::RUNNING;
            status->currentStep = "SENDING_COMMAND";
            status->message = "Sent command: " + command + " with parameter: " + parameter;
            taskStatusCallback(status);
        }

        return true;

    } catch (const std::exception& e) {
        lastError = "Failed to send command: " + std::string(e.what());
        return false;
    }
}


