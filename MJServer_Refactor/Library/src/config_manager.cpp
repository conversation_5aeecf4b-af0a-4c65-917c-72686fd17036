#include "config_manager.h"
#include <fstream>
#include <sstream>
#include <iostream>
#include <filesystem>
#include <regex>
#include <QSettings>
#include <QDir>
#include <QStandardPaths>

ConfigManager::ConfigManager() {
    // 初始化默认配置
    systemConfig = createDefaultConfig();
}

ConfigManager::~ConfigManager() = default;

bool ConfigManager::loadConfig(const std::string& filename) {
    currentConfigFile = filename;

    std::cout << "Loading config from: " << filename << std::endl;

    try {
        if (filename.find(".ini") != std::string::npos) {
            std::cout << "Using INI format" << std::endl;
            return loadFromIni(filename);
        } else if (filename.find(".xml") != std::string::npos) {
            std::cout << "Using XML format" << std::endl;
            return loadFromXml(filename);
        } else {
            // 默认使用INI格式
            std::cout << "Using default INI format" << std::endl;
            return loadFromIni(filename);
        }
    } catch (const std::exception& e) {
        std::cerr << "Exception in loadConfig: " << e.what() << std::endl;
        handleError("Failed to load config: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::saveConfig(const std::string& filename) {
    std::string saveFile = filename.empty() ? currentConfigFile : filename;

    try {
        if (saveFile.find(".ini") != std::string::npos) {
            return saveToIni(saveFile);
        } else if (saveFile.find(".xml") != std::string::npos) {
            return saveToXml(saveFile);
        } else {
            // 默认使用INI格式
            return saveToIni(saveFile);
        }
    } catch (const std::exception& e) {
        handleError("Failed to save config: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::reloadConfig() {
    return loadConfig(currentConfigFile);
}

void ConfigManager::setSystemConfig(const SystemConfig& config) {
    std::string errorMessage;
    if (validateConfig(config, errorMessage)) {
        systemConfig = config;
        notifyConfigChange("system", "all");
    } else {
        handleError("Invalid system config: " + errorMessage);
    }
}

bool ConfigManager::addFeederConfig(const FeederConfig& config) {
    std::string errorMessage;
    if (!validateFeederConfig(config, errorMessage)) {
        handleError("Invalid feeder config: " + errorMessage);
        return false;
    }

    // 检查是否已存在相同ID的投料机
    for (const auto& existingConfig : systemConfig.feeders) {
        if (existingConfig.id == config.id) {
            handleError("Feeder with ID " + std::to_string(config.id) + " already exists");
            return false;
        }
    }

    systemConfig.feeders.push_back(config);
    notifyConfigChange("feeder", std::to_string(config.id));
    return true;
}

bool ConfigManager::updateFeederConfig(int feederId, const FeederConfig& config) {
    std::string errorMessage;
    if (!validateFeederConfig(config, errorMessage)) {
        handleError("Invalid feeder config: " + errorMessage);
        return false;
    }

    for (auto& existingConfig : systemConfig.feeders) {
        if (existingConfig.id == feederId) {
            existingConfig = config;
            notifyConfigChange("feeder", std::to_string(feederId));
            return true;
        }
    }

    return false;
}

bool ConfigManager::removeFeederConfig(int feederId) {
    auto it = std::remove_if(systemConfig.feeders.begin(), systemConfig.feeders.end(),
        [feederId](const FeederConfig& config) { return config.id == feederId; });

    if (it != systemConfig.feeders.end()) {
        systemConfig.feeders.erase(it, systemConfig.feeders.end());
        notifyConfigChange("feeder", std::to_string(feederId));
        return true;
    }

    return false;
}

FeederConfig ConfigManager::getFeederConfig(int feederId) const {
    for (const auto& config : systemConfig.feeders) {
        if (config.id == feederId) {
            return config;
        }
    }

    return createDefaultFeederConfig(feederId);
}

void ConfigManager::setABBConfig(const ABBConfig& config) {
    std::string errorMessage;
    if (validateABBConfig(config, errorMessage)) {
        systemConfig.abbRobot = config;
        notifyConfigChange("abb", "config");
    } else {
        handleError("Invalid ABB config: " + errorMessage);
    }
}

void ConfigManager::setDirectModeEnabled(bool enabled) {
    if (systemConfig.enableDirectMode != enabled) {
        systemConfig.enableDirectMode = enabled;
        notifyConfigChange("system", "directMode");
    }
}

bool ConfigManager::validateConfig(const SystemConfig& config, std::string& errorMessage) {
    // 验证投料机配置
    for (const auto& feederConfig : config.feeders) {
        if (!validateFeederConfig(feederConfig, errorMessage)) {
            return false;
        }
    }

    // 验证ABB配置
    if (!validateABBConfig(config.abbRobot, errorMessage)) {
        return false;
    }

    return true;
}

bool ConfigManager::validateFeederConfig(const FeederConfig& config, std::string& errorMessage) {
    if (!isValidFeederId(config.id)) {
        errorMessage = "Invalid feeder ID: " + std::to_string(config.id);
        return false;
    }

    if (!isValidIP(config.ip)) {
        errorMessage = "Invalid feeder IP: " + config.ip;
        return false;
    }

    if (!isValidPort(config.port)) {
        errorMessage = "Invalid feeder port: " + std::to_string(config.port);
        return false;
    }

    return true;
}

bool ConfigManager::validateABBConfig(const ABBConfig& config, std::string& errorMessage) {
    if (!isValidIP(config.listenIp)) {
        errorMessage = "Invalid ABB listen IP address: " + config.listenIp;
        return false;
    }

    if (!isValidPort(config.listenPort)) {
        errorMessage = "Invalid ABB listen port: " + std::to_string(config.listenPort);
        return false;
    }

    if (config.timeout <= 0) {
        errorMessage = "Invalid ABB timeout: " + std::to_string(config.timeout);
        return false;
    }

    return true;
}

SystemConfig ConfigManager::createDefaultConfig() {
    SystemConfig config;
    config.enableDirectMode = false;
    config.logFilePath = "logs/system.log";

    // 创建默认投料机配置
    for (int i = 1; i <= Constants::FEEDERS_COUNT; ++i) {
        config.feeders.push_back(createDefaultFeederConfig(i));
    }

    config.abbRobot = createDefaultABBConfig();

    return config;
}

FeederConfig ConfigManager::createDefaultFeederConfig(int feederId) const {
    FeederConfig config;
    config.id = feederId;
    config.ip = "192.168.1." + std::to_string(100 + feederId);
    config.port = 502;
    config.enabled = true;
    config.timeout = 5000;
    config.enableHeartbeat = true;

    // 初始化料仓重量配置
    config.binMinWeights.resize(5, 100.0f);
    config.binMaxWeights.resize(5, 1000.0f);

    return config;
}

ABBConfig ConfigManager::createDefaultABBConfig() {
    ABBConfig config;
    config.listenIp = "0.0.0.0";
    config.listenPort = 7000;
    config.timeout = 10000;
    config.maxConnections = 1;
    config.enableHeartbeat = true;
    config.heartbeatInterval = 2000;

    return config;
}

bool ConfigManager::loadFromIni(const std::string& filename) {
    std::string fullPath = getConfigPath(filename);
    std::cout << "Opening INI config file: " << fullPath << std::endl;

    try {
        QSettings settings(QString::fromStdString(fullPath), QSettings::IniFormat);

        if (settings.status() != QSettings::NoError) {
            std::cerr << "Cannot open INI config file: " << fullPath << std::endl;
            handleError("Cannot open INI config file: " + filename);
            return false;
        }

        SystemConfig config = createDefaultConfig();

        // 读取基本配置
        config.enableDirectMode = settings.value("General/enable_direct_mode", false).toBool();
        config.logFilePath = settings.value("General/log_file_path", "logs/system.log").toString().toStdString();

        // 读取投料机配置
        int feederCount = settings.beginReadArray("Feeders");
        if (feederCount > 0) {
            config.feeders.clear();

            for (int i = 0; i < feederCount; ++i) {
                settings.setArrayIndex(i);

                FeederConfig feeder;
                feeder.id = settings.value("id", i + 1).toInt();
                feeder.ip = settings.value("ip", QString("192.168.1.%1").arg(100 + i + 1)).toString().toStdString();
                feeder.port = settings.value("port", 502).toInt();
                feeder.enabled = settings.value("enabled", true).toBool();
                feeder.timeout = settings.value("timeout", 5000).toInt();
                feeder.enableHeartbeat = settings.value("enable_heartbeat", true).toBool();

                // 读取料仓重量配置
                feeder.binMinWeights.clear();
                feeder.binMaxWeights.clear();
                for (int j = 0; j < 5; ++j) {
                    float minWeight = settings.value(QString("bin_min_weight_%1").arg(j), 100.0f).toFloat();
                    float maxWeight = settings.value(QString("bin_max_weight_%1").arg(j), 1000.0f).toFloat();
                    feeder.binMinWeights.push_back(minWeight);
                    feeder.binMaxWeights.push_back(maxWeight);
                }

                config.feeders.push_back(feeder);
            }
        }
        settings.endArray();

        // 读取ABB服务器配置
        settings.beginGroup("ABBServer");
        config.abbRobot.listenIp = settings.value("listen_ip", "0.0.0.0").toString().toStdString();
        config.abbRobot.listenPort = settings.value("listen_port", 7000).toInt();
        config.abbRobot.timeout = settings.value("timeout", 10000).toInt();
        config.abbRobot.maxConnections = settings.value("max_connections", 1).toInt();
        config.abbRobot.enableHeartbeat = settings.value("enable_heartbeat", true).toBool();
        config.abbRobot.heartbeatInterval = settings.value("heartbeat_interval", 2000).toInt();
        settings.endGroup();

        // 设置配置
        systemConfig = config;
        stats.loadCount++;
        stats.lastLoadTime = std::chrono::steady_clock::now();

        std::cout << "INI configuration loaded successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        handleError("INI parsing error: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::saveToIni(const std::string& filename) {
    if (!ensureConfigDirectory()) {
        return false;
    }

    std::string fullPath = getConfigPath(filename);
    std::cout << "Saving INI config file: " << fullPath << std::endl;

    try {
        QSettings settings(QString::fromStdString(fullPath), QSettings::IniFormat);

        // 保存基本配置
        settings.setValue("General/enable_direct_mode", systemConfig.enableDirectMode);
        settings.setValue("General/log_file_path", QString::fromStdString(systemConfig.logFilePath));

        // 保存投料机配置
        settings.beginWriteArray("Feeders");
        for (size_t i = 0; i < systemConfig.feeders.size(); ++i) {
            settings.setArrayIndex(static_cast<int>(i));
            const auto& feeder = systemConfig.feeders[i];

            settings.setValue("id", feeder.id);
            settings.setValue("ip", QString::fromStdString(feeder.ip));
            settings.setValue("port", feeder.port);
            settings.setValue("enabled", feeder.enabled);
            settings.setValue("timeout", feeder.timeout);
            settings.setValue("enable_heartbeat", feeder.enableHeartbeat);

            // 保存料仓重量配置
            for (size_t j = 0; j < feeder.binMinWeights.size() && j < 5; ++j) {
                settings.setValue(QString("bin_min_weight_%1").arg(j), feeder.binMinWeights[j]);
            }
            for (size_t j = 0; j < feeder.binMaxWeights.size() && j < 5; ++j) {
                settings.setValue(QString("bin_max_weight_%1").arg(j), feeder.binMaxWeights[j]);
            }
        }
        settings.endArray();

        // 保存ABB服务器配置
        settings.beginGroup("ABBServer");
        settings.setValue("listen_ip", QString::fromStdString(systemConfig.abbRobot.listenIp));
        settings.setValue("listen_port", systemConfig.abbRobot.listenPort);
        settings.setValue("timeout", systemConfig.abbRobot.timeout);
        settings.setValue("max_connections", systemConfig.abbRobot.maxConnections);
        settings.setValue("enable_heartbeat", systemConfig.abbRobot.enableHeartbeat);
        settings.setValue("heartbeat_interval", systemConfig.abbRobot.heartbeatInterval);
        settings.endGroup();

        // 确保数据写入文件
        settings.sync();

        if (settings.status() != QSettings::NoError) {
            handleError("Failed to write INI config file: " + filename);
            return false;
        }

        stats.saveCount++;
        stats.lastSaveTime = std::chrono::steady_clock::now();

        std::cout << "INI configuration saved successfully" << std::endl;
        return true;

    } catch (const std::exception& e) {
        handleError("INI save error: " + std::string(e.what()));
        return false;
    }
}

std::string ConfigManager::getConfigPath(const std::string& filename) {
    if (filename.find('/') != std::string::npos || filename.find('\\') != std::string::npos) {
        return filename; // 绝对路径
    }
    return "config/" + filename;
}

bool ConfigManager::ensureConfigDirectory() {
    try {
        std::filesystem::create_directories("config");
        return true;
    } catch (const std::exception& e) {
        handleError("Failed to create config directory: " + std::string(e.what()));
        return false;
    }
}

void ConfigManager::handleError(const std::string& error) {
    stats.errorCount++;
    stats.lastError = error;
    std::cerr << "[ConfigManager] Error: " << error << std::endl;
}

void ConfigManager::notifyConfigChange(const std::string& section, const std::string& key) {
    if (configChangeCallback) {
        configChangeCallback(section, key);
    }
}

bool ConfigManager::isValidIP(const std::string& ip) {
    std::regex ipRegex(R"(^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$)");
    std::smatch match;

    if (!std::regex_match(ip, match, ipRegex)) {
        return false;
    }

    for (int i = 1; i <= 4; ++i) {
        int octet = std::stoi(match[i].str());
        if (octet < 0 || octet > 255) {
            return false;
        }
    }

    return true;
}

bool ConfigManager::isValidPort(int port) {
    return port > 0 && port <= 65535;
}

bool ConfigManager::isValidFeederId(int feederId) {
    return feederId >= 1 && feederId <= Constants::FEEDERS_COUNT;
}

// 简化的XML方法实现
bool ConfigManager::loadFromXml(const std::string& filename) {
    handleError("XML format not implemented yet");
    return false;
}

bool ConfigManager::saveToXml(const std::string& filename) {
    handleError("XML format not implemented yet");
    return false;
}

// 简化的JSON方法实现（保留用于兼容性）
bool ConfigManager::loadFromJson(const std::string& filename) {
    handleError("JSON format temporarily disabled");
    return false;
}

bool ConfigManager::saveToJson(const std::string& filename) {
    handleError("JSON format temporarily disabled");
    return false;
}

std::string ConfigManager::configToJson(const SystemConfig& config) {
    return "{}"; // 空JSON对象
}

SystemConfig ConfigManager::jsonToConfig(const std::string& jsonStr) {
    return createDefaultConfig();
}

// MES数据配置实现
void ConfigManager::setMESConfig(const MESDataConfig& config) {
    systemConfig.mesConfig = config;
    stats.saveCount++;

    if (configChangeCallback) {
        configChangeCallback("mes", "config");
    }
}

bool ConfigManager::saveMESAreaData(char areaId, std::shared_ptr<AreaData> data) {
    if (areaId < 'A' || areaId > 'V' || !data) {
        return false;
    }

    systemConfig.mesConfig.areaData[areaId] = data;

    // 自动保存到文件
    if (systemConfig.mesConfig.serverConfig.autoSave) {
        saveMESDataToFile();
    }

    return true;
}

std::shared_ptr<AreaData> ConfigManager::loadMESAreaData(char areaId) {
    if (areaId < 'A' || areaId > 'V') {
        return nullptr;
    }

    auto it = systemConfig.mesConfig.areaData.find(areaId);
    if (it != systemConfig.mesConfig.areaData.end()) {
        return it->second;
    }

    // 如果没有找到，返回默认的空区域数据
    return std::make_shared<AreaData>();
}

std::map<char, std::shared_ptr<AreaData>> ConfigManager::loadAllMESAreaData() {
    return systemConfig.mesConfig.areaData;
}

bool ConfigManager::saveMESFeederData(int feederId, std::shared_ptr<FeederData> data) {
    if (feederId < 1 || feederId > 6 || !data) {
        return false;
    }

    systemConfig.mesConfig.feederData[feederId] = data;

    // 自动保存到文件
    if (systemConfig.mesConfig.serverConfig.autoSave) {
        saveMESDataToFile();
    }

    return true;
}

std::shared_ptr<FeederData> ConfigManager::loadMESFeederData(int feederId) {
    if (feederId < 1 || feederId > 6) {
        return nullptr;
    }

    auto it = systemConfig.mesConfig.feederData.find(feederId);
    if (it != systemConfig.mesConfig.feederData.end()) {
        return it->second;
    }

    // 如果没有找到，返回默认的空投料机数据
    return std::make_shared<FeederData>();
}

std::map<int, std::shared_ptr<FeederData>> ConfigManager::loadAllMESFeederData() {
    return systemConfig.mesConfig.feederData;
}

bool ConfigManager::saveMESSendData(std::shared_ptr<SendData> data) {
    if (!data) {
        return false;
    }

    // 添加到历史记录
    systemConfig.mesConfig.recentSendData.push_back(data);

    // 限制历史记录数量
    const size_t maxHistorySize = 1000;
    if (systemConfig.mesConfig.recentSendData.size() > maxHistorySize) {
        systemConfig.mesConfig.recentSendData.erase(
            systemConfig.mesConfig.recentSendData.begin(),
            systemConfig.mesConfig.recentSendData.begin() +
            (systemConfig.mesConfig.recentSendData.size() - maxHistorySize)
        );
    }

    // 自动保存到文件
    if (systemConfig.mesConfig.serverConfig.autoSave) {
        saveMESDataToFile();
    }

    return true;
}

std::vector<std::shared_ptr<SendData>> ConfigManager::loadMESSendDataHistory(int maxCount) {
    auto& history = systemConfig.mesConfig.recentSendData;

    if (maxCount <= 0 || maxCount >= static_cast<int>(history.size())) {
        return history;
    }

    return std::vector<std::shared_ptr<SendData>>(
        history.end() - maxCount, history.end()
    );
}

bool ConfigManager::clearMESSendDataHistory() {
    systemConfig.mesConfig.recentSendData.clear();

    // 自动保存到文件
    if (systemConfig.mesConfig.serverConfig.autoSave) {
        saveMESDataToFile();
    }

    return true;
}

bool ConfigManager::saveMESDataToFile(const std::string& filename) {
    try {
        // 简化实现，暂时不保存MES数据到文件
        std::cout << "MES data save to file: " << filename << " (not implemented)" << std::endl;
        return true;
    } catch (const std::exception& e) {
        handleError("Failed to save MES data: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::loadMESDataFromFile(const std::string& filename) {
    try {
        // 简化实现，暂时不从文件加载MES数据
        std::cout << "MES data load from file: " << filename << " (not implemented)" << std::endl;
        systemConfig.mesConfig = MESDataConfig();
        return true;
    } catch (const std::exception& e) {
        handleError("Failed to load MES data: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::exportConfig(const std::string& exportPath, const std::string& format) {
    try {
        if (format == "ini" || format.empty()) {
            // 导出为INI格式
            return saveToIni(exportPath);
        } else {
            handleError("Unsupported export format: " + format);
            return false;
        }
    } catch (const std::exception& e) {
        handleError("Failed to export config: " + std::string(e.what()));
        return false;
    }
}

bool ConfigManager::importConfig(const std::string& importPath, const std::string& format) {
    try {
        if (format == "ini" || format.empty()) {
            // 从INI格式导入
            return loadFromIni(importPath);
        } else {
            handleError("Unsupported import format: " + format);
            return false;
        }
    } catch (const std::exception& e) {
        handleError("Failed to import config: " + std::string(e.what()));
        return false;
    }
}