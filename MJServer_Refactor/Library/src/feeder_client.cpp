#include "feeder_client.h"
#include "Executor.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <modbus.h>
#include <errno.h>
#include <cstring>

// Modbus寄存器地址定义（根据协议文档）
namespace ModbusRegisters {
    // 读取寄存器地址
    const int FEEDER_HEARTBEAT = 40069;           // 投料机心跳变量
    const int BIN_DISABLE_STATUS = 40070;         // 料仓禁止倒料状态
    const int BIN1_ACTUAL_WEIGHT = 40071;         // 1号料仓实际重量
    const int BIN2_ACTUAL_WEIGHT = 40073;         // 2号料仓实际重量
    const int BIN3_ACTUAL_WEIGHT = 40075;         // 3号料仓实际重量
    const int BIN4_ACTUAL_WEIGHT = 40077;         // 4号料仓实际重量
    const int BIN5_ACTUAL_WEIGHT = 40079;         // 5号料仓实际重量

    // 写入寄存器地址
    const int CLIENT_HEARTBEAT = 40100;           // 倒料机心跳变量
    const int FEEDING_STATUS = 40101;             // 料仓倒料运行反馈
    const int BIN1_MIN_WEIGHT = 40102;            // 1号料仓缺料下限值
    const int BIN2_MIN_WEIGHT = 40104;            // 2号料仓缺料下限值
    const int BIN3_MIN_WEIGHT = 40106;            // 3号料仓缺料下限值
    const int BIN4_MIN_WEIGHT = 40108;            // 4号料仓缺料下限值
    const int BIN5_MIN_WEIGHT = 40110;            // 5号料仓缺料下限值
    const int BIN1_TARGET_WEIGHT = 40112;         // 1号料仓加料目标值
    const int BIN2_TARGET_WEIGHT = 40114;         // 2号料仓加料目标值
    const int BIN3_TARGET_WEIGHT = 40116;         // 3号料仓加料目标值
    const int BIN4_TARGET_WEIGHT = 40118;         // 4号料仓加料目标值
    const int BIN5_TARGET_WEIGHT = 40120;         // 5号料仓加料目标值
}

FeederClient::FeederClient(const FeederConfig& config)
    : config(config), modbusContext(nullptr), connected(false), running(false), heartbeatOK(false),
      heartbeatCounter(0) {

    // 创建执行器
    executor = std::make_shared<Fuxi::Common::Executor>(2);

    // 初始化缓存状态
    cachedStatus = std::make_shared<FeederStatus>();
    cachedStatus->feederId = config.id;

    clearError();
}

FeederClient::~FeederClient() {
    stop();
    disconnect();

    // 清理modbus上下文
    if (modbusContext) {
        modbus_close(modbusContext);
        modbus_free(modbusContext);
        modbusContext = nullptr;
    }
}

bool FeederClient::connect() {
    if (connected) {
        return true;
    }
    
    stats.connectAttempts++;
    
    try {
        // 清理之前的连接
        if (modbusContext) {
            modbus_close(modbusContext);
            modbus_free(modbusContext);
            modbusContext = nullptr;
        }

        // 创建modbus上下文
        modbusContext = modbus_new_tcp(config.ip.c_str(), config.port);
        if (!modbusContext) {
            setError("Failed to create modbus context for feeder " + std::to_string(config.id));
            return false;
        }

        // 尝试连接
        if (modbus_connect(modbusContext) == -1) {
            setError("Failed to connect to feeder " + std::to_string(config.id) +
                    " at " + config.ip + ":" + std::to_string(config.port) +
                    " - " + modbus_strerror(errno));
            modbus_free(modbusContext);
            modbusContext = nullptr;
            return false;
        }

        connected = true;
        stats.lastConnectTime = std::chrono::steady_clock::now();
        clearError();

        // 初始状态更新
        updateStatus();

        return true;

    } catch (const std::exception& e) {
        setError("Exception during connection: " + std::string(e.what()));
        return false;
    }
}

void FeederClient::disconnect() {
    if (modbusContext && connected) {
        modbus_close(modbusContext);
        modbus_free(modbusContext);
        modbusContext = nullptr;
    }
    connected = false;
    heartbeatOK = false;
}

bool FeederClient::start() {
    if (running) {
        return true;
    }
    
    if (!connected && !connect()) {
        return false;
    }
    
    running = true;
    
    // 启动心跳循环
    executor->postTask([this]() {
        heartbeatLoop();
    });
    
    // 启动状态更新循环
    executor->postTask([this]() {
        statusUpdateLoop();
    });
    
    return true;
}

void FeederClient::stop() {
    running = false;
    // executor会在析构时自动等待任务完成
}

std::shared_ptr<FeederStatus> FeederClient::getFeederStatus() {
    std::lock_guard<std::mutex> lock(statusMutex);
    
    // 如果状态太旧，尝试更新
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastStatusUpdate);
    
    if (elapsed.count() > Constants::STATUS_UPDATE_INTERVAL) {
        if (connected) {
            updateStatus();
        }
    }
    
    return cachedStatus;
}

bool FeederClient::getBinWeight(int binId, float& weight) {
    // 使用真实的Modbus协议读取料仓重量
    // binId参数是0-based，但协议中是1-based，所以需要转换
    return readBinActualWeight(binId + 1, weight);
}

bool FeederClient::getBinBlockStatus(int binId, bool& isBlocked) {
    if (!validateBinId(binId)) {
        setError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 使用真实的Modbus协议读取料仓禁止状态
    uint16_t disableStatus;
    if (!readBinDisableStatus(disableStatus)) {
        return false;
    }

    // binId是0-based，但协议中位0-4对应1-5号料仓
    isBlocked = (disableStatus & (1 << binId)) != 0;
    return true;
}

bool FeederClient::getAllBinBlockStatus(std::vector<bool>& blockStatus) {
    uint16_t blockReg;
    if (!readSingleRegister(Constants::ADDR_BIN_BLOCK, blockReg)) {
        return false;
    }

    blockStatus.clear();
    blockStatus.resize(Constants::BINS_PER_FEEDER);

    // 解析位状态
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        blockStatus[i] = (blockReg & (1 << i)) != 0;
    }

    return true;
}

bool FeederClient::getAllBinWeights(std::vector<float>& weights) {
    weights.clear();
    weights.resize(Constants::BINS_PER_FEEDER);
    
    // 读取所有料仓重量
    uint16_t buffer[Constants::BINS_PER_FEEDER * 2];
    if (!readRegisters(Constants::ADDR_BIN_WEIGHT_START, Constants::BINS_PER_FEEDER * 2, buffer)) {
        return false;
    }
    
    // 转换为浮点数
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        weights[i] = registersToFloat(buffer[i * 2], buffer[i * 2 + 1]);
    }
    
    return true;
}

bool FeederClient::setBinTargetWeight(int binId, float targetWeight) {
    if (!validateBinId(binId) || !validateWeight(targetWeight)) {
        setError("Invalid parameters: binId=" + std::to_string(binId) +
                ", weight=" + std::to_string(targetWeight));
        return false;
    }

    // 使用真实的Modbus协议写入目标重量
    // binId是0-based，但协议中是1-based，所以需要转换
    return writeBinTargetWeight(binId + 1, targetWeight);
}

bool FeederClient::setAllBinTargetWeights(const std::vector<float>& targetWeights) {
    if (targetWeights.size() != Constants::BINS_PER_FEEDER) {
        setError("Invalid target weights array size");
        return false;
    }

    // 验证所有重量值
    for (float weight : targetWeights) {
        if (!validateWeight(weight)) {
            setError("Invalid weight value: " + std::to_string(weight));
            return false;
        }
    }

    // 转换为寄存器值
    uint16_t buffer[Constants::BINS_PER_FEEDER * 2];
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        floatToRegisters(targetWeights[i], buffer[i * 2], buffer[i * 2 + 1]);
    }

    return writeRegisters(Constants::ADDR_BIN_TARGET_WEIGHT_START, Constants::BINS_PER_FEEDER * 2, buffer);
}

bool FeederClient::setBinMinWeight(int binId, float minWeight) {
    if (!validateBinId(binId) || !validateWeight(minWeight)) {
        setError("Invalid parameters: binId=" + std::to_string(binId) +
                ", weight=" + std::to_string(minWeight));
        return false;
    }

    // 使用真实的Modbus协议写入最小重量
    // binId是0-based，但协议中是1-based，所以需要转换
    return writeBinMinWeight(binId + 1, minWeight);
}

bool FeederClient::setAllBinMinWeights(const std::vector<float>& minWeights) {
    if (minWeights.size() != Constants::BINS_PER_FEEDER) {
        setError("Invalid min weights array size");
        return false;
    }

    // 验证所有重量值
    for (float weight : minWeights) {
        if (!validateWeight(weight)) {
            setError("Invalid weight value: " + std::to_string(weight));
            return false;
        }
    }

    // 转换为寄存器值
    uint16_t buffer[Constants::BINS_PER_FEEDER * 2];
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        floatToRegisters(minWeights[i], buffer[i * 2], buffer[i * 2 + 1]);
    }

    return writeRegisters(Constants::ADDR_BIN_MIN_WEIGHT_START, Constants::BINS_PER_FEEDER * 2, buffer);
}

bool FeederClient::setBinFeedingStatus(int binId, bool isFeeding) {
    if (!validateBinId(binId)) {
        setError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 读取当前投料状态
    uint16_t currentStatus;
    if (!readSingleRegister(Constants::ADDR_BIN_FEEDING, currentStatus)) {
        return false;
    }

    // 设置或清除对应位
    if (isFeeding) {
        currentStatus |= (1 << binId);
    } else {
        currentStatus &= ~(1 << binId);
    }

    return writeSingleRegister(Constants::ADDR_BIN_FEEDING, currentStatus);
}

bool FeederClient::setAllBinFeedingStatus(const std::vector<bool>& feedingStatus) {
    if (feedingStatus.size() != Constants::BINS_PER_FEEDER) {
        setError("Invalid feeding status array size");
        return false;
    }

    // 将布尔数组转换为位字段
    uint16_t statusReg = 0;
    for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
        if (feedingStatus[i]) {
            statusReg |= (1 << i);
        }
    }

    return writeSingleRegister(Constants::ADDR_BIN_FEEDING, statusReg);
}

bool FeederClient::startFeeding(int binId) {
    if (binId < 1 || binId > 5) {
        setError("Invalid bin ID: " + std::to_string(binId) + " (must be 1-5)");
        return false;
    }

    // 根据协议文档，读取当前投料状态（40101地址）
    uint16_t currentStatus;
    if (!readRegisters(ModbusRegisters::FEEDING_STATUS, 1, &currentStatus)) {
        setError("Failed to read current feeding status");
        return false;
    }

    // 设置对应料仓的投料位（位0-4对应1-5号料仓）
    currentStatus |= (1 << (binId - 1));

    // 写入新的投料状态
    bool result = writeFeedingStatus(currentStatus);
    if (!result) {
        setError("Failed to start feeding for bin " + std::to_string(binId));
    }

    return result;
}

bool FeederClient::stopFeeding(int binId) {
    if (binId < 1 || binId > 5) {
        setError("Invalid bin ID: " + std::to_string(binId) + " (must be 1-5)");
        return false;
    }

    // 根据协议文档，读取当前投料状态（40101地址）
    uint16_t currentStatus;
    if (!readRegisters(ModbusRegisters::FEEDING_STATUS, 1, &currentStatus)) {
        setError("Failed to read current feeding status");
        return false;
    }

    // 清除对应料仓的投料位（位0-4对应1-5号料仓）
    currentStatus &= ~(1 << (binId - 1));

    // 写入新的投料状态
    bool result = writeFeedingStatus(currentStatus);
    if (!result) {
        setError("Failed to stop feeding for bin " + std::to_string(binId));
    }

    return result;
}

bool FeederClient::stopAllFeeding() {
    // 停止所有料仓的投料（清除所有位）
    bool result = writeFeedingStatus(0);
    if (!result) {
        setError("Failed to stop all feeding operations");
    }

    return result;
}

bool FeederClient::emergencyStop() {
    // 停止所有投料
    bool result = stopAllFeeding();
    
    if (result) {
        notifyError("Emergency stop activated for feeder " + std::to_string(config.id));
    }
    
    return result;
}

void FeederClient::heartbeatLoop() {
    static auto lastReconnectAttempt = std::chrono::steady_clock::now();
    const auto RECONNECT_INTERVAL = std::chrono::seconds(30); // 30秒重连一次

    while (running) {
        try {
            if (connected) {
                if (!sendHeartbeat() || !checkHeartbeat()) {
                    stats.heartbeatMisses++;
                    if (stats.heartbeatMisses > 3) {
                        heartbeatOK = false;
                        notifyError("Heartbeat failed for feeder " + std::to_string(config.id));

                        // 断开连接，等待下次重连周期
                        disconnect();
                        lastReconnectAttempt = std::chrono::steady_clock::now();
                    }
                } else {
                    stats.heartbeatMisses = 0;
                    heartbeatOK = true;
                }
            } else {
                // 未连接状态，检查是否到了重连时间
                auto now = std::chrono::steady_clock::now();
                if (now - lastReconnectAttempt >= RECONNECT_INTERVAL) {
                    // 异步执行重连，避免阻塞UI
                    executor->postTask([this]() {
                        if (connect()) {
                            // 连接成功，静默处理
                        } else {
                            // 连接失败，静默处理，避免大量日志输出
                        }
                    });
                    lastReconnectAttempt = now;
                }
            }
        } catch (const std::exception& e) {
            setError("Heartbeat loop exception: " + std::string(e.what()));
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(Constants::HEARTBEAT_INTERVAL));
    }
}

void FeederClient::statusUpdateLoop() {
    while (running) {
        try {
            if (connected && heartbeatOK) {
                if (updateStatus()) {
                    auto status = getFeederStatus();
                    notifyStatus(status);
                }
            }
        } catch (const std::exception& e) {
            setError("Status update loop exception: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(Constants::STATUS_UPDATE_INTERVAL));
    }
}

bool FeederClient::updateStatus() {
    if (!connected) {
        return false;
    }

    std::lock_guard<std::mutex> lock(statusMutex);

    try {
        // 更新基本状态
        cachedStatus->isConnected = connected;
        cachedStatus->heartbeatOK = heartbeatOK;
        cachedStatus->feederId = config.id;

        // 读取投料机心跳（检查投料机状态）
        uint16_t feederHeartbeat;
        if (readFeederHeartbeat(feederHeartbeat)) {
            // 心跳正常，更新心跳状态
            lastHeartbeatReceive = std::chrono::steady_clock::now();
            heartbeatOK = true;
        } else {
            // 心跳读取失败
            heartbeatOK = false;
        }

        // 读取料仓禁止倒料状态（根据协议文档40070）
        uint16_t disableStatus;
        if (readBinDisableStatus(disableStatus)) {
            for (int i = 0; i < Constants::BINS_PER_FEEDER; ++i) {
                // 位0-4分别对应1-5号料仓的禁止状态
                cachedStatus->bins[i].isBlocked = (disableStatus & (1 << i)) != 0;
            }
        }

        // 读取各料仓的实际重量（根据协议文档40071-40080）
        for (int binId = 1; binId <= Constants::BINS_PER_FEEDER; ++binId) {
            float weight;
            if (readBinActualWeight(binId, weight)) {
                cachedStatus->bins[binId - 1].currentWeight = weight;
            } else {
                // 读取失败，保持上次的值或设为0
                cachedStatus->bins[binId - 1].currentWeight = 0.0f;
            }
        }

        // 注意：投料状态是我们写入给投料机的，不是从投料机读取的
        // 这里可以保持当前的投料状态不变，或者从内部状态获取

        lastStatusUpdate = std::chrono::steady_clock::now();
        return true;

    } catch (const std::exception& e) {
        setError("Status update exception: " + std::string(e.what()));
        return false;
    }
}

bool FeederClient::sendHeartbeat() {
    // 根据协议文档，发送0.2s方波心跳到40100地址
    // 心跳计数器在0和1之间切换，模拟方波
    static bool heartbeatToggle = false;
    heartbeatToggle = !heartbeatToggle;

    uint16_t heartbeatValue = heartbeatToggle ? 1 : 0;
    bool result = writeClientHeartbeat(heartbeatValue);

    if (result) {
        lastHeartbeatSend = std::chrono::steady_clock::now();
    }

    return result;
}

bool FeederClient::checkHeartbeat() {
    // 根据协议文档，从40069地址读取投料机心跳
    uint16_t heartbeat;
    bool result = readFeederHeartbeat(heartbeat);

    if (result) {
        lastHeartbeatReceive = std::chrono::steady_clock::now();
        stats.lastHeartbeatTime = lastHeartbeatReceive;

        // 检查心跳是否在变化（0.2s方波）
        static uint16_t lastHeartbeatValue = 0;
        static auto lastHeartbeatCheck = std::chrono::steady_clock::now();

        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastHeartbeatCheck);

        // 如果超过1秒心跳值没有变化，认为心跳异常
        if (elapsed.count() > 1000 && heartbeat == lastHeartbeatValue) {
            return false; // 心跳异常
        }

        if (heartbeat != lastHeartbeatValue) {
            lastHeartbeatValue = heartbeat;
            lastHeartbeatCheck = now;
        }
    }

    return result;
}

bool FeederClient::reconnect() {
    disconnect();
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    return connect();
}

bool FeederClient::readRegisters(int address, int count, uint16_t* buffer) {
    if (!connected || !modbusContext) {
        setError("Not connected to feeder");
        return false;
    }

    try {
        int result = modbus_read_registers(modbusContext, address, count, buffer);
        if (result == count) {
            stats.successfulReads++;
            return true;
        } else {
            stats.failedReads++;
            setError("Failed to read registers at address " + std::to_string(address) +
                    ": " + modbus_strerror(errno));
            return false;
        }
    } catch (const std::exception& e) {
        stats.failedReads++;
        setError("Exception reading registers: " + std::string(e.what()));
        return false;
    }
}

bool FeederClient::writeRegisters(int address, int count, const uint16_t* buffer) {
    if (!connected || !modbusContext) {
        setError("Not connected to feeder");
        return false;
    }

    try {
        int result = modbus_write_registers(modbusContext, address, count, buffer);
        if (result == count) {
            stats.successfulWrites++;
            return true;
        } else {
            stats.failedWrites++;
            setError("Failed to write registers at address " + std::to_string(address) +
                    ": " + modbus_strerror(errno));
            return false;
        }
    } catch (const std::exception& e) {
        stats.failedWrites++;
        setError("Exception writing registers: " + std::string(e.what()));
        return false;
    }
}

bool FeederClient::readSingleRegister(int address, uint16_t& value) {
    return readRegisters(address, 1, &value);
}

bool FeederClient::writeSingleRegister(int address, uint16_t value) {
    return writeRegisters(address, 1, &value);
}

bool FeederClient::readFloatRegisters(int address, float& value) {
    uint16_t buffer[2];
    if (readRegisters(address, 2, buffer)) {
        value = registersToFloat(buffer[0], buffer[1]);
        return true;
    }
    return false;
}

bool FeederClient::writeFloatRegisters(int address, float value) {
    uint16_t buffer[2];
    floatToRegisters(value, buffer[0], buffer[1]);
    return writeRegisters(address, 2, buffer);
}

float FeederClient::registersToFloat(uint16_t reg1, uint16_t reg2) {
    uint32_t combined = (static_cast<uint32_t>(reg1) << 16) | reg2;
    return *reinterpret_cast<float*>(&combined);
}

void FeederClient::floatToRegisters(float value, uint16_t& reg1, uint16_t& reg2) {
    uint32_t combined = *reinterpret_cast<uint32_t*>(&value);
    reg1 = static_cast<uint16_t>(combined >> 16);
    reg2 = static_cast<uint16_t>(combined & 0xFFFF);
}

void FeederClient::setError(const std::string& error) {
    lastError = error;

    // 限制错误输出频率，避免UI无响应
    static std::string lastErrorMsg;
    static auto lastErrorTime = std::chrono::steady_clock::now();
    static int errorCount = 0;

    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastErrorTime);

    if (error == lastErrorMsg) {
        errorCount++;
        // 相同错误只在第一次和每30秒输出一次
        if (errorCount == 1 || elapsed.count() >= 30) {
            std::cerr << "[FeederClient " << config.id << "] Error: " << error;
            if (errorCount > 1) {
                std::cerr << " (repeated " << errorCount << " times)";
            }
            std::cerr << std::endl;
            lastErrorTime = now;
            errorCount = 0;
        }
    } else {
        // 新的错误消息，立即输出
        std::cerr << "[FeederClient " << config.id << "] Error: " << error << std::endl;
        lastErrorMsg = error;
        lastErrorTime = now;
        errorCount = 1;
    }
}

void FeederClient::clearError() {
    lastError.clear();
}

void FeederClient::notifyError(const std::string& error) {
    if (errorCallback) {
        errorCallback(error);
    }
}

void FeederClient::notifyStatus(std::shared_ptr<FeederStatus> status) {
    if (statusCallback) {
        statusCallback(status);
    }
}

bool FeederClient::validateBinId(int binId) const {
    return binId >= 0 && binId < Constants::BINS_PER_FEEDER;
}

bool FeederClient::validateWeight(float weight) const {
    return weight >= 0.0f && weight <= 10000.0f; // 假设最大重量10吨
}

// 根据Modbus协议实现的专用方法
bool FeederClient::readFeederHeartbeat(uint16_t& heartbeat) {
    return readRegisters(ModbusRegisters::FEEDER_HEARTBEAT, 1, &heartbeat);
}

bool FeederClient::readBinDisableStatus(uint16_t& status) {
    return readRegisters(ModbusRegisters::BIN_DISABLE_STATUS, 1, &status);
}

bool FeederClient::readBinActualWeight(int binId, float& weight) {
    if (binId < 1 || binId > 5) {
        setError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 根据料仓ID确定寄存器地址
    int baseAddress = 0;
    switch (binId) {
        case 1: baseAddress = ModbusRegisters::BIN1_ACTUAL_WEIGHT; break;
        case 2: baseAddress = ModbusRegisters::BIN2_ACTUAL_WEIGHT; break;
        case 3: baseAddress = ModbusRegisters::BIN3_ACTUAL_WEIGHT; break;
        case 4: baseAddress = ModbusRegisters::BIN4_ACTUAL_WEIGHT; break;
        case 5: baseAddress = ModbusRegisters::BIN5_ACTUAL_WEIGHT; break;
    }

    // 读取REAL类型数据（2个寄存器）
    uint16_t registers[2];
    if (!readRegisters(baseAddress, 2, registers)) {
        return false;
    }

    // 将两个16位寄存器转换为32位浮点数（REAL类型）
    // 注意：需要根据实际的字节序进行转换
    uint32_t rawValue = (static_cast<uint32_t>(registers[0]) << 16) | registers[1];
    weight = *reinterpret_cast<float*>(&rawValue);

    // 协议规定单位是0.01KG，转换为KG
    weight = weight / 100.0f;

    return true;
}

bool FeederClient::writeClientHeartbeat(uint16_t heartbeat) {
    return writeRegisters(ModbusRegisters::CLIENT_HEARTBEAT, 1, &heartbeat);
}

bool FeederClient::writeFeedingStatus(uint16_t status) {
    return writeRegisters(ModbusRegisters::FEEDING_STATUS, 1, &status);
}

bool FeederClient::writeBinMinWeight(int binId, float weight) {
    if (binId < 1 || binId > 5) {
        setError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 根据料仓ID确定寄存器地址
    int baseAddress = 0;
    switch (binId) {
        case 1: baseAddress = ModbusRegisters::BIN1_MIN_WEIGHT; break;
        case 2: baseAddress = ModbusRegisters::BIN2_MIN_WEIGHT; break;
        case 3: baseAddress = ModbusRegisters::BIN3_MIN_WEIGHT; break;
        case 4: baseAddress = ModbusRegisters::BIN4_MIN_WEIGHT; break;
        case 5: baseAddress = ModbusRegisters::BIN5_MIN_WEIGHT; break;
    }

    // 转换为协议规定的单位（0.01KG）
    float protocolWeight = weight * 100.0f;

    // 将浮点数转换为两个16位寄存器
    uint32_t rawValue = *reinterpret_cast<uint32_t*>(&protocolWeight);
    uint16_t registers[2];
    registers[0] = static_cast<uint16_t>(rawValue >> 16);
    registers[1] = static_cast<uint16_t>(rawValue & 0xFFFF);

    return writeRegisters(baseAddress, 2, registers);
}

bool FeederClient::writeBinTargetWeight(int binId, float weight) {
    if (binId < 1 || binId > 5) {
        setError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 根据料仓ID确定寄存器地址
    int baseAddress = 0;
    switch (binId) {
        case 1: baseAddress = ModbusRegisters::BIN1_TARGET_WEIGHT; break;
        case 2: baseAddress = ModbusRegisters::BIN2_TARGET_WEIGHT; break;
        case 3: baseAddress = ModbusRegisters::BIN3_TARGET_WEIGHT; break;
        case 4: baseAddress = ModbusRegisters::BIN4_TARGET_WEIGHT; break;
        case 5: baseAddress = ModbusRegisters::BIN5_TARGET_WEIGHT; break;
    }

    // 转换为协议规定的单位（0.01KG）
    float protocolWeight = weight * 100.0f;

    // 将浮点数转换为两个16位寄存器
    uint32_t rawValue = *reinterpret_cast<uint32_t*>(&protocolWeight);
    uint16_t registers[2];
    registers[0] = static_cast<uint16_t>(rawValue >> 16);
    registers[1] = static_cast<uint16_t>(rawValue & 0xFFFF);

    return writeRegisters(baseAddress, 2, registers);
}
