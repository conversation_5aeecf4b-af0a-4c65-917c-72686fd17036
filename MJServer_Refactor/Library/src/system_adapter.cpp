#include "system_adapter.h"
#include "feeder_manager.h"
#include "abb_direct_client.h"
#include <iostream>
#include <chrono>
#include <queue>
#include <iomanip>
#include <sstream>

SystemAdapter::SystemAdapter() 
    : running(false), systemAuto(true) {
    
    // 初始化区域数据
    initializeAreaData();
    
    // 设置统计开始时间
    stats.startTime = std::chrono::steady_clock::now();
}

SystemAdapter::~SystemAdapter() {
    stop();
}

bool SystemAdapter::initialize(const SystemConfig& config) {
    this->config = config;
    
    try {
        // 创建投料机管理器
        feederManager = std::make_shared<FeederManager>();
        if (!feederManager->initialize(config.feeders)) {
            handleError("Failed to initialize feeder manager");
            return false;
        }
        
        // 创建ABB直连客户端
        abbClient = std::make_shared<ABBDirectClient>(config.abbRobot);
        
        // 设置回调
        feederManager->setStatusCallback([this](const std::vector<std::shared_ptr<FeederStatus>>& status) {
            std::lock_guard<std::mutex> lock(feederMutex);
            cachedFeederStatus = status;
            stats.feederUpdates++;
            
            if (feederStatusCallback) {
                feederStatusCallback(status);
            }
        });
        
        abbClient->setTaskStatusCallback([this](std::shared_ptr<TaskStatus> status) {
            std::lock_guard<std::mutex> lock(taskMutex);
            currentTaskStatus = status;
            
            if (status) {
                notifyTaskLog("Task " + status->taskId + " status: " + std::to_string(static_cast<int>(status->status)));
            }
        });
        
        abbClient->setErrorCallback([this](const std::string& error) {
            handleError("ABB Error: " + error);
        });
        
        feederManager->setErrorCallback([this](const std::string& error) {
            handleError("Feeder Error: " + error);
        });
        
        return true;
        
    } catch (const std::exception& e) {
        handleError("Initialization exception: " + std::string(e.what()));
        return false;
    }
}

bool SystemAdapter::start() {
    if (running) {
        return true;
    }
    
    try {
        // 启动投料机管理器
        if (!feederManager->start()) {
            handleError("Failed to start feeder manager");
            return false;
        }
        
        // 启动ABB客户端
        if (!abbClient->start()) {
            handleError("Failed to start ABB client");
            return false;
        }
        
        running = true;
        notifyStatus("System adapter started successfully");
        return true;
        
    } catch (const std::exception& e) {
        handleError("Start exception: " + std::string(e.what()));
        return false;
    }
}

void SystemAdapter::stop() {
    if (!running) {
        return;
    }
    
    running = false;
    
    if (feederManager) {
        feederManager->stop();
    }
    
    if (abbClient) {
        abbClient->stop();
    }
    
    notifyStatus("System adapter stopped");
}

std::shared_ptr<TaskInfo> SystemAdapter::findNextTask() {
    std::lock_guard<std::mutex> lock(taskQueueMutex);

    // 如果队列为空，尝试生成新任务
    if (taskQueue.empty()) {
        generateTasksFromFeederStatus();
    }

    if (taskQueue.empty()) {
        return nullptr;
    }

    // 找到最匹配的任务（优先级：紧急任务 > 重量不足的料仓 > 普通任务）
    auto bestTask = findBestMatchingTask();
    if (bestTask) {
        // 从队列中移除选中的任务
        removeTaskFromQueue(bestTask->taskId);
        notifyTaskLog("Found matching task: " + bestTask->taskId +
                     " for feeder " + std::to_string(bestTask->feederId) +
                     ", bin " + std::to_string(bestTask->binId));
    }

    return bestTask;
}

bool SystemAdapter::startTask(std::shared_ptr<TaskInfo> task) {
    if (!task || !validateTaskInfo(task)) {
        handleError("Invalid task information");
        return false;
    }

    if (!abbClient || !abbClient->isRobotConnected()) {
        handleError("ABB client not connected");
        return false;
    }

    try {
        notifyTaskLog("Starting complete feeder task: " + task->taskId);

        // 执行完整的投料流程
        bool result = executeCompleteFeederTask(task);

        if (result) {
            stats.tasksProcessed++;
            notifyTaskLog("Task " + task->taskId + " completed successfully");
        } else {
            handleError("Failed to complete feeder task: " + task->taskId);
        }

        return result;

    } catch (const std::exception& e) {
        handleError("Start task exception: " + std::string(e.what()));
        return false;
    }
}

std::shared_ptr<TaskStatus> SystemAdapter::getTaskStatus() const {
    std::lock_guard<std::mutex> lock(taskMutex);
    return currentTaskStatus;
}

std::vector<std::shared_ptr<FeederStatus>> SystemAdapter::getFeederStatus() {
    if (feederManager) {
        auto status = feederManager->getFeederStatus();
        
        std::lock_guard<std::mutex> lock(feederMutex);
        cachedFeederStatus = status;
        
        return status;
    }
    
    std::lock_guard<std::mutex> lock(feederMutex);
    return cachedFeederStatus;
}

bool SystemAdapter::isSystemAuto() const {
    return systemAuto;
}

bool SystemAdapter::hasError() const {
    if (feederManager && feederManager->hasError()) {
        return true;
    }
    
    if (abbClient && abbClient->hasError()) {
        return true;
    }
    
    return false;
}

bool SystemAdapter::isAreaEnabled(char areaId) const {
    // 首先检查区域ID是否有效
    if (areaId < 'A' || areaId > 'V') {
        return false;
    }

    // 检查配置文件中的区域屏蔽状态
    auto it = config.blockConfig.areaBlocked.find(areaId);
    if (it != config.blockConfig.areaBlocked.end() && it->second) {
        // 配置文件中该区域被屏蔽
        return false;
    }

    // 配置文件中未屏蔽，区域启用
    return true;
}

bool SystemAdapter::isFeederEnabled(int feederId) const {
    // 首先检查投料机ID是否有效（1-based）
    if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
        return false;
    }

    // 检查配置文件中的投料机屏蔽状态
    auto it = config.blockConfig.feederBlocked.find(feederId);
    if (it != config.blockConfig.feederBlocked.end() && it->second) {
        // 配置文件中该投料机被屏蔽
        return false;
    }

    // 检查投料机的真实连接状态
    if (feederManager) {
        // 转换为0-based索引检查真实状态
        bool isConnected = feederManager->isFeederConnected(feederId - 1);
        bool isEnabled = feederManager->isFeederEnabled(feederId - 1);

        // 只有配置文件未屏蔽且投料机连接正常且启用时才返回true
        return isConnected && isEnabled;
    }

    // 如果没有FeederManager，只能根据配置文件判断
    return true;
}

bool SystemAdapter::isBinEnabled(int feederId, int binId) const {
    // 首先检查参数是否有效（1-based）
    if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
        return false;
    }

    if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
        return false;
    }

    // 检查投料机是否启用（这会同时检查配置文件和真实状态）
    if (!isFeederEnabled(feederId)) {
        return false;
    }

    // 检查配置文件中的料仓屏蔽状态
    auto binKey = std::make_pair(feederId, binId);
    auto it = config.blockConfig.binBlocked.find(binKey);
    if (it != config.blockConfig.binBlocked.end() && it->second) {
        // 配置文件中该料仓被屏蔽
        return false;
    }

    // 检查投料机反馈的料仓禁止状态（从Modbus协议读取）
    if (feederManager) {
        // 转换为0-based索引检查真实状态
        bool binEnabled = feederManager->isBinEnabled(feederId - 1, binId - 1);

        // 只有配置文件未屏蔽且投料机反馈料仓未被禁止时才返回true
        return binEnabled;
    }

    // 如果没有FeederManager，只能根据配置文件判断
    return true;
}

bool SystemAdapter::isHeartbeatOK() const {
    bool feederOK = feederManager ? feederManager->isHeartbeatOK() : false;
    bool abbOK = abbClient ? abbClient->isHeartbeatOK() : false;
    
    return feederOK && abbOK;
}

std::map<char, std::shared_ptr<AreaData>> SystemAdapter::getAllPLCAreaData() {
    std::lock_guard<std::mutex> lock(areaMutex);
    
    // 更新区域数据缓存
    updateAreaDataCache();
    
    return cachedAreaData;
}

bool SystemAdapter::emergencyStop() {
    bool feederResult = feederManager ? feederManager->emergencyStop() : true;
    bool abbResult = abbClient ? abbClient->emergencyStop() : true;
    
    if (feederResult && abbResult) {
        notifyStatus("Emergency stop activated");
        return true;
    } else {
        handleError("Emergency stop failed");
        return false;
    }
}

bool SystemAdapter::reset() {
    bool feederResult = feederManager ? feederManager->reset() : true;
    bool abbResult = abbClient ? abbClient->reset() : true;
    
    if (feederResult && abbResult) {
        notifyStatus("System reset completed");
        return true;
    } else {
        handleError("System reset failed");
        return false;
    }
}

bool SystemAdapter::setSystemMode(bool autoMode) {
    systemAuto = autoMode;
    notifyStatus("System mode set to " + std::string(autoMode ? "AUTO" : "MANUAL"));
    return true;
}

void SystemAdapter::updateConfig(const SystemConfig& newConfig) {
    // 安全地更新配置，避免深拷贝问题
    try {
        // 逐个字段拷贝，避免复杂对象的深拷贝
        config.enableDirectMode = newConfig.enableDirectMode;
        config.feeders = newConfig.feeders;
        config.abbRobot = newConfig.abbRobot;
        config.blockConfig = newConfig.blockConfig;
        config.logFilePath = newConfig.logFilePath;
        // 暂时跳过mesConfig的拷贝，避免shared_ptr问题
        // config.mesConfig = newConfig.mesConfig;
    } catch (const std::exception& e) {
        std::cerr << "Error updating config: " << e.what() << std::endl;
        return;
    }

    // 重新初始化组件
    if (running) {
        stop();
        initialize(config);
        start();
    }
}

void SystemAdapter::initializeAreaData() {
    std::lock_guard<std::mutex> lock(areaMutex);
    
    // 初始化A-V区域数据
    for (char c = 'A'; c <= 'V'; ++c) {
        auto areaData = std::make_shared<AreaData>();
        areaData->sandType = "DefaultSand";
        
        // 初始化4个桶的数据
        for (int i = 0; i < Constants::BUCKETS_PER_AREA; ++i) {
            areaData->buckets[i].batchNumber = "BATCH_" + std::string(1, c) + "_" + std::to_string(i);
            areaData->buckets[i].weight = 0.0f;
        }
        
        cachedAreaData[c] = areaData;
    }
}

void SystemAdapter::updateFeederStatusCache() {
    if (feederManager) {
        std::lock_guard<std::mutex> lock(feederMutex);
        cachedFeederStatus = feederManager->getFeederStatus();
        stats.feederUpdates++;
    }
}

/**
 * 更新区域数据缓存
 * 从多个数据源同步区域数据，包括：
 * 1. MES配置数据
 * 2. 投料机状态信息
 * 3. 屏蔽配置
 * 4. 数据验证和清理
 */
void SystemAdapter::updateAreaDataCache() {
    try {
        // 1. 从配置管理器获取MES区域数据
        if (config.mesConfig.areaData.size() > 0) {
            for (const auto& pair : config.mesConfig.areaData) {
                char areaId = pair.first;
                auto mesAreaData = pair.second;

                // 更新缓存中的区域数据
                auto it = cachedAreaData.find(areaId);
                if (it != cachedAreaData.end() && mesAreaData) {
                    // 更新砂类型
                    it->second->sandType = mesAreaData->sandType;
                    it->second->isBlocked = mesAreaData->isBlocked;

                    // 更新桶数据
                    for (size_t i = 0; i < mesAreaData->buckets.size() && i < it->second->buckets.size(); ++i) {
                        it->second->buckets[i].batchNumber = mesAreaData->buckets[i].batchNumber;
                        it->second->buckets[i].weight = mesAreaData->buckets[i].weight;
                        it->second->buckets[i].isBlocked = mesAreaData->buckets[i].isBlocked;
                    }
                }
            }
        }

        // 2. 从投料机管理器获取相关的区域状态信息
        if (feederManager) {
            auto feederStatuses = feederManager->getFeederStatus();
            for (const auto& feederStatus : feederStatuses) {
                if (feederStatus && feederStatus->isConnected) {
                    // 根据投料机状态更新相关区域的可用性
                    // 这里可以根据业务逻辑确定投料机与区域的关联关系
                    updateAreaAvailabilityFromFeeder(feederStatus);
                }
            }
        }

        // 3. 应用屏蔽配置
        applyBlockConfigToAreas();

        // 4. 验证和清理数据
        validateAndCleanAreaData();

        // 5. 触发区域状态回调
        if (areaStatusCallback) {
            areaStatusCallback(cachedAreaData);
        }

        stats.areaUpdates++;

    } catch (const std::exception& e) {
        handleError("Failed to update area data cache: " + std::string(e.what()));
    }
}

/**
 * 根据投料机状态更新相关区域的可用性
 * @param feederStatus 投料机状态信息
 */
void SystemAdapter::updateAreaAvailabilityFromFeeder(std::shared_ptr<FeederStatus> feederStatus) {
    if (!feederStatus) return;

    // 根据投料机ID确定影响的区域
    // 这里假设投料机1-6分别对应区域A-F的可用性（可根据实际业务逻辑调整）
    char affectedAreaId = 'A' + (feederStatus->feederId - 1);

    if (affectedAreaId >= 'A' && affectedAreaId <= 'V') {
        auto it = cachedAreaData.find(affectedAreaId);
        if (it != cachedAreaData.end()) {
            // 根据投料机状态更新区域可用性
            // 如果投料机有错误或未连接，可能影响该区域的可用性
            bool areaAvailable = feederStatus->isConnected &&
                               feederStatus->heartbeatOK &&
                               !feederStatus->isBlocked;

            // 这里可以根据需要设置区域的可用性标志
            // 注意：AreaData结构中可能需要添加availability字段
        }
    }
}

/**
 * 应用屏蔽配置到区域数据
 * 根据系统配置中的屏蔽设置更新区域和桶的屏蔽状态
 */
void SystemAdapter::applyBlockConfigToAreas() {
    // 应用区域屏蔽配置
    for (const auto& pair : config.blockConfig.areaBlocked) {
        char areaId = pair.first;
        bool isBlocked = pair.second;

        auto it = cachedAreaData.find(areaId);
        if (it != cachedAreaData.end()) {
            it->second->isBlocked = isBlocked;
        }
    }

    // 应用桶屏蔽配置
    for (const auto& pair : config.blockConfig.bucketBlocked) {
        char areaId = pair.first.first;
        int bucketId = pair.first.second;
        bool isBlocked = pair.second;

        auto it = cachedAreaData.find(areaId);
        if (it != cachedAreaData.end() && bucketId >= 0 &&
            bucketId < static_cast<int>(it->second->buckets.size())) {
            it->second->buckets[bucketId].isBlocked = isBlocked;
        }
    }
}

/**
 * 验证和清理区域数据
 * 确保所有区域数据的完整性和有效性
 */
void SystemAdapter::validateAndCleanAreaData() {
    for (auto& pair : cachedAreaData) {
        auto& areaData = pair.second;
        if (!areaData) continue;

        // 验证砂类型
        if (areaData->sandType.empty()) {
            areaData->sandType = "Unknown";
        }

        // 验证桶数据
        for (auto& bucket : areaData->buckets) {
            // 确保批号不为空
            if (bucket.batchNumber.empty()) {
                bucket.batchNumber = "EMPTY_" + std::string(1, pair.first);
            }

            // 验证重量范围
            if (bucket.weight < 0.0f) {
                bucket.weight = 0.0f;
            }
            if (bucket.weight > 10000.0f) { // 假设最大重量限制
                bucket.weight = 10000.0f;
            }
        }
    }
}

std::shared_ptr<FeederStatus> SystemAdapter::convertToFeederStatus(int feederId) {
    // 从投料机管理器获取状态并转换格式
    if (feederManager) {
        return feederManager->getFeederStatus(feederId);
    }
    
    // 返回默认状态
    auto status = std::make_shared<FeederStatus>();
    status->feederId = feederId;
    status->isConnected = false;
    status->heartbeatOK = false;
    
    return status;
}

std::shared_ptr<AreaData> SystemAdapter::convertToAreaData(char areaId) {
    std::lock_guard<std::mutex> lock(areaMutex);
    
    auto it = cachedAreaData.find(areaId);
    if (it != cachedAreaData.end()) {
        return it->second;
    }
    
    // 返回默认区域数据
    auto areaData = std::make_shared<AreaData>();
    areaData->sandType = "Unknown";
    return areaData;
}

void SystemAdapter::handleError(const std::string& error) {
    stats.errors++;
    std::cerr << "[SystemAdapter] Error: " << error << std::endl;
    
    if (statusCallback) {
        statusCallback("ERROR: " + error);
    }
}

void SystemAdapter::notifyTaskLog(const std::string& message) {
    if (taskLogCallback) {
        taskLogCallback(message);
    }
}

void SystemAdapter::notifyStatus(const std::string& status) {
    if (statusCallback) {
        statusCallback(status);
    }
}

bool SystemAdapter::validateTaskInfo(std::shared_ptr<TaskInfo> task) {
    if (!task) {
        return false;
    }
    
    // 验证投料机ID
    if (task->feederId < 0 || task->feederId >= Constants::FEEDERS_COUNT) {
        return false;
    }
    
    // 验证料仓ID
    if (task->binId < 0 || task->binId >= Constants::BINS_PER_FEEDER) {
        return false;
    }
    
    // 验证区域ID
    if (task->areaId < 'A' || task->areaId > 'V') {
        return false;
    }
    
    // 验证重量
    if (task->weight <= 0.0f || task->weight > 10000.0f) {
        return false;
    }
    
    return true;
}

/**
 * 更新任务进度
 * 根据任务执行情况更新任务状态和进度信息
 * @param task 要更新进度的任务信息
 */
void SystemAdapter::updateTaskProgress(std::shared_ptr<TaskInfo> task) {
    if (!task) {
        handleError("Cannot update progress for null task");
        return;
    }

    try {
        std::lock_guard<std::mutex> lock(taskMutex);

        // 如果没有当前任务状态，创建一个新的
        if (!currentTaskStatus) {
            currentTaskStatus = std::make_shared<TaskStatus>();
            currentTaskStatus->taskId = task->taskId;
            currentTaskStatus->status = TaskStatus::PENDING;
            currentTaskStatus->progress = 0.0f;
            currentTaskStatus->startTime = getCurrentTimeString();
        }

        // 确保任务ID匹配
        if (currentTaskStatus->taskId != task->taskId) {
            // 如果是新任务，重置状态
            currentTaskStatus->taskId = task->taskId;
            currentTaskStatus->status = TaskStatus::PENDING;
            currentTaskStatus->progress = 0.0f;
            currentTaskStatus->startTime = getCurrentTimeString();
            currentTaskStatus->endTime.clear();
            currentTaskStatus->errorMessage.clear();
        }

        // 根据任务执行阶段更新进度
        updateProgressByExecutionPhase(task);

        // 更新任务统计信息
        updateTaskStatistics();

        // 检查任务是否完成
        checkTaskCompletion(task);

        // 记录进度更新日志
        notifyTaskLog("Task " + task->taskId + " progress updated: " +
                     std::to_string(static_cast<int>(currentTaskStatus->progress * 100)) + "%");

        stats.tasksProcessed++;

    } catch (const std::exception& e) {
        handleError("Failed to update task progress: " + std::string(e.what()));
        if (currentTaskStatus) {
            currentTaskStatus->status = TaskStatus::FAILED;
            currentTaskStatus->errorMessage = e.what();
            currentTaskStatus->endTime = getCurrentTimeString();
        }
    }
}

// 任务管理辅助方法实现
void SystemAdapter::generateTasksFromFeederStatus() {
    if (!feederManager) return;

    auto feederStatus = feederManager->getFeederStatus();

    for (auto& status : feederStatus) {
        if (!status) continue;

        // 检查每个料仓的重量
        for (int binId = 1; binId <= Constants::BINS_PER_FEEDER; binId++) {
            float currentWeight = 0.0f;
            float targetWeight = 0.0f;

            // 获取当前重量和目标重量（这里需要实际的重量获取逻辑）
            // 如果当前重量低于目标重量的80%，生成补料任务
            if (currentWeight < targetWeight * 0.8f) {
                auto task = std::make_shared<TaskInfo>();
                task->taskId = "AUTO_" + std::to_string(status->feederId) + "_" +
                              std::to_string(binId) + "_" +
                              std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                                  std::chrono::system_clock::now().time_since_epoch()).count());
                task->feederId = status->feederId;
                task->binId = binId;
                task->weight = targetWeight - currentWeight;
                task->minWeight = task->weight * 0.9f;
                task->maxWeight = task->weight * 1.1f;
                task->areaId = 'A' + (status->feederId - 1); // 简单的区域映射

                taskQueue.push(task);
                notifyTaskLog("Generated auto task: " + task->taskId +
                            " for low weight bin (current: " + std::to_string(currentWeight) +
                            "kg, target: " + std::to_string(targetWeight) + "kg)");
            }
        }
    }
}

std::shared_ptr<TaskInfo> SystemAdapter::findBestMatchingTask() {
    if (taskQueue.empty()) return nullptr;

    std::shared_ptr<TaskInfo> bestTask = nullptr;
    int highestPriority = -1;

    // 创建临时队列来遍历任务
    std::queue<std::shared_ptr<TaskInfo>> tempQueue = taskQueue;

    while (!tempQueue.empty()) {
        auto task = tempQueue.front();
        tempQueue.pop();

        int priority = calculateTaskPriority(task);
        if (priority > highestPriority) {
            highestPriority = priority;
            bestTask = task;
        }
    }

    return bestTask;
}

int SystemAdapter::calculateTaskPriority(std::shared_ptr<TaskInfo> task) {
    int priority = 0;

    // 基础优先级
    priority += 10;

    // 如果是紧急任务（重量很大），提高优先级
    if (task->weight > task->maxWeight * 0.9f) {
        priority += 50; // 紧急任务
    }

    // 如果投料机当前连接正常，提高优先级
    if (feederManager) {
        auto feederStatus = feederManager->getFeederStatus();
        for (auto& status : feederStatus) {
            if (status && status->feederId == task->feederId && status->isConnected) {
                priority += 20;
                break;
            }
        }
    }

    // 如果机器人当前连接正常，提高优先级
    if (abbClient && abbClient->isRobotConnected()) {
        priority += 15;
    }

    return priority;
}

void SystemAdapter::removeTaskFromQueue(const std::string& taskId) {
    std::queue<std::shared_ptr<TaskInfo>> newQueue;

    while (!taskQueue.empty()) {
        auto task = taskQueue.front();
        taskQueue.pop();

        if (task->taskId != taskId) {
            newQueue.push(task);
        }
    }

    taskQueue = newQueue;
}

// 完整投料流程实现
bool SystemAdapter::executeCompleteFeederTask(std::shared_ptr<TaskInfo> task) {
    notifyTaskLog("Starting complete feeder task execution for task: " + task->taskId);

    try {
        // 步骤1: 下发任务给机器人
        notifyTaskLog("Step 1: Sending task to ABB robot");
        if (!abbClient->sendTask(*task)) {
            handleError("Failed to send task to ABB robot");
            return false;
        }

        // 步骤2: 等待机器人到达投料点
        notifyTaskLog("Step 2: Waiting for robot to reach feeding position");
        if (!waitForRobotAtPosition(task)) {
            handleError("Robot failed to reach feeding position");
            return false;
        }

        // 步骤3: 机器人到达后，开始投料过程
        notifyTaskLog("Step 3: Starting feeding process");
        if (!startFeedingProcess(task)) {
            handleError("Failed to start feeding process");
            return false;
        }

        // 步骤4: 监控投料重量直到达到预定重量
        notifyTaskLog("Step 4: Monitoring feeding weight");
        if (!monitorFeedingWeight(task)) {
            handleError("Feeding weight monitoring failed");
            return false;
        }

        // 步骤5: 完成投料，机器人放桶回原位
        notifyTaskLog("Step 5: Completing feeding task");
        if (!completeFeedingTask(task)) {
            handleError("Failed to complete feeding task");
            return false;
        }

        // 步骤6: 检查桶重量，决定是否需要抓下一个桶
        notifyTaskLog("Step 6: Checking bucket weight for next task");
        if (!checkBucketWeightAndScheduleNext(task)) {
            notifyTaskLog("Warning: Bucket weight check failed, but task completed");
        }

        notifyTaskLog("Complete feeder task executed successfully: " + task->taskId);
        return true;

    } catch (const std::exception& e) {
        handleError("Exception in executeCompleteFeederTask: " + std::string(e.what()));
        return false;
    }
}

bool SystemAdapter::waitForRobotAtPosition(std::shared_ptr<TaskInfo> task) {
    const int MAX_WAIT_TIME_SECONDS = 300; // 最大等待5分钟
    const int CHECK_INTERVAL_MS = 500;     // 每500ms检查一次

    auto startTime = std::chrono::steady_clock::now();

    while (true) {
        // 检查超时
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(
            currentTime - startTime).count();

        if (elapsedSeconds >= MAX_WAIT_TIME_SECONDS) {
            handleError("Timeout waiting for robot to reach position");
            return false;
        }

        // 获取机器人当前任务状态
        auto robotStatus = abbClient->getCurrentTaskStatus();
        if (!robotStatus) {
            handleError("Failed to get robot task status");
            return false;
        }

        // 检查机器人是否到达目标位置
        if (robotStatus->status == TaskStatus::RUNNING &&
            robotStatus->currentStep == "AT_FEEDING_POSITION") {
            notifyTaskLog("Robot reached feeding position for area " +
                         std::string(1, task->areaId));
            return true;
        }

        // 检查机器人是否出错
        if (robotStatus->status == TaskStatus::FAILED) {
            handleError("Robot task failed: " + robotStatus->errorMessage);
            return false;
        }

        // 等待一段时间后再检查
        std::this_thread::sleep_for(std::chrono::milliseconds(CHECK_INTERVAL_MS));
    }
}

bool SystemAdapter::startFeedingProcess(std::shared_ptr<TaskInfo> task) {
    notifyTaskLog("Starting feeding process for feeder " +
                 std::to_string(task->feederId) + ", bin " + std::to_string(task->binId));

    // 检查投料机是否可用
    if (!feederManager) {
        handleError("Feeder manager not available");
        return false;
    }

    // 检查投料机是否连接
    if (!feederManager->isFeederConnected(task->feederId)) {
        handleError("Feeder " + std::to_string(task->feederId) + " not connected");
        return false;
    }

    // 检查投料机是否启用
    if (!feederManager->isFeederEnabled(task->feederId)) {
        handleError("Feeder " + std::to_string(task->feederId) + " not enabled");
        return false;
    }

    // 检查料仓是否启用
    if (!feederManager->isBinEnabled(task->feederId, task->binId)) {
        handleError("Bin " + std::to_string(task->binId) + " on feeder " +
                   std::to_string(task->feederId) + " not enabled");
        return false;
    }

    // 设置目标重量（转换为0-based索引）
    if (!feederManager->setBinTargetWeight(task->feederId - 1, task->binId - 1, task->weight)) {
        handleError("Failed to set target weight for feeder " + std::to_string(task->feederId) +
                   ", bin " + std::to_string(task->binId));
        return false;
    }

    // 启动投料（转换为0-based索引）
    if (!feederManager->startFeeding(task->feederId - 1, task->binId - 1)) {
        handleError("Failed to start feeding for feeder " + std::to_string(task->feederId) +
                   ", bin " + std::to_string(task->binId));
        return false;
    }

    notifyTaskLog("Feeding started successfully for feeder " +
                 std::to_string(task->feederId) + ", bin " + std::to_string(task->binId));
    return true;
}

bool SystemAdapter::monitorFeedingWeight(std::shared_ptr<TaskInfo> task) {
    const int MAX_FEEDING_TIME_SECONDS = 600; // 最大投料时间10分钟
    const int CHECK_INTERVAL_MS = 1000;       // 每1秒检查一次重量
    const float WEIGHT_TOLERANCE = 0.05f;     // 重量容差5%

    auto startTime = std::chrono::steady_clock::now();
    float initialWeight = 0.0f;
    float currentWeight = 0.0f;
    float accumulatedWeight = 0.0f;

    // 获取投料开始前的初始重量（桶中的重量）（转换为0-based索引）
    if (!feederManager->getBinWeight(task->feederId - 1, task->binId - 1, initialWeight)) {
        handleError("Failed to get initial bin weight");
        return false;
    }

    notifyTaskLog("Starting weight monitoring - Initial weight: " +
                 std::to_string(initialWeight) + "kg, Target additional weight: " +
                 std::to_string(task->weight) + "kg");

    while (true) {
        // 检查超时
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(
            currentTime - startTime).count();

        if (elapsedSeconds >= MAX_FEEDING_TIME_SECONDS) {
            handleError("Timeout during feeding process - stopping feeding");
            feederManager->stopFeeding(task->feederId - 1, task->binId - 1);
            return false;
        }

        // 获取当前总重量（桶+投料重量）（转换为0-based索引）
        if (!feederManager->getBinWeight(task->feederId - 1, task->binId - 1, currentWeight)) {
            handleError("Failed to get bin weight during feeding");
            continue; // 继续尝试，不立即失败
        }

        // 计算已投料的重量（当前总重量 - 初始重量）
        accumulatedWeight = currentWeight - initialWeight;

        // 检查投料重量是否在合理范围内增长
        if (accumulatedWeight < 0.0f) { // 投料重量不应该为负数
            handleError("Warning: Accumulated weight is negative: " +
                       std::to_string(accumulatedWeight) + "kg - possible sensor error");
        }

        // 检查是否达到目标投料重量
        float weightDifference = std::abs(accumulatedWeight - task->weight);
        float tolerance = task->weight * WEIGHT_TOLERANCE;

        if (weightDifference <= tolerance) {
            notifyTaskLog("Target feeding weight reached - Accumulated: " +
                         std::to_string(accumulatedWeight) + "kg (target: " +
                         std::to_string(task->weight) + "kg, total weight: " +
                         std::to_string(currentWeight) + "kg)");

            // 停止投料（转换为0-based索引）
            if (!feederManager->stopFeeding(task->feederId - 1, task->binId - 1)) {
                handleError("Failed to stop feeding after reaching target weight");
                return false;
            }

            notifyTaskLog("Feeding stopped successfully");
            return true;
        }

        // 检查是否超过最大投料重量
        if (accumulatedWeight > task->maxWeight) {
            handleError("Feeding weight exceeded maximum limit - Accumulated: " +
                       std::to_string(accumulatedWeight) + "kg (max: " +
                       std::to_string(task->maxWeight) + "kg)");

            // 立即停止投料（转换为0-based索引）
            feederManager->stopFeeding(task->feederId - 1, task->binId - 1);
            return false;
        }

        // 记录投料进度
        static float lastReportedWeight = 0.0f;
        if (std::abs(accumulatedWeight - lastReportedWeight) > 1.0f) { // 每增加1kg记录一次
            float progress = (accumulatedWeight / task->weight) * 100.0f;
            notifyTaskLog("Feeding progress - Accumulated: " + std::to_string(accumulatedWeight) +
                         "kg, Target: " + std::to_string(task->weight) + "kg (" +
                         std::to_string(progress) + "%), Total weight: " +
                         std::to_string(currentWeight) + "kg");
            lastReportedWeight = accumulatedWeight;
        }

        // 等待下次检查
        std::this_thread::sleep_for(std::chrono::milliseconds(CHECK_INTERVAL_MS));
    }
}

bool SystemAdapter::completeFeedingTask(std::shared_ptr<TaskInfo> task) {
    notifyTaskLog("Completing feeding task - instructing robot to return bucket");

    // 通知机器人投料完成，可以放桶回原位
    if (!abbClient->sendCommand("FEEDING_COMPLETE", task->taskId)) {
        handleError("Failed to send feeding complete command to robot");
        return false;
    }

    // 等待机器人确认并完成放桶动作
    const int MAX_WAIT_TIME_SECONDS = 180; // 最大等待3分钟
    const int CHECK_INTERVAL_MS = 1000;    // 每1秒检查一次

    auto startTime = std::chrono::steady_clock::now();

    while (true) {
        // 检查超时
        auto currentTime = std::chrono::steady_clock::now();
        auto elapsedSeconds = std::chrono::duration_cast<std::chrono::seconds>(
            currentTime - startTime).count();

        if (elapsedSeconds >= MAX_WAIT_TIME_SECONDS) {
            handleError("Timeout waiting for robot to complete bucket return");
            return false;
        }

        // 获取机器人任务状态
        auto robotStatus = abbClient->getCurrentTaskStatus();
        if (!robotStatus) {
            handleError("Failed to get robot status during task completion");
            return false;
        }

        // 检查任务是否完成
        if (robotStatus->status == TaskStatus::COMPLETED) {
            notifyTaskLog("Robot completed bucket return successfully");
            return true;
        }

        // 检查任务是否失败
        if (robotStatus->status == TaskStatus::FAILED) {
            handleError("Robot failed to complete bucket return: " + robotStatus->errorMessage);
            return false;
        }

        // 检查机器人是否在执行放桶动作
        if (robotStatus->status == TaskStatus::RUNNING &&
            robotStatus->currentStep == "RETURNING_BUCKET") {
            notifyTaskLog("Robot is returning bucket to original position");
        }

        // 等待下次检查
        std::this_thread::sleep_for(std::chrono::milliseconds(CHECK_INTERVAL_MS));
    }
}

bool SystemAdapter::checkBucketWeightAndScheduleNext(std::shared_ptr<TaskInfo> task) {
    notifyTaskLog("Checking area bucket weight to determine if next bucket is needed");

    try {
        // 获取区域内当前桶的剩余重量（通过区域数据获取）
        float currentBucketWeight = 0.0f;

        // 从缓存的区域数据中获取当前桶的重量
        {
            std::lock_guard<std::mutex> lock(areaMutex);
            auto areaIt = cachedAreaData.find(task->areaId);
            if (areaIt != cachedAreaData.end() && areaIt->second) {
                auto areaData = areaIt->second;
                if (task->bucketId >= 0 && task->bucketId < static_cast<int>(areaData->buckets.size())) {
                    currentBucketWeight = areaData->buckets[task->bucketId].weight;
                } else {
                    handleError("Invalid bucket ID: " + std::to_string(task->bucketId));
                    return false;
                }
            } else {
                handleError("Area data not found for area: " + std::string(1, task->areaId));
                return false;
            }
        }

        // 定义重量下限阈值（可以从配置中获取）
        const float BUCKET_MIN_WEIGHT_THRESHOLD = 50.0f; // 50kg以下认为需要更换桶

        notifyTaskLog("Current bucket weight in area " + std::string(1, task->areaId) +
                     ", bucket " + std::to_string(task->bucketId) + ": " +
                     std::to_string(currentBucketWeight) + "kg (threshold: " +
                     std::to_string(BUCKET_MIN_WEIGHT_THRESHOLD) + "kg)");

        if (currentBucketWeight <= BUCKET_MIN_WEIGHT_THRESHOLD) {
            notifyTaskLog("Bucket weight below threshold - scheduling next bucket task");

            // 创建下一个桶的任务
            auto nextTask = std::make_shared<TaskInfo>();
            nextTask->taskId = "NEXT_BUCKET_" + std::to_string(task->bucketId + 1) + "_" +
                              std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                                  std::chrono::system_clock::now().time_since_epoch()).count());
            nextTask->areaId = task->areaId;
            nextTask->bucketId = task->bucketId + 1; // 下一个桶
            nextTask->feederId = task->feederId;
            nextTask->binId = task->binId;
            nextTask->weight = task->weight; // 相同的投料重量
            nextTask->minWeight = task->minWeight;
            nextTask->maxWeight = task->maxWeight;
            nextTask->batchNumber = generateNextBatchNumber(task->batchNumber);

            // 将下一个任务加入队列
            {
                std::lock_guard<std::mutex> lock(taskQueueMutex);
                taskQueue.push(nextTask);
            }

            notifyTaskLog("Next bucket task scheduled: " + nextTask->taskId +
                         " (bucket " + std::to_string(nextTask->bucketId) + ")");

            // 通知机器人准备抓取下一个桶
            if (!abbClient->sendCommand("PREPARE_NEXT_BUCKET",
                                       std::to_string(task->areaId) + ":" +
                                       std::to_string(nextTask->bucketId))) {
                handleError("Failed to notify robot about next bucket preparation");
                // 不返回false，因为任务已经加入队列，机器人可以稍后处理
            }

        } else {
            notifyTaskLog("Bucket weight sufficient - no next bucket needed at this time");
        }

        return true;

    } catch (const std::exception& e) {
        handleError("Exception in checkBucketWeightAndScheduleNext: " + std::string(e.what()));
        return false;
    }
}

std::string SystemAdapter::generateNextBatchNumber(const std::string& currentBatch) {
    // 简单的批号生成逻辑，实际应用中可能需要更复杂的逻辑
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << "BATCH_" << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");

    return ss.str();
}

// 公共投料控制接口实现
bool SystemAdapter::startFeeding(int feederId, int binId) {
    if (!feederManager) {
        handleError("Feeder manager not available");
        return false;
    }

    // 验证参数（1-based）
    if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
        handleError("Invalid feeder ID: " + std::to_string(feederId));
        return false;
    }

    if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
        handleError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 转换为0-based索引并调用FeederManager
    return feederManager->startFeeding(feederId - 1, binId - 1);
}

bool SystemAdapter::stopFeeding(int feederId, int binId) {
    if (!feederManager) {
        handleError("Feeder manager not available");
        return false;
    }

    // 验证参数（1-based）
    if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
        handleError("Invalid feeder ID: " + std::to_string(feederId));
        return false;
    }

    if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
        handleError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    // 转换为0-based索引并调用FeederManager
    return feederManager->stopFeeding(feederId - 1, binId - 1);
}

bool SystemAdapter::stopAllFeeding() {
    if (!feederManager) {
        handleError("Feeder manager not available");
        return false;
    }

    return feederManager->stopAllFeeding();
}

bool SystemAdapter::setBinTargetWeight(int feederId, int binId, float targetWeight) {
    if (!feederManager) {
        handleError("Feeder manager not available");
        return false;
    }

    // 验证参数（1-based）
    if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
        handleError("Invalid feeder ID: " + std::to_string(feederId));
        return false;
    }

    if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
        handleError("Invalid bin ID: " + std::to_string(binId));
        return false;
    }

    if (targetWeight < 0.0f) {
        handleError("Invalid target weight: " + std::to_string(targetWeight));
        return false;
    }

    // 转换为0-based索引并调用FeederManager
    return feederManager->setBinTargetWeight(feederId - 1, binId - 1, targetWeight);
}

bool SystemAdapter::getBinWeight(int feederId, int binId, float& weight) {
    if (!feederManager) {
        handleError("Feeder manager not available");
        weight = 0.0f;
        return false;
    }

    // 验证参数（1-based）
    if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
        handleError("Invalid feeder ID: " + std::to_string(feederId));
        weight = 0.0f;
        return false;
    }

    if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
        handleError("Invalid bin ID: " + std::to_string(binId));
        weight = 0.0f;
        return false;
    }

    // 转换为0-based索引并调用FeederManager
    return feederManager->getBinWeight(feederId - 1, binId - 1, weight);
}

/**
 * 根据任务执行阶段更新进度
 * @param task 任务信息
 */
void SystemAdapter::updateProgressByExecutionPhase(std::shared_ptr<TaskInfo> task) {
    if (!currentTaskStatus || !task) return;

    // 获取机器人状态来判断执行阶段
    auto robotStatus = abbClient ? abbClient->getCurrentTaskStatus() : nullptr;

    if (robotStatus && robotStatus->taskId == task->taskId) {
        // 根据机器人任务状态更新进度
        switch (robotStatus->status) {
            case TaskStatus::PENDING:
                currentTaskStatus->progress = 0.0f;
                currentTaskStatus->currentStep = "等待开始";
                currentTaskStatus->status = TaskStatus::PENDING;
                break;

            case TaskStatus::RUNNING:
                currentTaskStatus->status = TaskStatus::RUNNING;
                // 根据当前步骤细分进度
                if (robotStatus->currentStep == "MOVING_TO_POSITION") {
                    currentTaskStatus->progress = 0.1f;
                    currentTaskStatus->currentStep = "移动到投料位置";
                } else if (robotStatus->currentStep == "AT_FEEDING_POSITION") {
                    currentTaskStatus->progress = 0.3f;
                    currentTaskStatus->currentStep = "到达投料位置";
                } else if (robotStatus->currentStep == "FEEDING") {
                    currentTaskStatus->progress = 0.6f;
                    currentTaskStatus->currentStep = "正在投料";
                } else if (robotStatus->currentStep == "RETURNING_BUCKET") {
                    currentTaskStatus->progress = 0.9f;
                    currentTaskStatus->currentStep = "归还料桶";
                } else {
                    currentTaskStatus->progress = 0.5f;
                    currentTaskStatus->currentStep = robotStatus->currentStep;
                }
                break;

            case TaskStatus::COMPLETED:
                currentTaskStatus->progress = 1.0f;
                currentTaskStatus->currentStep = "任务完成";
                currentTaskStatus->status = TaskStatus::COMPLETED;
                currentTaskStatus->endTime = getCurrentTimeString();
                break;

            case TaskStatus::FAILED:
                currentTaskStatus->status = TaskStatus::FAILED;
                currentTaskStatus->currentStep = "任务失败";
                currentTaskStatus->errorMessage = robotStatus->errorMessage;
                currentTaskStatus->endTime = getCurrentTimeString();
                break;

            case TaskStatus::CANCELLED:
                currentTaskStatus->status = TaskStatus::CANCELLED;
                currentTaskStatus->currentStep = "任务取消";
                currentTaskStatus->endTime = getCurrentTimeString();
                break;
        }
    } else {
        // 如果没有机器人状态，根据投料机状态估算进度
        if (feederManager) {
            auto feederStatus = feederManager->getFeederStatus(task->feederId);
            if (feederStatus && feederStatus->isConnected) {
                // 根据投料机状态更新进度
                currentTaskStatus->status = TaskStatus::RUNNING;
                currentTaskStatus->progress = 0.3f; // 基础进度
                currentTaskStatus->currentStep = "投料机准备中";
            }
        }
    }
}

/**
 * 更新任务统计信息
 */
void SystemAdapter::updateTaskStatistics() {
    if (!currentTaskStatus) return;

    // 统计任务队列中的任务状态
    std::lock_guard<std::mutex> lock(taskQueueMutex);

    int readyTasks = 0;
    int executingTasks = 0;
    int completedTasks = 0;
    float totalRemainingWeight = 0.0f;

    // 计算队列中的任务统计
    std::queue<std::shared_ptr<TaskInfo>> tempQueue = taskQueue;
    while (!tempQueue.empty()) {
        auto task = tempQueue.front();
        tempQueue.pop();

        if (task) {
            readyTasks++;
            totalRemainingWeight += task->weight;
        }
    }

    // 当前正在执行的任务
    if (currentTaskStatus->status == TaskStatus::RUNNING) {
        executingTasks = 1;
    } else if (currentTaskStatus->status == TaskStatus::COMPLETED) {
        completedTasks = 1;
    }

    // 更新统计信息
    currentTaskStatus->readyTasks = readyTasks;
    currentTaskStatus->executingTasks = executingTasks;
    currentTaskStatus->completedTasks = completedTasks;
    currentTaskStatus->remainingWeight = totalRemainingWeight;
}

/**
 * 检查任务是否完成
 * @param task 任务信息
 */
void SystemAdapter::checkTaskCompletion(std::shared_ptr<TaskInfo> task) {
    if (!currentTaskStatus || !task) return;

    // 检查任务是否已经标记为完成
    if (currentTaskStatus->status == TaskStatus::COMPLETED) {
        // 任务完成后的清理工作
        notifyTaskLog("Task " + task->taskId + " completed successfully");

        // 可以在这里添加任务完成后的回调或通知
        if (taskLogCallback) {
            taskLogCallback("Task completed: " + task->taskId +
                           " (Weight: " + std::to_string(task->weight) + "kg)");
        }

        return;
    }

    // 检查任务是否失败
    if (currentTaskStatus->status == TaskStatus::FAILED) {
        handleError("Task " + task->taskId + " failed: " + currentTaskStatus->errorMessage);
        return;
    }

    // 检查任务超时（可选）
    if (!currentTaskStatus->startTime.empty()) {
        // 这里可以添加超时检查逻辑
        // 例如：如果任务运行时间超过预设时间，标记为超时
    }
}

/**
 * 获取当前时间字符串
 * @return 格式化的时间字符串
 */
std::string SystemAdapter::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();

    return ss.str();
}
