#include "feeder_manager.h"
#include "feeder_client.h"
#include "Executor.h"
#include <iostream>
#include <chrono>
#include <algorithm>

FeederManager::FeederManager() : running(false) {
    // 创建执行器
    executor = std::make_shared<Fuxi::Common::Executor>(3);
    
    // 设置统计开始时间
    stats.startTime = std::chrono::steady_clock::now();
}

FeederManager::~FeederManager() {
    stop();
}

bool FeederManager::initialize(const std::vector<FeederConfig>& configs) {
    if (running) {
        notifyError("Cannot initialize while running");
        return false;
    }
    
    this->configs = configs;
    stats.totalFeeders = configs.size();
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    // 清理现有的投料机客户端
    feeders.clear();
    
    // 创建投料机客户端
    for (const auto& config : configs) {
        if (!createFeederClient(config)) {
            notifyError("Failed to create feeder client for feeder " + std::to_string(config.id));
            return false;
        }
    }
    
    // 初始化状态缓存
    cachedStatus.clear();
    cachedStatus.resize(configs.size());
    
    return true;
}

bool FeederManager::start() {
    if (running) {
        return true;
    }
    
    // 连接所有投料机
    if (!connectAllFeeders()) {
        notifyError("Failed to connect all feeders");
        return false;
    }
    
    running = true;
    
    // 启动监控循环
    executor->postTask([this]() { statusMonitorLoop(); });
    executor->postTask([this]() { healthCheckLoop(); });
    
    notifyFeederEvent(-1, "FeederManager started");
    return true;
}

void FeederManager::stop() {
    if (!running) {
        return;
    }
    
    running = false;
    
    // 断开所有投料机连接
    disconnectAllFeeders();
    
    notifyFeederEvent(-1, "FeederManager stopped");
}

bool FeederManager::connectAllFeeders() {
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    bool allConnected = true;
    stats.connectedFeeders = 0;
    
    for (auto& pair : feeders) {
        auto& client = pair.second;
        if (client && client->connect() && client->start()) {
            stats.connectedFeeders++;
            notifyFeederEvent(pair.first, "Connected");
        } else {
            allConnected = false;
            notifyFeederEvent(pair.first, "Connection failed");
        }
    }
    
    updateStatistics();
    return allConnected;
}

void FeederManager::disconnectAllFeeders() {
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    for (auto& pair : feeders) {
        auto& client = pair.second;
        if (client) {
            client->stop();
            client->disconnect();
            notifyFeederEvent(pair.first, "Disconnected");
        }
    }
    
    stats.connectedFeeders = 0;
    updateStatistics();
}

bool FeederManager::connectFeeder(int feederId) {
    if (!validateFeederId(feederId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        bool result = it->second->connect() && it->second->start();
        if (result) {
            stats.connectedFeeders++;
            notifyFeederEvent(feederId, "Connected");
            updateStatistics();
        }
        return result;
    }
    
    return false;
}

void FeederManager::disconnectFeeder(int feederId) {
    if (!validateFeederId(feederId)) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        it->second->stop();
        it->second->disconnect();
        stats.connectedFeeders--;
        notifyFeederEvent(feederId, "Disconnected");
        updateStatistics();
    }
}

std::vector<std::shared_ptr<FeederStatus>> FeederManager::getFeederStatus() {
    std::lock_guard<std::mutex> lock(statusMutex);
    
    // 如果缓存太旧，更新状态
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastStatusUpdate);
    
    if (elapsed.count() > Constants::STATUS_UPDATE_INTERVAL) {
        updateStatusCache();
    }
    
    return cachedStatus;
}

std::shared_ptr<FeederStatus> FeederManager::getFeederStatus(int feederId) {
    if (!validateFeederId(feederId)) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->getFeederStatus();
    }
    
    return nullptr;
}

bool FeederManager::isFeederConnected(int feederId) const {
    if (!validateFeederId(feederId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->isConnected();
    }
    
    return false;
}

bool FeederManager::isFeederEnabled(int feederId) const {
    // 检查投料机是否启用（基于配置）
    auto configIt = std::find_if(configs.begin(), configs.end(),
        [feederId](const FeederConfig& config) { return config.id == feederId; });
    
    if (configIt != configs.end()) {
        return configIt->enabled && isFeederConnected(feederId);
    }
    
    return false;
}

bool FeederManager::isBinEnabled(int feederId, int binId) const {
    if (!validateFeederId(feederId) || !validateBinId(binId)) {
        return false;
    }
    
    // 检查投料机是否启用
    if (!isFeederEnabled(feederId)) {
        return false;
    }
    
    // 检查料仓是否被禁止
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        auto status = it->second->getFeederStatus();
        if (status && binId < status->bins.size()) {
            return !status->bins[binId].isBlocked;
        }
    }
    
    return false;
}

bool FeederManager::isHeartbeatOK() const {
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    for (const auto& pair : feeders) {
        if (pair.second && pair.second->isConnected()) {
            if (!pair.second->isHeartbeatOK()) {
                return false;
            }
        }
    }
    
    return true;
}

bool FeederManager::hasError() const {
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    for (const auto& pair : feeders) {
        if (pair.second && pair.second->isConnected()) {
            if (!pair.second->getLastError().empty()) {
                return true;
            }
        }
    }
    
    return false;
}

bool FeederManager::getBinWeight(int feederId, int binId, float& weight) {
    if (!validateFeederId(feederId) || !validateBinId(binId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->getBinWeight(binId, weight);
    }
    
    return false;
}

bool FeederManager::setBinTargetWeight(int feederId, int binId, float targetWeight) {
    if (!validateFeederId(feederId) || !validateBinId(binId) || !validateWeight(targetWeight)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->setBinTargetWeight(binId, targetWeight);
    }
    
    return false;
}

bool FeederManager::startFeeding(int feederId, int binId) {
    if (!validateFeederId(feederId) || !validateBinId(binId)) {
        return false;
    }
    
    if (!isBinEnabled(feederId, binId)) {
        notifyError("Bin " + std::to_string(binId) + " on feeder " + std::to_string(feederId) + " is not enabled");
        return false;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        bool result = it->second->startFeeding(binId);
        if (result) {
            notifyFeederEvent(feederId, "Started feeding bin " + std::to_string(binId));
        }
        return result;
    }
    
    return false;
}

bool FeederManager::stopFeeding(int feederId, int binId) {
    if (!validateFeederId(feederId) || !validateBinId(binId)) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        bool result = it->second->stopFeeding(binId);
        if (result) {
            notifyFeederEvent(feederId, "Stopped feeding bin " + std::to_string(binId));
        }
        return result;
    }
    
    return false;
}

bool FeederManager::stopAllFeeding() {
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    bool allStopped = true;
    
    for (auto& pair : feeders) {
        if (pair.second && pair.second->isConnected()) {
            if (!pair.second->stopAllFeeding()) {
                allStopped = false;
            } else {
                notifyFeederEvent(pair.first, "Stopped all feeding");
            }
        }
    }
    
    return allStopped;
}

bool FeederManager::emergencyStop() {
    std::lock_guard<std::mutex> lock(feedersMutex);
    
    bool allStopped = true;
    
    for (auto& pair : feeders) {
        if (pair.second && pair.second->isConnected()) {
            if (!pair.second->emergencyStop()) {
                allStopped = false;
            } else {
                notifyFeederEvent(pair.first, "Emergency stop activated");
            }
        }
    }
    
    if (allStopped) {
        notifyFeederEvent(-1, "Emergency stop activated for all feeders");
    }
    
    return allStopped;
}

bool FeederManager::reset() {
    // 重置所有投料机
    disconnectAllFeeders();
    std::this_thread::sleep_for(std::chrono::seconds(2));

    bool result = connectAllFeeders();
    if (result) {
        notifyFeederEvent(-1, "All feeders reset successfully");
    } else {
        notifyError("Failed to reset all feeders");
    }

    return result;
}

void FeederManager::statusMonitorLoop() {
    while (running) {
        try {
            updateStatusCache();
            notifyStatusUpdate();
        } catch (const std::exception& e) {
            notifyError("Status monitor exception: " + std::string(e.what()));
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(Constants::STATUS_UPDATE_INTERVAL));
    }
}

void FeederManager::healthCheckLoop() {
    while (running) {
        try {
            performHealthCheck();
        } catch (const std::exception& e) {
            notifyError("Health check exception: " + std::string(e.what()));
        }

        std::this_thread::sleep_for(std::chrono::seconds(30)); // 每30秒检查一次
    }
}

bool FeederManager::createFeederClient(const FeederConfig& config) {
    try {
        auto client = std::make_shared<FeederClient>(config);

        // 设置回调
        client->setErrorCallback([this, feederId = config.id](const std::string& error) {
            handleFeederError(feederId, error);
        });

        feeders[config.id] = client;
        return true;

    } catch (const std::exception& e) {
        notifyError("Failed to create feeder client " + std::to_string(config.id) + ": " + e.what());
        return false;
    }
}

void FeederManager::removeFeederClient(int feederId) {
    std::lock_guard<std::mutex> lock(feedersMutex);

    auto it = feeders.find(feederId);
    if (it != feeders.end()) {
        if (it->second) {
            it->second->stop();
            it->second->disconnect();
        }
        feeders.erase(it);
    }
}

void FeederManager::updateStatistics() {
    stats.connectedFeeders = 0;
    stats.activeFeeders = 0;

    for (const auto& pair : feeders) {
        if (pair.second) {
            if (pair.second->isConnected()) {
                stats.connectedFeeders++;
            }
            if (pair.second->isRunning()) {
                stats.activeFeeders++;
            }
        }
    }
}

void FeederManager::updateStatusCache() {
    std::lock_guard<std::mutex> statusLock(statusMutex);
    std::lock_guard<std::mutex> feedersLock(feedersMutex);

    cachedStatus.clear();

    for (const auto& pair : feeders) {
        if (pair.second) {
            auto status = pair.second->getFeederStatus();
            if (status) {
                cachedStatus.push_back(status);
            }
        }
    }

    lastStatusUpdate = std::chrono::steady_clock::now();
}

void FeederManager::handleFeederError(int feederId, const std::string& error) {
    stats.totalErrors++;

    std::string fullError = "Feeder " + std::to_string(feederId) + " error: " + error;
    notifyError(fullError);

    // 尝试重连
    if (shouldAttemptReconnect(feederId)) {
        executor->postTask([this, feederId]() {
            attemptReconnection(feederId);
        });
    }
}

void FeederManager::notifyError(const std::string& error) {
    if (errorCallback) {
        errorCallback(error);
    }
}

void FeederManager::notifyFeederEvent(int feederId, const std::string& event) {
    if (feederEventCallback) {
        feederEventCallback(feederId, event);
    }
}

void FeederManager::notifyStatusUpdate() {
    if (statusCallback) {
        statusCallback(cachedStatus);
    }
}

bool FeederManager::validateFeederId(int feederId) const {
    return feederId >= 0 && feederId < Constants::FEEDERS_COUNT;
}

bool FeederManager::validateBinId(int binId) const {
    return binId >= 0 && binId < Constants::BINS_PER_FEEDER;
}

bool FeederManager::validateWeight(float weight) const {
    return weight >= 0.0f && weight <= 10000.0f;
}

void FeederManager::attemptReconnection(int feederId) {
    if (!running) {
        return;
    }

    std::this_thread::sleep_for(std::chrono::seconds(5)); // 等待5秒后重连

    if (connectFeeder(feederId)) {
        stats.totalReconnects++;
        notifyFeederEvent(feederId, "Reconnected successfully");
    } else {
        notifyFeederEvent(feederId, "Reconnection failed");
    }
}

bool FeederManager::shouldAttemptReconnect(int feederId) const {
    // 简单的重连策略：如果投料机配置为启用，则尝试重连
    auto configIt = std::find_if(configs.begin(), configs.end(),
        [feederId](const FeederConfig& config) { return config.id == feederId; });

    return configIt != configs.end() && configIt->enabled;
}

bool FeederManager::performHealthCheck() {
    bool allHealthy = true;

    std::lock_guard<std::mutex> lock(feedersMutex);

    for (const auto& pair : feeders) {
        if (pair.second) {
            if (!pair.second->isConnected() || !pair.second->isHeartbeatOK()) {
                allHealthy = false;
                notifyFeederEvent(pair.first, "Health check failed");
            }
        }
    }

    return allHealthy;
}

std::vector<std::string> FeederManager::getDiagnosticInfo() {
    std::vector<std::string> info;

    info.push_back("=== Feeder Manager Diagnostics ===");
    info.push_back("Total Feeders: " + std::to_string(stats.totalFeeders));
    info.push_back("Connected Feeders: " + std::to_string(stats.connectedFeeders));
    info.push_back("Active Feeders: " + std::to_string(stats.activeFeeders));
    info.push_back("Total Errors: " + std::to_string(stats.totalErrors));
    info.push_back("Total Reconnects: " + std::to_string(stats.totalReconnects));
    info.push_back("Running: " + std::string(running ? "Yes" : "No"));

    std::lock_guard<std::mutex> lock(feedersMutex);

    for (const auto& pair : feeders) {
        if (pair.second) {
            auto status = pair.second->getFeederStatus();
            if (status) {
                info.push_back("Feeder " + std::to_string(pair.first) + ": " +
                              (status->isConnected ? "Connected" : "Disconnected") + ", " +
                              (status->heartbeatOK ? "Heartbeat OK" : "Heartbeat Failed"));
            }
        }
    }

    return info;
}

bool FeederManager::calibrateFeeder(int feederId) {
    if (!validateFeederId(feederId)) {
        return false;
    }

    // 校准投料机的实现
    // 这里可以添加具体的校准逻辑
    notifyFeederEvent(feederId, "Calibration completed");
    return true;
}

// 批量操作的实现
bool FeederManager::setAllBinTargetWeights(int feederId, const std::vector<float>& targetWeights) {
    if (!validateFeederId(feederId)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(feedersMutex);

    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->setAllBinTargetWeights(targetWeights);
    }

    return false;
}

bool FeederManager::setAllBinMinWeights(int feederId, const std::vector<float>& minWeights) {
    if (!validateFeederId(feederId)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(feedersMutex);

    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->setAllBinMinWeights(minWeights);
    }

    return false;
}

bool FeederManager::getAllBinWeights(int feederId, std::vector<float>& weights) {
    if (!validateFeederId(feederId)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(feedersMutex);

    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->getAllBinWeights(weights);
    }

    return false;
}

bool FeederManager::setBinMinWeight(int feederId, int binId, float minWeight) {
    if (!validateFeederId(feederId) || !validateBinId(binId) || !validateWeight(minWeight)) {
        return false;
    }

    std::lock_guard<std::mutex> lock(feedersMutex);

    auto it = feeders.find(feederId);
    if (it != feeders.end() && it->second) {
        return it->second->setBinMinWeight(binId, minWeight);
    }

    return false;
}

void FeederManager::updateConfig(const std::vector<FeederConfig>& newConfigs) {
    if (running) {
        stop();
        initialize(newConfigs);
        start();
    } else {
        initialize(newConfigs);
    }
}
