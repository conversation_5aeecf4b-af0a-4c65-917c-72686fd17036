#include "weight_calculator.h"
#include <algorithm>
#include <cmath>
#include <fstream>
#include <iostream>
#include <sstream>

WeightCalculator::WeightCalculator() {
    // 初始化默认校准系数
    for (int i = 1; i <= Constants::FEEDERS_COUNT; ++i) {
        feederCalibrationFactors[i] = 1.0f; // 默认无校准偏差
    }
}

WeightCalculator::~WeightCalculator() = default;

std::vector<WeightCalculator::FeedingSuggestion> WeightCalculator::calculateFeedingSuggestions(
    const std::vector<std::shared_ptr<FeederStatus>>& feederStatus,
    const std::map<int, std::map<int, float>>& targetWeights) {
    
    std::vector<FeedingSuggestion> suggestions;
    stats.totalCalculations++;
    
    try {
        for (const auto& status : feederStatus) {
            if (!status || !status->isConnected) {
                continue;
            }
            
            int feederId = status->feederId;
            auto feederTargets = targetWeights.find(feederId);
            if (feederTargets == targetWeights.end()) {
                continue;
            }
            
            for (const auto& binTarget : feederTargets->second) {
                int binId = binTarget.first;
                float targetWeight = binTarget.second;
                
                if (binId >= 0 && binId < status->bins.size()) {
                    const auto& bin = status->bins[binId];
                    
                    if (!bin.isBlocked && !bin.isFeeding) {
                        FeedingSuggestion suggestion;
                        
                        switch (params.strategy) {
                            case CalculationStrategy::SIMPLE:
                                suggestion = calculateSimpleStrategy(feederId, binId,
                                    bin.currentWeight, targetWeight, bin.minWeight, bin.maxWeight);
                                break;
                            case CalculationStrategy::BALANCED:
                                suggestion = calculateBalancedStrategy(feederId, binId,
                                    bin.currentWeight, targetWeight, bin.minWeight, bin.maxWeight, feederStatus);
                                break;
                            case CalculationStrategy::OPTIMIZED:
                                suggestion = calculateOptimizedStrategy(feederId, binId,
                                    bin.currentWeight, targetWeight, bin.minWeight, bin.maxWeight);
                                break;
                            case CalculationStrategy::SAFETY_FIRST:
                                suggestion = calculateSafetyFirstStrategy(feederId, binId,
                                    bin.currentWeight, targetWeight, bin.minWeight, bin.maxWeight);
                                break;
                        }
                        
                        // 安全检查
                        if (params.enableSafetyCheck) {
                            auto safetyCheck = performSafetyCheck(suggestion, status);
                            suggestion.isSafe = safetyCheck.passed;
                            if (!safetyCheck.passed) {
                                suggestion.reason += " (Safety: " + safetyCheck.message + ")";
                                stats.safetyViolations++;
                            }
                        }
                        
                        if (suggestion.suggestedWeight > 0.0f) {
                            suggestions.push_back(suggestion);
                            updateStatistics(suggestion, true);
                        }
                    }
                }
            }
        }
        
        // 负载均衡
        if (params.enableLoadBalancing && suggestions.size() > 1) {
            auto balancedSuggestions = balanceFeederLoads(feederStatus);
            // 合并建议（这里简化处理）
            for (auto& suggestion : suggestions) {
                for (const auto& balanced : balancedSuggestions) {
                    if (suggestion.feederId == balanced.feederId && suggestion.binId == balanced.binId) {
                        suggestion.priority = std::max(suggestion.priority, balanced.priority);
                        break;
                    }
                }
            }
        }
        
        // 按优先级排序
        std::sort(suggestions.begin(), suggestions.end(),
            [](const FeedingSuggestion& a, const FeedingSuggestion& b) {
                return a.priority > b.priority;
            });
        
    } catch (const std::exception& e) {
        std::cerr << "Error in calculateFeedingSuggestions: " << e.what() << std::endl;
    }
    
    stats.lastCalculationTime = std::chrono::steady_clock::now();
    return suggestions;
}

WeightCalculator::FeedingSuggestion WeightCalculator::calculateSingleBinFeeding(
    int feederId, int binId, float currentWeight, float targetWeight,
    float minWeight, float maxWeight) {
    
    stats.totalCalculations++;
    
    FeedingSuggestion suggestion;
    suggestion.feederId = feederId;
    suggestion.binId = binId;
    suggestion.currentWeight = currentWeight;
    suggestion.targetWeight = targetWeight;
    suggestion.minWeight = minWeight;
    suggestion.maxWeight = maxWeight;
    
    switch (params.strategy) {
        case CalculationStrategy::SIMPLE:
            suggestion = calculateSimpleStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
            break;
        case CalculationStrategy::OPTIMIZED:
            suggestion = calculateOptimizedStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
            break;
        case CalculationStrategy::SAFETY_FIRST:
            suggestion = calculateSafetyFirstStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
            break;
        default:
            suggestion = calculateSimpleStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
            break;
    }
    
    updateStatistics(suggestion, suggestion.suggestedWeight > 0.0f);
    stats.lastCalculationTime = std::chrono::steady_clock::now();
    
    return suggestion;
}

bool WeightCalculator::validateWeight(float weight, float minLimit, float maxLimit) {
    return weight >= minLimit && weight <= maxLimit && !std::isnan(weight) && !std::isinf(weight);
}

bool WeightCalculator::validateFeedingAmount(float amount, float currentWeight, float targetWeight) {
    if (!validateWeight(amount, 0.0f, params.maxFeedingAmount)) {
        return false;
    }
    
    if (amount < params.minFeedingAmount) {
        return false;
    }
    
    float resultWeight = currentWeight + amount;
    return resultWeight <= targetWeight + params.toleranceRange;
}

bool WeightCalculator::isSafeToFeed(int feederId, int binId, float amount,
                                   const std::shared_ptr<FeederStatus>& status) {
    if (!status || !status->isConnected || status->isBlocked) {
        return false;
    }
    
    if (binId < 0 || binId >= status->bins.size()) {
        return false;
    }
    
    const auto& bin = status->bins[binId];
    if (bin.isBlocked || bin.isFeeding) {
        return false;
    }
    
    float resultWeight = bin.currentWeight + amount;
    return resultWeight <= bin.maxWeight;
}

WeightCalculator::FeedingSuggestion WeightCalculator::calculateSimpleStrategy(
    int feederId, int binId, float currentWeight, float targetWeight,
    float minWeight, float maxWeight) {
    
    FeedingSuggestion suggestion;
    suggestion.feederId = feederId;
    suggestion.binId = binId;
    suggestion.currentWeight = currentWeight;
    suggestion.targetWeight = targetWeight;
    suggestion.minWeight = minWeight;
    suggestion.maxWeight = maxWeight;
    
    float weightDifference = targetWeight - currentWeight;
    
    if (weightDifference <= params.toleranceRange) {
        suggestion.suggestedWeight = 0.0f;
        suggestion.priority = 0.0f;
        suggestion.reason = "Target weight already reached";
        return suggestion;
    }
    
    // 应用安全余量
    float safeAmount = weightDifference * (1.0f - params.safetyMargin);
    
    // 限制在最小和最大投料量之间
    safeAmount = clampWeight(safeAmount, params.minFeedingAmount, params.maxFeedingAmount);
    
    // 确保不超过料仓最大容量
    float maxAllowed = maxWeight - currentWeight;
    safeAmount = std::min(safeAmount, maxAllowed);
    
    // 应用校准系数
    safeAmount = applyCalibrationFactor(feederId, safeAmount);
    
    suggestion.suggestedWeight = safeAmount;
    suggestion.priority = calculatePriority(weightDifference, maxWeight);
    suggestion.reason = "Simple calculation with safety margin";
    suggestion.isSafe = safeAmount > 0.0f && safeAmount <= maxAllowed;
    
    return suggestion;
}

WeightCalculator::FeedingSuggestion WeightCalculator::calculateBalancedStrategy(
    int feederId, int binId, float currentWeight, float targetWeight,
    float minWeight, float maxWeight,
    const std::vector<std::shared_ptr<FeederStatus>>& allStatus) {
    
    // 先计算简单策略
    auto suggestion = calculateSimpleStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
    
    // 计算投料机负载
    float feederLoad = 0.0f;
    for (const auto& status : allStatus) {
        if (status && status->feederId == feederId) {
            feederLoad = calculateFeederLoad(status);
            break;
        }
    }
    
    // 根据负载调整优先级
    if (feederLoad > 0.8f) { // 高负载
        suggestion.priority *= 0.5f;
        suggestion.reason += " (High load adjustment)";
    } else if (feederLoad < 0.3f) { // 低负载
        suggestion.priority *= 1.2f;
        suggestion.reason += " (Low load boost)";
    }
    
    return suggestion;
}

WeightCalculator::FeedingSuggestion WeightCalculator::calculateOptimizedStrategy(
    int feederId, int binId, float currentWeight, float targetWeight,
    float minWeight, float maxWeight) {
    
    auto suggestion = calculateSimpleStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
    
    // 优化策略：尽量减少投料次数
    float weightDifference = targetWeight - currentWeight;
    if (weightDifference > params.maxFeedingAmount) {
        // 如果需要多次投料，优先选择较大的投料量
        suggestion.suggestedWeight = params.maxFeedingAmount;
        suggestion.reason = "Optimized for fewer feeding cycles";
    }
    
    return suggestion;
}

WeightCalculator::FeedingSuggestion WeightCalculator::calculateSafetyFirstStrategy(
    int feederId, int binId, float currentWeight, float targetWeight,
    float minWeight, float maxWeight) {
    
    auto suggestion = calculateSimpleStrategy(feederId, binId, currentWeight, targetWeight, minWeight, maxWeight);
    
    // 安全优先：使用更大的安全余量
    float conservativeAmount = suggestion.suggestedWeight * 0.8f;
    suggestion.suggestedWeight = conservativeAmount;
    suggestion.reason = "Conservative safety-first approach";
    
    return suggestion;
}

std::vector<WeightCalculator::FeedingSuggestion> WeightCalculator::balanceFeederLoads(
    const std::vector<std::shared_ptr<FeederStatus>>& feederStatus) {
    
    std::vector<FeedingSuggestion> suggestions;
    
    // 计算每个投料机的负载
    std::map<int, float> feederLoads;
    for (const auto& status : feederStatus) {
        if (status && status->isConnected) {
            feederLoads[status->feederId] = calculateFeederLoad(status);
        }
    }
    
    // 找出负载最低的投料机，给予更高优先级
    if (!feederLoads.empty()) {
        auto minLoadIt = std::min_element(feederLoads.begin(), feederLoads.end(),
            [](const auto& a, const auto& b) { return a.second < b.second; });
        
        int preferredFeederId = minLoadIt->first;
        
        // 为首选投料机创建建议
        for (const auto& status : feederStatus) {
            if (status && status->feederId == preferredFeederId) {
                for (int binId = 0; binId < status->bins.size(); ++binId) {
                    const auto& bin = status->bins[binId];
                    if (!bin.isBlocked && !bin.isFeeding && bin.currentWeight < bin.targetWeight) {
                        FeedingSuggestion suggestion;
                        suggestion.feederId = preferredFeederId;
                        suggestion.binId = binId;
                        suggestion.priority = 0.9f; // 高优先级
                        suggestion.reason = "Load balancing preference";
                        suggestions.push_back(suggestion);
                    }
                }
                break;
            }
        }
    }
    
    return suggestions;
}

float WeightCalculator::calculateFeederLoad(const std::shared_ptr<FeederStatus>& status) {
    if (!status) return 0.0f;
    
    float totalCurrent = 0.0f;
    float totalCapacity = 0.0f;
    
    for (const auto& bin : status->bins) {
        totalCurrent += bin.currentWeight;
        totalCapacity += bin.maxWeight;
    }
    
    return totalCapacity > 0.0f ? (totalCurrent / totalCapacity) : 0.0f;
}

WeightCalculator::SafetyCheck WeightCalculator::performSafetyCheck(
    const FeedingSuggestion& suggestion, const std::shared_ptr<FeederStatus>& status) {
    
    SafetyCheck check;
    
    if (!status || !status->isConnected) {
        check.passed = false;
        check.message = "Feeder not connected";
        check.riskLevel = 1.0f;
        return check;
    }
    
    if (suggestion.binId < 0 || suggestion.binId >= status->bins.size()) {
        check.passed = false;
        check.message = "Invalid bin ID";
        check.riskLevel = 1.0f;
        return check;
    }
    
    const auto& bin = status->bins[suggestion.binId];
    
    if (bin.isBlocked) {
        check.passed = false;
        check.message = "Bin is blocked";
        check.riskLevel = 0.8f;
        return check;
    }
    
    if (bin.isFeeding) {
        check.passed = false;
        check.message = "Bin is already feeding";
        check.riskLevel = 0.6f;
        return check;
    }
    
    float resultWeight = bin.currentWeight + suggestion.suggestedWeight;
    if (resultWeight > bin.maxWeight) {
        check.passed = false;
        check.message = "Would exceed bin capacity";
        check.riskLevel = 0.9f;
        return check;
    }
    
    if (suggestion.suggestedWeight < params.minFeedingAmount) {
        check.passed = false;
        check.message = "Amount below minimum threshold";
        check.riskLevel = 0.3f;
        return check;
    }
    
    check.passed = true;
    check.message = "Safety check passed";
    check.riskLevel = 0.0f;
    
    return check;
}

float WeightCalculator::applyCalibrationFactor(int feederId, float weight) {
    auto it = feederCalibrationFactors.find(feederId);
    if (it != feederCalibrationFactors.end()) {
        return weight * it->second;
    }
    return weight;
}

float WeightCalculator::calculatePriority(float weightDifference, float totalCapacity) {
    if (totalCapacity <= 0.0f) return 0.0f;
    
    float ratio = weightDifference / totalCapacity;
    return std::min(1.0f, std::max(0.0f, ratio));
}

float WeightCalculator::clampWeight(float weight, float minWeight, float maxWeight) {
    return std::max(minWeight, std::min(maxWeight, weight));
}

void WeightCalculator::updateStatistics(const FeedingSuggestion& suggestion, bool successful) {
    if (successful) {
        stats.successfulSuggestions++;
    } else {
        stats.rejectedSuggestions++;
    }
    
    // 更新平均准确度（简化计算）
    float totalSuggestions = stats.successfulSuggestions + stats.rejectedSuggestions;
    if (totalSuggestions > 0) {
        stats.averageAccuracy = static_cast<float>(stats.successfulSuggestions) / totalSuggestions;
    }
}

void WeightCalculator::recordCalculation(const FeedingSuggestion& suggestion, bool executed) {
    CalculationRecord record;
    record.timestamp = std::chrono::steady_clock::now();
    record.suggestion = suggestion;
    record.wasExecuted = executed;
    record.actualResult = 0.0f; // 需要后续更新
    
    history.push_back(record);
    
    // 限制历史记录数量
    if (history.size() > 1000) {
        history.erase(history.begin(), history.begin() + 100);
    }
}
