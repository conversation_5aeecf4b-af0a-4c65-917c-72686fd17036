#include "control_system.h"
#include "system_adapter.h"
#include "mes_server.h"
#include "config_manager.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>

ControlSystem::ControlSystem()
    : running(false), baseServersRunning(false), emergencyStopFlag(false) {

    // 创建执行器
    executor = std::make_shared<Fuxi::Common::Executor>(2);

    // 初始化统计信息
    stats.startTime = std::chrono::steady_clock::now();

    // 创建默认配置
    config = SystemConfig();
}

ControlSystem::~ControlSystem() {
    stop();
}

bool ControlSystem::start() {
    if (running) {
        return true;
    }
    
    try {
        // 初始化组件
        if (!initializeComponents()) {
            handleSystemError("Failed to initialize components");
            return false;
        }
        
        running = true;
        emergencyStopFlag = false;
        
        // 启动工作线程
        executor->postTask([this]() { run(); });
        executor->postTask([this]() { monitorSystem(); });
        
        notifyStatus("Control system started successfully");
        return true;
        
    } catch (const std::exception& e) {
        handleSystemError("Start exception: " + std::string(e.what()));
        return false;
    }
}

void ControlSystem::stop() {
    if (!running) {
        return;
    }

    running = false;
    baseServersRunning = false;

    // 停止系统适配器
    if (systemAdapter) {
        systemAdapter->stop();
    }

    // 停止MES服务器
    if (mesServer) {
        mesServer->stop();
    }

    notifyStatus("Control system stopped");
}

bool ControlSystem::startBaseServers() {
    if (baseServersRunning) {
        notifyStatus("Base servers already running");
        return true;
    }

    try {
        // 初始化基础组件（仅MES服务器和ABB服务器）
        if (config.enableDirectMode) {
            if (!initializeBaseServersDirectMode()) {
                handleSystemError("Failed to initialize base servers in direct mode");
                return false;
            }
        } else {
            if (!initializeBaseServersLegacyMode()) {
                handleSystemError("Failed to initialize base servers in legacy mode");
                return false;
            }
        }

        baseServersRunning = true;
        notifyStatus("Base servers (ABB & MES) started successfully");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Start base servers exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::connectOtherDevices() {
    if (!baseServersRunning) {
        handleSystemError("Base servers must be running before connecting other devices");
        return false;
    }

    if (running) {
        notifyStatus("Other devices already connected");
        return true;
    }

    try {
        // 连接其他设备（投料机等）
        if (config.enableDirectMode) {
            if (!connectDevicesDirectMode()) {
                handleSystemError("Failed to connect devices in direct mode");
                return false;
            }
        } else {
            if (!connectDevicesLegacyMode()) {
                handleSystemError("Failed to connect devices in legacy mode");
                return false;
            }
        }

        running = true;
        emergencyStopFlag = false;

        // 启动工作线程
        executor->postTask([this]() { run(); });
        executor->postTask([this]() { monitorSystem(); });

        notifyStatus("All devices connected successfully");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Connect devices exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::loadConfig(const std::string& configFile) {
    try {
        ConfigManager configManager;
        if (configManager.loadConfig(configFile)) {
            config = configManager.getSystemConfig();
            notifyStatus("Configuration loaded from " + configFile);
            return true;
        } else {
            handleSystemError("Failed to load configuration from " + configFile);
            return false;
        }
    } catch (const std::exception& e) {
        handleSystemError("Config load exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::saveConfig(const std::string& configFile) {
    try {
        ConfigManager configManager;
        configManager.setSystemConfig(config);
        if (configManager.saveConfig(configFile)) {
            notifyStatus("Configuration saved to " + configFile);
            return true;
        } else {
            handleSystemError("Failed to save configuration to " + configFile);
            return false;
        }
    } catch (const std::exception& e) {
        handleSystemError("Config save exception: " + std::string(e.what()));
        return false;
    }
}

void ControlSystem::setConfig(const SystemConfig& newConfig) {
    // 暂时跳过配置更新以避免崩溃
    std::cout << "Config update temporarily disabled for debugging" << std::endl;

    // TODO: 修复配置更新中的内存问题
    // config = newConfig;
    // systemAdapter->updateConfig(config);

    // 如果系统正在运行，重新初始化组件
    // if (running) {
    //     stop();
    //     start();
    // }
}

bool ControlSystem::enableDirectMode(bool enable) {
    if (config.enableDirectMode != enable) {
        config.enableDirectMode = enable;
        stats.modeChanges++;
        
        notifyStatus("Direct mode " + std::string(enable ? "enabled" : "disabled"));
        
        // 重新初始化系统
        if (running) {
            stop();
            return start();
        }
    }
    
    return true;
}

std::shared_ptr<TaskInfo> ControlSystem::findNextTask() {
    if (systemAdapter) {
        return systemAdapter->findNextTask();
    }
    
    // 从任务队列中获取下一个任务
    std::lock_guard<std::mutex> lock(taskQueueMutex);
    if (!taskQueue.empty()) {
        auto task = taskQueue.front();
        taskQueue.pop();
        return task;
    }
    
    return nullptr;
}

bool ControlSystem::startTask(std::shared_ptr<TaskInfo> task) {
    if (!task || !validateTask(task)) {
        handleTaskError(task ? task->taskId : "unknown", "Invalid task");
        return false;
    }
    
    if (emergencyStopFlag) {
        handleTaskError(task->taskId, "System in emergency stop state");
        return false;
    }
    
    try {
        bool result = false;
        
        if (systemAdapter) {
            result = systemAdapter->startTask(task);
        }
        
        if (result) {
            std::lock_guard<std::mutex> lock(currentTaskMutex);
            currentTask = std::make_shared<TaskStatus>();
            currentTask->taskId = task->taskId;
            currentTask->status = TaskStatus::RUNNING;
            currentTask->startTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count());
            
            stats.tasksCompleted++;
            notifyTaskLog("Task " + task->taskId + " started successfully");
        } else {
            stats.tasksFailed++;
            handleTaskError(task->taskId, "Failed to start task");
        }
        
        return result;
        
    } catch (const std::exception& e) {
        handleTaskError(task->taskId, "Start task exception: " + std::string(e.what()));
        return false;
    }
}

std::shared_ptr<TaskStatus> ControlSystem::getTaskStatus() const {
    if (systemAdapter) {
        return systemAdapter->getTaskStatus();
    }
    
    std::lock_guard<std::mutex> lock(currentTaskMutex);
    return currentTask;
}

std::vector<std::shared_ptr<FeederStatus>> ControlSystem::getFeederStatus() {
    if (systemAdapter) {
        return systemAdapter->getFeederStatus();
    }
    
    return std::vector<std::shared_ptr<FeederStatus>>();
}

std::map<char, std::shared_ptr<AreaData>> ControlSystem::getAreaStatus() {
    if (systemAdapter) {
        return systemAdapter->getAllPLCAreaData();
    }
    
    return std::map<char, std::shared_ptr<AreaData>>();
}

bool ControlSystem::isSystemAuto() const {
    if (systemAdapter) {
        return systemAdapter->isSystemAuto();
    }
    
    return true; // 默认自动模式
}

bool ControlSystem::hasError() const {
    if (systemAdapter) {
        return systemAdapter->hasError();
    }
    
    return false;
}

bool ControlSystem::isHeartbeatOK() const {
    if (systemAdapter) {
        return systemAdapter->isHeartbeatOK();
    }
    
    return true;
}

bool ControlSystem::emergencyStop() {
    emergencyStopFlag = true;
    
    if (systemAdapter) {
        systemAdapter->emergencyStop();
    }
    
    // 取消当前任务
    {
        std::lock_guard<std::mutex> lock(currentTaskMutex);
        if (currentTask) {
            currentTask->status = TaskStatus::CANCELLED;
            currentTask->message = "Emergency stop activated";
            currentTask->endTime = std::to_string(std::chrono::duration_cast<std::chrono::seconds>(
                std::chrono::system_clock::now().time_since_epoch()).count());
        }
    }
    
    notifyStatus("Emergency stop activated");
    return true;
}

bool ControlSystem::reset() {
    emergencyStopFlag = false;
    
    if (systemAdapter) {
        systemAdapter->reset();
    }
    
    // 清空任务队列
    {
        std::lock_guard<std::mutex> lock(taskQueueMutex);
        std::queue<std::shared_ptr<TaskInfo>> empty;
        taskQueue.swap(empty);
    }
    
    notifyStatus("System reset completed");
    return true;
}

bool ControlSystem::setSystemMode(bool autoMode) {
    if (systemAdapter) {
        return systemAdapter->setSystemMode(autoMode);
    }
    
    notifyStatus("System mode set to " + std::string(autoMode ? "AUTO" : "MANUAL"));
    return true;
}

bool ControlSystem::initializeComponents() {
    try {
        if (config.enableDirectMode) {
            return initializeDirectMode();
        } else {
            return initializeLegacyMode();
        }
    } catch (const std::exception& e) {
        handleSystemError("Component initialization exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::initializeDirectMode() {
    // 创建系统适配器
    systemAdapter = std::make_shared<SystemAdapter>();
    if (!systemAdapter->initialize(config)) {
        handleSystemError("Failed to initialize system adapter");
        return false;
    }
    
    // 设置回调
    systemAdapter->setStatusCallback([this](const std::string& status) {
        notifyStatus(status);
    });
    
    systemAdapter->setTaskLogCallback([this](const std::string& message) {
        notifyTaskLog(message);
    });
    
    // 创建MES服务器
    mesServer = std::make_shared<MesServer>("0.0.0.0", 502);
    mesServer->setSystemAdapter(systemAdapter);
    
    // 启动组件
    if (!systemAdapter->start()) {
        handleSystemError("Failed to start system adapter");
        return false;
    }
    
    if (!mesServer->start()) {
        handleSystemError("Failed to start MES server");
        return false;
    }
    
    notifyStatus("Direct mode initialized successfully");
    return true;
}

bool ControlSystem::initializeLegacyMode() {
    try {
        notifyStatus("Initializing legacy PLC mode...");

        // 创建系统适配器（用于兼容原有接口）
        systemAdapter = std::make_shared<SystemAdapter>();
        if (!systemAdapter) {
            handleSystemError("Failed to create system adapter");
            return false;
        }

        // 初始化系统适配器
        if (!systemAdapter->initialize(config)) {
            handleSystemError("Failed to initialize system adapter");
            return false;
        }

        // 设置回调函数
        systemAdapter->setTaskLogCallback([this](const std::string& message) {
            notifyTaskLog(message);
        });

        systemAdapter->setStatusCallback([this](const std::string& status) {
            notifyStatus(status);
        });

        // 启动系统适配器
        if (!systemAdapter->start()) {
            handleSystemError("Failed to start system adapter");
            return false;
        }

        stats.connectionAttempts++;
        stats.successfulConnections++;

        notifyStatus("Legacy PLC mode initialized successfully");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Exception in legacy mode initialization: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::initializeBaseServersDirectMode() {
    try {
        // 创建MES服务器
        mesServer = std::make_shared<MesServer>("0.0.0.0", 502);

        // 启动MES服务器
        if (!mesServer->start()) {
            handleSystemError("Failed to start MES server");
            return false;
        }

        notifyStatus("Base servers (MES) started in direct mode");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Base servers direct mode exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::initializeBaseServersLegacyMode() {
    try {
        // 在传统模式下，基础服务器包括MES服务器
        // ABB PLC连接将在connectDevicesLegacyMode中处理

        notifyStatus("Base servers started in legacy mode (MES ready)");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Base servers legacy mode exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::connectDevicesDirectMode() {
    try {
        // 创建系统适配器
        systemAdapter = std::make_shared<SystemAdapter>();
        if (!systemAdapter->initialize(config)) {
            handleSystemError("Failed to initialize system adapter");
            return false;
        }

        // 设置回调
        systemAdapter->setStatusCallback([this](const std::string& status) {
            notifyStatus(status);
        });

        systemAdapter->setTaskLogCallback([this](const std::string& message) {
            notifyTaskLog(message);
        });

        // 将系统适配器连接到MES服务器
        if (mesServer) {
            mesServer->setSystemAdapter(systemAdapter);
        }

        // 启动系统适配器（连接投料机等设备）
        if (!systemAdapter->start()) {
            handleSystemError("Failed to start system adapter");
            return false;
        }

        notifyStatus("All devices connected in direct mode");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Connect devices direct mode exception: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::connectDevicesLegacyMode() {
    try {
        // 创建系统适配器（用于兼容原有接口）
        systemAdapter = std::make_shared<SystemAdapter>();
        if (!systemAdapter) {
            handleSystemError("Failed to create system adapter");
            return false;
        }

        // 初始化系统适配器
        if (!systemAdapter->initialize(config)) {
            handleSystemError("Failed to initialize system adapter");
            return false;
        }

        // 设置回调函数
        systemAdapter->setTaskLogCallback([this](const std::string& message) {
            notifyTaskLog(message);
        });

        systemAdapter->setStatusCallback([this](const std::string& status) {
            notifyStatus(status);
        });

        // 启动系统适配器
        if (!systemAdapter->start()) {
            handleSystemError("Failed to start system adapter");
            return false;
        }

        stats.connectionAttempts++;
        stats.successfulConnections++;

        notifyStatus("All devices connected in legacy mode");
        return true;

    } catch (const std::exception& e) {
        handleSystemError("Connect devices legacy mode exception: " + std::string(e.what()));
        return false;
    }
}

void ControlSystem::run() {
    while (running) {
        try {
            processTaskQueue();
        } catch (const std::exception& e) {
            handleSystemError("Run loop exception: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

void ControlSystem::monitorSystem() {
    while (running) {
        try {
            performSystemCheck();
        } catch (const std::exception& e) {
            handleSystemError("Monitor exception: " + std::string(e.what()));
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

/**
 * 处理任务队列
 * 从队列中取出任务并执行，管理任务的生命周期
 */
void ControlSystem::processTaskQueue() {
    if (!running || emergencyStopFlag) {
        return;
    }

    try {
        std::shared_ptr<TaskInfo> nextTask = nullptr;

        // 检查当前是否有正在执行的任务
        {
            std::lock_guard<std::mutex> lock(currentTaskMutex);
            if (currentTask &&
                (currentTask->status == TaskStatus::RUNNING ||
                 currentTask->status == TaskStatus::PENDING)) {
                // 有任务正在执行，更新任务进度
                if (systemAdapter) {
                    // 通过SystemAdapter的公共接口获取任务状态
                    auto adapterTaskStatus = systemAdapter->getTaskStatus();
                    if (adapterTaskStatus && adapterTaskStatus->taskId == currentTask->taskId) {
                        // 同步任务状态
                        currentTask->progress = adapterTaskStatus->progress;
                        currentTask->status = adapterTaskStatus->status;
                        currentTask->currentStep = adapterTaskStatus->currentStep;
                        if (!adapterTaskStatus->errorMessage.empty()) {
                            currentTask->errorMessage = adapterTaskStatus->errorMessage;
                        }
                    }
                }
                return; // 等待当前任务完成
            }
        }

        // 从队列中获取下一个任务
        {
            std::lock_guard<std::mutex> lock(taskQueueMutex);
            if (!taskQueue.empty()) {
                nextTask = taskQueue.front();
                taskQueue.pop();
            }
        }

        // 如果没有任务，直接返回
        if (!nextTask) {
            return;
        }

        // 验证任务
        if (!validateTask(nextTask)) {
            handleTaskError(nextTask->taskId, "Task validation failed");
            stats.tasksFailed++;
            return;
        }

        // 检查系统状态是否允许执行任务
        if (!isSystemReadyForTask(nextTask)) {
            // 将任务重新放回队列前端
            std::lock_guard<std::mutex> lock(taskQueueMutex);
            std::queue<std::shared_ptr<TaskInfo>> tempQueue;
            tempQueue.push(nextTask);
            while (!taskQueue.empty()) {
                tempQueue.push(taskQueue.front());
                taskQueue.pop();
            }
            taskQueue = tempQueue;
            return;
        }

        // 创建任务状态
        auto taskStatus = std::make_shared<TaskStatus>();
        taskStatus->taskId = nextTask->taskId;
        taskStatus->status = TaskStatus::PENDING;
        taskStatus->progress = 0.0f;
        taskStatus->startTime = getCurrentTimeString();

        // 设置当前任务
        {
            std::lock_guard<std::mutex> lock(currentTaskMutex);
            currentTask = taskStatus;
        }

        // 执行任务
        bool taskStarted = executeTask(nextTask);

        if (taskStarted) {
            // 任务启动成功
            {
                std::lock_guard<std::mutex> lock(currentTaskMutex);
                currentTask->status = TaskStatus::RUNNING;
            }

            notifyTaskLog("Task started: " + nextTask->taskId +
                         " (Feeder: " + std::to_string(nextTask->feederId) +
                         ", Bin: " + std::to_string(nextTask->binId) +
                         ", Weight: " + std::to_string(nextTask->weight) + "kg)");

            stats.lastTaskTime = std::chrono::steady_clock::now();

        } else {
            // 任务启动失败
            {
                std::lock_guard<std::mutex> lock(currentTaskMutex);
                currentTask->status = TaskStatus::FAILED;
                currentTask->errorMessage = "Failed to start task execution";
                currentTask->endTime = getCurrentTimeString();
            }

            handleTaskError(nextTask->taskId, "Failed to start task execution");
            stats.tasksFailed++;
        }

    } catch (const std::exception& e) {
        handleSystemError("Error in processTaskQueue: " + std::string(e.what()));
        stats.systemErrors++;
    }
}

bool ControlSystem::validateTask(std::shared_ptr<TaskInfo> task) {
    if (!task) return false;
    if (task->taskId.empty()) return false;
    if (task->feederId < 0 || task->feederId >= Constants::FEEDERS_COUNT) return false;
    if (task->binId < 0 || task->binId >= Constants::BINS_PER_FEEDER) return false;
    if (task->weight <= 0.0f) return false;
    
    return true;
}

void ControlSystem::updateTaskStatistics(std::shared_ptr<TaskStatus> status) {
    if (!status) return;
    
    switch (status->status) {
        case TaskStatus::COMPLETED:
            stats.tasksCompleted++;
            break;
        case TaskStatus::FAILED:
        case TaskStatus::CANCELLED:
            stats.tasksFailed++;
            break;
        default:
            break;
    }
    
    stats.lastTaskTime = std::chrono::steady_clock::now();
}

void ControlSystem::handleSystemError(const std::string& error) {
    stats.systemErrors++;

    // 生成带时间戳的错误消息
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << "[" << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count() << "] ";
    ss << "[ControlSystem] ERROR: " << error;

    std::string timestampedError = ss.str();

    // 输出到控制台
    std::cerr << timestampedError << std::endl;

    // 记录到日志文件（如果配置了日志路径）
    if (!config.logFilePath.empty()) {
        try {
            std::ofstream logFile(config.logFilePath, std::ios::app);
            if (logFile.is_open()) {
                logFile << timestampedError << std::endl;
                logFile.close();
            }
        } catch (const std::exception& e) {
            std::cerr << "[ControlSystem] Failed to write to log file: " << e.what() << std::endl;
        }
    }

    // 通知状态回调
    if (statusCallback) {
        statusCallback("ERROR: " + error);
    }

    // 如果错误过多，考虑触发紧急停止
    const int MAX_ERRORS_BEFORE_EMERGENCY_STOP = 10;
    if (stats.systemErrors >= MAX_ERRORS_BEFORE_EMERGENCY_STOP && !emergencyStopFlag) {
        std::cerr << "[ControlSystem] Too many errors (" << stats.systemErrors
                  << "), triggering emergency stop" << std::endl;
        emergencyStop();
    }
}

void ControlSystem::handleTaskError(const std::string& taskId, const std::string& error) {
    std::string fullError = "Task " + taskId + " error: " + error;
    handleSystemError(fullError);
    
    if (taskLogCallback) {
        taskLogCallback(fullError);
    }
}

void ControlSystem::notifyStatus(const std::string& status) {
    if (statusCallback) {
        statusCallback(status);
    }
}

void ControlSystem::notifyTaskLog(const std::string& message) {
    if (taskLogCallback) {
        taskLogCallback(message);
    }
}

void ControlSystem::notifyFeederStatus(const std::vector<std::shared_ptr<FeederStatus>>& status) {
    if (feederStatusCallback) {
        feederStatusCallback(status);
    }
}

void ControlSystem::notifyAreaStatus(const std::map<char, std::shared_ptr<AreaData>>& status) {
    if (areaStatusCallback) {
        areaStatusCallback(status);
    }
}

/**
 * 执行系统健康检查
 * 检查所有关键组件的状态和连接
 * @return true如果系统健康，false如果发现问题
 */
bool ControlSystem::performSystemCheck() {
    bool systemHealthy = true;
    std::vector<std::string> issues;

    try {
        // 1. 检查核心组件状态
        if (!systemAdapter) {
            issues.push_back("SystemAdapter not initialized");
            systemHealthy = false;
        }

        if (!mesServer) {
            issues.push_back("MesServer not initialized");
            systemHealthy = false;
        }

        if (!executor) {
            issues.push_back("Executor not initialized");
            systemHealthy = false;
        }

        // 2. 检查投料机状态
        if (systemAdapter) {
            auto feederStatuses = systemAdapter->getFeederStatus();
            int connectedFeeders = 0;
            int totalFeeders = 0;

            for (const auto& status : feederStatuses) {
                if (status) {
                    totalFeeders++;
                    if (status->isConnected) {
                        connectedFeeders++;

                        // 检查心跳状态
                        if (!status->heartbeatOK) {
                            issues.push_back("Feeder " + std::to_string(status->feederId) + " heartbeat failed");
                            systemHealthy = false;
                        }

                        // 检查屏蔽状态
                        if (status->isBlocked) {
                            issues.push_back("Feeder " + std::to_string(status->feederId) + " is blocked");
                        }

                        // 检查料仓状态
                        for (size_t i = 0; i < status->bins.size(); ++i) {
                            const auto& bin = status->bins[i];
                            if (bin.currentWeight < 0) {
                                issues.push_back("Feeder " + std::to_string(status->feederId) +
                                                " Bin " + std::to_string(i + 1) + " has invalid weight");
                            }
                            if (bin.isBlocked) {
                                issues.push_back("Feeder " + std::to_string(status->feederId) +
                                                " Bin " + std::to_string(i + 1) + " is blocked");
                            }
                        }
                    } else {
                        issues.push_back("Feeder " + std::to_string(status->feederId) + " is disconnected");
                        systemHealthy = false;
                    }
                }
            }

            // 检查投料机连接率
            if (totalFeeders > 0) {
                float connectionRate = static_cast<float>(connectedFeeders) / totalFeeders;
                if (connectionRate < 0.5f) { // 如果连接率低于50%
                    issues.push_back("Low feeder connection rate: " +
                                    std::to_string(static_cast<int>(connectionRate * 100)) + "%");
                    systemHealthy = false;
                }
            }
        }

        // 3. 检查ABB机器人状态（如果启用直连模式）
        if (config.enableDirectMode && systemAdapter) {
            // 通过SystemAdapter检查机器人连接状态
            bool robotConnected = systemAdapter->isHeartbeatOK();
            if (!robotConnected) {
                issues.push_back("ABB Robot connection failed");
                systemHealthy = false;
            }
        }

        // 4. 检查区域状态
        try {
            auto areaStatuses = getAreaStatus();
            for (const auto& pair : areaStatuses) {
                char areaId = pair.first;
                auto areaData = pair.second;

                if (areaData) {
                    // 检查区域屏蔽状态
                    if (areaData->isBlocked) {
                        issues.push_back("Area " + std::string(1, areaId) + " is blocked");
                    }

                    // 检查桶状态
                    for (size_t i = 0; i < areaData->buckets.size(); ++i) {
                        const auto& bucket = areaData->buckets[i];
                        if (bucket.weight < 0) {
                            issues.push_back("Area " + std::string(1, areaId) +
                                            " Bucket " + std::to_string(i + 1) + " has invalid weight");
                        }
                        if (bucket.isBlocked) {
                            issues.push_back("Area " + std::string(1, areaId) +
                                            " Bucket " + std::to_string(i + 1) + " is blocked");
                        }
                    }
                }
            }
        } catch (const std::exception& e) {
            issues.push_back("Failed to check area status: " + std::string(e.what()));
        }

        // 5. 检查系统错误计数
        if (stats.systemErrors > 10) {
            issues.push_back("High system error count: " + std::to_string(stats.systemErrors));
            systemHealthy = false;
        }

        if (stats.failedOperations > stats.feedingOperations * 0.2f && stats.feedingOperations > 0) {
            issues.push_back("High operation failure rate: " +
                           std::to_string(static_cast<int>((static_cast<float>(stats.failedOperations) /
                                                          stats.feedingOperations) * 100)) + "%");
            systemHealthy = false;
        }

        // 6. 检查紧急停止状态
        if (emergencyStopFlag) {
            issues.push_back("Emergency stop is active");
            systemHealthy = false;
        }

        // 7. 记录检查结果
        if (!issues.empty()) {
            std::string issueList = "System health issues found: ";
            for (size_t i = 0; i < issues.size(); ++i) {
                if (i > 0) issueList += "; ";
                issueList += issues[i];
            }

            if (!systemHealthy) {
                handleSystemError(issueList);
            } else {
                notifyStatus("System warnings: " + issueList);
            }
        } else {
            // 系统健康，可以定期记录正常状态
            static int healthyCheckCount = 0;
            healthyCheckCount++;
            if (healthyCheckCount % 12 == 0) { // 每分钟记录一次（假设5秒检查一次）
                notifyStatus("System health check passed - all components normal");
            }
        }

        return systemHealthy;

    } catch (const std::exception& e) {
        handleSystemError("Exception during system health check: " + std::string(e.what()));
        stats.systemErrors++;
        return false;
    }
}

std::vector<std::string> ControlSystem::getDiagnosticInfo() {
    std::vector<std::string> info;
    
    info.push_back("=== Control System Diagnostics ===");
    info.push_back("Running: " + std::string(running ? "Yes" : "No"));
    info.push_back("Direct Mode: " + std::string(config.enableDirectMode ? "Enabled" : "Disabled"));
    info.push_back("Emergency Stop: " + std::string(emergencyStopFlag ? "Active" : "Inactive"));
    info.push_back("Tasks Completed: " + std::to_string(stats.tasksCompleted));
    info.push_back("Tasks Failed: " + std::to_string(stats.tasksFailed));
    info.push_back("System Errors: " + std::to_string(stats.systemErrors));
    info.push_back("Mode Changes: " + std::to_string(stats.modeChanges));
    
    return info;
}

// 其他简化的接口实现
bool ControlSystem::isFeederEnabled(int feederId) const {
    return systemAdapter ? systemAdapter->isFeederEnabled(feederId) : false;
}

bool ControlSystem::isBinEnabled(int feederId, int binId) const {
    return systemAdapter ? systemAdapter->isBinEnabled(feederId, binId) : false;
}

bool ControlSystem::startFeeding(int feederId, int binId) {
    try {
        // 验证参数
        if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
            handleSystemError("Invalid feeder ID: " + std::to_string(feederId));
            return false;
        }

        if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
            handleSystemError("Invalid bin ID: " + std::to_string(binId));
            return false;
        }

        // 检查系统状态
        if (!running) {
            handleSystemError("Cannot start feeding: system not running");
            return false;
        }

        if (emergencyStopFlag) {
            handleSystemError("Cannot start feeding: emergency stop active");
            return false;
        }

        // 检查料仓是否被禁用
        if (!isBinEnabled(feederId, binId)) {
            handleSystemError("Cannot start feeding: bin " + std::to_string(binId) +
                            " on feeder " + std::to_string(feederId) + " is disabled");
            return false;
        }

        // 通过系统适配器执行投料
        if (systemAdapter) {
            bool result = systemAdapter->startFeeding(feederId, binId);
            if (result) {
                stats.feedingOperations++;
                notifyTaskLog("Started feeding: Feeder " + std::to_string(feederId) +
                            ", Bin " + std::to_string(binId));
            } else {
                stats.failedOperations++;
                handleSystemError("Failed to start feeding via system adapter");
            }
            return result;
        } else {
            handleSystemError("System adapter not available");
            return false;
        }

    } catch (const std::exception& e) {
        stats.failedOperations++;
        handleSystemError("Exception in startFeeding: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::stopFeeding(int feederId, int binId) {
    try {
        // 验证参数
        if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
            handleSystemError("Invalid feeder ID: " + std::to_string(feederId));
            return false;
        }

        if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
            handleSystemError("Invalid bin ID: " + std::to_string(binId));
            return false;
        }

        // 通过系统适配器执行停止投料
        if (systemAdapter) {
            bool result = systemAdapter->stopFeeding(feederId, binId);
            if (result) {
                notifyTaskLog("Stopped feeding: Feeder " + std::to_string(feederId) +
                            ", Bin " + std::to_string(binId));
            } else {
                stats.failedOperations++;
                handleSystemError("Failed to stop feeding via system adapter");
            }
            return result;
        } else {
            handleSystemError("System adapter not available");
            return false;
        }

    } catch (const std::exception& e) {
        stats.failedOperations++;
        handleSystemError("Exception in stopFeeding: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::stopAllFeeding() {
    try {
        bool allSuccess = true;

        // 通过系统适配器执行停止所有投料
        if (systemAdapter) {
            bool result = systemAdapter->stopAllFeeding();
            if (result) {
                notifyTaskLog("Stopped all feeding operations");
            } else {
                stats.failedOperations++;
                handleSystemError("Failed to stop all feeding via system adapter");
                allSuccess = false;
            }
        } else {
            handleSystemError("System adapter not available");
            allSuccess = false;
        }

        // 如果系统适配器失败，尝试逐个停止所有投料机
        if (!allSuccess) {
            notifyTaskLog("Attempting to stop feeding on individual feeders...");
            for (int feederId = 1; feederId <= Constants::FEEDERS_COUNT; feederId++) {
                for (int binId = 1; binId <= Constants::BINS_PER_FEEDER; binId++) {
                    stopFeeding(feederId, binId);
                }
            }
        }

        return allSuccess;

    } catch (const std::exception& e) {
        stats.failedOperations++;
        handleSystemError("Exception in stopAllFeeding: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::setBinTargetWeight(int feederId, int binId, float targetWeight) {
    try {
        // 验证参数
        if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
            handleSystemError("Invalid feeder ID: " + std::to_string(feederId));
            return false;
        }

        if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
            handleSystemError("Invalid bin ID: " + std::to_string(binId));
            return false;
        }

        if (targetWeight < 0.0f) {
            handleSystemError("Invalid target weight: " + std::to_string(targetWeight));
            return false;
        }

        // 检查重量范围是否合理
        const float MAX_BIN_WEIGHT = 1000.0f; // 假设最大重量为1000kg
        if (targetWeight > MAX_BIN_WEIGHT) {
            handleSystemError("Target weight too large: " + std::to_string(targetWeight) +
                            " kg (max: " + std::to_string(MAX_BIN_WEIGHT) + " kg)");
            return false;
        }

        // 通过系统适配器设置目标重量
        if (systemAdapter) {
            bool result = systemAdapter->setBinTargetWeight(feederId, binId, targetWeight);
            if (result) {
                notifyTaskLog("Set target weight: Feeder " + std::to_string(feederId) +
                            ", Bin " + std::to_string(binId) + ", Weight " + std::to_string(targetWeight) + " kg");
            } else {
                stats.failedOperations++;
                handleSystemError("Failed to set target weight via system adapter");
            }
            return result;
        } else {
            handleSystemError("System adapter not available");
            return false;
        }

    } catch (const std::exception& e) {
        stats.failedOperations++;
        handleSystemError("Exception in setBinTargetWeight: " + std::string(e.what()));
        return false;
    }
}

bool ControlSystem::getBinWeight(int feederId, int binId, float& weight) {
    try {
        // 验证参数
        if (feederId < 1 || feederId > Constants::FEEDERS_COUNT) {
            handleSystemError("Invalid feeder ID: " + std::to_string(feederId));
            return false;
        }

        if (binId < 1 || binId > Constants::BINS_PER_FEEDER) {
            handleSystemError("Invalid bin ID: " + std::to_string(binId));
            return false;
        }

        // 通过系统适配器获取重量
        if (systemAdapter) {
            bool result = systemAdapter->getBinWeight(feederId, binId, weight);
            if (!result) {
                stats.failedOperations++;
                handleSystemError("Failed to get bin weight via system adapter");
                weight = 0.0f; // 设置默认值
            }
            return result;
        } else {
            handleSystemError("System adapter not available");
            weight = 0.0f;
            return false;
        }

    } catch (const std::exception& e) {
        stats.failedOperations++;
        handleSystemError("Exception in getBinWeight: " + std::string(e.what()));
        weight = 0.0f;
        return false;
    }
}

/**
 * 检查系统是否准备好执行任务
 * @param task 要执行的任务
 * @return true如果系统准备就绪，false否则
 */
bool ControlSystem::isSystemReadyForTask(std::shared_ptr<TaskInfo> task) {
    if (!task || !running || emergencyStopFlag) {
        return false;
    }

    try {
        // 检查系统是否处于自动模式
        if (!isSystemAuto()) {
            return false;
        }

        // 检查投料机状态
        if (!isFeederEnabled(task->feederId)) {
            return false;
        }

        // 检查料仓状态
        if (!isBinEnabled(task->feederId, task->binId)) {
            return false;
        }

        // 检查投料机连接状态
        auto feederStatuses = getFeederStatus();
        bool feederReady = false;
        for (const auto& status : feederStatuses) {
            if (status && status->feederId == task->feederId) {
                feederReady = status->isConnected && status->heartbeatOK && !status->isBlocked;
                break;
            }
        }

        if (!feederReady) {
            return false;
        }

        // 检查区域状态（如果任务包含区域信息）
        if (task->areaId != '\0') {
            auto areaStatuses = getAreaStatus();
            auto it = areaStatuses.find(task->areaId);
            if (it != areaStatuses.end() && it->second) {
                if (it->second->isBlocked) {
                    return false;
                }
            }
        }

        return true;

    } catch (const std::exception& e) {
        handleSystemError("Error checking system readiness: " + std::string(e.what()));
        return false;
    }
}

/**
 * 执行任务
 * @param task 要执行的任务
 * @return true如果任务启动成功，false否则
 */
bool ControlSystem::executeTask(std::shared_ptr<TaskInfo> task) {
    if (!task || !systemAdapter) {
        return false;
    }

    try {
        // 通过SystemAdapter启动任务
        bool result = systemAdapter->startTask(task);

        if (result) {
            stats.feedingOperations++;
            notifyStatus("Task execution started: " + task->taskId);
        } else {
            stats.failedOperations++;
            handleTaskError(task->taskId, "SystemAdapter failed to start task");
        }

        return result;

    } catch (const std::exception& e) {
        stats.failedOperations++;
        handleTaskError(task->taskId, "Exception during task execution: " + std::string(e.what()));
        return false;
    }
}

/**
 * 获取当前时间字符串
 * @return 格式化的时间字符串
 */
std::string ControlSystem::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();

    return ss.str();
}
