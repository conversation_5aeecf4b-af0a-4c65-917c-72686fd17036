// MettlerBalance.h
#pragma once
#include <string>
#include <winsock2.h>
#include <windows.h>

class MettlerBalance {
public:
    MettlerBalance(const std::string& port = "COM1");
    ~MettlerBalance();

    bool connect();
    void disconnect();

    // 核心功能
    std::string getModel();
    double getWeight(bool stable = true);
    bool tare();


private:
    HANDLE hSerial;
    std::string portName;
    bool isConnected;

    bool sendCommand(const std::string& cmd);
    std::string readResponse(int timeout = 1000);
    bool parseWeightResponse(const std::string& response, double& weight);
};
