
#include "MettlerBalance.h"
#include <iostream>
#include <sstream>

MettlerBalance::MettlerBalance(const std::string& port)
    : portName(port), isConnected(false), hSerial(INVALID_HANDLE_VALUE) {}

bool MettlerBalance::connect() {
    hSerial = CreateFileA(portName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL);

    if (hSerial == INVALID_HANDLE_VALUE) {
        return false;
    }

    DCB dcbSerialParams = {0};
    dcbSerialParams.DCBlength = sizeof(dcbSerialParams);
    if (!GetCommState(hSerial, &dcbSerialParams)) {
        CloseHandle(hSerial);
        return false;
    }

    dcbSerialParams.BaudRate = CBR_9600;
    dcbSerialParams.ByteSize = 8;
    dcbSerialParams.StopBits = ONESTOPBIT;
    dcbSerialParams.Parity = NOPARITY;
    if (!SetCommState(hSerial, &dcbSerialParams)) {
        CloseHandle(hSerial);
        return false;
    }

    COMMTIMEOUTS timeouts = {0};
    timeouts.ReadIntervalTimeout = 50;
    timeouts.ReadTotalTimeoutConstant = 50;
    timeouts.ReadTotalTimeoutMultiplier = 10;
    timeouts.WriteTotalTimeoutConstant = 50;
    timeouts.WriteTotalTimeoutMultiplier = 10;
    if (!SetCommTimeouts(hSerial, &timeouts)) {
        CloseHandle(hSerial);
        return false;
    }

    isConnected = true;
    return true;
}

void MettlerBalance::disconnect() {
    if (isConnected) {
        CloseHandle(hSerial);
        isConnected = false;
    }
}

std::string MettlerBalance::getModel() {
    if (!sendCommand("I4\r\n")) return "";
    std::string response = readResponse();

    // 解析响应格式：I4 A "123456789"
    size_t start = response.find('"');
    if (start != std::string::npos) {
        size_t end = response.find('"', start+1);
        if (end != std::string::npos) {
            return response.substr(start+1, end-start-1);
        }
    }
    return "";
}

double MettlerBalance::getWeight(bool stable) {
    std::string cmd = stable ? "S\r\n" : "SI\r\n";
    if (!sendCommand(cmd)) return -9999.99;

    std::string response = readResponse();
    double weight;
    if (parseWeightResponse(response, weight)) {
        return weight;
    }
    return -9999.99;
}

bool MettlerBalance::tare() {
    if (!sendCommand("Z\r\n")) return false;
    std::string response = readResponse();
    return response.find("ZA") != std::string::npos;
}

// 以下为私有方法实现
bool MettlerBalance::sendCommand(const std::string& cmd) {
    if (!isConnected) return false;

    DWORD bytesWritten;
    return WriteFile(hSerial, cmd.c_str(), cmd.size(), &bytesWritten, NULL);
}

std::string MettlerBalance::readResponse(int timeout) {
    const int BUFFER_SIZE = 256;
    char buffer[BUFFER_SIZE];
    std::string result;
    DWORD bytesRead;

    do {
        if (ReadFile(hSerial, buffer, BUFFER_SIZE, &bytesRead, NULL)) {
            result.append(buffer, bytesRead);
        }
    } while (bytesRead > 0);

    return result;
}

bool MettlerBalance::parseWeightResponse(const std::string& response, double& weight) {
    // 示例响应：S S      100.00 g[3](@ref)
    if (response.substr(0, 2) != "S ") return false;

    size_t unitPos = response.find_last_of(' ');
    size_t valuePos = response.find_last_of(' ', unitPos-1);

    try {
        weight = std::stod(response.substr(valuePos+1, unitPos-valuePos-1));
        return true;
    } catch (...) {
        return false;
    }
}



MettlerBalance::~MettlerBalance() {
    disconnect();
}
