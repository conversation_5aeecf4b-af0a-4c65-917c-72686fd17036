#pragma once
#include <string>
#include <glog.h>
#include <windows.h>
#include <gdiplus.h>
#include <memory>
#include <iostream>
#include <vector>
#include "qrcodegen.hpp"

#pragma comment(lib, "gdiplus.lib")

class LabelPrinter {
public:
    LabelPrinter();
    ~LabelPrinter();

    // 打印标签的主要接口
    bool PrintLabel(const std::wstring& sampleId, 
                   const std::wstring& qrData, 
                   const std::wstring& additionalInfo);

private:
    // 标签尺寸（像素，300DPI）
    const int width;   // 30mm
    const int height;  // 20mm
    bool CreateQRCode(const std::wstring& text, std::vector<uint8_t>& qrImage, int& size);
    bool ConfigurePrinter(const std::wstring& printerName = L"");
    bool CreateLabel(const std::wstring& sampleId,
                    const std::wstring& qrData,
                    const std::wstring& additionalInfo,
                    Gdiplus::Bitmap& labelImage);
    bool ShowPrintPreview(Gdiplus::Bitmap& labelImage);

    ULONG_PTR gdiplusToken;
};