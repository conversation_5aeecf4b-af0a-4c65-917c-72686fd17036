#include "LabelPrinter.h"
#include <chrono>
#include <iomanip>
#include <sstream>
#include <CommDlg.h>


LabelPrinter::LabelPrinter()
    : width(static_cast<int>(30 * 11.811))  // 30mm at 300DPI
    , height(static_cast<int>(20 * 11.811)) // 20mm at 300DPI
{
    Gdiplus::GdiplusStartupInput gdiplusStartupInput;
    Gdiplus::GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL);
}

LabelPrinter::~LabelPrinter()
{
    Gdiplus::GdiplusShutdown(gdiplusToken);
}



// 添加辅助函数用于宽字符串转换
std::string ws2s(const std::wstring& ws) {
    std::string s;
    int len = WideCharToMultiByte(CP_UTF8, 0, ws.c_str(), -1, nullptr, 0, nullptr, nullptr);
    if (len > 0) {
        s.resize(len - 1);  // 不包含结尾的null字符
        WideCharToMultiByte(CP_UTF8, 0, ws.c_str(), -1, &s[0], len, nullptr, nullptr);
    }
    return s;
}

bool LabelPrinter::PrintLabel(const std::wstring& sampleId,
                            const std::wstring& qrData,
                            const std::wstring& additionalInfo)
{
    try {
        LOG(INFO) << "开始打印标签 - 样品ID: " << ws2s(sampleId);
        
        // 创建标签图像
        Gdiplus::Bitmap labelImage(width, height);
        if (!CreateLabel(sampleId, qrData, additionalInfo, labelImage)) {
            LOG(ERROR) << "创建标签图像失败";
            return false;
        }
        LOG(INFO) << "标签图像创建成功";

        // 显示打印预览
        if (!ShowPrintPreview(labelImage)) {
            LOG(WARNING) << "用户取消打印";
            return false;
        }
        LOG(INFO) << "用户确认打印";

        // 获取默认打印机
        WCHAR printerName[256] = { 0 };
        DWORD printerNameSize = 256;
        GetDefaultPrinterW(printerName, &printerNameSize);
        LOG(INFO) << "获取默认打印机: " << ws2s(printerName);

        // // 配置打印机
        if (!ConfigurePrinter(printerName)) {
            LOG(ERROR) << "打印机配置失败";
            // return false;
        }
        LOG(INFO) << "打印机配置成功";

        // 创建打印DC
        HDC hdc = CreateDCW(L"WINSPOOL", printerName, NULL, NULL);
        if (!hdc) {
            LOG(ERROR) << "创建打印DC失败, 错误码: " << GetLastError();
            return false;
        }
        LOG(INFO) << "创建打印DC成功";

        // 设置打印机的映射模式和分辨率
        LOG(INFO) << "设置打印机分辨率 - DPI_X: " << GetDeviceCaps(hdc, LOGPIXELSX) 
                 << ", DPI_Y: " << GetDeviceCaps(hdc, LOGPIXELSY);
        SetMapMode(hdc, MM_ISOTROPIC);
        SetWindowExtEx(hdc, width, height, NULL);
        SetViewportExtEx(hdc, GetDeviceCaps(hdc, LOGPIXELSX) * width / 300,
                        GetDeviceCaps(hdc, LOGPIXELSY) * height / 300, NULL);

        // 开始打印
        DOCINFOW di = { sizeof(DOCINFOW) };
        di.lpszDocName = L"Label Print Job";
        di.lpszOutput = NULL;
        di.lpszDatatype = NULL;
        di.fwType = 0;
        if (StartDocW(hdc, &di) <= 0) {
            LOG(ERROR) << "StartDoc失败, 错误码: " << GetLastError();
            DeleteDC(hdc);
            return false;
        }
        LOG(INFO) << "StartDoc成功";

        if (StartPage(hdc) <= 0) {
            LOG(ERROR) << "StartPage失败, 错误码: " << GetLastError();
            EndDoc(hdc);
            DeleteDC(hdc);
            return false;
        }
        LOG(INFO) << "StartPage成功";

        // 使用GDI+绘制到打印机DC
        Gdiplus::Graphics graphics(hdc);
        graphics.SetPageUnit(Gdiplus::UnitPixel);
        graphics.SetInterpolationMode(Gdiplus::InterpolationModeHighQuality);
        graphics.SetSmoothingMode(Gdiplus::SmoothingModeHighQuality);

        // 计算打印位置
        int printX = (GetDeviceCaps(hdc, PHYSICALWIDTH) - width) / 2;
        int printY = 0;
        LOG(INFO) << "打印位置 - X: " << printX << ", Y: " << printY;
        
        SetViewportOrgEx(hdc, printX, printY, NULL);
        
        // 绘制图像
        Gdiplus::Status status = graphics.DrawImage(&labelImage, 0, 0, width, height);
        if (status != Gdiplus::Ok) {
            LOG(ERROR) << "DrawImage失败, 状态码: " << status;
            EndPage(hdc);
            EndDoc(hdc);
            DeleteDC(hdc);
            return false;
        }
        LOG(INFO) << "图像绘制成功";

        // 结束打印
        if (EndPage(hdc) <= 0) {
            LOG(ERROR) << "EndPage失败, 错误码: " << GetLastError();
            EndDoc(hdc);
            DeleteDC(hdc);
            return false;
        }

        EndDoc(hdc);
        DeleteDC(hdc);
        LOG(INFO) << "打印任务完成";
        return true;
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "打印过程发生异常: " << e.what();
        return false;
    }
    catch (...) {
        LOG(ERROR) << "打印过程发生未知异常";
        return false;
    }
}



bool LabelPrinter::CreateLabel(const std::wstring& sampleId,
                             const std::wstring& qrData,
                             const std::wstring& additionalInfo,
                             Gdiplus::Bitmap& labelImage)
{
    Gdiplus::Graphics graphics(&labelImage);
    graphics.Clear(Gdiplus::Color::White);

    // 创建二维码
    std::vector<uint8_t> qrImage;
    int qrSize;
    CreateQRCode(qrData, qrImage, qrSize);

    // 计算二维码位置和大小
    int qrDisplaySize = std::min<int>(height - 10, width / 3);  // 修复: 添加模板参数类型
    
    // 绘制二维码
    for (int y = 0; y < qrSize; ++y) {
        for (int x = 0; x < qrSize; ++x) {
            if (qrImage[y * qrSize + x]) {
                Gdiplus::SolidBrush brush(Gdiplus::Color::Black);
                float cellSize = static_cast<float>(qrDisplaySize) / qrSize;
                graphics.FillRectangle(&brush, 
                    5 + x * cellSize, 
                    (height - qrDisplaySize) / 2 + y * cellSize, 
                    cellSize, 
                    cellSize);
            }
        }
    }

    // 添加文字
    Gdiplus::FontFamily fontFamily(L"Microsoft YaHei");
    Gdiplus::Font font(&fontFamily, 12, Gdiplus::FontStyleRegular);
    Gdiplus::SolidBrush brush(Gdiplus::Color::Black);

    // 获取当前时间
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::wstringstream dateStr;
    std::tm tm;
    localtime_s(&tm, &time);
    dateStr << std::put_time(&tm, L"%Y-%m-%d");

    // 修复类型转换警告
    float textX = static_cast<float>(qrDisplaySize + 15);
    graphics.DrawString((L"样品ID: " + sampleId).c_str(), -1, &font, 
        Gdiplus::PointF(textX, 10), &brush);
    graphics.DrawString((L"信息: " + additionalInfo).c_str(), -1, &font, 
        Gdiplus::PointF(textX, 40), &brush);
    graphics.DrawString((L"日期: " + dateStr.str()).c_str(), -1, &font, 
        Gdiplus::PointF(textX, 70), &brush);

    return true;
}
bool LabelPrinter::CreateQRCode(const std::wstring& text, std::vector<uint8_t>& qrImage, int& size)
{
    try {
        // 将宽字符串转换为UTF-8编码的字符串
        int utf8Length = WideCharToMultiByte(CP_UTF8, 0, text.c_str(), -1, nullptr, 0, nullptr, nullptr);
        std::string utf8Text(utf8Length, 0);
        WideCharToMultiByte(CP_UTF8, 0, text.c_str(), -1, &utf8Text[0], utf8Length, nullptr, nullptr);

        // 创建QR码
        qrcodegen::QrCode qr = qrcodegen::QrCode::encodeText(utf8Text.c_str(), qrcodegen::QrCode::Ecc::MEDIUM);
        size = qr.getSize();
        qrImage.resize(size * size);

        // 将QR码数据转换为位图数据
        for (int y = 0; y < size; y++) {
            for (int x = 0; x < size; x++) {
                qrImage[y * size + x] = qr.getModule(x, y) ? 1 : 0;
            }
        }
        return true;
    }
    catch (...) {
        return false;
    }
}

bool LabelPrinter::ConfigurePrinter(const std::wstring& printerName)
{
    HANDLE hPrinter = nullptr;
    LOG(INFO) << "开始配置打印机: " << ws2s(printerName);

    if (!OpenPrinterW(const_cast<LPWSTR>(printerName.c_str()), &hPrinter, nullptr)) {
        LOG(ERROR) << "打开打印机失败, 错误码: " << GetLastError();
        return false;
    }

    try {
        // 获取所需缓冲区大小
        DWORD needed = 0;
        GetPrinterW(hPrinter, 2, nullptr, 0, &needed);
        if (needed == 0) {
            LOG(ERROR) << "获取打印机信息缓冲区大小失败";
            ClosePrinter(hPrinter);
            return false;
        }
        LOG(INFO) << "打印机信息缓冲区大小: " << needed << " 字节";

        // 分配内存
        std::vector<BYTE> buffer(needed);
        PRINTER_INFO_2W* pInfo = reinterpret_cast<PRINTER_INFO_2W*>(buffer.data());

        // 获取打印机信息
        if (!GetPrinterW(hPrinter, 2, buffer.data(), needed, &needed)) {
            LOG(ERROR) << "获取打印机信息失败, 错误码: " << GetLastError();
            ClosePrinter(hPrinter);
            return false;
        }

        // 配置 DEVMODE
        DEVMODEW* pDevMode = pInfo->pDevMode;
        if (pDevMode) {
            LOG(INFO) << "当前打印机设置:";
            LOG(INFO) << "  - 纸张大小: " << pDevMode->dmPaperSize;
            LOG(INFO) << "  - 打印质量: " << pDevMode->dmPrintQuality << " DPI";
            LOG(INFO) << "  - 打印方向: " << pDevMode->dmOrientation;

            // 设置要修改的字段标志
            pDevMode->dmFields |= DM_ORIENTATION;    // 打印方向
            pDevMode->dmFields |= DM_PAPERSIZE;      // 纸张大小
            pDevMode->dmFields |= DM_PAPERLENGTH;    // 纸张长度
            pDevMode->dmFields |= DM_PAPERWIDTH;     // 纸张宽度
            pDevMode->dmFields |= DM_PRINTQUALITY;   // 打印质量
            pDevMode->dmFields |= DM_DEFAULTSOURCE;  // 纸张来源
            pDevMode->dmFields |= DM_COPIES;         // 打印份数
            pDevMode->dmFields |= DM_COLOR;          // 颜色设置

            // 设置打印参数
            pDevMode->dmOrientation = DMORIENT_PORTRAIT;    // 纵向打印
            pDevMode->dmPaperSize = DMPAPER_USER;          // 自定义纸张
            pDevMode->dmPaperLength = 200;                 // 20mm (单位:0.1mm)
            pDevMode->dmPaperWidth = 300;                  // 30mm (单位:0.1mm)
            pDevMode->dmPrintQuality = 300;               // 300 DPI
            pDevMode->dmDefaultSource = DMBIN_AUTO;       // 自动选择纸张来源
            pDevMode->dmCopies = 1;                      // 打印份数
            pDevMode->dmColor = DMCOLOR_MONOCHROME;      // 黑白打印

            LOG(INFO) << "应用新的打印机设置";
            LOG(INFO) << "  - 纸张尺寸: " << pDevMode->dmPaperWidth/10.0 << "mm x " 
                     << pDevMode->dmPaperLength/10.0 << "mm";
            LOG(INFO) << "  - 打印质量: " << pDevMode->dmPrintQuality << " DPI";

            // 应用设置
            if (!SetPrinterW(hPrinter, 2, buffer.data(), 0)) {
                LOG(ERROR) << "应用打印机设置失败, 错误码: " << GetLastError();
                ClosePrinter(hPrinter);
                return false;
            }
            LOG(INFO) << "打印机设置应用成功";
        }
        else {
            LOG(ERROR) << "获取打印机DEVMODE失败";
            ClosePrinter(hPrinter);
            return false;
        }

        ClosePrinter(hPrinter);
        LOG(INFO) << "打印机配置完成";
        return true;
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "配置打印机时发生异常: " << e.what();
        if (hPrinter) {
            ClosePrinter(hPrinter);
        }
        return false;
    }
    catch (...) {
        LOG(ERROR) << "配置打印机时发生未知异常";
        if (hPrinter) {
            ClosePrinter(hPrinter);
        }
        return false;
    }
}

bool LabelPrinter::ShowPrintPreview(Gdiplus::Bitmap& labelImage)
{
    return true;
}