// ElectricGripperDriver.h
#ifndef ELECTRIC_GRIPPER_DRIVER_H
#define ELECTRIC_GRIPPER_DRIVER_H

#include <modbus.h>
#include <string>
#include <stdexcept>

class ElectricGripperDriver {
public:
    // 构造函数
    ElectricGripperDriver(const std::string& port, int baud_rate = 115200,
                        char parity = 'N', int data_bit = 8, int stop_bit = 1,
                        int slave_addr = 1);

    // 析构函数
    virtual ~ElectricGripperDriver();

    // 连接设备
    void connect();

    // 断开连接
    void disconnect();

    // 使能夹爪
    void enable();
    
    // 停用夹爪
    void disable();

    // 设置夹持参数
    void set_clamp_parameters(uint16_t position, uint16_t speed, uint16_t force);

    // 夹持操作
    void clamp();

    // 释放操作
    void release();

    // 全力打开夹爪
    void full_open();
    
    // 全力关闭夹爪
    void full_close();

    // 获取当前状态
    uint16_t get_status();
    
    // 旋转功能
    // 使能旋转功能
    void enable_rotation();
    
    // 停用旋转功能
    void disable_rotation();
    
    // 设置旋转参数（位置、速度、扭矩）
    void set_rotation_parameters(int16_t position, uint8_t speed, uint8_t torque);
    
    // 绝对位置旋转
    void rotate_absolute(int16_t position, uint8_t speed = 0xFF, uint8_t torque = 0xFF);
    
    // 相对位置旋转
    void rotate_relative(int16_t position, uint8_t speed = 0xFF, uint8_t torque = 0xFF);
    
    // 全力顺时针旋转360°
    void rotate_full_clockwise();
    
    // 全力逆时针旋转360°
    void rotate_full_counterclockwise();
    
    // 停止旋转
    void stop_rotation();
    
    // 获取旋转状态
    uint16_t get_rotation_status();
    
    // 获取旋转当前位置
    int16_t get_rotation_position();

    // 错误处理
    class ConnectionError : public std::runtime_error {
    public:
        using std::runtime_error::runtime_error;
    };

private:
    modbus_t* ctx_;
    std::string port_;
    int baud_;
    char parity_;
    int data_bit_;
    int stop_bit_;
    bool connected_;
    int slave_addr_;

    // MODBUS寄存器地址 - 夹持功能
    static const int CONTROL_REG        = 0x03E8;
    static const int PRESET_POS_REG     = 0x03EB;
    static const int PRESET_SPEED_REG   = 0x03EB; // 高字节
    static const int PRESET_FORCE_REG   = 0x03EC;
    static const int STATUS_REG         = 0x07D0;

    // MODBUS寄存器地址 - 旋转功能
    static const int ROTATION_CONTROL_REG         = 0x03E9;  // 旋转控制寄存器
    static const int ROTATION_PRESET_PARAM_REG    = 0x03E9;  // 旋转预设参数控制寄存器(高字节)
    static const int ROTATION_ABS_POS_REG         = 0x03EC;  // 旋转绝对位置寄存器
    static const int ROTATION_SPEED_REG           = 0x03ED;  // 旋转速度寄存器
    static const int ROTATION_TORQUE_REG          = 0x03ED;  // 旋转扭矩寄存器(高字节)
    static const int ROTATION_REL_POS_REG         = 0x03EE;  // 旋转相对位置寄存器
    static const int ROTATION_TRIGGER_REG         = 0x03EF;  // 旋转运动触发寄存器
    static const int ROTATION_REVS_REG            = 0x03EF;  // 旋转运动圈数(高字节)
    static const int ROTATION_STATUS_REG          = 0x07D1;  // 旋转状态寄存器
    static const int ROTATION_POS_STATUS_REG      = 0x07D4;  // 旋转绝对位置状态寄存器

    // 控制命令 - 夹持
    static const uint16_t CMD_CLAMP     = 0x0001;
    static const uint16_t CMD_RELEASE   = 0x0002;
    static const uint16_t CMD_STOP      = 0x0004;
    
    // 控制命令位 - 夹爪控制
    static const uint8_t GRIPPER_ACT    = 0x01;    // 使能夹爪
    static const uint8_t GRIPPER_MODE   = 0x02;    // 预设参数模式
    static const uint8_t GRIPPER_GTO    = 0x08;    // 运动到目标位置
    static const uint8_t GRIPPER_STOP   = 0x10;    // 停止夹爪
    
    // 预设参数命令 - 夹爪
    static const uint8_t PRESET_FULL_OPEN  = 0x03;  // 全力全速打开
    static const uint8_t PRESET_FULL_CLOSE = 0x04;  // 全力全速关闭
    
    // 控制命令位 - 旋转控制
    static const uint8_t ROT_ACT          = 0x01;  // 使能旋转
    static const uint8_t ROT_GTO          = 0x08;  // 运动到目标位置
    static const uint8_t ROT_STOP         = 0x10;  // 停止旋转
    
    // 预设参数命令 - 旋转
    static const uint8_t ROT_PRESET_CW    = 0x03;  // 全力全速顺时针旋转一圈
    static const uint8_t ROT_PRESET_CCW   = 0x04;  // 全力全速逆时针旋转一圈
    
    // 控制命令 - 旋转
    static const uint8_t ROT_ENABLE         = 0x01;  // 使能旋转
    static const uint8_t ROT_GOTO_POS       = 0x08;  // 运动到目标位置
    static const uint8_t ROT_ABS_MOVE       = 0x01;  // 绝对位置运动
    static const uint8_t ROT_REL_MOVE       = 0x02;  // 相对位置运动
};

#endif // ELECTRIC_GRIPPER_DRIVER_H
