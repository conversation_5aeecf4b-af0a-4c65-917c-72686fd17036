#include "ElectricGripperDriver.h"
#include <modbus.h>
#include <cstring>

ElectricGripperDriver::ElectricGripperDriver(const std::string& port, int baud,
                                           char parity, int data_bit, int stop_bit,
                                           int slave_addr)
    : port_(port), baud_(baud), parity_(parity),
      data_bit_(data_bit), stop_bit_(stop_bit), connected_(false),
      slave_addr_(slave_addr) {
    ctx_ = modbus_new_rtu(port_.c_str(), baud_, parity_, data_bit_, stop_bit_);
    if(ctx_ == nullptr) {
        throw ConnectionError("Failed to create MODBUS context");
    }
    modbus_set_slave(ctx_, slave_addr_);  // 使用参数化的从机地址
}

ElectricGripperDriver::~ElectricGripperDriver() {
    disconnect();
    if(ctx_) {
        modbus_free(ctx_);
    }
}

void ElectricGripperDriver::connect() {
    if(!connected_ && modbus_connect(ctx_) == 0) {
        connected_ = true;
    } else {
        throw ConnectionError("Failed to connect to device");
    }
}

void ElectricGripperDriver::disconnect() {
    if(connected_) {
        modbus_close(ctx_);
        connected_ = false;
    }
}

void ElectricGripperDriver::enable() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 发送使能命令 (rACT=1)
    if(modbus_write_register(ctx_, CONTROL_REG, GRIPPER_ACT) < 0) {
        throw ConnectionError("Failed to enable gripper");
    }
}

void ElectricGripperDriver::disable() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 发送停用命令 (rACT=0)
    if(modbus_write_register(ctx_, CONTROL_REG, 0x0000) < 0) {
        throw ConnectionError("Failed to disable gripper");
    }
}

void ElectricGripperDriver::set_clamp_parameters(uint16_t position, uint16_t speed, uint16_t force) {
    if(!connected_) throw ConnectionError("Not connected");

    // 写位置寄存器（0x03EB低字节）
    if(modbus_write_register(ctx_, PRESET_POS_REG, position) < 0) {
        throw ConnectionError("Failed to set position");
    }

    // 写速度寄存器（0x03EB高字节）
    if(modbus_write_register(ctx_, PRESET_SPEED_REG , speed) < 0) {
        throw ConnectionError("Failed to set speed");
    }

    // 写力寄存器（0x03EC低字节）
    if(modbus_write_register(ctx_, PRESET_FORCE_REG, force) < 0) {
        throw ConnectionError("Failed to set force");
    }
}

void ElectricGripperDriver::clamp() {
    if(!connected_) throw ConnectionError("Not connected");

    // 发送夹持命令
    if(modbus_write_register(ctx_, CONTROL_REG, CMD_CLAMP) < 0) {
        throw ConnectionError("Failed to send clamp command");
    }
}

void ElectricGripperDriver::release() {
    if(!connected_) throw ConnectionError("Not connected");

    // 发送释放命令
    if(modbus_write_register(ctx_, CONTROL_REG, CMD_RELEASE) < 0) {
        throw ConnectionError("Failed to send release command");
    }
}

void ElectricGripperDriver::full_open() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 使能夹爪、预设参数模式、移动到目标位置、全力全速打开
    uint16_t command = (PRESET_FULL_OPEN << 8) | (GRIPPER_ACT | GRIPPER_MODE | GRIPPER_GTO);
    
    if(modbus_write_register(ctx_, CONTROL_REG, command) < 0) {
        throw ConnectionError("Failed to execute full open command");
    }
}

void ElectricGripperDriver::full_close() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 使能夹爪、预设参数模式、移动到目标位置、全力全速关闭
    uint16_t command = (PRESET_FULL_CLOSE << 8) | (GRIPPER_ACT | GRIPPER_MODE | GRIPPER_GTO);
    
    if(modbus_write_register(ctx_, CONTROL_REG, command) < 0) {
        throw ConnectionError("Failed to execute full close command");
    }
}

uint16_t ElectricGripperDriver::get_status() {
    if(!connected_) throw ConnectionError("Not connected");

    uint16_t status;
    if(modbus_read_registers(ctx_, STATUS_REG, 1, &status) < 0) {
        throw ConnectionError("Failed to read status");
    }
    return status;
}

void ElectricGripperDriver::enable_rotation() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 使能旋转功能 (rACT=1)
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, ROT_ACT) < 0) {
        throw ConnectionError("Failed to enable rotation");
    }
}

void ElectricGripperDriver::disable_rotation() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 停用旋转功能 (rACT=0)
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, 0x0000) < 0) {
        throw ConnectionError("Failed to disable rotation");
    }
}

void ElectricGripperDriver::rotate_full_clockwise() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 使能旋转、预设参数模式、移动到目标位置、全力全速顺时针旋转
    uint16_t command = (ROT_PRESET_CW << 8) | (ROT_ACT | ROT_GTO);
    
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, command) < 0) {
        throw ConnectionError("Failed to execute full clockwise rotation");
    }
}

void ElectricGripperDriver::rotate_full_counterclockwise() {
    if(!connected_) throw ConnectionError("Not connected");
    
    // 使能旋转、预设参数模式、移动到目标位置、全力全速逆时针旋转
    uint16_t command = (ROT_PRESET_CCW << 8) | (ROT_ACT | ROT_GTO);
    
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, command) < 0) {
        throw ConnectionError("Failed to execute full counterclockwise rotation");
    }
}

void ElectricGripperDriver::set_rotation_parameters(int16_t position, uint8_t speed, uint8_t torque) {
    if(!connected_) throw ConnectionError("Not connected");

    // 写入旋转绝对位置寄存器(0x03EC)
    if(modbus_write_register(ctx_, ROTATION_ABS_POS_REG, position) < 0) {
        throw ConnectionError("Failed to set rotation position");
    }

    // 写入旋转速度寄存器(0x03ED低字节)
    if(modbus_write_register(ctx_, ROTATION_SPEED_REG, speed) < 0) {
        throw ConnectionError("Failed to set rotation speed");
    }

//    // 写入旋转扭矩寄存器(0x03ED高字节)
//    if(modbus_write_register(ctx_, ROTATION_TORQUE_REG + 0x100, torque) < 0) {
//        throw ConnectionError("Failed to set rotation torque");
//    }
}

void ElectricGripperDriver::rotate_absolute(int16_t position, uint8_t speed, uint8_t torque) {
    if(!connected_) throw ConnectionError("Not connected");

    // 设置旋转参数
    set_rotation_parameters(position, speed, torque);

    // 使能旋转控制寄存器(0x03E9低字节)
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, ROT_ACT | ROT_GTO) < 0) {
        throw ConnectionError("Failed to enable rotation");
    }

    // 触发绝对位置运动(0x03EF低字节)
    if(modbus_write_register(ctx_, ROTATION_TRIGGER_REG, ROT_ABS_MOVE) < 0) {
        throw ConnectionError("Failed to trigger absolute rotation");
    }
}

void ElectricGripperDriver::rotate_relative(int16_t position, uint8_t speed, uint8_t torque) {
    if(!connected_) throw ConnectionError("Not connected");

    // 写入旋转相对位置寄存器(0x03EE)
    if(modbus_write_register(ctx_, ROTATION_REL_POS_REG, position) < 0) {
        throw ConnectionError("Failed to set relative rotation position");
    }

    // 写入旋转速度寄存器(0x03ED低字节)
    if(modbus_write_register(ctx_, ROTATION_SPEED_REG, speed) < 0) {
        throw ConnectionError("Failed to set rotation speed");
    }

//    // 写入旋转扭矩寄存器(0x03ED高字节)
//    if(modbus_write_register(ctx_, ROTATION_TORQUE_REG + 0x100, torque) < 0) {
//        throw ConnectionError("Failed to set rotation torque");
//    }

    // 使能旋转控制寄存器(0x03E9低字节)
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, ROT_ACT | ROT_GTO) < 0) {
        throw ConnectionError("Failed to enable rotation");
    }

    // 触发相对位置运动(0x03EF低字节)
    if(modbus_write_register(ctx_, ROTATION_TRIGGER_REG, ROT_REL_MOVE) < 0) {
        throw ConnectionError("Failed to trigger relative rotation");
    }
}

void ElectricGripperDriver::stop_rotation() {
    if(!connected_) throw ConnectionError("Not connected");

    // 发送停止旋转命令
    if(modbus_write_register(ctx_, ROTATION_CONTROL_REG, ROT_ACT | ROT_STOP) < 0) {
        throw ConnectionError("Failed to stop rotation");
    }
}

uint16_t ElectricGripperDriver::get_rotation_status() {
    if(!connected_) throw ConnectionError("Not connected");

    uint16_t status;
    if(modbus_read_registers(ctx_, ROTATION_STATUS_REG, 1, &status) < 0) {
        throw ConnectionError("Failed to read rotation status");
    }
    return status;
}

int16_t ElectricGripperDriver::get_rotation_position() {
    if(!connected_) throw ConnectionError("Not connected");

    uint16_t position;
    if(modbus_read_registers(ctx_, ROTATION_POS_STATUS_REG, 1, &position) < 0) {
        throw ConnectionError("Failed to read rotation position");
    }
    return static_cast<int16_t>(position); // Need to cast since position is signed
}
