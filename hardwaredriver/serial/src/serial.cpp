//
// Created by mp on 19-1-21.
//


#include "serial.h"
#include "Executor.h"

std::shared_ptr<Fuxi::Common::Executor> _executor;

using namespace PLC;

SerialDriver::SerialDriver() {
    _executor = std::make_shared<Fuxi::Common::Executor>(1);
    hComm = INVALID_HANDLE_VALUE;
}

SerialDriver::~SerialDriver() {
    if (hComm != INVALID_HANDLE_VALUE) {
        ClosePort();
    }
}

bool
SerialDriver::OpenPort(const std::string &_port, int _baudrate, DataBits _dbits, Parity _parity, StopBits _stopbits) {
    char parity;
    int databits, stopbits;
    
    // 确保使用Windows格式的COM端口名称
    std::string portName = _port;
    // 如果端口名不是以\\.\开头，添加它以支持COM10以上的端口
    if (portName.find("\\\\") == std::string::npos && portName.find("COM") != std::string::npos) {
        portName = "\\\\.\\" + portName;
    }

    switch (_dbits) {
        case Data7:
            databits = 7;
            break;
        case Data8:
            databits = 8;
            break;
        default:
            databits = 8;
            break;
    }

    switch (_parity) {
        case Even:
            parity = 'E';
            break;
        case Odd:
            parity = 'O';
            break;
        case None:
            parity = 'N';
            break;
        default:
            parity = 'N';
            break;
    }

    switch (_stopbits) {
        case Stop1_0:
            stopbits = 1;
            break;
        case Stop2_0:
            stopbits = 2;
            break;
        default:
            stopbits = 1;
            break;
    }

    // 使用Windows API打开串口
    hComm = CreateFileA(
        portName.c_str(),
        GENERIC_READ | GENERIC_WRITE,
        0,                          // 不共享
        NULL,                       // 默认安全属性
        OPEN_EXISTING,              // 只打开存在的设备
        0,                          // 非重叠I/O
        NULL                        // 非模板文件
    );

    if (hComm == INVALID_HANDLE_VALUE) {
        return false;
    }

    // 设置端口参数
    int res = SetPort(_baudrate, databits, parity, stopbits);
    if (res == -1) {
        printf("set_port: Can't configure Serial Port %d, %d, %c, %d!\n", _baudrate, databits, parity, stopbits);
        CloseHandle(hComm);
        hComm = INVALID_HANDLE_VALUE;
        return false;
    }

    // 设置超时参数
    COMMTIMEOUTS timeouts = { 0 };
    timeouts.ReadIntervalTimeout = MAXDWORD;  // 不使用间隔超时
    timeouts.ReadTotalTimeoutMultiplier = 0;
    timeouts.ReadTotalTimeoutConstant = 0;    // 读取立即返回
    timeouts.WriteTotalTimeoutMultiplier = 0;
    timeouts.WriteTotalTimeoutConstant = 0;

    if (!SetCommTimeouts(hComm, &timeouts)) {
        CloseHandle(hComm);
        hComm = INVALID_HANDLE_VALUE;
        return false;
    }

    PortState = true;
    PortName = _port;
    return true;
}

int SerialDriver::SetPort(int iBaudRate, int iDataSize, char cParity, int iStopBit) {
    // 获取当前DCB结构
    if (!GetCommState(hComm, &dcb)) {
        return -1;
    }

    // 设置基本参数
    dcb.DCBlength = sizeof(DCB);
    
    // 明确指定标准波特率
    switch (iBaudRate) {
        case 110:
            dcb.BaudRate = CBR_110;
            break;
        case 300:
            dcb.BaudRate = CBR_300;
            break;
        case 600:
            dcb.BaudRate = CBR_600;
            break;
        case 1200:
            dcb.BaudRate = CBR_1200;
            break;
        case 2400:
            dcb.BaudRate = CBR_2400;
            break;
        case 4800:
            dcb.BaudRate = CBR_4800;
            break;
        case 9600:
            dcb.BaudRate = CBR_9600;
            break;
        case 14400:
            dcb.BaudRate = CBR_14400;
            break;
        case 19200:
            dcb.BaudRate = CBR_19200;
            break;
        case 38400:
            dcb.BaudRate = CBR_38400;
            break;
        case 57600:
            dcb.BaudRate = CBR_57600;
            break;
        case 115200:
            dcb.BaudRate = CBR_115200;
            break;
        case 128000:
            dcb.BaudRate = CBR_128000;
            break;
        case 256000:
            dcb.BaudRate = CBR_256000;
            break;
        default:
            // 对于非标准波特率，尝试直接设置
            // 注意：不是所有设备都支持任意波特率
            dcb.BaudRate = iBaudRate;
            printf("Warning: Non-standard baud rate: %d\n", iBaudRate);
            break;
    }
    
    dcb.ByteSize = iDataSize;

    // 设置校验位
    switch (cParity) {
        case 'N':                   // 无校验
            dcb.Parity = NOPARITY;
            break;
        case 'O':                    // 奇校验
            dcb.Parity = ODDPARITY;
            break;
        case 'E':                    // 偶校验
            dcb.Parity = EVENPARITY;
            break;
        default:
            return -1;
    }

    // 设置停止位
    switch (iStopBit) {
        case 1:
            dcb.StopBits = ONESTOPBIT;
            break;
        case 2:
            dcb.StopBits = TWOSTOPBITS;
            break;
        default:
            return -1;
    }

    // 设置流控制
    dcb.fBinary = TRUE;                // 二进制模式
    dcb.fOutxCtsFlow = FALSE;          // 无CTS流控制
    dcb.fOutxDsrFlow = FALSE;          // 无DSR流控制
    dcb.fDtrControl = DTR_CONTROL_ENABLE; // 启用DTR
    dcb.fDsrSensitivity = FALSE;       // 忽略DSR状态
    dcb.fOutX = FALSE;                 // 无XON/XOFF输出流控制
    dcb.fInX = FALSE;                  // 无XON/XOFF输入流控制
    dcb.fErrorChar = FALSE;            // 禁用错误替换
    dcb.fNull = FALSE;                 // 禁用空字符过滤
    dcb.fRtsControl = RTS_CONTROL_ENABLE; // 启用RTS
    dcb.fAbortOnError = FALSE;         // 错误时不中止

    // 应用设置
    if (!SetCommState(hComm, &dcb)) {
        return -1;
    }

    return 0;
}

int SerialDriver::ReadPort(void *buf, int iByte) {
    auto f = _executor->postTask<int>([&, buf, iByte]() {
        DWORD dwRead = 0;
        if (!iByte) {
            return 0;
        }
        
        if (!ReadFile(hComm, buf, iByte, &dwRead, NULL)) {
            return 0;
        }
        
        return (int)dwRead;
    });
    return f.get();
}

bool SerialDriver::WritePort(const void *buf, int iByte) {
    DWORD dwWritten = 0;
    
    if (!iByte) {
        return false;
    }
    
    unsigned char *_buf = (unsigned char *)buf;
    printf("write data: ");
    for (int ii = 0; ii < iByte; ii++)
        printf("%d ", _buf[ii]);
    printf("\n");
    
    if (!WriteFile(hComm, buf, iByte, &dwWritten, NULL)) {
        return false;
    }
    
    return (dwWritten == (DWORD)iByte);
}

int SerialDriver::ClosePort() {
    if (hComm == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    BOOL result = CloseHandle(hComm);
    hComm = INVALID_HANDLE_VALUE;
    PortState = false;
    PortName = "";
    
    return result ? 0 : -1;
}

int SerialDriver::GetComListFromReg(std::vector<std::string> &_PortList) {
    _PortList.clear();
    int count = 0;
    
    // 使用Windows注册表查询COM端口
    HKEY hKey;
    if (RegOpenKeyEx(HKEY_LOCAL_MACHINE, "HARDWARE\\DEVICEMAP\\SERIALCOMM", 0, KEY_READ, &hKey) != ERROR_SUCCESS) {
        return 0;
    }

    char valueName[256];
    char valueData[256];
    DWORD valueNameSize;
    DWORD valueDataSize;
    DWORD valueType;
    DWORD index = 0;

    while (true) {
        valueNameSize = sizeof(valueName);
        valueDataSize = sizeof(valueData);
        
        if (RegEnumValue(hKey, index, valueName, &valueNameSize, NULL, &valueType, 
                         (LPBYTE)valueData, &valueDataSize) != ERROR_SUCCESS) {
            break;
        }
        
        if (valueType == REG_SZ) {
            _PortList.push_back(std::string(valueData));
            count++;
        }
        
        index++;
    }

    RegCloseKey(hKey);
    return count;
}





