//
// Created by Administrator on 2025/4/11.
//

#ifndef AGILEROBOTDRIVER_H
#define AGILEROBOTDRIVER_H

#include <string>
#include <memory>
#include <vector>
#include <rw/math/Q.hpp>
#include <rw/math/Transform3D.hpp>
#include <Executor.h>

// Diana API相关定义
#define MAX_JOINT_COUNT 7  // Diana机器人为7轴
#define NO_ERROR_CODE 0

// 网络配置结构体
struct srv_net_st {
    char SrvIp[32];
    int HeartbeatPort;
    int StatePort;
    int CmdPort;
};

// 机器人状态信息结构体
struct StrRobotStateInfo {
    double jointPos[MAX_JOINT_COUNT];
    double jointAngularVel[MAX_JOINT_COUNT];
    double jointCurrent[MAX_JOINT_COUNT];
    double jointTorque[MAX_JOINT_COUNT];
    double tcpPos[6];
    double tcpExternalForce[6];
    bool bCollision;
    bool bTcpForceValid;
    double tcpForce[6];
    double jointForce[MAX_JOINT_COUNT];
};

// 关节运动方向枚举
enum joint_direction_e {
    T_MOVE_UP = 0,
    T_MOVE_DOWN = 1
};

// TCP运动方向枚举
enum tcp_direction_e {
    T_MOVE_X_UP = 0,
    T_MOVE_X_DOWN = 1,
    T_MOVE_Y_UP = 2,
    T_MOVE_Y_DOWN = 3,
    T_MOVE_Z_UP = 4,
    T_MOVE_Z_DOWN = 5
};

// TCP旋转方向枚举
enum tcp_rotation_e {
    T_ROT_X_UP = 0,
    T_ROT_X_DOWN = 1,
    T_ROT_Y_UP = 2,
    T_ROT_Y_DOWN = 3,
    T_ROT_Z_UP = 4,
    T_ROT_Z_DOWN = 5
};

// 回调函数类型定义
typedef void (*FNCERRORCALLBACK)(int errorCode, const char* strIpAddress);
typedef void (*FNSTATECALLBACK)(StrRobotStateInfo* pinfo, const char* strIpAddress);

// Diana API函数声明
extern "C" {
    void initSrvNetInfo(srv_net_st* pInfo);
    int initSrv(FNCERRORCALLBACK fnError, FNSTATECALLBACK fnState, srv_net_st* pinfo);
    int destroySrv(const char* strIpAddress = "");
    int stop(const char* strIpAddress = "");
    int moveJ(double* joints, double speed, double acc, int zv_shaper_order = 0, const char* strIpAddress = "");
    int moveL(double* pose, double speed, double acc, int zv_shaper_order = 0, const char* strIpAddress = "");
    int moveJToTarget(double* joints, double speed, double acc, int zv_shaper_order = 0, double zv_shaper_frequency = 0, double zv_shaper_damping_ratio = 0, const char* strIpAddress = "");
    int moveLToPose(double* pose, double speed, double acc, double* active_tcp = nullptr, int zv_shaper_order = 0, double zv_shaper_frequency = 0, double zv_shaper_damping_ratio = 0, bool avoid_singular = false, const char* strIpAddress = "");
    int moveTCP(tcp_direction_e dir, double velocity, double acceleration, const char* strIpAddress = "");
    int moveTCP(const char* strIpAddress = "");  // Simplified version for teach mode
    int rotationTCP(tcp_rotation_e dir, double velocity, double acceleration, const char* strIpAddress = "");
    int moveJoint(joint_direction_e dir, int jointIndex, double velocity, double acceleration, const char* strIpAddress = "");
    int moveJoint(const char* strIpAddress = "");  // Simplified version for teach mode
    int readDI(const char* group_name, const char* name, int& value, const char* strIpAddress = "");
    int writeDO(const char* group_name, const char* name, int value, const char* strIpAddress = "");
    int getTcpPos(double* pose, const char* strIpAddress = "");
    int getJointPos(double* joints, const char* strIpAddress = "");
    int getJointAngularVel(double* jointVels, const char* strIpAddress = "");
    int getJointVel(double* jointVels, const char* strIpAddress = "");  // Alternative velocity function
    int forward(double* joints, double* pose, double* active_tcp = nullptr, const char* strIpAddress = "");
    int inverse(double* pose, double* joints, int trace = 0, double* active_tcp = nullptr, const char* strIpAddress = "");
    const char* formatError(int errorCode);
    int getLastError(const char* strIpAddress = "");
}

class agilerobotDriver {
public:
    enum Teach_Mode
    {
        NO_TEACH = 0,
        JOINT_TEACH,
        TCP_TEACH
    };

    agilerobotDriver();

    ~agilerobotDriver();

    bool connect(std::string ipAddress);

    bool disconnect();

    bool stop();

    bool isConnect();

    bool movej(rw::math::Q q, double speed, int zone, double acc);

    bool movel(rw::math::Transform3D<> poseTarget, double speed, int zone, double acc);

    bool setDigitalOutput(int portIndex, bool value, bool reset=false);

    int getDigitalInput(int ioNum);

    bool setAcceleration(double acc, double ramp);

    rw::math::Transform3D<> getCurrentTCP();

    rw::math::Q getCurrentJointPositions();

    bool getForwardKinematics(rw::math::Q joint, rw::math::Transform3D<> &position);

    bool getInverseKinematics(rw::math::Q currentJoint, rw::math::Transform3D<> targetPosition,
                             rw::math::Q &resultJoint);

    bool startTeachMode(Teach_Mode mode);

    bool stopTeachMode();

    std::string getErrorMessage();



private:
    bool _isConnected;

    std::string _ipAddress;
    std::vector<double> _previousJointVelocity;
    std::vector<double> _currentJointVelocity;
    
    std::shared_ptr<Fuxi::Common::Executor> _messageHandler, _messageReceiver;
    
    bool waitForMotionComplete(int timeout_ms = 30000);
    bool checkJointMotion(const rw::math::Q& targetJoints, double tolerance = 0.01);
};

#endif //AGILEROBOTDRIVER_H
