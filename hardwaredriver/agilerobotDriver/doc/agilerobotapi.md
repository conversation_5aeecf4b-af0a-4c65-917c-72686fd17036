# f


## 第I页

- 概述 目录
- 兼容性说明
- 函数说明
- 1 initSrvNetInfo
- 2 initSrv
- 3 initSrvV2
- 4 destroySrv
- 5 setPushPeriod
- 6 moveTCP
- 7 rotationTCP
- 8 moveJoint
- 9 moveJToTarget
- 10 moveJToPose
- 11 moveJ
- 12 moveL
- 13 moveLToTarget
- 14 moveLToPose
- 15 speedJ
- 16 speedL
- 17 freeDriving
- 18 stop
- 19 forward
- 20 inverse
- 21 getJointPos
- 22 getJointAngularVel
- 23 getJointCurrent
- 24 getJointTorque
- 25 getTcpPos
- 26 getTcpExternalForce
- 27 releaseBrake
- 28 holdBrake
- 29 changeControlMode
- 30 getLibraryVersion
- 31 formatError
- 32 getLastError
- 33 setLastError
- 34 getLastWarning
- 35 setLastWarning
- 36 setDefaultActiveTcp..............................................................................................
- 37 getLinkState
- 38 getTcpForce
- 39 getJointForce
- 40 isCollision 第II页
- 41 initDHCali
- 42 getDHCaliResult
- 43 setDH
- 44 setWrd2BasRT
- 45 setFLa2TcpRT
- 46 getRobotState
- 47 resume
- 48 setJointCollision
- 49 setCartCollision
- 50 enterForceMode
- 51 leaveForceMode
- 52 setDefaultActiveTcpPose
- 53 setResultantCollision
- 54 setJointImpeda
- 55 getJointImpeda
- 56 setCartImpeda
- 57 getCartImpeda
- 58 zeroSpaceFreeDriving
- 59 createPath
- 60 addMoveL
- 61 addMoveJ
- 62 runPath
- 63 destroyPath
- 64 rpy2Axis
- 65 axis2RPY
- 66 homogeneous2Pose
- 67 pose2Homogeneous
- 68 enableTorqueReceiver
- 69 sendTorque_rt........................................................................................................
- 70 enableCollisionDetection
- 71 setActiveTcpPayload
- 72 servoJ
- 73 servoL
- 74 servoJ_ex
- 75 servoL_ex
- 76 speedJ_ex
- 77 speedL_ex
- 78 dumpToUDisk
- 79 inverse_ext
- 80 getJointLinkPos
- 81 createComplexPath
- 82 addMoveLByTarget
- 83 addMoveLByPose
- 84 addMoveJByTarget 第III页
- 85 addMoveJByPose
- 86 addMoveCByTarget
- 87 addMoveCByPose
- 88 runComplexPath
- 89 destroyComplexPath
- 90 saveEnvironment
- 91 enterForceMode_ex
- 92 readDI
- 93 readDO
- 94 readAI
- 95 readAO
- 96 setAIMode
- 97 writeDO
- 98 writeAO
- 99 readBusCurrent
- 100 readBusVoltage
- 101 getDH
- 102 getOriginalJointTorque
- 103 getJacobiMatrix
- 104 resetDH
- 105 runProgram
- 106 stopProgram
- 107 getVariableValue
- 108 setVariableValue
- 109 isTaskRunning
- 110 pauseProgram
- 111 resumeProgram
- 112 stopAllProgram
- 113 isAnyTaskRunning
- 114 cleanErrorInfo
- 115 setCollisionLevel
- 116 mappingInt8Variant
- 117 mappingDoubleVariant
- 118 mappingInt8IO
- 119 mappingDoubleIO
- 120 setMappingAddress
- 121 lockMappingAddress
- 122 unlockMappingAddress
- 123 getJointCount
- 124 getWayPoint
- 125 setWayPoint
- 126 addWayPoint
- 127 deleteWayPoint
- 128 getDefaultActiveTcp 第IV页
- 129 getDefaultActiveTcpPose......................................................................................
- 130 getActiveTcpPayload
- 131 zeroSpaceManualMove
- 132 moveTcp_ex
- 133 rotationTCP_ex
- 134 setExternalAppendTorCutoffFreq
- 135 poseTransform
- 136 setEndKeyEnableState
- 137 updateForce
- 138 inverseClosedFull
- 139 getInverseClosedResultSize
- 140 getInverseClosedJoints
- 141 destoryInverseClosedItems
- 142 nullSpaceFreeDriving
- 143 nullSpaceManualMove
- 144 getGravInfo
- 145 setGravInfo
- 146 getGravAxis
- 147 setGravAxis
- 148 speedLOnTcp
- 149 getTcpForceInToolCoordinate
- 150 calculateJacobi
- 151 calculateJacobiTF
- 152 getMechanicalJointsPositionRange
- 153 getMechanicalMaxJointsVel
- 154 getMechanicalMaxJointsAcc
- 155 getMechanicalMaxCartVelAcc
- 156 getJointsPositionRange
- 157 getMaxJointsVel
- 158 getMaxJointsAcc
- 159 getMaxCartTranslationVel
- 160 getMaxCartRotationVel
- 161 getMaxCartTranslationAcc
- 162 getMaxCartRotationAcc
- 163 setJointsPositionRange
- 164 setMaxJointsVel
- 165 setMaxJointsAcc
- 166 setMaxCartTranslationVel
- 167 setMaxCartRotationVel
- 168 setMaxCartTranslationAcc
- 169 setMaxCartRotationAcc
- 170 requireHandlingError
- 171 getJointsSoftLimitRange
- 172 setJointsSoftLimitRange 第V页
- 173 getFunctionOptI4
- 174 setFunctionOptI4
- 175 enterRescueMode
- 176 leaveRescueMode
- 177 getCartImpedanceCoordinateType......................................................................
- 178 setCartImpedanceCoordinateType
- 179 setJointLockedInCartImpedanceMode
- 180 getJointLockedInCartImpedanceMode
- 181 setThresholdTorque
- 182 getThresholdTorque
- 183 setHeartbeatParam
- 184 getHeartbeatParam
- 185 customRobotState
- 186 getCustomRobotState
- 187 getTcpPoseByTcpName
- 188 getTcpPoseByWorkPieceName
- 189 getPayLoadByTcpName
- 190 setDefaultToolTcpCoordinate
- 191 setDefaultWorkPieceTcpCoordinate
- 192 getDefaultTcpCoordinate
- 193 getDefaultWorkPieceCoordinate
- 194 setVelocityPercentValue
- 195 switchRescueMode
- 196 getAvoidSingular
- 197 setAvoidSingular
- 198 get_version
- 附件A：DianaApi接口错误码
- 附录B：如何确保运动学逆解唯一


```
第VI页
```
### 修订历史




## 概述

```
该操作库函数的所有输入输出参数，均采用国际单位，即力（N），扭矩（Nm），
```
电流（A），长度（m），线速度（m/s），线加速度（m/s^2 ），角度（rad），角速度

（rad/s），角加速度（rad/s^2 ），时间（s）。如无特殊说明，所有输入输出参数均为轴角

或轴角转换的齐次矩阵。另外，文档中涉及关节个数的位置均用JOINT_NUM表示，针对

Diana，JOINT_NUM=7，针对Thor，JOINT_NUM=6。

## 兼容性说明

## 函数说明

## 1 initSrvNetInfo

```
void initSrvNetInfo(srv_net_st* pInfo)
初始化机器人的网络结构体，这会使得全部端口随机分配。注：srv_net_st结构体最好均
通过initSrvNetInfo来初始化各个端口，然后再手动给pInfo->SrvIp赋值，见调用示例。
参数：
pInfo： 网络结构体，包含IP地址以及所需要的全部端口号。
返回值：
无
调用示例：
srv_net_st* pInfo = new srv_net_st();
initSrvNetInfo(pInfo);
strcpy(pInfo->SrvIp,"*************");
```
```
delete pInfo;
pInfo = nullptr;
```
## 2 initSrv


int initSrv(fnError, fnState, srv_net_st *pinfo)
初始化API，完成其他功能函数使用前的初始化准备工作。
参数：
fnError：错误处理回调函数。函数声明形式：FNCERRORCALLBACK fnError(int e) 其中
e为错误码（包含通信错误例如版本不匹配，链路错误例如网络断开，硬件故障例如编
码器错误等），可调用formatError获取字符串提示信息。fnError函数会用于多线程中实
时反馈，所以尽量不要在函数实现中使用sleep函数之类会阻塞线程的操作。
fnState：robot state回调函数。回调函数参数名为StrRobotStateInfo的结构体，包含：关
节角度数组（jointPos），关节角速度数组（jointAngularVel），关节电流数组
（jointCurrent），关节扭矩数组（jointTorque），TCP位姿向量（tcpPos），TCP外部力
（tcpExternalForce），是否发生碰撞标志（bCollision），TCP外部力是否有效标志
（bTcpForceValid），TCP六维力数组（tcpForce）和轴空间外部力数组（jointForce）。
pinfo：srv_net_st结构体指针，用于配置本地连接服务器、心跳服务和状态反馈服务的端
口号信息及服务器IP。端口号如果传 0 则由系统自动分配。
返回值：
0 ：成功。

- 1 ：失败。
调用示例：
#include "DianaAPIDef.h"
void logRobotState(StrRobotStateInfo *pinfo,const char *strIpAddress)
{
strIpAddress="*************";
static int staCnt = 1;
if((staCnt++ % 1000 == 0) && pinfo)
{
for(int i = 0; i < JOINT_NUM; ++i)
{
printf("jointPos[%d] = %f \n", i, pinfo->jointPos[i]);
printf("jointCurrent [%d] = %f \n", i, pinfo-> jointCurrent [i]);
printf("jointTorque [%d] = %f \n", i, pinfo-> jointTorque [i]);
if(i < 6)
{
printf("tcpPos [%d] = %f \n", i, pinfo-> tcpPos [i]);
}
}
    }


### }

```
void errorControl(int e,const char *strIpAddress )
{
strIpAddress="*************";
const char * strError = formatError(e); //该函数后面会介绍
printf("error code (%d):%s\n", e, strError);
}
```
```
srv_net_st * pinfo = new srv_net_st();
initSrvNetInfo(pinfo);
```
```
memcpy(pinfo ->SrvIp, "*************", strlen("*************"));
```
```
int ret = initSrv(errorControl, logRobotState, pinfo);
if(ret < 0)
{
printf("************* initSrv failed! Return value = %d\n", ret);
}
if(pinfo)
{
delete pinfo;
pinfo = nullptr;
}
destroySrv(strIpAddress);
```
## 3 initSrvV

```
int initSrvV2(fnError, fnWarning, fnState, srv_net_st *pinfo)
初始化API，完成其他功能函数使用前的初始化准备工作。比initSrv多了一个fnWarning
函数参数。
参数：
fnError：同initSrv。
fnWarning: 警告处理回调函数。函数声明形式 FNCWARNINGCALLBACK fnWarning(int
e, const char *strIpAddress) 其中e为警告码，strIpAddress为机械臂的IP地址字符串。
fnWarning函数会用于多线程中实时反馈，所以尽量不要在函数实现中使用sleep函数之
```

### 类会阻塞线程的操作。

fnState：同initSrv。
返回值：
0 ：成功。

- 1 ：失败。
调用示例：
#include "DianaAPIDef.h"
void logRobotState(StrRobotStateInfo *pinfo,const char *strIpAddress)
{
strIpAddress="*************";
static int staCnt = 1;
if((staCnt++ % 1000 == 0) && pinfo)
{
for(int i = 0; i < JOINT_NUM; ++i)
{
printf("jointPos[%d] = %f \n", i, pinfo->jointPos[i]);
printf("jointCurrent [%d] = %f \n", i, pinfo-> jointCurrent [i]);
printf("jointTorque [%d] = %f \n", i, pinfo-> jointTorque [i]);
if(i < 6)
{
printf("tcpPos [%d] = %f \n", i, pinfo-> tcpPos [i]);
}
}
    }
}
void errorControl(int e,const char *strIpAddress )
{
strIpAddress="*************";
    const char * strError = formatError(e); //该函数后面会介绍
    printf("error code (%d):%s\n", e, strError);
}

void warningControl(int w,const char *strIpAddress )
{
strIpAddress="*************";
printf("error code (%d) \n", e);
}


```
srv_net_st * pinfo = new srv_net_st();
initSrvNetInfo(pinfo);
```
```
memcpy(pinfo ->SrvIp, "*************", strlen("*************"));
```
```
int ret = initSrvV2(errorControl, warningControl , logRobotState, pinfo);
if(ret < 0)
{
printf("************* initSrvV2 failed! Return value = %d\n", ret);
}
if(pinfo)
{
delete pinfo;
pinfo = nullptr;
}
destroySrv(strIpAddress);
```
## 4 destroySrv

```
int destroySrv(const char* strIpAddress = "")
结束调用API，用于结束时释放指定IP地址机械臂的资源。如果该函数未被调用就退出
系统（例如客户端程序在运行期间崩溃），服务端将因为检测不到心跳而认为客户端异
常掉线，直至客户端再次运行，重新连接。除此之外不会引起严重后果。
参数：
strIpAddress:可选参数，需要释放服务资源的机械臂的IP地址字符串，如果为空，则会
释放全部已经成功initSrv的机械臂的资源。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = destroySrv(strIpAddress);
if(ret < 0)
{
printf("destroySrv failed! Return value = %d\n", ret);


### }

## 5 setPushPeriod

```
int setPushPeriod(int intPeriod, const char *strIpAddress = "")
设置指定IP地址机械臂的数据推送周期
参数：
intPeriod：输入参数。推送周期，单位为ms。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = setPushPeriod(10,strIpAddress);
if(ret < 0)
{
printf("setPushPeriod failed! Return value = %d\n", ret);
}

## 6 moveTCP

```
int moveTCP(d, v, a, active_tcp=nullptr, strIpAddress = "")
手动移动指定IP地址的机械臂工具中心点。该函数会立即返回，停止运动需要调用stop
函数。
参数：
d：表示移动方向的枚举类型，参考坐标系通过active_tcp指定，枚举及其含义如下
```
1. T_MOVE_X_UP表示沿x轴正向；
2. T_MOVE_X_DOWN表示沿x轴负向；
3. T_MOVE_Y_UP表示沿y轴正向；
4. T_MOVE_Y_DOWN表示沿y轴负向；
5. T_MOVE_Z_UP表示沿z轴正向；
6. T_MOVE_Z_DOWN表示沿z轴负向。
v：速度，单位：m/s。
a：加速度，单位：m/s^2 。
active_tcp：d参数的参考坐标系（基于法兰坐标系），大小为 6 的数组（位置和旋转矢
量（轴角））；为空时将参考基坐标系。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂


### 时生效。

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
tcp_direction_e dtype = T_MOVE_X_UP;
double vel = 0.1;
double acc = 0.2;
const char* strIpAddress = "*************";
int ret = moveTCP(dtype, vel, acc, nullptr, strIpAddress);
if(ret < 0)
{
printf("moveTCP failed! Return value = %d\n", ret);
}
M_SLEEP(3000);
stop(strIpAddress);
```
## 7 rotationTCP

```
int rotationTCP(d, v, a, active_tcp=nullptr, strIpAddress = "")
使指定的IP地址的机械臂绕当前工具中心点变换姿态。该函数会立即返回，停止运动需
要调用stop函数。
参数：
d：表示旋转方向的枚举类型，参考坐标系通过active_tcp指定，枚举及其含义如下：
```
1. T_MOVE_X_UP表示绕x轴正向旋转；
2. T_MOVE_X_DOWN表示绕x轴负向旋转；
3. T_MOVE_Y_UP表示绕y轴正向旋转；
4. T_MOVE_Y_DOWN表示绕y轴负向旋转；
5. T_MOVE_Z_UP表示绕z轴正向旋转；
6. T_MOVE_Z_DOWN表示绕z轴负向旋转。
v：速度，单位：rad/s。
a：加速度，单位：rad/s^2 。
active_tcp：d 参数的参考坐标系（基于法兰坐标系），大小为 6 的数组（位置和旋转矢
量（轴角）），旋转时仅参考方向，旋转中心是系统当前工具中心点；为空时使用基坐
标系，旋转中心仍是系统当前工具中心点。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：


### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
tcp_direction_e dtype = T_MOVE_X_UP;
double vel = 0.1;
double acc = 0.2;
const char* strIpAddress = "*************";
int ret = rotationTCP(dtype, vel, acc, nullptr, strIpAddress);
if(ret < 0)
{
printf("rotationTCP failed! Return value = %d\n", ret);
}
M_SLEEP(3000);
stop(strIpAddress);
```
## 8 moveJoint

```
moveJoint(d, i, v, a, strIpAddress = "")
手动控制指定IP地址的机械臂关节移动。该函数会立即返回，停止运动需要调用stop函
数。
参数：
d：表示关节移动方向的枚举类型。T_MOVE_ UP 表示关节沿正向旋转；
T_MOVE_DOWN表示关节沿负向旋转。
i：关节索引号。
v：速度，单位：rad/s。
a：加速度，单位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
joint_direction_e dtype = T_MOVE_UP;
int index = 1;
double vel = 0.8;
double acc = 0.8;
const char* strIpAddress = "*************";


```
int ret = moveJoint(dtype, index, vel, acc, strIpAddress);
if(ret < 0)
{
printf("moveJoint failed! Return value = %d\n", ret);
}
M_SLEEP(3000);
stop(strIpAddress);
```
## 9 moveJToTarget

```
int moveJToTarget(joints, v, a, zv_shaper_order=0, zv_shaper_frequency=0,
zv_shaper_damping_ratio=0, strIpAddress = "")
控制指定IP地址的机械臂以JOINT_NUM个关节角度为终点的moveJ。该函数会立即返
回，停止运动需要调用stop函数。
参数：
joints：终点关节角度数组首地址。
v：速度，单位：rad/s。
a：加速度，单位：rad/s^2 。
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
```
```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
void wait_move(const char* strIpAddress)
{
    M_SLEEP(20);
while (true)


### {

```
const char state = getRobotState(strIpAddress);
if (state != 0)
{
break;
}
else
{
M_SLEEP(1);
}
}
stop(strIpAddress);
}
double joints[JOINT_NUM] = {0.0};
double vel = 0.2;
double acc = 0.4;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
int ret = moveJToTarget(joints, vel, acc, zv_shaper_order, zv_shaper_frequency,
zv_shaper_damping_ratio, strIpAddress);
if(ret < 0)
{
printf("moveJToTarget failed! Return value = %d\n", ret);
}
wait_move(strIpAddress);
```
## 10 moveJToPose

```
int moveJToPose(pose, v, a, zv_shaper_order=0, zv_shaper_frequency=0,
zv_shaper_damping_ratio=0, active_tcp=nullptr, strIpAddress = "")
控制指定IP地址的机械臂以moveJ的方式移动指定的工具中心点至位姿pose。该函数会
立即返回，停止运动需要调用stop函数。
```
```
参数：
pose：基坐标系下的终点位姿数组首地址，数组长度为 6 。保存TCP坐标（x, y, z）和轴
角（rx, ry, rz）组合的矢量数据。
v：速度，单位：rad/s。
a：加速度，单位：rad/s^2 。
active_tcp：需要移动的工具中心点对应的位姿向量（基于法兰坐标系），大小为 6 的数
组（位置和旋转矢量（轴角））；为空时将移动系统当前工具的中心点至pose。（注
意：此处active_tcp代表需要移动的工具）
```

```
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
```
```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double poses[6] = {0.087,0.0,1.0827,0.0,0.0,0.0};
double vel = 0.2;
double acc = 0.4;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
const char* strIpAddress = "*************";
int ret = moveJToPose(poses, vel, acc, nullptr, zv_shaper_order, zv_shaper_frequency,
zv_shaper_damping_ratio, strIpAddress);
if(ret < 0)
{
printf("moveJToPose failed! Return value = %d\n", ret);
}
wait_move(strIpAddress);

## 11 moveJ

```
moveJ
宏定义，默认匹配moveJToTarget。
参数：
同moveJToTarget。
```

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
double joints[JOINT_NUM] = {0.0};
double vel = 0.2;
double acc = 0.4;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
const char* strIpAddress = "*************";
int ret = moveJ (joints, vel, acc,
zv_shaper_order,zv_shaper_frequency,zv_shaper_damping_ratio,strIpAddress);
if(ret < 0)
{
printf("moveJ failed! Return value = %d\n", ret);
}
wait_move(strIpAddress);
```
## 12 moveL

```
moveL
宏定义，默认匹配moveLToPose。
参数：
同moveLToPose。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double poses[6] = {0.087,0.0,1.0827,0.0,0.0,0.0};
double vel = 0.2;
double acc = 0.4;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
int ret = moveL (poses, vel, acc, nullptr, zv_shaper_order, zv_shaper_frequency,


```
zv_shaper_damping_ratio, avoid_singular,strIpAddress);
if(ret < 0)
{
printf("moveL failed! Return value = %d\n", ret);
}
wait_move(strIpAddress);
```
## 13 moveLToTarget

```
int moveLToTarget(joints, v, a, zv_shaper_order=0, zv_shaper_frequency=0,
zv_shaper_damping_ratio=0, avoid_singuar = false,strIpAddress = "")
控制指定IP地址的机械臂以JOINT_NUM个关节角度为终点的moveL。该函数会立即返
回，停止运动需要调用stop函数。
参数：
joints：终点关节角度数组首地址，数组长度为JOINT_NUM。
v：速度，单位：m/s。
a：加速度，单位：m/s^2 。
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
avoid_singular : 是否开启奇异点避让。strIpAddress：可选参数，需要控制机械臂的IP地
址字符串，不填仅当只连接一台机械臂时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double joints[JOINT_NUM] = {0.0};
double vel = 0.2;
double acc = 0.4;
int zv_shaper_order = 0;


```
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
int ret = moveLToTarget(joints, vel, acc, zv_shaper_order, zv_shaper_frequency,
zv_shaper_damping_ratio,avoid_singular);
if(ret < 0)
{
printf("moveLToTarget failed! Return value = %d\n", ret);
}
wait_move(strIpAddress);
```
## 14 moveLToPose

```
int moveLToPose(pose, v, a, active_tcp=nullptr, zv_shaper_order=0, zv_shaper_frequency=0,
zv_shaper_damping_ratio=0, avoid_singuar = false,strIpAddress = "")
控制指定IP地址的机械臂以moveL的方式移动工具中心点至位姿pose。该函数会立即返
回，停止运动需要调用stop函数。
```
```
参数：
pose：基坐标系下的终点位姿数组首地址，数组长度为 6 ，保存TCP坐标（x, y, z）和轴
角（rx, ry, rz）组合数据。
v：速度，单位：m/s。
a：加速度，单位：m/s^2 。
active_tcp：需要移动的工具中心点对应的位姿向量（基于法兰坐标系），大小为 6 的数
组（位置和旋转矢量（轴角））；为空时将移动系统当前工具的中心点至pose。（注
意：此处active_tcp代表需要移动的工具）
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
avoid_singular : 是否开启奇异点避让。
```

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double poses[6] = {0.087,0.0,1.0827,0.0,0.0,0.0};
double vel = 0.2;
double acc = 0.4;
double radius = 0.0;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
int ret = moveLToPose(poses, vel, acc, nullptr, zv_shaper_order, zv_shaper_frequency,
zv_shaper_damping_ratio, avoid_singular, strIpAddress);
if(ret < 0)
{
printf("moveLToPose failed! Return value = %d\n", ret);
}
wait_move(strIpAddress);

## 15 speedJ

int speedJ(speed, a, t, strIpAddress = "")
控制指定IP地址的机械臂进入速度模式，关节空间运动。时间t为非零时，控制指定IP
地址的机械臂将在t时间后减速。如果t为 0 ，机械臂将在达到目标速度时减速。该函数调
用后立即返回。停止运动需要调用stop函数。
该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使用
了传送带功能请注意规避。
参数：
speed：关节角速度数组首地址，数组长度为JOINT_NUM。单位：rad/s。
a：加速度，单位：rad/s^2 。
t：时间，单位：s。
strIpAddress：可选参数，需要控制机械臂的 IP地址字符串，不填仅当只连接一台机械臂
时生效。


## 16 speedL

```
int speedL(speed, a, t, active_tcp=nullptr, strIpAddress = "")
控制指定IP地址的机械臂进入速度模式，进行笛卡尔空间下直线运动，支持同步旋转，
但笛卡尔方向必须有速度或者加速度才能旋转。时间t为非零时，机器人将在t时间后减
速。如果t为 0 ，机械臂将在达到目标速度时减速。该函数调用后立即返回。停止运动需
要调用stop函数。
该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使
用了传送带功能请注意规避。
参数：
speed：基坐标系下的工具空间速度（位置和旋转），数组长度为 6,其中前 3 个单位为
m/s，后 3 个单位为rad/s。位置和旋转均参考基坐标系，旋转时的旋转中心可由active_tcp
指定。
a：加速度数组，数组长度为 2 ，单位：m/s^2 ，rad/s^2 。
t：时间，单位：s。
active_tcp：基于法兰坐标系的位姿，指定旋转时的旋转中心，大小为 6 的数组（位置+旋
转矢量（轴角））；为空时旋转中心是系统当前工具中心点。（注意：机械臂做直线运
动时中心点会随动，所以无旋转运动的情况下，此参数看不出影响）
```
### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

double speeds[JOINT_NUM] = {0.0};
speeds[0]=0.2;
double acc = 0.40;

const char* strIpAddress = "*************";
int ret = speedJ(speeds, acc, 0, strIpAddress);
if(ret < 0)
{
printf("speedJ failed! Return value = %d\n", ret);
}
M_SLEEP(5000);
stop(strIpAddress);


```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double speeds[6] = {0.1,0.0,0.0,0.0,0.0,0.0};
double acc[2] = {0.30, 0.50};
const char* strIpAddress = "*************";
int ret = speedL(speeds, acc, 0, nullptr, strIpAddress);
if(ret < 0)
{
printf("speedL failed! Return value = %d\n", ret);
}

## 17 freeDriving

```
int freeDriving(mode, strIpAddress = "")
实现控制指定IP地址的机械臂正常模式与零力驱动模式之间的切换。
参数：
mode：int变量，描述零力驱动工作模式， 0 表明退出零力驱动， 1 为进入正常零力驱动
模式。 2 为进入安全零力驱动模式。安全零力驱动模式进入之前必须先利用
enterRescueMode进入安全处理，再使用switchRescueMode(12)激活安全零力功能，见示
例。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = freeDriving(1, strIpAddress);
if(ret < 0)
{
printf("freeDriving failed! Return value = %d\n", ret);
}
freeDriving(0, strIpAddress);


### //安全零力驱动示例

```
enterRescueMode();
switchRescueMode(12);
releaseBrake();
freeDriving(2);
sleep(10);
freeDriving(0);
leaveRescueMode();
```
## 18 stop

```
int stop(strIpAddress = "")
控制指定IP地址的机械臂停止当前执行的任务。将会以最大加速度停止。
```
```
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = stop(strIpAddress);
if(ret < 0)
{
printf("stop failed! Return value = %d\n", ret);
}

## 19 forward

```
int forward(joints, pose, active_tcp=nullptr, strIpAddress = "")
正解函数，针对指定IP地址机器人，由传入的关节角计算出的正解TCP位姿。现支持在
不同工具坐标系下求正解，由传入的active_tcp决定。
参数：
joints：传入关节角度数组首地址，数组长度为JOINT_NUM。单位：rad。
pose：输出位姿数组首地址，数组长度为 6 。用于传递转化后的结果，数据为包含
active_tcp坐标（x, y, z）和旋转矢量（轴角坐标）组合。
```

```
active_tcp：工具坐标系对应的位姿向量，大小为 6 的数组，为空时，将使用默认的工具
坐标系default_tcp。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double joints[JOINT_NUM] = {0.0};
double pose[6] = {0.0};
const char* strIpAddress = "*************";
int ret = forward(joints, pose, nullptr, strIpAddress);
if(ret < 0)
{
printf("forward failed! Return value = %d\n", ret);
}
else
{
    printf("forward succeed! Pose: %f, %f, %f, %f, %f, %f\n",pose[0], pose[1], pose[2], pose[3],
pose[4], pose[5]);
}

## 20 inverse

```
int inverse(pose, joints, active_tcp=nullptr, strIpAddress = "")
逆解函数，针对指定IP地址机器人，通过TCP位姿计算出最佳逆解关节角度。现支持在
不同工具坐标系下求逆解，由传入的active_tcp决定。
参数：
pose：输入位姿数组首地址，数据为包含active_tcp坐标（x, y, z）和旋转矢量（轴角坐
标）组合。
joints：输出关节角度数组首地址，用于传递转换的结果。
active_tcp：工具坐标系对应的位姿向量，大小为 6 的数组，为空时，将使用默认的工具
坐标系default_tcp。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```

### - 1 ：失败。

### 调用示例：

```
double pose[6] = {0.4221, 0.0, 0.9403, 0.0, 0.0, 0.0};
double joints[JOINT_NUM] = {0.0};
const char* strIpAddress = "*************";
int ret = inverse(pose, joints, nullptr,strIpAddress);
if(ret < 0)
{
printf("inverse failed! Return value = %d\n", ret);
}
else
{
printf("inverse succeed! joints: %f, %f, %f, %f, %f, %f, %f\n",joints[0], joints[1], joints[2],
joints[3], joints[4], joints[5], joints[6]);
}
```
## 21 getJointPos

```
int getJointPos(joints, strIpAddress = "")
获取指定IP地址机械臂各个关节角度的位置，库初始化后，后台会自动同步机器人状态
信息，因此所有的监测函数都是从本地缓存取数。
参数：
joints：输出关节角数组首地址，数组长度为JOINT_NUM。用于传递获取到的结果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double joints[JOINT_NUM] = {0.0};
const char* strIpAddress = "*************";
int ret = getJointPos(joints, strIpAddress);
if(ret < 0)
{
printf("getJointPos failed! Return value = %d\n", ret);
}
else{
printf("getJointPos: %f, %f, %f, %f, %f, %f, %f\n", joints[0],


```
joints[1],joints[2],joints[3],joints[4],joints[5],joints[6]);
}
```
## 22 getJointAngularVel

```
int getJointAngularVel(vels, strIpAddress = "")
获取指定IP地址机械臂当前各关节的角速度。
参数：
vels：输出关节角速度数组首地址，数组长度为JOINT_NUM。用于传递获取到的结果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double speeds[JOINT_NUM] = {0.0};
const char* strIpAddress = "*************";
int ret = getJointAngularVel(speeds,strIpAddress);
if(ret < 0)
{
printf("getJointAngularVel failed! Return value = %d\n", ret);
}
else{
    printf("getJointAngularVel: %f, %f, %f, %f, %f, %f, %f\n", speeds[0],
speeds[1],speeds[2],speeds[3],speeds[4],speeds[5],speeds[6]);
}

## 23 getJointCurrent

```
int getJointCurrent(joints, strIpAddress = "")
获取当前关节电流。
参数：
joints：输出关节电流数组首地址，数组长度为 JOINT_NUM。用于传递转获取到的结
果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```

### - 1 ：失败。

### 调用示例：

```
double joints[JOINT_NUM] = {0.0};
strIpAddress="*************"
int ret = getJointCurrent(joints, strIpAddress);
if(ret < 0)
{
printf("getJointCurrent failed! Return value = %d\n", ret);
}
else
{
printf("getJointCurrent: %f, %f, %f, %f, %f, %f, %f\n", joints[0],
joints[1],joints[2],joints[3],joints[4],joints[5],joints[6]);
}
```
## 24 getJointTorque

```
int getJointTorque(torques, strIpAddress = "")
获取指定IP地址机械臂各关节真实扭矩数据，即减去零偏的数据
参数：
torques：输出关节扭矩数组首地址，数组长度为JOINT_NUM。用于传递获取到的结
果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double torques[JOINT_NUM] = {0.0};
const char* strIpAddress = "*************";
int ret = getJointTorque(torques, strIpAddress);
if(ret < 0)
{
printf("getJointTorque failed! Return value = %d\n", ret);
}
else
{
printf("getJointTorque: %f, %f, %f, %f, %f, %f, %f\n", torques[0],


```
torques[1],torques[2],torques[3],torques[4],torques[5],torques[6]);
}
```
## 25 getTcpPos

```
int getTcpPos(pose, strIpAddress = "")
获取指定IP地址机械臂当前 TCP位姿数据，TCP位姿可被 setDefaultActiveTcp和
setDefaultActiveTcpPose函数改变。
参数：
pose：输出TCP位姿数组首地址，数组长度为 6 。用于传递获取到的结果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double poses[6] = {0.0};
const char* strIpAddress = "*************";
int ret = getTcpPos(poses, strIpAddress);
if(ret < 0)
{
printf("getTcpPos failed! Return value = %d\n", ret);
}
else
{
printf("getTcpPos: %f, %f, %f, %f, %f, %f\n", poses[0],
poses[1],poses[2],poses[3],poses[4],poses[5]);
}

## 26 getTcpExternalForce

```
double getTcpExternalForce(strIpAddress = "")
获取指定IP地址机械臂TCP实际感受到的合力大小，TCP位姿可被setDefaultActiveTcp
函数改变。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

### 返回力的大小。

### 调用示例：

```
const char* strIpAddress = "*************";
double force = getTcpExternalForce(strIpAddress);
if(ret < 0)
{
printf("getTcpExternalForce failed! Return value = %d\n", ret);
}
else
{
printf("getTcpExternalForce: %f\n", force);
}
```
## 27 releaseBrake

```
int releaseBrake(strIpAddress = "")
打开指定IP地址机械臂的抱闸，启动机械臂。调用该接口后，需要调用者延时2s后再做
其他操作。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = releaseBrake (strIpAddress);
if(ret < 0)
{
printf("releaseBrake failed! Return value = %d\n", ret);
}
M_SLEEP(2000);

## 28 holdBrake

```
int holdBrake(strIpAddress = "")
关闭指定IP地址机械臂的抱闸，停止机械臂。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
```

### 时生效。

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
int ret = holdBrake (strIpAddress);
if(ret < 0)
{
printf("holdBrake failed! Return value = %d\n", ret);
}
```
## 29 changeControlMode

```
int changeControlMode(m, strIpAddress = "")
控制指定IP地址机械臂的模式切换。
参数：
m：枚举类型。
```
1. T_MODE_INVALID无意义；
2. T_MODE_POSITION位置模式；
3. T_MODE_JOINT_IMPEDANCE关节空间阻抗模式；
4. T_MODE_CART_IMPEDANCE笛卡尔空间阻抗模式。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = changeControlMode(T_MODE_POSITION, strIpAddress);
if(ret < 0)
{
printf("changeControlMode failed! Return value = %d\n", ret);
}

## 30 getLibraryVersion

```
unsigned short getLibraryVersion()
```

### 获取当前库的版本号。

### 参数：

### 无。

### 返回值：

### 当前版本号,高 8 位为主版本号，低 8 位为次版本号。

### 调用示例：

```
unsigned short uVersion = getLibraryVersion();
```
## 31 formatError

```
const char *formatError(e, const char* strIpAddress = "")
获取指定IP地址机械臂的错误码e的字符串描述，该错误码在初始化指定的回调函数中
会作为形参传入，也可以在函数调用失败后查询得到。对于错误码为- 2001 的硬件错误，
会延时回馈，一般建议对此类错误延时 100 毫秒后调用formatError函数获取具体硬件错
误提示信息，否则将提示"refresh later ..."而看不到具体内容。
参数：
e：错误码。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
错误描述信息。
调用示例：
int e = -1003;
const char* msg = formatError(e,"*************");
printf("error [%d] : %s\n",e, msg);
```
## 32 getLastError

```
int getLastError(const char* strIpAddress = "")
返回指定IP地址机械臂最近发生的错误码。该错误码会一直保存，确保可以查询得到，
直至库卸载，因此，当库函数调用失败后，如果想知道具体的错误原因，应该调用该函
数获取错误码。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：没有错误。
```

### 其它值：具体错误码。

### 调用示例：

```
const char* strIpAddress = "*************";
int e = getLastError(strIpAddress);
printf("getLastError code [%d] \n",e);
```
## 33 setLastError

```
int setLastError(int e, const char* strIpAddress = "")
重置指定IP地址机械臂错误码。将系统中记录的错误码重置为e，通常用于清除错误。
参数：
e：错误码。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
错误码。
调用示例：
const char* strIpAddress = "*************";
int e = setLastError(0, strIpAddress);
printf("setLastError code [%d] \n",e);
```
## 34 getLastWarning

```
int getLastWarning(const char* strIpAddress = "")
返回指定IP地址机械臂最近发生的警告码。该警告码会一直保存，确保可以查询得到，
直至库卸载，因此，当库函数调用失败后，如果想知道具体的警告原因，应该调用该函
数获取警告码。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：没有警告。
其它值：具体警告码。
调用示例：
const char* strIpAddress = "*************";
int w = getLastWarning(strIpAddress);
printf("getLastWarning code [%d] \n",w);
```

## 35 setLastWarning

```
int setLastWarning(int w, const char* strIpAddress = "")
重置指定IP地址机械臂警告码。将系统中记录的警告码重置为w，通常用于警告错误。
参数：
w：警告码。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
警告码。
调用示例：
const char* strIpAddress = "*************";
int w = setLastWarning(0, strIpAddress);
printf("setLastWarning code [%d] \n",w);
```
## 36 setDefaultActiveTcp..............................................................................................

```
int setDefaultActiveTcp(default_tcp , strIpAddress = "")
设置指定IP地址字符串的默认工具坐标系。在没有调用该函数时，默认工具中心点为法
兰盘中心，调用该函数后，默认的工具坐标系将被改变。该函数将会改变 moveTCP，
rotationTCP，moveJToPos，moveLToPose，speedJ，speedL，forward，inverse，
getTcpPos，getTcpExternalForce的默认行为。
参数：
default_tcp：输入参数，TCP相对于末端法兰盘的4*4齐次变换矩阵的首地址，数组长度
为 16 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = setDefaultActiveTcp(nullptr, strIpAddress);
if(ret < 0)
{
printf("setDefaultActiveTcp failed! Return value = %d\n", ret);
}


## 37 getLinkState

```
int getLinkState(strIpAddress = "")
获取与指定IP地址机械臂间的链路状态。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：链路正常。
```
- 1 ：链路断开。
调用示例：
const char* strIpAddress = "*************";
int ret = getLinkState(strIpAddress);
if(ret == 0)
{
    printf("network connected \n");
}
else
{
    printf("network disconnected \n");
}

## 38 getTcpForce

```
int getTcpForce(forces, strIpAddress = "")
获取指定IP地址机械臂的TCP所受外部六维力，TCP位姿可被setDefaultActiveTcp函数
改变。
参数：
forces：工具中心点处六维力数组的首地址，数组长度为 6 。用于传递获取到的结果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ： 获取到六维力且六维力有效。
```
- 1 ： 获取不到六维力矩，或六维力数值无效（发生奇异）。
调用示例：
double tcpForce[6];
const char* strIpAddress = "*************";


```
int ret = getTcpForce(tcpForce,strIpAddress);
if(ret == 0)
{
printf("get tcp force: %f, %f, %f, %f, %f, %f \n", tcpForce[0] , tcpForce[1] , tcpForce[2] ,
tcpForce[3] , tcpForce[4] , tcpForce[5] );
}
else
{
printf("get tcp force failed \n");
}
```
## 39 getJointForce

```
int getJointForce(forces, strIpAddress = "")
获取指定IP地址机械臂的轴空间JOINT_NUM个关节所受外力。
参数：
forces：关节轴力矩数组的首地址，数组长度为JOINT_NUM。用于传递获取到的结果。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：获取成功。
```
- 1 ：获取失败。
调用示例：
double jointForce[JOINT_NUM];
const char* strIpAddress = "*************";
int ret = getJointForce (jointForce, strIpAddress);
if(ret < 0)
{
printf("get joint force failed \n");
}
else
{
printf("get joint force: %f, %f, %f, %f, %f, %f, %f \n", jointForce[0] , jointForce[1] ,
jointForce[2] , jointForce[3] , jointForce[4] , jointForce[5], jointForce[6] );
}

## 40 isCollision

```
bool isCollision(strIpAddress = "")
```

### 从轴空间判断指定IP地址机械臂是否发生碰撞。

### 参数：

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
true：机器人发生碰撞。
false：机器人未发生碰撞。
调用示例：
const char* strIpAddress = "*************";
if(isCollision(strIpAddress))
{
printf("Warning: Robot got collision\n");
}
else
{
printf("Robot is running\n");
}
```
## 41 initDHCali

```
int initDHCali( tcpMeas, jntPosMeas, nrSets, strIpAddress = "")
根据输入的关节角以及TCP位置数组计算指定IP地址机械臂的DH参数。
参数：
tcpMeas：输入参数。TCP 位置数组的首地址，数组长度为3 * nrSets。每组数据为
[x,y,z]，共nrSets组。单位：米。
jntPosMeas：输入参数。关节角位置数组的首地址，数组长度为JOINT_NUM * nrSets，
每组数据为各关节角位置信息，共nrSets组。单位：弧度。
nrSets：输入参数。测量样本数量，最少 32 组，至少保证大于或等于需要辨识的参数。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：正常。
```
- 1 ：失败。
调用示例：
int rowNo_refData = 32
double jntMeas[JOINT_NUM*32] = {...};


```
double tcpMeas[96] = {...};
const char* strIpAddress = "*************";
if (initDHCali(tcpMeas, jntMeas, rowNo_refData, strIpAddress) != 0)
{
printf("DHCaliFcns.InitDHCali falid!\n");
}
```
## 42 getDHCaliResult

```
int getDHCaliResult(rDH, wRT, tRT, confid, strIpAddress = "")
获取指定IP地址机械臂DH参数的计算结果。
参数：
rDH：输出参数。机器人各关节DH参数数组的首地址，数组长度为4*JOINT_NUM。每
JOINT_NUM个数为一组，共四组数据[a，alpha，d，theta]。单位：rad、m。
wRT：输出参数。机器人基坐标系相对于世界坐标系下的位姿数组的首地址，数组长度
为 6 。位姿数据[x, y, z, Rx，Ry，Rz]。单位：rad、m。
tRT： 输出参数。靶球在法兰坐标系下的位置描述数组的首地址，数组长度为 3 。数组为
靶球位置坐标[x,y,z]。单位：m。
confid：输出参数。绝对定位精度参考值数组的首地址，数组长度为 2 。其中，第一个值
为标定前绝对定位精度，第二个值为标定后绝对定位精度。单位：m。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
该API接口一般不能直接调用，需要配合initDHCali接口使用，直接调用会失败。
返回值：
0 ：获取成功。
1 ：获取结果精度可能够较低。
```
- 1 ：获取失败，发生异常。
调用示例：
double rDH[4*JOINT_NUM], wRT[6], tRT[3], confid[2];
const char* strIpAddress = "*************";
if (getDHCaliResult(rDH, wRT, tRT, confid, strIpAddress) < 0)
{
printf("DHCaliFcns.getDHCaliResult falid.\n");
}

## 43 setDH

```
int setDH(a, alpha, d, theta, strIpAddress = "")
```

### 设置指定IP地址机械臂当前DH参数。特别注意，错误的参数设置可能引起机器人损

### 坏，需谨慎设置！

### 参数：

```
a：输入参数。各关节的a参数数组的首地址，数组长度为JOINT_NUM。
alpha：输入参数。各关节的alpha参数数组的首地址，数组长度为JOINT_NUM。
d：输入参数。各关节的d参数数组的首地址，数组长度为JOINT_NUM。
theta：输入参数。各关节的theta参数数组的首地址，数组长度为JOINT_NUM。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double a[JOINT_NUM], alpha[JOINT_NUM], d[JOINT_NUM], theta[JOINT_NUM];
const char* strIpAddress = "*************";
if (setDH(a, alpha, d, theta, strIpAddress) < 0)
{
    printf("DHCaliFcns.setDH falid.\n");
}

## 44 setWrd2BasRT

```
int setWrd2BasRT(RTw2b, strIpAddress = "")
初始化世界坐标系到指定IP地址机械臂坐标系的平移和旋转位姿。用于DH参数标定前
设置，若用户不能提供此参数，DH参数标定功能依旧可以使用。如果调用此函数则使
用用户自定义的位姿。特别注意，此功能每次移动机器人与激光跟踪仪都需要重新计
算，使用错误的参数可能引起DH参数计算不准确或标定异常。
参数：
RTw2b：输入参数。世界坐标系到机器人坐标系的平移和旋转位姿数组的首地址，数组
长度为 6 。单位：米和弧度。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：


```
double wRT[6] = {...}
const char* strIpAddress = "*************";
if (setWrd2BasRT(wRT, strIpAddress) < 0)
{
printf("DHCaliFcns. setWrd2BasRT falid.\n");
}
```
## 45 setFLa2TcpRT

```
int setFla2TcpRT(RTf2t, strIpAddress = "")
初始化指定IP地址机械臂法兰坐标系到工具坐标系的平移位置。用于DH参数标定前设
置，若用户不能提供此参数，DH参数标定功能依旧可以使用。如果调用此函数则使用
用户自定义的位姿。特别注意，此功能每次移动机器人与激光跟踪仪都需要重新计算，
使用错误的参数可能引起DH参数计算不准确或标定异常。
参数：
RTf2t：输入参数。初始化法兰坐标系到工具坐标系的平移位置数组的首地址，数组长度
为 3 ，位置信息数据[x,y,z]。单位：米。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double Fla[3] = {...}
const char* strIpAddress = "*************";
if (setFla2TcpRT (Fla, strIpAddress) < 0)
{
    printf("DHCaliFcns. setFla2TcpRT falid.\n");
}

## 46 getRobotState

```
const char getRobotState(strIpAddress = "")
获取指定IP地址机械臂当前工作状态。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

```
0 ：running（机械臂正在运动或者示教器程序正在运行）
1 ：paused（暂停正在运动的机械臂或者暂停正在运行的示教器程序）
2 ：idle（空闲状态）
3 ：free-driving（进入正常零力驱动模式）
4 ：zero-space-free-driving（进入零空间零力驱动模式）
5 ：hold-brake（机械臂关闭抱闸）
6 ：error（机械臂发生错误）
7 ：handling-error（机械臂的关节超出极限位置）
8 ：rescueMode（安全处理模式）
调用示例：
const char* strIpAddress = "*************";
const char state = getRobotState(strIpAddress);
if (0 == state)
{
printf("\t[robot state]:running\n");
}
else if (1 == state)
{
printf("\t[robot state]:paused\n");
}
else if (2 == state)
{
printf("\t[robot state]:idle\n");
}
else
{
printf("\t[robot state]: unknown state \n");
}
```
## 47 resume

```
int resume(strIpAddress = "")
当指定IP地址机械臂发生碰撞或其他原因暂停后，恢复运行时使用。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
if (resume(strIpAddress) < 0)
{
printf("Diana API resume failed!\n");
}
```
## 48 setJointCollision

```
int setJointCollision(collision,strIpAddress = "")
设置指定IP地址机械臂关节空间碰撞检测的力矩阈值。
参数：
collision：输入参数。JOINT_NUM个关节轴力矩阈值数组的首地址，数组长度为
JOINT_NUM。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：设置成功。
```
- 1 ：设置失败。
调用示例：
const char* strIpAddress = "*************";
double collision[7] = {200, 200, 200, 200, 200, 200, 200};//以 7 轴机器人为例
if (setJointCollision(collision, strIpAddress) < 0)
{
printf("Diana API setJointCollision failed!\n");
}

## 49 setCartCollision

```
int setCartCollision(collision,strIpAddress = "")
设置指定IP地址机械臂笛卡尔空间碰撞检测的六维力阈值。
参数：
collision：输入参数。在基坐标系下的六维力数组的首地址，数组长度为 6 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

### 0 ：设置成功。

### - 1 ：设置失败。

### 调用示例：

```
double collision[6] = { 200, 200, 200, 200, 200, 200};
const char* strIpAddress = "*************";
if (setCartCollision (collision, strIpAddress) < 0)
{
printf("Diana API setCartCollision failed!\n");
}
```
## 50 enterForceMode

```
int enterForceMode(intFrameType, dblFrameMatrix, dblForceDirection, dblForceValue,
dblMaxApproachVelocity, dblMaxAllowTcpOffset,strIpAddress = "")
使指定IP地址机械臂进入力控模式。
参数：
intFrameType：参考坐标系类型。 0 ：基坐标系； 1 ：工具坐标系； 2 ：自定义坐标系（暂
不支持）。
dblFrameMatrix：自定义坐标系矩阵（暂不支持），使用时传单位矩阵即可。
dblForceDirection：力指令的方向的数组首地址，数组长度为 3 。
dblForceValue：力指令的大小。单位：N。
dblMaxApproachVelocity： 最 大 接 近 速 度 。 单 位 ：m/s。 推 荐 取 值 范 围
（0.01m/s~0.5m/s）。
dblMaxAllowTcpOffset：允许的最大偏移。单位：m。推荐取值范围（0.01m~1m）。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int iFrameType = 1;
double dblFrameMatrix[16] = {1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1};
double dblForceDir[3]={0,0,-1};
double dblForceValue = 2.0;
double dblMaxVel = 0.1;
double dblMaxOffset = 0.2;
const char* strIpAddress = "*************";


```
if (enterForceMode(iFrameType, dblFrameMatrix, dblForceDir, dblForceValue, dblMaxVel,
dblMaxOffset,strIpAddress) < 0)
{
printf("Diana API enterForceMode failed!\n");
}
```
## 51 leaveForceMode

```
int leaveForceMode (intExitMode,strIpAddress = "")
设置指定IP地址机械臂退出力控模式,并设置退出后机械臂的工作模式。
参数：
intExitMode：控制模式。
0 ：代表位置模式；
1 ：代表关节空间阻抗模式；
2 ：代表笛卡尔空间阻抗模式。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int intExitMode = 0;
const char* strIpAddress = "*************";
if (leaveForceMode (intExitMode, strIpAddress) < 0)
{
printf("Diana API leaveForceMode failed!\n");
}

## 52 setDefaultActiveTcpPose

```
int setDefaultActiveTcpPose (arrPose,strIpAddress = "")
设置指定IP地址机械臂默认的工具坐标系。在没有调用该函数时，默认工具中心点为法
兰盘中心，调用该函数后，默认的工具坐标系将被改变。该函数将会改变 moveTCP，
rotationTCP，moveJToPos，moveLToPose，speedJ，speedL，forward，inverse，
getTcpPos，getTcpExternalForce的默认行为。调用该函数后，客户端生效有一个推送周
期的延迟，所以需要延迟一个推送周期的时间，再使用需要获取最新工具坐标系下的位
姿信息的函数，例如forward、 inverse等。
参数：
```

```
arrPose：输入参数。TCP相对于末端法兰盘的位姿向量的首地址，数组长度为 6 ，其
中，后三个角度为轴角
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double pose[6] = {0.1,0.1,0.1,0, 0, 0};
const char* strIpAddress = "*************";
if (setDefaultActiveTcpPose (pose,strIpAddress) < 0)
{
printf("Diana API setDefaultActiveTcpPose failed!\n");
}

## 53 setResultantCollision

```
int setResultantCollision (force,strIpAddress = "")
设置指定IP地址机械臂笛卡尔空间碰撞检测TCP的合力阈值。
参数：
force：合力值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double force = 8.9;
const char* strIpAddress = "*************";
if (setResultantCollision (force, strIpAddress) < 0)
{
printf("Diana API setResultantCollision failed!\n");
}

## 54 setJointImpeda

```
int setJointImpeda (arrStiff, dblDamp,strIpAddress = "")
设置指定IP地址机械臂各关节阻抗参数，包含刚度Stiffiness和阻尼比DampingRatio的
```

### 数据。

### 参数：

```
arrStiff：表示各关节刚度Stiffiness的数组的首地址，数组长度为JOINT_NUM。
dblDamp：表示关节阻尼比DampingRatio。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double arrStiff[7] = { 3000, 3000, 3000, 1000, 500, 1000, 1000};
double dblDamp = 0.7;
const char* strIpAddress = "*************";
if (setJointImpeda (arrStiff, dblDamp, strIpAddress) < 0)
{
printf("Diana API setJointImpeda failed!\n");
}

## 55 getJointImpeda

```
int getJointImpeda(arrStiff, dblDamp,strIpAddress = "")
获取指定IP地址机械臂各关节阻抗参数，包含刚度Stiffiness和阻尼比DampingRatio的
数据。
参数：
arrStiff：表示各关节刚度Stiffiness的数组的首地址，数组长度为JOINT_NUM，用于接
收获取到的值。
dblDamp：表示关节阻尼比DampingRatio。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double arrStiff [JOINT_NUM] = { 0};
double dblDamp= 0;
const char* strIpAddress = "*************";


```
if (getJointImpeda (arrStiff, &dblDamp, strIpAddress) < 0)
{
printf("Diana API getJointImpeda failed!\n");
}
else
{
printf("getJointImpeda : Stiff=%f, %f, %f, %f, %f, %f, %f\n Damp=%f \n",
arrStiff[0], arrStiff[1], arrStiff[2], arrStiff[3], arrStiff[4], arrStiff[5], arrStiff[6],
dblDamp);
}
```
## 56 setCartImpeda

```
int setCartImpeda (arrStiff, dblDamp, strIpAddress = "")
设置指定IP地址机械臂笛卡尔空间阻抗参数。
参数：
arrStiff：表示笛卡尔空间，各维度刚度Stiffiness的数组的首地址，数组长度为 6 。
dblDamp：表示笛卡尔空间的阻尼比DampingRatio。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double arrStiff[6] = { 1000, 1000, 1000, 500, 500, 500};
double dblDamp = 0.7;
const char* strIpAddress = "*************";
if (setCartImpeda(arrStiff, dblDamp, strIpAddress) < 0)
{
printf("Diana API setCartImpeda failed!\n");
}

## 57 getCartImpeda

```
int getCartImpeda (arrStiff, dblDamp, strIpAddress = "")
获取指定 IP地址机械臂笛卡尔空间各维度阻抗参数，包含刚度Stiffiness 和阻尼比
DampingRatio的数据。
参数：
arrStiff：表示笛卡尔空间，各维度刚度Stiffiness的数组的首地址，数组长度为 6 ，用于
```

### 接收获取到的值。

```
dblDamp：表示笛卡尔空间的阻尼比DampingRatio。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double arrStiff [6] = {0};
double dblDamp = 0;
const char* strIpAddress = "*************";
if (getCartImpeda (arrStiff, &dblDamp, strIpAddress) < 0)
{
printf("Diana API getCartImpeda failed!\n");
}
else
{
printf("getCartImpeda: Stiff=%f, %f, %f, %f, %f, %f\n Damp=%f \n",
arrStiff[0], arrStiff[1], arrStiff[2], arrStiff[3], arrStiff[4], arrStiff[5],
dblDamp);
}

## 58 zeroSpaceFreeDriving

```
int zeroSpaceFreeDriving (enable, strIpAddress = "")
控制指定IP地址机械臂进入或退出零空间自由驱动模式。
参数：
enable：输入参数。
true为进入零空间自由驱动模式；
false为退出零空间自由驱动模式。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";


```
if (zeroSpaceFreeDriving (true, strIpAddress) < 0)
{
printf("Diana API zeroSpaceFreeDriving failed!\n");
}
else
{
printf("Diana API zeroSpaceFreeDriving succeed!\n");
M_SLEEP(5000);
zeroSpaceFreeDriving(false, strIpAddress);
}
```
## 59 createPath

```
int createPath (type, id_path, strIpAddress = "")
为指定IP地址机械臂创建一个路段。
参数：
type：输入参数。1 :表示moveJ, 2：表示moveL。
id_path：输出参数。用于保存新创建Path的ID。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
详见addMoveJ或addMoveL调用示例。

## 60 addMoveL

```
int addMoveL (id_path, joints, vel, acc, blendradius, strIpAddress = "")
向指定IP地址机械臂已创建的路段添加MoveL路点。
参数：
id_path：输入参数。要添加路点的路径ID。
joints：输入参数。要添加的路点，即该路点的各关节角度数组的首地址，数组长度为
JOINT_NUM。单位：rad。
vel：moveL移动到目标路点的速度。单位：m/s。
acc：moveL移动到目标路点的加速度。单位：m/s^2 。
blendradius _percent：交融半径。单位：m。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
```

### 时生效。

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
#define PI 3.14159265358979323846
#define DEGREE_TO_RAD(x) ((x)*PI / 180.0)
#define RAD_TO_DEGREE(x) ((x)*180.0 / PI)
```
```
double dblFirstPosition[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(90.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0) };
double dblSecondPosition[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(120.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-60.0), DEGREE_TO_RAD(0.0) };
double dblThirdPosition[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(150.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-30.0), DEGREE_TO_RAD(0.0) };
```
```
unsigned int path_id = 0;
printf("start test moveJ path.\n");
const char* strIpAddress = "*************";
createPath(2, path_id,strIpAddress);
addMoveL(path_id, dblFirstMoveLPosition, 0.2, 0.2, 0.3,strIpAddress);
addMoveL(path_id, dblSecondMoveLPosition, 0.2, 0.2, 0.3,strIpAddress);
addMoveL(path_id, dblThirdMoveLPosition, 0.2, 0.2, 0.3,strIpAddress);
runPath(path_id,strIpAddress);
destroyPath(path_id,strIpAddress);
wait_move(strIpAddress);
```
## 61 addMoveJ

```
int addMoveJ (id_path, joints, vel_percent, acc_percent, blendradius_percent, strIpAddress = "")
向指定IP地址机械臂已创建的路段添加MoveJ路点。
参数：
id_path：输入参数。要添加路点的路径ID。
joints：输入参数。要添加的路点，即该路点的各关节角度数组的首地址，数组长度为
```

```
JOINT_NUM。单位：rad。
vel_percent：moveJ移动到目标路点的速度百分比。
acc_percent：moveJ移动到目标路点的加速度百分比。
blendradius _percent：交融半径百分比。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：

```
#define PI 3.14159265358979323846
#define DEGREE_TO_RAD(x) ((x)*PI / 180.0)
#define RAD_TO_DEGREE(x) ((x)*180.0 / PI)
```
```
double dblFirstPosition[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(90.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0) };
double dblSecondPosition[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(120.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-60.0), DEGREE_TO_RAD(0.0) };
double dblThirdPosition[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(150.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-30.0), DEGREE_TO_RAD(0.0) };
```
```
unsigned int path_id = 0;
printf("start test moveJ path.\n");
const char* strIpAddress = "*************";
createPath(1, path_id,strIpAddress);
addMoveJ(path_id, dblFirstPosition, 0.2, 0.2, 0.3,strIpAddress);
addMoveJ(path_id, dblSecondPosition, 0.2, 0.2, 0.3,strIpAddress);
addMoveJ(path_id, dblThirdPosition, 0.2, 0.2, 0.3,strIpAddress);
runPath(path_id,strIpAddress);
destroyPath(path_id,strIpAddress);
wait_move(strIpAddress);
```
## 62 runPath


```
int runPath (id_path, strIpAddress = "")
为指定IP地址机械臂启动运行设置好的路段。
参数：
id_path：输入参数。要运行的路径ID。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
详见addMoveJ或addMoveL调用示例。

## 63 destroyPath

```
int destroyPath (id_path, strIpAddress = "")
销毁指定IP地址机械臂某个路段。
参数：
id_path：输入参数。要销毁的路径ID。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
详见addMoveJ或addMoveL调用示例。

## 64 rpy2Axis

```
int rpy2Axis (arr)
欧拉角转轴角。
参数：
arr：输入参数。欧拉角数组的首地址，数组长度为 3 。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const double PI= 3.1415926;


```
double rpy[3]={PI, PI/3, PI/6};
int ret = rpy2Axis(rpy);
printf("Diana API rpy2Axis got: %f, %f, %f\n", rpy[0] , rpy[1] , rpy[2]);
输出结果：2.431323, 0.651471, -1.403725
```
## 65 axis2RPY

```
int axis2RPY (arr)
轴角转欧拉角。
参数：
arr：输入参数。轴角数组的首地址，数组长度为 3 。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const double PI= 3.1415926;
double rpy[3]={2.431323, 0.651471, -1.403725};
ret = axis2RPY(rpy);
printf("Diana API axis2Rpy got: %f, %f, %f\n", rpy[0] , rpy[1] , rpy[2]);
输出结果： - 3.141593, 1.047198, 0.523599

## 66 homogeneous2Pose

```
int homogeneous2Pose (matrix, pose)
齐次变换矩阵转位姿。
参数：
matrix：齐次变换矩阵数组的首地址，数组长度为 16 。
pose：位姿数组的首地址，数组长度为 6 。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double pose[6]={-0.231, 0.155, 0.934, PI, PI/3, PI/6};
double matrix[16]={ 0.433013, 0.250000, -0.866025, 0.000000, 0.500000, -0.866025, -
0.000000, 0.000000, -0.750000, -0.433013, -0.500000, 0.000000, -0.231000, 0.155000,
0.934000, 1.000000};
int ret = homogeneous2Pose(matrix, pose);
printf("Diana API pose2Homogeneous got: %f, %f, %f, %f, %f, %f\n",


```
pose[0] , pose[1] , pose[2] , pose[3], pose[4] , pose[5]);
输出结果：
Diana API pose2Homogeneous got: -0.231000, 0.155000, 0.934000, 2.431323, 0.651471, -
1.403724
```
## 67 pose2Homogeneous

```
int pose2Homogeneous (pose, matrix)
位姿转齐次变换矩阵。
参数：
pose：位姿数组的首地址，数组长度为 6 。
matrix：齐次变换矩阵数组的首地址，数组长度为 16 。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const double PI= 3.1415926;
double pose[6]={-0.231, 0.155, 0.934, PI, PI/3, PI/6};
double matrix[16]={0};
ret = pose2Homogeneous(pose, matrix);
printf("Diana API pose2Homogeneous got:
\n%f, %f, %f, %f\n%f, %f, %f, %f\n%f, %f, %f, %f\n%f, %f, %f, %f\n",
matrix[0] , matrix[1] , matrix[2] , matrix[3],
matrix[4] , matrix[5] , matrix[6] , matrix[7],
matrix[8] , matrix[9] , matrix[10] , matrix[11],
matrix[12] , matrix[13] , matrix[14] , matrix[15]);
输出结果：
Diana API pose2Homogeneous got:
0.758804, 0.546150, 0.354875, 0.000000
0.611590, -0.784849, -0.099843, 0.000000
0.223995, 0.292800, -0.929567, 0.000000
- 0.231000, 0.155000, 0.934000, 1.000000

## 68 enableTorqueReceiver

```
int enableTorqueReceiver(bEnable, strIpAddress = "")
在指定IP地址机械臂上，打开或关闭实时扭矩的接收。
参数：
bEnable：输入参数，是否开启实时扭矩的接收。
```

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
bool bEnable = true;
int ret = enableTorqueReceiver (bEnable,strIpAddress);
if(ret < 0)
{
printf("enableTorqueReceiver failed! Return value = %d\n", ret);
}

## 69 sendTorque_rt........................................................................................................

```
int sendTorque_rt(torque,t,strIpAddress = "")
对指定IP地址机械臂，用户发送实时扭矩
参数：
torque：输入参数，用户传入的扭矩值，大小为JOINT_NUM的数组。
t:持续时间，单位s
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
double dblTorque[JOINT_NUM] = {0.0};
double t = 1000;
int ret = sendTorque_rt(dblTorque,t,strIpAddress);
if(ret < 0)
{
printf("sendTorque_rt failed! Return value = %d\n", ret);
}

## 70 enableCollisionDetection


```
int enableCollisionDetection(bEnable,strIpAddress = "")
开启指定IP地址机械臂碰撞检测
参数：
bEnable：输入参数，是否开启碰撞检测模式。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = enableCollisionDetection(true,strIpAddress);
if(ret < 0)
{
printf("enableCollisionDetection failed! Return value = %d\n", ret);
}

## 71 setActiveTcpPayload

```
int setActiveTcpPayload(payload,strIpAddress = "")
设置指定IP地址机械臂的负载信息
参数：
payload：负载信息，第 1 位为质量，2~4位为质心，5~10位为张量，大小为 10 的数组
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
double dblPayload[10] = {0.0};
int ret = setActiveTcpPayload (dblPayload,strIpAddress);
if(ret < 0)
{
printf("setActiveTcpPayload failed! Return value = %d\n", ret);
}


## 72 servoJ

```
int servoJ (joints, time, look_ahead_time, gain, strIpAddress = "")
关节空间内，伺服指定IP地址机械臂到指定关节角位置。 servoJ函数用于在线控制机械
臂，lookahead时间和gain能够调整轨迹是否平滑或尖锐。 注意：太高的gain或太短的
lookahead时间可能会导致不稳定。由于该函数主要用于以较短位移为目标点的多次频繁
调用，建议在实时系统环境下使用。
该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使
用了传送带功能请注意规避。
参数：
joints：目标关节角位置数组的首地址，数组长度为JOINT_NUM。单位：rad。
time：运动时间。单位：s。
look_ahead_time：时间（S），范围（0.03-0.2）用这个参数使轨迹更平滑。
gain：目标位置的比例放大器，范围（100,2000）。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const double PI=3.1415926;
//以 7 轴机器人为例
double target1[7]={0, PI/6, 0, PI/2, 0, -PI/2, 0};
const char* strIpAddress = "*************";
for(int i = 0; i < 100; ++i)
{
target1[6] = target1[6]+PI/180;
ret = servoJ( target1, 0.01, 0.1, 300, strIpAddress);
if(ret < 0)
    break;
sleep(0.001);
}
stop(strIpAddress);

## 73 servoL

```
int servoL (pose, time, look_ahead_time, gain, scale, active_tcp=nullptr, strIpAddress = "")
```

### 笛卡尔空间内，控制指定IP地址机械臂工具中心点到指定位姿。由于该函数主要用于以

### 较短位移为目标点的多次频繁调用，建议在实时系统环境下使用。

### 该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使

### 用了传送带功能请注意规避。

### 参数：

```
pose：基坐标系下的目标位姿数组的首地址，数组长度为 6 。前三个元素单位：m；后三
个元素单位：rad，注意，后三个角度需要是轴角。
time：运动时间。单位：s。
look_ahead_time：时间（S），范围（0.03-0.2）用这个参数使轨迹更平滑。
gain：目标位置的比例放大器，范围（100,2000）。
scale：平滑比例系数。范围（0.0~1.0）。
active_tcp：需要移动的工具中心点对应的位姿向量（基于法兰坐标系），大小为 6 的数
组，为空时将移动系统当前工具的中心点至pose。（注意：此处active_tcp作为工具）
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const double PI=3.141592653;
const char* strIpAddress = "*************";
double joints[7]={0, 0, 0, PI/2, 0, -PI/2, 0};//以 7 轴机器人为例
moveJ(joints,0.1,0.1,strIpAddress);
wait_move(strIpAddress);
double pose[6]={0};
getTcpPos(pose,strIpAddress);
for(int i = 0; i < 1000; ++i){
pose[2] = pose[2] + 0.001;
ret = servoL(pose, 0.01, 0.1, 300, 1.0, nullptr, strIpAddress);
if(ret < 0)
break;
sleep(0.001);
}
stop(strIpAddress);

## 74 servoJ_ex


```
int servoJ_ex (joints, time, look_ahead_time, gain, realiable, strIpAddress = "")
关节空间内，伺服指定IP地址机械臂到指定关节角位置优化版。 servoJ_ex函数用于在
线控制机器人，lookahead时间和gain能够调整轨迹是否平滑或尖锐。 注意：太高的gain
或太短的lookahead时间可能会导致不稳定。由于该函数主要用于以较短位移为目标点的
多次频繁调用，建议在实时系统环境下使用。
该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使
用了传送带功能请注意规避。
参数：
joints：目标关节角位置数组的首地址，数组长度为JOINT_NUM。单位：rad。
time：运动时间。单位：s。
look_ahead_time：时间（S），范围（0.03-0.2）用这个参数使轨迹更平滑。
gain：目标位置的比例放大器，范围（100,2000）。
realiable：bool型变量，值为true需要socket反馈通信状态，行为等同servoJ；值为false
则无需反馈直接返回。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
timespec next_time;
//获取系统时间存入next_time
    const char* strIpAddress = "*************";
for (int i = 0; i < 50000; i++)
{
target_point[0] = (cos(2 * PI / 5 * i * 0.001) - 1) * PI / 3;
servoJ_ex(target_point, 0.001, 0.1, 500, false, strIpAddress);
next_time.tv_nsec += 1000000;//计算下次唤醒时间
//sleep到next_time
}
stop(strIpAddress);

## 75 servoL_ex

```
int servoL_ex (pose, time, look_ahead_time, gain, scale,
realiable,active_tcp=nullptr,strIpAddress = "")
```

### 笛卡尔空间内，控制指定IP地址机械臂工具中心点到指定位姿优化版。由于该函数主要

### 用于以较短位移为目标点的多次频繁调用，建议在实时系统环境下使用。

### 该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使

### 用了传送带功能请注意规避。

### 参数：

pose：基坐标系下的目标位姿数组的首地址，数组长度为 6 。前三个元素单位：m；后三
个元素单位：rad。注意，后三个角度需要是轴角。
time：运动时间。单位：s。
look_ahead_time：时间（S），范围（0.03-0.2）用这个参数使轨迹更平滑。
gain：目标位置的比例放大器，范围（100,2000）。
scale：平滑比例系数。范围（0.0~1.0）。
realiable：bool型变量，值为true需要socket反馈通信状态，行为等同servoJ；值为false
则无需反馈直接返回。
active_tcp：需要移动的工具中心点对应的位姿向量（基于法兰坐标系），大小为 6 的数
组，为空时将移动系统当前工具的中心点至pose。（注意：此处active_tcp作为工具）
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。

- 1 ：失败。
调用示例：
const double PI=3.141592653;
const char* strIpAddress = "*************";
double joints[7]={0, 0, 0, PI/2, 0, -PI/2, 0};//以 7 轴机器人为例
moveJ(joints,0.1,0.1);
wait_move(strIpAddress);
double pose[6]={0};
getTcpPos(pose,strIpAddress);
for(int i = 0; i < 1000; ++i){
pose[2] = pose[2] + 0.001;
ret = servoL_ex(pose, 0.01, 0.1, 300, 1.0,true,nullptr, strIpAddress);
if(ret < 0)
break;
sleep(0.001);
}


```
stop(strIpAddress);
```
## 76 speedJ_ex

```
int speedJ_ex (speed, a, t, realiable, strIpAddress = "")
速度模式优化版，使指定IP地址机械臂进行关节空间运动。时间t为非零时，机器人将
在t时间后减速。如果 t为 0 ，机器人将在达到目标速度时减速。该函数调用后立即返
回。停止运动需要调用stop函数。
该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使
用了传送带功能请注意规避。
参数：
speed：关节角速度数组首地址，数组长度为JOINT_NUM。单位：rad/s。
a：加速度，单位：rad/s^2 。
t：时间，单位：s。
realiable：bool型变量，值为true需要socket反馈通信状态，行为等同speedJ；值为false
则无需反馈直接返回。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double speeds[JOINT_NUM] = {0.0};
speeds[1]=0.2;
double acc = 0.40;
const char* strIpAddress = "*************";
int ret = speedJ_ex(speeds, acc, 0, true, strIpAddress);
if(ret < 0)
{
printf("speedJ_ex failed! Return value = %d\n", ret);
}

## 77 speedL_ex

```
int speedL_ex(speed, a, t,,realiable, active_tcp=nullptr, strIpAddress = "")
速度模式优化版，使指定IP地址机械臂笛卡尔空间下直线运动，支持同步旋转，但笛卡
尔方向必须有速度或者加速度才能旋转。时间t为非 0 时，机器人将在t时间后减速。如
果t为 0 ，机器人将在达到目标速度时减速。该函数调用后立即返回。停止运动需要调用
```

```
stop函数。
该函数暂不支持传送带跟踪期间使用，目前没有主动报错，使用时会卡住程序，如果使
用了传送带功能请注意规避。
参数：
speed：基坐标系下的工具空间速度，数组长度为 6 （位置和旋转），其中前 3 个单位为
m/s，后 3 个单位为rad/s。位置和旋转均参考基坐标系，旋转时的旋转中心可由active_tcp
指定。
a：加速度数组，数组长度为 2 ，单位：m/s^2 ，rad/s^2 。
t：时间，单位：s。
realiable：bool型变量，值为true需要socket反馈通信状态，行为等同speedL；值为false
则无需反馈直接返回。
active_tcp：基于法兰坐标系的位姿，指定旋转时的旋转中心，大小为 6 的数组（位置+旋
转矢量（轴角））；为空时旋转中心是系统当前工具中心点。（注意：机械臂做直线运
动时中心点会随动，所以无旋转运动的情况下，此参数看不出影响）
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double speeds[6] = {0.1,0.0,0.0,0.0,0.0,0.0};
double acc[2] = {0.30, 0.50};
const char* strIpAddress = "*************";
int ret = speedL_ex(speeds, acc, 0, true, nullptr, strIpAddress);
if(ret < 0)
{
printf("speedL_ex failed! Return value = %d\n", ret);
}

## 78 dumpToUDisk

```
int dumpToUDisk (time_out, strIpAddress = "")
导出指定IP地址机械臂的日志文件到u盘。控制箱中的系统日志文件（主要包含
ControllerLog.txt和DianaServerLog.txt）会自动复制到u盘。需要注意的是目前控制箱仅
支持FAT32格式u盘，调用dumpToUDisk函数前需先插好u盘，如果系统日志拷贝失败
将不会提示。
```

### 参数：

```
time_out：单位 秒，设置超时时间，一般需要大于 3 秒,- 1 表示设置超时时间无穷大。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
1. 系统开机
2. 插入u盘到控制箱
3. 调用Api函数dumpToUDisk(-1,"*************")
4. 拔下u盘查看

## 79 inverse_ext

```
int inverse_ext(ref_joints, pose,joints,active_tcp=nullptr, strIpAddress = "")
针对指定IP地址机器人，逆解函数，给定一个参考关节角，算出欧式距离最近的逆解。
现支持在不同工具坐标系下求逆解，由传入的active_tcp决定。
参数：
ref_joints：参考的关节角，大小为JOINT_NUM的数组。
pose：输入位姿数组首地址，数据为包含active_tcp坐标（x, y, z）和旋转矢量（轴角坐
标）组合。
joints：输出关节角度数组首地址，用于传递转换的结果，大小为JOINT_NUM的数组。
active_tcp：工具坐标系对应的位姿向量，大小为 6 的数组，为空时，将使用默认的工具
坐标系default_tcp。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double ref_joints[JOINT_NUM] = {0.0};
double pose[6] = {0.4221, 0.0, 0.9403, 0.0, 0.0, 0.0};
double joints[JOINT_NUM] = {0.0};
const char* strIpAddress = "*************";
int ret = inverse_ext(ref_joints,pose, joints, nullptr,strIpAddress);
if(ret < 0)


### {

```
printf("inverse_ext failed! Return value = %d\n", ret);
}
else
{
printf("inverse_ext :%f, %f, %f, %f, %f, %f, %f\n", joints[0], joints[1], joints[2], joints[3],
joints[4], joints[5], joints[6]);
}
```
## 80 getJointLinkPos

```
int getJointLinkPos(joints,strIpAddress = "")
获取指定IP地址机械臂当前低速侧关节角
参数：
joints：输出参数。低速侧关节角,大小为JOINT_NUM的数组。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
double joints[JOINT_NUM] = {0.0};
int ret = getJointLinkPos(joints,strIpAddress);
if(ret < 0)
{
printf("getJointLinkPos failed! Return value = %d\n", ret);
}
else
{
printf("getJointLinkPos :%f, %f, %f, %f, %f, %f, %f\n", joints[0], joints[1], joints[2],
joints[3], joints[4], joints[5], joints[6]);
}

## 81 createComplexPath

```
int createComplexPath (complex_path_type, complex_path_id, strIpAddress = "")
在指定IP地址机械臂上创建一个复杂路段。
参数：
```

```
complex_path_type：输入参数。
0 :表示NORMAL_JOINT，该模式下路径中可以添加moveL、moveJ、moveC对应的路径
点，路径点用JOINT_NUM个关节角度确定。
1 ：表示MOVEP_JOINT，该模式下机械臂关节做点对点的匀速运动，路径中可以添加
moveL、moveC对应的路径点，不可以添加moveJ对应的路径点，路径点用JOINT_NUM
个关节角度确定。
2:表示NORMAL_POSE ，该模式下路径中可以添加moveL、moveJ、moveC对应的路径
点，路径点用末端TCP位姿数据确定。
3 ：表示 MOVEP_POSE，该模式下机械臂末端做点对点的匀速运动，路径中可以添加
moveL、moveC对应的路径点，不可以添加moveJ对应的路径点，路径点用末端TCP位
姿数据确定。
complex_path_id：输出参数。用于保存新创建complexPath的ID。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
详见addMoveJByTarget或addMoveLByTarget调用示例。

## 82 addMoveLByTarget

```
int addMoveLByTarget (complex_path_id, target_joints, vel, acc, blendradius,
zv_shaper_order=0, zv_shaper_frequency=0, zv_shaper_damping_ratio=0,avoid_singular
=false, strIpAddress = "")
在指定IP地址机械臂上，向已创建的路段添加MoveL路点。
参数：
complex_path_id：输入参数。要添加路点的路径ID。
target_joints：输入参数。要添加的路点，即该路点的各关节角度数组的首地址，数组长
度为JOINT_NUM。单位：rad。
vel：moveL移动到目标路点的速度。单位：m/s。
acc：moveL移动到目标路点的加速度。单位：m/s2。
blendradius：交融半径。单位：m。
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
```

zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
avoid_singular : 是否开启奇异点避让。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。

- 1 ：失败。
调用示例：

#define PI 3.14159265358979323846
#define DEGREE_TO_RAD(x) ((x)*PI / 180.0)
#define RAD_TO_DEGREE(x) ((x)*180.0 / PI)

double dblFirstPosition [JOINT_NUM] = {DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(90.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0)};
double dblSecondPosition [JOINT_NUM] = {DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(150.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(-30.0), DEGREE_TO_RAD(0.0) };

unsigned int uintComplexPathId = 0;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
createComplexPath(NORMAL_JOINT_PATH, uintComplexPathId, strIpAddress);
addMoveLByTarget(uintComplexPathId, dblFirstPosition, 0.2, 0.4, 0, zv_shaper_order,
zv_shaper_frequency, zv_shaper_damping_ratio, avoid_singular, strIpAddress);
addMoveLByTarget(uintComplexPathId, dblSecondPosition, 0.2, 0.2, 0, zv_shaper_order,


```
zv_shaper_frequency, zv_shaper_damping_ratio, avoid_singular, strIpAddress);
runComplexPath(uintComplexPathId, strIpAddress);
destroyComplexPath(uintComplexPathId, strIpAddress);
wait_move(strIpAddress);
```
## 83 addMoveLByPose

```
int addMoveLByPose(complex_path_id, target_pose, vel, acc, blendradius, zv_shaper_order=0,
zv_shaper_frequency=0, zv_shaper_damping_ratio=0, avoid_singular =false, strIpAddress = "")
在指定IP地址机械臂上，向已创建的路段添加MoveL路点。
参数：
complex_path_id,：输入参数。要添加路点的路径ID。
target_pose：输入参数。路径点位姿数组首地址，数组长度为 6 ，保存TCP坐标（x, y,
z）和轴角（rx, ry, rz）组合数据。
vel：moveL移动到目标路点的速度。单位：m/s。
acc：moveL移动到目标路点的加速度。单位：m/s2。
blendradius：交融半径。单位：m。
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
avoid_singular : 是否开启奇异点避让。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double dblFirstPose [6] = { 0.4000, 0.0, 0.3000, 0.0, 0.0, 0.0 };
double dblSecondPose [6] = { 0.1000, 0.0, 0.8000, 0.0, 0.0, 0.0 };
unsigned int uintComplexPathId = 0;


```
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
createComplexPath(NORMAL_POSE_PATH, uintComplexPathId, strIpAddress);
addMoveLByPose(uintComplexPathId, dblFirstPose, 0.2, 0.4, 0, zv_shaper_order,
zv_shaper_frequency, zv_shaper_damping_ratio, avoid_singular, strIpAddress);
addMoveLByPose(uintComplexPathId, dblSecondPose, 0.2, 0.2, 0, zv_shaper_order,
zv_shaper_frequency, zv_shaper_damping_ratio, avoid_singular, strIpAddress);
runComplexPath(uintComplexPathId, strIpAddress);
destroyComplexPath(uintComplexPathId, strIpAddress);
wait_move(strIpAddress);
```
## 84 addMoveJByTarget

```
int addMoveJByTarget (complex_path_id, target_joints, vel_percent, acc_percent,
blendradius_percent, zv_shaper_order=0, zv_shaper_frequency=0,
zv_shaper_damping_ratio=0, strIpAddress = "")
在指定IP地址机械臂上，向已创建的路段添加MoveJ路点。
参数：
complex_path_id：输入参数。要添加路点的路径ID。
target_joints：输入参数。要添加的路点，即该路点的各关节角度数组的首地址，数组长
度为JOINT_NUM。单位：rad。
vel_percent：moveJ移动到目标路点的速度百分比，相对于系统安全中设定关节最大速度
的百分比。
acc_percent：moveJ移动到目标路点的加速度百分比，相对于系统安全中设定关节最大
加速度的百分比。
blendradius _percent：交融半径百分比，相对于该轨迹最大交融半径的百分比。
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
```

### 机械臂默认配置参数。

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
#define PI 3.14159265358979323846
#define DEGREE_TO_RAD(x) ((x)*PI / 180.0)
#define RAD_TO_DEGREE(x) ((x)*180.0 / PI)

```
double dblFirstPosition [JOINT_NUM] = {DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(90.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0)};
double dblSecondPosition [JOINT_NUM] = {DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(150.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(-30.0), DEGREE_TO_RAD(0.0) };
```
```
unsigned int uintComplexPathId = 0;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
const char* strIpAddress = "*************";
createComplexPath(NORMAL_JOINT_PATH, uintComplexPathId, strIpAddress);
addMoveJByTarget (uintComplexPathId, dblFirstPosition, 0.2, 0.4, 0, zv_shaper_order,
zv_shaper_frequency, zv_shaper_damping_ratio, strIpAddress);
addMoveJByTarget(uintComplexPathId, dblSecondPosition, 0.2, 0.2, 0, zv_shaper_order,
zv_shaper_frequency, zv_shaper_damping_ratio, strIpAddress);
runComplexPath(uintComplexPathId, strIpAddress);
destroyComplexPath(uintComplexPathId, strIpAddress);
wait_move(strIpAddress);
```
## 85 addMoveJByPose

```
int addMoveJByPose (complex_path_id, target_pose, vel_percent, acc_percent,
blendradius_percent, zv_shaper_order=0, zv_shaper_frequency=0,
zv_shaper_damping_ratio=0, strIpAddress = "")
```

在指定IP地址机械臂上，向已创建的路段添加MoveJ路点。
参数：
complex_path_id：输入参数。要添加路点的路径ID。
target_pose：输入参数。要添加的路点，路径点位姿数组首地址，数组长度为 6 ，保存
TCP坐标（x, y, z）和轴角（rx, ry, rz）组合数据。
vel_percent：moveJ移动到目标路点的速度百分比。相对于系统安全中设定关节最大速度
的百分比。
acc_percent：moveJ移动到目标路点的加速度百分比。相对于系统安全中设定关节最大
加速度的百分比。
blendradius _percent：交融半径百分比。相对于该轨迹最大交融半径的百分比。
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。

strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。

- 1 ：失败。
调用示例：
double dblFirstPose [6] = { 0.4000, 0.0, 0.3000, 0.0, 0.0, 0.0 };
double dblSecondPose [6] = { 0.1000, 0.0, 0.8000, 0.0, 0.0, 0.0 };
unsigned int uintComplexPathId = 0;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
const char* strIpAddress = "*************";
createComplexPath(NORMAL_POSE_PATH, uintComplexPathId, strIpAddress);
addMoveJByPose(uintComplexPathId, dblFirstPose, 0.2, 0.4, 0, zv_shaper_order,


```
zv_shaper_frequency, zv_shaper_damping_ratio, strIpAddress);
addMoveJByPose(uintComplexPathId, dblSecondPose, 0.2, 0.2, 0, zv_shaper_order,
zv_shaper_frequency, zv_shaper_damping_ratio, strIpAddress);
runComplexPath(uintComplexPathId, strIpAddress);
destroyComplexPath(uintComplexPathId, strIpAddress);
wait_move(strIpAddress);
```
## 86 addMoveCByTarget

```
int addMoveCByTarget (complex_path_id, pass_joints, target_joints, vel, acc, blendradius,
ignore_rotation, zv_shaper_order=0, zv_shaper_frequency=0, zv_shaper_damping_ratio=0,
avoid_singular = false, strIpAddress = "")
在指定IP地址机械臂上，向已创建的路段添加MoveC路点。
参数：
complex_path_id：输入参数。要添加路点的路径ID。
pass_joints：输入参数。要添加的moveC中间路点，即该路点的各关节角度数组的首地
址，数组长度为JOINT_NUM。单位：rad。
target_joints：输入参数。要添加的moveC目标路点，即该路点的各关节角度数组的首地
址，数组长度为JOINT_NUM。单位：rad。
vel：moveC移动到目标路点的速度。单位：m/s。
acc：moveC移动到目标路点的加速度。单位：m/s2。
blendradius：交融半径。单位：m。
ignore_rotation：是否忽略姿态变化
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
avoid_singular : 是否开启奇异点避让。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
#define PI 3.14159265358979323846
#define DEGREE_TO_RAD(x) ((x)*PI / 180.0)
#define RAD_TO_DEGREE(x) ((x)*180.0 / PI)
```
```
double dblFirstPosition [JOINT_NUM] = { DEGREE_TO_RAD(20.0), DEGREE_TO_RAD(-
30.0), DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(120.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0) };
double dblSecondPosition [JOINT_NUM] = { DEGREE_TO_RAD(50.0),
DEGREE_TO_RAD(-30.0), DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(120.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0) };
```
```
unsigned int uintComplexPathId = 0;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
createComplexPath(NORMAL_JOINT_PATH, uintComplexPathId, strIpAddress);
addMoveCByTarget (uintComplexPathId, dblFirstPosition, dblSecondPosition, 0.2, 0.4, 0,true,
zv_shaper_order, zv_shaper_frequency, zv_shaper_damping_ratio,
avoid_singular ,strIpAddress);
runComplexPath(uintComplexPathId, strIpAddress);
destroyComplexPath(uintComplexPathId, strIpAddress);
wait_move(strIpAddress);
```
## 87 addMoveCByPose

```
int addMoveCByPose (complex_path_id, pass_pose, target_pose, vel, acc, blendradius,
ignore_rotation, zv_shaper_order=0, zv_shaper_frequency=0, zv_shaper_damping_ratio=0,
avoid_singular = false, strIpAddress = "")
在指定IP地址机械臂上，向已创建的路段添加MoveC路点。
参数：
complex_path_id：输入参数。要添加路点的路径ID。
pass _pose：输入参数。要添加的moveC中间路点，即路径点位姿数组首地址，数组长度
为 6 ，保存TCP坐标（x, y, z）和轴角（rx, ry, rz）组合数据。
```

target_pose：输入参数。要添加的moveC目标路点，即该路径点位姿数组首地址，数组
长度为 6 ，保存TCP坐标（x, y, z）和轴角（rx, ry, rz）组合数据。
vel：moveL移动到目标路点的速度。单位：m/s。
acc：moveL移动到目标路点的加速度。单位：m/s^2 。
blendradius：交融半径。单位：m。
ignore_rotation：是否忽略姿态变化
zv_shaper_order，zv_shaper_frequency，zv_shaper_damping_ratio为整形功能相关参数，
函数中默认值都为 0 ，此时使用机械臂默认配置参数。
zv_shaper_order：阶次，整数型参数，范围[-1,2]，其中取值- 1 时，关闭整形功能， 当
zv_shaper_order， zv_shaper_frequency， zv_shaper_damping_ratio任意一值不为 0 ，则使
用函数传入的阶次值。
zv_shaper_frequency：频率，浮点型参数，单位：Hz。支持取值范围[0，1000]，取值为
0 时，使用机械臂默认配置参数。
zv_shaper_damping_ratio：阻尼比，浮点型参数。支持取值范围[0,1]，取值为 0 时，使用
机械臂默认配置参数。
avoid_singular : 是否开启奇异点避让。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。

- 1 ：失败。
调用示例：
double dblFirstPose [6] = { 0.269391, 0.1000, 0.663360, 0.0, 0.0, 0.0 };
double dblSecondPose [6] = { 0.269391, 0.1000, 0.863360, 0.0, 0.0, 0.0 };
unsigned int uintComplexPathId = 0;
int zv_shaper_order = 0;
double zv_shaper_frequency = 0;
double zv_shaper_damping_ratio = 0;
bool avoid_singular = false;
const char* strIpAddress = "*************";
createComplexPath(NORMAL_POSE_PATH, uintComplexPathId, strIpAddress);
addMoveCByPose(uintComplexPathId, dblFirstPose, dblSecondPose, 0.2, 0.4, 0,true,
zv_shaper_order, zv_shaper_frequency, zv_shaper_damping_ratio, avoid_singular ,
strIpAddress);
runComplexPath(uintComplexPathId, strIpAddress);


```
destroyComplexPath(uintComplexPathId, strIpAddress);
wait_move(strIpAddress);
```
## 88 runComplexPath

```
int runComplexPath (complex_path_id, strIpAddress = "")
在指定IP地址机械臂上，启动运行设置好的路段。
参数：
complex_path_id：输入参数。要运行的路径ID。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
详见addMoveJByTarget或addMoveLByTarget调用示例。

## 89 destroyComplexPath

```
int destroyComplexPath (complex_id_path, strIpAddress = "")
在指定IP地址机械臂上，销毁某个路段。
参数：
complex_id_path：输入参数。要销毁的路径ID。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
详见addMoveJBytarget或addMoveLByTarget调用示例。

## 90 saveEnvironment

```
int saveEnvironment (strIpAddress = "")
将指定IP地址机械臂的控制器当前参数数据写入配置文件，用于重启机器人时初始化设
置各参数，包括碰撞检测阈值、阻抗参数、DH参数等所有可通过API设置的参数数据。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
```

### 时生效。

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
int ret = saveEnvironment (strIpAddress);
if(ret < 0)
{
printf("Save failed!\n");
}
```
## 91 enterForceMode_ex

```
int enterForceMode_ex(dblForceDirection,dblForceValue,dblMaxApproachVelocity,
dblMaxAllowTcpOffset, double *dblActiveTcp = nullptr,strIpAddress = "")
使指定IP地址机械臂进入力控模式，相较于enterForceMode增加不同坐标系的支持。
参数：
dblForceDirection：力指令的方向的数组首地址，数组长度为 3 。
dblForceValue：力指令的大小。单位：N。
dblMaxApproachVelocity： 最 大 接 近 速 度 。 单 位 ：m/s。 推 荐 取 值 范 围
（0.01m/s~0.5m/s）。
dblMaxAllowTcpOffset：允许的最大偏移。单位：m。推荐取值范围（0.01m~1m）。
dblActiveTcp ：支持的坐标系对应的矩阵，大小为 16 的数组
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double dblForceDir[3]={0,0,-1};
double dblForceValue = 2.0;
double dblMaxVel = 0.1;
double dblMaxOffset = 0.2;
double dblActiveTcp[16] = {1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1};
const char* strIpAddress = "*************";
if (enterForceMode_ex(dblForceDir, dblForceValue, dblMaxVel, dblMaxOffset,


```
dblActiveTcp,strIpAddress) < 0)
{
printf("Diana API enterForceMode_ex failed!\n");
}
```
## 92 readDI

```
int readDI(group_name, name, value, strIpAddress = "")
读取指定IP地址机械臂一个数字输入的值。
参数：
group_name：数字输入的分组，例如，’board’,’plc’, ‘endpoint’；
name：数字输入的信号名，例如，’di0’；
value：读取返回的值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int value;
const char* strIpAddress = "*************";
int ret = readDI("board", "di0", value, strIpAddress);
if(ret < 0)
{
    printf("readDI failed!\n");
}
else
{
printf("di0:%d\n", value);
}

## 93 readDO

```
int readDO(group_name, name, value, strIpAddress = "")
读取指定IP地址机械臂一个数字输出的值。
参数：
group_name：数字输出的分组，例如，’board’,’plc’, ‘endpoint’；
name：数字输出的信号名，例如，’do0’；
```

```
value：读取返回的值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int value;
const char* strIpAddress = "*************";
int ret = readDO("board", "do0", value, strIpAddress);
if(ret < 0)
{
    printf("readDO failed!\n");
}
else
{
    printf("do0: %d\n", value);
}

## 94 readAI

```
int readAI (group_name, name, mode, value, strIpAddress = "")
读取指定IP地址机械臂一个模拟输入的值和模式。
参数：
group_name：模拟输入的分组，例如，’board’,’plc’, ‘endpoint’；
name：模拟输入的信号名，例如，’ai0’；
mode：当前模拟输入模式；
value：读取返回的值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int mode;
double value;
const char* strIpAddress = "*************";


```
int ret = readAI("board", "ai0",mode,value, strIpAddress);
if(ret < 0)
{
printf("ReadAI failed!\n");
}
else
{
printf("ai0 mode= %d, value=%f\n", mode, value);
}
```
## 95 readAO

```
int readAO (group_name, name, mode, value, strIpAddress = "")
读取指定IP地址机械臂一个模拟输出的值和模式。
参数：
group_name：模拟输出的分组，例如，’board’,’plc’, ‘endpoint’；
name：模拟输出的信号名，例如，’ao0’；
mode：当前模拟输出模式；
value：读取返回的值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int mode;
double value;
const char* strIpAddress = "*************";
int ret = readAO("board", "ao0",mode,value, strIpAddress);
if(ret < 0)
{
printf("ReadAO failed!\n");
}
else
{
printf("ao0 mode= %d, value=%f\n", mode, value);
}


## 96 setAIMode

```
int setAIMode (group_name, name, mode, strIpAddress = "")
设置指定IP地址机械臂模拟输入的模式。
参数：
group_name：模拟输入的分组，例如，’board’,’plc’, ‘endpoint’；
name：模拟输入的信号名，例如，’ai0’；
mode: 模拟输入模式， 1 代表电流， 2 代表电压。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int mode = 1;
const char* strIpAddress = "*************";
int ret = setAIMode("board", "ai0",mode, strIpAddress);
if(ret < 0)
{
printf("SetAIMode failed!\n");
}

## 97 writeDO

```
int writeDO (group_name, name,value, strIpAddress = "")
设置指定IP地址机械臂一个数字输出的值。
可用于改变模拟信号的模式，将信号名替换为"aimode"或"aomode"。
参数：
group_name：数字输出的分组，例如，’board’,’plc’, ‘endpoint’；
name：数字输出的信号名，例如，’do0’
value：设置的值
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效
返回值：
0 ：成功。
```
- 1 ：失败。


### 调用示例：

```
int value = 0;
const char* strIpAddress = "*************";
int ret = writeDO ("board", "do0",value, strIpAddress);
if(ret < 0)
{
printf("WriteDO failed!\n");
}
```
## 98 writeAO

```
int writeAO (group_name, name, mode, value, strIpAddress = "")
设置指定IP地址机械臂一个模拟输出的值和模式。
参数：
group_name：模拟输出的分组，例如，’board’,’plc’, ‘endpoint’；
name：模拟输出的信号名，例如，’ao0’；
mode：当前模拟输出模式；
value：设置输出的值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int mode = 1;
double value = 8.8;
const char* strIpAddress = "*************";
int ret = writeAO ("board", "ao0", mode, value, strIpAddress);
if(ret < 0)
{
printf("WriteAO failed!\n");
}

## 99 readBusCurrent

```
int readBusCurrent(current, strIpAddress = "")
读取指定IP地址机械臂总线电流。
参数：
```

```
current：总线电流。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double current;
const char* strIpAddress = "*************";
int ret = readBusCurrent(current, strIpAddress);
if(ret < 0)
{
printf("ReadBusCurrent failed!\n");
}
else
{
printf("ReadBusCurrent :%f\n", current);
}

## 100 readBusVoltage

```
int readBusVoltage (voltage, strIpAddress = "")
读取指定IP地址机械臂总线电压。
参数：
voltage：总线电压。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double voltage;
const char* strIpAddress = "*************";
int ret = readBusVoltage(voltage, strIpAddress);
if(ret < 0)
{
printf("ReadBusVoltage failed!\n");


### }

```
else
{
printf("readBusVoltage :%f\n", voltage);
}
```
## 101 getDH

```
int getDH(aDH, alphaDH,dDH,thetaDH, strIpAddress = "")
获取指定IP地址机械臂的DH参数。
参数：
aDH：连杆长度的数组，长度为JOINT_NUM
alphaDH:连杆转角的数组，长度为JOINT_NUM
dDH:连杆偏距的数组，长度为JOINT_NUM
thetaDH:连杆的关节角的数组，长度为JOINT_NUM
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double a[JOINT_NUM],alpha[JOINT_NUM],d[JOINT_NUM],theta[JOINT_NUM];
const char* strIpAddress = "*************";
int ret = getDH(a,alpha,d,theta, strIpAddress);
if(ret < 0)
{
printf("getDH failed!\n");
}

## 102 getOriginalJointTorque

```
int getOriginalJointTorque(torques, strIpAddress = "")
获取指定IP地址机械臂传感器反馈的扭矩值，未减去零偏。
参数：
torques：反馈扭矩的数组，长度为JOINT_NUM
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
double torques[JOINT_NUM];
const char* strIpAddress = "*************";
int ret = getOriginalJointTorque(torques, strIpAddress);
if(ret < 0)
{
printf("getOriginalJointTorque failed!\n");
}
```
## 103 getJacobiMatrix

```
int getJacobiMatrix(matrix_jacobi, strIpAddress = "")
获取指定IP地址机械臂的雅各比矩阵。
参数：
matrix_jacobi：雅各比矩阵的数组，长度为6*JOINT_NUM
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double matrix_jacobi [6*JOINT_NUM]={0.0};
const char* strIpAddress = "*************";
int ret = getJacobiMatrix (matrix_jacobi, strIpAddress);
if(ret < 0)
{
printf("getJacobiMatrix failed!\n");
}

## 104 resetDH

```
int resetDH(strIpAddress = "")
重置指定IP地址机械臂用户自定义DH参数。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
```

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
int ret = resetDH(strIpAddress);
if(ret < 0)
{
printf("resetDH failed!\n");
}
```
## 105 runProgram

```
int runProgram(programName, strIpAddress = "")
运行指定IP地址机械臂某个程序，不支持文本工程。
参数：
programName：程序的名字，最长 127 个英文字符。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret=runProgram("AgileRobots", strIpAddress);
if(ret < 0)
{
printf(" runProgram failed!\n");
}

## 106 stopProgram

```
int stopProhram(programName, strIpAddress = "")
停止指定IP地址机械臂某个程序，不支持文本工程。
参数：
programName：程序的名字，最长 127 个英文字符。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
```

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
int ret=stopProgram("AgileRobots", strIpAddress);
if(ret < 0)
{
printf(" stopProgram failed!\n");
}
```
## 107 getVariableValue

```
int getVariableValue(variableName,value, strIpAddress = "")
获取指定IP地址机械臂某个全局变量的值。
参数：
variableName：全局变量的名字
value: 获取的全局变量的值
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double value;
const char* strIpAddress = "*************";
int ret = getVariableValue ("GLOBAL", value, strIpAddress);
if(ret < 0)
{
printf("getVariableValue failed!\n");
}

## 108 setVariableValue

```
int setVariableValue(variableName,value, strIpAddress = "")
设置指定IP地址机械臂某个全局变量的值。
参数：
variableName：全局变量的名字
```

```
value: 设置的全局变量的值
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double value=2.0;
const char* strIpAddress = "*************";
int ret = setVariableValue ("GLOBAL",value, strIpAddress);
if(ret < 0)
{
printf("setVariableValue failed!\n");
}

## 109 isTaskRunning

```
int isTaskRunning(programName, strIpAddress = "")
判断指定IP地址机械臂某个程序是否在运行，不支持文本工程。
参数：
programName：程序名称，最长 127 个英文字符。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：运行中。
```
- 1 ：没有运行中。
调用示例：
const char* strIpAddress = "*************";
int ret = isTaskRunning ("AgileRobots", strIpAddress);
if(ret < 0)
{
printf("AgileRobots is not running!\n");
}

## 110 pauseProgram

```
int pauseProgram(strIpAddress = "")
暂停指定IP地址机械臂所有程序，不支持文本工程。
```

### 注意：该指令会暂停所有程序，且内部会保留暂停标记，调用过此指令后必须调用

```
resumeProgram或者 stopAllProgram清除暂停标记，否则下次运行程序会直接进入暂停
态。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = pauseProgram(strIpAddress);
if(ret == 0)
{
printf("All program is paused!\n");
}

## 111 resumeProgram

```
int resumeProgram(strIpAddress = "")
恢复运行指定IP地址机械臂已经暂停的程序，不支持文本工程。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = resumeProgram(strIpAddress);
if(ret == 0)
{
printf("All program is resume!\n");
}

## 112 stopAllProgram

```
int stopAllProgram(strIpAddress = "")
```

### 停止指定IP地址机械臂所有程序，不支持文本工程。

### 参数：

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = stopAllProgram(strIpAddress);
if(ret == 0)
{
printf("All program is stop!\n");
}

## 113 isAnyTaskRunning

```
int isAnyTaskRunning(strIpAddress = "")
判断指定IP地址机械臂是否有程序在运行，不支持文本工程。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：有任务运行。
```
- 1 ：无任务运行。
调用示例：
const char* strIpAddress = "*************";
int ret = isAnyTaskRunning(strIpAddress);
if(ret < 0)
{
printf("Any program is not running!\n");
}

## 114 cleanErrorInfo

```
int cleanErrorInfo(strIpAddress = "")
清除指定IP地址机械臂的错误信息。
参数：
```

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = cleanErrorInfo(strIpAddress);
if(ret < 0)
{
printf("cleanErrorInfo failed!\n");
}

## 115 setCollisionLevel

```
int setCollisionLevel(level, strIpAddress = "")
设置指定IP地址机械臂的碰撞检测类型
参数：
level：碰撞等级，int类型，值为 0 或者下面几种值的组合（ 0 表示只考虑后台最大保护
阈值）：
```
```
0x01：关节空间检测
0x02：笛卡尔空间检测
0x04： TCP合力检测
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = setCollisionLevel(0x01|0x02, strIpAddress);
if(ret < 0)
{
printf("setCollision failed!\n");
}


## 116 mappingInt8Variant

```
int mappingInt8Variant(variableName,index, strIpAddress = "")
在指定IP地址机械臂上映射int8型变量。
参数：
variableName：变量名称
index: 映射变量序号
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setMappingAddress示例。

## 117 mappingDoubleVariant

```
int mappingDoubleVariant(variableName,index, strIpAddress = "")
在指定IP地址机械臂上映射double型变量。
参数：
variableName：变量名称
index: 映射变量序号
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setMappingAddress示例。

## 118 mappingInt8IO

```
int mappingInt8IO(groupName,name,index, strIpAddress = "")
在指定IP地址机械臂上映射数字信号IO。
参数：
groupName：IO组名
name：IO名字
```

```
index: 映射序号
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setMappingAddress示例。

## 119 mappingDoubleIO

```
int mappingDoubleIO(groupName,name,index, strIpAddress = "")
在指定IP地址机械臂上映射模拟信号IO。
参数：
groupName：IO组名
name: IO名称
index: 映射序号
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setMappingAddress示例。

## 120 setMappingAddress

```
int setMappingAddress(dblAddress,doubleItemCount,int8Address,int8ItemCount, strIpAddress
= "")
在指定IP地址机械臂上设置地址映射。
参数：
dblAddress：double型数据的映射地址，double数组
doubleItemCount：double型数据的映射数量，int型，最大支持 40 个
int8Address：int8型数据的映射地址，int8数组
int8ItemCount: int8型数据的映射数量，int型，最大支持 160 个
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
```

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

mapping的使用说明：
mapping的作用是将机械臂上double型变量，int8型变量（程序变量表中），数字信号
IO，模拟信号IO通过索引与自定义的内存地址（数组）建立映射连接，通过自定义的
内存地址访问上述变量

1 、步骤 1 ：首先定义一些INT8型和DOUBLE型的索引值，通过枚举方式：
enum INT8_VALUE_INDEX
{
DI0 = 0,
DI1,
DI2,
DI3,
DI4,
DI5,
};

enum DOUBLE_VALUE_INDEX
{
AI0 = 0,
AI1,
TEST_VALUE_0,
TEST_VALUE_1,
};

2 、步骤 2 ：将机械臂上double型变量，int8型变量（程序变量表中），数字信号IO，
模拟信号IO与索引值建立映射：
//mapping double value
mappingDoubleIO("board", "ai0", AI0);
mappingDoubleIO("board", "ai1", AI1);
mappingDoubleVariant("test_value_0", TEST_VALUE_0);
mappingDoubleVariant("test_value_1", TEST_VALUE_1);


```
//mapping int8 value
mappingInt8IO("board", "di0", DI0);
mappingInt8IO("board", "di1", DI1);
mappingInt8IO("board", "di2", DI2);
mappingInt8IO("board", "di3", DI3);
mappingInt8IO("board", "di4", DI4);
mappingInt8IO("board", "di5", DI5);
```
```
3 、步骤 3 ：设置映射地址（自定义的内存地址（数组））
```
```
int8_t int8_array[6] = {0};
double double_array[4] = {0.0};
setMappingAddress(double_array, 4, int8_array, 6);
```
```
4 、步骤 4 ：使用自定义的double_array和int8_array地址名通过索引值访问机械臂上
double型变量，int8型变量（程序变量表中），数字信号IO，模拟信号IO的值
```
```
lockMappingAddress();//给数据加锁
printf("数字信号DI[0-7] = {%d, %d, %d, %d, %d, %d, %d, %d}\n", int8_array[DI0],
int8_array[DI1], int8_array[DI2], int8_array[DI3], int8_array[DI4], int8_array[DI5]);
printf("模拟信号AI[2] = {%f, %f}\n", double_array[AI0], double_array[AI1]);
printf("double型变量test_value_0=%f, test_value_1=%f\n",
double_array[TEST_VALUE_0], double_array[TEST_VALUE_1]);
unlockMappingAddress();//给数据解锁
```
## 121 lockMappingAddress

```
int lockMappingAddress(strIpAddress = "")
在指定IP地址机械臂上访问数据时加锁。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setMappingAddress示例。


## 122 unlockMappingAddress

```
int unlockMappingAddress(strIpAddress = "")
在指定IP地址机械臂上解锁数据锁。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setMappingAddress示例。

## 123 getJointCount

```
int getJointCount(strIpAddress = "")
在指定IP地址机械臂上，获取其机械臂的关节数目
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
关节数量。
调用示例：
const char* strIpAddress = "*************";
int count = getJointCount(strIpAddress);
printf("Joint Count = %d\n",count);
```
## 124 getWayPoint

```
int getWayPoint(strWayPointName, dblTcppos, dblJoints, strToolName, strWorkpieceName,
strIpAddress = "")
获取指定IP地址机械臂上路点变量信息。
参数：
strWayPointName：路点变量名称 。
dblTcppos：位姿信息，大小为 6 的数组，其中，后三个角度为轴角
dblJoints：关节角信息。
strToolName: 路点关联工具坐标系名称；
strWorkpieceName：路点关联工件坐标系名称；
```

```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setWayPoint示例。

## 125 setWayPoint

```
int setWayPoint(strWayPointName ,dblTcppos, dblJoints, strToolName, strWorkpieceName,
strIpAddress = "")
修改指定IP地址机械臂上路点变量的值
参数：
strWayPointName：路点变量名称 。
dblTcppos：位姿信息，大小为 6 的数组，其中，后三个角度为轴角
dblJoints：关节角信息。
strToolName: 路点关联工具坐标系名称；
strWorkpieceName：路点关联工件坐标系名称；
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
<0：失败。失败原因详见错误码。
调用示例：
```
```
double Target[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(90.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0) };
moveJ(Target, 0.8, 0.4);
wait_move();
```
```
const char *strWayPointName = "api_01";
const char *strToolName = "tool1";
const char *strWorkpieceName = "work1";
char strToolTmp[64];
char strWorkpieceTmp[64];
```

double add_dblTcppos[6] = { 0.0 };
getTcpPos(add_dblTcppos);
double add_dblJoints[7] = { 0.0 };
getJointPos(add_dblJoints);

int ret = addWayPoint(strWayPointName, add_dblTcppos, add_dblJoints, strToolName,
strWorkpieceName);
if (ret < 0)
{
printf( "addWayPoint failed!\n");
}
else
{
printf( "addWayPoint succeed!\n");
}

double get_dblTcppos[6] = { 0.0 };
double get_dblJoints[7] = { 0.0 };
ret = getWayPoint(strWayPointName, get_dblTcppos, get_dblJoints, strToolTmp,
strWorkpieceTmp);
if (ret < 0)
{
printf( "getWayPoint failed!\n");
}
else
{
printf( "getWayPoint succeed!\n");
printf( "tool name : %s\n", strToolTmp);
printf( "workpiece name : %s\n", strWorkpieceTmp);
printf( "addWayPoint-Tcppos: %f, %f, %f, %f, %f, %f\n",
add_dblTcppos[0], add_dblTcppos[1], add_dblTcppos[2], add_dblTcppos[3],
add_dblTcppos[4], add_dblTcppos[5]);
printf( "getWayPoint-Tcppos: %f, %f, %f, %f, %f, %f\n\n\n",
get_dblTcppos[0], get_dblTcppos[1], get_dblTcppos[2], get_dblTcppos[3],
get_dblTcppos[4], get_dblTcppos[5]);

printf( "addWayPoint-Joints: %f, %f, %f, %f, %f, %f, %f\n",
add_dblJoints[0], add_dblJoints[1], add_dblJoints[2], add_dblJoints[3],


add_dblJoints[4], add_dblJoints[5], add_dblJoints[6]);
printf( "getWayPoint-Joints: %f, %f, %f, %f, %f, %f, %f\n\n\n",
get_dblJoints[0], get_dblJoints[1], get_dblJoints[2], get_dblJoints[3],
get_dblJoints[4], get_dblJoints[5], get_dblJoints[6]);
}

double Target1[7] = { DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(-60.0),
DEGREE_TO_RAD(0.0), DEGREE_TO_RAD(150.0), DEGREE_TO_RAD(0.0),
DEGREE_TO_RAD(-90.0), DEGREE_TO_RAD(0.0) };
moveJ(Target1, 0.8, 0.4);
wait_move();

double set_dblTcppos[6] = { 0.0 };
getTcpPos(set_dblTcppos);
double set_dblJoints[7] = { 0.0 };
getJointPos(set_dblJoints);
ret = setWayPoint(strWayPointName, set_dblTcppos, set_dblJoints, strToolName,
strWorkpieceName);
if (ret < 0)
{
printf( "setWayPoint failed!\n");
}
else
{
printf( "setWayPoint succeed!\n");
}

ret = getWayPoint(strWayPointName, get_dblTcppos, get_dblJoints, strToolName,
strWorkpieceName);
if (ret < 0)
{
printf( "getWayPoint failed!\n");
}
else
{
printf( "getWayPoint succeed!\n");
printf( "setWayPoint-Tcppos: %f, %f, %f, %f, %f, %f\n",
set_dblTcppos[0], set_dblTcppos[1], set_dblTcppos[2], set_dblTcppos[3],


```
set_dblTcppos[4], set_dblTcppos[5]);
printf( "getWayPoint-Tcppos: %f, %f, %f, %f, %f, %f\n\n\n",
get_dblTcppos[0], get_dblTcppos[1], get_dblTcppos[2], get_dblTcppos[3],
get_dblTcppos[4], get_dblTcppos[5]);
```
```
printf( "setWayPoint-Joints: %f, %f, %f, %f, %f, %f, %f\n",
set_dblJoints[0], set_dblJoints[1], set_dblJoints[2], set_dblJoints[3],
set_dblJoints[4], set_dblJoints[5], set_dblJoints[6]);
printf( "getWayPoint-Joints: %f, %f, %f, %f, %f, %f, %f\n\n\n",
get_dblJoints[0], get_dblJoints[1], get_dblJoints[2], get_dblJoints[3],
get_dblJoints[4], get_dblJoints[5], get_dblJoints[6]);
}
```
```
ret = deleteWayPoint(strWayPointName);
if (ret < 0)
{
printf( "deleteWayPoint failed!\n");
}
else
{
ret = getWayPoint(strWayPointName, get_dblTcppos, get_dblJoints ,strToolTmp,
strWorkpieceTmp);
if (ret < 0)
{
printf( "deleteWayPoint succeed!\n");
}
else
{
printf( "deleteWayPoint failed!\n");
}
}
```
## 126 addWayPoint

```
int addWayPoint(strWayPointName, dblTcppos, dblJoints, strToolName, strWorkpieceName,
strIpAddress = "")
在指定IP地址机械臂上新增路点变量。
参数：
```

```
strWayPointName：路点变量名称 。
dblTcppos：位姿信息，大小为 6 的数组，其中，后三个角度为轴角
dblJoints：关节角信息。
strToolName: 路点关联工具坐标系名称；
strWorkpieceName：路点关联工件坐标系名称；
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
<0：失败。失败原因详见错误码。
调用示例：
参考setWayPoint示例。
```
## 127 deleteWayPoint

```
int deleteWayPoint(strWayPointName, strIpAddress = "")
在指定IP地址机械臂上删除路点变量。
参数：
strWayPointName：路点变量名称。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
参考setWayPoint示例。

## 128 getDefaultActiveTcp

```
int getDefaultActiveTcp(default_tcp , strIpAddress = "")
获取指定IP地址机械臂的默认工具坐标系。
参数：
default_tcp：输出参数，Tcp相对于末端法兰盘的4*4齐次变换矩阵的首地址，数组长度
为 16 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
double default_tcp[16] = {0.0};
int ret = getDefaultActiveTcp(default_tcp, strIpAddress);
if(ret < 0)
{
printf("getDefaultActiveTcp failed! Return value = %d\n", ret);
}
```
## 129 getDefaultActiveTcpPose......................................................................................

```
int getDefaultActiveTcpPose (arrPose,strIpAddress = "")
获取指定IP地址机械臂默认的工具坐标系的位姿。
参数：
arrPose：输出参数。TCP相对于末端法兰盘的位姿向量的首地址，数组长度为 6
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double pose[6] = {0.0};
const char* strIpAddress = "*************";
if (getDefaultActiveTcpPose (pose,strIpAddress) < 0)
{
printf("Diana API getDefaultActiveTcpPose failed!\n");
}
else
{
printf("getDefaultActiveTcpPose: %f, %f, %f, %f, %f, %f\n", pose[0], pose[1], pose[2],
pose[3], pose[4], pose[5]);
}

## 130 getActiveTcpPayload

```
int getActiveTcpPayload(payload,strIpAddress = "")
获取指定IP地址机械臂的负载信息
```

### 参数：

```
payload：负载信息，第 1 位为质量，2~4位为质心，5~10位为张量，大小为 10 的数组
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
double dblPayload[10] = {0.0};
int ret = getActiveTcpPayload (dblPayload,strIpAddress);
if(ret < 0)
{
printf("getActiveTcpPayload failed! Return value = %d\n", ret);
}
else
{
printf("getActiveTcpPayload: %f, %f, %f, %f, %f, %f, %f, %f, %f, %f\n", dblPayload[0],
dblPayload[1], dblPayload[2], dblPayload[3], dblPayload[4], dblPayload[5],
dblPayload[6], dblPayload[7], dblPayload[8], dblPayload[9]);
}

## 131 zeroSpaceManualMove

```
int zeroSpaceManualMove (direction,dblJointVel,dblJointAcc,strIpAddress = "")
控制指定IP地址机械臂进入或退出零空间手动移动模式。
参数：
direction：输入参数，控制零空间手动运动的方向， 1 表示向前，- 1 表示向后运动
dblJointVel：输入参数，各关节运动的速度，单位rad/s
dblJointAcc：输入参数，各关节运动的加速度，单位rad/s^2
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";


### //以 7 轴机器人为例

```
double dblJointVel[7]={0.1,0.1,0.1,0.1,0.1,0.1,0.1};
double dblJointAcc[7]={0.1,0.1,0.1,0.1,0.1,0.1,0.1};
int ret = zeroSpaceManualMove(1,dblJointVel,dblJointAcc,strIpAddress);
if (ret < 0){
printf("Diana API zeroSpaceManualMove failed!\n");
}
else{
M_SLEEP(5000);
}
```
## 132 moveTcp_ex

```
int moveTcp_ex(c,d, v, a, strIpAddress = "")
手动移动指定IP地址的机械臂TCP。该函数会立即返回，停止运动需要调用stop函数。
参数：
c：坐标系类型，枚举及其含义如下：
⚫ E_BASE_COORDINATE：基坐标系
⚫ E_TOOL_COORDINATE：工具坐标系
⚫ E_WORK_PIECE_COORDINATE：工件坐标系
⚫ E_VIEW_COORDINATE：视角坐标系
d：表示移动方向的枚举类型，枚举及其含义如下
⚫ T_MOVE_X_UP表示沿x轴正向
⚫ T_MOVE_X_DOWN表示沿x轴负向
⚫ T_MOVE_Y_UP表示沿y轴正向
⚫ T_MOVE_Y_DOWN表示沿y轴负向
⚫ T_MOVE_Z_UP表示沿z轴正向
⚫ T_MOVE_Z_DOWN表示沿z轴负向
v：速度，单位：m/s。
a：加速度，单位：m/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
coordinate_e e = E_BASE_COORDINATE;
tcp_direction_e dtype = T_MOVE_X_UP;
double vel = 0.1;
double acc = 0.2;


```
const char* strIpAddress = "*************";
int ret = moveTcp_ex(e, dtype, vel, acc, strIpAddress);
if(ret < 0)
{
printf("moveTcp_ex failed! Return value = %d\n", ret);
}
```
## 133 rotationTCP_ex

```
int rotationTCP_ex(c, d, v, a, strIpAddress = "")
使指定的IP地址的机械臂绕TCP变换位姿。该函数会立即返回，停止运动需要调用stop
函数。
参数：
c：坐标系类型，枚举及其含义如下：
⚫ E_BASE_COORDINATE：基坐标系
⚫ E_TOOL_COORDINATE：工具坐标系
⚫ E_WORK_PIECE_COORDINATE：工件坐标系
⚫ E_VIEW_COORDINATE：视角坐标系
d：表示移动方向的枚举类型，枚举及其含义如下：
⚫ T_MOVE_X_UP表示绕x轴正向旋转
⚫ T_MOVE_X_DOWN表示绕x轴负向旋转
⚫ T_MOVE_Y_UP表示绕y轴正向旋转
⚫ T_MOVE_Y_DOWN表示绕y轴负向旋转
⚫ T_MOVE_Z_UP表示绕z轴正向旋转
⚫ T_MOVE_Z_DOWN表示绕z轴负向旋转
v：速度，单位：rad/s。
a：加速度，单位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
coordinate_e e = E_BASE_COORDINATE;
tcp_direction_e dtype = T_MOVE_X_UP;
double vel = 0.1;
double acc = 0.2;
const char* strIpAddress = "*************";
int ret = rotationTCP_ex(e,dtype, vel, acc, strIpAddress);
if(ret < 0)


### {

```
printf("rotationTCP failed! Return value = %d\n", ret);
}
else
{
M_SLEEP(3000);
}
```
## 134 setExternalAppendTorCutoffFreq

```
int setExternalAppendTorCutoffFreq(dblFreq, strIpAddress = "")
针对指定IP地址机械臂，设置各关节附加力矩的滤波截止频率
参数：
dblFreq：输入参数，各关节附加力矩的滤波截止频率，需要提供一个正值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int ret = setExternalAppendTorCutoffFreq(1.0,"*************");
if(ret < 0){
printf("setExternalAppendTorCutoffFreq failed! Return value = %d\n", ret);
}

## 135 poseTransform

```
int poseTransform(srcPose, srcMatrixPose,dstMatrixPose,dstPose)
把指定坐标系下的位姿转换到其他坐标系下
参数：
srcPose：输入参数，源坐标系下的位姿，大小为 6 的数组
srcMatrixPose：输入参数，源坐标系对应的位姿向量 ，大小为 6 的数组
dstMatrixPose：输入参数，目标坐标系对应的位姿向量 ，大小为 6 的数组
dstPose：输出参数，转换到目标坐标系下的位姿，大小为 6 的数组
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：


```
double srcPose[6] = {0.087,0.0,1.0827,0.0,0.0,0.0};
double srcMatrixPose[6] = {0};
double dstMatrixPose[6] = {0.1, 0.2, 0.3, 0.0, 0.0, 0.0};
double dstPose[6] = {0};
poseTransform(srcPose,srcMatrixPose,dstMatrixPose,dstPose);
for(int i = 0;i<6;++i)
{
printf("%lf ",dstPose[i]);
}
```
## 136 setEndKeyEnableState

```
int setEndKeyEnableState(bEnable, strIpAddress = "")
针对指定IP地址机械臂，使能其末端零力驱动按钮
参数：
bEnable：输入参数，使能末端零力驱动按钮
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
int ret = setEndKeyEnableState(true,"*************");
if(ret < 0){
printf("setEndKeyEnableState failed! Return value = %d\n", ret);
}

## 137 updateForce

```
int updateForce(dblForceValue,strIpAddress = "")
针对指定IP地址机械臂上，在力控模式下，实时改变力指令的大小
参数：
dblForceValue：输入参数，力的大小，需要是一个大于 0 的数
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。


### 调用示例：

```
int iFrameType = 1;
double dblFrameMatrix[16] = {1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1};
double dblForceDirection[3]={0,0,-1};
double dblForceValue = 1.0;
double dblMaxVel = 0.1;
double dblMaxOffset = 0.2;
const char* strIpAddress = "*************";
double joints[7] = {0,0,0,3.141592653/2,0,0,0};//以 7 轴机器人为例
moveJ(joints,0.2,0.2,strIpAddress);
wait_move(strIpAddress);
if (enterForceMode(iFrameType, dblFrameMatrix, dblForceDirection, dblForceValue,
dblMaxVel, dblMaxOffset,strIpAddress) < 0)
{
printf("Diana API enterForceMode failed!\n");
}else{
int count = 0;
while(count++ < 2000){
dblForceValue -=0.001;
updateForce(dblForceValue,strIpAddress);
M_SLEEP(1);
}
int intExitMode = 0;
if (leaveForceMode(intExitMode, strIpAddress) < 0)
{
printf("Diana API leaveForceMode failed!\n");
}
}
```
## 138 inverseClosedFull

```
int inverseClosedFull(pose,lock_joint_index, lock_joint_position, ref_joints,
active_tcp=nullptr,strIpAddress = "")
在指定IP地址机械臂上，基于工具坐标系，给定一个参考关节角，约束单轴求逆解，注
意，工业版 Diana只能锁定第七轴。现支持在不同工具坐标系下求逆解，由传入的
active_tcp决定。
参数：
pose：输入位姿数组首地址，数据为包含active_tcp坐标（x, y, z）和旋转矢量（轴角坐
```

### 标）组合。

lock_joint_index：输入参数，被约束的关节号
lock_joint_position：输入参数，被约束关节的角度，单位为弧度
ref_joints：参考的关节角，大小为JOINT_NUM的数组。
active_tcp：工具坐标系对应的位姿向量，大小为 6 的数组，为空时，将使用默认的工具
坐标系default_tcp。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
非负数：生成逆解对应的ID

- 1 ：失败。
调用示例：
const char *strIpAddress = "*************";
double pose[6] = {0.087, 0.0, 1.0827, 0.0, 0.0, 0.0};
int lock_joint_index = JOINT_NUM;
double lock_joint_position = 0;
double ref_joints[ JOINT_NUM] = {0.0};
double joints[JOINT_NUM]={0.0};
int id = inverseClosedFull(pose, lock_joint_index, lock_joint_position, ref_joints, nullptr,
strIpAddress);
if (id == -1){
printf("inverseClosedFull failed! Return value = %d\n", id);
}
else{
int size = getInverseClosedResultSize(id);
int ret = -1;
if (size > 0){
for (int i = 0; i < size; ++i){
if (getInverseClosedJoints(id, i, joints, strIpAddress) == 0){
printf("(%lf,%lf,%lf,%lf,%lf,%lf,%lf)", joints[0], joints[1], joints[2], joints[3],
joints[4], joints[5], joints[6]);
}
}
destoryInverseClosedItems(id);
}
}


## 139 getInverseClosedResultSize

```
int getInverseClosedResultSize(id,strIpAddress = "")
在指定IP地址的机械臂上，根据ID获取约束单轴求逆解结果的组数
参数：
id：输入参数，所求逆解对应的ID
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
非负数：ID对应逆解的组数
```
- 1 ：失败。
调用示例：
见inverseClosedFull示例

## 140 getInverseClosedJoints

```
int getInverseClosedJoints(id,index,joints,strIpAddress = "")
在指定IP地址机械臂上，根据ID按索引获取对应关节角
参数：
id：输入参数，所求逆解对应的ID
index：输入参数，对应的多组逆解中的编号
joints：输出参数，需要求的多组逆解中编号对应的逆解值
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功；
```
- 1 ：失败。
调用示例：
见inverseClosedFull示例

## 141 destoryInverseClosedItems

```
int destoryInverseClosedItems(id,strIpAddress = "")
在指定IP地址机械臂上，根据ID删除约束单轴求逆解的结果数据集
参数：
id：输入参数，逆解对应的ID
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
```

### 时生效。

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
见inverseClosedFull示例
```
## 142 nullSpaceFreeDriving

```
int nullSpaceFreeDriving(enable,strIpAddress = "")
zeroSpaceFreeDriving的宏，用法参见zeroSpaceFreeDriving
调用示例：
见zeroSpaceFreeDriving示例
```
## 143 nullSpaceManualMove

```
int nullSpaceFreeDriving(enable,strIpAddress = "")
zeroSpaceManualMove的宏，用法参见zeroSpaceManualMove
调用示例：
见zeroSpaceManualMove示例
```
## 144 getGravInfo

```
int getGravInfo(grav,strIpAddress = "")
针对指定IP地址的机械臂，获取其安装信息的重力矢量
参数：
grav：重力矢量，大小为 3 的数组
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* ipAddress = "*************";
double grav[3] = {0.0};
int ret = getGravInfo(grav,ipAddress);
if(ret < 0){
printf("getGravInfo failed! Return value = %d\n", ret);
}


```
else{
printf("getGravInfo :%f, %f, %f\n", grav[0], grav[1], grav[2]);
}
```
## 145 setGravInfo

```
int setGravInfo(grav,strIpAddress = "")
针对指定IP地址的机械臂，设置其重力矢量
参数：
grav：重力矢量，大小为 3 的数组
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* ipAddress = "*************";
double grav[3] = {0.0};
int ret = setGravInfo(grav,ipAddress);
if(ret < 0){
printf("setGravInfo failed! Return value = %d\n", ret);
}

## 146 getGravAxis

```
int getGravAxis(grav_axis,strIpAddress = "")
针对指定IP地址的机械臂，获取其安装信息的轴角
参数：
grav_axis：安装时的轴角，单位为rad
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* ipAddress = "*************";
double grav_axis[3] = {0.0};
int ret = getGravAxis (grav_axis,ipAddress);


```
if(ret < 0){
printf("getGravAxis failed! Return value = %d\n", ret);
}
else{
printf("getGravAxis :%f, %f, %f\n", grav_axis[0], grav_axis[1], grav_axis[2]);
}
```
## 147 setGravAxis

```
int setGravAxis(grav_axis,strIpAddress = "")
针对指定IP地址的机械臂，设置其安装信息的轴角
参数：
grav_axis：安装轴角，单位rad
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* ipAddress = "*************";
double grav_axis [3] = {0.0};
int ret = setGravAxis(grav_axis,ipAddress);
if(ret < 0){
printf("setGravAxis failed! Return value = %d\n", ret);
}

## 148 speedLOnTcp

```
int speedLOnTcp(speed, a, t, active_tcp=nullptr, strIpAddress = "")
速度模式优化版，使指定IP地址机械臂笛卡尔空间下直线运动，支持同步旋转，但笛卡
尔方向必须有速度或加速度才能旋转。时间t为非零时，机器人将在t时间后减速。如果
t为 0 ，机器人将在达到目标速度时减速。该函数调用后立即返回。停止运动需要调用
stop函数。
```
```
参数：
speed：指定系统当前工具中心点的空间速度，数组长度为 6 （位置和旋转），其中前 3
个单位为m/s，后 3 个单位为rad/s。平移速度和旋转速度的参考坐标系由active_tcp指
定。
```

```
a：加速度数组，数组长度为 2 ，前一个代表平移加速度，单位：m/s^2 ；后一个代表旋转
加速度，单位：rad/s^2
t：时间，单位：s。
active_tcp：基于法兰中心的位姿，大小为 6 的数组（位置和旋转矢量（轴角）），平移
和旋转方向参考此位姿，旋转中心是系统当前工具中心点；为空时平移和旋转方向参考
系统当前工具坐标系，旋转中心是系统当前工具中心点。strIpAddress：可选参数，需要
控制机械臂的IP地址字符串，不填仅当只连接一台机械臂时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double speeds[6] = {0.1,0.0,0.0,0.0,0.0,0.0};
double acc[2] = {0.30, 0.50};
const char* strIpAddress = "*************";
int ret = speedLOnTcp(speeds, acc, 0, nullptr, strIpAddress);
if(ret < 0)
{
printf("speedLOnTcp failed! Return value = %d\n", ret);
}
M_SLEEP(2000);

## 149 getTcpForceInToolCoordinate

```
int getTcpForceInToolCoordinate(forces,strIpAddress = "")
在指定IP地址机械臂上，获取工具坐标系的Tcp外力值
参数：
forces：输出参数，Tcp外力，大小为 6
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char *strIpAddress = "*************";
double forces[6] = {0.0};
int ret = getTcpForceInToolCoordinate(forces,strIpAddress);
for(int j=0;j<6;++j){


```
printf("%lf,",forces[j]);
}
```
## 150 calculateJacobi

```
int calculateJacobi(dblJacobiMatrix,dblJointPosition,intJointCount,strIpAddress = "")
在指定IP地址机械臂上，求解末端法兰中心点坐标系相对于基坐标系的雅各比矩阵
参数：
dblJacobiMatrix：输出参数，雅各比矩阵，大小为6* JOINT_NUM的矩阵
dblJointPosition：输入参数，用于计算雅各比矩阵的关节角（与当前机械臂反馈关节角
无关），大小为 JOINT_NUM的数组
intJointCount：输入参数，关节数量
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char *strIpAddress = "*************";
const int intJointCount = JOINT_NUM;
const int intTcpCount = 6;
double dblJacobiMatrix[intTcpCount*intJointCount] = {0};
double dblJointPosition[intJointCount] = {0};
int ret = calculateJacobi(dblJacobiMatrix,dblJointPosition,intJointCount,strIpAddress);
if(ret == - 1){
printf("cannot get jacobi matrix");
}else{
for(int i = 0;i< intJointCount;++i){
for(int j=0;j< intTcpCount;++j){
printf("%lf,",dblJacobiMatrix[i* intTcpCount + j]);
}
printf("\n");
}
}

## 151 calculateJacobiTF

```
int
calculateJacobiTF(dblJacobiMatrix,dblJointPosition,intJointCount,active_tcp=nullptr,strIpAddr
```

```
ess = "")
在指定IP地址机械臂上，求解工具中心点坐标系相对于基坐标系的雅各比矩阵，现在支
持求解不同的工具，由传入的 active_tcp决定
参数：
dblJacobiMatrix：输出参数，雅各比矩阵，大小为6* JOINT_NUM的矩阵
dblJointPosition：输入参数，用于计算雅各比矩阵的关节角（与当前机械臂反馈关节角无
关），大小为 JOINT_NUM的数组
intJointCount：输入参数，关节数量
active_tcp：基于法兰中心的工具位姿，大小为 6 的数组，根据关节角dblJointPosition求
解得到此工具中心点相对于基坐标系的雅各比矩阵；为空时，视为无工具，求解得到法
兰中心相对基坐标系的雅各比矩阵。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char *strIpAddress = "*************";
const int intJointCount = JOINT_NUM;
const int intTcpCount = 6;
double dblJacobiMatrix[intTcpCount*intJointCount] = {0};
double dblJointPosition[intJointCount] = {0};
int ret = calculateJacobiTF(dblJacobiMatrix,dblJointPosition,intJointCount,
nullptr ,strIpAddress);
if(ret == - 1){
printf("cannot get jacobi matrix");
}else{
for(int i = 0;i<intTcpCount;++i){
for(int j=0;j<intJointCount;++j){
printf("%lf,",dblJacobiMatrix[i* intTcpCount + j]);
}
printf("\n");
}
}

## 152 getMechanicalJointsPositionRange


```
int getMechanicalJointsPositionRange (dblMinPos, dblMaxPos, strIpAddress = " ")
获取指定IP地址机械臂各关节角的机械限位。
参数：
dblMinPos：输出参数，用于存放各关节角的最小机械限位，大小为JOINT_NUM的数
组，单位：rad。
dblMaxPos：输出参数，用于存放各关节角的最大机械限位，大小为JOINT_NUM的数
组，单位：rad。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMinPos[7] = {0}, dblMaxPos[7]={0};
int ret = getMechanicalJointsPositionRange(dblMinPos, dblMaxPos, strIpAddress);
printf("getMechanicalJointsPositionRange ret = %d dblMinPos = {%f,%f,%f,%f,%f,%f,%f}
dblMaxPos = {%f,%f,%f,%f,%f,%f,%f}\n"
, ret
, dblMinPos[0]
, dblMinPos[1]
, dblMinPos[2]
, dblMinPos[3]
, dblMinPos[4]
, dblMinPos[5]
, dblMinPos[6]
, dblMaxPos[0]
, dblMaxPos[1]
, dblMaxPos[2]
, dblMaxPos[3]
, dblMaxPos[4]
, dblMaxPos[5]
, dblMaxPos[6]);

## 153 getMechanicalMaxJointsVel

```
int getMechanicalMaxJointsVel (dblVel, strIpAddress = " ")
获取指定IP地址机械臂各关节最大机械速度。
参数：
dblVel：输出参数，用于存放各关节的最大机械速度，大小为 JOINT_NUM的数组，单
位：rad/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
```

### 返回值：

### - 1 ：失败。

### 0 ：成功。

### 调用示例：

```
const char* strIpAddress= "*************";
double dblMaxVel[7]={0};
int ret = getMechanicalMaxJointsVel(dblMaxVel, strIpAddress);
printf("getMechanicalJointsMaxVel ret = %d dblMaxVel = {%f,%f,%f,%f,%f,%f,%f}\n"
, ret
, dblMaxVel[0]
, dblMaxVel[1]
, dblMaxVel[2]
, dblMaxVel[3]
, dblMaxVel[4]
, dblMaxVel[5]
, dblMaxVel[6]);
```
## 154 getMechanicalMaxJointsAcc

```
int getMechanicalMaxJointsAcc (dblAcc, strIpAddress = " ")
获取指定IP地址机械臂各关节最大机械加速度。
参数：
dblAcc：输出参数，用于存放各关节的最大机械加速度，大小为 JOINT_NUM的数组，
单位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMaxAcc[7]={0};
int ret = getMechanicalMaxJointsAcc(dblMaxAcc, strIpAddress);
printf("getMechanicalJointsMaxAcc ret = %d dblMaxAcc = {%f,%f,%f,%f,%f,%f,%f}\n"
, ret
, dblMaxAcc[0]
, dblMaxAcc[1]
, dblMaxAcc[2]
, dblMaxAcc[3]
, dblMaxAcc[4]
, dblMaxAcc[5]
, dblMaxAcc[6]);

## 155 getMechanicalMaxCartVelAcc

```
int getMechanicalMaxCartVelAcc (dblMaxCartTranslationVel, dblMaxCartRotationVel,
dblMaxCartTranslationAcc, dblMaxCartRotationAcc, strIpAddress = " ")
```

### 获取指定IP地址笛卡尔空间最大机械平移速度、最大机械旋转速度、最大机械平移加速

### 度和最大机械旋转加速度。

### 参数：

```
dblMaxCartTranslationVel：输出参数，用于存放笛卡尔空间最大机械平移速度，double
型变量，单位：m/s。
dblMaxCartRotationVel：输出参数，用于存放笛卡尔空间最大机械旋转速度，double型变
量，单位：rad/s。
dblMaxCartTranslationAcc：输出参数，用于存放笛卡尔空间最大机械平移加速度，
double型变量，单位：m/s^2 。
dblMaxCartRotationAcc：输出参数，用于存放笛卡尔空间最大机械旋转加速度，double
型变量，单位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblLinearVel = 0, dblRotationVel = 0, dblLinearAcc = 0, dblRotationAcc = 0;
int ret = getMechanicalMaxCartVelAcc(dblLinearVel, dblRotationVel, dblLinearAcc,
dblRotationAcc, strIpAddress);
printf("getMechanicalCartMaxVelAcc ret = %d
{dblLinearVel(%f),dblRotationVel(%f),dblLinearAcc(%f),dblRotationAcc(%f)}\n"
, ret
, dblLinearVel
, dblRotationVel
, dblLinearAcc
, dblRotationAcc);

## 156 getJointsPositionRange

```
int getJointsPositionRange (dblMinPos, dblMaxPos, strIpAddress = " ")
获取指定IP地址机械臂各关节的极限位。
参数：
dblMinPos：输出参数，用于存放关节角的最小极限位，大小为JOINT_NUM的数组，单
位：rad。
dblMaxPos：输出参数，用于存放关节角的最大极限位，大小为JOINT_NUM的数组，单
位：rad。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
```

### 返回值：

### - 1 ：失败。

### 0 ：成功。

### 调用示例：

```
const char* strIpAddress= "*************";
double dblMinPos[7] = {0}, dblMaxPos[7]={0};
int ret = getJointsPositionRange(dblMinPos, dblMaxPos, strIpAddress);
printf("getJointsPositionRange ret = %d dblMinPos = {%f,%f,%f,%f,%f,%f,%f} dblMaxPos =
{%f,%f,%f,%f,%f,%f,%f}\n"
, ret
, dblMinPos[0]
, dblMinPos[1]
, dblMinPos[2]
, dblMinPos[3]
, dblMinPos[4]
, dblMinPos[5]
, dblMinPos[6]
, dblMaxPos[0]
, dblMaxPos[1]
, dblMaxPos[2]
, dblMaxPos[3]
, dblMaxPos[4]
, dblMaxPos[5]
, dblMaxPos[6]);
```
## 157 getMaxJointsVel

```
int getMaxJointsVel (dblVel, strIpAddress = " ")
获取指定IP地址机械臂各关节最大软速度。
参数：
dblVel：输出参数，用于存放各关节的最大软速度，大小为JOINT_NUM的数组，单位：
rad/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMaxVel[7]={0};
int ret = getMaxJointsVel(dblMaxVel, strIpAddress);
printf("getMaxJointsVel ret = %d dblMaxVel = {%f,%f,%f,%f,%f,%f,%f}\n"
, ret
, dblMaxVel[0]
, dblMaxVel[1]
, dblMaxVel[2]
, dblMaxVel[3]


```
, dblMaxVel[4]
, dblMaxVel[5]
, dblMaxVel[6]);
```
## 158 getMaxJointsAcc

```
int getMaxJointsAcc (dblAcc, strIpAddress = " ")
获取指定IP地址机械臂各关节最大软加速度。
参数：
dblAcc：输出参数，用于存放各关节的最大软加速度，大小为JOINT_NUM的数组，单
位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMaxAcc[7]={0};
int ret = getMaxJointsAcc(dblMaxAcc, strIpAddress);
printf("getMaxJointsAcc ret = %d dblMaxAcc = {%f,%f,%f,%f,%f,%f,%f}\n"
, ret
, dblMaxAcc[0]
, dblMaxAcc[1]
, dblMaxAcc[2]
, dblMaxAcc[3]
, dblMaxAcc[4]
, dblMaxAcc[5]
, dblMaxAcc[6]);

## 159 getMaxCartTranslationVel

```
int getMaxCartTranslationVel (dblMaxCartTranslationVel, strIpAddress = " ")
获取指定IP地址机械臂笛卡尔空间最大软平移速度。
参数：
dblMaxCartTranslationVel：输出参数，用于存放笛卡尔空间最大软平移速度，double型
变量，单位：m/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：


```
const char* strIpAddress= "*************";
double dblLinearVel = 0;
int ret = getMaxCartTranslationVel(dblLinearVel, strIpAddress);
printf("getMaxCartTranslationVel ret = %d dblLinearVel=%f\n"
, ret
, dblLinearVel
);
```
## 160 getMaxCartRotationVel

```
int getMaxCartRotationVel (dblMaxCartRotationVel, strIpAddress = " ")
获取指定IP地址机械臂笛卡尔空间最大软旋转速度。
参数：
dblMaxCartRotationVel：输出参数，用于存放笛卡尔空间最大软旋转速度，double型变
量，单位：rad/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblRotationVel = 0;
int ret = getMaxCartRotationVel(dblRotationVel, strIpAddress);
printf("getMaxCartRotationVel ret = %d dblRotationVel=%f\n"
, ret
, dblRotationVel);

## 161 getMaxCartTranslationAcc

```
int getMaxCartTranslationAcc (dblMaxCartTranslationAcc, strIpAddress = " ")
获取指定IP地址机械臂笛卡尔空间最大软平移加速度。
参数：
dblMaxCartTranslationAcc：输出参数，用于存放笛卡尔空间最大软平移加速度，double
型变量，单位：m/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblLinearAcc = 0;


```
int ret = getMaxCartTranslationAcc(dblLinearAcc, strIpAddress);
printf("getMaxCartTranslationAcc ret = %d dblLinearAcc=%f\n"
, ret
, dblLinearAcc);
```
## 162 getMaxCartRotationAcc

```
int getMaxCartRotationAcc (dblMaxCartRotationAcc, strIpAddress = " ")
在指定IP地址机械臂笛卡尔空间最大软旋转加速度。
参数：
dblMaxCartRotationAcc：输出参数，用于存放笛卡尔空间最大软旋转加速度，double型
变量，单位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblRotationAcc = 0;
int ret = getMaxCartRotationAcc(dblRotationAcc, strIpAddress);
printf("getMaxCartRotationAcc ret = %d dblRotationAcc=%f\n"
, ret
, dblRotationAcc);

## 163 setJointsPositionRange

```
int setJointsPositionRange (dblMinPos, dblMaxPos, strIpAddress = " ")
设置指定IP地址机械臂各关节极限位。
参数：
dblMinPos：输入参数，关节角的最小极限限位，大小为 JOINT_NUM的数组，单位：
rad。
dblMaxPos：输入参数，关节角的最大极限限位，大小为JOINT_NUM的数组，单位：
rad。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
<0：失败。失败原因详见错误码。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMinPos[7] = {0}, dblMaxPos[7]={0};
```

```
int ret = getJointsPositionRange(dblMinPos, dblMaxPos, strIpAddress);
for (int i = 0; i < 7; ++i) {
dblMinPos[i]++;
dblMaxPos[i]--;
}
ret = setJointsPositionRange(dblMinPos,dblMaxPos,strIpAddress);
printf("setJointsPositionRange return %d\n",ret);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);
```
## 164 setMaxJointsVel

```
int setMaxJointsVel (dblVel, strIpAddress = " ")
设置指定IP地址机械臂各关节的最大软速度。
参数：
dblVel：输入参数，各关节的最大软速度，大小为JOINT_NUM的数组，单位：rad/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
<0：失败。失败原因详见错误码。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMaxVel[7]={0};
int ret = getMaxJointsVel(dblMaxVel, strIpAddress);
for (int i = 0; i < 7; ++i) {
dblMaxVel[i]--;
}
ret = setMaxJointsVel(dblMaxVel, strIpAddress);
printf("setMaxJointsVel return %d\n",ret);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);
```
## 165 setMaxJointsAcc

```
int setMaxJointsAcc (dblAcc, strIpAddress = " ")
设置指定IP地址机械臂各关节的最大软加速度。
参数：
dblAcc：输入参数，用于存放各关节的最大软加速度，大小为JOINT_NUM的数组，单
位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。


### 0 ：成功。

### 调用示例：

```
const char* strIpAddress= "*************";
double dblMaxAcc[7]={0};
int ret = getMaxJointsAcc(dblMaxAcc, strIpAddress);
for (int i = 0; i < 7; ++i) {
dblMaxAcc[i]--;
}
ret = setMaxJointsAcc(dblMaxAcc, strIpAddress);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);
```
## 166 setMaxCartTranslationVel

```
int setMaxCartTranslationVel (dblMaxCartTranslationVel, strIpAddress = " ")
设置指定IP地址机械臂笛卡尔空间最大软平移速度。
参数：
dblMaxCartTranslationVel：输入参数，用于存放笛卡尔空间最大软平移速度，double型
变量，单位：m/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblLinearVel = 0.5;
int ret = setMaxCartTranslationVel(dblLinearVel, strIpAddress);
printf("setMaxCartTranslationVel return = %d\n",ret);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);

## 167 setMaxCartRotationVel

```
int setMaxCartRotationVel (dblMaxCartRotationVel, strIpAddress = " ")
设置指定IP地址机械臂笛卡尔空间最大软旋转速度。
参数：
dblMaxCartRotationVel：输入参数，迪卡尔空间最大软旋转速度，double型变量，单位：
rad/s。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。


### 0 ：成功。

### 调用示例：

```
const char* strIpAddress= "*************";
double dblRotationVel = 1;
int ret = setMaxCartRotationVel(dblRotationVel, strIpAddress);
printf("setMaxCartRotationVel return = %d\n",ret);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);
```
## 168 setMaxCartTranslationAcc

```
int setMaxCartTranslationAcc (dblMaxCartTranslationAcc, strIpAddress = " ")
设置指定IP地址机械臂笛卡尔空间最大软平移加速度。
参数：
dblMaxCartTranslationAcc：输入参数，笛卡尔空间最大软平移加速度，double型变量，
单位：m/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblLinearAcc = 1;
int ret = setMaxCartTranslationAcc(dblLinearAcc, strIpAddress);
printf("setMaxCartTranslationAcc return = %d\n",ret);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);

## 169 setMaxCartRotationAcc

```
int setMaxCartRotationAcc (dblMaxCartRotationAcc, strIpAddress = " ")
设置指定IP地址机械臂笛卡尔空间最大软旋转加速度。
参数：
dblMaxCartRotationAcc：输入参数，笛卡尔空间最大软旋转加速度，double型变量，单
位：rad/s^2 。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。


### 调用示例：

```
const char* strIpAddress= "*************";
double dblRotationAcc = 1;
int ret = setMaxCartRotationAcc(dblRotationAcc, strIpAddress);
printf("setMaxCartRotationAcc return = %d\n",ret);
ret = saveEnvironment(strIpAddress);
printf("saveEnvironment return %d\n",ret);
```
## 170 requireHandlingError

```
int requireHandlingError (int *error, strIpAddress = " ")
获取已确认且待处理的错误码。
参数：
error：用于存放错误码的值
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
**int errorCode = 0, ret = requireHandlingError(&errorCode);**

## 171 getJointsSoftLimitRange

```
int getJointsSoftLimitRange (double* dblMinPos, double* dblMaxPos, strIpAddress = " ")
获取指定IP地址机械臂各关节软限位。
参数：
dblMinPos：输出参数，关节角的最小软限位，大小为 JOINT_NUM的数组，单位：
rad。
dblMaxPos：输出参数，关节角的最大软限位，大小为 JOINT_NUM的数组，单位：
rad。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
<0：失败。失败原因详见错误码。
0 ：成功。
```

### 调用示例：

```
const char* strIpAddress= "*************";
double dblMinPos[7] = {0}, dblMaxPos[7]={0};
int ret = getJointsSoftLimitRange(dblMinPos, dblMaxPos, strIpAddress);
printf("getJointsSoftLimitRange return %d\n",ret);
for (int i = 0; i < 7; ++i) {
printf("Joint %d min:%f\n", i + 1, dblMinPos[i]);
printf("Joint %d max:%f\n", i + 1, dblMaxPos[i]);
```
```
}
```
## 172 setJointsSoftLimitRange

```
int setJointsSoftLimitRange (dblMinPos, dblMaxPos, strIpAddress = " ")
设置指定IP地址机械臂各关节软限位。
参数：
dblMinPos：输入参数，关节角的最小软限位，大小为 JOINT_NUM的数组，单位：
rad。
dblMaxPos：输入参数，关节角的最大软限位，大小为 JOINT_NUM的数组，单位：
rad。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* strIpAddress= "*************";
double dblMinPos[7] = {0}, dblMaxPos[7]={0};
int ret = getJointsSoftLimitRange(dblMinPos, dblMaxPos, strIpAddress);
for (int i = 0; i < 7; ++i) {
dblMinPos[i]++;
dblMaxPos[i]--;
}
setJointsSoftLimitRange(dblMinPos, dblMaxPos, strIpAddress);

## 173 getFunctionOptI4

```
int getFunctionOptI4 (function_id, opt_name, int *opt_value, strIpAddress = " ")
获取指定功能的可选参数。
参数：
```

```
function_id：指定功能Id, 0为自由驱动（暂不支持）， 1 为笛卡尔阻抗。
opt_name：功能的可选参数，function_id 为 1 时，参数如下：
a) 0x10100：获取笛卡尔阻抗的参考坐标系；
b) 0x10101：获取笛卡尔阻抗是否锁轴。
opt_value：用于存放参数的值，随opt_name的值变化：
a) 0x10100： 0 代表基坐标系， 1 代表工具坐标系；
b) 0x10101： 1 代表锁轴。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
int opt_value = 0;
int ret = getFunctionOptI4 (1, 0x10100, &opt_value);

## 174 setFunctionOptI4

```
int setFunctionOptI4 (function_id, opt_name, int opt_value, strIpAddress = " ")
设置指定功能的可选参数。
参数：
function_id：指定功能Id, 0为自由驱动（暂不支持）， 1 为笛卡尔阻抗。
opt_name：功能的可选参数，function_id 为 1 时，参数如下：
a) 0x10100：表示笛卡尔阻抗的参考坐标系；
b) 0x10102：笛卡尔阻抗锁轴。
opt_value：用于设置参数的值，随opt_name的值变化：
a) 0x10100： 0 代表基坐标系， 1 代表工具坐标系；
b) 0x10102：当前版本仅支持锁第 3 轴（即取值 2 ），- 1 代表解锁。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
int ret = setFunctionOptI4 (1, 0x10100, 1);


## 175 enterRescueMode

```
int enterRescueMode(strIpAddress = "")
进入安全处理状态，安全处理状态有三种模式，安全零力模式，关节驱动模式和笛卡尔
驱动模式，默认进入关节驱动模式，进入安全处理状态后可通过switchRescueMode来切
换安全处理模式，只有控制器处于 Idle 状态时可以进行该操作。原函数名为
enterSafetyIdle。
参数：
```
```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = enterRescueMode(strIpAddress);

## 176 leaveRescueMode

```
int leaveRescueMode (strIpAddress = "")
退出安全处理模式。原函数名为leaveSafetyIdle。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = leaveRescueMode(strIpAddress);

## 177 getCartImpedanceCoordinateType......................................................................


```
int getCartImpedanceCoordinateType(strIpAddress = " ")
在指定IP地址机械臂上，获取设置笛卡尔阻抗模式时坐标系种类。
参数：
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
坐标系的类型， 0 ：基坐标系， 1 ：工具坐标系。
调用示例：
const char* strIpAddress = "************* ";
int ret = getCartImpedanceCoordinateType(strIpAddress);
if(ret == 0){
printf( "it’s base coordinate\n ");
}
else{
printf( "it’s tool coordinate\n ");
}
```
## 178 setCartImpedanceCoordinateType

```
int setCartImpedanceCoordinateType(intCoordinateType, strIpAddress = " ")
在指定IP地址机械臂上，设置笛卡尔阻抗模式时坐标系种类。
参数：
intCoordinateType：坐标系的类型， 0 ：基坐标系， 1 ：工具坐标系。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "************* ";
int ret = setCartImpedanceCoordinateType(0,strIpAddress);
if(ret < 0){
printf( "setCartImpedanceCoordinateType failed! Return value = %d\n ", ret);
}


## 179 setJointLockedInCartImpedanceMode

```
int setJointLockedInCartImpedanceMode(const bool bLock, const int intLockedJointIndex,
strIpAddress = "")
设置指定IP地址机械臂在笛卡尔阻抗和/或力控模式下锁定/解锁某轴（当前版本仅支持
第 3 轴）。该设置将在机械臂进入笛卡尔阻抗或者力控模式时正式生效。（注：该API
是一个设置选项，默认情况下不锁轴，如果需要在笛卡尔阻抗模式或力控模式下锁定某
轴，需要先设置该选项再进入笛卡尔阻抗模式或力控模式）
参数：
bLock：输入参数，如果该值为 true，表示机械臂在笛卡尔阻抗和/或力控模式下将锁定
某轴（当前版本仅支持锁定第 3 轴，即intLockedJointIdex必须为 2 ，否则锁定无效）；
如果该值为false，则不论intLockedJointIdex取值多少，均表示机械臂在笛卡尔阻抗和/或
力控模式下解锁某轴（当前版本仅支持解锁第 3 轴）。
intLockedJointIdex：输入可选参数，表示轴的索引值（索引从 0 开始），缺省值为 2 （即
第 3 轴）。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
// 设置锁定 3 轴
const char* robot0 = "*************";
const bool bLock = true;
int intLockedJointIndex = 2;
int ret = setJointLockedInCartImpedanceMode(bLock, intLockedJointIndex, robot0);
printf("setJointLockedInCartImpedanceMode return = %d\n",ret);
// 设置解锁 3 轴
const char* robot0 = "*************";
const bool bLock = false;
int intLockedJointIndex = -1;
int ret = setJointLockedInCartImpedanceMode(bLock, intLockedJointIndex, robot0);
printf("setJointLockedInCartImpedanceMode return = %d\n",ret);

## 180 getJointLockedInCartImpedanceMode

```
int getJointLockedInCartImpedanceMode(bool &isLocked, strIpAddress = "")
查询指定IP地址机械臂在笛卡尔阻抗和/或力控模式下某轴（当前版本仅支持第 3 轴）是
否属于锁定/解锁状态。
```

### 参数：

```
isLocked：输出参数，如果该值为 true，表示机械臂在笛卡尔阻抗和/或力控模式下某轴
（当前版本仅支持第 3 轴）处于锁定状态；如果该值为false，则表示机械臂在笛卡尔阻
抗和/或力控模式下某轴（当前版本仅支持第 3 轴）处于解锁状态。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
```
- 1 ：失败。
0 ：成功。
调用示例：
const char* robot0 = "*************";
bool bIsLocked = false;
int ret = getJointLockedInCartImpedanceMode(bIsLocked, robot0);
printf("getJointLockedInCartImpedanceMode return = %d\n",ret);

## 181 setThresholdTorque

```
int setThresholdTorque (arrThreshold,strIpAddress = "")
设置指定IP地址机械臂各关节传感器检测阈值，此阈值在切换工作模式时用于检测是否
允许切换工作模式。
参数：
arrThreshold：表示各关节阈值arrThreshold 的数组的首地址，数组长度为JOINT_NUM,
单位（N.m)
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double arrThreshold[7] = { 6, 6, 5, 5, 2, 2, 2};
const char* strIpAddress = "*************";
if (setThresholdTorque (arrThreshold, strIpAddress) < 0)
{
printf("Diana API setThresholdTorque failed!\n");
}

## 182 getThresholdTorque

```
int getThresholdTorque(arrThreshold,strIpAddress = "")
```

### 获取指定IP地址机械臂各关节传感器检测阈值，此阈值在切换工作模式时用于检测是否

### 允许切换工作模式。

### 参数：

```
arrThreshold：表示各关节阈值arrThreshold 的数组的首地址，数组长度为JOINT_NUM,
单位（N.m)
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
double arrThreshold [JOINT_NUM] = { 0};
const char* strIpAddress = "*************";
if ( getThresholdTorque (arrThreshold, strIpAddress) < 0)
{
printf("Diana API getThresholdTorque failed!\n");
}
else
{
printf(" getThresholdTorque : arrThreshold=%f, %f, %f, %f, %f, %f, %f\n",
arrThreshold[0], arrThreshold[1], arrThreshold[2], arrThreshold[3], arrThreshold[4],
arrThreshold[5], arrThreshold[6]);
}

## 183 setHeartbeatParam

```
int setHeartbeatParam(int disconnectTimeout, int stopRobotTimeout, strIpAddress = "")
设置API心跳相关的超时时间。
参数：
disconnectTimeout：API断连超时时间,单位(毫秒ms)(- 1 时相当于永不断连)
stopRobotTimeout：停止机器人超时时间,单位(毫秒ms)(- 1 时相当于永不停止)
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。


### 调用示例：

```
const char* strIpAddress = "*************";
int ret = setHeartbeatParam(-1, -1, strIpAddress);
if (ret == -1) {
printf("setHeartbeatParam fail\n");
}
```
## 184 getHeartbeatParam

```
int getHeartbeatParam(int *disconnectTimeout, int *stopRobotTimeout, strIpAddress = "")
获取API心跳相关的超时时间。
参数：
disconnectTimeout：API断连超时时间,单位(毫秒ms)(- 1 时相当于永不断连)
stopRobotTimeout：停止机器人超时时间,单位(毫秒ms)(- 1 时相当于永不停止)
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int disconnectTimeout;
int stopRobotTimeout;
int ret = getHeartbeatParam(&disconnectTimeout, &stopRobotTimeout, strIpAddress);
if (ret == 0) {
printf("timeout:%d:%d\n", disconnectTimeout, stopRobotTimeout);
}

## 185 customRobotState

```
int customRobotState(int action, unsigned long long customBits, strIpAddress = "")
定制机器人状态推送信息。
参数：
action：_CustmRoobotStateAction枚举类型，分别定义为：
API_CUSTOM_ADD：增加customBits指示的推送信息。
API_CUSTOM_DEL：减少customBits指示的推送信息。
API_CUSTOM_RSEET：重置为customBits指示的推送信息。
CustomBits：定制推送信息比特位。可定制以下信息：
```

```
#define ROBOTSTATE_CUSTOM_BASIC (0x00000001) //基本状态信息，必须
推送，不可定制，包括 BasicRobotState_1 中 stateBits, trajectoryState及 BasicRobotState2
中所有数据
#define ROBOTSTATE_CUSTOM_JOINTPOS (0x00000002) //关节反馈位置
#define ROBOTSTATE_CUSTOM_LINKJOINTPOS (0x00000004) //低速侧关节反
馈位置
#define ROBOTSTATE_CUSTOM_JOINTANGULARVEL (0x00000008) // 关节反馈
速度
#define ROBOTSTATE_CUSTOM_JOINTCURRENT (0x00000010) // 关节反馈电流
#define ROBOTSTATE_CUSTOM_ORIGINJOINTTORQUE (0x00000020) // 关节反馈
力矩(含零偏)
#define ROBOTSTATE_CUSTOM_JOINTTORQUEOFFSET (0x00000040) //关节扭矩
传感器零偏
#define ROBOTSTATE_CUSTOM_JOINTFORCE (0x00000080) // 关节外力
#define ROBOTSTATE_CUSTOM_TCPFORCE (0x00000100) // 末端受到的外力
#define ROBOTSTATE_CUSTOM_ALL (0xffffffff) //定制所有
```
```
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = customRobotState(API_CUSTOM_RESET, ROBOTSTATE_CUSTOM_TCPFORCE
| ROBOTSTATE_CUSTOM_JOINTFORCE |
ROBOTSTATE_CUSTOM_JOINTANGULARVEL, strIpAddress);
if (ret == -1) {
printf("customRobotState failed\n");
}

## 186 getCustomRobotState

```
int getCustomRobotState(unsigned long long *customBits, strIpAddress = "")
获取当前定制的推送信息。
参数：
```

```
customBits：定制的推送信息比特位。参见customRobotState
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
unsigned long long stateFlags = 0;
ret = getCustomRobotState(&stateFlags, strIpAddress);
if (ret == -1) {
printf("getCustomRobotState failed\n");
} else {
if (stateFlags & ROBOTSTATE_CUSTOM_JOINTPOS == 0) {
printf("关节反馈位置推送信息未定制");
} else {
printf("关节反馈位置推送信息已定制");
}
}

## 187 getTcpPoseByTcpName

```
int getTcpPoseByTcpName(const char *tcpName, double *coordinate,strIpAddress = "")
根据工具坐标系名称获取工具坐标系位姿。
参数：
tcpName：工具坐标系名称
cooridnate：位姿数组的首地址，数组长度为 6
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
const char *tcpName = "coor1";
double coorTcpPose[6];
int ret = getTcpPoseByTcpName(tcpName, coorTcpPose, strIpAddress);


```
if (ret == 0) {
for (int i = 0; i < 6; i++) {
printf("%f, ", coorTcpPose[i]);
}
}
```
## 188 getTcpPoseByWorkPieceName

```
int getTcpPoseByWorkPieceName(const char *workPieceName, double *coordinate,
strIpAddress = "")
根据工件坐标系名称获取工件坐标位姿。
参数：
workPieceName：工件坐标系名称
cooridnate：位姿数组的首地址，数组长度为 6
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
const char *tcpName = "wpcoor1";
double coorTcpPose[6];
int ret = getTcpPoseByWorkPieceName(tcpName, coorTcpPose, strIpAddress);
if (ret == 0) {
for (int i = 0; i < 6; i++) {
printf("%f, ", coorTcpPose[i]);
}
}

## 189 getPayLoadByTcpName

```
int getPayLoadByTcpName(const char *tcpName, double *payload, strIpAddress = "")
根据工具坐标系名称获取工具坐标系负载信息。
参数：
tcpName：工具坐标系名称
payload：负载数组首地址，数组长度为 10
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
```

### 时生效。

### 返回值：

### 0 ：成功。

### - 1 ：失败。

### 调用示例：

```
const char* strIpAddress = "*************";
double payLoad[10];
int ret = getPayLoadByTcpName(tcpName, payLoad, strIpAddress);
if (ret == 0) {
for (int i = 0; i < 10; i++) {
printf("%f, ", payLoad[i]);
}
}
```
## 190 setDefaultToolTcpCoordinate

```
int setDefaultToolTcpCoordinate(const char *tcpName, strIpAddress = "")
设置默认工具坐标系。
参数：
tcpName：工具坐标系名称
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
const char *tcpName = "coor1";
int ret = setDefaultToolTcpCoordinate(tcpName, strIpAddress);
if (ret == -1) {
printf("setDefaultToolTcpCoordinate fail \n");
}

## 191 setDefaultWorkPieceTcpCoordinate

```
int setDefaultWorkPieceTcpCoordinate(const char *workPieceName, strIpAddress = "")
设置默认工件坐标系。
参数：
```

```
workPieceName：工件坐标系名称
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
const char *setWpName = "wpcoor1";
int ret = setDefaultWorkPieceTcpCoordinate(setWpName, strIpAddress);
if (ret == -1) {
printf(" setDefaultWorkPieceTcpCoordinate fail \n");
}

## 192 getDefaultTcpCoordinate

```
int getDefaultTcpCoordinate(char *tcpName, strIpAddress = "")
获取默认的工具坐标系名称。
参数：
tcpName：工具坐标系名称数组首地址，数组长度不大于 256
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
char defaultTcpName[256];
int ret = getDefaultTcpCoordinate(defaultTcpName, strIpAddress);
if (ret == 0) {
printf("default Tcp Name:%s\n", defaultTcpName);
}

## 193 getDefaultWorkPieceCoordinate

```
int getDefaultWorkPieceCoordinate(const char *workpieceName, strIpAddress = "")
获取默认的工件坐标系名称。
参数：
```

```
workpieceName：工件坐标系名称数组首地址，数组长度不少于 256
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
char defaultWpName[256];
int ret = getDefaultWorkPieceCoordinate(defaultWpName, strIpAddress);
if (ret == 0) {
printf("defaultWpName:%s\n", defaultWpName);
}

## 194 setVelocityPercentValue

```
int setVelocityPercentValue(int value, strIpAddress = "")
设置速度百分比。
参数：
value：整形，百分比的值。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int value = 20;
int ret = setVelocityPercentValue(value, strIpAddress);
if (ret == 0) {
printf("setVelocityPercentValue:%s\n", value);
}

## 195 switchRescueMode

```
int switchRescueMode (int scuWorkMode, strIpAddress = "")
切换安全处理模式，只有在安全处理模式下才能进行切换。
参数：
```

```
scuWorkMode：安全处理模式，取值为： 12 - 安全零力模式， 13 - 关节驱动模式， 14 - 笛卡
尔驱动模式。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
int ret = enterRescueMode(strIpAddress);
if (ret == 0) {
ret = switchRescueMode(12, strIpAddress);
}

## 196 getAvoidSingular

```
int getAvoidSingular(bool* bEnable, strIpAddress = "")
获取手动页是否开启了奇异点避让。
参数：
bEnable：布尔型，奇异点避让是否开启。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
bool enable = false;
int ret = getAvoidSingular(&enable, strIpAddress);
if (ret == 0) {
printf("getAvoidSingular:%d\n", enable);
}

## 197 setAvoidSingular

```
int wetAvoidSingular(bool bEnable, strIpAddress = "")
设置手动页是否开启奇异点避让。
```

### 参数：

```
bEnable：布尔型，奇异点避让是否开启。
strIpAddress：可选参数，需要控制机械臂的IP地址字符串，不填仅当只连接一台机械臂
时生效。
返回值：
0 ：成功。
```
- 1 ：失败。
调用示例：
const char* strIpAddress = "*************";
bool enable = false;
int ret = setAvoidSingular(enable, strIpAddress);
if (ret == 0) {
printf("setAvoidSingular:%d\n", enable);
}

## 198 get_version

```
bool get_version(const char *ip, int type, char *pVersion, unsigned short &size)
获取机械臂相关版本信息。
参数：
ip：控制机械臂ip地址信息字符串。
type：版本信息类型，取值范围包括以下几种
1 ：控制箱系统版本。
2 ：服务器版本。
3 ：应用框架版本。
4 ：算法版本。
5 ：Profinet版本。
6 ：机械配置版本。
7 ：IO板协议版本。
8 ：关节 1 协议版本。
9 ：关节 2 协议版本。
10 ：关节 3 协议版本。
11 ：关节 4 协议版本。
12 ：关节 5 协议版本。
13 ：关节 6 协议版本。
14 ：关节 7 协议版本。
```

```
pVersion：版本类型对应信息内容。
size：pVersion内容所占空间大小。
返回值：
true：成功。
false：失败。
调用示例：
const char* ip= "*************";
unsigned short a = 256;
char versionContent[256];
int type = 1;
bool ret = get_version (ip, type, versionContent, a);
if (ret == true) {
printf("versionContent:%s\n", versionContent);
}
```
## 附件A：DianaApi接口错误码

```
表 1 ：Diana API接口错误码表
系统错误宏定义 错误码 说明
```
ERROR_CODE_WSASTART_FAIL - (^1001) 加载
windows
系统
socket
库失败
ERROR_CODE_CREATE_SOCKET_FAIL - (^1002) 创建
socket
对象失
败
ERROR_CODE_BIND_PORT_FAIL - 1003 socket
绑定端
口失败
ERROR_CODE_SOCKET_READ_FAIL - 1004 socket
的select
调用失
败
ERROR_CODE_SOCKET_TIMEOUT - 1005 socket


的select
调用超
时
ERROR_CODE_RECVFROM_FAIL - 1006 socket
接收数
据失败
ERROR_CODE_SENDTO_FAIL - 1007 socket
发送数
据失败

ERROR_CODE_LOST_HEARTBEAT - (^1008) 服务端
的心跳
连接丢
失
ERROR_CODE_LOST_ROBOTSTATE - (^1009) 服务端
信息反
馈丢失
ERROR_CODE_GET_DH_FAILED - (^1010) 获取DH
信息失
败
ERROR_CODE_RELEASE_BRAKE_FAILED - (^1011) 打开抱
闸失败
ERROR_CODE_HOLD_BRAKE_FAILED - 1012 关闭抱
闸失败
ERROR_CODE_IP_ADDRESS_NOT_REGISTER - 1013 该IP机
械臂尚
未
initSrv
ERROR_CODE_ROBOTARM_OVERNUMBER - 1014 超过最
大支持
机械臂
数
ERROR_CODE_SOCKET_OTHER_ERROR - (^1015) 其他
socket
连接错


### 误

ERROR_CODE_JOINT_REGIST_ERROR - (^2001) 硬件错
误
ERROR_CODE_EEPROM_READ - 2010 关节读
EEPROM
参数错
误
ERROR_CODE_EEPROM_WRITE - 2011 关节写
EEPROM
参数错
误
ERROR_CODE_LS_ENCODER_OVERSPEED - 2012 低速侧
编码器
反馈位
置超速
ERROR_CODE_LS_ENCODER_FB_ERROR - (^2013) 低速侧
编码器
反馈数
据错误
ERROR_CODE_MS_SINGAL_Z_ERROR - (^2014) 高速侧
编码器Z
信号异
常
ERROR_CODE_THREE_PHASE_CURRENT - 2015 电机三
相电流
瞬时过
流
ERROR_CODE_TORQUE_SENSOR_READ_ERROR - 2016 扭矩传
感器读
取故障
ERROR_CODE_COMMUNICATE_ERROR - 2101 底层通
信失败
ERROR_CODE_LOST_HEART_WITH_DIANAROBOT_ERROR - 2102 与后台


### 服务心

### 跳断开

ERROR_CODE_CALLING_CONFLICT_ERROR - (^2201) 调用冲
突
ERROR_CODE_COLLISION_ERROR - (^2202) 发生碰
撞
ERROR_CODE_NOT_FOLLOW_POSITION_CMD - 2203 力控模
式关节
位置与
指令发
生滞后
ERROR_CODE_NOT_FOLLOW_TCP_CMD - (^2204) 力控模
式 TCP
位置与
指令发
生滞后
ERROR_CODE_NOT_ALL_AT_OP_STATE - 2205 有关节
未进入
正常状
态
ECODE_OUT_RANGE_FEEDBACK - (^2206) 关节角
反馈超
软限位
ECODE_EMERGENCY_STOP - 2207 急停已
拍下
ECODE_NO_INIT_PARAMETER - (^2208) 找不到
关节初
始参数
ECODE_NOT_MATCH_LOAD - (^2209) 负载与
理论值
不匹配
ERROR_CODE_CANNOT_MOVE_WHILE_FREE_DRIVING - 2210 自由驱
动模式


### 不能执

### 行其他

### 运动

ERROR_CODE_CANNOT_MOVE_WHILE_ZERO_SPACE_FREE_DRIVING - (^2211) 零空间
自由驱
动模式
下不能
执行其
他运动
ERROR_CODE_ROBOT_IN_VIRTUAL_WALL - (^2214) 有关节
在虚拟
墙内
ERROR_CODE_CONFLICT_TASK_RUNNING - (^2215) 运动任
务冲突
ERROR_CODE_OUT_OF_PHYSICAL_RANGE_FEEDBACK - 2216 超出物
理限位
ERROR_CODE_OUT_SOFT_RANGE_FEEDBACK - 2217 超出软
限位
ERROR_CODE_CONVEYOR_NOT_ONLINE - (^2218) 传送带
编码器
不在线
ERROR_CODE_CONVEYOR_IS_TRACKED
- (^2219) 传送带
正在被
跟踪，
不能
moveJ
ERROR_CODE_CONVEYOR_CANNOT_TRACK

- 2220 开启跟
    踪传送
    带失败

ERROR_CODE_INPUT_OUT_OF_EXTREME_POSITION_RANGE - (^2221) 超出关
节极限
位置
ERROR_CODE_SLOPOVER_VIRTUAWALL - (^2222) 关节越


### 过虚拟

### 墙

ERROR_CODE_SLOPOVER_REDUCE_VIRTUAWALL - (^2223) 关节越
过减速
墙
ERROR_CODE_PLAN_ERROR - 2301 路径规
划失败
ERROR_CODE_INTERPOLATE_POSITION_ERROR - (^2302) 位置模
式插补
失败
ERROR_CODE_INTERPOLATE_TORQUE_ERROR - 2303 阻抗模
式插补
失败
ERROR_CODE_SINGULAR_VALUE_ERROR - 2304 奇异位
置
ERROR_CODE_ PLANNER_ERROR - (^2305) 规划失
败
ERROR_CODE_ HOME_POSITION_ERROR - (^2306) 需要寻
零
ERROR_CODE_ FATAL - 2307 严重错
误(关节
位置超
出物理
极限)
ERROR_CODE_ POS_LIMIT - (^2308) 位置超
出限制
ERROR_CODE_ FORCE_LIMIT - 2309 关节力
矩超出
限制
ERROR_CODE_ SPEED_LIMIT - 2310 速度超
出限制
ERROR_CODE_ ACC_LIMIT - 2311 加速度
超出限


### 制

### ERROR_CODE_ JERK_LIMIT - 2312 加加速

### 度超出

### 限制

ERROR_CODE_ MOTION_LIMIT - (^2313) 位置超
出限制
ERROR_CODE_ IK_TRACK - 2314 轨迹跟
踪过程
逆解求
解失败
ERROR_CODE_ IK_GENERAL - 2315 通用位
置逆解
求解失
败
ERROR_CODE_ PLAN_INPUT - (^2316) 轨迹规
划输入
错误
ERROR_CODE_ PLAN_MOVJ - 2317 关节空
间轨迹
规划失
败
ERROR_CODE_ PLAN_MOVL - (^2318) 直线轨
迹规划
失败
ERROR_CODE_ PLAN_MOVC - 2319 圆弧轨
迹规划
失败
ERROR_CODE_ PLAN_BLEND - 2320 过渡轨
迹规划
失败
ERROR_CODE_ PLAN_SPDJ - 2321 SpeedJ
轨迹规
划失败


ERROR_CODE_ PLAN_SPDL - 2322 SpeedL
轨迹规
划失败
ERROR_CODE_ PLAN_SRVJ - 2323 ServoJ
轨迹规
划失败
ERROR_CODE_ PLAN_SRVL - 2324 ServoL
轨迹规
划失败

ERROR_CODE_ MOVE_UNKNOWN - (^2325) 未知运
动类型
或运动
类型不
匹配
ERROR_CODE_ MOVE_UNPLAN - (^2326) 轨迹未
规划
ERROR_CODE_ MOVE_INPUT - (^2327) 轨迹插
补输入
错误
ERROR_CODE_ MOVE_INTERP - (^2328) 轨迹插
补失败
ERROR_CODE_ PLAN_TRANSLATION - 2329 移动规
划失败
ERROR_CODE_ PLAN_ROTATION - 2330 旋转规
划失败
ERROR_CODE_ PLAN_JOINTS - (^2331) 关节规
划失败
ERROR_CODE_ UNMATCHED_JOINTS_NUMBER - 2332 零空间
自由驱
动关节
数不匹
配
ERROR_CODE_ TCPCALI_FUTILE_WPS - (^2333) 示教点
不合理


### ERROR_CODE_ TCPCALI_FIT_FAIL - 2334 拟合

### TCP 失

### 败

ERROR_CODE_ DHCALI_FIT_WF_FAIL - (^2335) DH参数
初始化
世界坐
标系失
败
ERROR_CODE_ DHCALI_FIT_TF_FAIL - (^2336) DH参数
初始化
工具坐
标系失
败
ERROR_CODE_DHCALI_FIT_DH_FAIL - 2337 DH参数
拟合失
败
ERROR_CODE_ DHCALI_INIT_FAIL - (^2338) DH参数
初始化
失败
ERROR_CODE_ SLFMOV_SINGULAR - (^2339) 零空间
运动至
奇异位
置
ERROR_CODE_ SLFMOV_FUTILE - 2340 零空间
运动在
笛卡尔
空间内
无效
ERROR_CODE_SLFMOV_JNTLIM - (^2341) 零空间
运动至
关节限
位
ERROR_CODE_ SLFMOV_SPDLIM - (^2342) 零空间


### 运动至

### 关节限

### 位

ERROR_CODE_ SLFMOV_FAIL - (^2343) 零空间
运动插
补失败
ERROR_CODE_ SLFMOV_FFC_FAIL - (^2344) 零空间
运动前
馈补偿
错误
ERROR_CODE_ LOADIDENT_INIT_FAIL - (^2345) 负载辨
识初始
化失败
ERROR_CODE_ LOADIDENT_UFB_FAIL - (^2346) 负载辨
识更新
反馈数
据错误
ERROR_CODE_ LOADIDENT_FIT_FAIL - (^2347) 负载辨
识失败
ERROR_CODE_ LOADIDENT_NONLOADED - 2348 未检测
到有效
负载
ERROR_CODE_PARAMETER_POINTER_EQUALS_NULLPTR - 2901 输入参
数为空
ERROR_CODE_PARAMETER_POINTER_EQUALS_NAN_OR_INF - 2902 输入参
数存在
nan 或
者inf
ERROR_CODE_ENTER_FORCE_MODE_ERROR - (^2903) 进入力
控模式
失败
ERROR_CODE_CANNOT_SET_VELOCITY_PERCENT_VALUE - 2904 设置速
度百分


### 比失败

ERROR_CODE_INPUT_OUT_OF_PHYSICAL_POSITION_RANGE - (^2905) 输入参
数超出
物理极
限位置
ERROR_CODE_RESOURCE_UNAVAILABLE - (^3001) 参数错
误
ERROR_CODE_DUMP_LOG_TIMEOUT - (^3002) 导出
Log 文
件超时
ERROR_CODE_DUMP_LOG_FAILED - (^3003) 导出
Log 文
件失败
RESET_DH_FAILED - (^3004) 重置DH
参数失
败
ILLEGAL_PARAMETER - 3006 接口函
数传入
非法参
数
ROBOT_TCP_OVER_SPEED - (^3008) 机器人
TCP 超
速
ROBOT_JOINTS_OVER_SPEED - (^3009) 机器人
关节超
速
注 ： 表 1 中 ERROR_CODE_JOINT_REGIST_ERROR（- 2001 ） 硬 件 错 误 和
ERROR_CODE_NOT_ALL_AT_OP_STATE(-2205)的OP状态错误需要通过调用holdBrake()
合抱闸函数或重启硬件来清除错误。


## 附录B：如何确保运动学逆解唯一

```
Diana机械臂为七自由度机械臂，由于多了一个冗余自由度，理论上存在无数多组逆解
```
(根据末端位姿求解关节角) ，在实际应用中，当执行逆解运算或者进行笛卡尔空间运动时，

有可能出现逆解不唯一的情况。为了确保逆解的唯一性，可采取如下解决方案。

```
第一种情况: 已知其中某个路点对应的关节角
```
例：已知A点关节角Joints_A和B点位姿Pose_B，机械臂在两点之间进行往复运动。

解决方案：向目标点A运动时，调用moveJToTarget或moveLToTarget函数。

const char* strIpAddress = "*************";

```
double Joints_A[7] = {0.000000, 0.523599, 0.000000, 1.570796, 0.000000, -0.872665,
0.000000}; // A点关节角
double pose_B[6] = {0.5, 0.5, 0.5, 0, 0, 0}; // B点位姿
double velL = 0.2, accL = 0.8; // 直线运动的速度与加速度
moveJToTarget(Joints_A, velL, accL,strIpAddress);
wait_move(strIpAddress);
int count = 10;
for (int i = 0; i < count; i++)
{
// 调用moveJToTarget或moveLToTarget函数移动至目标点A
moveLToTarget(Joints_A, velL, accL, strIpAddress);
wait_move(strIpAddress);
// 调用moveJToPose或moveLToPose函数移动至目标点B
moveLToPose(pose_B, velL, accL, nullptr, strIpAddress);
wait_move(strIpAddress);
}
第二种情况: 所有路点对应的关节角均未知
例：已知A点位姿Pose_A和B点位姿Pose_B，机械臂在两点之间进行往复运动。
解决方案：首先在A点和B点附近分别示教一个参考点，并记录下参考点位下的机械
```
臂关节角q_ref_A和q_ref_B，然后利用inverse_ext函数，求解目标位姿下相对于参考点关

节角距离最近的逆解，最后调用moveJToTarget或moveLToTarget函数进行运动。


```
const char* strIpAddress = "*************";
double Pose_A[6] = {0.5, 0.5, 0.5, 0, 0, 0};
double Pose_B[6] = {0.4, 0.6, 0.2, 0, 0, 0};
// 示教两个参考点位并记录下关节角q_ref_A, q_ref_B
double q_ref_A [7] = {-0.645772, 0.261799, -0.157080, 1.675516, 0.052360, -1.186824, -
```
0.802851};

```
double q_ref_B[7] = {-0.645772, 0.261799, -0.157080, 1.675516, 0.052360, -1.186824, -
```
0.802851};

```
double velJ = 0.25, accJ = 1.0; // 关节空间运动的速度与加速度
double velL = 0.1, accL = 0.4; // 直线运动的速度与加速度
int count = 10;
for ( int i = 0; i < count; i++)
{
// 调用inverse_ext函数, 根据q_ref_A计算Pose_A所对应的关节角 Joints_A
double Joints_A [7] = {0.0};
inverse_ext(q_ref_A, Pose_A, Joints_A);
// 调用moveJToTarget或moveLToTarget函数移动到目标点A
moveJToTarget(Joints_A, velJ, accJ);
wait_move(strIpAddress);
// 调用inverse_ext函数, 根据q_ref_B计算Pose_B所对应的关节角 Joints_B
double Joints_B [7] = {0.0};
inverse_ext(q_ref_B, Pose_B, Joints_B);
// 调用moveJToTarget或moveLToTarget函数移动到目标点B
moveLToTarget(Joints_B, velL, accL);
wait_move(strIpAddress);
}
```

