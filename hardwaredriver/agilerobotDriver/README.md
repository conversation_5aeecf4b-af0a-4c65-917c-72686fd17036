# AgileRobot Driver

这是一个用于控制AgileRobot机械臂的C++驱动程序，基于Diana API实现。

## 功能特性

- 机器人连接管理
- 关节空间和笛卡尔空间运动控制
- 实时状态获取（关节位置、TCP位姿等）
- 正逆运动学计算
- 数字I/O控制
- 示教模式支持
- 错误处理和状态监控

## 主要API

### 运动控制
- `movej(q, speed, zone, acc)` - 关节空间运动
  - `speed`: 角速度 (rad/s)，范围 0.0-10.0
  - `acc`: 角加速度 (rad/s²)，范围 0.0-50.0
- `movel(pose, speed, zone, acc)` - 笛卡尔空间直线运动
  - `speed`: 线速度 (m/s)，范围 0.0-5.0
  - `acc`: 线加速度 (m/s²)，范围 0.0-20.0

### 状态获取
- `getCurrentTCP()` - 获取当前TCP位姿
- `getCurrentJointPositions()` - 获取当前关节位置
- `getForwardKinematics(q)` - 正运动学计算
- `getInverseKinematics(pose, q_near)` - 逆运动学计算

### I/O控制
- `setDigitalOutput(port, value)` - 设置数字输出
- `getDigitalInput(port)` - 读取数字输入

### 示教模式
- `startTeachMode(mode)` - 启动示教模式
- `stopTeachMode()` - 停止示教模式

## 重要修复说明

### API参数单位修复
根据Diana API文档，已修复以下参数单位问题：

1. **movej函数**：
   - 速度参数：从比例值(0.0-1.0)改为实际角速度(rad/s)
   - 加速度参数：从比例值(0.0-1.0)改为实际角加速度(rad/s²)
   - 现在调用`moveJToTarget`而不是`moveJ`宏

2. **movel函数**：
   - 速度参数：从比例值(0.0-1.0)改为实际线速度(m/s)
   - 加速度参数：从比例值(0.0-1.0)改为实际线加速度(m/s²)
   - 现在调用`moveLToPose`而不是`moveL`宏
   - 支持奇异点避让功能

### 新增API支持
- 添加了`moveJToTarget`和`moveLToPose`函数声明
- 支持整形功能参数（zv_shaper_order, zv_shaper_frequency, zv_shaper_damping_ratio）
- 支持奇异点避让参数（avoid_singular）

## 使用示例

```cpp
#include "agilerobotDriver.h"

int main() {
    // 创建驱动实例
    agilerobotDriver robot;
    
    // 连接机器人
    if (robot.connect("192.168.1.100")) {
        std::cout << "机器人连接成功" << std::endl;
        
        // 创建目标关节位置
        rw::math::Q targetJoints(7);
        targetJoints[0] = 0.0;
        targetJoints[1] = -0.5;
        targetJoints[2] = 0.0;
        targetJoints[3] = -1.5;
        targetJoints[4] = 0.0;
        targetJoints[5] = 1.0;
        targetJoints[6] = 0.0;
        
        // 执行关节运动
        if (robot.movej(targetJoints, 0.1, 0, 0.1)) {
            std::cout << "关节运动执行成功" << std::endl;
        }
        
        // 获取当前TCP位姿
        auto currentTCP = robot.getCurrentTCP();
        std::cout << "当前TCP位置: " 
                  << currentTCP.P()[0] << ", " 
                  << currentTCP.P()[1] << ", " 
                  << currentTCP.P()[2] << std::endl;
        
        // 断开连接
        robot.disconnect();
    } else {
        std::cout << "机器人连接失败: " << robot.getErrorMessage() << std::endl;
    }
    
    return 0;
}
```

## 编译说明

### 依赖项
- C++17 或更高版本
- RobWork库
- Diana API库
- Eigen3
- glog
- fuxicommon

### 编译步骤
```bash
mkdir build
cd build
cmake .. -DBUILD_TESTS=ON
make
```

### 运行测试
```bash
./bin/test_agilerobotDriver
```

## API参考

### 示教模式枚举
```cpp
enum Teach_Mode {
    NO_TEACH = 0,    // 无示教模式
    JOINT_TEACH,     // 关节示教模式
    TCP_TEACH        // TCP示教模式
};
```

### 错误处理
所有函数都包含完整的错误检查和参数验证：
- 连接状态检查
- 参数范围验证
- API调用错误处理
- 详细的错误信息输出

### 线程安全
驱动程序使用全局变量来管理错误状态和机器人状态信息，在多线程环境中使用时需要注意同步。

## 注意事项

1. **IP地址验证**: 连接前会验证IP地址格式和长度
2. **参数范围检查**: 所有运动参数都会进行范围检查
3. **超时处理**: 运动完成等待包含超时机制
4. **错误恢复**: 提供详细的错误信息和恢复建议

## 版本历史

### v1.0.0
- 初始版本
- 实现所有基本功能
- 添加完整的错误处理
- 包含详细的代码注释

## 技术支持

如有问题或建议，请联系技术支持团队。