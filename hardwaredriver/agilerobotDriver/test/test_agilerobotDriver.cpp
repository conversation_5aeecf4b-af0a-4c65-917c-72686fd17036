/**
 * @file test_agilerobotDriver.cpp
 * @brief Test program for agilerobotDriver class
 * <AUTHOR> by AI Assistant
 * @date 2024
 */

#include <iostream>
#include <chrono>
#include <thread>
#include "../include/agilerobotDriver.h"

/**
 * @brief Test basic connection and disconnection
 */
void testConnection() {
    std::cout << "\n=== Testing Connection ===" << std::endl;
    
    agilerobotDriver robot;
    
    // Test connection with a sample IP address
    std::string testIP = "*************";
    std::cout << "Attempting to connect to robot at " << testIP << std::endl;
    
    bool connected = robot.connect(testIP);
    if (connected) {
        std::cout << "✓ Connection successful" << std::endl;
        
        // Test connection status
        if (robot.isConnect()) {
            std::cout << "✓ Connection status verified" << std::endl;
        } else {
            std::cout << "✗ Connection status check failed" << std::endl;
        }
        
        // Test disconnection
        if (robot.disconnect()) {
            std::cout << "✓ Disconnection successful" << std::endl;
        } else {
            std::cout << "✗ Disconnection failed" << std::endl;
        }
    } else {
        std::cout << "✗ Connection failed (expected if robot not available)" << std::endl;
        std::cout << "Error: " << robot.getErrorMessage() << std::endl;
    }
}

/**
 * @brief Test motion functions (without actual robot)
 */
void testMotionFunctions() {
    std::cout << "\n=== Testing Motion Functions ===" << std::endl;
    
    agilerobotDriver robot;
    
    // Create test joint positions
    rw::math::Q testJoints(7);
    testJoints[0] = 0.0;
    testJoints[1] = -0.5;
    testJoints[2] = 0.0;
    testJoints[3] = -1.5;
    testJoints[4] = 0.0;
    testJoints[5] = 1.0;
    testJoints[6] = 0.0;
    
    // Test movej function with correct physical units
    std::cout << "Testing movej function..." << std::endl;
    bool result = robot.movej(testJoints, 0.5, 0, 1.0);  // 0.5 rad/s, 1.0 rad/s²
    std::cout << "movej result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
    
    // Create test TCP pose
    rw::math::Transform3D<> testPose;
    testPose.P()[0] = 0.5;  // X position
    testPose.P()[1] = 0.0;  // Y position
    testPose.P()[2] = 0.3;  // Z position
    
    std::cout << "Testing movel function..." << std::endl;
    result = robot.movel(testPose, 0.1, 0, 0.5);  // 0.1 m/s, 0.5 m/s²
    std::cout << "movel result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
}

/**
 * @brief Test kinematics functions
 */
void testKinematics() {
    std::cout << "\n=== Testing Kinematics Functions ===" << std::endl;
    
    agilerobotDriver robot;
    
    // Test forward kinematics
    rw::math::Q testJoints(7);
    testJoints[0] = 0.0;
    testJoints[1] = -0.5;
    testJoints[2] = 0.0;
    testJoints[3] = -1.5;
    testJoints[4] = 0.0;
    testJoints[5] = 1.0;
    testJoints[6] = 0.0;
    
    rw::math::Transform3D<> resultPose;
    std::cout << "Testing forward kinematics..." << std::endl;
    bool result = robot.getForwardKinematics(testJoints, resultPose);
    std::cout << "Forward kinematics result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
    
    // Test inverse kinematics
    rw::math::Transform3D<> targetPose;
    targetPose.P()[0] = 0.5;
    targetPose.P()[1] = 0.0;
    targetPose.P()[2] = 0.3;
    
    rw::math::Q resultJoints;
    std::cout << "Testing inverse kinematics..." << std::endl;
    result = robot.getInverseKinematics(testJoints, targetPose, resultJoints);
    std::cout << "Inverse kinematics result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
}

/**
 * @brief Test I/O functions
 */
void testIO() {
    std::cout << "\n=== Testing I/O Functions ===" << std::endl;
    
    agilerobotDriver robot;
    
    // Test digital output
    std::cout << "Testing digital output..." << std::endl;
    bool result = robot.setDigitalOutput(1, true);
    std::cout << "Digital output result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
    
    // Test digital input
    std::cout << "Testing digital input..." << std::endl;
    int inputValue = robot.getDigitalInput(1);
    std::cout << "Digital input result: " << inputValue << " (expected -1 without connection)" << std::endl;
}

/**
 * @brief Test teach mode functions
 */
void testTeachMode() {
    std::cout << "\n=== Testing Teach Mode Functions ===" << std::endl;
    
    agilerobotDriver robot;
    
    // Test joint teach mode
    std::cout << "Testing joint teach mode..." << std::endl;
    bool result = robot.startTeachMode(agilerobotDriver::JOINT_TEACH);
    std::cout << "Joint teach mode result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
    
    // Test TCP teach mode
    std::cout << "Testing TCP teach mode..." << std::endl;
    result = robot.startTeachMode(agilerobotDriver::TCP_TEACH);
    std::cout << "TCP teach mode result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
    
    // Test stop teach mode
    std::cout << "Testing stop teach mode..." << std::endl;
    result = robot.stopTeachMode();
    std::cout << "Stop teach mode result: " << (result ? "✓ Success" : "✗ Failed (expected without connection)") << std::endl;
}

/**
 * @brief Test error handling
 */
void testErrorHandling() {
    std::cout << "\n=== Testing Error Handling ===" << std::endl;
    
    agilerobotDriver robot;
    
    // Test error message retrieval
    std::string errorMsg = robot.getErrorMessage();
    std::cout << "Error message: \"" << errorMsg << "\"" << std::endl;
    
    // Test with invalid parameters
    std::cout << "Testing with invalid joint count..." << std::endl;
    rw::math::Q invalidJoints(10);  // Too many joints
    bool result = robot.movej(invalidJoints, 0.1, 0, 0.1);
    std::cout << "Invalid joint count result: " << (result ? "✗ Unexpected success" : "✓ Correctly failed") << std::endl;
}

/**
 * @brief Main test function
 */
int main() {
    std::cout << "=== agilerobotDriver Test Suite ===" << std::endl;
    std::cout << "This test suite verifies the functionality of the agilerobotDriver class." << std::endl;
    std::cout << "Note: Most tests will fail without an actual robot connection, which is expected." << std::endl;
    
    try {
        testConnection();
        testMotionFunctions();
        testKinematics();
        testIO();
        testTeachMode();
        testErrorHandling();
        
        std::cout << "\n=== Test Suite Complete ===" << std::endl;
        std::cout << "All tests executed. Check individual results above." << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "Test suite failed with exception: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}