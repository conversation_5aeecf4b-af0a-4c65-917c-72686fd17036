#include "agilerobotDriver.h"
#include <chrono>
#include <thread>
#include <iostream>
#include <cstring>
#include <cmath>
#include <algorithm>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

// 错误和状态管理的全局变量
static std::string g_lastErrorMessage;
static StrRobotStateInfo g_lastRobotState;
static bool g_stateValid = false;

// Diana机器人API错误回调函数
void errorCallback(int errorCode, const char* strIpAddress) {
    g_lastErrorMessage = "Robot error: " + std::to_string(errorCode) +
                        " (" + std::string(formatError(errorCode)) + ") at IP: " +
                        std::string(strIpAddress);
    std::cerr << g_lastErrorMessage << std::endl;
}

// Diana机器人API状态回调函数
void stateCallback(StrRobotStateInfo* pinfo, const char* strIpAddress) {
    if (pinfo != nullptr) {
        g_lastRobotState = *pinfo;
        g_stateValid = true;
    }
}

// 构造函数：初始化驱动器
agilerobotDriver::agilerobotDriver()
    : _isConnected(false)
{
    // 初始化关节速度向量
    _previousJointVelocity.resize(MAX_JOINT_COUNT, 0.0);
    _currentJointVelocity.resize(MAX_JOINT_COUNT, 0.0);

    // 创建消息处理器
    _messageHandler = std::make_shared<Fuxi::Common::Executor>(1);
    _messageReceiver = std::make_shared<Fuxi::Common::Executor>(1);

    // 清除全局错误状态
    g_lastErrorMessage.clear();
    g_stateValid = false;
}

// 析构函数：确保正确断开连接
agilerobotDriver::~agilerobotDriver() {
    if (_isConnected) {
        disconnect();
    }
}

// 连接到Diana机器人
bool agilerobotDriver::connect(std::string ipAddress) {
    // 检查是否已连接
    if (_isConnected) {
        std::cout << "Already connected to robot at " << _ipAddress << std::endl;
        return true;
    }

    // 验证IP地址
    if (ipAddress.empty()) {
        std::cerr << "Error: IP address cannot be empty" << std::endl;
        return false;
    }

    _ipAddress = ipAddress;

    // 初始化网络配置
    srv_net_st netInfo;
    initSrvNetInfo(&netInfo);

    // 设置机器人IP地址
    if (ipAddress.length() >= sizeof(netInfo.SrvIp)) {
        std::cerr << "Error: IP address too long" << std::endl;
        return false;
    }
    std::strcpy(netInfo.SrvIp, ipAddress.c_str());

    g_lastErrorMessage.clear();

    // 初始化服务器连接
    int result = initSrv(errorCallback, stateCallback, &netInfo);

    if (result == NO_ERROR_CODE) {
        _isConnected = true;
        std::cout << "Successfully connected to robot at " << ipAddress << std::endl;

        // 等待初始状态更新
        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        return true;
    }

    // 连接失败
    std::cerr << "Failed to connect to robot at " << ipAddress
              << ": " << formatError(result) << std::endl;
    _ipAddress.clear();
    return false;
}

// 断开与Diana机器人的连接
bool agilerobotDriver::disconnect() {
    if (!_isConnected) {
        std::cout << "Robot is not connected" << std::endl;
        return true;
    }

    // 断开前停止运动
    stop();

    // 销毁服务器连接
    int result = destroySrv(_ipAddress.c_str());
    if (result == NO_ERROR_CODE) {
        _isConnected = false;
        std::cout << "Successfully disconnected from robot at " << _ipAddress << std::endl;
        _ipAddress.clear();
        g_stateValid = false;
        return true;
    }

    std::cerr << "Failed to disconnect from robot: " << formatError(result) << std::endl;
    return false;
}

// 立即停止所有机器人运动
bool agilerobotDriver::stop() {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    int result = ::stop(_ipAddress.c_str());
    if (result == NO_ERROR_CODE) {
        std::cout << "Robot motion stopped successfully" << std::endl;
        return true;
    }

    std::cerr << "Failed to stop robot motion: " << formatError(result) << std::endl;
    return false;
}

// 检查机器人是否已连接
bool agilerobotDriver::isConnect() {
    return _isConnected;
}

// 移动机器人关节到目标位置
bool agilerobotDriver::movej(rw::math::Q q, double speed, int zone, double acc) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    // 验证输入参数
    if (q.size() > MAX_JOINT_COUNT) {
        std::cerr << "Error: Too many joints specified. Maximum is " << MAX_JOINT_COUNT << std::endl;
        return false;
    }

    // 验证速度范围
    if (speed < 0.0 || speed > 10.0) {
        std::cerr << "Error: Speed must be between 0.0 and 10.0 rad/s" << std::endl;
        return false;
    }

    // 验证加速度范围
    if (acc < 0.0 || acc > 50.0) {
        std::cerr << "Error: Acceleration must be between 0.0 and 50.0 rad/s²" << std::endl;
        return false;
    }

    // 检查关节角度范围
    const double MAX_JOINT_ANGLE = 2 * 3.14159265359; // ±360度
    for (size_t i = 0; i < q.size(); ++i) {
        if (std::abs(q[i]) > MAX_JOINT_ANGLE) {
            std::cerr << "Warning: Joint " << i << " angle " << q[i]
                      << " rad may be out of range" << std::endl;
        }
    }

    // 转换Q到double数组
    double joints[MAX_JOINT_COUNT] = {0};
    for (size_t i = 0; i < q.size() && i < MAX_JOINT_COUNT; ++i) {
        joints[i] = q[i];
    }

    // 调用moveJToTarget函数
    int result = moveJToTarget(joints, speed, acc, 0, 0, 0, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "moveJToTarget failed: " << formatError(result) << std::endl;
        return false;
    }

    std::cout << "Joint movement command sent successfully (speed: " << speed
              << " rad/s, acc: " << acc << " rad/s²)" << std::endl;
    return waitForMotionComplete();
}

// 使用直线运动移动机器人TCP到目标位姿
bool agilerobotDriver::movel(rw::math::Transform3D<> poseTarget, double speed, int zone, double acc) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    // 验证速度范围
    if (speed < 0.0 || speed > 5.0) {
        std::cerr << "Error: Speed must be between 0.0 and 5.0 m/s" << std::endl;
        return false;
    }

    // 验证加速度范围
    if (acc < 0.0 || acc > 20.0) {
        std::cerr << "Error: Acceleration must be between 0.0 and 20.0 m/s²" << std::endl;
        return false;
    }

    // 转换Transform3D到位姿数组（轴角表示）
    double pose[6] = {0};

    // 提取位置
    auto position = poseTarget.P();
    pose[0] = position[0];
    pose[1] = position[1];
    pose[2] = position[2];

    // 验证位置值
    const double MAX_POSITION = 10.0; // 10米工作空间
    for (int i = 0; i < 3; ++i) {
        if (std::abs(pose[i]) > MAX_POSITION) {
            std::cerr << "Warning: Position component " << i << " (" << pose[i]
                      << ") may be out of workspace" << std::endl;
        }
    }

    // 提取旋转并转换为轴角表示
    auto rotation = poseTarget.R();

    // 计算旋转角度
    double trace = rotation(0,0) + rotation(1,1) + rotation(2,2);

    // 限制trace范围避免数值错误
    trace = std::max(-1.0, std::min(3.0, trace));
    double angle = std::acos(std::max(-1.0, std::min(1.0, (trace - 1.0) / 2.0)));

    // 避免小角度除零
    if (std::abs(angle) < 1e-10) {
        // 单位旋转
        pose[3] = 0.0;
        pose[4] = 0.0;
        pose[5] = 0.0;
    } else {
        // 计算旋转轴
        double sinAngle = std::sin(angle);
        if (std::abs(sinAngle) < 1e-10) {
            pose[3] = 0.0;
            pose[4] = 0.0;
            pose[5] = 0.0;
        } else {
            double x = (rotation(2,1) - rotation(1,2)) / (2.0 * sinAngle);
            double y = (rotation(0,2) - rotation(2,0)) / (2.0 * sinAngle);
            double z = (rotation(1,0) - rotation(0,1)) / (2.0 * sinAngle);

            // 归一化轴
            double norm = std::sqrt(x*x + y*y + z*z);
            if (norm > 1e-10) {
                x /= norm;
                y /= norm;
                z /= norm;
            }

            // 轴角表示
            pose[3] = x * angle;
            pose[4] = y * angle;
            pose[5] = z * angle;
        }
    }

    // 调用moveLToPose函数
    int result = moveLToPose(pose, speed, acc, nullptr, 0, 0, 0, false, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "moveLToPose failed: " << formatError(result) << std::endl;
        return false;
    }

    std::cout << "Linear movement command sent successfully (speed: " << speed
              << " m/s, acc: " << acc << " m/s²)" << std::endl;
    return waitForMotionComplete();
}

// 设置数字输出端口值
bool agilerobotDriver::setDigitalOutput(int portIndex, bool value, bool reset) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    // 验证端口索引
    if (portIndex < 0 || portIndex >= 32) {
        std::cerr << "Error: Digital output port index " << portIndex
                  << " is out of range (0-31)" << std::endl;
        return false;
    }

    // 使用writeDO函数设置数字输出
    const char* groupName = "default";
    char portName[32];
    snprintf(portName, sizeof(portName), "DO%d", portIndex);

    int result = writeDO(groupName, portName, value ? 1 : 0, _ipAddress.c_str());

    if (result == NO_ERROR_CODE) {
        std::cout << "Digital output " << portIndex << " set to "
                  << (value ? "HIGH" : "LOW") << std::endl;
        return true;
    }

    std::cerr << "Failed to set digital output " << portIndex
              << ": " << formatError(result) << std::endl;
    return false;
}

// 获取数字输入端口值
int agilerobotDriver::getDigitalInput(int ioNum) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return -1;
    }

    // 验证端口号
    if (ioNum < 0 || ioNum >= 32) {
        std::cerr << "Error: Digital input port number " << ioNum
                  << " is out of range (0-31)" << std::endl;
        return -1;
    }

    // 使用readDI函数获取数字输入
    const char* groupName = "default";
    char portName[32];
    snprintf(portName, sizeof(portName), "DI%d", ioNum);

    int value = 0;
    int result = readDI(groupName, portName, value, _ipAddress.c_str());

    if (result == NO_ERROR_CODE) {
        std::cout << "Digital input " << ioNum << " value: "
                  << (value ? "HIGH" : "LOW") << std::endl;
        return value;
    }

    std::cerr << "Failed to read digital input " << ioNum
              << ": " << formatError(result) << std::endl;
    return -1;
}

// 设置机器人加速度参数（Diana API按运动命令处理加速度）
bool agilerobotDriver::setAcceleration(double acc, double ramp) {
    // Diana API按运动命令处理加速度，此函数保持兼容性
    std::cout << "Note: Diana robot handles acceleration per motion command" << std::endl;
    return true;
}

// 获取当前TCP位置和姿态
rw::math::Transform3D<> agilerobotDriver::getCurrentTCP() {
    rw::math::Transform3D<> current;

    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return current;
    }

    double pose[6] = {0};
    int result = getTcpPos(pose, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "getTcpPos failed: " << formatError(result) << std::endl;
        return current;
    }

    // 转换位姿数组到Transform3D
    rw::math::Vector3D<> position(pose[0], pose[1], pose[2]);

    // 转换轴角表示到旋转矩阵
    rw::math::Rotation3D<> rotation;

    // 从后三个分量提取轴和角度
    double rx = pose[3];
    double ry = pose[4];
    double rz = pose[5];

    // 计算角度（轴角向量的模长）
    double angle = std::sqrt(rx*rx + ry*ry + rz*rz);

    if (angle < 1e-10) {
        // 小角度时为单位旋转
        rotation = rw::math::Rotation3D<>::identity();
    } else {
        // 归一化轴
        double x = rx / angle;
        double y = ry / angle;
        double z = rz / angle;

        // 使用Rodrigues公式计算旋转矩阵
        double c = std::cos(angle);
        double s = std::sin(angle);
        double t = 1.0 - c;

        // 构建旋转矩阵
        double r11 = t*x*x + c;
        double r12 = t*x*y - z*s;
        double r13 = t*x*z + y*s;

        double r21 = t*x*y + z*s;
        double r22 = t*y*y + c;
        double r23 = t*y*z - x*s;

        double r31 = t*x*z - y*s;
        double r32 = t*y*z + x*s;
        double r33 = t*z*z + c;

        rotation(0,0) = r11; rotation(0,1) = r12; rotation(0,2) = r13;
        rotation(1,0) = r21; rotation(1,1) = r22; rotation(1,2) = r23;
        rotation(2,0) = r31; rotation(2,1) = r32; rotation(2,2) = r33;
    }

    current = rw::math::Transform3D<>(position, rotation);
    return current;
}

// 获取当前关节位置
rw::math::Q agilerobotDriver::getCurrentJointPositions() {
    rw::math::Q jointPositions(MAX_JOINT_COUNT);

    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        // 返回零值Q
        for (int i = 0; i < MAX_JOINT_COUNT; ++i) {
            jointPositions[i] = 0.0;
        }
        return jointPositions;
    }

    double joints[MAX_JOINT_COUNT] = {0};
    int result = getJointPos(joints, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "getJointPos failed: " << formatError(result) << std::endl;
        // 错误时返回零值Q
        for (int i = 0; i < MAX_JOINT_COUNT; ++i) {
            jointPositions[i] = 0.0;
        }
        return jointPositions;
    }

    // 复制关节值到Q对象
    for (int i = 0; i < MAX_JOINT_COUNT; ++i) {
        jointPositions[i] = joints[i];
    }

    return jointPositions;
}

// 计算给定关节位置的正向运动学
bool agilerobotDriver::getForwardKinematics(rw::math::Q joint, rw::math::Transform3D<> &position) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    // 验证输入关节数量
    if (joint.size() > MAX_JOINT_COUNT) {
        std::cerr << "Error: Too many joints specified. Maximum is " << MAX_JOINT_COUNT << std::endl;
        return false;
    }

    // 转换Q到double数组
    double joints[MAX_JOINT_COUNT] = {0};
    for (size_t i = 0; i < joint.size() && i < MAX_JOINT_COUNT; ++i) {
        joints[i] = joint[i];
    }

    double pose[6] = {0};
    int result = forward(joints, pose, nullptr, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "Forward kinematics failed: " << formatError(result) << std::endl;
        return false;
    }

    // 转换位姿数组到Transform3D
    rw::math::Vector3D<> pos(pose[0], pose[1], pose[2]);

    // 转换轴角到旋转矩阵
    rw::math::Rotation3D<> rot;

    // 从后三个分量提取轴和角度
    double rx = pose[3];
    double ry = pose[4];
    double rz = pose[5];

    // 计算角度（轴角向量的模长）
    double angle = std::sqrt(rx*rx + ry*ry + rz*rz);

    if (angle < 1e-10) {
        // 小角度时为单位旋转
        rot = rw::math::Rotation3D<>::identity();
    } else {
        // 归一化轴
        double x = rx / angle;
        double y = ry / angle;
        double z = rz / angle;

        // 使用Rodrigues公式计算旋转矩阵
        double c = std::cos(angle);
        double s = std::sin(angle);
        double t = 1.0 - c;

        // 构建旋转矩阵
        double r11 = t*x*x + c;
        double r12 = t*x*y - z*s;
        double r13 = t*x*z + y*s;

        double r21 = t*x*y + z*s;
        double r22 = t*y*y + c;
        double r23 = t*y*z - x*s;

        double r31 = t*x*z - y*s;
        double r32 = t*y*z + x*s;
        double r33 = t*z*z + c;

        rot(0,0) = r11; rot(0,1) = r12; rot(0,2) = r13;
        rot(1,0) = r21; rot(1,1) = r22; rot(1,2) = r23;
        rot(2,0) = r31; rot(2,1) = r32; rot(2,2) = r33;
    }

    position = rw::math::Transform3D<>(pos, rot);
    return true;
}

// 计算给定目标位姿的逆向运动学
bool agilerobotDriver::getInverseKinematics(rw::math::Q currentJoint, rw::math::Transform3D<> targetPosition, rw::math::Q &resultJoint) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    // 验证输入关节数量
    if (currentJoint.size() > MAX_JOINT_COUNT) {
        std::cerr << "Error: Too many joints specified. Maximum is " << MAX_JOINT_COUNT << std::endl;
        return false;
    }

    // 转换当前Q到double数组作为参考
    double currentJoints[MAX_JOINT_COUNT] = {0};
    for (size_t i = 0; i < currentJoint.size() && i < MAX_JOINT_COUNT; ++i) {
        currentJoints[i] = currentJoint[i];
    }

    // 转换Transform3D到位姿数组（轴角表示）
    double targetPose[6] = {0};

    // 提取位置
    auto position = targetPosition.P();
    targetPose[0] = position[0];
    targetPose[1] = position[1];
    targetPose[2] = position[2];

    // 验证位置值
    const double MAX_POSITION = 10.0; // 10米工作空间
    for (int i = 0; i < 3; ++i) {
        if (std::abs(targetPose[i]) > MAX_POSITION) {
            std::cerr << "Warning: Target position component " << i << " (" << targetPose[i]
                      << ") may be out of workspace" << std::endl;
        }
    }

    // 提取旋转并转换为轴角表示
    auto rotation = targetPosition.R();

    // 计算旋转角度
    double trace = rotation(0,0) + rotation(1,1) + rotation(2,2);

    // 限制trace范围避免数值错误
    trace = std::max(-1.0, std::min(3.0, trace));
    double angle = std::acos(std::max(-1.0, std::min(1.0, (trace - 1.0) / 2.0)));

    // 避免小角度除零
    if (std::abs(angle) < 1e-10) {
        // 单位旋转
        targetPose[3] = 0.0;
        targetPose[4] = 0.0;
        targetPose[5] = 0.0;
    } else {
        // 计算旋转轴
        double sinAngle = std::sin(angle);
        if (std::abs(sinAngle) < 1e-10) {
            targetPose[3] = 0.0;
            targetPose[4] = 0.0;
            targetPose[5] = 0.0;
        } else {
            double x = (rotation(2,1) - rotation(1,2)) / (2.0 * sinAngle);
            double y = (rotation(0,2) - rotation(2,0)) / (2.0 * sinAngle);
            double z = (rotation(1,0) - rotation(0,1)) / (2.0 * sinAngle);

            // 归一化轴
            double norm = std::sqrt(x*x + y*y + z*z);
            if (norm > 1e-10) {
                x /= norm;
                y /= norm;
                z /= norm;
            }

            // 轴角表示
            targetPose[3] = x * angle;
            targetPose[4] = y * angle;
            targetPose[5] = z * angle;
        }
    }

    // 准备结果数组
    double resultJoints[MAX_JOINT_COUNT] = {0};

    // 调用逆向运动学
    int result = inverse(targetPose, resultJoints, 0, nullptr, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "Inverse kinematics failed: " << formatError(result) << std::endl;
        return false;
    }

    // 转换结果到Q
    resultJoint = rw::math::Q(MAX_JOINT_COUNT);
    for (int i = 0; i < MAX_JOINT_COUNT; ++i) {
        resultJoint[i] = resultJoints[i];
    }

    std::cout << "Inverse kinematics calculation successful" << std::endl;
    return true;
}

// 启动机器人示教模式
bool agilerobotDriver::startTeachMode(Teach_Mode mode) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    int result = NO_ERROR_CODE;

    switch (mode) {
        case JOINT_TEACH:
            std::cout << "Starting joint teach mode..." << std::endl;
            result = moveJoint(_ipAddress.c_str());
            if (result == NO_ERROR_CODE) {
                std::cout << "Joint teach mode started successfully" << std::endl;
            } else {
                std::cerr << "Failed to start joint teach mode: " << formatError(result) << std::endl;
            }
            break;

        case TCP_TEACH:
            std::cout << "Starting TCP teach mode..." << std::endl;
            result = moveTCP(_ipAddress.c_str());
            if (result == NO_ERROR_CODE) {
                std::cout << "TCP teach mode started successfully" << std::endl;
            } else {
                std::cerr << "Failed to start TCP teach mode: " << formatError(result) << std::endl;
            }
            break;

        default:
            std::cerr << "Error: Invalid teach mode specified (" << static_cast<int>(mode) << ")" << std::endl;
            return false;
    }

    return result == NO_ERROR_CODE;
}

// 停止示教模式并返回正常操作
bool agilerobotDriver::stopTeachMode() {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    std::cout << "Stopping teach mode..." << std::endl;
    int result = ::stop(_ipAddress.c_str());

    if (result == NO_ERROR_CODE) {
        std::cout << "Teach mode stopped successfully" << std::endl;
        return true;
    } else {
        std::cerr << "Failed to stop teach mode: " << formatError(result) << std::endl;
        return false;
    }
}

// 获取机器人系统的最后错误消息
std::string agilerobotDriver::getErrorMessage() {
    // 首先检查是否有来自回调的缓存错误消息
    if (!g_lastErrorMessage.empty()) {
        std::string errorMsg = g_lastErrorMessage;
        // 获取后清除缓存消息
        g_lastErrorMessage.clear();
        return errorMsg;
    }

    // 如果没有缓存错误，尝试从API获取最后错误
    if (!_isConnected) {
        return "Robot is not connected";
    }

    try {
        int lastError = getLastError(_ipAddress.c_str());
        if (lastError != NO_ERROR_CODE) {
            std::string errorMsg = formatError(lastError);
            std::cout << "Retrieved error from robot: " << errorMsg << std::endl;
            return errorMsg;
        }
    } catch (...) {
        return "Failed to retrieve error information from robot";
    }

    return ""; // 无错误
}

// 通过监控关节速度等待机器人运动完成
bool agilerobotDriver::waitForMotionComplete(int timeout_ms) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    const int CHECK_INTERVAL_MS = 100; // 每100ms检查一次
    const double VELOCITY_THRESHOLD = 0.001; // 所有关节速度<0.001rad/s时认为运动停止
    int elapsed_ms = 0;

    std::cout << "Waiting for motion to complete (timeout: " << timeout_ms << "ms)..." << std::endl;

    while (elapsed_ms < timeout_ms) {
        // 获取当前关节速度
        double jointVelocities[MAX_JOINT_COUNT] = {0};
        int result = getJointAngularVel(jointVelocities, _ipAddress.c_str());

        if (result != NO_ERROR_CODE) {
            std::cerr << "Warning: Failed to get joint velocities: " << formatError(result) << std::endl;
            // 继续检查 - 这可能是临时通信问题
        } else {
            // 检查所有关节速度是否低于阈值
            bool motionStopped = true;
            for (int i = 0; i < MAX_JOINT_COUNT; ++i) {
                if (std::abs(jointVelocities[i]) > VELOCITY_THRESHOLD) {
                    motionStopped = false;
                    break;
                }
            }

            if (motionStopped) {
                std::cout << "Motion completed after " << elapsed_ms << "ms" << std::endl;
                return true;
            }
        }

        // 休眠检查间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(CHECK_INTERVAL_MS));
        elapsed_ms += CHECK_INTERVAL_MS;

        // 每5秒打印进度
        if (elapsed_ms % 5000 == 0) {
            std::cout << "Still waiting for motion to complete... (" << elapsed_ms << "ms elapsed)" << std::endl;
        }
    }

    std::cerr << "Warning: Motion did not complete within timeout (" << timeout_ms << "ms)" << std::endl;
    return false;
}

// 检查机器人是否在容差范围内到达目标关节位置
bool agilerobotDriver::checkJointMotion(const rw::math::Q& targetJoints, double tolerance) {
    if (!_isConnected) {
        std::cerr << "Error: Robot is not connected" << std::endl;
        return false;
    }

    if (targetJoints.size() > MAX_JOINT_COUNT) {
        std::cerr << "Error: Too many target joints specified. Maximum is " << MAX_JOINT_COUNT << std::endl;
        return false;
    }

    if (tolerance <= 0.0) {
        std::cerr << "Error: Tolerance must be positive" << std::endl;
        return false;
    }

    // 获取当前关节位置
    double currentJoints[MAX_JOINT_COUNT] = {0};
    int result = getJointPos(currentJoints, _ipAddress.c_str());

    if (result != NO_ERROR_CODE) {
        std::cerr << "Error: Failed to get current joint positions: " << formatError(result) << std::endl;
        return false;
    }

    // 检查每个关节是否在容差范围内
    bool allJointsInPosition = true;
    for (size_t i = 0; i < targetJoints.size() && i < MAX_JOINT_COUNT; ++i) {
        double error = std::abs(currentJoints[i] - targetJoints[i]);
        if (error > tolerance) {
            std::cout << "Joint " << i << " not in position: current=" << currentJoints[i]
                      << ", target=" << targetJoints[i] << ", error=" << error
                      << " (tolerance=" << tolerance << ")" << std::endl;
            allJointsInPosition = false;
        }
    }

    if (allJointsInPosition) {
        std::cout << "All joints are within tolerance of target positions" << std::endl;
    }

    return allJointsInPosition;
}
