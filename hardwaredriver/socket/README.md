# Socket通信库

这是一个基于Windows标准库实现的Socket通信库，专为Windows 11优化。该库提供了简单易用的Socket编程接口，可用于实现客户端和服务器端通信。

## 特性

- 仅使用Windows标准库实现
- 支持TCP客户端和服务器
- 支持阻塞和非阻塞操作
- 支持超时设置
- 异常处理机制
- 简单易用的API

## 编译方法

使用CMake编译:

```bash
mkdir build
cd build
cmake ..
cmake --build .
```

## 使用示例

### 服务器端示例

```cpp
// 创建服务器Socket
Socket serverSocket;

// 允许地址重用
serverSocket.setReuseAddr(true);

// 绑定到本地8888端口
serverSocket.bind("0.0.0.0", 8888);

// 开始监听连接
serverSocket.listen();

// 接受客户端连接
Socket clientSocket = serverSocket.accept();

// 接收数据
std::string message = clientSocket.receiveString();

// 发送数据
clientSocket.send("Hello, client!");

// 关闭连接
clientSocket.close();
serverSocket.close();
```

### 客户端示例

```cpp
// 创建客户端Socket
Socket clientSocket;

// 连接到服务器
clientSocket.connect("127.0.0.1", 8888);

// 发送数据
clientSocket.send("Hello, server!");

// 接收数据
std::string response = clientSocket.receiveString();

// 关闭连接
clientSocket.close();
```

### 非阻塞服务器示例

参考 `examples/multi_client_server.cpp` 获取完整示例。

## API参考

### 构造函数和析构函数

- `Socket()` - 创建新的Socket对象
- `~Socket()` - 释放Socket资源

### 服务器方法

- `void bind(const std::string& address, int port)` - 绑定到指定地址和端口
- `void listen(int backlog = SOMAXCONN)` - 开始监听连接
- `Socket accept()` - 接受客户端连接

### 客户端方法

- `void connect(const std::string& address, int port)` - 连接到服务器

### 通用方法

- `void close()` - 关闭Socket连接
- `int send(const char* data, int length)` - 发送二进制数据
- `int send(const std::string& data)` - 发送字符串数据
- `int receive(char* buffer, int length)` - 接收二进制数据
- `std::string receiveString(int maxLength = 4096)` - 接收字符串数据

### 设置选项

- `void setReuseAddr(bool reuse)` - 设置地址重用
- `void setBlocking(bool blocking)` - 设置阻塞/非阻塞模式
- `void setTimeout(int seconds)` - 设置超时时间

### 状态查询

- `bool isConnected() const` - 检查是否已连接
- `bool isListening() const` - 检查是否正在监听
- `bool isInitialized() const` - 检查是否已初始化

## 注意事项

- 该库仅支持Windows平台
- 使用前请确保已安装Windows开发工具包
- 非阻塞模式下需要正确处理异常 