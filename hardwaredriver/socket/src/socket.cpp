//
// Created by Administrator on 2025/4/11.
//

#include "socket.h"
#include <iostream>

// 构造函数 - 初始化Socket
Socket::Socket() : socketHandle(INVALID_SOCKET), initialized(false), connected(false), listening(false) {
    initializeWinsock();
    
    // 创建socket
    socketHandle = ::socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (socketHandle == INVALID_SOCKET) {
        throw SocketException("创建socket失败，错误码: " + std::to_string(WSAGetLastError()));
    }
    
    initialized = true;
}

// 析构函数 - 确保资源被释放
Socket::~Socket() {
    close();
    
    // 清理Winsock
    WSACleanup();
}

// 初始化Winsock库
void Socket::initializeWinsock() {
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        throw SocketException("WSAStartup失败，错误码: " + std::to_string(result));
    }
}

// 服务器端: 绑定地址和端口
void Socket::bind(const std::string& address, int port) {
    if (!initialized) {
        throw SocketException("Socket未初始化");
    }
    
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(static_cast<u_short>(port));
    
    // 将IP地址从字符串转换为网络字节序
    if (address.empty() || address == "0.0.0.0") {
        serverAddr.sin_addr.s_addr = INADDR_ANY;
    } else {
        inet_pton(AF_INET, address.c_str(), &(serverAddr.sin_addr));
    }
    
    // 绑定地址和端口
    int result = ::bind(socketHandle, reinterpret_cast<sockaddr*>(&serverAddr), sizeof(serverAddr));
    if (result == SOCKET_ERROR) {
        throw SocketException("绑定地址和端口失败，错误码: " + std::to_string(WSAGetLastError()));
    }
}

// 服务器端: 开始监听连接
void Socket::listen(int backlog) {
    if (!initialized) {
        throw SocketException("Socket未初始化");
    }
    
    int result = ::listen(socketHandle, backlog);
    if (result == SOCKET_ERROR) {
        throw SocketException("监听失败，错误码: " + std::to_string(WSAGetLastError()));
    }
    
    listening = true;
}

// 服务器端: 接受客户端连接
Socket Socket::accept() {
    if (!listening) {
        throw SocketException("Socket未处于监听状态");
    }
    
    // Create a new socket for the client connection
    Socket clientSocket;
    
    // Accept connection and get socket descriptor
    struct sockaddr_in clientAddr;
    socklen_t clientAddrLen = sizeof(clientAddr);
    
    SOCKET clientFd = ::accept(socketHandle, (struct sockaddr*)&clientAddr, &clientAddrLen);
    
    if (clientFd == INVALID_SOCKET)
    {
        throw SocketException("Accept failed");
    }
    
    // Set the socket descriptor in the client socket and return it using move semantics
    clientSocket.socketHandle = clientFd;
    clientSocket.connected = true;
    
    return std::move(clientSocket);  // Explicitly use std::move for clarity
}

// 客户端: 连接到服务器
void Socket::connect(const std::string& address, int port) {
    if (!initialized) {
        throw SocketException("Socket未初始化");
    }
    
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_port = htons(static_cast<u_short>(port));
    
    // 将IP地址从字符串转换为网络字节序
    inet_pton(AF_INET, address.c_str(), &(serverAddr.sin_addr));
    
    // 连接到服务器
    int result = ::connect(socketHandle, reinterpret_cast<sockaddr*>(&serverAddr), sizeof(serverAddr));
    if (result == SOCKET_ERROR) {
        throw SocketException("连接服务器失败，错误码: " + std::to_string(WSAGetLastError()));
    }
    
    connected = true;
}

// 关闭Socket连接
void Socket::close() {
    if (initialized && socketHandle != INVALID_SOCKET) {
        closesocket(socketHandle);
        socketHandle = INVALID_SOCKET;
    }
    
    initialized = false;
    connected = false;
    listening = false;
}

// 发送数据
int Socket::send(const char* data, int length) {
    if (!connected) {
        throw SocketException("Socket未连接");
    }
    
    int bytesSent = ::send(socketHandle, data, length, 0);
    if (bytesSent == SOCKET_ERROR) {
        throw SocketException("发送数据失败，错误码: " + std::to_string(WSAGetLastError()));
    }
    
    return bytesSent;
}

// 发送字符串数据
int Socket::send(const std::string& data) {
    return send(data.c_str(), static_cast<int>(data.length()));
}

// 接收数据
int Socket::receive(char* buffer, int length) {
    if (!connected) {
        throw SocketException("Socket未连接");
    }
    
    int bytesReceived = recv(socketHandle, buffer, length, 0);
    if (bytesReceived == SOCKET_ERROR) {
        throw SocketException("接收数据失败，错误码: " + std::to_string(WSAGetLastError()));
    }
    
    return bytesReceived;
}

// 接收字符串数据
std::string Socket::receiveString(int maxLength) {
    char* buffer = new char[maxLength + 1];
    memset(buffer, 0, maxLength + 1);
    
    int bytesReceived = receive(buffer, maxLength);
    std::string result(buffer, bytesReceived);
    
    delete[] buffer;
    return result;
}

// 设置地址重用选项
void Socket::setReuseAddr(bool reuse) {
    if (!initialized) {
        throw SocketException("Socket未初始化");
    }
    
    int optval = reuse ? 1 : 0;
    int result = setsockopt(socketHandle, SOL_SOCKET, SO_REUSEADDR, 
                            reinterpret_cast<const char*>(&optval), sizeof(optval));
    
    if (result == SOCKET_ERROR) {
        throw SocketException("设置SO_REUSEADDR选项失败，错误码: " + std::to_string(WSAGetLastError()));
    }
}

// 设置非阻塞模式
void Socket::setBlocking(bool blocking) {
    if (!initialized) {
        throw SocketException("Socket未初始化");
    }
    
    u_long mode = blocking ? 0 : 1;
    int result = ioctlsocket(socketHandle, FIONBIO, &mode);
    
    if (result == SOCKET_ERROR) {
        throw SocketException("设置阻塞模式失败，错误码: " + std::to_string(WSAGetLastError()));
    }
}

// 设置超时时间
void Socket::setTimeout(int seconds) {
    if (!initialized) {
        throw SocketException("Socket未初始化");
    }
    
    DWORD timeout = seconds * 1000; // 转换为毫秒
    
    // 设置接收超时
    int result = setsockopt(socketHandle, SOL_SOCKET, SO_RCVTIMEO, 
                           reinterpret_cast<const char*>(&timeout), sizeof(timeout));
    if (result == SOCKET_ERROR) {
        throw SocketException("设置接收超时失败，错误码: " + std::to_string(WSAGetLastError()));
    }
    
    // 设置发送超时
    result = setsockopt(socketHandle, SOL_SOCKET, SO_SNDTIMEO, 
                       reinterpret_cast<const char*>(&timeout), sizeof(timeout));
    if (result == SOCKET_ERROR) {
        throw SocketException("设置发送超时失败，错误码: " + std::to_string(WSAGetLastError()));
    }
}

// 状态查询方法
bool Socket::isConnected() const {
    return connected;
}

bool Socket::isListening() const {
    return listening;
}

bool Socket::isInitialized() const {
    return initialized;
}

// Implement move constructor
Socket::Socket(Socket&& other) noexcept 
    : socketHandle(other.socketHandle) // Assuming socketHandle is the member variable for the socket descriptor
{
    // Transfer ownership of the socket descriptor
    other.socketHandle = INVALID_SOCKET; // Use appropriate invalid value for your platform
}

// Implement move assignment operator
Socket& Socket::operator=(Socket&& other) noexcept
{
    if (this != &other)
    {
        // Close current socket if open
        if (socketHandle != INVALID_SOCKET)
        {
            close();
        }
        
        // Transfer ownership
        socketHandle = other.socketHandle;
        other.socketHandle = INVALID_SOCKET;
    }
    return *this;
}
