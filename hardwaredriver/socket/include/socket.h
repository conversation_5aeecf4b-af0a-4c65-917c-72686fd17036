//
// Created by Administrator on 2025/4/11.
//

#ifndef SOCKET_H
#define SOCKET_H

#include <winsock2.h>
#include <ws2tcpip.h>
#include <string>
#include <stdexcept>

#pragma comment(lib, "ws2_32.lib")

class SocketException : public std::runtime_error {
public:
    explicit SocketException(const std::string& message) : std::runtime_error(message) {}
};

class Socket {
private:
    SOCKET socketHandle;
    bool initialized;
    bool connected;
    bool listening;
    
    // 初始化Winsock
    void initializeWinsock();
    
public:
    // 构造和析构函数
    Socket();
    ~Socket();
    
    // Delete copy constructor and assignment operator
    Socket(const Socket&) = delete;
    Socket& operator=(const Socket&) = delete;
    
    // Add move constructor and assignment operator
    Socket(Socket&& other) noexcept;
    Socket& operator=(Socket&& other) noexcept;
    
    // 服务器端方法
    void bind(const std::string& address, int port);
    void listen(int backlog = SOMAXCONN);
    Socket accept();
    
    // 客户端方法
    void connect(const std::string& address, int port);
    
    // 通用方法
    void close();
    int send(const char* data, int length);
    int send(const std::string& data);
    int receive(char* buffer, int length);
    std::string receiveString(int maxLength = 4096);
    
    // 设置选项
    void setReuseAddr(bool reuse);
    void setBlocking(bool blocking);
    void setTimeout(int seconds);
    
    // 状态查询
    bool isConnected() const;
    bool isListening() const;
    bool isInitialized() const;
};

#endif //SOCKET_H
