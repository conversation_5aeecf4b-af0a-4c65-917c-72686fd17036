//
// Created by cobot on 2020/10/7.
//

#include "AbbRwsWrapper.h"


Sensor::Robot::ABB::AbbRwsWrapper::AbbRwsWrapper(const std::string ip, int port, const std::string username,
                                                 const std::string password) : _ip(ip), _port(port),
                                                                               _username(username),
                                                                               _password(password) {
    _interface = std::make_shared<abb::rws::RWSInterface>(_ip, port, username, password);

}

rw::math::Q Sensor::Robot::ABB::AbbRwsWrapper::getQ(std::string mechunit) {
        abb::rws::JointTarget current_jointtarget;
        _interface->getMechanicalUnitJointTarget(mechunit, &current_jointtarget);
        std::vector<double> qs;
        qs.push_back(current_jointtarget.robax.rax_1.value);
        qs.push_back(current_jointtarget.robax.rax_2.value);
        qs.push_back(current_jointtarget.robax.rax_3.value);
        qs.push_back(current_jointtarget.robax.rax_4.value);
        qs.push_back(current_jointtarget.robax.rax_5.value);
        qs.push_back(current_jointtarget.robax.rax_6.value);
        return qs;
}

rw::math::Transform3D<> Sensor::Robot::ABB::AbbRwsWrapper::getP(std::string mechunit) {
        abb::rws::RobTarget current_robtarget;
        _interface->getMechanicalUnitRobTarget("ROB_1", &current_robtarget);
        std::vector<float> qs;
        qs.push_back(current_robtarget.pos.x.value);
        qs.push_back(current_robtarget.pos.y.value);
        qs.push_back(current_robtarget.pos.z.value);
        qs.push_back(current_robtarget.orient.q1.value);
        qs.push_back(current_robtarget.orient.q2.value);
        qs.push_back(current_robtarget.orient.q3.value);
        qs.push_back(current_robtarget.orient.q4.value);
        rw::math::Quaternion<> quaternion(qs[4], qs[5], qs[6], qs[3]);
        rw::math::Rotation3D<> rot3D = quaternion.toRotation3D();
        return rw::math::Transform3D<>({qs[0] / 1000, qs[1] / 1000, qs[2] / 1000}, rot3D);
}


bool Sensor::Robot::ABB::AbbRwsWrapper::isRAPIDstate() {
        return _interface->isRAPIDRunning().isTrue();
}

bool Sensor::Robot::ABB::AbbRwsWrapper::startRAPIDExecution() {

        _interface->startRAPIDExecution();
        return _interface->isRAPIDRunning().isTrue();
}

bool Sensor::Robot::ABB::AbbRwsWrapper::resetRAPIDProgramPointer() {

        if (_interface->isRAPIDRunning().isTrue()) {
            return true;
        } else {
            _interface->resetRAPIDProgramPointer();
            return true;
        }
}

bool Sensor::Robot::ABB::AbbRwsWrapper::setMotorsOn() {

        if (_interface->isMotorsOn().isTrue()) {
            return true;
        } else {
            _interface->setMotorsOn();
            return _interface->isMotorsOn().isTrue();
        }
}

bool Sensor::Robot::ABB::AbbRwsWrapper::getDI(int ioNum) {

        return (_interface->getIOSignal("di" + std::to_string(ioNum)) == abb::rws::SystemConstants::IOSignals::HIGH)
               ? true
               : false;
}

bool Sensor::Robot::ABB::AbbRwsWrapper::getEMG() {

        return (_interface->getIOSignal("IEMG") == abb::rws::SystemConstants::IOSignals::HIGH) ? true
                                                                                               : false;
}

bool Sensor::Robot::ABB::AbbRwsWrapper::resetEMG() {

        return (_interface->setIOSignal("REMGSTOP", abb::rws::SystemConstants::IOSignals::HIGH)) ? true : false;

}

bool Sensor::Robot::ABB::AbbRwsWrapper::setDo(int ioNum, bool value) {

        std::string iovalue = (value) ? abb::rws::SystemConstants::IOSignals::HIGH
                                      : abb::rws::SystemConstants::IOSignals::LOW;
        return (_interface->setIOSignal("do" + std::to_string(ioNum), iovalue)) ? true : false;

}

bool Sensor::Robot::ABB::AbbRwsWrapper::stopRAPIDExecution() {

        _interface->stopRAPIDExecution();
        return _interface->isRAPIDRunning().isTrue();
}


