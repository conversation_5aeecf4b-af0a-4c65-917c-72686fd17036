#include "AbbRobotDriver.h"
#include <glog.h>
#include <QString>
#include <JSON.h>
#include <QtCore/QStringList>
#include <sstream>
#include <vector>
#include <string>
#include "stringAlgorithm.h"



namespace {
    const double M2MM = 1000;
}

AbbRobotDriverEgmUpdate::AbbRobotDriverEgmUpdate() {
    _moveTimeOut = 5;
    _wrapper = nullptr;
    _dataReceived = false;
}

AbbRobotDriverEgmUpdate::~AbbRobotDriverEgmUpdate() {
    stop();
    _stopReceiveThreads = true;

    if (_motionReceiveThread.joinable()) {
        _motionReceiveThread.join();
    }
    if (_loggerReceiveThread.joinable()) {
        _loggerReceiveThread.join();
    }

    if (_motionclientLogger) {
        _motionclientLogger->close();
    }
    if (_motionclient) {
        _motionclient->close();
    }
}


bool AbbRobotDriverEgmUpdate::connect(std::string ip, int port) {
    if (ip.empty()) {
        ip = _ip;
    }
    if (port == 0) {
        port = _port;
    }
    _ip = ip;
    _port = port;

    // 创建RWS连接
    try {
        _wrapper = std::make_shared<Sensor::Robot::ABB::AbbRwsWrapper>(ip);
        
        // 初始化返回消息
        _returnMsg = ReturnMsg();
        
        // 检查机器人状态
        if (_wrapper->getEMG()) {
            LOG(ERROR) << "Robot is in emergency stop state";
            return false;
        }

        // 设置电机
        if (!_wrapper->setMotorsOn()) {
            LOG(ERROR) << "Failed to turn on motors";
            return false;
        }

        // 重置程序指针
        if (!_wrapper->resetRAPIDProgramPointer()) {
            LOG(ERROR) << "Failed to reset RAPID program pointer";
            return false;
        }

        // 启动RAPID程序
        if (!_wrapper->startRAPIDExecution()) {
            LOG(ERROR) << "Failed to start RAPID execution";
            return false;
        }

        _message = std::make_shared<Fuxi::Common::Executor>(1);

        // 初始化 socket 客户端
        try {
            _motionclient = std::make_shared<Socket>();
            _motionclient->setTimeout(5);  // 设置5秒超时

            LOG(INFO) << "Connecting motion client to " << ip << ":" << (port + 1);
        } catch (const SocketException& e) {
            LOG(ERROR) << "Failed to create motion socket: " << e.what();
            return false;
        }

        // 初始化日志客户端
        try {
            _motionclientLogger = std::make_shared<Socket>();
            _motionclientLogger->setTimeout(5);  // 设置5秒超时

            LOG(INFO) << "Connecting logger client to " << ip << ":" << (port + 2);
        } catch (const SocketException& e) {
            LOG(ERROR) << "Failed to create logger socket: " << e.what();
            return false;
        }

        // 连接 socket 客户端
        try {
            _motionclient->connect(ip, port + 1);
            LOG(INFO) << "Motion client connected to robot";
        } catch (const SocketException& e) {
            LOG(ERROR) << "Failed to connect motion client: " << e.what();
            return false;
        }

        try {
            _motionclientLogger->connect(ip, port + 2);
            LOG(INFO) << "Logger client connected to robot";
        } catch (const SocketException& e) {
            LOG(ERROR) << "Failed to connect logger client: " << e.what();
            return false;
        }

        // 启动接收线程
        _stopReceiveThreads = false;
        _motionReceiveThread = std::thread(&AbbRobotDriverEgmUpdate::motionReceiveThreadFunc, this);
        _loggerReceiveThread = std::thread(&AbbRobotDriverEgmUpdate::loggerReceiveThreadFunc, this);

        LoadData();
        _connect = true;
        _returnMsg.connected = true;
        _returnMsg.State = 1;  // 设置为正常运行状态
        return true;
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Failed to connect to robot: " << e.what();
        _connect = false;
        _returnMsg.connected = false;
        _returnMsg.State = 3;  // 设置为错误状态
        return false;
    }
}

bool AbbRobotDriverEgmUpdate::stop() {

    return true;
}


bool AbbRobotDriverEgmUpdate::setAcceleration(int acc, int ramp) {
    _acc = acc;
    return true;
}


void AbbRobotDriverEgmUpdate::movej(rw::math::Q q, double tcpSpeed, int zone, int acc) {
    if (!_connect || isNearJointpoint(q, zone) || _mode == 0 || q.size() != 6)
        return;

    RobotMessage msg;
    msg.instructionCode = 2;  // MoveJ
    msg.params = {
        9.0,  // Move type (9 for joint move)
        (float)(q[0] * rw::math::Rad2Deg),
        (float)(q[1] * rw::math::Rad2Deg),
        (float)(q[2] * rw::math::Rad2Deg),
        (float)(q[3] * rw::math::Rad2Deg),
        (float)(q[4] * rw::math::Rad2Deg),
        (float)(q[5] * rw::math::Rad2Deg),
        0.0,  // External axis
        (float)tcpSpeed,
        (float)zone,
        0.0,  // Singular area
        0.0,  // Reserved
        (float)acc
    };
    msg.nParams = msg.params.size();
    
    if(sendRobotMessageWithRetry(msg)) {
        waitForMotionComplete(q, zone);
    }
}


bool AbbRobotDriverEgmUpdate::isNearJointpoint(rw::math::Q q, int zone) {

    auto joints = getRobotJointQ();
    LOG(INFO) << joints;
    LOG(INFO) << q;
    LOG(INFO) << " (joints - q).norm1() " << (joints - q).norm1();
    if ((joints - q).norm1() < zoneData[zone].ori * rw::math::Deg2Rad) {
        return true;
    }
    return false;
}

std::string toString(rw::math::Transform3D<> t) {
    auto p = t.P();
    rw::math::RPY<> rpy(t.R());
    std::string text = "";
    text.append(std::to_string(p(0))).append(" ");
    text.append(std::to_string(p(1))).append(" ");
    text.append(std::to_string(p(2))).append(" ");
    text.append(std::to_string(rpy(0))).append(" ");
    text.append(std::to_string(rpy(1))).append(" ");
    text.append(std::to_string(rpy(2))).append(" ");
    return text;
}

void AbbRobotDriverEgmUpdate::movel(rw::math::Transform3D<> poseTarget, double tcpSpeed, int zone, int acc) {
    if (!_connect || isNearPosepoint(poseTarget, zone) || _mode == 0)
        return;

    auto p = poseTarget.P();
    auto quaternion = rw::math::Quaternion<>(poseTarget.R());

    RobotMessage msg;
    msg.instructionCode = 1;  // MoveL
    msg.params = {
        1.0,  // Move type (1 for linear move)
        (float)(p[0] * M2MM),
        (float)(p[1] * M2MM), 
        (float)(p[2] * M2MM),
        (float)quaternion.getQw(),
        (float)quaternion.getQx(),
        (float)quaternion.getQy(),
        (float)quaternion.getQz(),
        0.0,  // External axis
        (float)tcpSpeed,
        (float)zone,
        0.0,  // Singular area
        0.0,  // Reserved
        (float)acc
    };
    msg.nParams = msg.params.size();
    
    if(sendRobotMessageWithRetry(msg)) {
        waitForMotionComplete(poseTarget, zone);
    }
}


bool AbbRobotDriverEgmUpdate::isNearPosepoint(rw::math::Transform3D<> poseTarget, int zone) {
    auto tcp = getRobotPoseData().pose;
    auto tmp_pnew = tcp.P();
    auto tmp_quaternionnew = rw::math::Quaternion<>(tcp.R());
    auto tmp_p = poseTarget.P();
    auto tmp_quaternion = rw::math::Quaternion<>(poseTarget.R());
    if (fabs(tmp_pnew[0] - tmp_p[0]) < zoneData[zone].p_tcp / 1000
        && fabs(tmp_pnew[1] - tmp_p[1]) < zoneData[zone].p_tcp / 1000
        && fabs(tmp_pnew[2] - tmp_p[2]) < zoneData[zone].p_tcp / 1000) {
        return true;
    }
    return false;
}

void AbbRobotDriverEgmUpdate::LoadData() {
    _message->postTimerTaskWithFixRateMs([&]() {
        if (!_connect || !_wrapper) {
            _returnMsg.connected = false;
            return;
        }
        
        try {
            // 检查RAPID执行状态
            if (!_wrapper->isRAPIDstate()) {
                LOG(WARNING) << "RAPID program is not running";
                _returnMsg.State = 0;  // 设置为未运行状态
                _returnMsg.connected = false;
                _connect = false;
                return;
            }

            // 检查急停状态
            if (_wrapper->getEMG()) {
                LOG(ERROR) << "Emergency stop activated";
                _returnMsg.State = 2;  // 设置为急停状态
                _returnMsg.connected = false;
                _connect = false;
                return;
            }

            // 更新机器人状态
            auto pose = _wrapper->getP();
            auto joints = _wrapper->getQ();
            
            // 更新返回消息
            _returnMsg.cartesian = pose;
            _returnMsg.joint = joints;
            _returnMsg.connected = true;
            _returnMsg.State = 1;  // 设置为正常运行状态
            _returnMsg.ok = 1;     // 设置为正常状态
        }
        catch (const std::exception& e) {
            LOG(ERROR) << "Exception in LoadData: " << e.what();
            _returnMsg.connected = false;
            _returnMsg.State = 3;  // 设置为错误状态
            _returnMsg.ok = 0;     // 设置为错误状态
            _connect = false;
        }
    }, 500);
}



rw::math::Transform3D<> AbbRobotDriverEgmUpdate::getRobotActualTCP() {
    if (!_connect || !_wrapper) return rw::math::Transform3D<>();
    return _wrapper->getP();
}


AbbRobotDriverEgmUpdate::RobotPosData AbbRobotDriverEgmUpdate::getRobotPoseData() {
    RobotPosData data;
    if (_connect && _wrapper) {
        data.joint = _wrapper->getQ();
        data.pose = _wrapper->getP();
    }
    return data;
}

rw::math::Q AbbRobotDriverEgmUpdate::getRobotJointQ() {
    if (!_connect || !_wrapper) return rw::math::Q(6, 0.0);
    return _wrapper->getQ();
}


void AbbRobotDriverEgmUpdate::setOutValue(int portIndex, bool val, bool reset) {
    if (!_connect || !_wrapper) return;
    _wrapper->setDo(portIndex, val);
}


int AbbRobotDriverEgmUpdate::getInputValue(int ioNum) {
    if (!_connect || !_wrapper) return -1;
    return _wrapper->getDI(ioNum) ? 1 : 0;
}


bool AbbRobotDriverEgmUpdate::isConnect() {
    if(!_connect) return false;
    
    // 发送ping测试连接
    RobotMessage msg;
    msg.instructionCode = 0;
    msg.nParams = 0;
    
    if(sendRobotMessage(msg)) {
        RobotResponse response;
        if(receiveRobotResponse(response) && response.ok == SERVER_OK) {
            return true;
        }
    }
    
    _connect = false;
    return false;
}


void AbbRobotDriverEgmUpdate::move(std::string path) {
    if (!_connect || _mode == 0)
        return;

    try {
        RobotMovePath robotPath;
        Fuxi::Common::JSON::parseJSON(path, robotPath);

        // 清除缓冲区
        RobotMessage clearMsg;
        clearMsg.instructionCode = 30;  // Clear path buffer
        clearMsg.nParams = 0;
        if(!sendRobotMessageWithRetry(clearMsg)) {
            LOG(ERROR) << "Failed to clear buffer";
            return;
        }

        // 添加路径点
        for(const auto& point : robotPath.Path) {
            char buff[999];
            std::string msgtmp;

            // 根据运动类型构造消息
            if(point.MoveType == 1) {  // Joint move
                sprintf(buff, "%.2d %.1d", 30, 9);  // 30 for buffer add, 9 for joint
                msgtmp = buff;
                
                // 添加关节角度
                for(size_t i = 0; i < point.JointPos.size(); i++) {
                    sprintf(buff, " %+07.3lf", point.JointPos[i] * rw::math::Rad2Deg);
                    msgtmp += buff;
                }
                
                // 添加外部轴
                msgtmp += " +000.000";
                
            } else if(point.MoveType == 2) {  // Linear move
                sprintf(buff, "%.2d %.1d", 30, 1);  // 30 for buffer add, 1 for linear
                msgtmp = buff;
                
                // 添加位置和四元数
                rw::math::RPY<> rpy(point.DcartPos[3], point.DcartPos[4], point.DcartPos[5]);
                rw::math::Rotation3D<> rot3D = rpy.toRotation3D();
                rw::math::Quaternion<> quaternion(rot3D);
                
                sprintf(buff, " %+07.1lf %+07.1lf %+07.1lf %+07.5lf %+07.5lf %+07.5lf %+07.5lf",
                    point.DcartPos[0] * M2MM,
                    point.DcartPos[1] * M2MM,
                    point.DcartPos[2] * M2MM,
                    quaternion.getQw(),
                    quaternion.getQx(),
                    quaternion.getQy(),
                    quaternion.getQz()
                );
                msgtmp += buff;
            }

            // 添加运动参数
            sprintf(buff, " %04d %04d %03d %1d %1d %03d #",
                0,                    // Reserved
                point.speed,         // Speed
                point.zone,          // Zone
                point.singArea,      // Singular area
                0,                   // Reserved
                point.acc            // Acceleration
            );
            msgtmp += buff;

            LOG(INFO) << "Adding path point: " << msgtmp;
            try {
                int bytesSent = _motionclient->send(msgtmp);
                if (bytesSent <= 0) {
                    LOG(ERROR) << "Failed to add path point";
                    return;
                }
            } catch (const SocketException& e) {
                LOG(ERROR) << "Failed to add path point: " << e.what();
                return;
            }

            // 等待点添加确认
            RobotResponse response;
            if(!receiveRobotResponse(response) || response.ok != SERVER_OK) {
                LOG(ERROR) << "Failed to confirm point addition";
                return;
            }
        }

        // 执行路径
        RobotMessage execMsg;
        execMsg.instructionCode = 31;  // Execute path
        execMsg.nParams = 0;
        
        if(sendRobotMessageWithRetry(execMsg)) {
            // 等待执行完成
            while(true) {
                RobotResponse response;
                if(receiveRobotResponse(response) && response.ok == SERVER_OK) {
                    if(response.message == "1") {
                        LOG(INFO) << "Path execution completed";
                        break;
                    }
                }
                Sleep(100);
            }
        }
    }
    catch(const std::exception& e) {
        LOG(ERROR) << "Exception in move: " << e.what();
    }
}


void AbbRobotDriverEgmUpdate::sendMessage(const std::string &sendData) {
    if (_motionclient && _motionclient->isConnected()) {
        try {
            _motionclient->send(sendData);
        } catch (const SocketException& e) {
            LOG(ERROR) << "Failed to send message: " << e.what();
        }
    } else {
        LOG(ERROR) << "Motion client not connected, cannot send message: " << sendData;
    }
}

bool AbbRobotDriverEgmUpdate::parserProgStatus(const QString &text) {
    if (text.startsWith("SET")) {
        auto list = text.split(" ", QString::SkipEmptyParts);
        LOG(INFO) << list.size();
        for (auto i: list) {
            LOG(INFO) << i.toStdString();
        }
        if (list.size() == 3) {
            auto progCmd = list.at(1);
            auto progValue = list.at(2);
            LOG(INFO) << progCmd.toStdString();
            if (progCmd == "MotionFinish") {
                LOG(INFO) << std::left << std::setw(16) << progCmd.toStdString() << ": " << progValue.toStdString();
                return true;
            } else if (progCmd == "MotionStart") {
                LOG(INFO) << std::left << std::setw(16) << progCmd.toStdString();
                return true;

            } else if (progCmd == "Interruption") {
                LOG(INFO) << std::left << std::setw(16) << progCmd.toStdString();
                return true;
            }
        }
    }
    return false;
}


bool AbbRobotDriverEgmUpdate::ParseMessage
        (
                const std::string &msg, ReturnMsg &returnmsg
        ) {
    int ok = -1;
    int idCode = -1;
    int completState = -1;

    sscanf(msg.c_str(), "%d %d %d",
           &idCode, &ok, &completState);

    if (ok == -1) {
        return false;
    }
    returnmsg.id = idCode;
    returnmsg.ok = ok;
    returnmsg.State = completState;
    LOG(INFO) << "idCode   " << idCode << " ok " << ok << " completState  " << completState;
    return true;
}


RobotMiddlePoint::RobotMiddlePoint() {

}


RobotMovePath::RobotMovePath() {

}


bool AbbRobotDriverEgmUpdate::saveMiddlePoint(RobotMiddlePoint abbRobotMiddlePoint) {
//    _message->postTask([&, abbRobotMiddlePoint]() {
    char buff[999];
    std::string msgtmp;
    if (abbRobotMiddlePoint.MoveType == 1) {
        sprintf(buff, "%.2d %.1d %+07.3lf %+07.3lf %+07.3lf %+07.3lf %+07.3lf %+07.3lf %+07.3lf $ ",
                30,
                9,
                abbRobotMiddlePoint.JointPos[0] * rw::math::Rad2Deg,
                abbRobotMiddlePoint.JointPos[1] * rw::math::Rad2Deg,
                abbRobotMiddlePoint.JointPos[2] * rw::math::Rad2Deg,
                abbRobotMiddlePoint.JointPos[3] * rw::math::Rad2Deg,
                abbRobotMiddlePoint.JointPos[4] * rw::math::Rad2Deg,
                abbRobotMiddlePoint.JointPos[5] * rw::math::Rad2Deg,
                0.0
        );

//        LOG(INFO) << "move path movejMsg----------" << motionMsg;

    }

    if (abbRobotMiddlePoint.MoveType == 2) {
        rw::math::RPY<> rpy(abbRobotMiddlePoint.DcartPos[3], abbRobotMiddlePoint.DcartPos[4],
                            abbRobotMiddlePoint.DcartPos[5]);
        rw::math::Rotation3D<> rot3D = rpy.toRotation3D();
        rw::math::Quaternion<> quaternion(rot3D);
        sprintf(buff,
                "%.2d %.1d %+07.1lf %+07.1lf %+07.1lf %+07.5lf %+07.5lf %+07.5lf %+07.5lf $ ",
                30,
                1,
                abbRobotMiddlePoint.DcartPos[0] * M2MM,
                abbRobotMiddlePoint.DcartPos[1] * M2MM,
                abbRobotMiddlePoint.DcartPos[2] * M2MM,
                quaternion.getQw(),
                quaternion.getQx(),
                quaternion.getQy(),
                quaternion.getQz()
        );
//        LOG(INFO) << "move path CartesianMsg----------" << motionMsg;

    }
    msgtmp += buff;
    sprintf(buff, "%04d ", 0);
    msgtmp += buff;
    sprintf(buff, "%04d ", abbRobotMiddlePoint.speed);
    msgtmp += buff;
    sprintf(buff, "%03d ", abbRobotMiddlePoint.zone);
    msgtmp += buff;

    sprintf(buff, "%1d ", abbRobotMiddlePoint.singArea);
    msgtmp += buff;

    sprintf(buff, "%1d ", 0);
    msgtmp += buff;

    sprintf(buff, "%03d ", abbRobotMiddlePoint.acc);
    msgtmp += buff;

    msgtmp += "#";

    LOG(INFO) << msgtmp;
    sendMessage(msgtmp);
//    });
    return true;
}


bool AbbRobotDriverEgmUpdate::disconnect() {
    if (_connect && _wrapper) {
        try {
            _wrapper->stopRAPIDExecution();
            _connect = false;
            _returnMsg.connected = false;
            _returnMsg.State = 0;  // 设置为未运行状态
        }
        catch (const std::exception& e) {
            LOG(ERROR) << "Error during disconnect: " << e.what();
            _returnMsg.State = 3;  // 设置为错误状态
        }
    }
    
    // 停止接收线程
    _stopReceiveThreads = true;
    if (_motionReceiveThread.joinable()) {
        _motionReceiveThread.join();
    }
    if (_loggerReceiveThread.joinable()) {
        _loggerReceiveThread.join();
    }

    if (_motionclient) {
        _motionclient->close();
    }
    if (_motionclientLogger) {
        _motionclientLogger->close();
    }
    
    return true;
}

bool AbbRobotDriverEgmUpdate::reset() {
    if (!_connect || !_wrapper) return false;
    
    try {
        // 停止RAPID执行
        _wrapper->stopRAPIDExecution();
        Sleep(100);
        
        // 重置程序指针
        _wrapper->resetRAPIDProgramPointer();
        Sleep(100);
        
        // 重新启动RAPID执行
        return _wrapper->startRAPIDExecution();
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Failed to reset robot: " << e.what();
        return false;
    }
}

void AbbRobotDriverEgmUpdate::setEgmEnable(int mode, int val) {
    RobotMessage msg;
    msg.instructionCode = 25;  // Set EGM mode
    msg.params = {
        (float)mode,
        (float)val
    };
    msg.nParams = msg.params.size();
    
    if(!sendRobotMessageWithRetry(msg)) {
        LOG(ERROR) << "Failed to set EGM mode " << mode << " value " << val;
    }
}

// Add new message sending implementation
bool AbbRobotDriverEgmUpdate::sendRobotMessage(const RobotMessage& msg) {
    // 按照RAPID脚本要求的格式构造消息
    char buff[999];
    std::string message;
    
    // 指令代码格式化为2位数字
    sprintf(buff, "%.2d", msg.instructionCode);
    message = buff;
    
    // 添加参数，保持精度和格式
    for(float param : msg.params) {
        if(std::abs(param) < 1000) {
            sprintf(buff, " %+07.3f", param);  // 小数点后3位
        } else {
            sprintf(buff, " %+07.1f", param);  // 大数值用1位小数
        }
        message += buff;
    }
    
    message += " #";
    LOG(INFO) << "Sending message: " << message;

    try {
        int bytesSent = _motionclient->send(message);
        return bytesSent > 0;
    } catch (const SocketException& e) {
        LOG(ERROR) << "Failed to send robot message: " << e.what();
        return false;
    }
}

// Add response parsing
bool AbbRobotDriverEgmUpdate::parseRobotResponse(const std::string& response, RobotResponse& parsed) {
    std::vector<std::string> parts = stringAlgorithm::splitString(response, " ");
    
    if(parts.size() < 2) return false;
    
    try {
        parsed.instructionCode = std::stoi(parts[0]);
        parsed.ok = std::stoi(parts[1]);
        
        // Combine remaining parts as message
        if(parts.size() > 2) {
            parsed.message = "";
            for(size_t i = 2; i < parts.size(); i++) {
                if(i > 2) parsed.message += " ";
                parsed.message += parts[i];
            }
        }
        return true;
    }
    catch(...) {
        return false;
    }
}

// Add receive response implementation
bool AbbRobotDriverEgmUpdate::receiveRobotResponse(RobotResponse& response) {
    try {
        std::unique_lock<std::mutex> lock(_receiveMutex);

        // 等待数据到达，超时时间为5秒
        if (!_receiveCondition.wait_for(lock, std::chrono::seconds(5), [this] { return !_receiveBuffer.empty(); })) {
            LOG(ERROR) << "Timeout waiting for robot response";
            return false;
        }

        // 获取接收到的数据
        auto data = _receiveBuffer.front();
        _receiveBuffer.pop();

        if (data.empty()) {
            LOG(ERROR) << "Received empty response from robot";
            return false;
        }

        std::string responseStr(data.begin(), data.end());
        LOG(INFO) << "Received response: " << responseStr;

        // 移除结尾的 #
        if(!responseStr.empty() && responseStr.back() == '#') {
            responseStr.pop_back();
        }

        return parseRobotResponse(responseStr, response);
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Exception in receiveRobotResponse: " << e.what();
        return false;
    }
}

bool AbbRobotDriverEgmUpdate::sendRobotMessageWithRetry(const RobotMessage& msg, int maxRetries) {
    for(int i = 0; i < maxRetries; i++) {
        if(sendRobotMessage(msg)) {
            RobotResponse response;
            if(receiveRobotResponse(response)) {
                if(response.ok == SERVER_OK) {
                    return true;
                }
                LOG(ERROR) << "Robot error: " << response.message;
            }
        }
        LOG(WARNING) << "Retry " << (i+1) << " of " << maxRetries;
        Sleep(100);
    }
    return false;
}

AbbRobotDriverEgmUpdate::RobotStatus AbbRobotDriverEgmUpdate::getRobotStatus() {
    RobotStatus status;
    
    RobotMessage msg;
    msg.instructionCode = 98;  // Get robot info
    msg.nParams = 0;
    
    if(sendRobotMessage(msg)) {
        RobotResponse response;
        if(receiveRobotResponse(response) && response.ok == SERVER_OK) {
            std::vector<std::string> parts =  stringAlgorithm::splitString(response.message, "*");
            if(parts.size() >= 3) {
                status.serialNo = parts[0];
                status.swVersion = parts[1];
                status.robotType = parts[2];
            }
        }
    }
    
    return status;
}

// 运动客户端接收线程
void AbbRobotDriverEgmUpdate::motionReceiveThreadFunc() {
    const int bufferSize = 4096;
    char buffer[bufferSize];

    while (!_stopReceiveThreads && _motionclient && _motionclient->isConnected()) {
        try {
            std::string receivedData = _motionclient->receiveString(bufferSize);
            if (!receivedData.empty()) {
                // 将接收到的数据转换为vector<uint8_t>并添加到缓冲区
                std::vector<uint8_t> data(receivedData.begin(), receivedData.end());

                std::lock_guard<std::mutex> lock(_receiveMutex);
                _receiveBuffer.push(data);
                _dataReceived = true;
                _receiveCondition.notify_one();
            }
        } catch (const SocketException& e) {
            if (!_stopReceiveThreads) {
                LOG(ERROR) << "Motion receive thread error: " << e.what();
            }
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    LOG(INFO) << "Motion receive thread stopped";
}

// 日志客户端接收线程
void AbbRobotDriverEgmUpdate::loggerReceiveThreadFunc() {
    const int bufferSize = 4096;
    char buffer[bufferSize];

    while (!_stopReceiveThreads && _motionclientLogger && _motionclientLogger->isConnected()) {
        try {
            std::string receivedData = _motionclientLogger->receiveString(bufferSize);
            if (!receivedData.empty()) {
                LOG(INFO) << "Logger received: " << receivedData;
            }
        } catch (const SocketException& e) {
            if (!_stopReceiveThreads) {
                LOG(ERROR) << "Logger receive thread error: " << e.what();
            }
            break;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }

    LOG(INFO) << "Logger receive thread stopped";
}

void AbbRobotDriverEgmUpdate::waitForMotionComplete(const rw::math::Transform3D<>& target, int zone) {
    double time = 0;
    while (!isNearPosepoint(target, zone)) {
        time += 0.1;
        if (time > _moveTimeOut) {
            LOG(ERROR) << "Motion timeout after " << time << " seconds";
            break;
        }
        Sleep(100);
    }
    LOG(INFO) << "Motion completed in " << time << " seconds";
}

void AbbRobotDriverEgmUpdate::waitForMotionComplete(const rw::math::Q& target, int zone) {
    int time = 0;
    while (!isNearJointpoint(target, zone)) {
        time++;
        if (time > _moveTimeOut) {
            LOG(ERROR) << "Motion timeout after " << time*0.2 << " seconds";
            break;
        }
        Sleep(200);
    }
    LOG(INFO) << "Motion completed in " << time*0.2 << " seconds";
}

bool AbbRobotDriverEgmUpdate::handleRobotError(const RobotResponse& response) {
    if(response.ok != SERVER_OK) {
        RobotError error;
        try {
            error.code = static_cast<RobotErrorCode>(std::stoi(response.message));
            switch(error.code) {
                case ERR_MOTION_IN_PROGRESS:
                    error.message = "Motion in progress";
                    break;
                case ERR_OUTSIDE_WORKSPACE:
                    error.message = "Target outside workspace";
                    break;
                case ERR_SINGULARITY:
                    error.message = "Singularity detected";
                    break;
                case ERR_BUFFER_FULL:
                    error.message = "Path buffer full";
                    break;
                default:
                    error.message = "Unknown error: " + response.message;
            }
        }
        catch(...) {
            error.code = ERR_INVALID_MESSAGE;
            error.message = response.message;
        }
        
        LOG(ERROR) << "Robot error: " << error.message;
        return false;
    }
    return true;
}




