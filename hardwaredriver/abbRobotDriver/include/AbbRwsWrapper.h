//
// Created by cobot on 2020/10/7.
//

#ifndef SPARROWABBROBOTTEST_ABBRWSWRAPPER_H
#define SPARROWABBROBOTTEST_ABBRWSWRAPPER_H

#include "rw/math.hpp"
#include <abb_librws/rws_interface.h>


namespace Sensor {
    namespace Robot {
        namespace ABB {

            class AbbRwsWrapper {
            public:
                AbbRwsWrapper(const std::string ip, int port = 80, const std::string username = "Default User",
                              const std::string password = "robotics");

                rw::math::Q getQ(std::string mechunit = "ROB_1");

                rw::math::Transform3D<> getP(std::string mechunit = "ROB_1");

                bool startRAPIDExecution();

                bool resetRAPIDProgramPointer();

                bool setMotorsOn();

                bool isRAPIDstate();

                bool stopRAPIDExecution();

                bool getDI(int ioNum);

                bool getEMG();

                bool resetEMG();

                bool setDo(int ioNum, bool value);


            private:
                std::string _ip;
                std::string _username;
                std::string _password;
                int _port;
                std::shared_ptr<abb::rws::RWSInterface> _interface;
            };

        }
    }
}

#endif //SPARROWABBROBOTTEST_ABBRWSWRAPPER_H
