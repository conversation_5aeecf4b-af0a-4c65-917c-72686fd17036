#pragma once
#define CPPHTTPLIB_OPENSSL_SUPPORT

#include "AbbRwsWrapper.h"
#include <rw/math/Rotation3D.hpp>
#include <rw/math/Quaternion.hpp>
#include <rw/math/RPY.hpp>
#include "rw/math.hpp"
#include <rttr/registration.h>
#include <QString>
#include <Executor.h>
#include <mutex>
#include <condition_variable>
#include <queue>
#include "socket.h"
#include <thread>


        /**
         * @brief 机器人中间点结构体
         */
        struct RobotMiddlePoint {
            int MoveType;
            std::vector<double> DcartPos;
            std::vector<double> JointPos;
            int speed;
            int zone;
            int acc;
            int singArea; // For ABB, 0: Off, 1: Wrist, 2:Lock4Axis
            RobotMiddlePoint();

        RTTR_ENABLE()
        };

        /**
         * @brief 机器人路径结构体
         */
        struct RobotMovePath {
            std::vector<RobotMiddlePoint> Path;

            RobotMovePath();

        RTTR_ENABLE()
        };


struct Base{
    std::string href;
RTTR_ENABLE()
};
struct Links{
    Base base;
RTTR_ENABLE()
};


struct CartesianData{
    std::string type;
    std::string title;
    double a1;
    double a2;
    double a3;
    double a4;
    double a5;
    double a6;
    double a7;
    double a8;
    double a9;
    double a10;
    double a11;
    double a12;
RTTR_ENABLE()
};



struct Embedded{
    std::vector<CartesianData> cartesianData;
RTTR_ENABLE()
};

struct RobotCartesian {
    Links links;
    Embedded embedded;
RTTR_ENABLE()
};

RTTR_PLUGIN_REGISTRATION {

    rttr::registration::class_<Base>("base")
            .
                    constructor<>()
            .property("href", &Base::href);

    rttr::registration::class_<Links>("_links")
            .
                    constructor<>()
            .property("base", &Links::base);



    rttr::registration::class_<CartesianData>("CartesianState")
            .
                    constructor<>()
            .property("_type", &CartesianData::type)
            .property("_title", &CartesianData::title)
            .property("x", &CartesianData::a1)
            .property("y", &CartesianData::a2)
            .property("z", &CartesianData::a3)
            .property("q1", &CartesianData::a4)
            .property("q2", &CartesianData::a5)
            .property("q3", &CartesianData::a6)
            .property("q4", &CartesianData::a7)
            .property("j1", &CartesianData::a8)
            .property("j4", &CartesianData::a9)
            .property("j6", &CartesianData::a10)
            .property("jx", &CartesianData::a11);

    rttr::registration::class_<CartesianData>("JointState")
            .
                    constructor<>()
            .property("_type", &CartesianData::type)
            .property("_title", &CartesianData::title)
            .property("rax_1",&CartesianData::a1)
            .property("rax_2",&CartesianData::a2)
            .property("rax_3",&CartesianData::a3)
            .property("rax_4", &CartesianData::a4)
            .property("rax_5", &CartesianData::a5)
            .property("rax_6", &CartesianData::a6)
            .property("eax_a", &CartesianData::a7)
            .property("eax_b", &CartesianData::a8)
            .property("eax_c", &CartesianData::a9)
            .property("eax_d", &CartesianData::a10)
            .property("eax_e", &CartesianData::a11);


    rttr::registration::class_<Embedded>("_embedded")
            .
                    constructor<>()
            .property("_state", &Embedded::cartesianData);

    rttr::registration::class_<RobotCartesian>("RobotCartesian")
            .
                    constructor<>()
            .property("_links", &RobotCartesian::links)
            .property("_embedded", &RobotCartesian::embedded);
};


/**
         * @brief ABB机器人含sevoj功能的更新驱动，包含分段加减速
         */
        class AbbRobotDriverEgmUpdate {


            /**
             * @brief zone结构体
             */
            typedef enum {
                ZONE_FINE = 0,
                ZONE_1,
                ZONE_2,
                ZONE_3,
                ZONE_4,
                ZONE_5,
                ZONE_6,
                ZONE_7,
                ZONE_8,
                ZONE_9,
                ZONE_10,
                NUM_ZONES
            } ZONE_TYPE;

            /**
             * @brief zone子结构体
             */
            typedef struct {
                double p_tcp; // TCP path zone (mm)
                double p_ori; // Zone size for orientation (mm)
                double ori;   // Tool orientation (degrees)
            } zoneVals;



            /**
             * @brief zonedata初始化
             */
            zoneVals zoneData[NUM_ZONES] =
                    {
                            // p_tcp (mm), p_ori (mm), ori (deg)
                            {0.0,   0.0,   0.0},   // ZONE_FINE
                            {0.3,   0.3,   0.1},  // ZONE_1
                            {1.0,   1.0,   0.1},   // ZONE_2
                            {5.0,   8.0,   0.8},   // ZONE_3
                            {10.0,  15.0,  1.5},   // ZONE_4
                            {20.0,  30.0,  3.0},   // ZONE_5
                            {30.0,  45.0,  4.5},  // ZONE_6
                            {40.0,  60.0,  6.0},  // ZONE_7
                            {50.0,  75.0,  7.5},  // ZONE_8
                            {100.0, 150.0, 15.0},   // ZONE_9
                            {150.0, 225.0, 23.0}    // ZONE_10
                    };
            /**
             * @brief 机器人回传的状态结构体
             */
            struct ReturnMsg {
                int id;                             // 消息ID
                int ok;                             // 执行状态
                int State;                          // 机器人状态
                bool connected;                     // 连接状态
                rw::math::Transform3D<> cartesian; // 笛卡尔位置
                rw::math::Q joint;                 // 关节位置
                
                ReturnMsg() : id(0), ok(0), State(0), connected(false),
                              cartesian(rw::math::Transform3D<>()),
                              joint(rw::math::Q(6, 0.0)) {}
            };

            struct RobotPosData{
                rw::math::Transform3D<> pose;
                rw::math::Q joint;
            };

        public:
            /**
             * @brief 构造函数
             */

            AbbRobotDriverEgmUpdate();

            /**
             * @brief 虚构函数
             */

            ~AbbRobotDriverEgmUpdate();

            /**
             * @brief 机器人连接函数，进行机器人程序初始化操作，机器人启动操作
             * @param ip
             * @param port
             * @return
             */

             bool connect(std::string ip = "", int port = 0) ;

            bool disconnect() ;

            bool reset() ;

            void setEgmEnable(int mode, int val) ;

            /**
             * @brief 机器人停止操作，目前没有实现
             * @return
             */

             bool stop() ;

            /**
             * @brief sevoj运动函数，目前只用于机器人初始化回home点操作
             * @param qTrajectory
             */


            /**
             * @brief 获取机器人的连接状态
             * @return
             */

             bool isConnect() ;
            /**
             * @brief 设置机器人加减速，参数只有acc有用，范围0-100，加速度承数值变大而变大；
             * @param acc
             * @param ramp
             * @return
             */
             bool setAcceleration(int acc, int ramp) ;

            /**
             * @brief 获取机器人实际的tcp坐标，不含tool长度；
             * @return
             */
             rw::math::Transform3D<> getRobotActualTCP() ;

            RobotPosData getRobotPoseData();

            /**
             * @brief 获取机器人的关节坐标
             * @return
             */
             rw::math::Q getRobotJointQ() ;

            /**
             * @brief 设置机器人IO输出
             * @param portIndex
             * @param val
             * @param reset
             */
             void setOutValue(int portIndex, bool val, bool reset = true) ;

            /**
             * @brief 获取机器人的输入信号
             * @param ioNum
             * @return
             */
             int getInputValue(int ioNum) ;


            /**
             * @brief 机器人movej轴运动，可以配置机器人轴关节，轴速度、加减速，交融半径目前没有用
             * @param q
             * @param tcpSpeed
             * @param zone
             * @param acc
             */
             void movej(rw::math::Q q, double tcpSpeed, int zone,int acc) ;

            /**
             * @brief 机器人movel直线运动，可以配置机器人点坐标，线速度，加减速，交融半径目前没有用
             * @param poseTarget
             * @param tcpSpeed
             * @param zone
             * @param acc
             */
             void movel(rw::math::Transform3D<> poseTarget, double tcpSpeed, int zone,int acc) ;

            /**
             * @brief 机器人混合轨迹，直接交融半径，需要用rttr进行序列化。
             * @param path
             */
             void move(std::string path) ;

            struct RobotStatus {
                std::string serialNo;
                std::string swVersion;
                std::string robotType;
            };
            
            RobotStatus getRobotStatus();

            std::string _ip="*************";
            int _port;
            int _mode=0;
        private:


            /**
             * @brief 用于混合路径保存中间点位使用
             * @param abbRobotMiddlePoint
             * @return
             */
            bool saveMiddlePoint(RobotMiddlePoint abbRobotMiddlePoint);

            /**
             * @brief 判断是否接近目前轴关节，配合movej达到运动堵塞的效果，也可以作为过滤点的依据
             * @param q
             * @param zone
             * @return
             */
            bool isNearJointpoint(rw::math::Q q, int zone);
            /**
             * @brief 判断是否接近目前的tcp坐标，配合movel达到运动堵塞的效果，也可以作为过滤点的依据
             * @param poseTarget
             * @param zone
             * @return
             */
            bool isNearPosepoint(rw::math::Transform3D<> poseTarget, int zone);

            /**
             * @brief 监听线程用于监听机器人的实时状态
             */
            void LoadData();


            void sendMessage(const std::string& sendData);

            /**
             * @brief 解析机器人回传的数据
             * @param text
             * @return
             */
            bool parserProgStatus(const QString &text);


            char motionMsg[999];
            char jointMsg[999];
            char CartesianMsg[999];
            char EgmmotionMsg[999];
            bool _connect = false;
            int _moveTimeOut = 10;
            int _acc=100;
            ReturnMsg _returnMsg;
            std::shared_ptr<Sensor::Robot::ABB::AbbRwsWrapper> _wrapper;
            std::shared_ptr<Socket> _motionclient;
            std::shared_ptr<Socket> _motionclientLogger;
            std::shared_ptr<Fuxi::Common::Executor> _message;


            /**
             * @brief 解析机器人回传数据的机器人状态位
             * @param msg
             * @param returnmsg
             * @return
             */
            bool ParseMessage(const std::string &msg, ReturnMsg &returnmsg);

            // Add new message structure for robot communication
            struct RobotMessage {
                uint16_t nParams;      // Number of parameters
                uint16_t instructionCode;  // Instruction code
                std::vector<float> params; // Parameters
            };

            // Add response structure
            struct RobotResponse {
                int instructionCode;
                int ok;  // SERVER_OK=1, SERVER_BAD_MSG=0
                std::string message;
            };

            // Add constants
            const int SERVER_BAD_MSG = 0;
            const int SERVER_OK = 1;

            // Add methods for message handling
            bool sendRobotMessage(const RobotMessage& msg);
            bool receiveRobotResponse(RobotResponse& response);
            bool parseRobotResponse(const std::string& response, RobotResponse& parsed);
            
            // Add buffer for raw message construction
            std::vector<uint8_t> messageBuffer;

            // Add motion completion wait functions
            void waitForMotionComplete(const rw::math::Transform3D<>& target, int zone);
            void waitForMotionComplete(const rw::math::Q& target, int zone);
            
            // Add retry function declaration
            bool sendRobotMessageWithRetry(const RobotMessage& msg, int maxRetries = 3);

            // Add error codes
            enum RobotErrorCode {
                ERR_NONE = 0,
                ERR_INVALID_MESSAGE = 1,
                ERR_MOTION_IN_PROGRESS = 2,
                ERR_OUTSIDE_WORKSPACE = 3,
                ERR_SINGULARITY = 4,
                ERR_BUFFER_FULL = 5,
                ERR_EXECUTION_ERROR = 6
            };

            // Add error message structure
            struct RobotError {
                RobotErrorCode code;
                std::string message;
            };

            // Add error handling
            bool handleRobotError(const RobotResponse& response);
            
            // Add error tracking
            RobotError _lastError;

            // Add data buffer for socket communication
            std::mutex _receiveMutex;
            std::condition_variable _receiveCondition;
            std::queue<std::vector<uint8_t>> _receiveBuffer;
            bool _dataReceived = false;

            // Add socket communication threads
            std::thread _motionReceiveThread;
            std::thread _loggerReceiveThread;
            std::atomic<bool> _stopReceiveThreads{false};

            // Add socket receive methods
            void motionReceiveThreadFunc();
            void loggerReceiveThreadFunc();
        };

