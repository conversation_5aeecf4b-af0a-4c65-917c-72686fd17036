#include "HikVisionCamera.h"
#include <iostream>

HikVisionCamera::~HikVisionCamera()
{
    if (m_isGrabbing) {
        stopGrabbing();
    }
    if (m_isConnected) {
        disconnect();
    }
    if (m_pData) {
        delete[] m_pData;
        m_pData = nullptr;
    }
}

HikVisionCamera::HikVisionCamera() :
        m_hDevice(nullptr),
        m_isConnected(false),
        m_isGrabbing(false),
        m_pData(nullptr),
        m_triggerMode(TRIGGER_MODE_OFF)
{
    memset(&m_stIntValue, 0, sizeof(MVCC_INTVALUE));
    memset(&m_stImageInfo, 0, sizeof(MV_FRAME_OUT_INFO_EX));
}

bool HikVisionCamera::connectToDevice(const std::string& serialNumber)
{
    if (m_isConnected) {
        disconnect();
    }

    MV_CC_DEVICE_INFO_LIST stDeviceList;
    memset(&stDeviceList, 0, sizeof(MV_CC_DEVICE_INFO_LIST));

    int nRet = MV_CC_EnumDevices(MV_GIGE_DEVICE | MV_USB_DEVICE, &stDeviceList);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to enumerate devices, error code: " + std::to_string(nRet));
        }
        return false;
    }

    if (stDeviceList.nDeviceNum <= 0) {
        if (m_errorCallback) {
            m_errorCallback("No devices found");
        }
        return false;
    }

    bool found = false;
    int deviceIndex = -1;
    for (unsigned int i = 0; i < stDeviceList.nDeviceNum; i++) {
        MV_CC_DEVICE_INFO* pDeviceInfo = stDeviceList.pDeviceInfo[i];
        if (pDeviceInfo == nullptr) {
            continue;
        }

        std::string deviceSerial;
        if (pDeviceInfo->nTLayerType == MV_GIGE_DEVICE) {
            deviceSerial = reinterpret_cast<char*>(pDeviceInfo->SpecialInfo.stGigEInfo.chSerialNumber);
        } else if (pDeviceInfo->nTLayerType == MV_USB_DEVICE) {
            deviceSerial = reinterpret_cast<char*>(pDeviceInfo->SpecialInfo.stUsb3VInfo.chSerialNumber);
        }

        if (deviceSerial == serialNumber) {
            deviceIndex = i;
            found = true;
            break;
        }
    }

    if (!found) {
        if (m_errorCallback) {
            m_errorCallback("Camera with serial number " + serialNumber + " not found");
        }
        return false;
    }

    nRet = MV_CC_CreateHandle(&m_hDevice, stDeviceList.pDeviceInfo[deviceIndex]);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to create handle, error code: " + std::to_string(nRet));
        }
        return false;
    }

    nRet = MV_CC_OpenDevice(m_hDevice);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to open device, error code: " + std::to_string(nRet));
        }
        return false;
    }

    // For GigE cameras, set optimal packet size
    if (stDeviceList.pDeviceInfo[deviceIndex]->nTLayerType == MV_GIGE_DEVICE) {
        int nPacketSize = MV_CC_GetOptimalPacketSize(m_hDevice);
        if (nPacketSize > 0) {
            nRet = MV_CC_SetIntValue(m_hDevice, "GevSCPSPacketSize", nPacketSize);
            if (nRet != MV_OK && m_errorCallback) {
                m_errorCallback("Warning: Set packet size failed, error code: " + std::to_string(nRet));
            }
        }
    }

    // Set trigger mode off
    nRet = MV_CC_SetEnumValue(m_hDevice, "TriggerMode", 0);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to set trigger mode, error code: " + std::to_string(nRet));
        }
        return false;
    }

    m_isConnected = true;
    if (m_connectionStatusCallback) {
        m_connectionStatusCallback(true);
    }
    return true;
}

void HikVisionCamera::disconnect()
{
    if (m_isConnected) {
        if (m_isGrabbing) {
            stopGrabbing();
        }

        MV_CC_CloseDevice(m_hDevice);
        MV_CC_DestroyHandle(m_hDevice);
        m_hDevice = nullptr;
        m_isConnected = false;
        if (m_connectionStatusCallback) {
            m_connectionStatusCallback(false);
        }
    }
}

bool HikVisionCamera::startGrabbing()
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    int nRet = MV_CC_StartGrabbing(m_hDevice);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to start grabbing, error code: " + std::to_string(nRet));
        }
        return false;
    }

    // Get payload size
    memset(&m_stIntValue, 0, sizeof(MVCC_INTVALUE));
    nRet = MV_CC_GetIntValue(m_hDevice, "PayloadSize", &m_stIntValue);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to get payload size, error code: " + std::to_string(nRet));
        }
        return false;
    }

    m_nDataSize = m_stIntValue.nCurValue;

    // Free previous buffer if exists
    if (m_pData) {
        delete[] m_pData;
        m_pData = nullptr;
    }

    // Allocate memory for image data
    m_pData = new unsigned char[m_nDataSize];
    if (!m_pData) {
        if (m_errorCallback) {
            m_errorCallback("Failed to allocate memory for image data");
        }
        return false;
    }

    m_isGrabbing = true;
    return true;
}

void HikVisionCamera::stopGrabbing()
{
    if (m_isGrabbing) {
        MV_CC_StopGrabbing(m_hDevice);
        m_isGrabbing = false;
    }
}

cv::Mat HikVisionCamera::getFrame()
{
    cv::Mat frame;
    if (!m_isGrabbing || !m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not grabbing or not connected");
        }
        return frame;
    }

    // If in software trigger mode, we need to trigger the camera
    if (m_triggerMode == TRIGGER_MODE_SOFTWARE) {
        if (!softwareTrigger()) {
            return frame;
        }
    }

    // For hardware trigger, we wait for the trigger signal
    // For software trigger, we've just triggered it
    // For free-running mode, frames are continuously generated

    // Get one frame
    int nRet = MV_CC_GetOneFrameTimeout(m_hDevice, m_pData, m_nDataSize, &m_stImageInfo, 1000);
    if (nRet == MV_OK) {
        if (convertToMat(m_pData, frame)) {
            if (m_frameReceivedCallback) {
                m_frameReceivedCallback(frame);
            }
        } else {
            if (m_errorCallback) {
                m_errorCallback("Failed to convert image to Mat format");
            }
        }
    } else {
        if (m_errorCallback) {
            m_errorCallback("Failed to get frame, error code: " + std::to_string(nRet));
        }
    }

    return frame;
}

bool HikVisionCamera::convertToMat(unsigned char* pData, cv::Mat& image)
{
    if (!pData) {
        return false;
    }

    // Handle different pixel formats
    switch (m_stImageInfo.enPixelType)
    {
        case PixelType_Gvsp_Mono8:  // Mono8 format
            image = cv::Mat(m_stImageInfo.nHeight, m_stImageInfo.nWidth, CV_8UC1, pData);
            break;

        case PixelType_Gvsp_RGB8_Packed:  // RGB8 format
            // Convert RGB to BGR (OpenCV uses BGR)
            for (unsigned int j = 0; j < m_stImageInfo.nHeight; j++)
            {
                for (unsigned int i = 0; i < m_stImageInfo.nWidth; i++)
                {
                    unsigned char red = pData[j * (m_stImageInfo.nWidth * 3) + i * 3];
                    pData[j * (m_stImageInfo.nWidth * 3) + i * 3] = pData[j * (m_stImageInfo.nWidth * 3) + i * 3 + 2];
                    pData[j * (m_stImageInfo.nWidth * 3) + i * 3 + 2] = red;
                }
            }
            image = cv::Mat(m_stImageInfo.nHeight, m_stImageInfo.nWidth, CV_8UC3, pData);
            break;

        default:
            if (m_errorCallback) {
                m_errorCallback("Unsupported pixel format");
            }
            return false;
    }

    return true;
}

bool HikVisionCamera::setExposureTime(float exposureTime)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    int nRet = MV_CC_SetFloatValue(m_hDevice, "ExposureTime", exposureTime);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to set exposure time");
        }
        return false;
    }
    return true;
}

bool HikVisionCamera::setGain(float gain)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    int nRet = MV_CC_SetFloatValue(m_hDevice, "Gain", gain);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to set gain");
        }
        return false;
    }
    return true;
}

bool HikVisionCamera::setFrameRate(float frameRate)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    int nRet = MV_CC_SetFloatValue(m_hDevice, "AcquisitionFrameRate", frameRate);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to set frame rate");
        }
        return false;
    }
    return true;
}

bool HikVisionCamera::getExposureTime(float& exposureTime)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    MVCC_FLOATVALUE stFloatValue = {0};
    int nRet = MV_CC_GetFloatValue(m_hDevice, "ExposureTime", &stFloatValue);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to get exposure time");
        }
        return false;
    }
    exposureTime = stFloatValue.fCurValue;
    return true;
}

bool HikVisionCamera::getGain(float& gain)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    MVCC_FLOATVALUE stFloatValue = {0};
    int nRet = MV_CC_GetFloatValue(m_hDevice, "Gain", &stFloatValue);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to get gain");
        }
        return false;
    }
    gain = stFloatValue.fCurValue;
    return true;
}

bool HikVisionCamera::getFrameRate(float& frameRate)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    MVCC_FLOATVALUE stFloatValue = {0};
    int nRet = MV_CC_GetFloatValue(m_hDevice, "AcquisitionFrameRate", &stFloatValue);
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to get frame rate");
        }
        return false;
    }
    frameRate = stFloatValue.fCurValue;
    return true;
}

bool HikVisionCamera::setTriggerMode(TriggerMode mode)
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    int nRet = MV_OK;

    // First set trigger mode on or off
    if (mode == TRIGGER_MODE_OFF) {
        nRet = MV_CC_SetEnumValue(m_hDevice, "TriggerMode", 0);
    } else {
        // For both hardware and software trigger, need to enable trigger mode
        nRet = MV_CC_SetEnumValue(m_hDevice, "TriggerMode", 1);
    }

    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to set trigger mode, error code: " + std::to_string(nRet));
        }
        return false;
    }

    // Then set trigger source based on the mode
    if (mode == TRIGGER_MODE_SOFTWARE) {
        nRet = MV_CC_SetEnumValue(m_hDevice, "TriggerSource", MV_TRIGGER_SOURCE_SOFTWARE);
    } else if (mode == TRIGGER_MODE_HARDWARE) {
        // Set to Line0 as default hardware trigger source
        nRet = MV_CC_SetEnumValue(m_hDevice, "TriggerSource", MV_TRIGGER_SOURCE_LINE0);
    }

    if (nRet != MV_OK && mode != TRIGGER_MODE_OFF) {
        if (m_errorCallback) {
            m_errorCallback("Failed to set trigger source, error code: " + std::to_string(nRet));
        }
        return false;
    }

    m_triggerMode = mode;
    return true;
}

bool HikVisionCamera::softwareTrigger()
{
    if (!m_isConnected) {
        if (m_errorCallback) {
            m_errorCallback("Camera not connected");
        }
        return false;
    }

    if (m_triggerMode != TRIGGER_MODE_SOFTWARE) {
        if (m_errorCallback) {
            m_errorCallback("Camera is not in software trigger mode");
        }
        return false;
    }

    int nRet = MV_CC_SetCommandValue(m_hDevice, "TriggerSoftware");
    if (nRet != MV_OK) {
        if (m_errorCallback) {
            m_errorCallback("Failed to execute software trigger, error code: " + std::to_string(nRet));
        }
        return false;
    }

    return true;
}

TriggerMode HikVisionCamera::getTriggerMode() const
{
    return m_triggerMode;
}
