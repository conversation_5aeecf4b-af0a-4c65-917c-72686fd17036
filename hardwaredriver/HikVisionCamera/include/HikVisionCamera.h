#pragma once

#include <string>
#include <opencv2/opencv.hpp>
#include "MvCameraControl.h"
#include <functional>

// Define callback function types
using ConnectionStatusCallback = std::function<void(bool)>;
using FrameReceivedCallback = std::function<void(const cv::Mat&)>;
using ErrorCallback = std::function<void(const std::string&)>;

// Define trigger modes
enum TriggerMode {
    TRIGGER_MODE_OFF,      // Free-running mode
    TRIGGER_MODE_SOFTWARE, // Software trigger
    TRIGGER_MODE_HARDWARE  // Hardware trigger (external)
};

class HikVisionCamera
{
public:
    explicit HikVisionCamera();
    ~HikVisionCamera();

    bool connectToDevice(const std::string& serialNumber);
    void disconnect();
    bool startGrabbing();
    void stopGrabbing();
    cv::Mat getFrame();
    bool isConnected() const { return m_isConnected; }

    // Set callback functions
    void setConnectionStatusCallback(ConnectionStatusCallback callback) { m_connectionStatusCallback = callback; }
    void setFrameReceivedCallback(FrameReceivedCallback callback) { m_frameReceivedCallback = callback; }
    void setErrorCallback(ErrorCallback callback) { m_errorCallback = callback; }

    // Camera parameter adjustments
    bool setExposureTime(float exposureTime);
    bool setGain(float gain);
    bool setFrameRate(float frameRate);
    bool getExposureTime(float& exposureTime);
    bool getGain(float& gain);
    bool getFrameRate(float& frameRate);

    // Trigger mode controls
    bool setTriggerMode(TriggerMode mode);
    bool softwareTrigger();
    TriggerMode getTriggerMode() const;

private:
    void initCamera();
    bool convertToMat(unsigned char* pData, cv::Mat& image);

private:
    void* m_hDevice;
    bool m_isConnected;
    bool m_isGrabbing;
    MVCC_INTVALUE m_stIntValue;
    MV_FRAME_OUT_INFO_EX m_stImageInfo;
    unsigned char* m_pData;
    unsigned int m_nDataSize;

    // Callback function members
    ConnectionStatusCallback m_connectionStatusCallback;
    FrameReceivedCallback m_frameReceivedCallback;
    ErrorCallback m_errorCallback;

    TriggerMode m_triggerMode;
};