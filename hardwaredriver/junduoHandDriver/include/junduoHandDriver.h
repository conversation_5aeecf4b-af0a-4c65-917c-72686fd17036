//
// Created by tian on 2022/11/12.
//

#ifndef FUXIOS_JUNDUOHANDCONTROL_H
#define FUXIOS_JUNDUOHANDCONTROL_H
#include <modbus.h>
#include <stdio.h>
#include <windows.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>
#include "glog.h"


#define MODBUS_SERIAL_DEV           "COM1"
#define MODBUS_SERIAL_BAUDRATE      115200    /* 9600, 38400, 115200, ... */
#define MODBUS_SERIAL_PARITY        'N'     /* 'N', 'E', or 'O' */
#define MODBUS_SERIAL_DATABITS      8       /* 5, 6, 7, or 8 */
#define MODBUS_SERIAL_STOPBITS      1       /* 1 or 2 */
#define MODBUS_DEVICE_ID            9

namespace Motion{
    namespace Common
    {
        class JunDuoHandControl {
        public:
            JunDuoHandControl();
            ~JunDuoHandControl();

            bool InitHand(const std::string serialDev);

            void HandAct();

            bool HandActFinish();

            bool HandOpen(int pose,int force,int speed);

            int getHandOpenStatus();

            std::vector<int> WaitHandFinish();


        private:
            modbus_t *_ctx = NULL;
            int _currentPose=0;

            std::string unsignedCharToHexString(unsigned char ch);
        };
    }
}
#endif // FUXIOS_JUNDUOHANDCONTROL_H
