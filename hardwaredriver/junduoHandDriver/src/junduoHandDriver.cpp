//
// Created by tian on 2022/11/12.
//

#include "junduoHandDriver.h"
#include "glog.h"
#include <serialAlgorithm.h>

using namespace Motion::Common;


JunDuoHandControl::JunDuoHandControl() {

}

JunDuoHandControl::~JunDuoHandControl() {

}

bool JunDuoHandControl::InitHand(const std::string serialDev) {
    //打开端口: 端口，波特率，校验位，数据位，停止位
    _ctx = modbus_new_rtu(serialDev.c_str(), MODBUS_SERIAL_BAUDRATE, MODBUS_SERIAL_PARITY, MODBUS_SERIAL_DATABITS, MODBUS_SERIAL_STOPBITS);
    //设置从机地址
    modbus_set_slave(_ctx, MODBUS_DEVICE_ID);
    //设置串口模式(可选)
//    modbus_rtu_set_serial_mode(_ctx, MODBUS_RTU_RS485);
    //设置RTS(可选)
//    modbus_rtu_set_rts(_ctx, MODBUS_R);
    //建立连接
    if (modbus_connect(_ctx) == -1) {
        fprintf(stderr, "Connexion failed: %s\n", modbus_strerror(errno));
        modbus_free(_ctx);
        return false;
    }
    //设置应答延时(可选)
//    timeval* outTime=new timeval();
//    outTime->tv_sec=0;
//    outTime->tv_usec=1000000;
//    modbus_set_response_timeout(_ctx, outTime);
    return true;
}

void JunDuoHandControl::HandAct() {
    uint16_t value[1]={0};
    modbus_write_registers(_ctx,1000,1,value);
    Sleep(100);
    uint16_t value1[1]={1};
    modbus_write_registers(_ctx,1000,1,value1);
}

bool JunDuoHandControl::HandActFinish() {
    for(;;){
        uint16_t value[2]={0};
        modbus_read_registers(_ctx,2000,2,value);
        if(value[0]==0)
            return true;
        Sleep(100);
    }
}

bool JunDuoHandControl::HandOpen(int pose, int force,int speed) {

    unsigned char poseTemp=uint8_t(pose & 0xFF);
    unsigned char forceTemp=uint8_t(force & 0xFF);
    unsigned char speedTemp=uint8_t(speed & 0xFF);
    std::string reg1,reg2;
    reg1.append(unsignedCharToHexString(poseTemp));
    reg1.append("00");
    reg2.append(unsignedCharToHexString(forceTemp));
    reg2.append(unsignedCharToHexString(speedTemp));


    int regInt1,regInt2;
    std::stringstream ss1,ss2;
    ss1 << std::hex << reg1;  //std::oct（八进制）、std::dec（十进制）
    ss1 >> regInt1;

    ss2 << std::hex << reg2;  //std::oct（八进制）、std::dec（十进制）
    ss2 >> regInt2;

    uint16_t value[3]={9,static_cast<uint16_t>(regInt1),static_cast<uint16_t>(regInt2)};
    auto rc = modbus_write_registers(_ctx, 1000,3, value);
    return rc;
}

int JunDuoHandControl::getHandOpenStatus() {
    return _currentPose;
}

std::vector<int> JunDuoHandControl::WaitHandFinish() {
    uint16_t value[3]={0,0,0};
    modbus_read_input_registers(_ctx, 2000,3, value);
    int value1,value2;
    std::vector<int> res;
    for(unsigned short i : value){
        std::ostringstream ss;
        ss << std::hex << i;
        std::string result = ss.str();
        if(result.size()!=4){
            std::string tmp;
            for(int j=0;j<4-result.size();++j){
                tmp.append("0");
            }
            result=tmp.append(result);
        }
        sscanf(result.substr(0,2).c_str(), "%x", &value1);
        sscanf(result.substr(2,2).c_str(), "%x", &value2);
        res.push_back(value1);
        res.push_back(value2);
    }
    _currentPose=res[2];
    return res;
}

std::string
JunDuoHandControl::unsignedCharToHexString(unsigned char ch) {
    const char hex_chars[] = "0123456789abcdef";
    std::string result = "";
    unsigned int highHalfByte = (ch >> 4) & 0x0f;
    unsigned int lowHalfByte = (ch & 0x0f);
    result += hex_chars[highHalfByte];
    result += hex_chars[lowHalfByte];
    return result;
}