#pragma once

#include <windows.h>
#include <objbase.h>
#include <oleauto.h>
#include <string>
#include <map>

// Simple COM smart pointer replacement for CComPtr
template<class T>
class ComPtr
{
private:
    T* ptr;

public:
    ComPtr() : ptr(nullptr) {}
    ComPtr(T* p) : ptr(p) {}
    ~ComPtr() { Release(); }

    ComPtr(const ComPtr& other) : ptr(other.ptr)
    {
        if (ptr) ptr->AddRef();
    }

    ComPtr& operator=(const ComPtr& other)
    {
        if (this != &other)
        {
            Release();
            ptr = other.ptr;
            if (ptr) ptr->AddRef();
        }
        return *this;
    }

    ComPtr& operator=(T* p)
    {
        if (ptr != p)
        {
            Release();
            ptr = p;
        }
        return *this;
    }

    T* operator->() const { return ptr; }
    T& operator*() const { return *ptr; }
    operator T*() const { return ptr; }
    T** operator&() { Release(); return &ptr; }

    void Release()
    {
        if (ptr)
        {
            ptr->Release();
            ptr = nullptr;
        }
    }

    void Attach(T* p)
    {
        Release();
        ptr = p;
    }

    T* Detach()
    {
        T* temp = ptr;
        ptr = nullptr;
        return temp;
    }

    HRESULT CoCreateInstance(const CLSID& clsid, IUnknown* pUnkOuter = nullptr, DWORD dwClsContext = CLSCTX_ALL)
    {
        Release();
        return ::CoCreateInstance(clsid, pUnkOuter, dwClsContext, __uuidof(T), (void**)&ptr);
    }

    HRESULT CoCreateInstance(LPCOLESTR szProgID, IUnknown* pUnkOuter = nullptr, DWORD dwClsContext = CLSCTX_ALL)
    {
        CLSID clsid;
        HRESULT hr = CLSIDFromProgID(szProgID, &clsid);
        if (SUCCEEDED(hr))
        {
            hr = CoCreateInstance(clsid, pUnkOuter, dwClsContext);
        }
        return hr;
    }

    template<class Q>
    HRESULT QueryInterface(Q** pp)
    {
        if (!ptr) return E_POINTER;
        return ptr->QueryInterface(__uuidof(Q), (void**)pp);
    }
};

typedef DWORD OPCHANDLE;

enum class OPCDATASOURCE
{	OPC_DS_CACHE	= 1,
    OPC_DS_DEVICE	= OPC_DS_CACHE + 1
};

struct OPCITEMSTATE
{
    OPCHANDLE hClient;
    FILETIME ftTimeStamp;
    WORD wQuality;
    WORD wReserved;
    VARIANT vDataValue;
};

struct OPCITEMDEF
{
    LPWSTR szAccessPath;
    LPWSTR szItemID;
    BOOL bActive;
    OPCHANDLE hClient;
    DWORD dwBlobSize;
    /* [size_is] */ BYTE *pBlob;
    VARTYPE vtRequestedDataType;
    WORD wReserved;
};

struct OPCITEMRESULT
{
    OPCHANDLE hServer;
    VARTYPE vtCanonicalDataType;
    WORD wReserved;
    DWORD dwAccessRights;
    DWORD dwBlobSize;
    /* [size_is] */ BYTE *pBlob;
};

MIDL_INTERFACE("39c13a4d-011e-11d0-9675-0020afd8adb3")
IOPCServer : IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddGroup(
            LPCWSTR szName,
            BOOL bActive,
            DWORD dwRequestedUpdateRate,
            OPCHANDLE hClientGroup,
            /* [in][unique] */ LONG *pTimeBias,
            /* [in][unique] */ FLOAT *pPercentDeadband,
            DWORD dwLCID,
            /* [out] */ OPCHANDLE *phServerGroup,
            /* [out] */ DWORD *pRevisedUpdateRate,
            REFIID riid,
            /* [iid_is][out] */ LPUNKNOWN *ppUnk) = 0;
};

MIDL_INTERFACE("39c13a50-011e-11d0-9675-0020afd8adb3")
IOPCGroupStateMgt : IUnknown {};

MIDL_INTERFACE("39c13a52-011e-11d0-9675-0020afd8adb3")
IOPCSyncIO : IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Read(
            OPCDATASOURCE dwSource,
            DWORD dwCount,
            /* [size_is][in] */ OPCHANDLE *phServer,
            /* [size_is][out] */ OPCITEMSTATE **ppItemValues,
            /* [size_is][out] */ HRESULT **ppErrors) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
            DWORD dwCount,
            /* [size_is][in] */ OPCHANDLE *phServer,
            /* [size_is][in] */ VARIANT *pItemValues,
            /* [size_is][out] */ HRESULT **ppErrors) = 0;
};

MIDL_INTERFACE("39c13a54-011e-11d0-9675-0020afd8adb3")
IOPCItemMgt : IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddItems(
            /* [in] */ DWORD dwCount,
            /* [size_is][in] */ OPCITEMDEF *pItemArray,
            /* [size_is][out] */ OPCITEMRESULT **ppAddResults,
            /* [size_is][out] */ HRESULT **ppErrors) = 0;
};

class OPCDAClientSync
{
    ComPtr<IOPCSyncIO> m_pIOPCSyncIO;
    ComPtr<IOPCItemMgt> m_pIOPCItemMgt;
    ComPtr<IOPCServer> m_pIOPCServer;
    std::map<std::wstring, DWORD> m_server_handles;

    void AddItem (const std::wstring & name);

public:

    static void Initialize ();
    static void Uninitialize ();

    OPCDAClientSync (const std::wstring & serverProgID);
    std::wstring ReadItem (const std::wstring & name);
    void WriteItem (const std::wstring & name, const std::wstring & val);
};