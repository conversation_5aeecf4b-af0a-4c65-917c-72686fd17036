
#ifndef HAWKUSBCameraClient_USBCameraClient_H
#define HAWKUSBCameraClient_USBCameraClient_H

#include <Executor.h>
#include <string>
#include <vector>
#include <memory>
#include <thread>
#include <functional>
#include "VisionInputImage.h"
#include "Camera2DClient.h"
#include "opencv2/opencv.hpp"
#include <iostream>
#include <sstream>
#include <mutex>


namespace USB {
    namespace CAMERA {

        enum triggerType {
            SOFTWARE,
            HARDWARE
        };

        const triggerType chosenTrigger = SOFTWARE;


        class USBCameraClient : public Hawk::Camera::Common::Camera2DClient {
        public:
            USBCameraClient();

            virtual ~USBCameraClient();

            bool open(const std::string &key, Hawk::Camera::Common::TriggerMode mode) override;

            void close() override;

            std::shared_ptr<Hawk::Camera::Common::VisionInputImage> capture2DSync() override;

            void capture2DAsync(int tryCount = 1) override;

            void setCameraParam(const Hawk::Camera::Common::Camera2DParam &param) override;

            void setVisionInputImageCallBackFunction(
                    const Hawk::Camera::Common::VisionInputImageCallBackFunction &function) override { _function = function; }

            void setMessageCallBackFunction(
                    const Hawk::Camera::Common::MessageCallBackFunction &function) override { _messageCallBackFunction = function; }


            void setCameraAlias(std::string alias) override { _cameraAlias = alias; }

            bool isConnected() override { return _isOpen; }

            std::string getKey() override { return _id; }

            std::string getCameraAlias() override { return _cameraAlias; }

        private:

            bool initDeviceSetting();


        private:
            bool _isOpen;

            std::mutex m_ioMutex;
            cv::VideoCapture captureCamera;

            unsigned int _nCurValue = 0;
            unsigned char *_pData = nullptr;
            unsigned char *_pDataForRGB = nullptr;
            std::string _id;
            std::string _cameraAlias;
            Hawk::Camera::Common::TriggerMode _mode;
            Hawk::Camera::Common::Camera2DParam _param;

            std::shared_ptr<Fuxi::Common::Executor> _messageQueue;
            Hawk::Camera::Common::VisionInputImageCallBackFunction _function;
            Hawk::Camera::Common::MessageCallBackFunction _messageCallBackFunction;

            std::shared_ptr<Hawk::Camera::Common::VisionInputImage> capture();
        };
    }

}


#endif ///HAWKUSBCameraClient_USBCameraClient_H
