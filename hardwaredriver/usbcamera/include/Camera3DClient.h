
#pragma once

#include <string>
#include "VisionInputImage.h"

namespace Hawk {
    namespace Camera {
        namespace Common {
            struct Camera3DClient {
                virtual ~Camera3DClient() {};

                /**
                 * 打开相机
                 * @param id 相机的id
                 * @return
                 */
                virtual bool open(std::string id = "") = 0;

                /**
                 * 关闭相机
                 */
                virtual void close() = 0;

                /**
                 * 同步拍照
                 * @param captureMode 拍照模式,如果3d相机支持单独2d拍照可以使用此参数自定义
                 * @return 返回拍照图片,如果有多张就返回多张
                 */
                virtual std::vector<std::shared_ptr<Hawk::Camera::Common::VisionInputImage>>
                capture3DSync(int captureMode = 0) = 0;

                /**
                 * 设置相机拍照的回调函数
                 * @param function 照片回调函数
                 */
                virtual void setVisionInput3DImageCallBackFunction(
                        Hawk::Camera::Common::VisionInput3DImageCallBackFunction function) = 0;

                /**
                 * 相机异常状态通知回调函数,比如相机超时
                 * @param function 异常状态回调函数
                 */
                virtual void setMessageCallBackFunction(Hawk::Camera::Common::MessageCallBackFunction function) = 0;

                /**
                 * 拍照工作完成通知,此接口是为了异步提高性能,在相机拍照完成,没有生成图片数据之前通知外接可以进行后续的业务,比如两个相机连续拍照.
                 * @param function 相机拍照动作已经完成回调函数
                 */
                virtual void setCaptureFinishFunction(std::function<void()> function) {};

                /**
                 * 异步拍图
                 * @param tryCount　超时重拍次数，根据业务来调整
                 * @return
                 */
                virtual void capture3DASync() = 0;

                /**
                 * 获取相机的id
                 * @return
                 */
                virtual std::string getID() = 0;

                /**
                 * 获取相机状态
                 * @return
                 */
                virtual bool isConnected() = 0;

                /**
                 * 设置相机的别名,主要在多个相机应用中使用,最后在图片中会包含别名,用来区别图片来自那个相机.
                 * @param alias
                 */
                virtual void setCameraAlias(std::string alias) = 0;

                /**
                 *  获取别名
                 * @return
                 */
                virtual std::string getCameraAlias() = 0;
            };
        }
    }
}