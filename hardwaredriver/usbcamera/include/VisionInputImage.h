
#pragma once

#include "opencv2/opencv.hpp"
#include <memory>
#include <functional>

namespace Hawk {
    namespace Camera {
        namespace Common {
            /**
             * 图像类型
             */
            enum class ImageType {
                None = 0,
                /**
                 * 彩色图
                 */
                Color,
                /**
                 * 深度图
                 */
                Depth,
                /**
                 * 红外图
                 */
                Ir,
                /**
                 * 灰度图
                 */
                Mono,
                /**
                 * 点云图
                 */
                Cloud,
                /**
                 * 彩色点云图
                 */
                ColorCloud
            };

            /**
             * 相机拍照返回的图片
             */
            struct VisionInputImage {
                /**
                 * 图片数据
                 */
                cv::Mat image;
                /**
                 * 当前图片类型
                 */
                ImageType type;
                /**
                 * 相机的id
                 */
                std::string cameraID;
                /**
                 * 相机别名，主要是为了方便相关的配置，如模型更关注的是侧面相机还是顶面相机而不是相机ｉｄ
                 */
                std::string cameraAlias;
                /**
                 * 视觉参数，通常是一些视觉要用的鲜艳信息，
                 * 检测的项目中是由dataset里的方法提供，在传入视觉代码中会附带这些信息．
                 */
                std::map<std::string, std::string> visionParameters;

                VisionInputImage() { };

                VisionInputImage(cv::Mat image, ImageType type) : image(image), type(type) { };

            };

            /**
             * 图片回调函数
             */
            typedef std::function<void(std::shared_ptr<VisionInputImage>,
                                         std::string)> VisionInputImageCallBackFunction;
            /**
             * 多个图片回调函数
             */
            typedef std::function<void(
                    std::vector<std::shared_ptr<VisionInputImage>>)> VisionInput3DImageCallBackFunction;
            /**
             * 相机拍照状态的回调函数
             */
            typedef std::function<void(std::string, std::string, std::string)> MessageCallBackFunction;
        }
    }
};