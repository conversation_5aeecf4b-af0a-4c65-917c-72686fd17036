
#pragma once

#include <string>
#include "VisionInputImage.h"

namespace Hawk {
    namespace Camera {
        namespace Common {

            /**
             * 相机参数。 使用中一般优先使用相机客户端设置和保存参数
             *          在客户端设置不起作用或参数需要动态改变的情况下，再通过代码设置
             *          useXXX标识设为false时该项设置不起作用
             */
            struct Camera2DParam {
                bool useExposureTime = false;
                int exposureTime = 100000;

                bool useGain = false;
                float gain = 0;

                bool useWhiteBalance = false;
                float r = 1.;
                float g = 1.;
                float b = 1.;

                bool useBinn = false;   /// 是否启用Binn ///
                int binnH = 1;          /// 根据客户端选择参数 目前支持1、2///
                int binnV = 1;          /// 一般设置与binnH相同///
                std::string id;
            };

            /**
             * 相机触发模式
             */
            enum TriggerMode {
                SOFTWARE_TRIGGER,
                HARDWARE_TRIGGER
            };

            struct Camera2DClient {
                /**
                 * 打开并初始化相机
                 * @param key　有多个相机时，通过key来指定打开某个相机。key可以是序列号、ip或其他可标识相机的编号，跟具体的相机型号有关
                 *            若不指定或指定为空，有的相机可能默认打开第一个相机
                 * @param mode 指定相机的触发方式
                 * @return
                 */
                virtual bool open(const std::string &key, TriggerMode mode) = 0;

                virtual void close() = 0;

                virtual void setCameraParam(const Camera2DParam &param) = 0;

                /**
                 * 同步拍图
                 * @return
                 */
                virtual std::shared_ptr<Hawk::Camera::Common::VisionInputImage> capture2DSync() = 0;

                /**
                 * 异步拍图
                 * @param tryCount　超时重拍次数，根据业务来调整
                 * @return
                 */
                virtual void capture2DAsync(int tryCount) = 0;

                /**
                 * 出图回调函数。一般在两种情况下有效，一是使用异步拍图，二是使用硬触发
                 * @return
                 */
                virtual void
                setVisionInputImageCallBackFunction(
                        const Hawk::Camera::Common::VisionInputImageCallBackFunction &function) = 0;

                /**
                 * 此函数只用于异常通知，发出此通知意味着相机已经无法正常工作
                 * @param function
                 */
                virtual void setMessageCallBackFunction(
                        const Hawk::Camera::Common::MessageCallBackFunction &function) = 0;

                virtual bool isConnected() = 0;

                virtual std::string getKey() = 0;

                /**
                 * 设置相机别名，一般用于在有多个相机时方便区分相机
                 */
                virtual void setCameraAlias(std::string alias) = 0;

                virtual std::string getCameraAlias() = 0;
            };

        }
    }
}