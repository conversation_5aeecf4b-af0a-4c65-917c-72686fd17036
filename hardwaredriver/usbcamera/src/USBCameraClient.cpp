
#include "USBCameraClient.h"
#include <algorithm>
#include <chrono>
#include <glog.h>

using namespace Fuxi::Common;
using namespace Hawk::Camera::Common;


namespace USB { namespace CAMERA {

    USBCameraClient::USBCameraClient() {
        _messageQueue = std::make_shared<Executor>(1);
        _isOpen = false;
    }

    USBCameraClient::~USBCameraClient() {
        close();
    }

    bool USBCameraClient::open(const std::string &key, Hawk::Camera::Common::TriggerMode mode) {

        captureCamera = cv::VideoCapture(key);
        captureCamera.set(cv::CAP_PROP_FOURCC, cv::VideoWriter::fourcc('M', 'J', 'P', 'G'));
        captureCamera.set(cv::CAP_PROP_FRAME_WIDTH, 3264);
        captureCamera.set(cv::CAP_PROP_FRAME_HEIGHT, 2448);
        captureCamera.set(cv::CAP_PROP_FPS, 10);
        captureCamera.set(cv::CAP_PROP_AUTO_EXPOSURE, 1);
        captureCamera.set(cv::CAP_PROP_EXPOSURE, _param.exposureTime);
        if (captureCamera.isOpened())
            return true;
        else
            return false;
    }

    bool USBCameraClient::initDeviceSetting() {

        captureCamera.set(cv::CAP_PROP_FRAME_WIDTH, 3264);
        captureCamera.set(cv::CAP_PROP_FRAME_HEIGHT, 2448);
        ////    capture.set(CAP_PROP_FOURCC, FOURCC('M', 'J', 'P', 'G'));

        captureCamera.set(cv::CAP_PROP_FPS, 10);


        printf("width = %.2f\n", captureCamera.get(cv::CAP_PROP_FRAME_WIDTH));
        printf("height = %.2f\n", captureCamera.get(cv::CAP_PROP_FRAME_HEIGHT));
        printf("fbs = %.2f\n", captureCamera.get(cv::CAP_PROP_FPS));

        return true;
    }


    void USBCameraClient::close() {

        std::lock_guard<std::mutex> lock(m_ioMutex);
        captureCamera.release();
        if (!_isOpen)
            return;


    }

    cv::Mat WhiteBalcane_PRA(cv::Mat src) {
        cv::Mat result = src.clone();
        if (src.channels() != 3) {
            std::cout << "The number of image channels is not 3." << std::endl;
            return result;
        }


        std::vector<cv::Mat> Channel;
        cv::split(src, Channel);


        int row = src.rows;
        int col = src.cols;
        int RGBSum[766] = {0};
        uchar maxR, maxG, maxB;


        for (int i = 0; i < row; ++i) {
            uchar *b = Channel[0].ptr<uchar>(i);
            uchar *g = Channel[1].ptr<uchar>(i);
            uchar *r = Channel[2].ptr<uchar>(i);
            for (int j = 0; j < col; ++j) {
                int sum = b[j] + g[j] + r[j];
                RGBSum[sum]++;
                maxB = std::max<uchar>(maxB, b[j]);
                maxG = std::max<uchar>(maxG, g[j]);
                maxR = std::max<uchar>(maxR, r[j]);
            }
        }


        int T = 0;
        int num = 0;
        int K = static_cast<int>(row * col * 0.1);
        for (int i = 765; i >= 0; --i) {
            num += RGBSum[i];
            if (num > K) {
                T = i;
                break;
            }
        }


        double Bm = 0.0, Gm = 0.0, Rm = 0.0;
        int count = 0;
        for (int i = 0; i < row; ++i) {
            uchar *b = Channel[0].ptr<uchar>(i);
            uchar *g = Channel[1].ptr<uchar>(i);
            uchar *r = Channel[2].ptr<uchar>(i);
            for (int j = 0; j < col; ++j) {
                int sum = b[j] + g[j] + r[j];
                if (sum > T) {
                    Bm += b[j];
                    Gm += g[j];
                    Rm += r[j];
                    count++;
                }
            }
        }
        Bm /= count;
        Gm /= count;
        Rm /= count;


        Channel[0] *= maxB / Bm;
        Channel[1] *= maxG / Gm;
        Channel[2] *= maxR / Rm;


        cv::merge(Channel, result);

        return result;
    }

    std::shared_ptr<VisionInputImage> USBCameraClient::capture() {
        if (!captureCamera.isOpened())
            return {};

        cv::Mat frame;
        int max_image = 0;
        while (1) {
            captureCamera >> frame;

            flip(frame, frame, 1);
            cv::Mat src = frame;
            if (!frame.empty()) {
                cv::Mat color = src.clone();
                std::shared_ptr<VisionInputImage> image2d = std::make_shared<VisionInputImage>(color, ImageType::Mono);
                ////            cv::cvtColor(image2d->image, image2d->image, cv::COLOR_RGB2BGR);
                image2d->cameraID = _id;
                image2d->cameraAlias = _cameraAlias;

                _messageQueue->postTask([&, image2d]() {
                    if (_function) {
                        LOG(INFO) << "update camera capture image " << _id << " " << _cameraAlias;
                        _function(image2d, _id);
                    }
                });
////                image2d->image = WhiteBalcane_PRA(image2d->image);
                return image2d;
            }
        }
    }

    void USBCameraClient::setCameraParam(const Hawk::Camera::Common::Camera2DParam &param) {
        _param = param;
        initDeviceSetting();
    }

    std::shared_ptr<VisionInputImage> USBCameraClient::capture2DSync() {
        if (_mode == HARDWARE_TRIGGER) {
            LOG(ERROR) << "HARDWARE_TRIGGER mode capture2DSync is unused";
            return nullptr;
        }

        try {
            return capture();
        } catch (...) {
            LOG(WARNING) << "capture2DSync exception capture again";
            return nullptr;
        }
    }

    void USBCameraClient::capture2DAsync(int tryCount) {
        if (_mode == HARDWARE_TRIGGER) {
            LOG(ERROR) << "HARDWARE_TRIGGER mode capture2DAsync is unused";
            return;
        }

        _messageQueue->postTask([&, tryCount]() {
            try {
                capture();
            } catch (...) {
                int temp = tryCount;
                temp--;

                if (temp < 0) {
                    LOG(ERROR) << "capture2DAsync reach max try count ,return empty image    " << _id << "   "
                               << _cameraAlias;
                    cv::Mat color;
                    std::shared_ptr<VisionInputImage> image2d = std::make_shared<VisionInputImage>(color,
                                                                                                   ImageType::Mono);
                    image2d->cameraID = _id;
                    image2d->cameraAlias = _cameraAlias;
                    _messageQueue->postTask([&, image2d]() {
                        if (_function) {
                            LOG(INFO) << "update camera capture image " << _id;
                            _function(image2d, _id);
                        }
                    });
                    return;
                }
                LOG(ERROR) << "capture2DAsync timeout please try again  " << temp << "   " << _id << "   "
                           << _cameraAlias;
            }
        });
    }
} }
