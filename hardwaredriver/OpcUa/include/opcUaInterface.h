//
// Created by xu260 on 2023/12/15.
//

#ifndef FUXIOS_OPCUAINTERFACE_H
#define FUXIOS_OPCUAINTERFACE_H

#include <iostream>
#include <Executor.h>
#include <glog.h>
#include "open62541.h"

#define IN_Start 0
#define IN_Pause 1
#define IN_Reset 2
#define IN_emergency_stop 3
#define IN_Boot 4
#define IN_air_pressure 5

#define IN_feeding_level_detection  7
#define IN_identification_photo_limit_detection 8
#define IN_Blowout_inplace_detection 9
#define IN_Confirm_photo_inspection 10
#define IN_Intermediate_aisle_crossbar_detection 11
#define IN_Left_aisle_crossbar_detection 94
#define IN_middle_channel_plug_tray_in_place_detection  12
#define IN_Left_channel_plug_plate_arrival_detection 13

#define IN_flip_into_position 15
#define IN_Toggle_reset 16
#define IN_Pull_the_hole_tray_into_place 17
#define IN_pull_the_hole_tray_into_reset 18
#define IN_Center_Channel_Cylinder_Launched 19
#define IN_Middle_channel_cylinder_reset 20
#define IN_left_channel_cylinder_launch_twenty_one 21
#define IN_left_channel_cylinder_reset_twenty_two 22
#define IN_Operation_Light_twenty_three 23
#define IN_Pause_Light 24
#define IN_reset_light 25
#define IN_Buzzer 26
#define IN_Relay_24V_12V 27

#define IN_Light_Source 28
#define IN_Blow_up_flip_cylinder 31
#define IN_Blowing_up_the_gear_lever 32
#define IN_The_plug_tray_is_pulled_into_the_turning_claw 33
#define IN_Pulling_in_the_tray 34
#define IN_Intermediate_channel_cylinder 35
#define IN_Intermediate_Channel_Crossbar 36
#define IN_Left_channel_cylinder 37
#define IN_Middle_Blow 38
#define IN_Blowout_hole_1 39
#define IN_Blowout_Hole_2 40
#define IN_Blowout_hole_3 41
#define IN_Blowout_hole_4 42
#define IN_Blowout_hole_5 43
#define IN_Blowout_hole_6 44
#define IN_Soil_Blowing_Conveyor 45
#define IN_Main_conveyor_operation 46
#define IN_Slide_air_lever_1_ejection 47
#define IN_Pen_shaped_air_bar_1_ejection 48
#define IN_Slide_air_lever_2_ejection 49
#define IN_Pen_shaped_air_bar_2_ejection 50
#define IN_Slide_air_lever_3_ejection  51
#define IN_Pen_shaped_air_bar_3_ejection 52
#define IN_Slide_air_lever_4_ejection 53
#define IN_Pen_shaped_air_bar_4_ejection 54
#define IN_slide_air_lever_5_ejection 55
#define IN_Pen_shaped_air_bar_5_ejection 56
#define IN_Slide_air_lever_6_ejection 57
#define IN_Pen_shaped_air_bar_6_ejection 58
#define IN_Slide_air_lever_7_ejection 59
#define IN_Pen_shaped_air_bar_7_ejection 60
#define IN_Slide_air_lever_8_ejection 61
#define IN_Pen_shaped_air_bar_8_ejection 62
#define IN_slide_air_lever_9_ejection 63
#define IN_Pen_shaped_air_bar_9_ejection 64
#define IN_slide_air_lever_10_ejection 65
#define IN_Pen_shaped_air_bar_10_ejection 66
#define IN_Slide_table_air_bar_11_ejection 67
#define IN_Pen_shaped_air_bar_11_ejection 68
#define IN_Slide_table_air_bar_12_ejection 69
#define IN_Pen_shaped_air_bar_12_ejection 70


#define IN_Device_startup 71
#define IN_device_stopped 72
#define IN_Feeding_servo_is_running 73
#define IN_Feeding_servo_alarm 74
#define IN_Soil_blowing_servo_is_running 75
#define IN_Blowing_soil_servo_alarm 76
#define IN_Seedling_retrieval_servo_is_running 77
#define IN_Seedling_retrieval_servo_alarm 78
#define IN_The_seeding_servo_is_running 79
#define IN_Seedling_replenishment_servo_alarm 80
#define IN_The_left_and_right_servo_is_running 81
#define IN_Left_and_right_servo_alarm 82
#define IN_Up_and_down_servo_is_running 83
#define IN_Up_and_down_servo_alarm 84
#define IN_Feeding_servo_start_control 85
#define IN_Soil_plowing_servo_positioning_signal 86
#define IN_Seedling_replenishing_servo_go_to_origin_signal 87
#define IN_Seedling_patching_servo_positioning_signal 88
#define IN_Seedling_retrieval_servo_goes_to_origin_signal 89
#define IN_Seedling_retrieval_servo_positioning_signal 90
#define IN_Left_and_right_servo_positioning_signals 91
#define IN_Up_and_down_servo_positioning_signal 92
#define IN_Light_source_control_signal 93


#define OUT_Light_Source 205
#define OUT_Blow_up_flip_cylinder 208
#define OUT_Blowing_up_the_gear_lever 209
#define OUT_The_plug_tray_is_pulled_into_the_turning_claw 210
#define OUT_Pulling_in_the_tray 211
#define OUT_Intermediate_channel_cylinder 212
#define OUT_Intermediate_Channel_Crossbar 213
#define OUT_Left_channel_cylinder 214
#define OUT_Middle_Blow 215
#define OUT_Blowout_hole_1 216
#define OUT_Blowout_Hole_2 217
#define OUT_Blowout_hole_3 218
#define OUT_Blowout_hole_4 219
#define OUT_Blowout_hole_5 220
#define OUT_Blowout_hole_6 221
#define OUT_Soil_Blowing_Conveyor 222
#define OUT_Main_conveyor_operation 223
#define OUT_Slide_air_lever_1_ejection 224
#define OUT_Pen_shaped_air_bar_1_ejection 225
#define OUT_Slide_air_lever_2_ejection 226
#define OUT_Pen_shaped_air_bar_2_ejection 227
#define OUT_Slide_air_lever_3_ejection  228
#define OUT_Pen_shaped_air_bar_3_ejection 229
#define OUT_Slide_air_lever_4_ejection 230
#define OUT_Pen_shaped_air_bar_4_ejection 231
#define OUT_slide_air_lever_5_ejection 232
#define OUT_Pen_shaped_air_bar_5_ejection 233
#define OUT_Slide_air_lever_6_ejection 234
#define OUT_Pen_shaped_air_bar_6_ejection 235
#define OUT_Slide_air_lever_7_ejection 236
#define OUT_Pen_shaped_air_bar_7_ejection 237
#define OUT_Slide_air_lever_8_ejection 238
#define OUT_Pen_shaped_air_bar_8_ejection 239
#define OUT_slide_air_lever_9_ejection 240
#define OUT_Pen_shaped_air_bar_9_ejection 241
#define OUT_slide_air_lever_10_ejection 242
#define OUT_Pen_shaped_air_bar_10_ejection 243
#define OUT_Slide_table_air_bar_11_ejection 244
#define OUT_Pen_shaped_air_bar_11_ejection 245
#define OUT_Slide_table_air_bar_12_ejection 246
#define OUT_Pen_shaped_air_bar_12_ejection 247

#define OUT_Feeding_servo_start_control 248
#define OUT_Soil_plowing_servo_positioning_signal 249
#define OUT_Seedling_replenishing_servo_go_to_origin_signal 250
#define OUT_Seedling_patching_servo_positioning_signal 251
#define OUT_Seedling_retrieval_servo_goes_to_origin_signal 252
#define OUT_Seedling_retrieval_servo_positioning_signal 253
#define OUT_Left_and_right_servo_positioning_signals 254
#define OUT_Up_and_down_servo_positioning_signal 255
#define OUT_Light_source_control_signal 256

#define Get_blow_soil_Pose 0
#define Get_Take_seedlings_Pose 2
#define Get_Replenishing_seedlings_Pose 4
#define Get_left_and_right_Pose 6
#define Get_up_and_down_Pose 8

#define Set_blow_soil_Pose 50
#define Set_Take_seedlings_Pose 52
#define Set_Replenishing_seedlings_Pose 54
#define Set_left_and_right_Pose 56
#define Set_up_and_down_Pose 58






class opcUaInterface {
public:
    opcUaInterface();

    ~opcUaInterface();

    bool connect(const std::string &ip);

    bool GetInput(int port);

    void SetOutput(int port, int value);

    void SetOutputs(int port, std::vector<bool> value);

    void ResOutputs(int port, std::vector<bool> value);

    void SetOutputs(int port, std::vector<bool> value,int time);

    float GetPose(int port);

    void SetPose(int port, float pose);

    bool WaitGetInput(int port, int outTime=20);

    bool WaitGotoPose(int port, float pose,float offset=0.1);

    void ResetOutput();

private:

    UA_Client *_client{};

    bool opcUaConnect();

    bool opcUaDisconnect();

    int opcUaReadInt(int port);

    int opcUaReadBool(int port);

    bool opcUaWriteInt(int port,int value);

    float opcUaReadFloat(int port);

    bool opcUaWriteFloat(int port,float value);
};


#endif //FUXIOS_OPCUAINTERFACE_H
