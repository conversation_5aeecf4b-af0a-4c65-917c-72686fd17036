//
// Created by ASUS on 2023/11/26.
//

#ifndef FUXIOS_CONFIGDATA_H
#define FUXIOS_CONFIGDATA_H

#include <iostream>
#include <rttr/registration.h>
#include "JSON.h"
#include "fstream"
#include <rapidjson/document.h>
#include <rttr/type>
#include <opencv2/opencv.hpp>

namespace fuxi{

    // 结果结构体
    struct Result {
        int row;
        int col;
        bool ret;
        cv::Mat img;
    };

    struct ConfigData {
        std::string CameraID;
        std::string PlcIp;
        float Moto1PositiveLimit;
        float Moto1NegativeLimit;
        float Moto2PositiveLimit;
        float Moto2NegativeLimit;
        float Moto3PositiveLimit;
        float Moto3NegativeLimit;
        float Moto4PositiveLimit;
        float Moto4NegativeLimit;
        float Moto5PositiveLimit;
        float Moto5NegativeLimit;
        float Moto6PositiveLimit;
        float Moto6NegativeLimit;
    RTTR_ENABLE()
    };









RTTR_PLUGIN_REGISTRATION {
    rttr::registration::class_<ConfigData>("ConfigData")
            .constructor<>()(rttr::policy::ctor::as_object)
            .property("CameraID", &ConfigData::CameraID)
            .property("PlcIp", &ConfigData::PlcIp)
            .property("Moto1PositiveLimit", &ConfigData::Moto1PositiveLimit)
            .property("Moto1NegativeLimit", &ConfigData::Moto1NegativeLimit)
            .property("Moto2PositiveLimit", &ConfigData::Moto2PositiveLimit)
            .property("Moto2NegativeLimit", &ConfigData::Moto2NegativeLimit)
            .property("Moto3PositiveLimit", &ConfigData::Moto3PositiveLimit)
            .property("Moto3NegativeLimit", &ConfigData::Moto3NegativeLimit)
            .property("Moto4PositiveLimit", &ConfigData::Moto4PositiveLimit)
            .property("Moto4NegativeLimit", &ConfigData::Moto4NegativeLimit)
            .property("Moto5PositiveLimit", &ConfigData::Moto5PositiveLimit)
            .property("Moto5NegativeLimit", &ConfigData::Moto5NegativeLimit)
            .property("Moto6PositiveLimit", &ConfigData::Moto6PositiveLimit)
            .property("Moto6NegativeLimit", &ConfigData::Moto6NegativeLimit);


}

}
#endif //FUXIOS_CONFIGDATA_H
