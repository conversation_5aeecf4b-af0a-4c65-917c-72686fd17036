//
// Created by xu260 on 2023/12/15.
//

#include "opcUaInterface.h"
#include "timer.h"


opcUaInterface::~opcUaInterface() {
    opcUaDisconnect();
}

bool opcUaInterface::connect(const std::string &ip) {
    bool ret= opcUaConnect();
    return ret;
}

bool opcUaInterface::GetInput(int port) {
    if(port>200){

        if(opcUaReadInt(port)==1)
            return true;
        else
            return false;
    }else{

        if(opcUaReadBool(port)==1)
            return true;
        else
            return false;
    }

}

void opcUaInterface::SetOutput(int port, int value) {
    opcUaWriteInt(port,value);
}

void opcUaInterface::SetOutputs(int port, std::vector<bool> value) {
    for(int i=0;i<value.size();i++){
//        LOG(INFO)<<" i "<<i;
        if(value[i])
            opcUaWriteInt(port+i,1);
//        Sleep(1000);
    }
}

void opcUaInterface::ResOutputs(int port, std::vector<bool> value) {
    for(int i=0;i<value.size();i++){
        if(value[i])
            opcUaWriteInt(port+i,0);
    }
}

void opcUaInterface::SetOutputs(int port, std::vector<bool> value,int time) {
    for(int i=0;i<value.size();i++){
        if(value[i]){
            opcUaWriteInt(port+i,1);
            Sleep(time);
            opcUaWriteInt(port+i,0);
        }
    }
}

float opcUaInterface::GetPose(int port) {
    return opcUaReadFloat(port);
}

void opcUaInterface::SetPose(int port, float pose) {
    opcUaWriteFloat(port,pose);
}

bool opcUaInterface::WaitGetInput(int port, int outTime) {
    Timer t1;
    t1.reset();
    for (;;) {
        LOG(INFO)<<" WaitGetInput "<<port;
        if (GetInput(port)) {
            return true;
        }
        if (t1.elapsed_second() > outTime) {
            return false;
        }
        Sleep(100);
    }
}

bool opcUaInterface::WaitGotoPose(int port, float pose,float offset) {
    for (;;) {
        LOG(INFO)<<std::abs(pose - GetPose(port));
        if (std::abs(pose - GetPose(port)) < offset)
            break;
        Sleep(100);
    }
    return true;
}

void opcUaInterface::ResetOutput() {
    for(int i=200;i<300;i++){
        SetOutput(i,0);
    }
}

bool opcUaInterface::opcUaConnect() {
    _client = UA_Client_new();
    UA_ClientConfig_setDefault(UA_Client_getConfig(_client));

    /* 连接到服务器 */
    UA_StatusCode retval = UA_Client_connect(_client, "opc.tcp://127.0.0.1:49320");
    if(retval != UA_STATUSCODE_GOOD) {
        UA_Client_delete(_client);
        return false;
    }
    return true;
}

bool opcUaInterface::opcUaDisconnect() {
    UA_Client_disconnect(_client);
    UA_Client_delete(_client);
    return true;
}

int opcUaInterface::opcUaReadInt(int port) {
    std::string tmpport="channel1.device1.W"+std::to_string(port);
    char* cstr = new char[tmpport.length() + 1];
    std::strcpy(cstr, tmpport.c_str());
    /* 读取节点的值 */
    UA_Variant value; /* Variants can hold scalar values and arrays of any type */
    UA_Variant_init(&value);
    const UA_NodeId nodeId = UA_NODEID_STRING(2, cstr);
    auto retval = UA_Client_readValueAttribute(_client, nodeId, &value);

    if(retval == UA_STATUSCODE_GOOD &&
       UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_UINT16])) {
        UA_UInt16 value_int16 = *(UA_UInt16*)value.data;
//        LOG(INFO)<<"value_int16 "<<port<<"  "<<value_int16;
        return value_int16;
    }
    return -1;
}

int opcUaInterface::opcUaReadBool(int port) {
    std::string tmpport="channel1.device1.W"+std::to_string(port);
    char* cstr = new char[tmpport.length() + 1];
    std::strcpy(cstr, tmpport.c_str());
    /* 读取节点的值 */
    UA_Variant value; /* Variants can hold scalar values and arrays of any type */
    UA_Variant_init(&value);
    const UA_NodeId nodeId = UA_NODEID_STRING(2, cstr);
    auto retval = UA_Client_readValueAttribute(_client, nodeId, &value);

    if(retval == UA_STATUSCODE_GOOD &&
       UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_BOOLEAN])) {
        UA_Boolean value_bool = *(UA_Boolean*)value.data;
//        LOG(INFO)<<"value_bool "<<port<<"  "<<value_bool;
        if(value_bool)
            return 1;
        else
            return 0;
    }
    return -1;
}

bool opcUaInterface::opcUaWriteInt(int port, int value) {
    std::string tmpport="channel1.device1.W"+std::to_string(port);
    char* cstr = new char[tmpport.length() + 1];
    std::strcpy(cstr, tmpport.c_str());

    UA_Variant tmpvalue; /* Variants can hold scalar values and arrays of any type */
    UA_Variant_init(&tmpvalue);
    const UA_NodeId nodeId = UA_NODEID_STRING(2, cstr);
    UA_UInt16 newValue = value;
    UA_Variant_setScalar(&tmpvalue, &newValue, &UA_TYPES[UA_TYPES_UINT16]);
    auto retval = UA_Client_writeValueAttribute(_client, nodeId, &tmpvalue);
    if(retval != UA_STATUSCODE_GOOD) {
        return false;
    }
    return true;
}

float opcUaInterface::opcUaReadFloat(int port) {
    std::string tmpport="channel1.device1.F"+std::to_string(port);
    char* cstr = new char[tmpport.length() + 1];
    std::strcpy(cstr, tmpport.c_str());

    /* 读取节点的值 */
    UA_Variant value; /* Variants can hold scalar values and arrays of any type */
    UA_Variant_init(&value);
    const UA_NodeId nodeId = UA_NODEID_STRING(2, cstr);
    auto retval = UA_Client_readValueAttribute(_client, nodeId, &value);
    if (retval == UA_STATUSCODE_GOOD &&
        UA_Variant_hasScalarType(&value, &UA_TYPES[UA_TYPES_FLOAT])) {
        UA_Float value_float = *(UA_Float *) value.data;
//        printf("the value is: %f\n", value_float);
        return value_float;
    }
    return -1.0;
}

bool opcUaInterface::opcUaWriteFloat(int port, float value) {
    std::string tmpport="channel1.device1.F"+std::to_string(port);
    char* cstr = new char[tmpport.length() + 1];
    std::strcpy(cstr, tmpport.c_str());

    UA_Variant tmpvalue; /* Variants can hold scalar values and arrays of any type */
    UA_Variant_init(&tmpvalue);
    const UA_NodeId nodeId = UA_NODEID_STRING(2, cstr);
    UA_Float newValue = value;

    UA_Variant_setScalar(&tmpvalue, &newValue, &UA_TYPES[UA_TYPES_FLOAT]);
    auto retval = UA_Client_writeValueAttribute(_client, nodeId, &tmpvalue);
    if(retval != UA_STATUSCODE_GOOD) {
        return false;
    }
    return true;
}

opcUaInterface::opcUaInterface() {

}

