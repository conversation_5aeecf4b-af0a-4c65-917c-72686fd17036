#pragma once


#include "aubo_sdk/rpc.h"
#include <math.h>
#include <Executor.h>
#include <rw/math/Q.hpp>
#include <rw/math/Transform3D.hpp>
#include <boost/function.hpp>
#include "rw/math.hpp"
#include "rw/trajectory.hpp"


using namespace arcs::common_interface;
using namespace arcs::aubo_sdk;


class AuboArcsDriver {
public:
    enum Aubo_Teach_Mode
    {
        NO_TEACH = 0,
        JOINT1,
        JOINT2,
        JOINT3,
        JOINT4,
        JOINT5,
        JOINT6,
        MOV_X,
        MOV_Y,
        MOV_Z,
        ROT_X,
        ROT_Y,
        ROT_Z
    };

    AuboArcsDriver();

    ~AuboArcsDriver();

    bool connect(std::string ip, int port);

    bool stop();

    bool isConnect();

    bool movej(rw::math::Q q, double tcpSpeed, int zone, double acc);

    void movel(rw::math::Transform3D<> poseTarget, double tcpSpeed, int zone, int acc);

    void setOutValue(int portIndex, bool val, bool reset=false);

    int getInputValue(int ioNum);

    bool setAcceleration(int acc, int ramp);

    rw::math::Transform3D<> getRobotActualTCP();

    rw::math::Q getRobotJointQ();

    bool getFK(rw::math::Q joint, rw::math::Transform3D<> &pos);


    bool getIK(rw::math::Q joint, rw::math::Transform3D<> pos,
               rw::math::Q &outjoint);


    void robotServiceTeachStart(Aubo_Teach_Mode mode, bool direction, int speedPercent);

    void robotServiceTeachStop();

    std::string _ip;

    int _port = 8899;

private:

    std::string _user = "aubo";
    std::string _pswd = "123456";
    bool _isClientOpened;
    std::shared_ptr<RpcClient> _rpc_cli;

    std::shared_ptr<RobotInterface> _robot_interface;

    std::vector<double> _preJointVel;
    std::vector<double> _curJointVel;

    std::shared_ptr<Fuxi::Common::Executor> _message, _getmessage;


    int waitArrival();

    int checkJointMove(rw::math::Q target_joint);

};

