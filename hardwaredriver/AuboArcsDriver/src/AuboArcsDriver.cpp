//
// Created by cobot on 2021/1/8.
//

#include "AuboArcsDriver.h"
#include "glog.h"
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>



AuboArcsDriver::AuboArcsDriver() {
    _message = std::make_shared<Fuxi::Common::Executor>(1);
    _getmessage = std::make_shared<Fuxi::Common::Executor>(1);
    _isClientOpened = false;
}

AuboArcsDriver::~AuboArcsDriver() {
    _rpc_cli->disconnect();
}

bool AuboArcsDriver::connect(std::string ip, int port) {
    _rpc_cli = std::make_shared<RpcClient>();
    // 接口调用: 设置 RPC 超时
    _rpc_cli->setRequestTimeout(1000);
    // 接口调用: 连接到 RPC 服务
    int ret = _rpc_cli->connect(ip, 30004);


    if (ret == 0)
        std::cout << "rpc连接成功！" << std::endl;
    else {
        std::cout << "rpc连接失败！" << std::endl;
        _isClientOpened = false;
        return false;
    }
    // 接口调用: 登录
    _rpc_cli->login("aubo", "123456");
    auto robot_name = _rpc_cli->getRobotNames().front();

    _robot_interface = _rpc_cli->getRobotInterface(robot_name);
    _isClientOpened = true;
    return true;
}


bool AuboArcsDriver::stop() {
    _rpc_cli->logout();
    _isClientOpened = false;
    return true;
}

bool AuboArcsDriver::isConnect() {
    return _isClientOpened;
}

bool AuboArcsDriver::movej(rw::math::Q q, double tcpSpeed, int zone, double acc) {
    if (!_isClientOpened)
        return false;
        
    // 先检查目标位置是否过近
    if (checkJointMove(q.toStdVector()) == -1) {
        std::cout << "目标位置与当前位置过近，无需移动" << std::endl;
        return true;
    }

    _robot_interface->getMotionControl()->setSpeedFraction(0.8);

    // 接口调用: 关节运动
    _robot_interface->getMotionControl()->moveJoint(
            q.toStdVector(), acc, tcpSpeed / 2.0, 0, 0);
    // 阻塞
    int ret = waitArrival();
    if (ret == 0) {
        std::cout << "关节运动到路点1成功" << std::endl;
    } else {
        std::cout << "关节运动到路点1失败" << std::endl;
    }

    return true;
}

int AuboArcsDriver::waitArrival() {
    const int max_retry_count = 5;
    int cnt = 0;

    //接口调用: 获取当前的运动指令 ID
    int exec_id = _robot_interface->getMotionControl()->getExecId();

    // 等待机械臂开始运动
    while (exec_id == -1) {
        if (cnt++ > max_retry_count) {
            return -1;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
        exec_id = _robot_interface->getMotionControl()->getExecId();
    }

    // 等待机械臂动作完成
    while (_robot_interface->getMotionControl()->getExecId() != -1) {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));
    }

    return 0;
}

int AuboArcsDriver::checkJointMove(rw::math::Q target_joint) {
    double threshold = 1.0 / 180 * M_PI;

    // 接口调用: 获取机械臂当前的关节角，单位弧度
    std::vector<double> current_joint =
            _robot_interface->getRobotState()->getJointPositions();

    for (int i = 0; i < 6; i++) {
        if (std::abs(current_joint[i] - target_joint[i]) >= threshold) {
            return 0;
        }
    }

//    std::cout << "当前位置距离目标位置过近，可能会导致关节运动失败."
//              << std::endl;

    return -1;
}


void AuboArcsDriver::movel(rw::math::Transform3D<> poseTarget, double tcpSpeed, int zone, int acc) {
    if (!_isClientOpened)
        return;
    _robot_interface->getMotionControl()->setSpeedFraction(0.8);
    auto p = poseTarget.P();
    rw::math::RPY<> rpy(poseTarget.R());
    std::vector<double> pose = {p[0], p[1], p[2],
                                rpy[2], rpy[1], rpy[0]};
    _robot_interface->getMotionControl()->moveLine(pose, acc, tcpSpeed, zone / 1000.0, 0);
    // 阻塞
    waitArrival();
}

void AuboArcsDriver::setOutValue(int portIndex, bool val, bool reset) {
    _robot_interface->getIoControl()->setStandardDigitalOutput(portIndex, val);

}

int AuboArcsDriver::getInputValue(int ioNum) {
    return _robot_interface->getIoControl()->getStandardDigitalOutput(ioNum);
}

bool AuboArcsDriver::setAcceleration(int acc, int ramp) {
    return false;
}

rw::math::Transform3D<> AuboArcsDriver::getRobotActualTCP() {
    if(!_isClientOpened)
        return  rw::math::Transform3D<>({0, 0, 0},
                                   rw::math::RPY<>{0, 0, 0});
    std::vector<double> current_pose =
            _robot_interface->getRobotState()->getTcpPose();


    return rw::math::Transform3D<>({current_pose[0], current_pose[1], current_pose[2]},
                                   rw::math::RPY<>{current_pose[5], current_pose[4], current_pose[3]});

}

rw::math::Q AuboArcsDriver::getRobotJointQ() {
    if(!_isClientOpened)
        return rw::math::Q(6,0,0,0,0,0,0);
    std::vector<double> current_joint =
            _robot_interface->getRobotState()->getJointPositions();
    return rw::math::Q(current_joint);

}


bool AuboArcsDriver::getIK(rw::math::Q joint, rw::math::Transform3D<> pos, rw::math::Q &outjoint) {
    auto p = pos.P();
    auto rpy = rw::math::RPY<>(pos.R());
    auto result = _robot_interface->getRobotAlgorithm()
            ->inverseToolKinematics(joint.toStdVector(), {p[0], p[1], p[2], rpy[2], rpy[1], rpy[0]});
    if (std::get<1>(result) != 0) {
        std::cerr << "getIK失败" << std::endl;
        return false;
    }
    outjoint = rw::math::Q(std::get<0>(result));
    return true;
}

bool AuboArcsDriver::getFK(rw::math::Q joint, rw::math::Transform3D<> &pos) {


    auto result = _robot_interface->getRobotAlgorithm()
            ->forwardToolKinematics(joint.toStdVector());
    if (std::get<1>(result) != 0) {
        LOG(INFO)<<"getFK失败-----------------------------------";
//        std::cout << "getFK失败" << std::endl;
        return false;
    }
    auto resultPose = std::get<0>(result);

    pos = rw::math::Transform3D<>({resultPose[0], resultPose[1], resultPose[2]},
                                  rw::math::RPY<>{resultPose[5], resultPose[4], resultPose[3]});
    return true;
}


void AuboArcsDriver::robotServiceTeachStart(Aubo_Teach_Mode mode, bool direction, int speedPercent) {
    if (speedPercent <= 0)
        return;
    if (speedPercent >= 100)
        speedPercent = 100;
    std::vector<double> Velcs = std::vector<double>(6, 0.0);
    double moveLineMaxAcc = 1.0 * speedPercent / 100.0;
    double moveLineMaxVelc = 1.0 * speedPercent / 100.0;
    double moveAngleMaxAcc = 90.0 * speedPercent / 100.0;
    double moveAngleMaxVelc = 90.0 * speedPercent / 100.0;
    if (mode == Aubo_Teach_Mode::JOINT1) {
        Velcs[0] = moveAngleMaxVelc;
    } else if (mode == Aubo_Teach_Mode::JOINT2) {
        Velcs[1] = moveAngleMaxVelc;
    } else if (mode == Aubo_Teach_Mode::JOINT3) {
        Velcs[2] = moveAngleMaxVelc;
    } else if (mode == Aubo_Teach_Mode::JOINT4) {
        Velcs[3] = moveAngleMaxVelc;
    } else if (mode == Aubo_Teach_Mode::JOINT5) {
        Velcs[4] = moveAngleMaxVelc;
    } else if (mode == Aubo_Teach_Mode::JOINT6) {
        Velcs[5] = moveAngleMaxVelc;
    } else if (mode == Aubo_Teach_Mode::MOV_X) {
        Velcs[0] = moveLineMaxVelc;
    } else if (mode == Aubo_Teach_Mode::MOV_Y) {
        Velcs[1] = moveLineMaxVelc;
    } else if (mode == Aubo_Teach_Mode::MOV_Z) {
        Velcs[2] = moveLineMaxVelc;
    } else if (mode == Aubo_Teach_Mode::ROT_X) {
        Velcs[3] = moveLineMaxVelc;
    } else if (mode == Aubo_Teach_Mode::ROT_Y) {
        Velcs[4] = moveLineMaxVelc;
    } else if (mode == Aubo_Teach_Mode::ROT_Z) {
        Velcs[5] = moveLineMaxVelc;
    }

    if (mode < 7)
        _robot_interface->getMotionControl()->speedJoint(Velcs, moveAngleMaxAcc, 1);
    else
        _robot_interface->getMotionControl()->speedLine(Velcs, moveLineMaxAcc, 1);
}


void AuboArcsDriver::robotServiceTeachStop() {
    _robot_interface->getMotionControl()->stopMove(true, true);
}

