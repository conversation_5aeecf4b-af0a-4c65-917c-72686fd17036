#include "../include/stdafx.h"
#include "AuboRobot.h"
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <iostream>
#include <math.h>
#include <rw/math/Q.hpp>
#include <rw/math/Transform3D.hpp>
#include <chrono>
#include <thread>

AuboRobot::AuboRobot() 
    : robot_handle_(0)
    , is_connected_(false)
    , is_started_(false)
    , joint_velocity_(0.5) // 默认速度 0.5 rad/s
    , joint_acceleration_(1.0) // 默认加速度 1.0 rad/s^2
    , tool_velocity_(0.25) // 默认速度 0.25 m/s
    , tool_acceleration_(0.5) // 默认加速度 0.5 m/s^2
{
}

AuboRobot::~AuboRobot() {
    if (is_started_) {
        shutdown();
    }
    if (is_connected_) {
        disconnect();
    }
}

bool AuboRobot::connect(const char* ip, int port) {
    if (is_connected_) {
        return true;
    }

    // 初始化接口库
    if (rs_initialize() != RS_SUCC) {
        std::cerr << "初始化接口库失败" << std::endl;
        return false;
    }

    // 创建上下文
    if (rs_create_context(&robot_handle_) != RS_SUCC) {
        std::cerr << "创建上下文失败" << std::endl;
        return false;
    }

    // 登录机器人
    if (rs_login(robot_handle_, ip, port) != RS_SUCC) {
        std::cerr << "登录机器人失败" << std::endl;
        return false;
    }

    is_connected_ = true;
    return true;
}

bool AuboRobot::disconnect() {
    if (!is_connected_) {
        return true;
    }

    if (rs_logout(robot_handle_) != RS_SUCC) {
        std::cerr << "注销失败" << std::endl;
        return false;
    }

    is_connected_ = false;
    return true;
}

bool AuboRobot::startup() {
    if (!is_connected_) {
        std::cerr << "请先连接机器人" << std::endl;
        return false;
    }

    if (is_started_) {
        return true;
    }

    ToolDynamicsParam tool_dynamics = {0};
    uint8 colli_class = 6;
    bool read_pos = true;
    bool static_colli_detect = true;
    int board_maxacc = 30000;
    ROBOT_SERVICE_STATE state = ROBOT_SERVICE_READY;

    if (rs_robot_startup(robot_handle_, &tool_dynamics, colli_class, 
                        read_pos, static_colli_detect, board_maxacc, &state) != RS_SUCC) {
        std::cerr << "启动机器人失败" << std::endl;
        return false;
    }

    is_started_ = true;
    return true;
}

bool AuboRobot::shutdown() {
    if (!is_started_) {
        return true;
    }

    if (rs_robot_shutdown(robot_handle_) != RS_SUCC) {
        std::cerr << "关闭机器人失败" << std::endl;
        return false;
    }

    is_started_ = false;
    return true;
}

bool AuboRobot::moveJoint(const rw::math::Q& jointAngles, 
                         double velocity, 
                         double acceleration) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    // 设置关节运动参数
    JointVelcAccParam max_acc = {acceleration > 0 ? acceleration : joint_acceleration_};
    JointVelcAccParam max_vel = {velocity > 0 ? velocity : joint_velocity_};
    rs_set_global_joint_maxacc(robot_handle_, &max_acc);
    rs_set_global_joint_maxvelc(robot_handle_, &max_vel);

    // 转换为数组并执行运动
    double angles[ARM_DOF];
    for(size_t i = 0; i < ARM_DOF; i++) {
        angles[i] = jointAngles[i];
    }

    if(rs_move_joint(robot_handle_, angles) != RS_SUCC) {
        return false;
    }

    // 等待到达目标位置或超时
    auto startTime = std::chrono::steady_clock::now();
    while(!isReached(jointAngles)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(int(CHECK_INTERVAL * 1000)));
        
        auto currentTime = std::chrono::steady_clock::now();
        double elapsedTime = std::chrono::duration<double>(currentTime - startTime).count();
        
        if(elapsedTime > DEFAULT_TIMEOUT) {
            std::cerr << "运动超时，未到达目标位置" << std::endl;
            return false;
        }
    }

    return true;
}

bool AuboRobot::moveLine(const rw::math::Transform3D<>& target,
                        double velocity,
                        double acceleration) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    // 设置直线运动参数
    double useVel = velocity > 0 ? velocity : tool_velocity_;
    double useAcc = acceleration > 0 ? acceleration : tool_acceleration_;
    rs_set_global_end_max_line_acc(robot_handle_, useAcc);
    rs_set_global_end_max_line_velc(robot_handle_, useVel);

    // 获取位置
    rw::math::Vector3D<> translation = target.P();
    
    // 从旋转矩阵计算RPY角度
    rw::math::Rotation3D<> rot = target.R();
    
    // 计算RPY角度 (按照Z-Y-X顺序)
    double beta = atan2(-rot(2,0), sqrt(rot(0,0)*rot(0,0) + rot(1,0)*rot(1,0)));
    double alpha, gamma;
    
    if(fabs(beta - M_PI/2) < 1e-6) {
        alpha = 0;
        gamma = atan2(rot(0,1), rot(1,1));
    } else if(fabs(beta + M_PI/2) < 1e-6) {
        alpha = 0;
        gamma = -atan2(rot(0,1), rot(1,1));
    } else {
        alpha = atan2(rot(1,0)/cos(beta), rot(0,0)/cos(beta));
        gamma = atan2(rot(2,1)/cos(beta), rot(2,2)/cos(beta));
    }

    // 转换为位置数组 (x,y,z,rx,ry,rz)
    double pos[6];
    pos[0] = translation[0];
    pos[1] = translation[1];
    pos[2] = translation[2];
    pos[3] = gamma;  // Roll
    pos[4] = beta;   // Pitch
    pos[5] = alpha;  // Yaw

    if(rs_move_line(robot_handle_, pos) != RS_SUCC) {
        return false;
    }

    // 等待到达目标位置或超时
    auto startTime = std::chrono::steady_clock::now();
    while(!isReached(target)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(int(CHECK_INTERVAL * 1000)));
        
        auto currentTime = std::chrono::steady_clock::now();
        double elapsedTime = std::chrono::duration<double>(currentTime - startTime).count();
        
        if(elapsedTime > DEFAULT_TIMEOUT) {
            std::cerr << "运动超时，未到达目标位置" << std::endl;
            return false;
        }
    }

    return true;
}

bool AuboRobot::moveToPos(const rw::math::Vector3D<>& pos, 
                         double joint6Angle,
                         double velocity,
                         double acceleration) {
    return moveToPos(pos[0], pos[1], pos[2], joint6Angle, velocity, acceleration);
}

bool AuboRobot::moveToPos(double x, double y, double z, 
                         double joint6Angle,
                         double velocity,
                         double acceleration) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    // 设置关节运动参数
    JointVelcAccParam max_acc = {acceleration > 0 ? acceleration : joint_acceleration_};
    JointVelcAccParam max_vel = {velocity > 0 ? velocity : joint_velocity_};
    rs_set_global_joint_maxacc(robot_handle_, &max_acc);
    rs_set_global_joint_maxvelc(robot_handle_, &max_vel);

    // 获取当前路点信息
    aubo_robot_namespace::wayPoint_S wayPoint;
    aubo_robot_namespace::wayPoint_S targetPoint;
    double targetRadian[ARM_DOF] = {0};
    
    Pos pos = {x, y, z};

    if (rs_get_current_waypoint(robot_handle_, &wayPoint) != RS_SUCC) {
        std::cerr << "获取当前位置失败" << std::endl;
        return false;
    }

    // 逆解获取目标关节角度
    if (rs_inverse_kin(robot_handle_, wayPoint.jointpos, &pos, &wayPoint.orientation, &targetPoint) != RS_SUCC) {
        std::cerr << "逆解失败" << std::endl;
        return false;
    }

    // 设置目标位置
    for (int i = 0; i < 5; i++) {
        targetRadian[i] = targetPoint.jointpos[i];
    }
    targetRadian[5] = joint6Angle / 180.0 * M_PI;

    // 执行运动并等待完成
    if(rs_move_joint(robot_handle_, targetRadian) != RS_SUCC) {
        return false;
    }

    // 等待到达目标位置或超时
    auto startTime = std::chrono::steady_clock::now();
    rw::math::Q targetJoint(ARM_DOF, targetRadian);
    while(!isReached(targetJoint)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(int(CHECK_INTERVAL * 1000)));
        
        auto currentTime = std::chrono::steady_clock::now();
        double elapsedTime = std::chrono::duration<double>(currentTime - startTime).count();
        
        if(elapsedTime > DEFAULT_TIMEOUT) {
            std::cerr << "运动超时，未到达目标位置" << std::endl;
            return false;
        }
    }

    return true;
}

bool AuboRobot::setDO(const char* pin, bool value) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    return rs_set_board_io_status_by_name(robot_handle_, RobotBoardUserDO, pin, 
                                         value ? IO_STATUS_VALID : IO_STATUS_INVALID) == RS_SUCC;
}

bool AuboRobot::setToolDO(const char* pin, bool value) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    return rs_set_tool_do_status(robot_handle_, pin, 
                                value ? IO_STATUS_VALID : IO_STATUS_INVALID) == RS_SUCC;
}

bool AuboRobot::getDO(const char* pin, double& status) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    return rs_get_board_io_status_by_name(robot_handle_, RobotBoardUserDO, pin, &status) == RS_SUCC;
}

bool AuboRobot::getToolDO(const char* pin, double& status) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    return rs_get_tool_io_status(robot_handle_, pin, &status) == RS_SUCC;
}

bool AuboRobot::getCurrentPosition(aubo_robot_namespace::wayPoint_S& wayPoint) {
    if (!is_started_) {
        std::cerr << "请先启动机器人" << std::endl;
        return false;
    }

    return rs_get_current_waypoint(robot_handle_, &wayPoint) == RS_SUCC;
}

// 速度和加速度设置函数
bool AuboRobot::setJointVelocity(double velocity) {
    if (velocity <= 0) {
        std::cerr << "关节速度必须大于0" << std::endl;
        return false;
    }
    if (velocity > MAX_JOINT_VELOCITY_1_3) {
        std::cerr << "警告: 关节速度超过最大限制 (" 
                  << MAX_JOINT_VELOCITY_1_3 << " rad/s, " 
                  << MAX_JOINT_VELOCITY_1_3 * 180.0 / M_PI << "°/s)" << std::endl;
        return false;
    }
    joint_velocity_ = velocity;
    return true;
}

bool AuboRobot::setJointAcceleration(double acceleration) {
    if (acceleration <= 0) {
        std::cerr << "关节加速度必须大于0" << std::endl;
        return false;
    }
    if (acceleration > MAX_JOINT_ACCELERATION) {
        std::cerr << "警告: 关节加速度超过最大限制 (" 
                  << MAX_JOINT_ACCELERATION << " rad/s^2)" << std::endl;
        return false;
    }
    joint_acceleration_ = acceleration;
    return true;
}

bool AuboRobot::setToolVelocity(double velocity) {
    if (velocity <= 0) {
        std::cerr << "工具速度必须大于0" << std::endl;
        return false;
    }
    if (velocity > MAX_TOOL_VELOCITY) {
        std::cerr << "警告: 工具速度超过最大限制 (" 
                  << MAX_TOOL_VELOCITY << " m/s)" << std::endl;
        return false;
    }
    tool_velocity_ = velocity;
    return true;
}

bool AuboRobot::setToolAcceleration(double acceleration) {
    if (acceleration <= 0) {
        std::cerr << "工具加速度必须大于0" << std::endl;
        return false;
    }
    if (acceleration > MAX_TOOL_ACCELERATION) {
        std::cerr << "警告: 工具加速度超过最大限制 (" 
                  << MAX_TOOL_ACCELERATION << " m/s^2)" << std::endl;
        return false;
    }
    tool_acceleration_ = acceleration;
    return true;
}

bool AuboRobot::getCurrentJointPos(rw::math::Q& pos) {
    aubo_robot_namespace::wayPoint_S wayPoint;
    if(!getCurrentPosition(wayPoint)) {
        return false;
    }
    
    pos = rw::math::Q(ARM_DOF);
    for(size_t i = 0; i < ARM_DOF; i++) {
        pos[i] = wayPoint.jointpos[i];
    }
    return true;
}

bool AuboRobot::getCurrentCartesianPos(rw::math::Transform3D<>& pos) {
    aubo_robot_namespace::wayPoint_S wayPoint;
    if(!getCurrentPosition(wayPoint)) {
        return false;
    }

    // 构造Transform3D
    rw::math::Vector3D<> translation(wayPoint.cartPos.position.x,
                                   wayPoint.cartPos.position.y, 
                                   wayPoint.cartPos.position.z);
    
    // 从四元数构造旋转矩阵
    double w = wayPoint.orientation.w;
    double x = wayPoint.orientation.x;
    double y = wayPoint.orientation.y;
    double z = wayPoint.orientation.z;
    
    // 四元数转旋转矩阵
    double R11 = 1 - 2*y*y - 2*z*z;
    double R12 = 2*x*y - 2*w*z;
    double R13 = 2*x*z + 2*w*y;
    double R21 = 2*x*y + 2*w*z;
    double R22 = 1 - 2*x*x - 2*z*z;
    double R23 = 2*y*z - 2*w*x;
    double R31 = 2*x*z - 2*w*y;
    double R32 = 2*y*z + 2*w*x;
    double R33 = 1 - 2*x*x - 2*y*y;
    
    rw::math::Rotation3D<> rotation(R11, R12, R13,
                                   R21, R22, R23,
                                   R31, R32, R33);
    
    pos = rw::math::Transform3D<>(translation, rotation);
    return true;
}

bool AuboRobot::isReached(const rw::math::Q& targetJoint, double tolerance) const {
    rw::math::Q currentPos(ARM_DOF);
    if(!const_cast<AuboRobot*>(this)->getCurrentJointPos(currentPos)) {
        return false;
    }
    
    // 检查每个关节是否都在容差范围内
    for(size_t i = 0; i < ARM_DOF; i++) {
        if(std::abs(currentPos[i] - targetJoint[i]) > tolerance) {
            return false;
        }
    }
    return true;
}

bool AuboRobot::isReached(const rw::math::Transform3D<>& targetPose, 
                         double posTolerance, double rotTolerance) const {
    rw::math::Transform3D<> currentPose;
    if(!const_cast<AuboRobot*>(this)->getCurrentCartesianPos(currentPose)) {
        return false;
    }
    
    // 检查位置误差
    rw::math::Vector3D<> posError = targetPose.P() - currentPose.P();
    if(posError.norm2() > posTolerance) {
        return false;
    }
    
    // 检查旋转误差 (使用旋转矩阵的差值范数)
    rw::math::Rotation3D<> rotError = targetPose.R() * inverse(currentPose.R());
    double rotErrorNorm = sqrt(pow(rotError(0,1) - rotError(1,0), 2) +
                             pow(rotError(0,2) - rotError(2,0), 2) +
                             pow(rotError(1,2) - rotError(2,1), 2)) / 2.0;
    
    return rotErrorNorm <= rotTolerance;
}