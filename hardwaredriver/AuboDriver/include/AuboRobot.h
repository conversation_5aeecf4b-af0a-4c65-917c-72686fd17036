#pragma once

#include "rsdef.h"
#include <string>
#include <rw/math/Q.hpp> // RobWork Q类型
#include <rw/math/Transform3D.hpp> // RobWork Transform3D类型

class AuboRobot {
public:
    AuboRobot();
    ~AuboRobot();

    // 连接与初始化
    bool connect(const char* ip = "**************", int port = 8899);
    bool disconnect();
    bool startup();
    bool shutdown();

    // 运动控制
    /**
     * @brief 关节运动到指定位置
     * @param jointAngles 目标关节角度 (rad)
     * @param velocity 关节速度 (rad/s), -1表示使用默认速度(0.5 rad/s)
     * @param acceleration 关节加速度 (rad/s^2), -1表示使用默认加速度(1.0 rad/s^2)
     * @return 运动是否成功完成
     */
    bool moveJoint(const rw::math::Q& jointAngles,  
                  double velocity = -1,           
                  double acceleration = -1);      
    
    /**
     * @brief 直线运动到指定位置
     * @param target 目标位姿
     * @param velocity 工具速度 (m/s), -1表示使用默认速度(0.25 m/s)
     * @param acceleration 工具加速度 (m/s^2), -1表示使用默认加速度(0.5 m/s^2)
     * @return 运动是否成功完成
     */
    bool moveLine(const rw::math::Transform3D<>& target,
                 double velocity = -1,            
                 double acceleration = -1);       
    
    /**
     * @brief 移动到指定位置
     * @param pos 目标位置
     * @param joint6Angle 第6关节角度 (deg)
     * @param velocity 关节速度 (rad/s), -1表示使用默认速度(0.5 rad/s)
     * @param acceleration 关节加速度 (rad/s^2), -1表示使用默认加速度(1.0 rad/s^2)
     * @return 运动是否成功完成
     */
    bool moveToPos(const rw::math::Vector3D<>& pos, 
                  double joint6Angle = 0.0,
                  double velocity = -1,
                  double acceleration = -1);

    // IO控制
    bool setDO(const char* pin, bool value);    // 设置数字输出
    bool setToolDO(const char* pin, bool value); // 设置工具数字输出
    bool getDO(const char* pin, double& status); // 获取数字输出状态
    bool getToolDO(const char* pin, double& status); // 获取工具数字输出状态

    // 速度和加速度设置
    /**
     * @brief 设置默认关节运动速度
     * @param velocity 关节速度 (rad/s)
     * @details 各关节最大速度限制:
     *          关节1-3: 223°/s (3.89 rad/s)
     *          关节4-6: 237°/s (4.14 rad/s)
     * @return 设置是否成功
     */
    bool setJointVelocity(double velocity);

    /**
     * @brief 设置默认关节运动加速度
     * @param acceleration 关节加速度 (rad/s^2)
     * @details 推荐范围: 0.1 ~ 8.0 rad/s^2
     * @return 设置是否成功
     */
    bool setJointAcceleration(double acceleration);

    /**
     * @brief 设置默认工具运动速度
     * @param velocity 工具速度 (m/s)
     * @details 最大速度限制: 3.4 m/s
     * @return 设置是否成功
     */
    bool setToolVelocity(double velocity);

    /**
     * @brief 设置默认工具运动加速度
     * @param acceleration 工具加速度 (m/s^2)
     * @details 推荐范围: 0.1 ~ 10.0 m/s^2
     * @return 设置是否成功
     */
    bool setToolAcceleration(double acceleration);

    // 获取当前位置
    bool getCurrentJointPos(rw::math::Q& pos);
    bool getCurrentCartesianPos(rw::math::Transform3D<>& pos);


    // 位置检查相关
    bool isReached(const rw::math::Q& targetJoint, double tolerance = 0.01) const;  // 检查关节位置是否到达
    bool isReached(const rw::math::Transform3D<>& targetPose, double posTolerance = 0.001, double rotTolerance = 0.01) const;  // 检查笛卡尔位置是否到达


private:
    bool moveToPos(double x, double y, double z, 
                  double joint6Angle,
                  double velocity = -1,
                  double acceleration = -1);
    bool getCurrentPosition(aubo_robot_namespace::wayPoint_S& wayPoint);

    RSHD robot_handle_;
    bool is_connected_;
    bool is_started_;

    // 速度和加速度参数
    double joint_velocity_;
    double joint_acceleration_;
    double tool_velocity_;
    double tool_acceleration_;

    // 位置检查相关参数
    static constexpr double DEFAULT_TIMEOUT = 30.0;  // 默认超时时间(秒)
    static constexpr double CHECK_INTERVAL = 0.1;    // 检查间隔(秒)

    // 默认运动参数
    static constexpr double DEFAULT_JOINT_VELOCITY = 0.5;     // rad/s (约28.6°/s)
    static constexpr double DEFAULT_JOINT_ACCELERATION = 1.0; // rad/s^2
    static constexpr double DEFAULT_TOOL_VELOCITY = 0.25;     // m/s
    static constexpr double DEFAULT_TOOL_ACCELERATION = 0.5;  // m/s^2

    // 速度限制参数
    static constexpr double MAX_JOINT_VELOCITY_1_3 = 3.89;    // rad/s (223°/s)
    static constexpr double MAX_JOINT_VELOCITY_4_6 = 4.14;    // rad/s (237°/s)
    static constexpr double MAX_TOOL_VELOCITY = 3.4;          // m/s
    static constexpr double MAX_JOINT_ACCELERATION = 8.0;     // rad/s^2
    static constexpr double MAX_TOOL_ACCELERATION = 10.0;     // m/s^2
};