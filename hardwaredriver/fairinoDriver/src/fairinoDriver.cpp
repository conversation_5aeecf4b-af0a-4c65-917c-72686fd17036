//
// Created by Administrator on 2024/6/20/020.
//

#include "fairinoDriver.h"

using namespace robot;

fairinoDriver::fairinoDriver() {
    memset(&m_offset_pos, 0, sizeof(DescPose));
    memset(&m_epos, 0, sizeof(ExaxisPos));
}

fairinoDriver::~fairinoDriver() {
    StopMotion();
}

void fairinoDriver::StopMotion() const {
    int retval = m_robot->StopMotion();
    printf("StopMotion retval is: %d\n", retval);
}

void fairinoDriver::PauseMotion() const {
    int retval = m_robot->PauseMotion();
    printf("PauseMotion retval is: %d\n", retval);
}

void fairinoDriver::ResumeMotion() const {
    int retval = m_robot->ResumeMotion();
    printf("ResumeMotion retval is: %d\n", retval);
}


int fairinoDriver::Connect(const std::string &ip) {
    m_robot = std::make_shared<FRRobot>();
    return m_robot->RPC(ip.c_str());
}

int fairinoDriver::MoveJ(robotPose pose, float vel) {
    return m_robot->MoveJ(&pose.jointPos, &pose.descPose, m_tool, m_user, vel, m_acc, m_ovl, &m_epos, m_blendT, m_flag,
                          &m_offset_pos);
}

int fairinoDriver::MoveL(robotPose pose, float vel) {
    return m_robot->MoveL(&pose.jointPos, &pose.descPose, m_tool, m_user, vel, m_acc, m_ovl, m_blendR, &m_epos,
                          m_search, m_flag,
                          &m_offset_pos);
}

int fairinoDriver::MoveC(robotPose poseM, float velM, robotPose poseE, float velE) {
    return m_robot->MoveC(&poseM.jointPos, &poseM.descPose, m_tool, m_user, velM, m_acc, &m_epos, m_flag, &m_offset_pos,
                          &poseE.jointPos, &poseE.descPose, m_tool, m_user,
                          velE, m_acc, &m_epos, m_flag, &m_offset_pos, m_ovl, m_blendR);
}

robotPose fairinoDriver::GetRobotPose() const {
    robotPose pose{};
    m_robot->GetActualJointPosDegree(m_flag, &pose.jointPos);
    printf("joint pos deg:%f,%f,%f,%f,%f,%f\n", pose.jointPos.jPos[0], pose.jointPos.jPos[1], pose.jointPos.jPos[2],
           pose.jointPos.jPos[3], pose.jointPos.jPos[4], pose.jointPos.jPos[5]);

    m_robot->GetActualTCPPose(m_flag, &pose.descPose);
    printf("tcp pose:%f,%f,%f,%f,%f,%f\n", pose.descPose.tran.x, pose.descPose.tran.y, pose.descPose.tran.z,
           pose.descPose.rpy.rx, pose.descPose.rpy.ry, pose.descPose.rpy.rz);
    return pose;
}


int fairinoDriver::StartJOG(int nb, int dir, float vel) const {
    return m_robot->StartJOG(0, nb, dir, vel, 20.0, 30.0);;
}

int fairinoDriver::StopJOG() const {
    return m_robot->ImmStopJOG();
}
