//
// Created by Administrator on 2024/6/20/020.
//

#ifndef FUXIOS_FAIRINODRIVER_H
#define FUXIOS_FAIRINODRIVER_H

#include "libfairino/robot.h"
#include <string.h>
#include <windows.h>
#include <chrono>
#include <thread>

namespace robot {
    struct robotPose{
        JointPos jointPos;
        DescPose descPose;
    };

    class fairinoDriver {
    public:

        fairinoDriver();

        ~fairinoDriver();

        void StopMotion() const;

        void PauseMotion() const;

        void ResumeMotion() const;

        int Connect(const std::string& ip="************");

        int MoveJ(robotPose pose,float vel);

        int MoveL(robotPose pose,float vel);

        int MoveC(robotPose poseM,float velM,robotPose poseE,float velE);

        robotPose GetRobotPose() const;

        int StartJOG(int nb,int dir, float vel) const;

        int StopJOG() const;

        std::shared_ptr<FRRobot> m_robot;

    private:
        int m_tool = 0;
        int m_user = 0;
        float m_vel = 100.0;
        float m_acc = 100.0;
        float m_ovl = 100.0;
        float m_blendT = 0.0;
        float m_blendR = 0.0;
        uint8_t m_flag = 0;
        uint8_t m_search = 0;
        DescPose m_offset_pos{};
        ExaxisPos  m_epos{};
    };
};


#endif //FUXIOS_FAIRINODRIVER_H
