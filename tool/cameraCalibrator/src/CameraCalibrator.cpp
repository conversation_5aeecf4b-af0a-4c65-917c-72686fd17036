#include "CameraCalibrator.h"
#include <chrono>
#include <iomanip>
#include <sstream>

CameraCalibrator::CameraCalibrator(int squaresX, int squaresY, 
                                 float squareLength, float markerLength)
    : squaresX(squaresX), squaresY(squaresY),
      squareLength(squareLength), markerLength(markerLength)
{
    // 直接使用预定义字典
    dictionary = cv::aruco::getPredefinedDictionary(cv::aruco::DICT_4X4_50);
    
    // 创建 CharucoBoard
    board = cv::Ptr<cv::aruco::CharucoBoard>(new cv::aruco::CharucoBoard(
        cv::Size(squaresX, squaresY),
        squareLength,
        markerLength,
        dictionary));
    
    cameraMatrix = cv::Mat::eye(3, 3, CV_64F);
    distCoeffs = cv::Mat::zeros(8, 1, CV_64F);
    reprojectionError = 0.0;
}

cv::Mat CameraCalibrator::preprocessImage(const cv::Mat& input) {
    cv::Mat processed;
    cv::cvtColor(input, processed, cv::COLOR_BGR2GRAY);
    cv::GaussianBlur(processed, processed, cv::Size(3, 3), 0);
    
    cv::Mat clahe_output;
    cv::Ptr<cv::CLAHE> clahe = cv::createCLAHE(2.0, cv::Size(8, 8));
    clahe->apply(processed, clahe_output);
    
    return clahe_output;
}

bool CameraCalibrator::detectCharucoBoard(const cv::Mat& image,
                                        std::vector<cv::Point2f>& charucoCorners,
                                        std::vector<int>& charucoIds) {
    std::vector<std::vector<cv::Point2f>> markerCorners;
    std::vector<int> markerIds;
    
    cv::Mat processed = preprocessImage(image);
    
    cv::aruco::detectMarkers(processed, &dictionary, markerCorners, markerIds);
    
    if (markerIds.size() > 0) {
        cv::aruco::interpolateCornersCharuco(
            markerCorners, markerIds, processed, board,
            charucoCorners, charucoIds);
        
        return charucoIds.size() >= 15;
    }
    return false;
}

bool CameraCalibrator::calibrateFromImages(const std::vector<cv::Mat>& images) {
    allCharucoCorners.clear();
    allCharucoIds.clear();
    allImages.clear();

    for (const cv::Mat& image : images) {
        std::vector<cv::Point2f> charucoCorners;
        std::vector<int> charucoIds;

        if (detectCharucoBoard(image, charucoCorners, charucoIds)) {
            allCharucoCorners.push_back(charucoCorners);
            allCharucoIds.push_back(charucoIds);
            allImages.push_back(image.clone());
        }
    }

    return calibrateCamera();
}

bool CameraCalibrator::calibrateCamera() {
    if (allCharucoCorners.size() < 4) {
        return false;
    }
    
    std::vector<cv::Mat> rvecs, tvecs;
    cv::Size imageSize = allImages[0].size();
    
    reprojectionError = cv::aruco::calibrateCameraCharuco(
        allCharucoCorners, allCharucoIds, board, imageSize,
        cameraMatrix, distCoeffs, rvecs, tvecs,
        cv::CALIB_FIX_K3 | cv::CALIB_ZERO_TANGENT_DIST,
        cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::COUNT, 100, 1e-10));
    
    return true;
}

bool CameraCalibrator::calibrateHandEye(
    const std::vector<cv::Mat>& images,
    const std::vector<Eigen::Isometry3d>& robotPoses) {
    
    if (images.size() != robotPoses.size() || images.size() < 4) {
        return false;
    }
    
    std::vector<cv::Mat> rvecsCamera, tvecsCamera;
    std::vector<cv::Mat> rvecsRobot, tvecsRobot;
    
    // 获取每个位置的相机姿态
    for (size_t i = 0; i < images.size(); ++i) {
        std::vector<cv::Point2f> charucoCorners;
        std::vector<int> charucoIds;
        
        if (detectCharucoBoard(images[i], charucoCorners, charucoIds)) {
            cv::Mat rvec, tvec;
            bool valid = cv::aruco::estimatePoseCharucoBoard(
                charucoCorners, charucoIds, board, cameraMatrix, distCoeffs,
                rvec, tvec);
            
            if (valid) {
                rvecsCamera.push_back(rvec);
                tvecsCamera.push_back(tvec);
                
                // 转换机器人位姿为OpenCV格式
                Eigen::Vector3d translation = robotPoses[i].translation();
                Eigen::AngleAxisd rotation(robotPoses[i].rotation());
                
                cv::Mat rvecRobot = (cv::Mat_<double>(3,1) << 
                    rotation.axis()(0) * rotation.angle(),
                    rotation.axis()(1) * rotation.angle(),
                    rotation.axis()(2) * rotation.angle());
                
                cv::Mat tvecRobot = (cv::Mat_<double>(3,1) << 
                    translation(0), translation(1), translation(2));
                
                rvecsRobot.push_back(rvecRobot);
                tvecsRobot.push_back(tvecRobot);
            }
        }
    }
    
    if (rvecsCamera.size() < 4) {
        return false;
    }
    
    cv::Mat rotationMatrix, translationVector;
    cv::calibrateHandEye(rvecsRobot, tvecsRobot, rvecsCamera, tvecsCamera,
                        rotationMatrix, translationVector);
    
    // 转换结果为Eigen格式
    Eigen::Matrix3d rotation;
    for (int i = 0; i < 3; ++i)
        for (int j = 0; j < 3; ++j)
            rotation(i,j) = rotationMatrix.at<double>(i,j);
    
    Eigen::Vector3d translation(
        translationVector.at<double>(0),
        translationVector.at<double>(1),
        translationVector.at<double>(2));
    
    handEyeTransform.linear() = rotation;
    handEyeTransform.translation() = translation;
    
    return true;
}

bool CameraCalibrator::saveCalibration(const std::string& filename) const {
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        return false;
    }
    
    // 使用安全的时间函数
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);
    
    std::tm timeinfo;
#ifdef _WIN32
    localtime_s(&timeinfo, &in_time_t);
#else
    localtime_r(&in_time_t, &timeinfo);
#endif
    
    std::stringstream ss;
    ss << std::put_time(&timeinfo, "%Y-%m-%d %X");
    
    fs << "calibration_time" << ss.str();
    fs << "image_width" << allImages[0].cols;
    fs << "image_height" << allImages[0].rows;
    fs << "board_width" << squaresX;
    fs << "board_height" << squaresY;
    fs << "square_size" << squareLength;
    fs << "camera_matrix" << cameraMatrix;
    fs << "distortion_coefficients" << distCoeffs;
    fs << "avg_reprojection_error" << reprojectionError;
    fs << "used_frames" << (int)allImages.size();
    
    // 如果进行了手眼标定，保存变换矩阵
    if (handEyeTransform.matrix() != Eigen::Matrix4d::Identity()) {
        cv::Mat transform = (cv::Mat_<double>(4,4) <<
            handEyeTransform(0,0), handEyeTransform(0,1), handEyeTransform(0,2), handEyeTransform(0,3),
            handEyeTransform(1,0), handEyeTransform(1,1), handEyeTransform(1,2), handEyeTransform(1,3),
            handEyeTransform(2,0), handEyeTransform(2,1), handEyeTransform(2,2), handEyeTransform(2,3),
            0, 0, 0, 1);
        fs << "hand_eye_transform" << transform;
    }
    
    fs.release();
    return true;
}

void CameraCalibrator::drawDetection(cv::Mat& image,
                                   const std::vector<cv::Point2f>& charucoCorners,
                                   const std::vector<int>& charucoIds) const {
    if (charucoIds.empty()) return;
    
    // 绘制检测到的角点
    cv::aruco::drawDetectedCornersCharuco(image, charucoCorners, charucoIds);
    
    // 如果已经有相机参数，估计位姿并绘制坐标轴
    if (!cameraMatrix.empty() && !distCoeffs.empty()) {
        cv::Mat rvec, tvec;
        bool valid = cv::aruco::estimatePoseCharucoBoard(
            charucoCorners, charucoIds, board, cameraMatrix, distCoeffs,
            rvec, tvec);
            
        if (valid) {
            // 使用 0.1f 代替 0.1
            cv::drawFrameAxes(image, cameraMatrix, distCoeffs, 
                            rvec, tvec, 0.1f);
        }
    }
    
    // 显示检测到的角点数量
    std::string cornerText = "Detected corners: " + std::to_string(charucoIds.size());
    cv::putText(image, cornerText, cv::Point(10, 30), 
                cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(0, 255, 0), 2);
}

bool CameraCalibrator::previewDetection(const cv::Mat& input, cv::Mat& output) {
    if (input.empty()) {
        return false;
    }
    
    // 复制输入图像
    input.copyTo(output);
    
    // 检测角点
    std::vector<cv::Point2f> charucoCorners;
    std::vector<int> charucoIds;
    bool detected = detectCharucoBoard(input, charucoCorners, charucoIds);
    
    // 绘制检测结果
    drawDetection(output, charucoCorners, charucoIds);
    
    return detected;
} 