#pragma once

#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <opencv2/calib3d.hpp>
#include <opencv2/imgproc.hpp>
#include <vector>
#include <string>
#include <Eigen/Core>
#include <Eigen/Geometry>

class CameraCalibrator {
public:
    CameraCalibrator(int squaresX = 5, int squaresY = 7, 
                     float squareLength = 0.03f, float markerLength = 0.023f);
    
    // 从图像进行标定
    bool calibrateFromImages(const std::vector<cv::Mat>& images);
    
    // 手眼标定
    bool calibrateHandEye(const std::vector<cv::Mat>& images,
                         const std::vector<Eigen::Isometry3d>& robotPoses);
    
    // 保存标定结果
    bool saveCalibration(const std::string& filename) const;
    
    // 获取标定结果
    cv::Mat getCameraMatrix() const { return cameraMatrix; }
    cv::Mat getDistCoeffs() const { return distCoeffs; }
    Eigen::Isometry3d getHandEyeTransform() const { return handEyeTransform; }
    double getReprojectionError() const { return reprojectionError; }
    
    // 预览单张图片的检测效果
    bool previewDetection(const cv::Mat& input, cv::Mat& output);
    
    // 在图像上绘制检测结果
    void drawDetection(cv::Mat& image, 
                      const std::vector<cv::Point2f>& charucoCorners,
                      const std::vector<int>& charucoIds) const;

private:
    // ChArUco板配置
    cv::aruco::Dictionary dictionary;
    cv::Ptr<cv::aruco::CharucoBoard> board;
    int squaresX, squaresY;
    float squareLength, markerLength;
    
    // 标定结果
    cv::Mat cameraMatrix;
    cv::Mat distCoeffs;
    Eigen::Isometry3d handEyeTransform;
    double reprojectionError;
    
    // 采集的数据
    std::vector<std::vector<cv::Point2f>> allCharucoCorners;
    std::vector<std::vector<int>> allCharucoIds;
    std::vector<cv::Mat> allImages;
    
    // 内部辅助函数
    bool detectCharucoBoard(const cv::Mat& image, 
                          std::vector<cv::Point2f>& charucoCorners,
                          std::vector<int>& charucoIds);
    bool calibrateFromCamera(const std::string& cameraDevice);
    bool calibrateCamera();
    cv::Mat preprocessImage(const cv::Mat& input);
}; 