#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <vector>
#include <iostream>
#include <fstream>
#include <glog.h>
#include "AuboArcsDriver.h"
#include "rw/math.hpp"
#include "HikVisionCamera.h"
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>

using namespace std;
using namespace cv;

// 全局变量
std::shared_ptr<AuboArcsDriver> g_robot;
std::shared_ptr<HikVisionCamera> g_camera;
cv::Mat g_cameraMatrix, g_distCoeffs;
cv::Mat g_R_cam2gripper, g_t_cam2gripper; // 手眼标定结果


// 在全局变量部分添加卡尔曼滤波器相关变量
class KalmanFilter3D {
    private:
        cv::KalmanFilter KF;
        bool initialized;
    
    public:
        KalmanFilter3D() : KF(6, 3, 0), initialized(false) {
            // 状态向量: [x, y, z, vx, vy, vz]
            KF.transitionMatrix = (cv::Mat_<float>(6, 6) << 
                1, 0, 0, 1, 0, 0,
                0, 1, 0, 0, 1, 0,
                0, 0, 1, 0, 0, 1,
                0, 0, 0, 1, 0, 0,
                0, 0, 0, 0, 1, 0,
                0, 0, 0, 0, 0, 1);
    
            setIdentity(KF.measurementMatrix.setTo(0));
            KF.measurementMatrix(cv::Range(0,3), cv::Range(0,3)) = cv::Mat::eye(3, 3, CV_32F);
    
            setIdentity(KF.processNoiseCov, cv::Scalar::all(1e-5));
            setIdentity(KF.measurementNoiseCov, cv::Scalar::all(1e-2));
            setIdentity(KF.errorCovPost, cv::Scalar::all(1));
        }
    
        cv::Point3f update(const cv::Point3f& measurement) {
            if (!initialized) {
                KF.statePost.at<float>(0) = measurement.x;
                KF.statePost.at<float>(1) = measurement.y;
                KF.statePost.at<float>(2) = measurement.z;
                initialized = true;
                return measurement;
            }
    
            cv::Mat prediction = KF.predict();
            cv::Mat measurement_mat = (cv::Mat_<float>(3,1) << measurement.x, measurement.y, measurement.z);
            cv::Mat estimated = KF.correct(measurement_mat);
    
            return cv::Point3f(estimated.at<float>(0), estimated.at<float>(1), estimated.at<float>(2));
        }
    
        void reset() {
            initialized = false;
        }
    };
    
    // 添加全局变量
    KalmanFilter3D positionFilter;
    KalmanFilter3D rotationFilter;
    
    // 在主循环中修改相关代码

// 将OpenCV的R和t转换为RW数学库的Transform3D
rw::math::Transform3D<> rtToTransform3D(const cv::Mat& R, const cv::Mat& t) {
    rw::math::Rotation3D<> rotation(
        R.at<double>(0,0), R.at<double>(0,1), R.at<double>(0,2),
        R.at<double>(1,0), R.at<double>(1,1), R.at<double>(1,2),
        R.at<double>(2,0), R.at<double>(2,1), R.at<double>(2,2)
    );
    
    rw::math::Vector3D<> position(
        t.at<double>(0,0),
        t.at<double>(1,0),
        t.at<double>(2,0)
    );
    
    return rw::math::Transform3D<>(position, rotation);
}

// 将RW数学库的Transform3D转换为OpenCV的R和t
void transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t) {
    // 提取旋转矩阵
    R = cv::Mat::eye(3, 3, CV_64F);
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            R.at<double>(i,j) = transform.R()(i,j);
        }
    }
    
    // 提取平移向量
    t = cv::Mat(3, 1, CV_64F);
    t.at<double>(0,0) = transform.P()[0];
    t.at<double>(1,0) = transform.P()[1];
    t.at<double>(2,0) = transform.P()[2];
}

// 加载相机标定参数
bool loadCameraCalibration(const std::string& filename) {
    FileStorage fs(filename, FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "无法打开相机标定文件: " << filename;
        return false;
    }
    
    fs["camera_matrix"] >> g_cameraMatrix;
    fs["distortion_coefficients"] >> g_distCoeffs;
    fs.release();
    
    return true;
}

// 加载手眼标定参数
bool loadHandEyeCalibration(const std::string& filename) {
    FileStorage fs(filename, FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "无法打开手眼标定文件: " << filename;
        return false;
    }
    
    fs["rotation_matrix"] >> g_R_cam2gripper;
    fs["translation_vector"] >> g_t_cam2gripper;
    fs.release();
    
    return true;
}

int main(int argc, char** argv) {
    // 初始化glog
    google::InitGoogleLogging(argv[0]);
    FLAGS_colorlogtostderr = true;
    FLAGS_logtostderr = true;

    // 加载相机标定参数
    if (!loadCameraCalibration("camera_calibration.yml")) {
        LOG(ERROR) << "无法加载相机标定参数";
        return -1;
    }
    
    // 加载手眼标定参数
    if (!loadHandEyeCalibration("hand_eye_calibration.yml")) {
        LOG(ERROR) << "无法加载手眼标定参数";
        return -1;
    }
    
    LOG(INFO) << "成功加载标定参数";
    LOG(INFO) << "相机内参矩阵:\n" << g_cameraMatrix;
    LOG(INFO) << "相机畸变系数:\n" << g_distCoeffs;
    LOG(INFO) << "手眼标定旋转矩阵:\n" << g_R_cam2gripper;
    LOG(INFO) << "手眼标定平移向量:\n" << g_t_cam2gripper;

    // 初始化机器人
    g_robot = std::make_shared<AuboArcsDriver>();
    if (!g_robot->connect("*************", 8899)) {
        LOG(ERROR) << "无法连接机器人";
        return -1;
    }
    LOG(INFO) << "机器人连接成功";

    // 初始化相机
    g_camera = std::make_shared<HikVisionCamera>();
    g_camera->setErrorCallback([](const std::string& error) {
        LOG(ERROR) << "相机错误: " << error;
    });
    
    if (!g_camera->connectToDevice("*********")) { // 请替换为实际的相机序列号
        LOG(ERROR) << "无法连接相机";
        return -1;
    }
    
    // 设置相机参数
    g_camera->setExposureTime(120000.0f);
    g_camera->setGain(0.0f);
    g_camera->setFrameRate(30.0f);
    
    if (!g_camera->startGrabbing()) {
        LOG(ERROR) << "无法开始图像采集";
        return -1;
    }
    LOG(INFO) << "相机初始化成功";

    // 创建ChArUco标定板
    cv::aruco::Dictionary dictionarytmp = cv::aruco::getPredefinedDictionary(cv::aruco::DICT_4X4_50);
    cv::Ptr<cv::aruco::Dictionary> dictionary = cv::Ptr<cv::aruco::Dictionary>(new cv::aruco::Dictionary(dictionarytmp));

    const int squaresX = 5;
    const int squaresY = 7;
    const float squareLength = 0.006f;
    const float markerLength = 0.0046f;

    cv::Ptr<cv::aruco::CharucoBoard> board = cv::Ptr<cv::aruco::CharucoBoard>(
        new cv::aruco::CharucoBoard({squaresX, squaresY}, squareLength, markerLength, *dictionary));

    // 记录初始标定板位姿
    cv::Mat R_target2cam_initial, t_target2cam_initial;
    bool initialPoseSet = false;
    rw::math::Transform3D<> initialRobotPose;

    // 创建窗口
    cv::namedWindow("Target Tracking", cv::WINDOW_NORMAL);
    cv::resizeWindow("Target Tracking", 1280, 720);

    // 跟踪参数
    bool trackingEnabled = false;
    double moveThreshold = 0.05; // 5mm移动阈值
    double rotThreshold = 0.2;   // 约3度旋转阈值

    LOG(INFO) << "按空格键开始/暂停跟踪，按'q'退出";

    while (true) {
        // 获取图像
        Sleep(500);
        Mat frame = g_camera->getFrame();
        if (frame.empty()) {
            LOG(ERROR) << "无法读取图像";
            continue;
        }

        // 检测ChArUco角点
        vector<vector<Point2f>> markerCorners;
        vector<int> markerIds;
        vector<Point2f> charucoCorners;
        vector<int> charucoIds;

        cv::aruco::detectMarkers(frame, dictionary, markerCorners, markerIds);

        Mat displayFrame = frame.clone();

        if (markerIds.size() > 0) {
            aruco::drawDetectedMarkers(displayFrame, markerCorners, markerIds);
            
            // 插值ChArUco角点
            aruco::interpolateCornersCharuco(markerCorners, markerIds, frame, board,
                                           charucoCorners, charucoIds, g_cameraMatrix, g_distCoeffs);

            if (charucoIds.size() > 10) { // 确保检测到足够多的角点
                aruco::drawDetectedCornersCharuco(displayFrame, charucoCorners, charucoIds);
                
                // 估计标定板位姿
                cv::Mat rvec, tvec;
                bool valid = cv::aruco::estimatePoseCharucoBoard(
                    charucoCorners, charucoIds, board, g_cameraMatrix, g_distCoeffs,
                    rvec, tvec);
                
                if (valid) {
                    // 绘制坐标轴
                    cv::drawFrameAxes(displayFrame, g_cameraMatrix, g_distCoeffs, rvec, tvec, 0.05);
                    
                    // 转换旋转向量为旋转矩阵
                    cv::Mat R_target2cam;
                    cv::Rodrigues(rvec, R_target2cam);
                    
                    // 如果初始位姿未设置，则设置初始位姿
                    if (!initialPoseSet) {
                        R_target2cam_initial = R_target2cam.clone();
                        t_target2cam_initial = tvec.clone();
                        initialRobotPose = g_robot->getRobotActualTCP();
                        initialPoseSet = true;
                        LOG(INFO) << "初始标定板位姿已记录";
                    }
                    
                    // 计算标定板相对于初始位置的变换
                    cv::Mat R_relative = R_target2cam * R_target2cam_initial.t();
                    cv::Mat t_relative = tvec - R_relative * t_target2cam_initial;
                    
                    // 计算相对变换的大小（三维空间）
                    double translationMagnitude = cv::norm(t_relative);
                    
                    // 从旋转矩阵提取角度（三维旋转）
                    double trace = R_relative.at<double>(0,0) + R_relative.at<double>(1,1) + R_relative.at<double>(2,2);
                    double rotationAngle = acos((trace - 1.0) / 2.0);
                    
                    // 显示相对变换信息
                    putText(displayFrame, 
                            "Trans: " + std::to_string(translationMagnitude) + "m, Rot: " + std::to_string(rotationAngle * 180.0 / M_PI) + "deg", 
                            Point(10, 30), FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0, 255, 0), 2);
                    
                    // 打印阈值判断信息
                    LOG(INFO) << "当前移动量: " << translationMagnitude << "m (阈值: " << moveThreshold << "m)";
                    LOG(INFO) << "当前旋转量: " << rotationAngle * 180.0 / M_PI << "度 (阈值: " << rotThreshold * 180.0 / M_PI << "度)";
                    
                    // 如果跟踪启用且移动超过阈值，更新机器人位置
                    if (trackingEnabled && (translationMagnitude < moveThreshold && rotationAngle < rotThreshold)) {
                        LOG(INFO) << "超过阈值，开始更新机器人位置";
                        
                        // 计算相机坐标系下的目标位姿
                        cv::Mat R_target2cam_current = R_target2cam;
                        cv::Mat t_target2cam_current = tvec;
                        
                        // 使用手眼标定结果计算机器人基座标系下的目标位姿
                        // T_base_target = T_base_gripper * T_gripper_camera * T_camera_target
                        
                        // 1. 计算 T_camera_target (从相机到标定板)
                        rw::math::Transform3D<> T_camera_target = rtToTransform3D(R_target2cam_current, t_target2cam_current);
                        
                        // 2. 计算 T_gripper_camera (从机械臂末端到相机)
                        rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(g_R_cam2gripper, g_t_cam2gripper);
                        
                        // 3. 获取当前机器人位姿 T_base_gripper
                        rw::math::Transform3D<> T_base_gripper = g_robot->getRobotActualTCP();
                        
                        // 4. 计算目标位姿 T_base_target
                        rw::math::Transform3D<> T_base_target = T_base_gripper * T_gripper_camera * T_camera_target;
                        
                        // 5. 计算初始标定板在基座标系中的位姿
                        rw::math::Transform3D<> T_camera_target_initial = rtToTransform3D(R_target2cam_initial, t_target2cam_initial);
                        rw::math::Transform3D<> T_base_target_initial = initialRobotPose * T_gripper_camera * T_camera_target_initial;
                        
                        // 6. 计算标定板的相对运动
                        rw::math::Transform3D<> T_target_movement = T_base_target * inverse(T_base_target_initial);
                        
                        // 7. 应用相对运动到机器人当前位姿
                        rw::math::Transform3D<> newRobotPose = T_target_movement * initialRobotPose;
                        
                        // 打印移动变化量
                        rw::math::Vector3D<> positionChange = newRobotPose.P() - initialRobotPose.P();
                        rw::math::RPY<> initialRPY(initialRobotPose.R());
                        rw::math::RPY<> newRPY(newRobotPose.R());
                        rw::math::Vector3D<> rotationChange(
                            newRPY[0] - initialRPY[0],
                            newRPY[1] - initialRPY[1],
                            newRPY[2] - initialRPY[2]
                        );
                        
                        LOG(INFO) << "位置变化量 (米): X=" << positionChange[0] 
                                  << ", Y=" << positionChange[1] 
                                  << ", Z=" << positionChange[2];
                        LOG(INFO) << "旋转变化量 (弧度): R=" << rotationChange[0] 
                                  << ", P=" << rotationChange[1] 
                                  << ", Y=" << rotationChange[2];
                        
                        // 应用卡尔曼滤波
                        cv::Point3f rawPosition(positionChange[0], positionChange[1], positionChange[2]);
                        cv::Point3f rawRotation(rotationChange[0], rotationChange[1], rotationChange[2]);
                        
                        cv::Point3f filteredPosition = positionFilter.update(rawPosition);
                        cv::Point3f filteredRotation = rotationFilter.update(rawRotation);
                        
                        LOG(INFO) << "滤波后位置变化量 (米): X=" << filteredPosition.x 
                                  << ", Y=" << filteredPosition.y 
                                  << ", Z=" << filteredPosition.z;
                        LOG(INFO) << "滤波后旋转变化量 (弧度): R=" << filteredRotation.x 
                                  << ", P=" << filteredRotation.y 
                                  << ", Y=" << filteredRotation.z;
                        
                        // 构建滤波后的机器人位姿
                        rw::math::Vector3D<> filteredPositionVector(
                            initialRobotPose.P()[0] + filteredPosition.x,
                            initialRobotPose.P()[1] + filteredPosition.y,
                            initialRobotPose.P()[2] + filteredPosition.z
                        );
                        
                        rw::math::RPY<> filteredRPY(
                            initialRPY[0] + filteredRotation.x,
                            initialRPY[1] + filteredRotation.y,
                            initialRPY[2] + filteredRotation.z
                        );
                        
                        rw::math::Transform3D<> filteredRobotPose(
                            filteredPositionVector,
                            rw::math::Rotation3D<>(filteredRPY)
                        );
                        
                        // 移动机器人到新位置
                        LOG(INFO) << "移动机器人跟随标定板";
                        
                        // 从Transform3D转换为关节空间，使用逆运动学
                        rw::math::Q currentJointQ = g_robot->getRobotJointQ();
                        rw::math::Q newJointQ;
                        
                        if (g_robot->getIK(currentJointQ, filteredRobotPose, newJointQ)) {
                            // 计算关节角度变化
                            rw::math::Q jointChange = newJointQ - currentJointQ;
                            
                            // 将弧度转换为角度并检查安全阈值
                            const double safetyThreshold = 10.0; // 安全阈值：10度
                            bool isSafeMovement = true;
                            
                            // 检查每个关节的变化是否在安全范围内
                            for (size_t i = 0; i < jointChange.size(); i++) {
                                double changeInDegrees = jointChange[i] * 180.0 / M_PI;
                                if (std::abs(changeInDegrees) > safetyThreshold) {
                                    LOG(WARNING) << "关节 " << i+1 << " 变化过大: " 
                                                << changeInDegrees << "度，超过安全阈值 " 
                                                << safetyThreshold << "度";
                                    isSafeMovement = false;
                                    break;
                                }
                            }
                            
                            if (isSafeMovement) {
                                // 使用movej代替movel
                                g_robot->movej(newJointQ, 0.2, 5, 0.2);
                                
                                // 打印关节角度变化
                                // 将弧度转换为角度 (弧度 * 180 / M_PI = 角度)
                                LOG(INFO) << "关节角度变化 (角度): J1=" << jointChange[0] * 180.0 / M_PI
                                          << ", J2=" << jointChange[1] * 180.0 / M_PI
                                          << ", J3=" << jointChange[2] * 180.0 / M_PI
                                          << ", J4=" << jointChange[3] * 180.0 / M_PI
                                          << ", J5=" << jointChange[4] * 180.0 / M_PI
                                          << ", J6=" << jointChange[5] * 180.0 / M_PI;
                            } else {
                                LOG(ERROR) << "关节变化角度过大，为安全起见不执行运动";
                                // 不更新初始位姿，保持当前状态
                                continue;
                            }
                        } else {
                            LOG(ERROR) << "逆运动学计算失败，无法移动机器人";
                        }
                        
                        // 更新初始位姿，以便下一次计算相对运动
                        initialRobotPose = filteredRobotPose;  // 使用滤波后的位姿
                        R_target2cam_initial = R_target2cam_current.clone();
                        t_target2cam_initial = t_target2cam_current.clone();
                     
                    }
                }
            }
        }

        // 显示跟踪状态
        string statusText = trackingEnabled ? "跟踪: 开启" : "跟踪: 关闭";
        putText(displayFrame, statusText, Point(10, displayFrame.rows - 20), 
                FONT_HERSHEY_SIMPLEX, 0.7, trackingEnabled ? Scalar(0, 255, 0) : Scalar(0, 0, 255), 2);
        
        // 显示操作指南
        putText(displayFrame, "空格键: 开始/暂停跟踪 | 'q': 退出", Point(10, displayFrame.rows - 50), 
                FONT_HERSHEY_SIMPLEX, 0.7, Scalar(255, 255, 255), 2);

        // 调整图像大小并显示
        cv::Mat resizedFrame;
        cv::resize(displayFrame, resizedFrame, cv::Size(1280, 720));
        imshow("Target Tracking", resizedFrame);

        // 处理键盘输入
        char key = waitKey(1);
        if (key == ' ') {
            trackingEnabled = !trackingEnabled;
            LOG(INFO) << (trackingEnabled ? "跟踪已启用" : "跟踪已暂停");
            
            // 重置初始位姿和卡尔曼滤波器
            if (trackingEnabled) {
                initialPoseSet = false;
                positionFilter.reset();
                rotationFilter.reset();
            }
        }
        else if (key == 'q') {
            LOG(INFO) << "程序退出";
            break;
        }
    }

    // 关闭资源
    g_robot->stop();
    g_camera->stopGrabbing();
    g_camera->disconnect();
    destroyAllWindows();
    
    return 0;
}

