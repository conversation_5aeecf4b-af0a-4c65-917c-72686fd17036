#include <QApplication>
#include <glog.h>
#include <csignal>
#include <iostream>
#include "MainWindow.h"

// Global variables for signal handling
MainWindow* g_mainWindow = nullptr;

// Signal handler function
void signalHandler(int signal) {
    std::cout << "Received interrupt signal, safely exiting..." << std::endl;
    if (g_mainWindow) {
        g_mainWindow->close();
    }
    QApplication::quit();
}

int main(int argc, char *argv[]) {
    // Initialize glog
    google::InitGoogleLogging(argv[0]);
    FLAGS_colorlogtostderr = true;
    FLAGS_logtostderr = true;
    
    // Register signal handler
    std::signal(SIGINT, signalHandler);
    
    // Create Qt application
    QApplication app(argc, argv);
    
    // Set application style
    app.setStyle("Fusion");
    
    // Create main window
    MainWindow window;
    g_mainWindow = &window;
    window.show();
    
    LOG(INFO) << "Hand-Eye Calibration Tracking System started";
    
    // Start the event loop
    int result = app.exec();
    
    LOG(INFO) << "Application exiting";
    return result;
} 