#include "TrackingManager.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <glog.h>
#include <Windows.h>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>

TrackingManager::TrackingManager()
    : trackingEnabled(false)
    , moveThreshold(0.05) // 5cm default
    , rotThreshold(0.2)   // ~11 degrees default
    , currentTemplate("default")
    , initialPoseSet(false)
{
    // Initialize the ArUco dictionary and ChArUco board
    cv::aruco::Dictionary dictionaryTmp = cv::aruco::getPredefinedDictionary(cv::aruco::DICT_4X4_50);
    dictionary = cv::Ptr<cv::aruco::Dictionary>(new cv::aruco::Dictionary(dictionaryTmp));
    
    const int squaresX = 5;
    const int squaresY = 7;
    const float squareLength = 0.006f;
    const float markerLength = 0.0046f;
    
    board = cv::Ptr<cv::aruco::CharucoBoard>(
        new cv::aruco::CharucoBoard({squaresX, squaresY}, squareLength, markerLength, *dictionary));
    
    initialize();
}

TrackingManager::~TrackingManager()
{
    // Clean up resources
    if (robot) {
        robot->stop();
    }
    
    if (camera) {
        camera->stopGrabbing();
        camera->disconnect();
    }
}

bool TrackingManager::initialize()
{
    LOG(INFO) << "Initializing TrackingManager...";
    
    // Load camera calibration
    if (!loadCameraCalibration("camera_calibration.yml")) {
        LOG(ERROR) << "Failed to load camera calibration parameters";
        return false;
    }
    
    // Load hand-eye calibration
    if (!loadHandEyeCalibration("hand_eye_calibration.yml")) {
        LOG(ERROR) << "Failed to load hand-eye calibration parameters";
        return false;
    }
    
    // Initialize robot
    robot = std::make_shared<AuboArcsDriver>();
    if (!robot->connect("192.168.1.189", 8899)) {
        LOG(ERROR) << "Failed to connect to robot";
        return false;
    }
    
    // Initialize camera
    camera = std::make_shared<HikVisionCamera>();
    camera->setErrorCallback([](const std::string &error) {
        LOG(ERROR) << "Camera error: " << error;
    });
    
    if (!camera->connectToDevice("*********")) {  // Replace with actual camera serial
        LOG(ERROR) << "Failed to connect to camera";
        return false;
    }
    
    // Set camera parameters
    camera->setExposureTime(120000.0f);
    camera->setGain(0.0f);
    camera->setFrameRate(30.0f);
    
    if (!camera->startGrabbing()) {
        LOG(ERROR) << "Failed to start image acquisition";
        return false;
    }
    
    LOG(INFO) << "TrackingManager initialized successfully";
    return true;
}

cv::Mat TrackingManager::processFrame(TrackingStatus& status)
{
    // Get frame from camera
    cv::Mat frame = camera->getFrame();
    if (frame.empty()) {
        LOG(ERROR) << "Failed to capture frame";
        return frame;
    }
    
    cv::Mat displayFrame = frame.clone();
    
    // Update tracking status
    status.trackingEnabled = trackingEnabled;
    status.currentTemplate = currentTemplate;
    status.templateAvailable = !templates.empty();
    status.initialPoseSet = initialPoseSet;
    
    // Detect ChArUco markers
    std::vector<int> charucoIds;
    std::vector<cv::Point2f> charucoCorners;
    status.markersDetected = detectChArUcoBoard(frame, charucoIds, charucoCorners);
    
    if (status.markersDetected) {
        // Draw detected corners
        cv::aruco::drawDetectedCornersCharuco(displayFrame, charucoCorners, charucoIds);
        
        // Estimate board pose
        cv::Mat rvec, tvec;
        if (estimateBoardPose(charucoCorners, charucoIds, rvec, tvec)) {
            // Draw coordinate axes
            cv::drawFrameAxes(displayFrame, cameraMatrix, distCoeffs, rvec, tvec, 0.05);
            
            // Convert rotation vector to matrix
            cv::Mat R_target2cam;
            cv::Rodrigues(rvec, R_target2cam);
            
            // Calculate view angle
            cv::Mat boardNormal = R_target2cam * (cv::Mat_<double>(3, 1) << 0, 0, 1);
            cv::Mat cameraDirection = (cv::Mat_<double>(3, 1) << 0, 0, 1);
            double cosAngle = boardNormal.dot(cameraDirection) / 
                             (cv::norm(boardNormal) * cv::norm(cameraDirection));
            status.viewAngle = acos(cosAngle) * 180.0 / M_PI;
            
            // Initialize pose if needed
            if (!initialPoseSet) {
                // Try to load from template first
                if (templates.find(currentTemplate) != templates.end() && templates[currentTemplate].isValid) {
                    R_target2cam_initial = templates[currentTemplate].R_target2cam.clone();
                    t_target2cam_initial = templates[currentTemplate].t_target2cam.clone();
                    initialRobotPose = templates[currentTemplate].robotPose;
                    initialPoseSet = true;
                    LOG(INFO) << "Loaded initial pose from template: " << currentTemplate;
                    LOG(INFO) << "Loaded initial pose : " << initialRobotPose;
                } else {
                    R_target2cam_initial = R_target2cam.clone();
                    t_target2cam_initial = tvec.clone();
                    initialRobotPose = robot->getRobotActualTCP();
                    initialPoseSet = true;
                    LOG(INFO) << "Set initial board pose";
                    
                    // Auto-save as template if none exists
                    if (templates.empty()) {
                        saveTemplate(currentTemplate);
                    }
                }
                
                // Reset Kalman filters when initializing new pose
                positionFilter.reset();
                rotationFilter.reset();
            }
            
            // Calculate relative transformation
            cv::Mat R_relative = R_target2cam * R_target2cam_initial.t();
            cv::Mat t_relative = tvec - R_relative * t_target2cam_initial;
            
            // Calculate magnitudes
            status.translationMagnitude = cv::norm(t_relative);
            double trace = R_relative.at<double>(0, 0) + R_relative.at<double>(1, 1) + R_relative.at<double>(2, 2);
            status.rotationAngle = acos((trace - 1.0) / 2.0);
            
            // Update robot position if tracking is enabled and thresholds are exceeded
            if (trackingEnabled && (status.translationMagnitude <= moveThreshold || status.rotationAngle <= rotThreshold)) {
                // Check if view angle is near perpendicular
                if (abs(status.viewAngle - 90.0) < 15.0) {
                    LOG(WARNING) << "Camera nearly perpendicular to board (angle: " << status.viewAngle 
                                 << " deg), pausing movement for safety";
                } else {
                    updateRobotPosition(R_target2cam, tvec);
                }
            }
        }
    }
    
    return displayFrame;
}

bool TrackingManager::detectChArUcoBoard(const cv::Mat& frame, std::vector<int>& charucoIds, 
                                      std::vector<cv::Point2f>& charucoCorners)
{
    // Detect ArUco markers
    std::vector<std::vector<cv::Point2f>> markerCorners;
    std::vector<int> markerIds;
    
    cv::aruco::detectMarkers(frame, dictionary, markerCorners, markerIds);
    
    if (markerIds.size() > 0) {
        // Interpolate ChArUco corners
        cv::aruco::interpolateCornersCharuco(markerCorners, markerIds, frame, board,
                                          charucoCorners, charucoIds, cameraMatrix, distCoeffs);
        
        // Require a minimum number of corners for reliable pose estimation
        return charucoIds.size() > 10;
    }
    
    return false;
}

bool TrackingManager::estimateBoardPose(const std::vector<cv::Point2f>& charucoCorners, 
                                     const std::vector<int>& charucoIds,
                                     cv::Mat& rvec, cv::Mat& tvec)
{
    if (charucoCorners.empty() || charucoIds.empty()) {
        return false;
    }
    
    return cv::aruco::estimatePoseCharucoBoard(
        charucoCorners, charucoIds, board, cameraMatrix, distCoeffs, rvec, tvec);
}

void TrackingManager::updateRobotPosition(const cv::Mat& R_target2cam, const cv::Mat& t_target2cam)
{
    LOG(INFO) << "Updating robot position to follow target";
    
    // 1. Calculate camera-to-target transform
    rw::math::Transform3D<> T_camera_target = rtToTransform3D(R_target2cam, t_target2cam);
    
    // 2. Calculate gripper-to-camera transform
    rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);
    
    // 3. Get current robot pose
    rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();
    
    // 4. Calculate target pose in base coordinates
    rw::math::Transform3D<> T_base_target = T_base_gripper * T_gripper_camera * T_camera_target;
    
    // 5. Calculate initial target pose in base coordinates
    rw::math::Transform3D<> T_camera_target_initial = rtToTransform3D(R_target2cam_initial, t_target2cam_initial);
    rw::math::Transform3D<> T_base_target_initial = initialRobotPose * T_gripper_camera * T_camera_target_initial;
    
    // 6. Calculate relative movement
    rw::math::Transform3D<> T_target_movement = T_base_target * inverse(T_base_target_initial);
    
    // 7. Apply movement to initial robot pose
    rw::math::Transform3D<> newRobotPose = T_target_movement * initialRobotPose;
    
    // Calculate position and rotation changes
    rw::math::Vector3D<> positionChange = newRobotPose.P() - initialRobotPose.P();
    rw::math::RPY<> initialRPY(initialRobotPose.R());
    rw::math::RPY<> newRPY(newRobotPose.R());
    rw::math::Vector3D<> rotationChange(
        newRPY[0] - initialRPY[0],
        newRPY[1] - initialRPY[1],
        newRPY[2] - initialRPY[2]
    );
    
    // Apply Kalman filtering
    cv::Point3f rawPosition(positionChange[0], positionChange[1], positionChange[2]);
    cv::Point3f rawRotation(rotationChange[0], rotationChange[1], rotationChange[2]);
    
    cv::Point3f filteredPosition = positionFilter.update(rawPosition);
    cv::Point3f filteredRotation = rotationFilter.update(rawRotation);
    
    // Construct filtered robot pose
    rw::math::Vector3D<> filteredPositionVector(
        initialRobotPose.P()[0] + filteredPosition.x,
        initialRobotPose.P()[1] + filteredPosition.y,
        initialRobotPose.P()[2] + filteredPosition.z
    );
    
    rw::math::RPY<> filteredRPY(
        initialRPY[0] + filteredRotation.x,
        initialRPY[1] + filteredRotation.y,
        initialRPY[2] + filteredRotation.z
    );
    
    rw::math::Transform3D<> filteredRobotPose(
        filteredPositionVector,
        rw::math::Rotation3D<>(filteredRPY)
    );
    
    // Calculate IK and check safety constraints
    rw::math::Q currentJointQ = robot->getRobotJointQ();
    rw::math::Q newJointQ;
    
    if (robot->getIK(currentJointQ, filteredRobotPose, newJointQ)) {
        // Check safety thresholds
        rw::math::Q jointChange = newJointQ - currentJointQ;
        const double safetyThreshold = 10.0 * M_PI / 180.0; // 10 degrees in radians
        bool isSafeMovement = true;
        
        for (size_t i = 0; i < jointChange.size(); i++) {
            if (std::abs(jointChange[i]) > safetyThreshold) {
                LOG(WARNING) << "Joint " << i + 1 << " change too large: "
                             << jointChange[i] * 180.0 / M_PI << " deg, exceeding safety threshold "
                             << safetyThreshold * 180.0 / M_PI << " deg";
                isSafeMovement = false;
                break;
            }
        }
        
        if (isSafeMovement) {
            // Execute robot movement
            robot->movej(newJointQ, 0.2, 5, 0.2);
            
            // Update initial pose for next movement
            initialRobotPose = filteredRobotPose;
            R_target2cam_initial = R_target2cam.clone();
            t_target2cam_initial = t_target2cam.clone();
        } else {
            LOG(ERROR) << "Movement exceeds safety limits, robot movement aborted";
        }
    } else {
        LOG(ERROR) << "Inverse kinematics failed, cannot move robot";
    }
}

bool TrackingManager::saveTemplate(const std::string& name)
{
    if (!initialPoseSet) {
        LOG(ERROR) << "Cannot save template: initial pose not set";
        return false;
    }
    
    std::string filename = "tracking_template_" + name + ".yml";
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to create template file: " << filename;
        return false;
    }
    
    fs << "template_name" << name;
    fs << "R_target2cam" << R_target2cam_initial;
    fs << "t_target2cam" << t_target2cam_initial;
    
    // Save robot pose
    cv::Mat R_robot = cv::Mat::eye(3, 3, CV_64F);
    cv::Mat t_robot = cv::Mat(3, 1, CV_64F);
    initialRobotPose = robot->getRobotActualTCP();
    transform3DToRt(initialRobotPose, R_robot, t_robot);
    
    fs << "R_robot" << R_robot;
    fs << "t_robot" << t_robot;
    
    fs.release();
    
    // Update in-memory template
    TrackingTemplate tmpl;
    tmpl.R_target2cam = R_target2cam_initial.clone();
    tmpl.t_target2cam = t_target2cam_initial.clone();
    tmpl.robotPose = initialRobotPose;
    tmpl.isValid = true;
    templates[name] = tmpl;
    
    currentTemplate = name;
    
    LOG(INFO) << "Successfully saved tracking template: " << name;
    return true;
}

bool TrackingManager::loadTemplate(const std::string& name)
{
    // Check if template exists in memory
    if (templates.find(name) != templates.end() && templates[name].isValid) {
        LOG(INFO) << "Loading template from memory: " << name;
        currentTemplate = name;
        
        // Reset initial pose to force reinitialization with the template
        initialPoseSet = false;
        return true;
    }
    
    // Otherwise load from file
    std::string filename = "tracking_template_" + name + ".yml";
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open template file: " << filename;
        return false;
    }
    
    TrackingTemplate tmpl;
    fs["R_target2cam"] >> tmpl.R_target2cam;
    fs["t_target2cam"] >> tmpl.t_target2cam;
    
    cv::Mat R_robot, t_robot;
    fs["R_robot"] >> R_robot;
    fs["t_robot"] >> t_robot;
    
    // Convert to Transform3D
    tmpl.robotPose = rtToTransform3D(R_robot, t_robot);
    tmpl.isValid = true;
    
    // Save to memory
    templates[name] = tmpl;
    currentTemplate = name;
    
    // Reset initial pose to force reinitialization with the template
    initialPoseSet = false;
    
    fs.release();
    LOG(INFO) << "Successfully loaded tracking template: " << name;
    return true;
}

std::vector<std::string> TrackingManager::getAvailableTemplates()
{
    std::vector<std::string> result;
    
    // Add in-memory templates
    for (const auto& pair : templates) {
        if (pair.second.isValid) {
            result.push_back(pair.first);
        }
    }
    
    // Scan directory for template files
    WIN32_FIND_DATA findFileData;
    HANDLE hFind = FindFirstFile("tracking_template_*.yml", &findFileData);
    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            std::string filename = findFileData.cFileName;
            std::string templateName = filename.substr(18, filename.length() - 22); // Remove "tracking_template_" and ".yml"
            
            // Add if not already in the list
            if (std::find(result.begin(), result.end(), templateName) == result.end()) {
                // Try to load it to verify
                cv::FileStorage fs(filename, cv::FileStorage::READ);
                if (fs.isOpened()) {
                    result.push_back(templateName);
                    fs.release();
                }
            }
        } while (FindNextFile(hFind, &findFileData) != 0);
        FindClose(hFind);
    }
    
    return result;
}

std::string TrackingManager::getCurrentTemplate() const
{
    return currentTemplate;
}

void TrackingManager::setCurrentTemplate(const std::string& name)
{
    currentTemplate = name;
}

bool TrackingManager::toggleTracking()
{
    trackingEnabled = !trackingEnabled;
    
    if (trackingEnabled) {
        // Reset tracking state when enabling
        initialPoseSet = false;
        positionFilter.reset();
        rotationFilter.reset();
    }
    
    return trackingEnabled;
}

bool TrackingManager::isTracking() const
{
    return trackingEnabled;
}

void TrackingManager::setTracking(bool enabled)
{
    trackingEnabled = enabled;
    
    if (trackingEnabled) {
        // Reset tracking state when enabling
        initialPoseSet = false;
        positionFilter.reset();
        rotationFilter.reset();
    }
}

void TrackingManager::setMoveThreshold(double threshold)
{
    moveThreshold = threshold;
}

void TrackingManager::setRotThreshold(double threshold)
{
    rotThreshold = threshold;
}

double TrackingManager::getMoveThreshold() const
{
    return moveThreshold;
}

double TrackingManager::getRotThreshold() const
{
    return rotThreshold;
}

bool TrackingManager::loadCameraCalibration(const std::string& filename)
{
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Cannot open camera calibration file: " << filename;
        return false;
    }
    
    fs["camera_matrix"] >> cameraMatrix;
    fs["distortion_coefficients"] >> distCoeffs;
    fs.release();
    
    return !cameraMatrix.empty() && !distCoeffs.empty();
}

bool TrackingManager::loadHandEyeCalibration(const std::string& filename)
{
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Cannot open hand-eye calibration file: " << filename;
        return false;
    }
    
    fs["rotation_matrix"] >> R_cam2gripper;
    fs["translation_vector"] >> t_cam2gripper;
    fs.release();
    
    return !R_cam2gripper.empty() && !t_cam2gripper.empty();
}

rw::math::Transform3D<> TrackingManager::rtToTransform3D(const cv::Mat& R, const cv::Mat& t)
{
    rw::math::Rotation3D<> rotation(
        R.at<double>(0,0), R.at<double>(0,1), R.at<double>(0,2),
        R.at<double>(1,0), R.at<double>(1,1), R.at<double>(1,2),
        R.at<double>(2,0), R.at<double>(2,1), R.at<double>(2,2)
    );
    
    rw::math::Vector3D<> position(
        t.at<double>(0,0),
        t.at<double>(1,0),
        t.at<double>(2,0)
    );
    
    return rw::math::Transform3D<>(position, rotation);
}

void TrackingManager::transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t)
{
    // Extract rotation matrix
    R = cv::Mat::eye(3, 3, CV_64F);
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            R.at<double>(i,j) = transform.R()(i,j);
        }
    }
    
    // Extract translation vector
    t = cv::Mat(3, 1, CV_64F);
    t.at<double>(0,0) = transform.P()[0];
    t.at<double>(1,0) = transform.P()[1];
    t.at<double>(2,0) = transform.P()[2];
} 