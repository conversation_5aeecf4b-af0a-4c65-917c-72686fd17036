#include "MainWindow.h"
#include <QDebug>
#include <QDateTime>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(nullptr)
    , timer(new QTimer(this))
    , trackingManager(std::make_shared<TrackingManager>())
{
    setupUi();
    initConnections();
    setTechStyleSheet();
    
    // Initialize timer for frame updates
    timer->setInterval(33); // ~30 fps
    connect(timer, &QTimer::timeout, this, &MainWindow::updateFrame);
    timer->start();
    
    // Initialize and populate template list
    updateTemplateList();
    
    statusBar()->showMessage("System ready");
}

MainWindow::~MainWindow()
{
    timer->stop();
    delete ui;
}

void MainWindow::setupUi()
{
    // Main window settings
    setWindowTitle("Hand-Eye Calibration Tracking System");
    resize(1280, 800);
    
    // Main layout
    QWidget *centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    QHBoxLayout *mainLayout = new QHBoxLayout(centralWidget);
    
    // Left panel - Camera view
    QVBoxLayout *leftLayout = new QVBoxLayout();
    cameraViewLabel = new QLabel();
    cameraViewLabel->setMinimumSize(800, 600);
    cameraViewLabel->setAlignment(Qt::AlignCenter);
    cameraViewLabel->setStyleSheet("background-color: #111218; border: 1px solid #3498db;");
    leftLayout->addWidget(cameraViewLabel);
    
    // Status info below camera view
    statusLabel = new QLabel("Tracking: Disabled");
    statusLabel->setAlignment(Qt::AlignCenter);
    leftLayout->addWidget(statusLabel);
    
    // Add left panel to main layout
    mainLayout->addLayout(leftLayout, 7);
    
    // Right panel - Controls
    QVBoxLayout *rightLayout = new QVBoxLayout();
    
    // Tracking controls
    QGroupBox *controlsGroup = new QGroupBox("Controls");
    QVBoxLayout *controlsLayout = new QVBoxLayout(controlsGroup);
    
    startStopButton = new QPushButton("Start Tracking");
    startStopButton->setMinimumHeight(40);
    controlsLayout->addWidget(startStopButton);
    
    // Template management
    QGroupBox *templateGroup = new QGroupBox("Template Management");
    QVBoxLayout *templateLayout = new QVBoxLayout(templateGroup);
    
    templateComboBox = new QComboBox();
    templateLayout->addWidget(templateComboBox);
    
    QHBoxLayout *templateButtonsLayout = new QHBoxLayout();
    saveTemplateButton = new QPushButton("Save");
    loadTemplateButton = new QPushButton("Load");
    templateButtonsLayout->addWidget(saveTemplateButton);
    templateButtonsLayout->addWidget(loadTemplateButton);
    templateLayout->addLayout(templateButtonsLayout);
    
    // New template section
    QHBoxLayout *newTemplateLayout = new QHBoxLayout();
    newTemplateNameEdit = new QLineEdit();
    newTemplateNameEdit->setPlaceholderText("New template name");
    createTemplateButton = new QPushButton("Create");
    newTemplateLayout->addWidget(newTemplateNameEdit);
    newTemplateLayout->addWidget(createTemplateButton);
    templateLayout->addLayout(newTemplateLayout);
    
    // Tracking info
    trackingInfoGroup = new QGroupBox("Tracking Information");
    QVBoxLayout *infoLayout = new QVBoxLayout(trackingInfoGroup);
    
    translationLabel = new QLabel("Translation: 0.000 m");
    rotationLabel = new QLabel("Rotation: 0.000 deg");
    viewAngleLabel = new QLabel("View Angle: 0.000 deg");
    
    infoLayout->addWidget(translationLabel);
    infoLayout->addWidget(rotationLabel);
    infoLayout->addWidget(viewAngleLabel);
    
    // Threshold settings
    QGroupBox *thresholdGroup = new QGroupBox("Threshold Settings");
    QGridLayout *thresholdLayout = new QGridLayout(thresholdGroup);
    
    thresholdLayout->addWidget(new QLabel("Move Threshold:"), 0, 0);
    moveThresholdSlider = new QSlider(Qt::Horizontal);
    moveThresholdSlider->setRange(1, 500); // 0.001m to 0.5m
    moveThresholdSlider->setValue(50); // 0.05m default
    thresholdLayout->addWidget(moveThresholdSlider, 0, 1);
    moveThresholdLabel = new QLabel("0.050 m");
    thresholdLayout->addWidget(moveThresholdLabel, 0, 2);
    
    thresholdLayout->addWidget(new QLabel("Rotation Threshold:"), 1, 0);
    rotThresholdSlider = new QSlider(Qt::Horizontal);
    rotThresholdSlider->setRange(1, 900); // 0.1 to 90 degrees
    rotThresholdSlider->setValue(20); // 2 degrees default (0.2 rad)
    thresholdLayout->addWidget(rotThresholdSlider, 1, 1);
    rotThresholdLabel = new QLabel("2.000 deg");
    thresholdLayout->addWidget(rotThresholdLabel, 1, 2);
    
    // Add all groups to right panel
    rightLayout->addWidget(controlsGroup);
    rightLayout->addWidget(templateGroup);
    rightLayout->addWidget(trackingInfoGroup);
    rightLayout->addWidget(thresholdGroup);
    rightLayout->addStretch();
    
    // Add right panel to main layout
    mainLayout->addLayout(rightLayout, 3);
}

void MainWindow::initConnections()
{
    connect(startStopButton, &QPushButton::clicked, this, &MainWindow::onStartStopTracking);
    connect(saveTemplateButton, &QPushButton::clicked, this, &MainWindow::onSaveTemplate);
    connect(loadTemplateButton, &QPushButton::clicked, this, &MainWindow::onLoadTemplate);
    connect(createTemplateButton, &QPushButton::clicked, this, &MainWindow::onCreateNewTemplate);
    connect(templateComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &MainWindow::onTemplateSelected);
    connect(moveThresholdSlider, &QSlider::valueChanged, this, &MainWindow::onMoveThresholdChanged);
    connect(rotThresholdSlider, &QSlider::valueChanged, this, &MainWindow::onRotThresholdChanged);
}

void MainWindow::updateFrame()
{
    // Get frame and tracking status from manager
    TrackingStatus status;
    cv::Mat frame = trackingManager->processFrame(status);
    
    if (!frame.empty()) {
        // Draw tracking info on frame
        if (status.markersDetected) {
            // Display tracking info
            cv::putText(frame, "Trans: " + std::to_string(status.translationMagnitude) + "m, Rot: " + 
                       std::to_string(status.rotationAngle * 180.0 / M_PI) + "deg",
                       cv::Point(10, 30), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
            
            // View angle info
            cv::putText(frame, "View Angle: " + std::to_string(status.viewAngle) + " deg",
                       cv::Point(10, 60), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 255, 0), 2);
            
            // Warning for perpendicular view
            if (abs(status.viewAngle) < 15.0 || abs(status.viewAngle - 180.0) < 15.0) {
                cv::putText(frame, "Warning: Near perpendicular view!",
                           cv::Point(10, 90), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(0, 0, 255), 2);
            }
        }
        
        // Show tracking status
        std::string statusText = status.trackingEnabled ? "Tracking: Enabled" : "Tracking: Disabled";
        cv::putText(frame, statusText, cv::Point(10, frame.rows - 20),
                   cv::FONT_HERSHEY_SIMPLEX, 0.7, status.trackingEnabled ? cv::Scalar(0, 255, 0) : cv::Scalar(0, 0, 255), 2);
        
        // Show current template
        cv::putText(frame, "Current Template: " + status.currentTemplate,
                   cv::Point(10, frame.rows - 80), cv::FONT_HERSHEY_SIMPLEX, 0.7, cv::Scalar(255, 255, 255), 2);
        
        // Display frame in UI
        displayImage(frame);
        
        // Update tracking info in UI
        updateStatusInfo(status);
    }
}

void MainWindow::displayImage(const cv::Mat& image)
{
    QImage qimg;
    if (image.channels() == 3) {
        cv::Mat rgbImage;
        cv::cvtColor(image, rgbImage, cv::COLOR_BGR2RGB);
        qimg = QImage(rgbImage.data, rgbImage.cols, rgbImage.rows, rgbImage.step, QImage::Format_RGB888);
    } else {
        qimg = QImage(image.data, image.cols, image.rows, image.step, QImage::Format_Grayscale8);
    }
    
    cameraViewLabel->setPixmap(QPixmap::fromImage(qimg).scaled(cameraViewLabel->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation));
}

void MainWindow::updateStatusInfo(const TrackingStatus& status)
{
    // Update tracking labels
    QString transText = "Translation: " + formatValue(status.translationMagnitude, 3, "m");
    QString rotText = "Rotation: " + formatValue(status.rotationAngle * 180.0 / M_PI, 3, "deg");
    QString viewText = "View Angle: " + formatValue(status.viewAngle, 3, "deg");
    
    translationLabel->setText(transText);
    rotationLabel->setText(rotText);
    viewAngleLabel->setText(viewText);
    
    // Update status label
    statusLabel->setText(status.trackingEnabled ? "Tracking: Enabled" : "Tracking: Disabled");
    statusLabel->setStyleSheet(status.trackingEnabled ? 
                              "color: #2ecc71; font-weight: bold;" : 
                              "color: #e74c3c; font-weight: bold;");
}

QString MainWindow::formatValue(double value, int precision, const QString& unit)
{
    return QString::number(value, 'f', precision) + " " + unit;
}

void MainWindow::updateTemplateList()
{
    templateComboBox->clear();
    
    // Get available templates from tracking manager
    std::vector<std::string> templates = trackingManager->getAvailableTemplates();
    
    for (const auto& tmpl : templates) {
        templateComboBox->addItem(QString::fromStdString(tmpl));
    }
    
    // Select current template
    QString currentTemplate = QString::fromStdString(trackingManager->getCurrentTemplate());
    int index = templateComboBox->findText(currentTemplate);
    if (index >= 0) {
        templateComboBox->setCurrentIndex(index);
    }
}

void MainWindow::onStartStopTracking()
{
    bool isTracking = trackingManager->toggleTracking();
    startStopButton->setText(isTracking ? "Stop Tracking" : "Start Tracking");
    startStopButton->setStyleSheet(isTracking ? 
                                 "background-color: #e74c3c; color: white;" : 
                                 "background-color: #2ecc71; color: white;");
}

void MainWindow::onSaveTemplate()
{
    QString templateName = newTemplateNameEdit->text();
    if (templateName.isEmpty()) {
        templateName = QString::fromStdString(trackingManager->getCurrentTemplate());
    }
    
    if (trackingManager->saveTemplate(templateName.toStdString())) {
        QMessageBox::information(this, "Success", "Template saved successfully!");
        updateTemplateList();
    } else {
        QMessageBox::warning(this, "Error", "Failed to save template!");
    }
}

void MainWindow::onLoadTemplate()
{
    QString templateName = templateComboBox->currentText();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "Error", "No template selected!");
        return;
    }
    
    if (trackingManager->loadTemplate(templateName.toStdString())) {
        QMessageBox::information(this, "Success", "Template loaded successfully!");
    } else {
        QMessageBox::warning(this, "Error", "Failed to load template!");
    }
}

void MainWindow::onCreateNewTemplate()
{
    QString templateName = newTemplateNameEdit->text();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "Error", "Please enter a template name!");
        return;
    }
    
    trackingManager->setCurrentTemplate(templateName.toStdString());
    // Try to save the template instead of just setting the name
    if (trackingManager->saveTemplate(templateName.toStdString())) {
        newTemplateNameEdit->clear();
        QMessageBox::information(this, "Success", "New template created: " + templateName);
        updateTemplateList();
    } else {
        QMessageBox::warning(this, "Error", "Failed to create template. Make sure markers are detected and tracking is initialized.");
    }
}

void MainWindow::onTemplateSelected(int index)
{
    if (index >= 0) {
        QString templateName = templateComboBox->itemText(index);
        trackingManager->setCurrentTemplate(templateName.toStdString());
    }
}

void MainWindow::onMoveThresholdChanged(int value)
{
    double threshold = value / 1000.0; // Convert to meters
    trackingManager->setMoveThreshold(threshold);
    moveThresholdLabel->setText(formatValue(threshold, 3, "m"));
}

void MainWindow::onRotThresholdChanged(int value)
{
    double threshold = value / 10.0; // Convert to degrees
    double thresholdRad = threshold * M_PI / 180.0; // Convert to radians
    trackingManager->setRotThreshold(thresholdRad);
    rotThresholdLabel->setText(formatValue(threshold, 3, "deg"));
}

void MainWindow::setTechStyleSheet()
{
    // Set tech-style dark theme for the entire application
    setStyleSheet(
        "QMainWindow, QDialog { background-color: #1a1a2e; color: #e0e0e0; }"
        "QGroupBox { background-color: #16213e; color: #e0e0e0; border: 1px solid #3a86ff; border-radius: 5px; margin-top: 1ex; }"
        "QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top center; padding: 0 3px; }"
        "QPushButton { background-color: #3a86ff; color: white; border: none; padding: 5px; border-radius: 3px; }"
        "QPushButton:hover { background-color: #4361ee; }"
        "QPushButton:pressed { background-color: #3a0ca3; }"
        "QLineEdit, QComboBox { background-color: #0f3460; color: #e0e0e0; border: 1px solid #3a86ff; border-radius: 3px; padding: 3px; }"
        "QLabel { color: #e0e0e0; }"
        "QStatusBar { background-color: #16213e; color: #4cc9f0; }"
        "QSlider::groove:horizontal { height: 8px; background: #16213e; }"
        "QSlider::handle:horizontal { background: #3a86ff; border: none; width: 18px; margin: -5px 0; border-radius: 9px; }"
    );
} 