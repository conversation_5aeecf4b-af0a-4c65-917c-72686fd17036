#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QLineEdit>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QListWidget>
#include <QStatusBar>
#include <QMessageBox>
#include <QFileDialog>
#include <QImage>
#include <QPixmap>
#include <QString>
#include <QSlider>
#include <QCheckBox>
#include <opencv2/opencv.hpp>
#include "TrackingManager.h"
#include "KalmanFilter3D.h"

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void updateFrame();
    void onStartStopTracking();
    void onSaveTemplate();
    void onLoadTemplate();
    void onTemplateSelected(int index);
    void onCreateNewTemplate();
    void onMoveThresholdChanged(int value);
    void onRotThresholdChanged(int value);

private:
    Ui::MainWindow *ui;
    QTimer *timer;
    QLabel *cameraViewLabel;
    QPushButton *startStopButton;
    QPushButton *saveTemplateButton;
    QPushButton *loadTemplateButton;
    QComboBox *templateComboBox;
    QLineEdit *newTemplateNameEdit;
    QPushButton *createTemplateButton;
    QLabel *statusLabel;
    QLabel *infoLabel;
    QGroupBox *trackingInfoGroup;
    QLabel *translationLabel;
    QLabel *rotationLabel;
    QLabel *viewAngleLabel;
    QSlider *moveThresholdSlider;
    QSlider *rotThresholdSlider;
    QLabel *moveThresholdLabel;
    QLabel *rotThresholdLabel;
    
    std::shared_ptr<TrackingManager> trackingManager;
    
    void setupUi();
    void initConnections();
    void updateTemplateList();
    void displayImage(const cv::Mat& image);
    void updateStatusInfo(const TrackingStatus& status);
    QString formatValue(double value, int precision = 3, const QString& unit = "");
    void setTechStyleSheet();
};

#endif // MAINWINDOW_H 