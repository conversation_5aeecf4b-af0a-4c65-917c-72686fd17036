#ifndef TRACKINGMANAGER_H
#define TRACKINGMANAGER_H

#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include "HikVisionCamera.h"
#include "AuboArcsDriver.h"
#include "rw/math.hpp"
#include "KalmanFilter3D.h"

// Structure to hold tracking template data
struct TrackingTemplate {
    cv::Mat R_target2cam;
    cv::Mat t_target2cam;
    rw::math::Transform3D<> robotPose;
    bool isValid;

    TrackingTemplate() : isValid(false) {}
};

// Structure to hold tracking status information
struct TrackingStatus {
    bool trackingEnabled = false;
    bool markersDetected = false;
    double translationMagnitude = 0.0;
    double rotationAngle = 0.0;
    double viewAngle = 0.0;
    std::string currentTemplate = "default";
    bool templateAvailable = false;
    bool initialPoseSet = false;
};

class TrackingManager {
public:
    TrackingManager();
    ~TrackingManager();

    // Initialize components
    bool initialize();
    
    // Process a new frame and update tracking
    cv::Mat processFrame(TrackingStatus& status);
    
    // Template management
    bool saveTemplate(const std::string& name);
    bool loadTemplate(const std::string& name);
    std::vector<std::string> getAvailableTemplates();
    std::string getCurrentTemplate() const;
    void setCurrentTemplate(const std::string& name);
    
    // Tracking control
    bool toggleTracking();
    bool isTracking() const;
    void setTracking(bool enabled);
    
    // Threshold settings
    void setMoveThreshold(double threshold);
    void setRotThreshold(double threshold);
    double getMoveThreshold() const;
    double getRotThreshold() const;

private:
    // Components
    std::shared_ptr<AuboArcsDriver> robot;
    std::shared_ptr<HikVisionCamera> camera;
    
    // Calibration parameters
    cv::Mat cameraMatrix, distCoeffs;
    cv::Mat R_cam2gripper, t_cam2gripper;
    
    // ArUco/ChArUco detection
    cv::Ptr<cv::aruco::Dictionary> dictionary;
    cv::Ptr<cv::aruco::CharucoBoard> board;
    
    // Tracking state
    bool trackingEnabled;
    double moveThreshold;
    double rotThreshold;
    std::string currentTemplate;
    
    // Templates storage
    std::map<std::string, TrackingTemplate> templates;
    
    // Initial pose tracking
    cv::Mat R_target2cam_initial, t_target2cam_initial;
    bool initialPoseSet;
    rw::math::Transform3D<> initialRobotPose;
    
    // Kalman filters for smoothing
    KalmanFilter3D positionFilter;
    KalmanFilter3D rotationFilter;
    
    // Helper methods
    bool loadCameraCalibration(const std::string& filename);
    bool loadHandEyeCalibration(const std::string& filename);
    rw::math::Transform3D<> rtToTransform3D(const cv::Mat& R, const cv::Mat& t);
    void transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t);
    bool detectChArUcoBoard(const cv::Mat& frame, std::vector<int>& charucoIds, 
                          std::vector<cv::Point2f>& charucoCorners);
    bool estimateBoardPose(const std::vector<cv::Point2f>& charucoCorners, 
                         const std::vector<int>& charucoIds,
                         cv::Mat& rvec, cv::Mat& tvec);
    void updateRobotPosition(const cv::Mat& R_target2cam, const cv::Mat& t_target2cam);
};

#endif // TRACKINGMANAGER_H 