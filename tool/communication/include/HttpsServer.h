#pragma once

#include <string>
#include <map>
#include <functional>
#include <memory>
#include <json/json.h>

#include <openssl/ssl.h>
#include <windows.h>


class HttpsServer {
public:
    HttpsServer(const std::string& cert_file, const std::string& key_file, int port = 443);
    ~HttpsServer();

    // 启动服务器
    bool start();
    // 停止服务器
    void stop();

    // 任务状态枚举
    enum class TaskStatus {
        PENDING,
        RUNNING,
        COMPLETED,
        FAILED
    };

    // 系统状态结构
    struct SystemStatus {
        bool has_alarm;                  // 是否有报警
        std::string alarm_level;         // 报警等级
        std::string alarm_type;          // 报警类型
        std::string alarm_description;   // 报警描述
        std::string alarm_time;          // 报警时间
    };

private:
    // 处理HTTPS请求的回调函数
    void handleRequest(struct evhttp_request* req);
    
    // 处理不同类型的请求
    void handleTaskSubmission(const Json::Value& request, Json::Value& response);
    void handleTaskStatus(const Json::Value& request, Json::Value& response);
    void handleSystemStatus(Json::Value& response);
    void handleSetParameter(const Json::Value& request, Json::Value& response);
    void handleGetParameter(const Json::Value& request, Json::Value& response);

    // SSL相关
    bool initializeSSL();
    static SSL_CTX* createSSLContext();
    
    // 获取当前时间字符串
    std::string getCurrentTimeString();

    // 成员变量
    std::string cert_file_;
    std::string key_file_;
    int port_;
    
    struct event_base* base_;
    struct evhttp* http_;
    SSL_CTX* ssl_ctx_;
    
    // 任务管理
    std::map<std::string, TaskStatus> tasks_;
    // 参数存储
    std::map<std::string, Json::Value> parameters_;
    
    bool running_;
    WSADATA wsaData_;  // Windows套接字初始化数据
}; 