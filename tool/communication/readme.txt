私钥生成

openssl genrsa -out rsa_1024_priv.pem 1024

公钥生成

openssl rsa -pubout -in rsa_1024_priv.pem -out rsa_1024_pub.pem

Windows 系统使用 OpenSSL

1. 安装 OpenSSL
- 下载 OpenSSL 安装包：https://slproweb.com/products/Win32OpenSSL.html
- 选择适合的版本（32位或64位）进行安装
- 建议选择完整版本（不要选 Light 版本）

2. 配置环境变量
- 将 OpenSSL 安装目录下的 bin 文件夹添加到系统环境变量 Path 中
- 例如：C:\Program Files\OpenSSL-Win64\bin

3. 在项目中引用 OpenSSL
- 将 OpenSSL 安装目录下的 lib 文件夹添加到项目的库目录
- 添加以下库文件到项目：
  * libssl.lib
  * libcrypto.lib

4. 包含头文件
- 在代码中添加：
  #include <openssl/ssl.h>
  #include <openssl/err.h>

注意：如果使用 Visual Studio，确保项目配置中包含了正确的库路径和依赖项。

