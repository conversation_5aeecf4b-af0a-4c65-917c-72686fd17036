#include "HttpsServer.h"
#pragma comment(lib, "ws2_32.lib")
#include <chrono>
#include <event2/event.h>
#include <event2/http.h>
#include <event2/buffer.h>
#include <event2/util.h>
#include <event2/bufferevent.h>
#include <event2/bufferevent_ssl.h>
#include <event2/keyvalq_struct.h>
#include <openssl/err.h>
#include <openssl/ssl.h>
#include <sstream>
#include <ctime>
#include <iomanip>


HttpsServer::HttpsServer(const std::string& cert_file, const std::string& key_file, int port)
    : cert_file_(cert_file)
    , key_file_(key_file)
    , port_(port)
    , base_(nullptr)
    , http_(nullptr)
    , ssl_ctx_(nullptr)
    , running_(false)
{
    // 初始化Windows套接字
    if (WSAStartup(MAKEWORD(2, 2), &wsaData_) != 0) {
        throw std::runtime_error("Windows套接字初始化失败");
    }
}

HttpsServer::~HttpsServer() {
    stop();
    if (ssl_ctx_) {
        SSL_CTX_free(ssl_ctx_);
    }
    WSACleanup();  // 清理Windows套接字
}

bool HttpsServer::start() {
    if (!initializeSSL()) {
        return false;
    }

    base_ = event_base_new();
    if (!base_) {
        return false;
    }

    http_ = evhttp_new(base_);
    if (!http_) {
        return false;
    }

    // 设置通用回调
    evhttp_set_gencb(http_, [](evhttp_request* req, void* arg) {
        static_cast<HttpsServer*>(arg)->handleRequest(req);
    }, this);

    // 设置HTTPS
    evhttp_set_bevcb(http_, [](struct event_base* base, void* arg) -> bufferevent* {
        SSL_CTX* ctx = static_cast<SSL_CTX*>(arg);
        SSL* ssl = SSL_new(ctx);
        if (!ssl) return nullptr;
        
        // 创建基础的 bufferevent
        struct bufferevent* bev = bufferevent_socket_new(
            base,
            -1,
            BEV_OPT_CLOSE_ON_FREE
        );

        if (!bev) {
            SSL_free(ssl);
            return nullptr;
        }

        // 获取 buffer
        struct evbuffer* input = bufferevent_get_input(bev);
        struct evbuffer* output = bufferevent_get_output(bev);
        
        // 设置 SSL 的 BIO
        BIO* bio = BIO_new(BIO_s_mem());
        if (!bio) {
            bufferevent_free(bev);
            SSL_free(ssl);
            return nullptr;
        }

        SSL_set_bio(ssl, bio, bio);
        SSL_set_accept_state(ssl);  // 服务器模式

        // 设置读写回调
        bufferevent_setcb(bev, 
            // 读回调
            [](struct bufferevent* bev, void* ctx) {
                SSL* ssl = static_cast<SSL*>(ctx);
                char buffer[4096];
                size_t bytes_read;
                SSL_read_ex(ssl, buffer, sizeof(buffer), &bytes_read);
            },
            // 写回调
            [](struct bufferevent* bev, void* ctx) {
                SSL* ssl = static_cast<SSL*>(ctx);
                char buffer[4096];
                size_t bytes_written;
                SSL_write_ex(ssl, buffer, sizeof(buffer), &bytes_written);
            },
            // 事件回调
            [](struct bufferevent* bev, short events, void* ctx) {
                if (events & BEV_EVENT_ERROR) {
                    SSL* ssl = static_cast<SSL*>(ctx);
                    SSL_free(ssl);
                    bufferevent_free(bev);
                }
            },
            ssl
        );

        bufferevent_enable(bev, EV_READ | EV_WRITE);
        return bev;
    }, ssl_ctx_);

    if (evhttp_bind_socket(http_, "0.0.0.0", port_) != 0) {
        return false;
    }

    running_ = true;
    event_base_dispatch(base_);
    return true;
}

void HttpsServer::stop() {
    if (running_) {
        event_base_loopbreak(base_);
        running_ = false;
    }
    if (http_) {
        evhttp_free(http_);
        http_ = nullptr;
    }
    if (base_) {
        event_base_free(base_);
        base_ = nullptr;
    }
}

void HttpsServer::handleRequest(struct evhttp_request* req) {
    // 读取请求内容
    struct evbuffer* input = evhttp_request_get_input_buffer(req);
    size_t len = evbuffer_get_length(input);
    std::vector<char> data(len + 1);
    evbuffer_copyout(input, data.data(), len);
    data[len] = '\0';

    // 解析JSON
    Json::Value request;
    Json::Value response;
    Json::Reader reader;
    
    if (!reader.parse(data.data(), request)) {
        response["status"] = "error";
        response["message"] = "Invalid JSON format";
    } else {
        std::string action = request.get("action", "").asString();
        
        try {
            if (action == "submit_task") {
                handleTaskSubmission(request, response);
            } else if (action == "get_task_status") {
                handleTaskStatus(request, response);
            } else if (action == "get_system_status") {
                handleSystemStatus(response);
            } else if (action == "set_parameter") {
                handleSetParameter(request, response);
            } else if (action == "get_parameter") {
                handleGetParameter(request, response);
            } else {
                response["status"] = "error";
                response["message"] = "Unknown action";
            }
        }
        catch (const std::exception& e) {
            response["status"] = "error";
            response["message"] = e.what();
        }
    }

    // 发送响应
    Json::FastWriter writer;
    std::string response_str = writer.write(response);
    
    struct evbuffer* output = evhttp_request_get_output_buffer(req);
    evbuffer_add(output, response_str.c_str(), response_str.length());
    
    evhttp_add_header(evhttp_request_get_output_headers(req),
        "Content-Type", "application/json; charset=utf-8");
    evhttp_send_reply(req, HTTP_OK, "OK", output);
}

void HttpsServer::handleTaskSubmission(const Json::Value& request, Json::Value& response) {
    std::string task_id = request.get("task_id", "").asString();
    if (task_id.empty()) {
        response["status"] = "error";
        response["message"] = "Task ID is required";
        return;
    }

    tasks_[task_id] = TaskStatus::PENDING;
    // 这里可以添加实际的任务处理逻辑
    
    response["status"] = "success";
    response["task_id"] = task_id;
}

void HttpsServer::handleTaskStatus(const Json::Value& request, Json::Value& response) {
    std::string task_id = request.get("task_id", "").asString();
    auto it = tasks_.find(task_id);
    
    if (it == tasks_.end()) {
        response["status"] = "error";
        response["message"] = "Task not found";
        return;
    }

    response["status"] = "success";
    response["task_status"] = static_cast<int>(it->second);
}

void HttpsServer::handleSystemStatus(Json::Value& response) {
    SystemStatus status;
    status.has_alarm = false;
    status.alarm_level = "normal";
    status.alarm_type = "";
    status.alarm_description = "";
    status.alarm_time = getCurrentTimeString();

    // 这里可以添加实际的系统报警检测逻辑
    // 示例：检测内存使用情况
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    if (GlobalMemoryStatusEx(&memInfo)) {
        if (memInfo.dwMemoryLoad > 90) {  // 内存使用率超过90%
            status.has_alarm = true;
            status.alarm_level = "warning";
            status.alarm_type = "memory";
            status.alarm_description = "System memory usage is above 90%";
        }
    }

    response["status"] = "success";
    response["has_alarm"] = status.has_alarm;
    
    if (status.has_alarm) {
        response["alarm_level"] = status.alarm_level;
        response["alarm_type"] = status.alarm_type;
        response["alarm_description"] = status.alarm_description;
        response["alarm_time"] = status.alarm_time;
    }
}

void HttpsServer::handleSetParameter(const Json::Value& request, Json::Value& response) {
    std::string param_name = request.get("name", "").asString();
    if (param_name.empty()) {
        response["status"] = "error";
        response["message"] = "Parameter name is required";
        return;
    }

    parameters_[param_name] = request["value"];
    response["status"] = "success";
}

void HttpsServer::handleGetParameter(const Json::Value& request, Json::Value& response) {
    std::string param_name = request.get("name", "").asString();
    auto it = parameters_.find(param_name);
    
    if (it == parameters_.end()) {
        response["status"] = "error";
        response["message"] = "Parameter not found";
        return;
    }

    response["status"] = "success";
    response["value"] = it->second;
}

bool HttpsServer::initializeSSL() {
    SSL_load_error_strings();
    OpenSSL_add_ssl_algorithms();

    ssl_ctx_ = createSSLContext();
    if (!ssl_ctx_) {
        return false;
    }

    if (SSL_CTX_use_certificate_file(ssl_ctx_, cert_file_.c_str(), SSL_FILETYPE_PEM) <= 0) {
        return false;
    }

    if (SSL_CTX_use_PrivateKey_file(ssl_ctx_, key_file_.c_str(), SSL_FILETYPE_PEM) <= 0) {
        return false;
    }

    return true;
}

SSL_CTX* HttpsServer::createSSLContext() {
    const SSL_METHOD* method = TLS_server_method();
    SSL_CTX* ctx = SSL_CTX_new(method);

    if (!ctx) {
        return nullptr;
    }

    SSL_CTX_set_ecdh_auto(ctx, 1);
    return ctx;
}

std::string HttpsServer::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time), "%Y-%m-%d %H:%M:%S");
    return ss.str();
} 