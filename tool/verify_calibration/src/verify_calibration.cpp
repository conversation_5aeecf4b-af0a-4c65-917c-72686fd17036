#include <opencv2/opencv.hpp>
#include <iostream>
#include <glog.h>



using namespace std;
using namespace cv;

void drawGrid(Mat& img, int spacing) {
    const int width = img.cols;
    const int height = img.rows;
    
    // 绘制垂直线
    for (int x = 0; x < width; x += spacing) {
        line(img, Point(x, 0), Point(x, height), Scalar(0, 255, 0), 1);
    }
    
    // 绘制水平线
    for (int y = 0; y < height; y += spacing) {
        line(img, Point(0, y), Point(width, y), Scalar(0, 255, 0), 1);
    }
}

int main(int argc, char** argv) {

    // 读取标定参数
    FileStorage fs("camera_calibration.yml", FileStorage::READ);
    if (!fs.isOpened()) {

        return -1;
    }

    Mat cameraMatrix, distCoeffs;
    fs["camera_matrix"] >> cameraMatrix;
    fs["distortion_coefficients"] >> distCoeffs;
    int image_width, image_height;
    fs["image_width"] >> image_width;
    fs["image_height"] >> image_height;
    fs.release();



    // 打开相机
    VideoCapture cap;
    if (!cap.open(0, CAP_V4L2)) {

        return -1;
    }

    // 设置相机参数
    cap.set(CAP_PROP_FOURCC, VideoWriter::fourcc('M','J','P','G'));
    cap.set(CAP_PROP_FRAME_WIDTH, image_width);
    cap.set(CAP_PROP_FRAME_HEIGHT, image_height);
    cap.set(CAP_PROP_FPS, 30);

    // 计算新的相机矩阵
    Mat newCameraMatrix = getOptimalNewCameraMatrix(
        cameraMatrix, distCoeffs, Size(image_width, image_height), 1);

    // 计算重映射矩阵
    Mat map1, map2;
    initUndistortRectifyMap(
        cameraMatrix, distCoeffs, Mat(), newCameraMatrix,
        Size(image_width, image_height), CV_32FC1, map1, map2);

    int imageCount = 0;
    bool showGrid = false;

    while (true) {
        Mat frame;
        cap >> frame;
        if (frame.empty()) {

            break;
        }

        // 使用重映射进行去畸变
        Mat undistorted;
        remap(frame, undistorted, map1, map2, INTER_LINEAR);

        // 如果开启网格显示，则绘制网格
        if (showGrid) {
            drawGrid(frame, 50);
            drawGrid(undistorted, 50);
        }

        // 并排显示原始图像和去畸变后的图像
        Mat combined;
        hconcat(frame, undistorted, combined);
        
        // 添加文字标签
        putText(combined, "Original", Point(50, 30), 
                FONT_HERSHEY_SIMPLEX, 1, Scalar(0, 255, 0), 2);
        putText(combined, "Undistorted", Point(frame.cols + 50, 30), 
                FONT_HERSHEY_SIMPLEX, 1, Scalar(0, 255, 0), 2);
        
        // 显示操作说明
        putText(combined, "Press 's' to save, 'g' to toggle grid, 'q' to quit", 
                Point(50, combined.rows - 20), 
                FONT_HERSHEY_SIMPLEX, 0.8, Scalar(0, 255, 0), 2);

        imshow("Calibration Verification", combined);

        // 键盘控制
        char key = waitKey(1);
        if (key == 'q') {
            break;
        } else if (key == 's') {
            // 保存图像
            string filename = "calibration_verify_" + to_string(imageCount++) + ".jpg";
            imwrite(filename, combined);

        } else if (key == 'g') {
            // 切换网格显示
            showGrid = !showGrid;
        }
    }

    cap.release();
    destroyAllWindows();

    return 0;
}
