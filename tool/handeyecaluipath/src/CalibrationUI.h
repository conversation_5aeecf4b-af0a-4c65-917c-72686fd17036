#ifndef CALIBRATIONUI_H
#define CALIBRATIONUI_H

#include <QWidget>
#include <QPushButton>
#include <memory>

#include "TrackingManager.h"

class CalibrationUI : public QWidget
{
    Q_OBJECT
    
public:
    explicit CalibrationUI(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent = nullptr);
    ~CalibrationUI();
    
signals:
    // 定义信号
    void calibrationParametersViewed();               // 用户查看了标定参数
    void cameraCalibrationToolLaunched();             // 启动了相机标定工具
    void handEyeCalibrationToolLaunched();            // 启动了手眼标定工具
    void calibrationParametersReloaded(bool success); // 重新加载了标定参数
    
private slots:
    void onShowCalibrationParams();
    
private:
    std::shared_ptr<TrackingManager> trackingManager;
    
    // UI components
    QPushButton *viewCalibrationButton;
    
    void setupUi();
    void initConnections();
    void showCalibrationDialog();
};

#endif // CALIBRATIONUI_H 