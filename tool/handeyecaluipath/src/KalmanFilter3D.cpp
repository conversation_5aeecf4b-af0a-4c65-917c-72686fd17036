#include "KalmanFilter3D.h"

KalmanFilter3D::KalmanFilter3D() : KF(6, 3, 0), initialized(false) {
    // State vector: [x, y, z, vx, vy, vz]
    KF.transitionMatrix = (cv::Mat_<float>(6, 6) <<
        1, 0, 0, 1, 0, 0,
        0, 1, 0, 0, 1, 0,
        0, 0, 1, 0, 0, 1,
        0, 0, 0, 1, 0, 0,
        0, 0, 0, 0, 1, 0,
        0, 0, 0, 0, 0, 1);

    // Initialize measurement matrix
    KF.measurementMatrix = cv::Mat::zeros(3, 6, CV_32F);
    KF.measurementMatrix.at<float>(0, 0) = 1.0f;
    KF.measurementMatrix.at<float>(1, 1) = 1.0f;
    KF.measurementMatrix.at<float>(2, 2) = 1.0f;

    // Set process noise
    setIdentity(KF.processNoiseCov, cv::Scalar::all(1e-5));
    
    // Set measurement noise
    setIdentity(KF.measurementNoiseCov, cv::Scalar::all(1e-2));
    
    // Set error covariance
    setIdentity(KF.errorCovPost, cv::Scalar::all(1));
}

cv::Point3f KalmanFilter3D::update(const cv::Point3f& measurement) {
    if (!initialized) {
        // First measurement - initialize the filter
        KF.statePost.at<float>(0) = measurement.x;
        KF.statePost.at<float>(1) = measurement.y;
        KF.statePost.at<float>(2) = measurement.z;
        KF.statePost.at<float>(3) = 0;  // Initial velocity is zero
        KF.statePost.at<float>(4) = 0;
        KF.statePost.at<float>(5) = 0;
        initialized = true;
        return measurement;
    }

    // Predict next state
    cv::Mat prediction = KF.predict();
    
    // Update with new measurement
    cv::Mat measurement_mat = (cv::Mat_<float>(3, 1) << 
                               measurement.x, measurement.y, measurement.z);
    cv::Mat estimated = KF.correct(measurement_mat);

    // Return filtered position
    return cv::Point3f(estimated.at<float>(0), 
                       estimated.at<float>(1), 
                       estimated.at<float>(2));
}

void KalmanFilter3D::reset() {
    initialized = false;
} 