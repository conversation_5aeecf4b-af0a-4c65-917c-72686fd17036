#ifndef TRACKINGMANAGER_H
#define TRACKINGMANAGER_H

#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include "HikVisionCamera.h"
#include "AuboArcsDriver.h"
#include "rw/math.hpp"
#include "KalmanFilter3D.h"

// Structure to hold tracking template data
struct TrackingTemplate {
    cv::Mat R_target2cam;
    cv::Mat t_target2cam;
    rw::math::Transform3D<> robotPose;
    bool isValid;

    TrackingTemplate() : isValid(false) {}
};

// Structure to hold tracking status information
struct TrackingStatus {
    bool trackingEnabled = false;
    bool markersDetected = false;
    double translationMagnitude = 0.0;
    double rotationAngle = 0.0;
    double viewAngle = 0.0;
    std::string currentTemplate = "default";
    bool templateAvailable = false;
    bool initialPoseSet = false;
};

class TrackingManager {
public:
    TrackingManager();
    ~TrackingManager();

    // Initialize components
    bool initialize();
    
    // Process a new frame and update tracking
    cv::Mat processFrame(TrackingStatus& status);


    
    // Template management
    bool saveTemplate(const std::string& name);
    bool loadTemplate(const std::string& name);
    std::vector<std::string> getAvailableTemplates();
    std::string getCurrentTemplate() const;
    void setCurrentTemplate(const std::string& name);
    
    // Tracking control
    bool toggleTracking();
    bool isTracking() const;
    void setTracking(bool enabled);
    
    // Threshold settings
    void setMoveThreshold(double threshold);
    void setRotThreshold(double threshold);
    double getMoveThreshold() const;
    double getRotThreshold() const;
    
    // Robot control methods for path management
    rw::math::Transform3D<> getRobotPose() const;
    rw::math::Q getRobotJointConfig() const;
    bool moveRobotJoint(const rw::math::Q& jointConfig, double speed = 0.8, double acceleration = 0.2);
    bool moveRobotLinear(const rw::math::Transform3D<>& pose, double speed = 0.8, double acceleration = 0.1);
    bool setRobotIO(int port, bool state);
    
    // Inverse kinematics
    bool getIKForPose(const rw::math::Transform3D<>& pose, rw::math::Q& jointConfig);
    
    // Path correction functionality
    rw::math::Transform3D<> getTemplateDifference() const;
    rw::math::Transform3D<> getTemplateRobotPose() const;
    
    // 新增方法：移动机器人到模板记录的位置
    bool moveToTemplatePosition(const std::string& templateName);
    
    // 新增方法：获取当前标定板在相机坐标系下的位姿
    rw::math::Transform3D<> getCurrentBoardPose() const;
    
    // 新增方法：获取模板中标定板在相机坐标系下的位姿
    rw::math::Transform3D<> getTemplateBoardPose() const;
    
    // 新增方法：获取相机到机器人手爪的变换
    rw::math::Transform3D<> getCameraToGripperTransform() const;
    
    bool isInitialPoseSet() const { return initialPoseSet; }

    // 新增方法：获取相机内参矩阵
    cv::Mat getCameraMatrix() const;
    
    // 新增方法：获取相机畸变系数
    cv::Mat getDistCoeffs() const;
    
    // 新增方法：获取手眼标定旋转矩阵
    cv::Mat getRotationMatrix() const;
    
    // 新增方法：获取手眼标定平移向量
    cv::Mat getTranslationVector() const;
    
    // 新增方法：重新加载标定参数
    bool reloadCalibration();

private:
    // Components
    std::shared_ptr<AuboArcsDriver> robot;
    std::shared_ptr<HikVisionCamera> camera;
    
    // Calibration parameters
    cv::Mat cameraMatrix, distCoeffs;
    cv::Mat R_cam2gripper, t_cam2gripper;
    
    // ArUco/ChArUco detection
    cv::Ptr<cv::aruco::Dictionary> dictionary;
    cv::Ptr<cv::aruco::CharucoBoard> board;
    
    // Tracking state
    bool trackingEnabled;
    double moveThreshold;
    double rotThreshold;
    std::string currentTemplate;
    
    // Templates storage
    std::map<std::string, TrackingTemplate> templates;
    
    // 跟踪位姿管理
    // 初始检测时的标定板在相机中的姿态
    cv::Mat R_target2cam_initial, t_target2cam_initial;
    
    // 初始设置标志
    bool initialPoseSet;
    
    // 模板参考位姿 - 保存模板时的机器人位姿（不应在跟踪过程中更新）
    rw::math::Transform3D<> templateRobotPose;
    
    // 跟踪参考位姿 - 用于计算相对运动的参考位姿（在跟踪过程中会更新）
    rw::math::Transform3D<> referenceRobotPose;
    
    // 初始机器人位姿 - 仅在首次设置初始位姿时记录，不应被更新
    rw::math::Transform3D<> initialRobotPose;
    
    // Current board pose data
    cv::Mat R_target2cam, t_target2cam;
    
    // Kalman filters for smoothing
    KalmanFilter3D positionFilter;
    KalmanFilter3D rotationFilter;
    
    // Helper methods
    bool loadCameraCalibration(const std::string& filename);
    bool loadHandEyeCalibration(const std::string& filename);
    rw::math::Transform3D<> rtToTransform3D(const cv::Mat& R, const cv::Mat& t) const;
    void transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t);
    bool detectChArUcoBoard(const cv::Mat& frame, std::vector<int>& charucoIds, 
                          std::vector<cv::Point2f>& charucoCorners);
    bool estimateBoardPose(const std::vector<cv::Point2f>& charucoCorners, 
                         const std::vector<int>& charucoIds,
                         cv::Mat& rvec, cv::Mat& tvec);
    void updateRobotPosition(const cv::Mat& R_target2cam, const cv::Mat& t_target2cam);
};

#endif // TRACKINGMANAGER_H 