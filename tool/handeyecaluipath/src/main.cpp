#include <QApplication>
#include <glog.h>
#include <csignal>
#include <iostream>
#include "MainWindow.h"

// Global variables for signal handling
MainWindow* g_mainWindow = nullptr;
QApplication* g_app = nullptr;

// Signal handler function
void signalHandler(int signal) {
    std::cout << "Received interrupt signal, safely exiting..." << std::endl;
    if (g_mainWindow) {
        g_mainWindow->close();
    }
    if (g_app) {
        g_app->quit();
    }
}

int main(int argc, char *argv[]) {
    // Initialize glog
    google::InitGoogleLogging(argv[0]);
    FLAGS_colorlogtostderr = true;
    FLAGS_logtostderr = true;
    
    // Register signal handler
    std::signal(SIGINT, signalHandler);
    
    // Create Qt application
    QApplication app(argc, argv);
    g_app = &app;
    
    // Set application style
    app.setStyle("Fusion");
    
    {
        // Create main window in a separate scope
        MainWindow window;
        g_mainWindow = &window;
        window.show();
        
        LOG(INFO) << "Hand-Eye Calibration Tracking System started";
        
        // Start the event loop
        int result = app.exec();
        
        // Clear global pointer before window is destroyed
        g_mainWindow = nullptr;
        
        LOG(INFO) << "Application exiting";
        return result;
    }
} 