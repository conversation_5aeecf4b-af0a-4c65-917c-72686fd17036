#include "TrackingManager.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <glog.h>
#include <Windows.h>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>

TrackingManager::TrackingManager()
    : trackingEnabled(false)
    , moveThreshold(0.001) // 5cm default
    , rotThreshold(0.0017)   // ~11 degrees default
    , currentTemplate("default")
    , initialPoseSet(false)
{
    // Initialize the ArUco dictionary and ChArUco board
    cv::aruco::Dictionary dictionaryTmp = cv::aruco::getPredefinedDictionary(cv::aruco::DICT_4X4_50);
    dictionary = cv::Ptr<cv::aruco::Dictionary>(new cv::aruco::Dictionary(dictionaryTmp));

    const int squaresX = 5;
    const int squaresY = 7;
    const float squareLength = 0.0112f;
    const float markerLength = 0.0087f;

    board = cv::Ptr<cv::aruco::CharucoBoard>(
        new cv::aruco::CharucoBoard({squaresX, squaresY}, squareLength, markerLength, *dictionary));

    initialize();
}

TrackingManager::~TrackingManager()
{
    // Clean up resources
    if (robot) {
        robot->stop();
    }

    if (camera) {
        camera->stopGrabbing();
        camera->disconnect();
    }
}

bool TrackingManager::initialize()
{
    LOG(INFO) << "Initializing TrackingManager...";

    // Load camera calibration
    if (!loadCameraCalibration("camera_calibration.yml")) {
        LOG(ERROR) << "Failed to load camera calibration parameters";
        return false;
    }

    // Load hand-eye calibration
    if (!loadHandEyeCalibration("hand_eye_calibration.yml")) {
        LOG(ERROR) << "Failed to load hand-eye calibration parameters";
        return false;
    }

    // Initialize robot
    robot = std::make_shared<AuboArcsDriver>();
    if (!robot->connect("192.168.1.189", 8899)) {
        LOG(ERROR) << "Failed to connect to robot";
        return false;
    }

    // Initialize camera
    camera = std::make_shared<HikVisionCamera>();
    camera->setErrorCallback([](const std::string &error) {
        LOG(ERROR) << "Camera error: " << error;
    });

    if (!camera->connectToDevice("*********")) {  // Replace with actual camera serial
        LOG(ERROR) << "Failed to connect to camera";
        return false;
    }

    // Set camera parameters
    camera->setExposureTime(20000.0f);
    camera->setGain(0.0f);
    camera->setFrameRate(30.0f);

    if (!camera->startGrabbing()) {
        LOG(ERROR) << "Failed to start image acquisition";
        return false;
    }

    LOG(INFO) << "TrackingManager initialized successfully";
    return true;
}




cv::Mat TrackingManager::processFrame(TrackingStatus& status)
{

    // Get frame from camera
    if (camera)
    {
        frame = camera->getFrame();
        if (frame.empty()) {
            LOG(ERROR) << "Failed to capture frame";
            return frame;
        }
    }else
    {
        return frame;
    }


    cv::Mat displayFrame = frame.clone();

    // Update tracking status
    status.trackingEnabled = trackingEnabled;
    status.currentTemplate = currentTemplate;
    status.templateAvailable = !templates.empty();
    status.initialPoseSet = initialPoseSet;

    // Detect ChArUco markers
    std::vector<int> charucoIds;
    std::vector<cv::Point2f> charucoCorners;
    status.markersDetected = detectChArUcoBoard(frame, charucoIds, charucoCorners);

    if (status.markersDetected) {
        // Draw detected corners
        cv::aruco::drawDetectedCornersCharuco(displayFrame, charucoCorners, charucoIds);

        // Estimate board pose
        cv::Mat rvec, tvec;
        if (estimateBoardPose(charucoCorners, charucoIds, rvec, tvec)) {
            // Draw coordinate axes
            cv::drawFrameAxes(displayFrame, cameraMatrix, distCoeffs, rvec, tvec, 0.05);

            // Convert rotation vector to matrix
            cv::Mat R_target2cam;
            cv::Rodrigues(rvec, R_target2cam);

            // Store the current board pose for later use
            this->R_target2cam = R_target2cam.clone();
            this->t_target2cam = tvec.clone();

            // Calculate view angle
            cv::Mat boardNormal = R_target2cam * (cv::Mat_<double>(3, 1) << 0, 0, 1);
            cv::Mat cameraDirection = (cv::Mat_<double>(3, 1) << 0, 0, 1);
            double cosAngle = boardNormal.dot(cameraDirection) /
                             (cv::norm(boardNormal) * cv::norm(cameraDirection));
            status.viewAngle = acos(cosAngle) * 180.0 / M_PI;

            // Initialize pose if needed
            if (!initialPoseSet) {
                // Try to load from template first
                if (templates.find(currentTemplate) != templates.end() && templates[currentTemplate].isValid) {
                    R_target2cam_initial = templates[currentTemplate].R_target2cam.clone();
                    t_target2cam_initial = templates[currentTemplate].t_target2cam.clone();

                    // 使用模板中的机器人位姿作为参考位姿和模板位姿
                    templateRobotPose = templates[currentTemplate].robotPose;
                    referenceRobotPose = templates[currentTemplate].robotPose;
                    initialRobotPose = robot->getRobotActualTCP(); // 记录当前机器人位姿作为初始位姿

                    initialPoseSet = true;
                    LOG(INFO) << "Loaded initial pose from template: " << currentTemplate;
                }
                else {
//                    // 首次设置，所有位姿都设为当前值
                    R_target2cam_initial = R_target2cam.clone();
                    t_target2cam_initial = tvec.clone();

                    rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
                    initialRobotPose = currentRobotPose;
                    referenceRobotPose = currentRobotPose;
                    templateRobotPose = currentRobotPose;
//
                    initialPoseSet = true;
                    LOG(INFO) << "Set initial board pose";
//
                    // Auto-save as template if none exists
                    if (templates.empty()) {
                        saveTemplate(currentTemplate);
                    }
                }

                // Reset Kalman filters when initializing new pose
                positionFilter.reset();
                rotationFilter.reset();
            }

            // Calculate relative transformation
            cv::Mat R_relative = R_target2cam * R_target2cam_initial.t();
            cv::Mat t_relative = tvec - R_relative * t_target2cam_initial;

            // Calculate magnitudes
            status.translationMagnitude = cv::norm(t_relative);
            double trace = R_relative.at<double>(0, 0) + R_relative.at<double>(1, 1) + R_relative.at<double>(2, 2);
            status.rotationAngle = acos((trace - 1.0) / 2.0);

            // Update robot position if tracking is enabled and thresholds are exceeded
            if (trackingEnabled && (status.translationMagnitude > moveThreshold || status.rotationAngle > rotThreshold)) {
                // Check if view angle is near perpendicular
                if (abs(status.viewAngle - 90.0) < 15.0) {
                    LOG(WARNING) << "Camera nearly perpendicular to board (angle: " << status.viewAngle
                                 << " deg), pausing movement for safety";
                } else {
                    updateRobotPosition(R_target2cam, tvec);
                }
            }
        }
    }

    return displayFrame;
}

bool TrackingManager::detectChArUcoBoard(const cv::Mat& frame, std::vector<int>& charucoIds,
                                      std::vector<cv::Point2f>& charucoCorners)
{
    // Detect ArUco markers
    std::vector<std::vector<cv::Point2f>> markerCorners;
    std::vector<int> markerIds;

    cv::aruco::detectMarkers(frame, dictionary, markerCorners, markerIds);

    if (markerIds.size() > 0) {
        // Interpolate ChArUco corners
        cv::aruco::interpolateCornersCharuco(markerCorners, markerIds, frame, board,
                                          charucoCorners, charucoIds, cameraMatrix, distCoeffs);

        // Require a minimum number of corners for reliable pose estimation
        return charucoIds.size() > 15;
    }

    return false;
}

bool TrackingManager::estimateBoardPose(const std::vector<cv::Point2f>& charucoCorners,
                                     const std::vector<int>& charucoIds,
                                     cv::Mat& rvec, cv::Mat& tvec)
{
    if (charucoCorners.empty() || charucoIds.empty()) {
        return false;
    }

    return cv::aruco::estimatePoseCharucoBoard(
        charucoCorners, charucoIds, board, cameraMatrix, distCoeffs, rvec, tvec);
}

void TrackingManager::updateRobotPosition(const cv::Mat& R_target2cam, const cv::Mat& t_target2cam)
{
    LOG(INFO) << "Updating robot position to follow target";

    // 1. Calculate camera-to-target transform
    rw::math::Transform3D<> T_camera_target = rtToTransform3D(R_target2cam, t_target2cam);

    // 2. Calculate gripper-to-camera transform
    rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);

    // 3. Get current robot pose
    rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();

    // 4. Calculate target pose in base coordinates
    rw::math::Transform3D<> T_base_target = T_base_gripper * T_gripper_camera * T_camera_target;

    // 5. Calculate initial target pose in base coordinates - 使用参考位姿
    rw::math::Transform3D<> T_camera_target_initial = rtToTransform3D(R_target2cam_initial, t_target2cam_initial);
    rw::math::Transform3D<> T_base_target_initial = referenceRobotPose * T_gripper_camera * T_camera_target_initial;

    // 6. Calculate relative movement
    rw::math::Transform3D<> T_target_movement = T_base_target * inverse(T_base_target_initial);

    // 7. Apply movement to reference robot pose
    rw::math::Transform3D<> newRobotPose = T_target_movement * referenceRobotPose;

    // Calculate position and rotation changes
    rw::math::Vector3D<> positionChange = newRobotPose.P() - referenceRobotPose.P();
    rw::math::RPY<> initialRPY(referenceRobotPose.R());
    rw::math::RPY<> newRPY(newRobotPose.R());
    rw::math::Vector3D<> rotationChange(
        newRPY[0] - initialRPY[0],
        newRPY[1] - initialRPY[1],
        newRPY[2] - initialRPY[2]
    );

    // Apply Kalman filtering
    cv::Point3f rawPosition(positionChange[0], positionChange[1], positionChange[2]);
    cv::Point3f rawRotation(rotationChange[0], rotationChange[1], rotationChange[2]);

    cv::Point3f filteredPosition = positionFilter.update(rawPosition);
    cv::Point3f filteredRotation = rotationFilter.update(rawRotation);

    // Construct filtered robot pose
    rw::math::Vector3D<> filteredPositionVector(
        referenceRobotPose.P()[0] + filteredPosition.x,
        referenceRobotPose.P()[1] + filteredPosition.y,
        referenceRobotPose.P()[2] + filteredPosition.z
    );

    rw::math::RPY<> filteredRPY(
        initialRPY[0] + filteredRotation.x,
        initialRPY[1] + filteredRotation.y,
        initialRPY[2] + filteredRotation.z
    );

    rw::math::Transform3D<> filteredRobotPose(
        filteredPositionVector,
        rw::math::Rotation3D<>(filteredRPY)
    );

    // Calculate IK and check safety constraints
    rw::math::Q currentJointQ = robot->getRobotJointQ();
    rw::math::Q newJointQ;

    if (robot->getIK(currentJointQ, filteredRobotPose, newJointQ)) {
        // Check safety thresholds
        rw::math::Q jointChange = newJointQ - currentJointQ;
        const double safetyThreshold = 30.0 * M_PI / 180.0; // 30 degrees in radians
        bool isSafeMovement = true;

        for (size_t i = 0; i < jointChange.size(); i++) {
            if (std::abs(jointChange[i]) > safetyThreshold) {
                LOG(WARNING) << "Joint " << i + 1 << " change too large: "
                             << jointChange[i] * 180.0 / M_PI << " deg, exceeding safety threshold "
                             << safetyThreshold * 180.0 / M_PI << " deg";
                isSafeMovement = false;
                break;
            }
        }

        if (isSafeMovement) {
            // Execute robot movement
            robot->movej(newJointQ, 0.2, 0, 0.2);

            // 更新参考位姿为过滤后的机器人位姿，用于下一步移动计算
            // 注意：templateRobotPose 和 initialRobotPose 不应该在这里更新
            referenceRobotPose = filteredRobotPose;
//            R_target2cam_initial = R_target2cam.clone();
//            t_target2cam_initial = t_target2cam.clone();
        } else {
            LOG(ERROR) << "Movement exceeds safety limits, robot movement aborted";
        }
    } else {
        LOG(ERROR) << "Inverse kinematics failed, cannot move robot";
    }
}

bool TrackingManager::saveTemplate(const std::string& name)
{
    if (!initialPoseSet) {
        LOG(ERROR) << "Cannot save template: initial pose not set";
        return false;
    }

    std::string filename = "tracking_template_" + name + ".yml";
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to create template file: " << filename;
        return false;
    }
    rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
    referenceRobotPose = currentRobotPose;

    fs << "template_name" << name;
    fs << "R_target2cam" << R_target2cam_initial;
    fs << "t_target2cam" << t_target2cam_initial;

    // 保存机器人位姿 - 使用当前参考位姿作为模板位姿
    cv::Mat R_robot = cv::Mat::eye(3, 3, CV_64F);
    cv::Mat t_robot = cv::Mat(3, 1, CV_64F);
    transform3DToRt(referenceRobotPose, R_robot, t_robot);

    fs << "R_robot" << R_robot;
    fs << "t_robot" << t_robot;

    fs.release();

    // Update in-memory template
    TrackingTemplate tmpl;
    tmpl.R_target2cam = R_target2cam_initial.clone();
    tmpl.t_target2cam = t_target2cam_initial.clone();
    tmpl.robotPose = referenceRobotPose;
    tmpl.isValid = true;
    templates[name] = tmpl;

    // 更新模板位姿
    templateRobotPose = referenceRobotPose;

    currentTemplate = name;

    LOG(INFO) << "Successfully saved tracking template: " << name;
    return true;
}

bool TrackingManager::loadTemplate(const std::string& name)
{
    // Check if template exists in memory
    if (templates.find(name) != templates.end() && templates[name].isValid) {
        LOG(INFO) << "Loading template from memory: " << name;
        currentTemplate = name;

        // Reset initial pose to force reinitialization with the template
        initialPoseSet = false;
        return true;
    }

    // Otherwise load from file
    std::string filename = "tracking_template_" + name + ".yml";
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open template file: " << filename;
        return false;
    }

    TrackingTemplate tmpl;
    fs["R_target2cam"] >> tmpl.R_target2cam;
    fs["t_target2cam"] >> tmpl.t_target2cam;

    cv::Mat R_robot, t_robot;
    fs["R_robot"] >> R_robot;
    fs["t_robot"] >> t_robot;

    // Convert to Transform3D
    tmpl.robotPose = rtToTransform3D(R_robot, t_robot);
    tmpl.isValid = true;

    // Save to memory
    templates[name] = tmpl;
    currentTemplate = name;

    // Reset initial pose to force reinitialization with the template
    initialPoseSet = false;

    fs.release();
    LOG(INFO) << "Successfully loaded tracking template: " << name;
    return true;
}

std::vector<std::string> TrackingManager::getAvailableTemplates()
{
    std::vector<std::string> result;

    // Add in-memory templates
    for (const auto& pair : templates) {
        if (pair.second.isValid) {
            result.push_back(pair.first);
        }
    }

    // Scan directory for template files
    WIN32_FIND_DATA findFileData;
    HANDLE hFind = FindFirstFile("tracking_template_*.yml", &findFileData);
    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            std::string filename = findFileData.cFileName;
            std::string templateName = filename.substr(18, filename.length() - 22); // Remove "tracking_template_" and ".yml"

            // Add if not already in the list
            if (std::find(result.begin(), result.end(), templateName) == result.end()) {
                // Try to load it to verify
                cv::FileStorage fs(filename, cv::FileStorage::READ);
                if (fs.isOpened()) {
                    result.push_back(templateName);
                    fs.release();
                }
            }
        } while (FindNextFile(hFind, &findFileData) != 0);
        FindClose(hFind);
    }

    return result;
}

std::string TrackingManager::getCurrentTemplate() const
{
    return currentTemplate;
}

void TrackingManager::setCurrentTemplate(const std::string& name)
{
    currentTemplate = name;
}

bool TrackingManager::toggleTracking()
{
    trackingEnabled = !trackingEnabled;

    if (trackingEnabled) {
        // Reset tracking state when enabling
        initialPoseSet = false;
        positionFilter.reset();
        rotationFilter.reset();
    }

    return trackingEnabled;
}

bool TrackingManager::isTracking() const
{
    return trackingEnabled;
}

void TrackingManager::setTracking(bool enabled)
{
    trackingEnabled = enabled;

    if (trackingEnabled) {
        // Reset tracking state when enabling
        initialPoseSet = false;
        positionFilter.reset();
        rotationFilter.reset();
    }
}

void TrackingManager::setMoveThreshold(double threshold)
{
    moveThreshold = threshold;
}

void TrackingManager::setRotThreshold(double threshold)
{
    rotThreshold = threshold;
}

double TrackingManager::getMoveThreshold() const
{
    return moveThreshold;
}

double TrackingManager::getRotThreshold() const
{
    return rotThreshold;
}

bool TrackingManager::loadCameraCalibration(const std::string& filename)
{
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Cannot open camera calibration file: " << filename;
        return false;
    }

    fs["camera_matrix"] >> cameraMatrix;
    fs["distortion_coefficients"] >> distCoeffs;
    fs.release();

    return !cameraMatrix.empty() && !distCoeffs.empty();
}

bool TrackingManager::loadHandEyeCalibration(const std::string& filename)
{
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Cannot open hand-eye calibration file: " << filename;
        return false;
    }

    fs["rotation_matrix"] >> R_cam2gripper;
    fs["translation_vector"] >> t_cam2gripper;
    fs.release();

    return !R_cam2gripper.empty() && !t_cam2gripper.empty();
}

rw::math::Transform3D<> TrackingManager::rtToTransform3D(const cv::Mat& R, const cv::Mat& t) const
{
    rw::math::Rotation3D<> rotation(
        R.at<double>(0,0), R.at<double>(0,1), R.at<double>(0,2),
        R.at<double>(1,0), R.at<double>(1,1), R.at<double>(1,2),
        R.at<double>(2,0), R.at<double>(2,1), R.at<double>(2,2)
    );

    rw::math::Vector3D<> position(
        t.at<double>(0,0),
        t.at<double>(1,0),
        t.at<double>(2,0)
    );

    return rw::math::Transform3D<>(position, rotation);
}

void TrackingManager::transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t)
{
    // Extract rotation matrix
    R = cv::Mat::eye(3, 3, CV_64F);
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            R.at<double>(i,j) = transform.R()(i,j);
        }
    }

    // Extract translation vector
    t = cv::Mat(3, 1, CV_64F);
    t.at<double>(0,0) = transform.P()[0];
    t.at<double>(1,0) = transform.P()[1];
    t.at<double>(2,0) = transform.P()[2];
}

// Robot control methods for path management
rw::math::Transform3D<> TrackingManager::getRobotPose() const
{
    if (robot) {
        return robot->getRobotActualTCP();
    }
    return rw::math::Transform3D<>();
}

rw::math::Q TrackingManager::getRobotJointConfig() const
{
    if (robot) {
        return robot->getRobotJointQ();
    }
    return rw::math::Q(6, 0.0);
}

bool TrackingManager::moveRobotJoint(const rw::math::Q& jointConfig, double speed, double acceleration)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    try {
        return robot->movej(jointConfig, speed, 0.0, acceleration);
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Error moving robot: " << e.what();
        return false;
    }
}

bool TrackingManager::moveRobotLinear(const rw::math::Transform3D<>& pose, double speed, double acceleration)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    try {
        // 获取当前关节配置作为逆运动学的种子
        rw::math::Q currentQ = robot->getRobotJointQ();
        LOG(INFO) << "Using joint movement instead of linear movement"<<currentQ;
        LOG(INFO) << "Using joint movement instead of linear movement"<<pose.P();
        // 计算到达目标位姿的关节配置
        rw::math::Q targetQ;
        if (robot->getIK(currentQ, pose, targetQ)) {
            // 使用关节移动代替直线移动
            LOG(INFO) << "Using joint movement instead of linear movement";
            return robot->movej(targetQ, speed, 0.0, acceleration);
        } else {
            LOG(ERROR) << "Failed to compute inverse kinematics for target pose";
            return false;
        }
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Error moving robot: " << e.what();
        return false;
    }
}

bool TrackingManager::setRobotIO(int port, bool state)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    try {
         robot->setOutValue(port, state);
        return true;
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Error setting robot IO: " << e.what();
        return false;
    }
}

bool TrackingManager::getIKForPose(const rw::math::Transform3D<>& pose, rw::math::Q& jointConfig)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    // Get current joint configuration as seed for IK
    rw::math::Q currentQ = robot->getRobotJointQ();

    // Calculate IK solution
    return robot->getIK(currentQ, pose, jointConfig);
}

rw::math::Transform3D<> TrackingManager::getTemplateDifference() const
{
    // 获取当前模板的数据
    auto it = templates.find(currentTemplate);
    if (it == templates.end() || !it->second.isValid) {
        // 如果没有找到有效的模板，返回单位变换
        return rw::math::Transform3D<>();
    }
    
    // 获取模板机器人位姿
    rw::math::Transform3D<> templateRobotPose = it->second.robotPose;
    
    // 获取当前机器人位姿
    rw::math::Transform3D<> currentRobotPose = getRobotPose();
    
    // 计算从模板位姿到当前位姿的变换
    // 这是关键计算：问题可能在于变换方向
    // 从模板到当前的变换：
    // T_template_to_current = currentRobotPose * inverse(templateRobotPose)
    
    // 或者，从当前到模板的变换：
    // T_current_to_template = templateRobotPose * inverse(currentRobotPose)
    
    // 查看实际实现
    rw::math::Transform3D<> difference = templateRobotPose * inverse(currentRobotPose);
    
    return difference;
}

rw::math::Transform3D<> TrackingManager::getTemplateRobotPose() const
{
    auto it = templates.find(currentTemplate);
    if (it != templates.end() && it->second.isValid) {
        return it->second.robotPose;
    }
    return rw::math::Transform3D<>();
}

rw::math::Transform3D<> TrackingManager::getCurrentBoardPose() const
{
    // 检查是否有有效的检测结果
    if (R_target2cam.empty() || t_target2cam.empty()) {
        LOG(WARNING) << "No valid board detection available";
        return rw::math::Transform3D<>();
    }

    // 将OpenCV的旋转矩阵和平移向量转换为Transform3D
    return rtToTransform3D(R_target2cam, t_target2cam);
}

rw::math::Transform3D<> TrackingManager::getTemplateBoardPose() const
{
    // 检查是否有有效的模板
    if (!initialPoseSet || R_target2cam_initial.empty() || t_target2cam_initial.empty()) {
        LOG(WARNING) << "No valid template board pose available";
        return rw::math::Transform3D<>();
    }

    // 将保存的模板旋转矩阵和平移向量转换为Transform3D
    return rtToTransform3D(R_target2cam_initial, t_target2cam_initial);
}

rw::math::Transform3D<> TrackingManager::getCameraToGripperTransform() const
{
    // 手眼标定得到的是相机相对于机器人手爪的位姿
    // 我们需要的是手爪相对于相机的位姿，所以需要取逆
    if (R_cam2gripper.empty() || t_cam2gripper.empty()) {
        LOG(WARNING) << "Hand-eye calibration data not available";
        return rw::math::Transform3D<>();
    }

    // 将OpenCV的旋转矩阵和平移向量转换为Transform3D
    return rtToTransform3D(R_cam2gripper, t_cam2gripper);
}

// 新增方法：获取相机内参矩阵
cv::Mat TrackingManager::getCameraMatrix() const
{
    return cameraMatrix.clone(); // 返回副本以防止外部修改
}

// 新增方法：获取相机畸变系数
cv::Mat TrackingManager::getDistCoeffs() const
{
    return distCoeffs.clone(); // 返回副本以防止外部修改
}

// 新增方法：获取手眼标定旋转矩阵
cv::Mat TrackingManager::getRotationMatrix() const
{
    return R_cam2gripper.clone(); // 返回副本以防止外部修改
}

// 新增方法：获取手眼标定平移向量
cv::Mat TrackingManager::getTranslationVector() const
{
    return t_cam2gripper.clone(); // 返回副本以防止外部修改
}

// 新增方法：移动机器人到模板记录的位置
bool TrackingManager::moveToTemplatePosition(const std::string& templateName)
{
    LOG(INFO) << "尝试移动机器人到模板位置: " << templateName;

    // 检查机器人连接
    if (!robot || !robot->isConnect()) {
        LOG(ERROR) << "机器人未连接，无法移动到模板位置";
        return false;
    }

    // 检查模板是否存在于内存中
    if (templates.find(templateName) != templates.end() && templates[templateName].isValid) {
        // 使用内存中的模板
        LOG(INFO) << "使用内存中的模板: " << templateName;

        // 验证机器人位姿的有效性 - 使用安全的方法
        const rw::math::Transform3D<>& pose = templates[templateName].robotPose;

        // 简单检查位置是否在合理范围内
        rw::math::Vector3D<> position = pose.P();
        if (std::isnan(position[0]) || std::isnan(position[1]) || std::isnan(position[2])) {
            LOG(ERROR) << "模板中的机器人位置包含NaN值";
            return false;
        }

        // 检查位置是否在合理范围内
        double posNorm = sqrt(position[0]*position[0] + position[1]*position[1] + position[2]*position[2]);
        if (posNorm > 10.0) { // 假设位置不应超过10米
            LOG(ERROR) << "模板中的机器人位置超出合理范围: " << posNorm << " 米";
            return false;
        }

        return moveRobotLinear(pose, 0.5, 0.1);
    }

    // 如果不在内存中，尝试从文件加载
    std::string filename = "tracking_template_" + templateName + ".yml";
    LOG(INFO) << "尝试从文件加载模板: " << filename;

    try {
        cv::FileStorage fs(filename, cv::FileStorage::READ);
        if (!fs.isOpened()) {
            LOG(ERROR) << "无法打开模板文件: " << filename;
            return false;
        }

        // 读取机器人位姿
        cv::Mat R_robot, t_robot;
        fs["R_robot"] >> R_robot;
        fs["t_robot"] >> t_robot;
        fs.release();

        // 检查读取的数据有效性
        if (R_robot.empty() || t_robot.empty()) {
            LOG(ERROR) << "模板文件中没有机器人位姿数据";
            return false;
        }

        // 检查矩阵维度
        if (R_robot.rows != 3 || R_robot.cols != 3 || t_robot.rows != 3 || t_robot.cols != 1) {
            LOG(ERROR) << "模板文件中的矩阵维度不正确 R: "
                       << R_robot.rows << "x" << R_robot.cols
                       << " t: " << t_robot.rows << "x" << t_robot.cols;
            return false;
        }

        // 确保矩阵类型正确
        if (R_robot.type() != CV_64F) {
            R_robot.convertTo(R_robot, CV_64F);
        }
        if (t_robot.type() != CV_64F) {
            t_robot.convertTo(t_robot, CV_64F);
        }

        // 检查矩阵中是否有NaN值
        for (int i = 0; i < R_robot.rows; i++) {
            for (int j = 0; j < R_robot.cols; j++) {
                if (std::isnan(R_robot.at<double>(i, j))) {
                    LOG(ERROR) << "旋转矩阵包含NaN值";
                    return false;
                }
            }
        }

        for (int i = 0; i < t_robot.rows; i++) {
            if (std::isnan(t_robot.at<double>(i, 0))) {
                LOG(ERROR) << "平移向量包含NaN值";
                return false;
            }
        }

        // 检查旋转矩阵的行列式是否接近1（正交矩阵的特性）
        double det = cv::determinant(R_robot);
        if (std::abs(det - 1.0) > 0.1) {
            LOG(ERROR) << "旋转矩阵的行列式不接近1，值为: " << det;
            return false;
        }

        // 转换为Transform3D
        rw::math::Transform3D<> targetPose;
        try {
            targetPose = rtToTransform3D(R_robot, t_robot);
        } catch (const std::exception& e) {
            LOG(ERROR) << "转换矩阵到Transform3D失败: " << e.what();
            return false;
        }

        // 验证平移向量在合理范围内
        rw::math::Vector3D<> position = targetPose.P();
        double posNorm = sqrt(position[0]*position[0] + position[1]*position[1] + position[2]*position[2]);
        if (posNorm > 10.0) { // 假设位置不应超过10米
            LOG(ERROR) << "计算得到的目标位置超出合理范围: " << posNorm << " 米";
            return false;
        }

        // 移动机器人到目标位姿
        LOG(INFO) << "移动机器人到模板位置: " << templateName;
        return moveRobotLinear(targetPose, 0.1, 0.1);
    } catch (const cv::Exception& e) {
        LOG(ERROR) << "OpenCV异常: " << e.what();
        return false;
    } catch (const std::exception& e) {
        LOG(ERROR) << "标准异常: " << e.what();
        return false;
    } catch (...) {
        LOG(ERROR) << "未知异常";
        return false;
    }
}

// 新增方法：重新加载标定参数
bool TrackingManager::reloadCalibration()
{
    // 重新加载相机标定参数
    bool cameraCalibLoaded = loadCameraCalibration("camera_calibration.yml");

    // 重新加载手眼标定参数
    bool handEyeCalibLoaded = loadHandEyeCalibration("hand_eye_calibration.yml");

    LOG(INFO) << "重新加载标定参数：相机标定" << (cameraCalibLoaded ? "成功" : "失败")
              << "，手眼标定" << (handEyeCalibLoaded ? "成功" : "失败");

    return cameraCalibLoaded && handEyeCalibLoaded;
}