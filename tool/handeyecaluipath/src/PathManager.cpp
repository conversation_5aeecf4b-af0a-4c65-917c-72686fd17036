#include "PathManager.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QMessageBox>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QHeaderView>
#include <QApplication>
#include <QProgressDialog>
#include <QLabel>
#include <QProgressBar>
#include <QThread>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>
#include <QDateTime>
#include <QDebug>
#include <opencv2/opencv.hpp>

PathManager::PathManager(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent)
        : QWidget(parent)
        , trackingManager(trackingManager)
        , currentSelectedPoint(-1)
{
    setupUi();
    initConnections();
}

PathManager::~PathManager()
{
    // 资源会在析构时自动释放
}

void PathManager::setupUi()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 创建相机视图区域
    QGroupBox *cameraGroup = new QGroupBox(u8"相机视图", this);
//    mainLayout->addWidget(cameraGroup);

    QVBoxLayout *cameraLayout = new QVBoxLayout(cameraGroup);

    cameraViewLabel = new QLabel(this);
    // 设置固定大小
    cameraViewLabel->setFixedSize(320, 240);
    cameraViewLabel->setAlignment(Qt::AlignCenter);
    cameraViewLabel->setText(u8"相机画面载入中...");
    cameraViewLabel->setStyleSheet("border: 1px solid #3F3F46; background-color: #252526;");
    cameraLayout->addWidget(cameraViewLabel, 0, Qt::AlignLeft);

    // 创建水平布局（左侧路径表，右侧控制）
    QHBoxLayout *contentLayout = new QHBoxLayout();
    mainLayout->addLayout(contentLayout);

    // 左侧路径点表格
    QVBoxLayout *leftLayout = new QVBoxLayout();
    contentLayout->addLayout(leftLayout, 7); // 70%的宽度


    QLabel *tableLabel = new QLabel("路径点列表:", this);
    leftLayout->addWidget(tableLabel);

    pathPointsTable = new QTableWidget(0, 5, this);
    pathPointsTable->setHorizontalHeaderLabels(QStringList() << "序号" << "类型" << "位置" << "IO" << "描述");
    pathPointsTable->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);
    pathPointsTable->setSelectionBehavior(QTableWidget::SelectRows);
    pathPointsTable->setSelectionMode(QTableWidget::SingleSelection);
    pathPointsTable->setEditTriggers(QTableWidget::NoEditTriggers);
    leftLayout->addWidget(pathPointsTable);
    leftLayout->addWidget(cameraGroup);


    // 右侧控制面板
    QVBoxLayout *rightLayout = new QVBoxLayout();
    contentLayout->addLayout(rightLayout, 3); // 30%的宽度

    // 路径名称输入
    QHBoxLayout *pathNameLayout = new QHBoxLayout();
    rightLayout->addLayout(pathNameLayout);

    QLabel *pathNameLabel = new QLabel("路径名称:", this);
    pathNameLayout->addWidget(pathNameLabel);

    pathNameEdit = new QLineEdit(this);
    pathNameLayout->addWidget(pathNameEdit);

    // 路径操作组
    QGroupBox *pathOperationsGroup = new QGroupBox("路径操作", this);
    rightLayout->addWidget(pathOperationsGroup);
    QVBoxLayout *pathOperationsLayout = new QVBoxLayout(pathOperationsGroup);

    // 路径文件操作
    savePathButton = new QPushButton("保存路径", this);
    pathOperationsLayout->addWidget(savePathButton);

    loadPathButton = new QPushButton("加载路径", this);
    pathOperationsLayout->addWidget(loadPathButton);

    // 路径执行操作
    executePathButton = new QPushButton("执行路径", this);
    executePathButton->setStyleSheet("background-color: #007700; color: white;");
    pathOperationsLayout->addWidget(executePathButton);

    QHBoxLayout *correctionLayout = new QHBoxLayout();
    pathOperationsLayout->addLayout(correctionLayout);

    correctPathButton = new QPushButton("修正路径", this);
    correctionLayout->addWidget(correctPathButton);

    resetPathButton = new QPushButton("重置路径", this);
    correctionLayout->addWidget(resetPathButton);

    // 路径点操作组
    QGroupBox *pointOperationsGroup = new QGroupBox("路径点操作", this);
    rightLayout->addWidget(pointOperationsGroup);
    QVBoxLayout *pointOperationsLayout = new QVBoxLayout(pointOperationsGroup);

    // 添加点操作
    QHBoxLayout *addPointsLayout = new QHBoxLayout();
    pointOperationsLayout->addLayout(addPointsLayout);

    addPointButton = new QPushButton("添加当前点", this);
    addPointsLayout->addWidget(addPointButton);

    addTemplatePointButton = new QPushButton("添加模板点", this);
    addPointsLayout->addWidget(addTemplatePointButton);

    // 编辑/删除点操作
    QHBoxLayout *editPointsLayout = new QHBoxLayout();
    pointOperationsLayout->addLayout(editPointsLayout);

    editPointButton = new QPushButton("编辑选中点", this);
    editPointsLayout->addWidget(editPointButton);

    removePointButton = new QPushButton("删除选中点", this);
    editPointsLayout->addWidget(removePointButton);

    // 上移/下移点操作
    QHBoxLayout *movePointsLayout = new QHBoxLayout();
    pointOperationsLayout->addLayout(movePointsLayout);

    moveUpButton = new QPushButton("上移", this);
    movePointsLayout->addWidget(moveUpButton);

    moveDownButton = new QPushButton("下移", this);
    movePointsLayout->addWidget(moveDownButton);

    // 移动到选中点和运行选中点
    QHBoxLayout *pointActionLayout = new QHBoxLayout();
    pointOperationsLayout->addLayout(pointActionLayout);

    moveToPointButton = new QPushButton("移动到选中点", this);
    moveToPointButton->setStyleSheet("background-color: #007ACC; color: white;");
    pointActionLayout->addWidget(moveToPointButton);

    runPointButton = new QPushButton("运行选中点", this);
    runPointButton->setStyleSheet("background-color: #007700; color: white;");
    pointActionLayout->addWidget(runPointButton);

    // 移动类型和IO设置组
    QGroupBox *moveSettingsGroup = new QGroupBox("点设置", this);
    rightLayout->addWidget(moveSettingsGroup);
    QVBoxLayout *moveSettingsLayout = new QVBoxLayout(moveSettingsGroup);

    // 移动类型
    QHBoxLayout *moveTypeLayout = new QHBoxLayout();
    moveSettingsLayout->addLayout(moveTypeLayout);

    jointMoveRadio = new QRadioButton("关节移动", this);
    jointMoveRadio->setChecked(true);
    moveTypeLayout->addWidget(jointMoveRadio);

    linearMoveRadio = new QRadioButton("线性移动", this);
    moveTypeLayout->addWidget(linearMoveRadio);

    // IO设置
    QGridLayout *ioSettingsLayout = new QGridLayout();
    moveSettingsLayout->addLayout(ioSettingsLayout);

    ioOutputCheckBox = new QCheckBox("设置IO输出", this);
    ioSettingsLayout->addWidget(ioOutputCheckBox, 0, 0, 1, 2);

    QLabel *ioPortLabel = new QLabel("IO端口:", this);
    ioSettingsLayout->addWidget(ioPortLabel, 1, 0);

    ioPortSpinBox = new QSpinBox(this);
    ioPortSpinBox->setRange(0, 15);
    ioPortSpinBox->setValue(0);
    ioPortSpinBox->setEnabled(false);
    ioSettingsLayout->addWidget(ioPortSpinBox, 1, 1);

    ioStateCheckBox = new QCheckBox("IO状态 (开/关)", this);
    ioStateCheckBox->setEnabled(false);
    ioSettingsLayout->addWidget(ioStateCheckBox, 2, 0, 1, 2);

    // 添加弹性空间
    rightLayout->addStretch(1);

    // 初始化按钮状态
    updatePathPointsTable();
}

void PathManager::initConnections()
{
    // 路径点选择连接
    connect(pathPointsTable, &QTableWidget::cellClicked, this, &PathManager::onPathPointSelected);

    // 路径点操作连接
    connect(addPointButton, &QPushButton::clicked, this, &PathManager::onAddPathPoint);
    connect(addTemplatePointButton, &QPushButton::clicked, this, &PathManager::onAddTemplatePoint);
    connect(editPointButton, &QPushButton::clicked, this, &PathManager::onEditPathPoint);
    connect(removePointButton, &QPushButton::clicked, this, &PathManager::onRemovePathPoint);
    connect(moveUpButton, &QPushButton::clicked, this, &PathManager::onMovePointUp);
    connect(moveDownButton, &QPushButton::clicked, this, &PathManager::onMovePointDown);
    connect(moveToPointButton, &QPushButton::clicked, this, &PathManager::onMoveToSelectedPoint);
    connect(runPointButton, &QPushButton::clicked, this, &PathManager::onRunSelectedPoint);

    // 路径操作连接
    connect(savePathButton, &QPushButton::clicked, this, &PathManager::onSavePath);
    connect(loadPathButton, &QPushButton::clicked, this, &PathManager::onLoadPath);
    connect(executePathButton, &QPushButton::clicked, this, &PathManager::onExecutePath);
    connect(correctPathButton, &QPushButton::clicked, this, &PathManager::onCorrectPath);
    connect(resetPathButton, &QPushButton::clicked, this, &PathManager::onResetToOriginalPath);

    // IO设置连接
    connect(ioOutputCheckBox, &QCheckBox::toggled, [this](bool checked) {
        ioPortSpinBox->setEnabled(checked);
        ioStateCheckBox->setEnabled(checked);
    });
}

void PathManager::updatePathPointsTable()
{
    // 保存当前选中的行
    int selectedRow = currentSelectedPoint;

    // 断开表格的信号连接，防止在更新过程中触发选择事件
    pathPointsTable->blockSignals(true);

    // 清空表格
    pathPointsTable->clearContents();
    pathPointsTable->setRowCount(0);

    // 填充表格数据
    for (size_t i = 0; i < pathPoints.size(); i++) {
        const PathPoint& point = pathPoints[i];

        int row = pathPointsTable->rowCount();
        pathPointsTable->insertRow(row);

        // 序号
        pathPointsTable->setItem(row, 0, new QTableWidgetItem(QString::number(i + 1)));

        // 移动类型
        pathPointsTable->setItem(row, 1, new QTableWidgetItem(point.isJointMove ? "关节" : "线性"));

        // 位置信息
        rw::math::Vector3D<> position = point.pose.P();
        rw::math::RPY<> rpy(point.pose.R());
        QString posStr = QString("X:%1 Y:%2 Z:%3 R:%4 P:%5 Y:%6")
                .arg(position[0], 0, 'f', 2)
                .arg(position[1], 0, 'f', 2)
                .arg(position[2], 0, 'f', 2)
                .arg(rpy[0] * 180/M_PI, 0, 'f', 1)
                .arg(rpy[1] * 180/M_PI, 0, 'f', 1)
                .arg(rpy[2] * 180/M_PI, 0, 'f', 1);
        pathPointsTable->setItem(row, 2, new QTableWidgetItem(posStr));

        // IO信息
        QString ioStr = point.ioOutput >= 0 ?
                        QString("端口:%1 状态:%2").arg(point.ioOutput).arg(point.ioState ? "开" : "关") :
                        "无";
        pathPointsTable->setItem(row, 3, new QTableWidgetItem(ioStr));

        // 描述（这里可以添加更多元数据）
        pathPointsTable->setItem(row, 4, new QTableWidgetItem(""));
    }

    // 恢复选择
    if (selectedRow >= 0 && selectedRow < pathPointsTable->rowCount()) {
        pathPointsTable->selectRow(selectedRow);
        currentSelectedPoint = selectedRow;
    } else {
        currentSelectedPoint = -1;
    }

    // 启用表格信号
    pathPointsTable->blockSignals(false);

    // 强制重绘表格
    pathPointsTable->viewport()->update();

    // 更新按钮状态
    bool hasPoints = !pathPoints.empty();
    bool hasSelectedPoint = (currentSelectedPoint >= 0);

    // 启用/禁用相关按钮
    editPointButton->setEnabled(hasSelectedPoint);
    removePointButton->setEnabled(hasSelectedPoint);
    moveUpButton->setEnabled(hasSelectedPoint && currentSelectedPoint > 0);
    moveDownButton->setEnabled(hasSelectedPoint && currentSelectedPoint < pathPointsTable->rowCount() - 1);
    executePathButton->setEnabled(hasPoints);
    moveToPointButton->setEnabled(hasSelectedPoint);
    runPointButton->setEnabled(hasSelectedPoint);
    correctPathButton->setEnabled(hasPoints && trackingManager->isInitialPoseSet());
    resetPathButton->setEnabled(!originalPathPoints.empty());
}

void PathManager::onAddPathPoint()
{
    // 获取当前机器人状态
    PathPoint newPoint = getCurrentRobotState();

    // 设置移动类型
    newPoint.isJointMove = jointMoveRadio->isChecked();

    // 设置IO输出（如果启用）
    if (ioOutputCheckBox->isChecked()) {
        newPoint.ioOutput = ioPortSpinBox->value();
        newPoint.ioState = ioStateCheckBox->isChecked();
    }

    // 添加到路径点列表
    pathPoints.push_back(newPoint);

    // 更新表格
    updatePathPointsTable();

    // 选中新添加的点
    int newRow = pathPointsTable->rowCount() - 1;
    pathPointsTable->selectRow(newRow);
    currentSelectedPoint = newRow;

    // 自动保存原始路径（如果是首次添加点）
    if (pathPoints.size() == 1) {
        originalPathPoints = pathPoints;
    }
}

void PathManager::onEditPathPoint()
{
    if (currentSelectedPoint < 0 || currentSelectedPoint >= static_cast<int>(pathPoints.size())) {
        QMessageBox::warning(this, "编辑失败", "请先选择一个路径点。");
        return;
    }

    // 获取当前选中的路径点
    PathPoint& point = pathPoints[currentSelectedPoint];

    // 更新移动类型
    point.isJointMove = jointMoveRadio->isChecked();

    // 更新IO输出
    if (ioOutputCheckBox->isChecked()) {
        point.ioOutput = ioPortSpinBox->value();
        point.ioState = ioStateCheckBox->isChecked();
    } else {
        point.ioOutput = -1;
    }

    // 更新表格
    updatePathPointsTable();
}

void PathManager::onRemovePathPoint()
{
    if (currentSelectedPoint < 0 || currentSelectedPoint >= static_cast<int>(pathPoints.size())) {
        QMessageBox::warning(this, "删除失败", "请先选择一个路径点。");
        return;
    }

    // 确认删除
    QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认删除", "确定要删除选中的路径点吗？",
            QMessageBox::Yes | QMessageBox::No);

    if (reply != QMessageBox::Yes) {
        return;
    }

    // 删除路径点
    pathPoints.erase(pathPoints.begin() + currentSelectedPoint);

    // 更新表格
    updatePathPointsTable();
}

void PathManager::onMovePointUp()
{
    if (currentSelectedPoint <= 0 || currentSelectedPoint >= static_cast<int>(pathPoints.size())) {
        return;
    }

    // 交换当前点和上一个点
    std::swap(pathPoints[currentSelectedPoint], pathPoints[currentSelectedPoint - 1]);

    // 更新选中行
    currentSelectedPoint--;

    // 更新表格
    updatePathPointsTable();
}

void PathManager::onMovePointDown()
{
    if (currentSelectedPoint < 0 || currentSelectedPoint >= static_cast<int>(pathPoints.size()) - 1) {
        return;
    }

    // 交换当前点和下一个点
    std::swap(pathPoints[currentSelectedPoint], pathPoints[currentSelectedPoint + 1]);

    // 更新选中行
    currentSelectedPoint++;

    // 更新表格
    updatePathPointsTable();
}

void PathManager::onAddTemplatePoint()
{
    // 获取模板位姿
    rw::math::Transform3D<> templatePose = trackingManager->getTemplateRobotPose();

    // 获取当前机器人关节配置
    rw::math::Q jointConfig = trackingManager->getRobotJointConfig();

    // 创建新的路径点
    PathPoint newPoint;
    newPoint.pose = templatePose;
    newPoint.jointConfig = jointConfig;
    newPoint.isJointMove = jointMoveRadio->isChecked();

    // 设置IO输出（如果启用）
    if (ioOutputCheckBox->isChecked()) {
        newPoint.ioOutput = ioPortSpinBox->value();
        newPoint.ioState = ioStateCheckBox->isChecked();
    }

    // 添加到路径点列表
    pathPoints.push_back(newPoint);

    // 更新表格
    updatePathPointsTable();

    // 选中新添加的点
    int newRow = pathPointsTable->rowCount() - 1;
    pathPointsTable->selectRow(newRow);
    currentSelectedPoint = newRow;

    // 自动保存原始路径（如果是首次添加点）
    if (pathPoints.size() == 1) {
        originalPathPoints = pathPoints;
    }
}

void PathManager::onSavePath()
{
    if (pathPoints.empty()) {
        QMessageBox::warning(this, "保存失败", "没有路径点可保存。");
        return;
    }

    // 获取路径名称
    QString pathName = pathNameEdit->text().trimmed();
    if (pathName.isEmpty()) {
        pathName = "path_" + QString::number(QDateTime::currentSecsSinceEpoch());
        pathNameEdit->setText(pathName);
    }

    // 选择保存路径
    QString filename = QFileDialog::getSaveFileName(
            this, "保存路径", pathName + ".json", "路径文件 (*.json)");

    if (filename.isEmpty()) {
        return;
    }

    // 保存路径文件
    if (savePathToFile(filename)) {
        QMessageBox::information(this, "保存成功", "路径已成功保存到文件。");

        // 更新文件名为保存的名称
        QFileInfo fileInfo(filename);
        pathNameEdit->setText(fileInfo.baseName());

        // 保存原始路径副本（用于路径重置）
        originalPathPoints = pathPoints;

        // 发出路径保存信号
        emit pathSaved();
    } else {
        QMessageBox::warning(this, "保存失败", "无法保存路径文件。");
    }
}

bool PathManager::savePathToFile(const QString& filename)
{
    // 创建JSON文档
    QJsonObject pathObject;

    // 保存基本信息
    pathObject["name"] = pathNameEdit->text();
    pathObject["points_count"] = static_cast<int>(pathPoints.size());
    pathObject["created_time"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    // 创建路径点数组
    QJsonArray pointsArray;

    for (const PathPoint& point : pathPoints) {
        QJsonObject pointObject;

        // 保存位姿
        QJsonObject poseObject;

        // 位置
        QJsonArray positionArray;
        positionArray.append(point.pose.P()[0]);
        positionArray.append(point.pose.P()[1]);
        positionArray.append(point.pose.P()[2]);
        poseObject["position"] = positionArray;

        // 旋转 (RPY)
        rw::math::RPY<> rpy(point.pose.R());
        QJsonArray rotationArray;
        rotationArray.append(rpy[0]);
        rotationArray.append(rpy[1]);
        rotationArray.append(rpy[2]);
        poseObject["rotation"] = rotationArray;

        pointObject["pose"] = poseObject;

        // 保存关节配置
        QJsonArray jointArray;
        for (size_t i = 0; i < point.jointConfig.size(); i++) {
            jointArray.append(point.jointConfig[i]);
        }
        pointObject["joint_config"] = jointArray;

        // 保存移动类型
        pointObject["joint_move"] = point.isJointMove;

        // 保存IO设置
        pointObject["io_output"] = point.ioOutput;
        pointObject["io_state"] = point.ioState;

        // 添加到数组
        pointsArray.append(pointObject);
    }

    pathObject["points"] = pointsArray;

    // 创建JSON文档
    QJsonDocument document(pathObject);

    // 写入文件
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    file.write(document.toJson());
    file.close();

    return true;
}

void PathManager::onLoadPath()
{
    // 选择加载文件
    QString filename = QFileDialog::getOpenFileName(
            this, "加载路径", "", "路径文件 (*.json)");

    if (filename.isEmpty()) {
        return;
    }

    // 如果当前有路径点，询问是否覆盖
    if (!pathPoints.empty()) {
        QMessageBox::StandardButton reply = QMessageBox::question(
                this, "确认加载", "加载新路径将覆盖当前路径点。是否继续？",
                QMessageBox::Yes | QMessageBox::No);

        if (reply != QMessageBox::Yes) {
            return;
        }
    }

    // 加载路径文件
    if (loadPathFromFile(filename)) {
        // 更新文件名
        QFileInfo fileInfo(filename);
        pathNameEdit->setText(fileInfo.baseName());

        // 更新表格
        updatePathPointsTable();

        // 保存原始路径副本（用于路径重置）
        originalPathPoints = pathPoints;

        QMessageBox::information(this, "加载成功",
                                 QString("已成功加载路径，共 %1 个路径点。").arg(pathPoints.size()));

        // 发出路径加载信号
        emit pathLoaded();
    } else {
        QMessageBox::warning(this, "加载失败", "无法加载路径文件或文件格式错误。");
    }
}

bool PathManager::loadPathFromFile(const QString& filename)
{
    // 读取文件
    QFile file(filename);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QByteArray data = file.readAll();
    file.close();

    // 解析JSON
    QJsonDocument document = QJsonDocument::fromJson(data);
    if (document.isNull() || !document.isObject()) {
        return false;
    }

    QJsonObject pathObject = document.object();

    // 读取基本信息
    if (pathObject.contains("name")) {
        pathNameEdit->setText(pathObject["name"].toString());
    }

    // 清空当前路径点
    pathPoints.clear();

    // 读取路径点
    if (pathObject.contains("points") && pathObject["points"].isArray()) {
        QJsonArray pointsArray = pathObject["points"].toArray();

        for (int i = 0; i < pointsArray.size(); i++) {
            QJsonObject pointObject = pointsArray[i].toObject();
            PathPoint point;

            // 读取位姿
            if (pointObject.contains("pose") && pointObject["pose"].isObject()) {
                QJsonObject poseObject = pointObject["pose"].toObject();

                // 读取位置
                if (poseObject.contains("position") && poseObject["position"].isArray()) {
                    QJsonArray positionArray = poseObject["position"].toArray();
                    if (positionArray.size() >= 3) {
                        rw::math::Vector3D<> position(
                                positionArray[0].toDouble(),
                                positionArray[1].toDouble(),
                                positionArray[2].toDouble()
                        );

                        // 读取旋转
                        rw::math::RPY<> rotation;
                        if (poseObject.contains("rotation") && poseObject["rotation"].isArray()) {
                            QJsonArray rotationArray = poseObject["rotation"].toArray();
                            if (rotationArray.size() >= 3) {
                                rotation = rw::math::RPY<>(
                                        rotationArray[0].toDouble(),
                                        rotationArray[1].toDouble(),
                                        rotationArray[2].toDouble()
                                );
                            }
                        }

                        // 设置位姿
                        point.pose = rw::math::Transform3D<>(position, rotation.toRotation3D());
                    }
                }
            }

            // 读取关节配置
            if (pointObject.contains("joint_config") && pointObject["joint_config"].isArray()) {
                QJsonArray jointArray = pointObject["joint_config"].toArray();
                rw::math::Q jointConfig(jointArray.size());

                for (int j = 0; j < jointArray.size(); j++) {
                    jointConfig[j] = jointArray[j].toDouble();
                }

                point.jointConfig = jointConfig;
            }

            // 读取移动类型
            if (pointObject.contains("joint_move")) {
                point.isJointMove = pointObject["joint_move"].toBool();
            }

            // 读取IO设置
            if (pointObject.contains("io_output")) {
                point.ioOutput = pointObject["io_output"].toInt();
            }

            if (pointObject.contains("io_state")) {
                point.ioState = pointObject["io_state"].toBool();
            }

            // 添加到路径点列表
            pathPoints.push_back(point);
        }
    }

    return !pathPoints.empty();
}

void PathManager::onExecutePath()
{
    if (pathPoints.empty()) {
        QMessageBox::warning(this, "执行失败", "没有路径点可执行。");
        return;
    }

    // 确认执行
    QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认执行", "确定要执行当前路径吗？机器人将移动到路径中的所有点。",
            QMessageBox::Yes | QMessageBox::No);

    if (reply != QMessageBox::Yes) {
        return;
    }

    // 发出路径执行开始信号
    emit pathExecutionStarted();

    // 创建进度对话框
    QProgressDialog progressDialog("执行路径...", "取消", 0, pathPoints.size(), this);
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setWindowTitle("路径执行");

    // 执行路径
    for (size_t i = 0; i < pathPoints.size(); i++) {
        // 更新进度
        progressDialog.setValue(i);
        QApplication::processEvents();

        // 检查是否取消
        if (progressDialog.wasCanceled()) {
            QMessageBox::warning(this, "执行取消", "路径执行已取消。");
            emit pathExecutionError("用户取消执行");
            return;
        }

        const PathPoint& point = pathPoints[i];

        // 发出机器人开始移动信号
        emit robotMovementStarted(point);

        // 设置IO（如果需要）
        if (point.ioOutput >= 0) {
            trackingManager->setRobotIO(point.ioOutput, point.ioState);
        }

        // 移动机器人
        bool moveSuccess = false;
        if (point.isJointMove) {
            // 关节移动
            moveSuccess = trackingManager->moveRobotJoint(point.jointConfig,0.5);
        } else {
            // 线性移动
            moveSuccess = trackingManager->moveRobotLinear(point.pose);
        }

        // 发出机器人移动完成信号
        emit robotMovementFinished();

        if (!moveSuccess) {
            QMessageBox::warning(this, "执行错误",
                                 QString("移动到路径点 %1 失败，路径执行中止。").arg(i + 1));
            progressDialog.close();
            emit pathExecutionError(QString("移动到路径点 %1 失败").arg(i + 1));
            return;
        }
    }

    // 完成
    progressDialog.setValue(pathPoints.size());
    QMessageBox::information(this, "执行完成", "路径已成功执行。");

    // 发出路径执行完成信号
    emit pathExecutionFinished();
}

void PathManager::onCorrectPath()
{
    if (pathPoints.empty()) {
        QMessageBox::warning(this, u8"路径修正", u8"没有可修正的路径点。");
        return;
    }

    // 保存原始路径用于恢复
    originalPathPoints = pathPoints;

    // 确认是否要修正路径
    QMessageBox::StandardButton reply;
    reply = QMessageBox::question(this, u8"路径修正确认",
                                  u8"是否要根据当前模板位置修正路径？这将移动机器人并进行追踪。",
                                  QMessageBox::Yes | QMessageBox::No);
    if (reply != QMessageBox::Yes)
        return;

    // 显示进度对话框
    QDialog progressDialog(this);
    progressDialog.setWindowTitle(u8"路径修正进行中");
    progressDialog.setWindowModality(Qt::WindowModal);
    progressDialog.setMinimumWidth(400);

    QVBoxLayout *layout = new QVBoxLayout(&progressDialog);
    QLabel *statusLabel = new QLabel(u8"正在进行路径修正...", &progressDialog);
    layout->addWidget(statusLabel);

    QProgressBar *progressBar = new QProgressBar(&progressDialog);
    progressBar->setMinimum(0);
    progressBar->setMaximum(0); // 不确定进度
    layout->addWidget(progressBar);

    QLabel *detailLabel = new QLabel(u8"步骤 1/4: 移动到模板位置", &progressDialog);
    layout->addWidget(detailLabel);

    QPushButton *cancelButton = new QPushButton(u8"取消", &progressDialog);
    layout->addWidget(cancelButton);

    connect(cancelButton, &QPushButton::clicked, &progressDialog, &QDialog::reject);

    // 使用非模态方式显示对话框，这样我们可以更新其状态
    progressDialog.setModal(false);
    progressDialog.show();
    QApplication::processEvents();

    bool correctionSuccessful = false;

    try {
        // 步骤 1: 移动到模板位置
        detailLabel->setText(u8"步骤 1/4: 移动到模板位置");
        QApplication::processEvents();

        if (!trackingManager->moveToTemplatePosition(trackingManager->getCurrentTemplate())) {
            throw std::runtime_error(u8"无法移动到模板位置");
        }

        // 给机器人一些时间稳定下来
        QThread::msleep(1000);

        // 步骤 2: 开始跟踪
        detailLabel->setText(u8"步骤 2/4: 启动跟踪系统");
        QApplication::processEvents();

        // 启动跟踪
        trackingManager->setTracking(true);

        // 步骤 3: 等待跟踪稳定
        detailLabel->setText(u8"步骤 3/4: 等待跟踪稳定");
        progressBar->setMaximum(100);

        // 定义稳定性条件
        const int requiredStableFrames = 5;      // 需要连续稳定的帧数
        const double maxAcceptableMovement = 0.005; // 最大可接受的移动量(米)
        const double maxAcceptableRotation = 0.0068; // 最大可接受的旋转量(弧度)

        int stableFrameCount = 0;
        rw::math::Transform3D<> lastPose;
        bool isFirst = true;

        TrackingStatus status;

        // 等待跟踪稳定
        while (stableFrameCount < requiredStableFrames) {
            if (!progressDialog.isVisible()) {
                // 用户取消了操作
                trackingManager->setTracking(false);
                throw std::runtime_error(u8"用户取消了操作");
            }

            // 处理一帧跟踪
            trackingManager->processFrame(status);

            if (!status.markersDetected) {
                detailLabel->setText(u8"等待检测到标记...");
                stableFrameCount = 0;
                progressBar->setValue(0);
                QApplication::processEvents();
                QThread::msleep(100);
                continue;
            }

            rw::math::Transform3D<> currentPose = trackingManager->getRobotPose();

            if (isFirst) {
                lastPose = currentPose;
                isFirst = false;
                continue;
            }

            // 计算位移和旋转差异
            rw::math::Transform3D<> diff = inverse(lastPose) * currentPose;
            rw::math::Vector3D<> translation = diff.P();
            rw::math::RPY<> rotation(diff.R());

            double translationMagnitude = translation.norm2();

            // 修复 RPY 类的访问方法
            // 使用数组索引访问 roll, pitch, yaw 值
            double rotationMagnitude = std::sqrt(
                    rotation[0] * rotation[0] +
                    rotation[1] * rotation[1] +
                    rotation[2] * rotation[2]);

            if (translationMagnitude < maxAcceptableMovement &&
                rotationMagnitude < maxAcceptableRotation) {
                stableFrameCount++;
                progressBar->setValue((stableFrameCount * 100) / requiredStableFrames);
                detailLabel->setText(QString(u8"步骤 3/4: 跟踪稳定中 (%1/%2)...")
                                             .arg(stableFrameCount).arg(requiredStableFrames));
            } else {
                stableFrameCount = 0;
                progressBar->setValue(0);
                detailLabel->setText(u8"等待跟踪稳定...");
            }

            lastPose = currentPose;
            QApplication::processEvents();
            QThread::msleep(50);  // 降低CPU使用率
        }

        // 步骤 4: 计算变换矩阵并修正路径
        detailLabel->setText(u8"步骤 4/4: 计算变换矩阵并修正路径");
        progressBar->setValue(0);
        QApplication::processEvents();

        // 获取最终的机器人位姿（稳定后的追踪位姿）
        rw::math::Transform3D<> finalTrackingPose = trackingManager->getRobotPose();

        // 获取模板位姿
        rw::math::Transform3D<> templatePose = trackingManager->getTemplateRobotPose();

        // 打印模板位姿和跟踪位姿的信息
        qDebug() << "========= 路径修正调试信息 =========";
        qDebug() << "模板位姿位置:";
        qDebug() << "  X:" << templatePose.P()[0] << " Y:" << templatePose.P()[1] << " Z:" << templatePose.P()[2];
        rw::math::RPY<> templateRPY(templatePose.R());
        qDebug() << "  Roll:" << templateRPY[0] << " Pitch:" << templateRPY[1] << " Yaw:" << templateRPY[2];

        qDebug() << "最终跟踪位姿位置:";
        qDebug() << "  X:" << finalTrackingPose.P()[0] << " Y:" << finalTrackingPose.P()[1] << " Z:" << finalTrackingPose.P()[2];
        rw::math::RPY<> trackingRPY(finalTrackingPose.R());
        qDebug() << "  Roll:" << trackingRPY[0] << " Pitch:" << trackingRPY[1] << " Yaw:" << trackingRPY[2];

        // 计算变换矩阵: finalTrackingPose = templatePose * correctionTransform
        // 因此 correctionTransform = templatePose^(-1) * finalTrackingPose
        rw::math::Transform3D<> correctionTransform = inverse(templatePose) * finalTrackingPose;

        // 打印修正变换矩阵信息
        qDebug() << "修正变换矩阵:";
        qDebug() << "  位移: X:" << correctionTransform.P()[0] << " Y:" << correctionTransform.P()[1] << " Z:" << correctionTransform.P()[2];
        rw::math::RPY<> correctionRPY(correctionTransform.R());
        qDebug() << "  旋转: Roll:" << correctionRPY[0] << " Pitch:" << correctionRPY[1] << " Yaw:" << correctionRPY[2];

        // 关闭跟踪
        trackingManager->setTracking(false);

        // 修正路径中的每个点
        int pointCount = pathPoints.size();
        progressBar->setMaximum(pointCount);

        for (int i = 0; i < pointCount; i++) {

                // 只修正线性移动点的位姿（关节移动点保持不变）
                rw::math::Transform3D<> oldPose = pathPoints[i].pose;

                // 在修正前打印路径点信息
                if (i == 0 || i == pointCount - 1) {  // 只打印第一个和最后一个点，避免输出太多
                    qDebug() << "修正前路径点 #" << (i+1) << ":";
                    qDebug() << "  X:" << oldPose.P()[0] << " Y:" << oldPose.P()[1] << " Z:" << oldPose.P()[2];
                    rw::math::RPY<> oldRPY(oldPose.R());
                    qDebug() << "  Roll:" << oldRPY[0] << " Pitch:" << oldRPY[1] << " Yaw:" << oldRPY[2];
                }

                // 修正路径点：尝试两种变换顺序，看哪种有效
                // 方法1：右乘变换矩阵 (原始实现)
                pathPoints[i].pose = oldPose * correctionTransform;

                // 打印修正后的路径点信息
                if (i == 0 || i == pointCount - 1) {
                    rw::math::Transform3D<> newPose = pathPoints[i].pose;
                    qDebug() << "修正后路径点 #" << (i+1) << " (右乘变换):";
                    qDebug() << "  X:" << newPose.P()[0] << " Y:" << newPose.P()[1] << " Z:" << newPose.P()[2];
                    rw::math::RPY<> newRPY(newPose.R());
                    qDebug() << "  Roll:" << newRPY[0] << " Pitch:" << newRPY[1] << " Yaw:" << newRPY[2];

                    // 计算变化量
                    rw::math::Vector3D<> posDiff = newPose.P() - oldPose.P();
                    qDebug() << "  位置变化: X:" << posDiff[0] << " Y:" << posDiff[1] << " Z:" << posDiff[2];

                    // 另一种变换方式：左乘变换矩阵
                    rw::math::Transform3D<> altPose = correctionTransform * oldPose;
                    qDebug() << "另一种变换方式 (左乘变换):";
                    qDebug() << "  X:" << altPose.P()[0] << " Y:" << altPose.P()[1] << " Z:" << altPose.P()[2];
                    rw::math::RPY<> altRPY(altPose.R());
                    qDebug() << "  Roll:" << altRPY[0] << " Pitch:" << altRPY[1] << " Yaw:" << altRPY[2];
                }

                // 更新关节配置以匹配新的位姿
                if (!trackingManager->getIKForPose(pathPoints[i].pose, pathPoints[i].jointConfig)) {
                    QMessageBox::warning(this, u8"路径修正警告",
                                         QString(u8"无法为路径点 %1 找到有效的逆运动学解。"
                                                 u8"此点可能无法到达。").arg(i + 1));
                }


            progressBar->setValue(i + 1);
            QApplication::processEvents();
        }

        qDebug() << "========= 路径修正完成 =========";

        correctionSuccessful = true;
    }
    catch (const std::exception& e) {
        // 确保跟踪被关闭
        trackingManager->setTracking(false);

        QMessageBox::critical(this, u8"路径修正失败",
                              QString(u8"修正路径时发生错误: %1").arg(e.what()));
    }

    // 关闭进度对话框
    progressDialog.close();

    // 更新路径点表格
    updatePathPointsTable();

    if (correctionSuccessful) {
        QMessageBox::information(this, u8"路径修正完成", u8"路径已成功根据当前模板位置进行修正。");
        emit pathCorrected();
    }
}

void PathManager::onResetToOriginalPath()
{
    if (originalPathPoints.empty()) {
        QMessageBox::warning(this, "重置失败", "没有原始路径数据可供重置。");
        return;
    }

    // 确认重置
    QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认重置", "确定要将路径重置到原始状态吗？所有修正将被撤销。",
            QMessageBox::Yes | QMessageBox::No);

    if (reply != QMessageBox::Yes) {
        return;
    }

    // 恢复原始路径
    pathPoints = originalPathPoints;

    // 更新表格
    updatePathPointsTable();

    QMessageBox::information(this, "重置完成", "路径已重置到原始状态。");
}

void PathManager::onPathPointSelected(int row, int column)
{
    if (row < 0 || row >= static_cast<int>(pathPoints.size())) {
        currentSelectedPoint = -1;
        return;
    }

    // 更新当前选中点
    currentSelectedPoint = row;

    // 获取选中的路径点
    const PathPoint& point = pathPoints[row];

    // 更新UI控件为选中点的值
    jointMoveRadio->setChecked(point.isJointMove);
    linearMoveRadio->setChecked(!point.isJointMove);

    ioOutputCheckBox->setChecked(point.ioOutput >= 0);
    if (point.ioOutput >= 0) {
        ioPortSpinBox->setValue(point.ioOutput);
        ioStateCheckBox->setChecked(point.ioState);
    }

    // 更新按钮状态
    updatePathPointsTable();

    // 发出路径点选择信号
    emit pathPointSelected(row);
}

void PathManager::onMoveToSelectedPoint()
{
    if (currentSelectedPoint < 0 || currentSelectedPoint >= static_cast<int>(pathPoints.size())) {
        QMessageBox::warning(this, "移动失败", "请先选择一个路径点。");
        return;
    }

    // 确认移动
    QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认移动", "确定要移动到选中的路径点吗？",
            QMessageBox::Yes | QMessageBox::No);

    if (reply != QMessageBox::Yes) {
        return;
    }

    // 获取选中的路径点
    const PathPoint& point = pathPoints[currentSelectedPoint];

    // 发出机器人开始移动信号
    emit robotMovementStarted(point);

    // 移动机器人
    bool moveSuccess = false;
    if (point.isJointMove) {
        // 关节移动
        moveSuccess = trackingManager->moveRobotJoint(point.jointConfig);
    } else {
        // 线性移动
        moveSuccess = trackingManager->moveRobotLinear(point.pose);
    }

    // 发出机器人移动完成信号
    emit robotMovementFinished();

    if (moveSuccess) {
        QMessageBox::information(this, "移动完成", "机器人已移动到选中的路径点。");
    } else {
        QMessageBox::warning(this, "移动失败", "无法移动到选中的路径点，请检查机器人状态。");
    }
}

PathPoint PathManager::getCurrentRobotState()
{
    PathPoint point;

    // 获取当前机器人位姿
    point.pose = trackingManager->getRobotPose();

    // 获取当前关节配置
    point.jointConfig = trackingManager->getRobotJointConfig();

    return point;
}

void PathManager::onRunSelectedPoint()
{
    if (currentSelectedPoint < 0 || currentSelectedPoint >= static_cast<int>(pathPoints.size())) {
        QMessageBox::warning(this, "运行失败", "请先选择一个路径点。");
        return;
    }

    // 确认运行
    QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认运行", "确定要运行选中的路径点吗？",
            QMessageBox::Yes | QMessageBox::No);

    if (reply != QMessageBox::Yes) {
        return;
    }

    // 获取选中的路径点
    const PathPoint& point = pathPoints[currentSelectedPoint];

    // 发出机器人开始移动信号
    emit robotMovementStarted(point);

    // 设置IO（如果需要）
    if (point.ioOutput >= 0) {
        trackingManager->setRobotIO(point.ioOutput, point.ioState);
    }

    // 移动机器人
    bool moveSuccess = false;
    if (point.isJointMove) {
        // 关节移动
        moveSuccess = trackingManager->moveRobotJoint(point.jointConfig);
    } else {
        // 线性移动
        moveSuccess = trackingManager->moveRobotLinear(point.pose);
    }

    // 发出机器人移动完成信号
    emit robotMovementFinished();

    if (moveSuccess) {
        QMessageBox::information(this, "运行完成", "已成功运行选中的路径点。");
    } else {
        QMessageBox::warning(this, "运行失败", "无法运行选中的路径点，请检查机器人状态。");
    }
}

// 实现新添加的公共方法
void PathManager::correctPath()
{
    // 调用私有方法进行路径修正
    onCorrectPath();

    // 额外添加一个确认，确保表格被更新
    updatePathPointsTable();
}

void PathManager::updateCameraFrame()
{
    // 获取跟踪状态
    TrackingStatus status;

    // 处理当前帧并获取图像
    cv::Mat frame = trackingManager->processFrame(status);

    if (!frame.empty()) {
        // 显示带有追踪信息的图像
        displayImage(frame);
    }
}

void PathManager::displayImage(const cv::Mat& image)
{
    if (image.empty()) {
        return;
    }

    // 转换为RGB格式
    cv::Mat rgbImage;
    if (image.channels() == 1) {
        cv::cvtColor(image, rgbImage, cv::COLOR_GRAY2RGB);
    } else if (image.channels() == 3) {
        cv::cvtColor(image, rgbImage, cv::COLOR_BGR2RGB);
    } else if (image.channels() == 4) {
        cv::cvtColor(image, rgbImage, cv::COLOR_BGRA2RGB);
    } else {
        return;
    }

    // 使用固定大小
    QSize viewSize = cameraViewLabel->size();
    double imgRatio = (double)rgbImage.cols / rgbImage.rows;
    double viewRatio = (double)viewSize.width() / viewSize.height();

    // 计算保持纵横比的尺寸
    int newWidth, newHeight;
    if (imgRatio > viewRatio) {
        // 图像横向较宽，以宽度为基准
        newWidth = viewSize.width();
        newHeight = newWidth / imgRatio;
    } else {
        // 图像纵向较高，以高度为基准
        newHeight = viewSize.height();
        newWidth = newHeight * imgRatio;
    }

    // 调整图像大小，保持纵横比
    cv::Mat resizedImage;
    cv::resize(rgbImage, resizedImage, cv::Size(newWidth, newHeight), 0, 0, cv::INTER_AREA);

    // 创建与标签大小相同的背景图像（黑色）
    cv::Mat backgroundImage = cv::Mat::zeros(viewSize.height(), viewSize.width(), resizedImage.type());

    // 计算居中位置
    int offsetX = (viewSize.width() - newWidth) / 2;
    int offsetY = (viewSize.height() - newHeight) / 2;

    // 将调整大小的图像复制到背景中央
    cv::Rect roi(offsetX, offsetY, newWidth, newHeight);
    resizedImage.copyTo(backgroundImage(roi));

    // 将OpenCV图像转换为QImage
    QImage qImage(backgroundImage.data, backgroundImage.cols, backgroundImage.rows,
                  static_cast<int>(backgroundImage.step), QImage::Format_RGB888);

    // 显示图像
    cameraViewLabel->setPixmap(QPixmap::fromImage(qImage));
} 