#ifndef PATHMANAGER_H
#define PATHMANAGER_H

#include <QWidget>
#include <QTableWidget>
#include <QPushButton>
#include <QRadioButton>
#include <QCheckBox>
#include <QSpinBox>
#include <QLineEdit>
#include <memory>
#include <vector>

#include "TrackingManager.h"
#include <rw/math/Transform3D.hpp>
#include <rw/math/Q.hpp>
#include <QLabel>

// Define path point structure
struct PathPoint {
    rw::math::Transform3D<> pose;
    rw::math::Q jointConfig;
    bool isJointMove; // true: joint move, false: linear move
    int ioOutput; // -1: no IO, 0+: IO port number to set
    bool ioState; // IO state to set (true: on, false: off)

    PathPoint() : isJointMove(true), ioOutput(-1), ioState(false) {}
};

class PathManager : public QWidget
{
    Q_OBJECT
    
public:
    explicit PathManager(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent = nullptr);
    ~PathManager();

    // 添加新的公共方法
    bool hasPathPoints() const { return !pathPoints.empty(); }
    void correctPath(bool showMessages = true); // 更新公共接口，添加showMessages参数



signals:
    // 添加信号声明
    void pathExecutionStarted();                            // 路径执行开始
    void pathExecutionFinished();                           // 路径执行完成
    void pathExecutionError(const QString& errorMessage);   // 路径执行错误
    void pathLoaded();                                      // 加载了新路径
    void pathSaved();                                       // 保存了路径
    void pathCorrected();                                   // 路径被修正
    void pathPointSelected(int index);                      // 选择了路径点
    void robotMovementStarted(const PathPoint& point);      // 机器人开始移动
    void robotMovementFinished();                           // 机器人移动完成
    
private slots:
    void updateCameraFrame();
    void onAddPathPoint();
    void onEditPathPoint();
    void onRemovePathPoint();
    void onMovePointUp();
    void onMovePointDown();
    void onAddTemplatePoint();
    void onSavePath();
    void onLoadPath();
    void onExecutePath();
    void onCorrectPath();
    void onResetToOriginalPath();
    void onPathPointSelected(int row, int column);
    void onMoveToSelectedPoint();
    void onRunSelectedPoint();
    void onAutoProcess(); // 添加自动流程槽函数
    
private:
    std::shared_ptr<TrackingManager> trackingManager;
    
    // UI components
    QTableWidget *pathPointsTable;
    QPushButton *addPointButton;
    QPushButton *editPointButton;
    QPushButton *removePointButton;
    QPushButton *moveUpButton;
    QPushButton *moveDownButton;
    QPushButton *addTemplatePointButton;
    QPushButton *savePathButton;
    QPushButton *loadPathButton;
    QPushButton *executePathButton;
    QPushButton *correctPathButton;
    QPushButton *resetPathButton;
    QPushButton *moveToPointButton;
    QPushButton *runPointButton;
    QPushButton *autoProcessButton; // 添加自动流程按钮
    QRadioButton *jointMoveRadio;
    QRadioButton *linearMoveRadio;
    QCheckBox *ioOutputCheckBox;
    QSpinBox *ioPortSpinBox;
    QCheckBox *ioStateCheckBox;
    QLineEdit *pathNameEdit;
    QLabel *cameraViewLabel;
    
    // Path data
    std::vector<PathPoint> pathPoints;
    std::vector<PathPoint> originalPathPoints; // 保存原始路径点数据
    int currentSelectedPoint;
    bool isAutoProcessing; // 标记是否正在执行自动流程
    
    void setupUi();
    void initConnections();
    void updatePathPointsTable();
    bool savePathToFile(const QString& filename);
    bool loadPathFromFile(const QString& filename);
    PathPoint getCurrentRobotState();
    void displayImage(const cv::Mat &image);
};

#endif // PATHMANAGER_H 