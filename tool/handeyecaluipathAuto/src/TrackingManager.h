#ifndef TRACKINGMANAGER_H
#define TRACKINGMANAGER_H

#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include "HikVisionCamera.h"
#include "AuboArcsDriver.h"
#include "rw/math.hpp"
#include "KalmanFilter3D.h"
#include <deque>

// Structure to hold tracking template data
struct TrackingTemplate {
    cv::Mat R_target2cam;
    cv::Mat t_target2cam;
    rw::math::Transform3D<> robotPose;
    bool isValid;

    TrackingTemplate() : isValid(false) {}
};

// Structure to hold tracking status information
struct TrackingStatus {
    bool trackingEnabled = false;
    bool markersDetected = false;
    double translationMagnitude = 0.0;
    double rotationAngle = 0.0;
    double viewAngle = 0.0;
    std::string currentTemplate = "default";
    bool templateAvailable = false;
    bool initialPoseSet = false;
};

class TrackingManager {
public:
    TrackingManager();
    ~TrackingManager();

    // Initialize components
    bool initialize();
    cv::Mat getprocessFrame();

    // 新增方法：只获取相机图像，更新全局变量frame
    bool captureFrame();

    // 新增方法：只获取相机预览帧，不进行跟踪处理
    cv::Mat getPreviewFrame();

    // Process a new frame and update tracking
    cv::Mat processFrame(TrackingStatus& status);

    // Template management
    bool saveTemplate(const std::string& name);
    bool loadTemplate(const std::string& name);
    std::vector<std::string> getAvailableTemplates();
    std::string getCurrentTemplate() const;
    void setCurrentTemplate(const std::string& name);

    // Tracking control
    bool toggleTracking();
    bool isTracking() const;
    void setTracking(bool enabled);


    // 新增方法：直接获取当前位姿数据进行初始化，用于创建模板
    bool initializeCurrentPose();

    // Threshold settings
    void setMoveThreshold(double threshold);
    void setRotThreshold(double threshold);
    double getMoveThreshold() const;
    double getRotThreshold() const;

    // Robot control methods for path management
    rw::math::Transform3D<> getRobotPose() const;
    rw::math::Q getRobotJointConfig() const;
    bool moveRobotJoint(const rw::math::Q& jointConfig, double speed = 0.8, double acceleration = 0.2);
    bool moveRobotLinear(const rw::math::Transform3D<>& pose, double speed = 0.8, double acceleration = 0.1);
    bool setRobotIO(int port, bool state);

    // Inverse kinematics
    bool getIKForPose(const rw::math::Transform3D<>& pose, rw::math::Q& jointConfig);

    // Path correction functionality
    rw::math::Transform3D<> getTemplateDifference() const;
    rw::math::Transform3D<> getTemplateRobotPose() const;

    // 新增方法：移动机器人到模板记录的位置
    bool moveToTemplatePosition(const std::string& templateName);

    // 新增方法：获取当前标定板在相机坐标系下的位姿
    rw::math::Transform3D<> getCurrentBoardPose() const;

    // 新增方法：获取模板中标定板在相机坐标系下的位姿
    rw::math::Transform3D<> getTemplateBoardPose() const;

    // 新增方法：获取相机到机器人手爪的变换
    rw::math::Transform3D<> getCameraToGripperTransform() const;

    bool isInitialPoseSet() const { return initialPoseSet; }

    // 新增方法：获取相机内参矩阵
    cv::Mat getCameraMatrix() const;

    // 新增方法：获取相机畸变系数
    cv::Mat getDistCoeffs() const;

    // 新增方法：获取手眼标定旋转矩阵
    cv::Mat getRotationMatrix() const;

    // 新增方法：获取手眼标定平移向量
    cv::Mat getTranslationVector() const;

    // 新增方法：获取机器人是否正在运动
    bool isRobotInMotion() const { return isRobotMoving; }

    // 新增方法：获取当前帧的时间戳
    double getFrameTimestamp() const { return frameTimestamp; }

    // 新增方法：重新加载标定参数
    bool reloadCalibration();

    // 新增方法：配置ArUco参数
    bool configureArUcoBoard(int markersX, int markersY, float markerLength, float markerSeparation);

    // 新增方法：预热Kalman滤波器，使其更快收敛
    void warmupKalmanFilter();

    // 新增方法：执行固定次数的跟踪
    bool executeTracking(int iterations, double errorThreshold = 0.001);

    // 获取当前跟踪误差
    double getCurrentTrackingError() const;

    // 通过给定次数和错误阈值执行跟踪过程
    bool executeTrackingUntilConverged(int maxIterations, double errorThreshold);

    // 新增：设置和获取旋转调整控制
    void setEnableRotationAdjustment(bool enable);
    bool isRotationAdjustmentEnabled() const;

    // 新增：设置和获取旋转方向反转控制
    void setInvertRotationDirection(bool invert);
    bool isRotationDirectionInverted() const;

private:
    // Components
    std::shared_ptr<AuboArcsDriver> robot;
    std::shared_ptr<HikVisionCamera> camera;

    // Calibration parameters
    cv::Mat cameraMatrix, distCoeffs;
    cv::Mat R_cam2gripper, t_cam2gripper;

    // ArUco detection
    cv::Ptr<cv::aruco::Dictionary> dictionary;
    cv::Ptr<cv::aruco::GridBoard> board;
    cv::Ptr<cv::aruco::DetectorParameters> parameters;

    // Tracking state
    bool trackingEnabled;
    double moveThreshold;
    double rotThreshold;
    bool enableRotationAdjustment;  // 控制是否应用旋转调整
    bool invertRotationDirection;   // 控制旋转方向是否反转
    std::string currentTemplate;
    cv::Mat frame;

    // 图像同步相关
    bool isRobotMoving;          // 机器人是否正在运动
    double frameTimestamp;       // 当前帧的时间戳

    // Templates storage
    std::map<std::string, TrackingTemplate> templates;

    // 跟踪位姿管理
    // 初始检测时的标定板在相机中的姿态
    cv::Mat R_target2cam_initial, t_target2cam_initial;

    // 初始设置标志
    bool initialPoseSet;

    // 模板参考位姿 - 保存模板时的机器人位姿（不应在跟踪过程中更新）
    rw::math::Transform3D<> templateRobotPose;

    // 跟踪参考位姿 - 用于计算相对运动的参考位姿（在跟踪过程中会更新）
    rw::math::Transform3D<> referenceRobotPose;

    // 初始机器人位姿 - 仅在首次设置初始位姿时记录，不应被更新
    rw::math::Transform3D<> initialRobotPose;

    // Current board pose data
    cv::Mat R_target2cam, t_target2cam;

    // Kalman filters for smoothing
    KalmanFilter3D positionFilter;
    KalmanFilter3D rotationFilter;

    // PID控制参数
    struct PIDParams {
        double Kp;  // 比例系数
        double Ki;  // 积分系数
        double Kd;  // 微分系数
        double integral;  // 积分项
        double prevError; // 上一次误差
        double output;    // 输出值
        double minOutput; // 最小输出
        double maxOutput; // 最大输出
    };

    PIDParams moveDistancePID;  // 移动距离PID控制器

    // 初始化PID控制器
    void initPIDController();

    // 更新PID控制器
    double updatePIDController(double currentValue, double targetValue);

    // 动态调整的最大移动距离阈值 (毫米)
    double dynamicMaxMoveDistance;

    // 历史移动距离队列，用于判断趋势
    std::deque<double> recentMoveDistances;

    // 用于计算移动距离趋势的历史数据数量
    static const int MAX_MOVE_HISTORY = 3;

    // 动态调整最大移动距离阈值
    void updateDynamicMaxMoveDistance(double currentMoveDistance);

    // Helper methods
    bool loadCameraCalibration(const std::string& filename);
    bool loadHandEyeCalibration(const std::string& filename);
    rw::math::Transform3D<> rtToTransform3D(const cv::Mat& R, const cv::Mat& t) const;
    void transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t) const;
    bool detectArucoBoard(const cv::Mat& frame, std::vector<int>& ids,
                          std::vector<std::vector<cv::Point2f>>& corners);
    bool estimateBoardPose(const std::vector<std::vector<cv::Point2f>>& corners,
                           const std::vector<int>& ids,
                           cv::Mat& rvec, cv::Mat& tvec);
    void updateRobotPosition(const cv::Mat& R_target2cam, const cv::Mat& t_target2cam, const std::vector<int>& markerIds = std::vector<int>());
    std::string getCurrentTimeString() const;


};

#endif // TRACKINGMANAGER_H