#include "CalibrationUI.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QLabel>
#include <QTableWidget>
#include <QHeaderView>
#include <QDialog>
#include <QDialogButtonBox>
#include <QFileDialog>
#include <QMessageBox>
#include <QPainter>
#include <QFormLayout>
#include <QDoubleSpinBox>
#include <cmath>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>

CalibrationUI::CalibrationUI(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent)
    : QWidget(parent)
    , trackingManager(trackingManager)
{
    setupUi();
    initConnections();
}

CalibrationUI::~CalibrationUI()
{
    // 资源会在析构时自动释放
}

void CalibrationUI::setupUi()
{
    // 创建主布局
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    
    // 创建校准参数显示区域
    QGroupBox* calibrationGroup = new QGroupBox("标定参数", this);
    mainLayout->addWidget(calibrationGroup);
    
    QVBoxLayout* calibrationLayout = new QVBoxLayout(calibrationGroup);
    
    // 显示标定参数按钮
    viewCalibrationButton = new QPushButton("查看标定参数", this);
    calibrationLayout->addWidget(viewCalibrationButton);
    
    // 添加标定操作说明
    QLabel* infoLabel = new QLabel(
        "相机标定和手眼标定是系统准确跟踪所必需的。这些参数由外部标定程序生成，"
        "并保存在配置文件中。点击上方按钮可查看当前加载的标定参数。", this);
    infoLabel->setWordWrap(true);
    calibrationLayout->addWidget(infoLabel);
    
    // 创建校准工具区域
    QGroupBox* toolsGroup = new QGroupBox("标定工具", this);
    mainLayout->addWidget(toolsGroup);
    
    QVBoxLayout* toolsLayout = new QVBoxLayout(toolsGroup);
    
    // 相机标定工具按钮
    QPushButton* cameraCalibButton = new QPushButton("相机标定工具", this);
    toolsLayout->addWidget(cameraCalibButton);
    
    // 手眼标定工具按钮
    QPushButton* handEyeCalibButton = new QPushButton("手眼标定工具", this);
    toolsLayout->addWidget(handEyeCalibButton);
    
    // 重新加载标定文件按钮
    QPushButton* reloadCalibButton = new QPushButton("重新加载标定文件", this);
    toolsLayout->addWidget(reloadCalibButton);
    
    // 添加详细说明
    QLabel* toolsInfoLabel = new QLabel(
        "注意：标定工具是独立的程序，点击按钮将启动相应的工具应用。"
        "完成标定后，需要点击重新加载标定文件按钮更新系统中的标定参数。", this);
    toolsInfoLabel->setWordWrap(true);
    toolsLayout->addWidget(toolsInfoLabel);
    
    // 创建可视化区域
    QGroupBox* visualizationGroup = new QGroupBox("标定可视化", this);
    mainLayout->addWidget(visualizationGroup, 1); // 给可视化区域分配更多空间
    
    QVBoxLayout* visualizationLayout = new QVBoxLayout(visualizationGroup);
    
    QLabel* visualizationLabel = new QLabel(this);
    visualizationLabel->setMinimumSize(400, 300);
    visualizationLabel->setAlignment(Qt::AlignCenter);
    visualizationLabel->setText("点击查看标定参数按钮查看标定可视化");
    visualizationLabel->setStyleSheet("border: 1px solid #3F3F46; background-color: #252526;");
    visualizationLayout->addWidget(visualizationLabel);
    
    // 连接相机标定工具按钮
    connect(cameraCalibButton, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, "启动工具",
            "相机标定工具将在新窗口中启动。\n"
            "请按照工具中的说明进行相机标定。\n"
            "标定完成后，请重新加载标定文件。");
        
        // 发出相机标定工具启动信号
        emit cameraCalibrationToolLaunched();
        
        // 这里应该启动实际的相机标定工具
        // system("start CameraCalibrationTool.exe");
    });
    
    // 连接手眼标定工具按钮
    connect(handEyeCalibButton, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, "启动工具",
            "手眼标定工具将在新窗口中启动。\n"
            "请按照工具中的说明进行手眼标定。\n"
            "标定完成后，请重新加载标定文件。");
        
        // 发出手眼标定工具启动信号
        emit handEyeCalibrationToolLaunched();
        
        // 这里应该启动实际的手眼标定工具
        // system("start HandEyeCalibrationTool.exe");
    });
    
    // 连接重新加载标定文件按钮
    connect(reloadCalibButton, &QPushButton::clicked, [this]() {
        bool success = trackingManager->reloadCalibration();
        
        // 发出标定参数重新加载信号
        emit calibrationParametersReloaded(success);
        
        if (success) {
            QMessageBox::information(this, "重新加载", "标定参数已成功重新加载。");
        } else {
            QMessageBox::warning(this, "重新加载失败", "无法重新加载标定参数，请检查文件是否存在。");
        }
    });
    
    // 添加弹性空间
    mainLayout->addStretch(1);
}

void CalibrationUI::initConnections()
{
    // 连接查看标定参数按钮
    connect(viewCalibrationButton, &QPushButton::clicked, this, &CalibrationUI::onShowCalibrationParams);
}

void CalibrationUI::onShowCalibrationParams()
{
    // 发出查看标定参数信号
    emit calibrationParametersViewed();
    
    showCalibrationDialog();
}

void CalibrationUI::showCalibrationDialog()
{
    QDialog dialog(this);
    dialog.setWindowTitle("标定参数");
    dialog.setMinimumWidth(600);
    
    QVBoxLayout *layout = new QVBoxLayout(&dialog);
    
    // 创建标签页控件
    QTabWidget *tabWidget = new QTabWidget(&dialog);
    layout->addWidget(tabWidget);
    
    // ========== 相机内参标签页 ==========
    QWidget *cameraTab = new QWidget();
    tabWidget->addTab(cameraTab, "相机内参");
    
    QVBoxLayout *cameraLayout = new QVBoxLayout(cameraTab);
    
    // 获取相机内参矩阵
    cv::Mat cameraMatrix = trackingManager->getCameraMatrix();
    cv::Mat distCoeffs = trackingManager->getDistCoeffs();
    
    // 相机内参表格
    QGroupBox *intrinsicGroup = new QGroupBox("相机内参矩阵", cameraTab);
    cameraLayout->addWidget(intrinsicGroup);
    
    QGridLayout *intrinsicLayout = new QGridLayout(intrinsicGroup);
    
    if (!cameraMatrix.empty()) {
        QTableWidget *intrinsicTable = new QTableWidget(3, 3, intrinsicGroup);
        intrinsicTable->setEditTriggers(QTableWidget::NoEditTriggers);
        intrinsicTable->horizontalHeader()->setVisible(false);
        intrinsicTable->verticalHeader()->setVisible(false);
        
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                double value = cameraMatrix.at<double>(i, j);
                QTableWidgetItem *item = new QTableWidgetItem(QString::number(value, 'f', 6));
                item->setTextAlignment(Qt::AlignCenter);
                intrinsicTable->setItem(i, j, item);
            }
        }
        
        intrinsicTable->resizeColumnsToContents();
        intrinsicTable->resizeRowsToContents();
        intrinsicLayout->addWidget(intrinsicTable);
    } else {
        QLabel *noDataLabel = new QLabel("无相机内参数据", intrinsicGroup);
        intrinsicLayout->addWidget(noDataLabel);
    }
    
    // 畸变系数
    QGroupBox *distortionGroup = new QGroupBox("畸变系数", cameraTab);
    cameraLayout->addWidget(distortionGroup);
    
    QHBoxLayout *distortionLayout = new QHBoxLayout(distortionGroup);
    
    if (!distCoeffs.empty()) {
        QString distText = "";
        for (int i = 0; i < distCoeffs.rows * distCoeffs.cols; i++) {
            if (i > 0) distText += ", ";
            distText += QString::number(distCoeffs.at<double>(i), 'f', 6);
        }
        
        QLabel *distortionLabel = new QLabel(distText, distortionGroup);
        distortionLayout->addWidget(distortionLabel);
    } else {
        QLabel *noDataLabel = new QLabel("无畸变系数数据", distortionGroup);
        distortionLayout->addWidget(noDataLabel);
    }
    
    // 相机参数说明
    QLabel *cameraInfoLabel = new QLabel(
        "相机内参矩阵描述了相机的内部几何特性，包括焦距和光学中心。\n"
        "畸变系数用于校正镜头产生的畸变。\n"
        "这些参数通过相机标定获得，对图像处理和姿态估计至关重要。",
        cameraTab);
    cameraInfoLabel->setWordWrap(true);
    cameraLayout->addWidget(cameraInfoLabel);
    
    // ========== 手眼标定标签页 ==========
    QWidget *handEyeTab = new QWidget();
    tabWidget->addTab(handEyeTab, "手眼标定");
    
    QVBoxLayout *handEyeLayout = new QVBoxLayout(handEyeTab);
    
    // 获取手眼标定参数
    cv::Mat rotationMatrix = trackingManager->getRotationMatrix();
    cv::Mat translationVector = trackingManager->getTranslationVector();
    
    // 旋转矩阵
    QGroupBox *rotationGroup = new QGroupBox("旋转矩阵", handEyeTab);
    handEyeLayout->addWidget(rotationGroup);
    
    QGridLayout *rotationLayout = new QGridLayout(rotationGroup);
    
    if (!rotationMatrix.empty()) {
        QTableWidget *rotationTable = new QTableWidget(3, 3, rotationGroup);
        rotationTable->setEditTriggers(QTableWidget::NoEditTriggers);
        rotationTable->horizontalHeader()->setVisible(false);
        rotationTable->verticalHeader()->setVisible(false);
        
        for (int i = 0; i < 3; i++) {
            for (int j = 0; j < 3; j++) {
                double value = rotationMatrix.at<double>(i, j);
                QTableWidgetItem *item = new QTableWidgetItem(QString::number(value, 'f', 6));
                item->setTextAlignment(Qt::AlignCenter);
                rotationTable->setItem(i, j, item);
            }
        }
        
        rotationTable->resizeColumnsToContents();
        rotationTable->resizeRowsToContents();
        rotationLayout->addWidget(rotationTable);
    } else {
        QLabel *noDataLabel = new QLabel("无旋转矩阵数据", rotationGroup);
        rotationLayout->addWidget(noDataLabel);
    }
    
    // 平移向量
    QGroupBox *translationGroup = new QGroupBox("平移向量", handEyeTab);
    handEyeLayout->addWidget(translationGroup);
    
    QHBoxLayout *translationLayout = new QHBoxLayout(translationGroup);
    
    if (!translationVector.empty()) {
        QTableWidget *translationTable = new QTableWidget(3, 1, translationGroup);
        translationTable->setEditTriggers(QTableWidget::NoEditTriggers);
        translationTable->horizontalHeader()->setVisible(false);
        translationTable->verticalHeader()->setVisible(false);
        
        for (int i = 0; i < 3; i++) {
            double value = translationVector.at<double>(i);
            QTableWidgetItem *item = new QTableWidgetItem(QString::number(value, 'f', 6));
            item->setTextAlignment(Qt::AlignCenter);
            translationTable->setItem(i, 0, item);
        }
        
        translationTable->resizeColumnsToContents();
        translationTable->resizeRowsToContents();
        translationLayout->addWidget(translationTable);
    } else {
        QLabel *noDataLabel = new QLabel("无平移向量数据", translationGroup);
        translationLayout->addWidget(noDataLabel);
    }
    
    // 手眼标定说明
    QLabel *handEyeInfoLabel = new QLabel(
        "手眼标定描述了相机相对于机器人末端执行器的位置关系。\n"
        "旋转矩阵描述了相机相对于末端执行器的旋转。\n"
        "平移向量描述了相机相对于末端执行器的位移。\n"
        "这些参数通过收集机器人末端位姿和相机观察数据进行标定。",
        handEyeTab);
    handEyeInfoLabel->setWordWrap(true);
    handEyeLayout->addWidget(handEyeInfoLabel);
    
    // ========== 可视化标签页 ==========
    QWidget *visualizationTab = new QWidget();
    tabWidget->addTab(visualizationTab, "可视化");
    
    QVBoxLayout *visualizationLayout = new QVBoxLayout(visualizationTab);
    
    // 创建可视化画布
    QLabel *visualizationLabel = new QLabel(visualizationTab);
    visualizationLabel->setMinimumSize(400, 300);
    visualizationLabel->setAlignment(Qt::AlignCenter);
    visualizationLabel->setStyleSheet("border: 1px solid #3F3F46; background-color: #252526;");
    visualizationLayout->addWidget(visualizationLabel);
    
    // 可视化控制区域
    QGroupBox *visualControlGroup = new QGroupBox("可视化控制", visualizationTab);
    visualizationLayout->addWidget(visualControlGroup);
    
    QFormLayout *visualControlLayout = new QFormLayout(visualControlGroup);
    
    QSlider *rotationSlider = new QSlider(Qt::Horizontal, visualControlGroup);
    rotationSlider->setRange(0, 360);
    rotationSlider->setValue(30);
    visualControlLayout->addRow("旋转角度:", rotationSlider);
    
    QSlider *elevationSlider = new QSlider(Qt::Horizontal, visualControlGroup);
    elevationSlider->setRange(-90, 90);
    elevationSlider->setValue(30);
    visualControlLayout->addRow("仰角:", elevationSlider);
    
    QDoubleSpinBox *scaleSpinBox = new QDoubleSpinBox(visualControlGroup);
    scaleSpinBox->setRange(0.1, 10.0);
    scaleSpinBox->setValue(1.0);
    scaleSpinBox->setSingleStep(0.1);
    visualControlLayout->addRow("缩放:", scaleSpinBox);
    
    // 绘制坐标系和相机位置的函数
    auto drawCalibrationVisualization = [&]() {
        if (rotationMatrix.empty() || translationVector.empty()) {
            visualizationLabel->setText("无手眼标定数据可供可视化");
            return;
        }
        
        QPixmap pixmap(visualizationLabel->width(), visualizationLabel->height());
        pixmap.fill(Qt::black);
        
        QPainter painter(&pixmap);
        painter.setRenderHint(QPainter::Antialiasing);
        
        // 中心点和缩放
        int centerX = pixmap.width() / 2;
        int centerY = pixmap.height() / 2;
        double scale = scaleSpinBox->value() * 50; // 缩放因子
        
        // 旋转角度（弧度）
        double rotationRad = rotationSlider->value() * M_PI / 180.0;
        double elevationRad = elevationSlider->value() * M_PI / 180.0;
        
        // 计算旋转矩阵
        double cosRot = cos(rotationRad);
        double sinRot = sin(rotationRad);
        double cosElev = cos(elevationRad);
        double sinElev = sin(elevationRad);
        
        // 绘制机器人坐标系
        int axisLength = 100;
        
        // X轴（红色）
        painter.setPen(QPen(Qt::red, 2));
        int x1 = centerX + static_cast<int>(axisLength * cosRot * cosElev * scale / 100);
        int y1 = centerY - static_cast<int>(axisLength * sinRot * cosElev * scale / 100);
        painter.drawLine(centerX, centerY, x1, y1);
        painter.drawText(x1 + 5, y1, "X");
        
        // Y轴（绿色）
        painter.setPen(QPen(Qt::green, 2));
        int x2 = centerX + static_cast<int>(axisLength * cos(rotationRad + M_PI/2) * cosElev * scale / 100);
        int y2 = centerY - static_cast<int>(axisLength * sin(rotationRad + M_PI/2) * cosElev * scale / 100);
        painter.drawLine(centerX, centerY, x2, y2);
        painter.drawText(x2 + 5, y2, "Y");
        
        // Z轴（蓝色）
        painter.setPen(QPen(Qt::blue, 2));
        int x3 = centerX + static_cast<int>(axisLength * sinElev * scale / 100);
        int y3 = centerY - static_cast<int>(axisLength * cosElev * scale / 100);
        painter.drawLine(centerX, centerY, x3, y3);
        painter.drawText(x3 + 5, y3, "Z");
        
        // 计算相机位置
        double tx = translationVector.at<double>(0);
        double ty = translationVector.at<double>(1);
        double tz = translationVector.at<double>(2);
        
        // 应用视角旋转到平移向量
        double x = tx * cosRot - ty * sinRot;
        double y = tx * sinRot * cosElev + ty * cosRot * cosElev - tz * sinElev;
        
        // 绘制相机位置（黄色）
        int cameraX = centerX + static_cast<int>(x * scale);
        int cameraY = centerY - static_cast<int>(y * scale);
        
        painter.setPen(QPen(Qt::yellow, 2));
        painter.setBrush(QBrush(Qt::yellow));
        painter.drawEllipse(cameraX - 5, cameraY - 5, 10, 10);
        painter.drawText(cameraX + 10, cameraY, "相机");
        
        // 绘制相机到机器人的连线
        painter.setPen(QPen(Qt::white, 1, Qt::DashLine));
        painter.drawLine(centerX, centerY, cameraX, cameraY);
        
        visualizationLabel->setPixmap(pixmap);
    };
    
    // 连接控制器改变事件
    connect(rotationSlider, &QSlider::valueChanged, drawCalibrationVisualization);
    connect(elevationSlider, &QSlider::valueChanged, drawCalibrationVisualization);
    connect(scaleSpinBox, QOverload<double>::of(&QDoubleSpinBox::valueChanged), drawCalibrationVisualization);
    
    // 初始绘制
    drawCalibrationVisualization();
    
    // 对话框按钮
    QDialogButtonBox *buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok, &dialog);
    layout->addWidget(buttonBox);
    
    connect(buttonBox, &QDialogButtonBox::accepted, &dialog, &QDialog::accept);
    
    dialog.exec();
} 