#include "TrackingManager.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <glog.h>
#include <Windows.h>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>
#include <thread> // 添加线程支持，用于sleep_for
#include <chrono> // 添加时间支持，用于毫秒计时

TrackingManager::TrackingManager()
        : trackingEnabled(false)
        , moveThreshold(0.001) // 修改为3mm默认值，提高稳定性
        , rotThreshold(0.0017)   // 修改为约0.28度默认值，提高稳定性
        , enableRotationAdjustment(false) // 默认关闭旋转调整
        , invertRotationDirection(true) // 默认反转旋转方向
        , currentTemplate("default")
        , initialPoseSet(false)
        , isRobotMoving(false)
        , frameTimestamp(0.0)
        , dynamicMaxMoveDistance(10.0) // 初始化最大移动距离为10毫米
{
    // Initialize the ArUco dictionary and ArUco GridBoard
    cv::aruco::Dictionary dictionaryTmp = cv::aruco::getPredefinedDictionary(cv::aruco::DICT_6X6_250);
    dictionary = cv::Ptr<cv::aruco::Dictionary>(new cv::aruco::Dictionary(dictionaryTmp));

    // Similar to VisionServoController parameters
    const int markersX = 5;
    const int markersY = 7;
    const float markerLength = 0.006f;
    const float markerSeparation = 0.002f;

    board = cv::Ptr<cv::aruco::GridBoard>(new cv::aruco::GridBoard({markersX, markersY}, markerLength, markerSeparation, *dictionary));
    parameters = cv::makePtr<cv::aruco::DetectorParameters>();

    initialize();

    // 初始化PID控制器
    initPIDController();
}

TrackingManager::~TrackingManager()
{
    // Clean up resources
    if (robot) {
        robot->stop();
    }

    if (camera) {
        camera->stopGrabbing();
        camera->disconnect();
    }
}

bool TrackingManager::initialize()
{
    LOG(INFO) << "Initializing TrackingManager...";

    // Load camera calibration
    if (!loadCameraCalibration("camera_calibration.yml")) {
        LOG(ERROR) << "Failed to load camera calibration parameters";
        return false;
    }

    // Load hand-eye calibration
    if (!loadHandEyeCalibration("hand_eye_calibration.yml")) {
        LOG(ERROR) << "Failed to load hand-eye calibration parameters";
        return false;
    }

    // Initialize robot
    robot = std::make_shared<AuboArcsDriver>();
    if (!robot->connect("192.168.1.189", 8899)) {
        LOG(ERROR) << "Failed to connect to robot";
        return false;
    }

    // Initialize camera
    camera = std::make_shared<HikVisionCamera>();
    camera->setErrorCallback([](const std::string &error) {
        LOG(ERROR) << "Camera error: " << error;
    });

    if (!camera->connectToDevice("*********")) {  // Replace with actual camera serial
        LOG(ERROR) << "Failed to connect to camera";
        return false;
    }

    // Set camera parameters
    camera->setExposureTime(120000.0f);
    camera->setGain(0.0f);
    camera->setFrameRate(30.0f);

    if (!camera->startGrabbing()) {
        LOG(ERROR) << "Failed to start image acquisition";
        return false;
    }

    LOG(INFO) << "TrackingManager initialized successfully";
    return true;
}

cv::Mat TrackingManager::getprocessFrame()
{
    return frame;
}

cv::Mat TrackingManager::processFrame(TrackingStatus& status)
{
    // 使用全局图像frame而不是重新获取
    if (frame.empty()) {
        LOG(ERROR) << "没有可用的图像帧";
        return frame;
    }

    cv::Mat displayFrame = frame.clone();

    // Update tracking status
    status.trackingEnabled = trackingEnabled;
    status.currentTemplate = currentTemplate;
    status.templateAvailable = !templates.empty();
    status.initialPoseSet = initialPoseSet;

    // Detect ArUco markers
    std::vector<int> markerIds;
    std::vector<std::vector<cv::Point2f>> markerCorners;
    status.markersDetected = detectArucoBoard(frame, markerIds, markerCorners);

    if (status.markersDetected) {
        // Draw detected markers
        cv::aruco::drawDetectedMarkers(displayFrame, markerCorners, markerIds);

        // Estimate board pose
        cv::Mat rvec, tvec;
        if (estimateBoardPose(markerCorners, markerIds, rvec, tvec)) {
            // Draw coordinate axes
            cv::drawFrameAxes(displayFrame, cameraMatrix, distCoeffs, rvec, tvec, 0.05);

            // Convert rotation vector to matrix
            cv::Mat R_target2cam;
            cv::Rodrigues(rvec, R_target2cam);

            // Store the current board pose for later use
            this->R_target2cam = R_target2cam.clone();
            this->t_target2cam = tvec.clone();

            // Calculate view angle
            cv::Mat boardNormal = R_target2cam * (cv::Mat_<double>(3, 1) << 0, 0, 1);
            cv::Mat cameraDirection = (cv::Mat_<double>(3, 1) << 0, 0, 1);
            double cosAngle = boardNormal.dot(cameraDirection) /
                              (cv::norm(boardNormal) * cv::norm(cameraDirection));
            status.viewAngle = acos(cosAngle) * 180.0 / M_PI;

            // Initialize pose if needed
            if (!initialPoseSet) {
                // Try to load from template first
                if (templates.find(currentTemplate) != templates.end() && templates[currentTemplate].isValid) {
                    // 从模板中加载预期的相机到标记板的变换
                    R_target2cam_initial = templates[currentTemplate].R_target2cam.clone();
                    t_target2cam_initial = templates[currentTemplate].t_target2cam.clone();

                    // 记录机器人当前位姿
                    rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
                    initialRobotPose = currentRobotPose;
                    referenceRobotPose = currentRobotPose;

                    // 保存模板中的机器人位姿以供参考
                    templateRobotPose = templates[currentTemplate].robotPose;

                    initialPoseSet = true;
                    LOG(INFO) << "已加载模板中的ArUco标志板参考位姿: " << currentTemplate;
                }
                else {
                    // 首次设置，使用当前位姿作为预期位姿
                    R_target2cam_initial = R_target2cam.clone();
                    t_target2cam_initial = tvec.clone();

                    rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
                    initialRobotPose = currentRobotPose;
                    referenceRobotPose = currentRobotPose;
                    templateRobotPose = currentRobotPose;

                    initialPoseSet = true;
                    LOG(INFO) << "使用当前ArUco标志板位姿作为参考位姿";

                    // Auto-save as template if none exists
                    if (templates.empty()) {
                        saveTemplate(currentTemplate);
                    }
                }

                // Reset Kalman filters when initializing new pose
                positionFilter.reset();
                rotationFilter.reset();

                // 预热Kalman滤波器使其更快收敛
                warmupKalmanFilter();
            }

            // 计算当前检测到的标志板位姿与模板中参考位姿的相对变换
            cv::Mat R_relative;
            if (invertRotationDirection) {
                // 反转方向: 从初始位置到当前位置
                R_relative = R_target2cam_initial * R_target2cam.t();
                LOG(INFO) << "计算相对旋转矩阵 R_relative (反转方向): \n"
                        << R_relative.at<double>(0,0) << " " << R_relative.at<double>(0,1) << " " << R_relative.at<double>(0,2) << "\n"
                        << R_relative.at<double>(1,0) << " " << R_relative.at<double>(1,1) << " " << R_relative.at<double>(1,2) << "\n"
                        << R_relative.at<double>(2,0) << " " << R_relative.at<double>(2,1) << " " << R_relative.at<double>(2,2);
            } else {
                // 原始方向: 从当前位置到初始位置
                R_relative = R_target2cam * R_target2cam_initial.t();
                LOG(INFO) << "计算相对旋转矩阵 R_relative (原始方向): \n"
                        << R_relative.at<double>(0,0) << " " << R_relative.at<double>(0,1) << " " << R_relative.at<double>(0,2) << "\n"
                        << R_relative.at<double>(1,0) << " " << R_relative.at<double>(1,1) << " " << R_relative.at<double>(1,2) << "\n"
                        << R_relative.at<double>(2,0) << " " << R_relative.at<double>(2,1) << " " << R_relative.at<double>(2,2);
            }

            // 将相对旋转转换为旋转向量
            cv::Mat rvec;
            cv::Rodrigues(R_relative, rvec);
            double rotAngle = cv::norm(rvec);
            LOG(INFO) << "旋转角度: " << rotAngle * 180.0 / M_PI << " 度";

            // 检查是否启用旋转调整
            bool applyRotation = enableRotationAdjustment;
            if (!applyRotation) {
                LOG(INFO) << "旋转调整功能当前已禁用，将不应用旋转变换";
            } else {
                LOG(INFO) << "旋转调整功能已启用，将应用旋转变换";
                
                // 安全检查：视角安全检查 - 如果视角已经接近侧视，不应用旋转
                double cameraViewAngle = 0.0;
                cv::Mat boardNormal = R_target2cam * (cv::Mat_<double>(3, 1) << 0, 0, 1);
                cv::Mat cameraDirection = (cv::Mat_<double>(3, 1) << 0, 0, 1);
                double cosAngle = boardNormal.dot(cameraDirection) /
                                (cv::norm(boardNormal) * cv::norm(cameraDirection));
                cameraViewAngle = acos(cosAngle) * 180.0 / M_PI;
                
                LOG(INFO) << "当前标记板与相机视角: " << cameraViewAngle << " 度";
                
                // 如果视角过大（接近限制角度），则不应用旋转以防止跟踪丢失
                const double MAX_VIEW_ANGLE = 55.0; // 度，减小为55度更安全
                if (cameraViewAngle > MAX_VIEW_ANGLE) {
                    LOG(WARNING) << "当前视角 (" << cameraViewAngle << "°) 已接近临界值，不应用旋转以防止标记板离开视野";
                    applyRotation = false;
                }
                else {
                    // 安全检查：检测到的标记数量检查
                    const int BOARD_TOTAL_MARKERS = 35; // 5x7的标记板共有35个标记
                    int detectedMarkers = markerIds.size();
                    double detectionRatio = static_cast<double>(detectedMarkers) / BOARD_TOTAL_MARKERS;
                    
                    LOG(INFO) << "检测到 " << detectedMarkers << " 个标记，占标记板总数的 " 
                             << (detectionRatio * 100.0) << "%";
                    
                    // 要求至少检测到60%的标记才允许旋转
                    const double MIN_DETECTION_RATIO = 0.6;
                    if (detectionRatio < MIN_DETECTION_RATIO) {
                        LOG(WARNING) << "检测到的标记比例 (" << (detectionRatio * 100.0) 
                                   << "%) 不足，不应用旋转以防止跟踪丢失";
                        applyRotation = false;
                    }
                    else {
                        // 限制最大旋转角度（安全限制）- 减小为2.5度
                        const double MAX_ROTATION_ANGLE = 2.5 * M_PI / 180.0; // 2.5度最大旋转（比之前更小）
                        
                        // 应用渐进式旋转 - 根据检测到的标记比例调整旋转幅度
                        // 标记检测率越高，允许的旋转幅度越大
                        double adjustedMaxRotation = MAX_ROTATION_ANGLE * (detectionRatio / MIN_DETECTION_RATIO);
                        
                        if (rotAngle > adjustedMaxRotation) {
                            double scale = adjustedMaxRotation / rotAngle;
                            rvec = rvec * scale;
                            rotAngle = adjustedMaxRotation;
                            LOG(INFO) << "旋转角度过大，基于检测率(" << (detectionRatio * 100.0) 
                                     << "%)缩放至: " << rotAngle * 180.0 / M_PI << " 度";
                            // 重新计算旋转矩阵
                            cv::Rodrigues(rvec, R_relative);
                        }

                        // 检查旋转角度是否太小，若过小则不进行旋转
                        const double MIN_ROTATION_ANGLE = 0.3 * M_PI / 180.0; // 0.3度最小旋转（更小的阈值）
                        if (rotAngle < MIN_ROTATION_ANGLE) {
                            applyRotation = false;
                            LOG(INFO) << "计算的旋转角度 " << rotAngle * 180.0 / M_PI << " 度小于最小旋转阈值 "
                                    << MIN_ROTATION_ANGLE * 180.0 / M_PI << " 度，无需应用旋转";
                        }
                    }
                }
            }

            // 将相对旋转从相机坐标系转换到机器人基坐标系
            cv::Mat R_base_relative;
            if (applyRotation) {
                // 在相机坐标系中：R_camera_target_relative = R_target2cam_initial * R_target2cam.t()
                // 转换到机器人基坐标系：R_base_relative = R_base_camera * R_camera_target_relative * R_base_camera.t()
                // 声明R_base_camera变量
                cv::Mat R_base_camera;
                cv::Mat t_dummy;
                rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();
                rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);
                rw::math::Transform3D<> T_base_camera = T_base_gripper * T_gripper_camera;
                transform3DToRt(T_base_camera, R_base_camera, t_dummy);
                
                R_base_relative = R_base_camera * R_relative * R_base_camera.t();
                
                // 输出R_base_relative用于调试
                LOG(INFO) << "机器人基坐标系中的相对旋转矩阵: \n"
                        << R_base_relative.at<double>(0,0) << " " << R_base_relative.at<double>(0,1) << " " << R_base_relative.at<double>(0,2) << "\n"
                        << R_base_relative.at<double>(1,0) << " " << R_base_relative.at<double>(1,1) << " " << R_base_relative.at<double>(1,2) << "\n"
                        << R_base_relative.at<double>(2,0) << " " << R_base_relative.at<double>(2,1) << " " << R_base_relative.at<double>(2,2);
            }

            // 13. 计算目标位姿 (修改位置和姿态)
            // 确保T_base_gripper和position_offset在这里可用
            if (!robot) {
                LOG(ERROR) << "Robot not initialized";
                return displayFrame;
            }
            rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();
            
            // 确保position_offset已经定义
            // 定义dx、dy、dz变量
            double dx = t_target2cam.at<double>(0) - t_target2cam_initial.at<double>(0);
            double dy = t_target2cam.at<double>(1) - t_target2cam_initial.at<double>(1);
            double dz = t_target2cam.at<double>(2) - t_target2cam_initial.at<double>(2);
            cv::Mat camera_offset = (cv::Mat_<double>(3,1) << dx, dy, dz);
            cv::Mat R_base_camera;
            cv::Mat t_dummy;
            rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);
            rw::math::Transform3D<> T_base_camera = T_base_gripper * T_gripper_camera;
            transform3DToRt(T_base_camera, R_base_camera, t_dummy);
            cv::Mat base_offset = R_base_camera * camera_offset;
            
            // 构建位置偏移
            rw::math::Vector3D<> position_offset(
                base_offset.at<double>(0),
                base_offset.at<double>(1),
                base_offset.at<double>(2)
            );
            
            rw::math::Vector3D<> target_position = T_base_gripper.P() + position_offset;
            rw::math::Rotation3D<> currentRotation = T_base_gripper.R();
            rw::math::Rotation3D<> targetRotation;

            if (applyRotation) {
                // 应用相对旋转到当前旋转上
                // 原代码: targetRotation = currentRotation * relativeRotation;
                // 尝试改为:
                rw::math::Rotation3D<> relativeRotation(
                    R_base_relative.at<double>(0,0), R_base_relative.at<double>(0,1), R_base_relative.at<double>(0,2),
                    R_base_relative.at<double>(1,0), R_base_relative.at<double>(1,1), R_base_relative.at<double>(1,2),
                    R_base_relative.at<double>(2,0), R_base_relative.at<double>(2,1), R_base_relative.at<double>(2,2)
                );

                // 尝试使用逆旋转
                rw::math::Rotation3D<> inverseRelativeRotation = relativeRotation.inverse();
                targetRotation = currentRotation * inverseRelativeRotation; // 使用逆旋转

                // 输出旋转前后的欧拉角，用于调试
                rw::math::RPY<> currentRPY(currentRotation);
                rw::math::RPY<> targetRPY(targetRotation);
                LOG(INFO) << "当前欧拉角 (RPY): ["
                        << currentRPY[0] * 180.0 / M_PI << ", "
                        << currentRPY[1] * 180.0 / M_PI << ", "
                        << currentRPY[2] * 180.0 / M_PI << "] 度";
                LOG(INFO) << "目标欧拉角 (RPY): ["
                        << targetRPY[0] * 180.0 / M_PI << ", "
                        << targetRPY[1] * 180.0 / M_PI << ", "
                        << targetRPY[2] * 180.0 / M_PI << "] 度";
                LOG(INFO) << "应用旋转调整，旋转角度: " << rotAngle * 180.0 / M_PI << " 度 (反向应用)";
            } else {
                // 保持当前旋转不变
                targetRotation = currentRotation;
                LOG(INFO) << "旋转角度过小，保持当前旋转不变";
            }

            rw::math::Transform3D<> T_base_target(target_position, targetRotation);
            LOG(INFO) << "步骤13 - 计算的目标位置: ["
                      << target_position[0] << ", "
                      << target_position[1] << ", "
                      << target_position[2] << "] (米)";

            // 14. 应用Kalman滤波
            cv::Point3f rawPosition(position_offset[0], position_offset[1], position_offset[2]);
            // 临时屏蔽Kalman滤波器，直接使用原始值
            positionFilter.update(rawPosition); // 仍然调用update以保持状态一致
            cv::Point3f filteredPosition = rawPosition; // 直接使用原始值

            // 新增：对旋转偏移进行处理（如果有旋转的话）
            cv::Point3f rawRotation(0, 0, 0);
            cv::Point3f filteredRotation(0, 0, 0);
            if (applyRotation) {
                // 将旋转向量转换为三维点用于滤波
                rawRotation.x = rvec.at<double>(0);
                rawRotation.y = rvec.at<double>(1);
                rawRotation.z = rvec.at<double>(2);
                
                // 临时屏蔽旋转Kalman滤波，直接使用原始值
                rotationFilter.update(rawRotation); // 仍然调用update以保持状态一致
                filteredRotation = rawRotation; // 直接使用原始值
            }

            LOG(INFO) << "步骤14 - Kalman滤波已暂时屏蔽";
            LOG(INFO) << "步骤14 - 使用原始位置偏移: ["
                      << rawPosition.x << ", " << rawPosition.y << ", " << rawPosition.z << "] (米)";
            LOG(INFO) << "步骤14 - 使用原始旋转向量: ["
                      << rawRotation.x << ", " << rawRotation.y << ", " << rawRotation.z << "] (弧度)";

            // 15. 构建目标位姿
            rw::math::Vector3D<> filteredPositionVector(
                    T_base_gripper.P()[0] + filteredPosition.x,
                    T_base_gripper.P()[1] + filteredPosition.y,
                    T_base_gripper.P()[2] + filteredPosition.z
            );

            // 使用前面计算好的targetRotation（已经包含了是否应用旋转的逻辑）
            rw::math::Transform3D<> filteredRobotPose(
                    filteredPositionVector,
                    targetRotation  // 使用包含旋转调整的目标旋转
            );

            LOG(INFO) << "步骤15 - 最终过滤后的目标位置: ["
                      << filteredPositionVector[0] << ", "
                      << filteredPositionVector[1] << ", "
                      << filteredPositionVector[2] << "] (米)";
            if (applyRotation) {
                LOG(INFO) << "步骤15 - 应用了旋转调整，旋转角度: " << rotAngle * 180.0 / M_PI << " 度";
            } else {
                LOG(INFO) << "步骤15 - 未应用旋转调整";
            }

            // 计算实际的移动距离（使用原始位置偏移，而不是过滤后的）
            double moveDistance = position_offset.norm2() * 1000; // 转换为毫米
            LOG(INFO) << "机器人移动: 距离=" << moveDistance << "mm";
            if (applyRotation) {
                LOG(INFO) << "机器人旋转: 角度=" << rotAngle * 180.0 / M_PI << " 度";
            }

            // 计算IK并检查安全约束
            rw::math::Q currentJointQ = robot->getRobotJointQ();
            rw::math::Q newJointQ;

            if (robot->getIK(currentJointQ, filteredRobotPose, newJointQ)) {
                // 检查安全阈值
                rw::math::Q jointChange = newJointQ - currentJointQ;
                const double safetyThreshold = 15.0 * M_PI / 180.0; // 降低为15度
                bool isSafeMovement = true;

                for (size_t i = 0; i < jointChange.size(); i++) {
                    if (std::abs(jointChange[i]) > safetyThreshold) {
                        LOG(WARNING) << "Joint " << i + 1 << " change too large: "
                                     << jointChange[i] * 180.0 / M_PI << " deg, exceeding safety threshold "
                                     << safetyThreshold * 180.0 / M_PI << " deg";
                        isSafeMovement = false;
                        break;
                    }
                }

                if (isSafeMovement) {
                    // 执行机器人移动
                    robot->movej(newJointQ, 0.5, 0, 0.2);

                    // 更新参考位姿为过滤后的机器人位姿，用于下一步移动计算
                    referenceRobotPose = filteredRobotPose;
                } else {
                    LOG(ERROR) << "Movement exceeds safety limits, robot movement aborted";
                }
            } else {
                LOG(ERROR) << "Inverse kinematics failed, cannot move robot";
            }

            // 计算相对变换用于状态报告
            cv::Mat t_relative = tvec - R_target2cam * R_target2cam_initial.t() * t_target2cam_initial;
            
            // 计算变化量
            status.translationMagnitude = cv::norm(t_relative);
            double trace = R_relative.at<double>(0, 0) + R_relative.at<double>(1, 1) + R_relative.at<double>(2, 2);
            status.rotationAngle = acos((trace - 1.0) / 2.0);

            // 只有当机器人不在运动时才更新机器人位置
            if (trackingEnabled && !isRobotMoving &&
                (status.translationMagnitude > moveThreshold || status.rotationAngle > rotThreshold)) {
                // Check if view angle is near perpendicular
                if (abs(status.viewAngle - 90.0) < 15.0) {
                    LOG(WARNING) << "Camera nearly perpendicular to board (angle: " << status.viewAngle
                                << " deg), pausing movement for safety";
                } else {
                    updateRobotPosition(R_target2cam, tvec, markerIds);
                }
            }
        }
    }

    return displayFrame;
}

bool TrackingManager::detectArucoBoard(const cv::Mat& frame, std::vector<int>& ids,
                                       std::vector<std::vector<cv::Point2f>>& corners)
{
    // Detect ArUco markers
    std::vector<std::vector<cv::Point2f>> rejectedCorners;

    // 使用弃用但更简单的detectMarkers函数
    cv::aruco::detectMarkers(frame, dictionary, corners, ids, parameters, rejectedCorners);

    // 将最小检测标记数从25降低到15，以便更容易成功检测
    return ids.size() >= 15; // 降低检测阈值，提高成功率
}

bool TrackingManager::estimateBoardPose(const std::vector<std::vector<cv::Point2f>>& corners,
                                        const std::vector<int>& ids,
                                        cv::Mat& rvec, cv::Mat& tvec)
{
    if (corners.empty() || ids.empty()) {
        return false;
    }

    int boardDetected = cv::aruco::estimatePoseBoard(corners, ids, board, cameraMatrix, distCoeffs, rvec, tvec);

    // 将最小匹配标记数从25降低到15
    return boardDetected > 15; // 降低估计阈值，提高跟踪成功率
}

void TrackingManager::updateRobotPosition(const cv::Mat& R_target2cam, const cv::Mat& t_target2cam, const std::vector<int>& markerIds)
{
    LOG(INFO) << "Updating robot position to follow target";

    // 完全重新实现变换逻辑
    try {
        // 1. 设置机器人基坐标系和手爪坐标系的表示
        rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();
        LOG(INFO) << "步骤1 - 当前机器人位置: ["
                  << T_base_gripper.P()[0] << ", "
                  << T_base_gripper.P()[1] << ", "
                  << T_base_gripper.P()[2] << "] (米)";

        // 2. 设置手爪到相机的变换(手眼标定结果)
        rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);
        LOG(INFO) << "步骤2 - 手眼标定平移向量: ["
                  << t_cam2gripper.at<double>(0) << ", "
                  << t_cam2gripper.at<double>(1) << ", "
                  << t_cam2gripper.at<double>(2) << "] (米)";

        // 3. 计算基坐标系到相机的变换
        rw::math::Transform3D<> T_base_camera = T_base_gripper * T_gripper_camera;
        LOG(INFO) << "步骤3 - 基坐标系到相机的位置: ["
                  << T_base_camera.P()[0] << ", "
                  << T_base_camera.P()[1] << ", "
                  << T_base_camera.P()[2] << "] (米)";

        // 4. 当前标记板在相机中的位姿
        rw::math::Transform3D<> T_camera_target_current = rtToTransform3D(R_target2cam, t_target2cam);
        LOG(INFO) << "步骤4 - 当前标记板在相机中的位置: ["
                  << t_target2cam.at<double>(0) << ", "
                  << t_target2cam.at<double>(1) << ", "
                  << t_target2cam.at<double>(2) << "] (米)";

        // 5. 模板中标记板在相机中的位姿
        rw::math::Transform3D<> T_camera_target_expected = rtToTransform3D(R_target2cam_initial, t_target2cam_initial);
        LOG(INFO) << "步骤5 - 模板中标记板在相机中的位置: ["
                  << t_target2cam_initial.at<double>(0) << ", "
                  << t_target2cam_initial.at<double>(1) << ", "
                  << t_target2cam_initial.at<double>(2) << "] (米)";

        // 6. 计算当前标记板到期望标记板的纯位置偏差(不考虑旋转)
        double dx = t_target2cam.at<double>(0) - t_target2cam_initial.at<double>(0);
        double dy = t_target2cam.at<double>(1) - t_target2cam_initial.at<double>(1);
        double dz = t_target2cam.at<double>(2) - t_target2cam_initial.at<double>(2);
        LOG(INFO) << "步骤6 - 标记板位置偏差 dx=" << dx << ", dy=" << dy << ", dz=" << dz << " (米)";

        // 7. 相机坐标系中的位置偏差向量 - 注意这里需要取反，因为我们要移动到目标位置
        cv::Mat camera_offset = (cv::Mat_<double>(3,1) << dx, dy, dz);

        LOG(INFO) << "步骤7 - 相机坐标系中的位置偏差: ["
                  << camera_offset.at<double>(0) << ", "
                  << camera_offset.at<double>(1) << ", "
                  << camera_offset.at<double>(2) << "] (米)";

        // 8. 将相机坐标系中的偏差向量转换到基坐标系
        cv::Mat R_base_camera;
        cv::Mat t_dummy; // 创建一个临时变量来接收平移向量，虽然我们不需要它
        transform3DToRt(T_base_camera, R_base_camera, t_dummy);
        cv::Mat base_offset = R_base_camera * camera_offset;

        LOG(INFO) << "步骤8 - 旋转矩阵 R_base_camera: \n"
                  << R_base_camera.at<double>(0,0) << " " << R_base_camera.at<double>(0,1) << " " << R_base_camera.at<double>(0,2) << "\n"
                  << R_base_camera.at<double>(1,0) << " " << R_base_camera.at<double>(1,1) << " " << R_base_camera.at<double>(1,2) << "\n"
                  << R_base_camera.at<double>(2,0) << " " << R_base_camera.at<double>(2,1) << " " << R_base_camera.at<double>(2,2);

        LOG(INFO) << "步骤8 - 基坐标系中的位置偏差: ["
                  << base_offset.at<double>(0) << ", "
                  << base_offset.at<double>(1) << ", "
                  << base_offset.at<double>(2) << "] (米)";

        // 9. 构建位置偏移
        rw::math::Vector3D<> position_offset(
                base_offset.at<double>(0),
                base_offset.at<double>(1),
                base_offset.at<double>(2)
        );

        // 10. 计算移动幅度
        double moveDistance = position_offset.norm2()*1000; // 转换为毫米
        LOG(INFO) << "步骤10 - position_offset.norm2()=" << position_offset.norm2() << " (米)";
        LOG(INFO) << "步骤10 - 计算得到的移动距离: " << moveDistance << "mm";



        // 更新动态最大移动距离阈值
        updateDynamicMaxMoveDistance(moveDistance);
        // 11. 检查最大移动距离限制 - 使用动态阈值
        LOG(INFO) << "步骤11 - 当前动态最大移动距离阈值: " << dynamicMaxMoveDistance << "mm";
        if (moveDistance > dynamicMaxMoveDistance) {
            LOG(INFO) << "移动距离 " << moveDistance << "mm 超过当前动态阈值 " << dynamicMaxMoveDistance << "mm，将进行缩放";
            double scale = dynamicMaxMoveDistance / moveDistance;
            position_offset = position_offset * scale;
            moveDistance = dynamicMaxMoveDistance;
            LOG(INFO) << "缩放后的移动距离: " << moveDistance << "mm";
        }



        // 12. 只保留最小移动距离阈值检查
        const double MIN_MOVE_DISTANCE = 0.1; // 毫米 - 100微米的最小移动阈值

        // 检查移动距离是否过小，若过小则不进行移动
        if (moveDistance < MIN_MOVE_DISTANCE) {
            LOG(INFO) << "计算的移动距离 " << moveDistance << "mm 小于最小移动阈值 " << MIN_MOVE_DISTANCE << "mm，无需移动";
            return;
        }

        LOG(INFO) << "步骤11 - position_offset: ["
                  << position_offset[0] << ", " << position_offset[1] << ", " << position_offset[2] << "] (米)";

        // 新增：计算姿势偏移
        // 计算相对旋转矩阵 R_relative (从模板中的姿势旋转到当前姿势)
        // 原计算方式: R_target2cam * R_target2cam_initial.t()
        // 尝试反转计算方向:
        cv::Mat R_relative;
        if (invertRotationDirection) {
            // 反转方向: 从初始位置到当前位置
            R_relative = R_target2cam_initial * R_target2cam.t();
            LOG(INFO) << "计算相对旋转矩阵 R_relative (反转方向): \n"
                    << R_relative.at<double>(0,0) << " " << R_relative.at<double>(0,1) << " " << R_relative.at<double>(0,2) << "\n"
                    << R_relative.at<double>(1,0) << " " << R_relative.at<double>(1,1) << " " << R_relative.at<double>(1,2) << "\n"
                    << R_relative.at<double>(2,0) << " " << R_relative.at<double>(2,1) << " " << R_relative.at<double>(2,2);
        } else {
            // 原始方向: 从当前位置到初始位置
            R_relative = R_target2cam * R_target2cam_initial.t();
            LOG(INFO) << "计算相对旋转矩阵 R_relative (原始方向): \n"
                    << R_relative.at<double>(0,0) << " " << R_relative.at<double>(0,1) << " " << R_relative.at<double>(0,2) << "\n"
                    << R_relative.at<double>(1,0) << " " << R_relative.at<double>(1,1) << " " << R_relative.at<double>(1,2) << "\n"
                    << R_relative.at<double>(2,0) << " " << R_relative.at<double>(2,1) << " " << R_relative.at<double>(2,2);
        }

        // 将相对旋转转换为旋转向量
        cv::Mat rvec;
        cv::Rodrigues(R_relative, rvec);
        double rotAngle = cv::norm(rvec);
        LOG(INFO) << "旋转角度: " << rotAngle * 180.0 / M_PI << " 度";

        // 检查是否启用旋转调整
        bool applyRotation = enableRotationAdjustment;
        if (!applyRotation) {
            LOG(INFO) << "旋转调整功能当前已禁用，将不应用旋转变换";
        } else {
            LOG(INFO) << "旋转调整功能已启用，将应用旋转变换";
            
            // 安全检查：视角安全检查 - 如果视角已经接近侧视，不应用旋转
            double cameraViewAngle = 0.0;
            cv::Mat boardNormal = R_target2cam * (cv::Mat_<double>(3, 1) << 0, 0, 1);
            cv::Mat cameraDirection = (cv::Mat_<double>(3, 1) << 0, 0, 1);
            double cosAngle = boardNormal.dot(cameraDirection) /
                            (cv::norm(boardNormal) * cv::norm(cameraDirection));
            cameraViewAngle = acos(cosAngle) * 180.0 / M_PI;
            
            LOG(INFO) << "当前标记板与相机视角: " << cameraViewAngle << " 度";
            
            // 如果视角过大（接近限制角度），则不应用旋转以防止跟踪丢失
            const double MAX_VIEW_ANGLE = 55.0; // 度，减小为55度更安全
            if (cameraViewAngle > MAX_VIEW_ANGLE) {
                LOG(WARNING) << "当前视角 (" << cameraViewAngle << "°) 已接近临界值，不应用旋转以防止标记板离开视野";
                applyRotation = false;
            }
            else {
                // 安全检查：检测到的标记数量检查
                const int BOARD_TOTAL_MARKERS = 35; // 5x7的标记板共有35个标记
                int detectedMarkers = markerIds.size();
                double detectionRatio = static_cast<double>(detectedMarkers) / BOARD_TOTAL_MARKERS;
                
                LOG(INFO) << "检测到 " << detectedMarkers << " 个标记，占标记板总数的 " 
                         << (detectionRatio * 100.0) << "%";
                
                // 要求至少检测到60%的标记才允许旋转
                const double MIN_DETECTION_RATIO = 0.6;
                if (detectionRatio < MIN_DETECTION_RATIO) {
                    LOG(WARNING) << "检测到的标记比例 (" << (detectionRatio * 100.0) 
                               << "%) 不足，不应用旋转以防止跟踪丢失";
                    applyRotation = false;
                }
                else {
                    // 限制最大旋转角度（安全限制）- 减小为2.5度
                    const double MAX_ROTATION_ANGLE = 2.5 * M_PI / 180.0; // 2.5度最大旋转（比之前更小）
                    
                    // 应用渐进式旋转 - 根据检测到的标记比例调整旋转幅度
                    // 标记检测率越高，允许的旋转幅度越大
                    double adjustedMaxRotation = MAX_ROTATION_ANGLE * (detectionRatio / MIN_DETECTION_RATIO);
                    
                    if (rotAngle > adjustedMaxRotation) {
                        double scale = adjustedMaxRotation / rotAngle;
                        rvec = rvec * scale;
                        rotAngle = adjustedMaxRotation;
                        LOG(INFO) << "旋转角度过大，基于检测率(" << (detectionRatio * 100.0) 
                                 << "%)缩放至: " << rotAngle * 180.0 / M_PI << " 度";
                        // 重新计算旋转矩阵
                        cv::Rodrigues(rvec, R_relative);
                    }

                    // 检查旋转角度是否太小，若过小则不进行旋转
                    const double MIN_ROTATION_ANGLE = 0.3 * M_PI / 180.0; // 0.3度最小旋转（更小的阈值）
                    if (rotAngle < MIN_ROTATION_ANGLE) {
                        applyRotation = false;
                        LOG(INFO) << "计算的旋转角度 " << rotAngle * 180.0 / M_PI << " 度小于最小旋转阈值 "
                                << MIN_ROTATION_ANGLE * 180.0 / M_PI << " 度，无需应用旋转";
                    }
                }
            }
        }

        // 将相对旋转从相机坐标系转换到机器人基坐标系
        cv::Mat R_base_relative;
        if (applyRotation) {
            // 在相机坐标系中：R_camera_target_relative = R_target2cam_initial * R_target2cam.t()
            // 转换到机器人基坐标系：R_base_relative = R_base_camera * R_camera_target_relative * R_base_camera.t()
            R_base_relative = R_base_camera * R_relative * R_base_camera.t();
            
            // 输出R_base_relative用于调试
            LOG(INFO) << "机器人基坐标系中的相对旋转矩阵: \n"
                    << R_base_relative.at<double>(0,0) << " " << R_base_relative.at<double>(0,1) << " " << R_base_relative.at<double>(0,2) << "\n"
                    << R_base_relative.at<double>(1,0) << " " << R_base_relative.at<double>(1,1) << " " << R_base_relative.at<double>(1,2) << "\n"
                    << R_base_relative.at<double>(2,0) << " " << R_base_relative.at<double>(2,1) << " " << R_base_relative.at<double>(2,2);
        }

        // 13. 计算目标位姿 (修改位置和姿态)
        rw::math::Vector3D<> target_position = T_base_gripper.P() + position_offset;
        rw::math::Rotation3D<> currentRotation = T_base_gripper.R();
        rw::math::Rotation3D<> targetRotation;

        if (applyRotation) {
            // 应用相对旋转到当前旋转上
            // 原代码: targetRotation = currentRotation * relativeRotation;
            // 尝试改为:
            rw::math::Rotation3D<> relativeRotation(
                R_base_relative.at<double>(0,0), R_base_relative.at<double>(0,1), R_base_relative.at<double>(0,2),
                R_base_relative.at<double>(1,0), R_base_relative.at<double>(1,1), R_base_relative.at<double>(1,2),
                R_base_relative.at<double>(2,0), R_base_relative.at<double>(2,1), R_base_relative.at<double>(2,2)
            );

            // 尝试使用逆旋转
            rw::math::Rotation3D<> inverseRelativeRotation = relativeRotation.inverse();
            targetRotation = currentRotation * inverseRelativeRotation; // 使用逆旋转

            // 输出旋转前后的欧拉角，用于调试
            rw::math::RPY<> currentRPY(currentRotation);
            rw::math::RPY<> targetRPY(targetRotation);
            LOG(INFO) << "当前欧拉角 (RPY): ["
                    << currentRPY[0] * 180.0 / M_PI << ", "
                    << currentRPY[1] * 180.0 / M_PI << ", "
                    << currentRPY[2] * 180.0 / M_PI << "] 度";
            LOG(INFO) << "目标欧拉角 (RPY): ["
                    << targetRPY[0] * 180.0 / M_PI << ", "
                    << targetRPY[1] * 180.0 / M_PI << ", "
                    << targetRPY[2] * 180.0 / M_PI << "] 度";
            LOG(INFO) << "应用旋转调整，旋转角度: " << rotAngle * 180.0 / M_PI << " 度 (反向应用)";
        } else {
            // 保持当前旋转不变
            targetRotation = currentRotation;
            LOG(INFO) << "旋转角度过小，保持当前旋转不变";
        }

        rw::math::Transform3D<> T_base_target(target_position, targetRotation);
        LOG(INFO) << "步骤13 - 计算的目标位置: ["
                  << target_position[0] << ", "
                  << target_position[1] << ", "
                  << target_position[2] << "] (米)";

        // 14. 应用Kalman滤波
        cv::Point3f rawPosition(position_offset[0], position_offset[1], position_offset[2]);
        // 临时屏蔽Kalman滤波器，直接使用原始值
        positionFilter.update(rawPosition); // 仍然调用update以保持状态一致
        cv::Point3f filteredPosition = rawPosition; // 直接使用原始值

        // 新增：对旋转偏移进行处理（如果有旋转的话）
        cv::Point3f rawRotation(0, 0, 0);
        cv::Point3f filteredRotation(0, 0, 0);
        if (applyRotation) {
            // 将旋转向量转换为三维点用于滤波
            rawRotation.x = rvec.at<double>(0);
            rawRotation.y = rvec.at<double>(1);
            rawRotation.z = rvec.at<double>(2);
            
            // 临时屏蔽旋转Kalman滤波，直接使用原始值
            rotationFilter.update(rawRotation); // 仍然调用update以保持状态一致
            filteredRotation = rawRotation; // 直接使用原始值
        }

        LOG(INFO) << "步骤14 - Kalman滤波已暂时屏蔽";
        LOG(INFO) << "步骤14 - 使用原始位置偏移: ["
                  << rawPosition.x << ", " << rawPosition.y << ", " << rawPosition.z << "] (米)";
        LOG(INFO) << "步骤14 - 使用原始旋转向量: ["
                  << rawRotation.x << ", " << rawRotation.y << ", " << rawRotation.z << "] (弧度)";

        // 15. 构建目标位姿
        rw::math::Vector3D<> filteredPositionVector(
                T_base_gripper.P()[0] + filteredPosition.x,
                T_base_gripper.P()[1] + filteredPosition.y,
                T_base_gripper.P()[2] + filteredPosition.z
        );

        // 使用前面计算好的targetRotation（已经包含了是否应用旋转的逻辑）
        rw::math::Transform3D<> filteredRobotPose(
                filteredPositionVector,
                targetRotation  // 使用包含旋转调整的目标旋转
        );

        LOG(INFO) << "步骤15 - 最终过滤后的目标位置: ["
                  << filteredPositionVector[0] << ", "
                  << filteredPositionVector[1] << ", "
                  << filteredPositionVector[2] << "] (米)";
        if (applyRotation) {
            LOG(INFO) << "步骤15 - 应用了旋转调整，旋转角度: " << rotAngle * 180.0 / M_PI << " 度";
        } else {
            LOG(INFO) << "步骤15 - 未应用旋转调整";
        }

        // 计算实际的移动距离（使用原始位置偏移，而不是过滤后的）
         moveDistance = position_offset.norm2() * 1000; // 转换为毫米
        LOG(INFO) << "机器人移动: 距离=" << moveDistance << "mm";
        if (applyRotation) {
            LOG(INFO) << "机器人旋转: 角度=" << rotAngle * 180.0 / M_PI << " 度";
        }

        // 计算IK并检查安全约束
        rw::math::Q currentJointQ = robot->getRobotJointQ();
        rw::math::Q newJointQ;

        if (robot->getIK(currentJointQ, filteredRobotPose, newJointQ)) {
            // 检查安全阈值
            rw::math::Q jointChange = newJointQ - currentJointQ;
            const double safetyThreshold = 15.0 * M_PI / 180.0; // 降低为15度
            bool isSafeMovement = true;

            for (size_t i = 0; i < jointChange.size(); i++) {
                if (std::abs(jointChange[i]) > safetyThreshold) {
                    LOG(WARNING) << "Joint " << i + 1 << " change too large: "
                                 << jointChange[i] * 180.0 / M_PI << " deg, exceeding safety threshold "
                                 << safetyThreshold * 180.0 / M_PI << " deg";
                    isSafeMovement = false;
                    break;
                }
            }

            if (isSafeMovement) {
                // 执行机器人移动
                robot->movej(newJointQ, 0.5, 0, 0.2);

                // 更新参考位姿为过滤后的机器人位姿，用于下一步移动计算
                referenceRobotPose = filteredRobotPose;
            } else {
                LOG(ERROR) << "Movement exceeds safety limits, robot movement aborted";
            }
        } else {
            LOG(ERROR) << "Inverse kinematics failed, cannot move robot";
        }
    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception in updateRobotPosition: " << e.what();
    } catch (...) {
        LOG(ERROR) << "Unknown exception in updateRobotPosition";
    }
}

bool TrackingManager::saveTemplate(const std::string& name)
{
    if (!initialPoseSet) {
        LOG(ERROR) << "Cannot save template: initial pose not set";
        return false;
    }

    // 检查当前是否有有效的ArUco标志板检测结果
    if (R_target2cam.empty() || t_target2cam.empty()) {
        // 如果当前没有有效的检测结果，但初始位姿已设置，可以使用初始位姿数据
        if (!R_target2cam_initial.empty() && !t_target2cam_initial.empty()) {
            LOG(WARNING) << "当前没有有效的ArUco标志板检测结果，使用初始位姿数据保存模板";
            // 使用初始位姿数据更新当前检测结果变量
            R_target2cam = R_target2cam_initial.clone();
            t_target2cam = t_target2cam_initial.clone();
        } else {
            LOG(ERROR) << "当前没有有效的ArUco标志板检测结果，无法保存模板";
            return false;
        }
    }

    std::string filename = "tracking_template_" + name + ".yml";
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to create template file: " << filename;
        return false;
    }

    // 使用当前机器人位姿作为模板位姿
    rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();

    fs << "template_name" << name;
    fs << "creation_time" << getCurrentTimeString();

    // 保存ArUco标志板在相机坐标系中的位姿（关键数据）
    // 这些数据将用于后续的跟踪比对
    fs << "R_target2cam" << R_target2cam_initial;
    fs << "t_target2cam" << t_target2cam_initial;

    // 为验证目的，也保存当前的检测结果
    fs << "R_target2cam_current" << R_target2cam;
    fs << "t_target2cam_current" << t_target2cam;

    // 保存当前机器人位姿
    cv::Mat R_robot = cv::Mat::eye(3, 3, CV_64F);
    cv::Mat t_robot = cv::Mat(3, 1, CV_64F);
    transform3DToRt(currentRobotPose, R_robot, t_robot);

    fs << "R_robot" << R_robot;
    fs << "t_robot" << t_robot;

    fs.release();

    // 更新内存中模板
    TrackingTemplate tmpl;
    tmpl.R_target2cam = R_target2cam_initial.clone();
    tmpl.t_target2cam = t_target2cam_initial.clone();
    tmpl.robotPose = currentRobotPose;
    tmpl.isValid = true;
    templates[name] = tmpl;

    // 更新模板位姿和当前使用的模板名称
    templateRobotPose = currentRobotPose;
    currentTemplate = name;

    LOG(INFO) << "Successfully saved tracking template: " << name;
    LOG(INFO) << "模板中保存了ArUco标志板在相机坐标系中的位姿，将用于跟踪比对";
    return true;
}

bool TrackingManager::loadTemplate(const std::string& name)
{
    LOG(INFO) << "加载模板: " << name;

    // Check if template exists in memory
    if (templates.find(name) != templates.end() && templates[name].isValid) {
        LOG(INFO) << "从内存中加载模板: " << name;
        currentTemplate = name;

        // Reset initial pose to force reinitialization with the template
        initialPoseSet = false;
        return true;
    }

    // Otherwise load from file
    std::string filename = "tracking_template_" + name + ".yml";
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "无法打开模板文件: " << filename;
        return false;
    }

    TrackingTemplate tmpl;

    // 加载ArUco标志板在相机坐标系中的位姿（关键数据）
    // 这是模板存储的主要数据，将用于后续的跟踪比对
    fs["R_target2cam"] >> tmpl.R_target2cam;
    fs["t_target2cam"] >> tmpl.t_target2cam;

    if (tmpl.R_target2cam.empty() || tmpl.t_target2cam.empty()) {
        LOG(ERROR) << "模板文件中缺少ArUco标志板位姿数据";
        fs.release();
        return false;
    }

    LOG(INFO) << "已加载ArUco标志板在相机坐标系中的位姿数据";

    // 加载机器人位姿数据
    cv::Mat R_robot, t_robot;
    fs["R_robot"] >> R_robot;
    fs["t_robot"] >> t_robot;

    if (R_robot.empty() || t_robot.empty()) {
        LOG(WARNING) << "模板中缺少机器人位姿数据，但可以继续使用";
    }

    // Convert to Transform3D
    tmpl.robotPose = rtToTransform3D(R_robot, t_robot);
    tmpl.isValid = true;

    // Save to memory
    templates[name] = tmpl;
    currentTemplate = name;

    // Reset initial pose to force reinitialization with the template
    initialPoseSet = false;

    fs.release();
    LOG(INFO) << "已成功加载模板: " << name;
    LOG(INFO) << "跟踪时将使用模板中保存的ArUco标志板位姿作为参考";
    return true;
}

std::vector<std::string> TrackingManager::getAvailableTemplates()
{
    std::vector<std::string> result;

    // Add in-memory templates
    for (const auto& pair : templates) {
        if (pair.second.isValid) {
            result.push_back(pair.first);
        }
    }

    // Scan directory for template files
    WIN32_FIND_DATA findFileData;
    HANDLE hFind = FindFirstFile("tracking_template_*.yml", &findFileData);
    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            std::string filename = findFileData.cFileName;
            std::string templateName = filename.substr(18, filename.length() - 22); // Remove "tracking_template_" and ".yml"

            // Add if not already in the list
            if (std::find(result.begin(), result.end(), templateName) == result.end()) {
                // Try to load it to verify
                cv::FileStorage fs(filename, cv::FileStorage::READ);
                if (fs.isOpened()) {
                    result.push_back(templateName);
                    fs.release();
                }
            }
        } while (FindNextFile(hFind, &findFileData) != 0);
        FindClose(hFind);
    }

    return result;
}

std::string TrackingManager::getCurrentTemplate() const
{
    return currentTemplate;
}

void TrackingManager::setCurrentTemplate(const std::string& name)
{
    currentTemplate = name;
}

bool TrackingManager::toggleTracking()
{
    trackingEnabled = !trackingEnabled;

    if (trackingEnabled) {
        dynamicMaxMoveDistance=10.0;
        initPIDController();
        // 清空队列并释放内存
        std::deque<double>().swap(recentMoveDistances);
        // Reset tracking state when enabling
        initialPoseSet = false;
        positionFilter.reset();
        rotationFilter.reset();
    }
    // Ensure reference positions are completely reset
    if (robot) {
        rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
        referenceRobotPose = currentRobotPose;
        initialRobotPose = currentRobotPose;
    }
    return trackingEnabled;
}

bool TrackingManager::isTracking() const
{
    return trackingEnabled;
}

void TrackingManager::setTracking(bool enabled)
{
    trackingEnabled = enabled;

    if (trackingEnabled) {
        dynamicMaxMoveDistance=10.0;
        initPIDController();
        // 清空队列并释放内存
        std::deque<double>().swap(recentMoveDistances);
        // Reset tracking state when enabling
        initialPoseSet = false;
        positionFilter.reset();
        rotationFilter.reset();
    }
    // Ensure reference positions are completely reset
    if (robot) {
        rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
        referenceRobotPose = currentRobotPose;
        initialRobotPose = currentRobotPose;
    }
}

void TrackingManager::setMoveThreshold(double threshold)
{
    moveThreshold = threshold;
}

void TrackingManager::setRotThreshold(double threshold)
{
    rotThreshold = threshold;
}

double TrackingManager::getMoveThreshold() const
{
    return moveThreshold;
}

double TrackingManager::getRotThreshold() const
{
    return rotThreshold;
}

bool TrackingManager::loadCameraCalibration(const std::string& filename)
{
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Cannot open camera calibration file: " << filename;
        return false;
    }

    fs["camera_matrix"] >> cameraMatrix;
    fs["distortion_coefficients"] >> distCoeffs;
    fs.release();

    return !cameraMatrix.empty() && !distCoeffs.empty();
}

bool TrackingManager::loadHandEyeCalibration(const std::string& filename)
{
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Cannot open hand-eye calibration file: " << filename;
        return false;
    }

    fs["rotation_matrix"] >> R_cam2gripper;
    fs["translation_vector"] >> t_cam2gripper;
    fs.release();

    return !R_cam2gripper.empty() && !t_cam2gripper.empty();
}

rw::math::Transform3D<> TrackingManager::rtToTransform3D(const cv::Mat& R, const cv::Mat& t) const
{
    rw::math::Rotation3D<> rotation(
            R.at<double>(0,0), R.at<double>(0,1), R.at<double>(0,2),
            R.at<double>(1,0), R.at<double>(1,1), R.at<double>(1,2),
            R.at<double>(2,0), R.at<double>(2,1), R.at<double>(2,2)
    );

    rw::math::Vector3D<> position(
            t.at<double>(0,0),
            t.at<double>(1,0),
            t.at<double>(2,0)
    );

    return rw::math::Transform3D<>(position, rotation);
}

void TrackingManager::transform3DToRt(const rw::math::Transform3D<>& transform, cv::Mat& R, cv::Mat& t) const
{
    // Extract rotation matrix
    R = cv::Mat::eye(3, 3, CV_64F);
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            R.at<double>(i,j) = transform.R()(i,j);
        }
    }

    // Extract translation vector
    t = cv::Mat(3, 1, CV_64F);
    t.at<double>(0,0) = transform.P()[0];
    t.at<double>(1,0) = transform.P()[1];
    t.at<double>(2,0) = transform.P()[2];
}

// Robot control methods for path management
rw::math::Transform3D<> TrackingManager::getRobotPose() const
{
    if (robot) {
        return robot->getRobotActualTCP();
    }
    return rw::math::Transform3D<>();
}

rw::math::Q TrackingManager::getRobotJointConfig() const
{
    if (robot) {
        return robot->getRobotJointQ();
    }
    return rw::math::Q(6, 0.0);
}

bool TrackingManager::moveRobotJoint(const rw::math::Q& jointConfig, double speed, double acceleration)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    try {
        // 设置机器人正在运动标志
        isRobotMoving = true;

        bool result = robot->movej(jointConfig, speed, 0.0, acceleration);

        // 运动完成，清除标志
        isRobotMoving = false;

        return result;
    }
    catch (const std::exception& e) {
        // 出错时也要清除标志
        isRobotMoving = false;
        LOG(ERROR) << "Error moving robot: " << e.what();
        return false;
    }
}

bool TrackingManager::moveRobotLinear(const rw::math::Transform3D<>& pose, double speed, double acceleration)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    try {
        // 设置机器人正在运动标志
        isRobotMoving = true;

        // 获取当前关节配置作为逆运动学的种子
        rw::math::Q currentQ = robot->getRobotJointQ();
        LOG(INFO) << "Using joint movement instead of linear movement"<<currentQ;
        LOG(INFO) << "Using joint movement instead of linear movement"<<pose.P();
        // 计算到达目标位姿的关节配置
        rw::math::Q targetQ;
        if (robot->getIK(currentQ, pose, targetQ)) {
            // 使用关节移动代替直线移动
            LOG(INFO) << "Using joint movement instead of linear movement";
            bool result = robot->movej(targetQ, speed, 0.0, acceleration);

            // 运动完成，清除标志
            isRobotMoving = false;

            return result;
        } else {
            // 逆运动学失败，清除标志
            isRobotMoving = false;
            LOG(ERROR) << "Failed to compute inverse kinematics for target pose";
            return false;
        }
    }
    catch (const std::exception& e) {
        // 出错时也要清除标志
        isRobotMoving = false;
        LOG(ERROR) << "Error moving robot: " << e.what();
        return false;
    }
}

bool TrackingManager::setRobotIO(int port, bool state)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    try {
        robot->setOutValue(port, state);
        return true;
    }
    catch (const std::exception& e) {
        LOG(ERROR) << "Error setting robot IO: " << e.what();
        return false;
    }
}

bool TrackingManager::getIKForPose(const rw::math::Transform3D<>& pose, rw::math::Q& jointConfig)
{
    if (!robot) {
        LOG(ERROR) << "Robot not initialized";
        return false;
    }

    // Get current joint configuration as seed for IK
    rw::math::Q currentQ = robot->getRobotJointQ();

    // Calculate IK solution
    return robot->getIK(currentQ, pose, jointConfig);
}

rw::math::Transform3D<> TrackingManager::getTemplateDifference() const
{
    try {
        // 获取当前模板的数据
        auto it = templates.find(currentTemplate);
        if (it == templates.end() || !it->second.isValid) {
            // 如果没有找到有效的模板，返回单位变换
            return rw::math::Transform3D<>();
        }

        // 获取当前标记板位姿
        if (R_target2cam.empty() || t_target2cam.empty() ||
            it->second.R_target2cam.empty() || it->second.t_target2cam.empty()) {
            // 缺少必要数据
            LOG(WARNING) << "缺少计算位姿差异所需的数据";
            return rw::math::Transform3D<>();
        }

        // 计算位置偏差
        double dx = t_target2cam.at<double>(0) - it->second.t_target2cam.at<double>(0);
        double dy = t_target2cam.at<double>(1) - it->second.t_target2cam.at<double>(1);
        double dz = t_target2cam.at<double>(2) - it->second.t_target2cam.at<double>(2);

        LOG(INFO) << "getTemplateDifference - 标记板位置偏差 dx=" << dx << ", dy=" << dy << ", dz=" << dz << " (米)";

        // 相机坐标系中的位置偏差向量
        cv::Mat camera_offset = (cv::Mat_<double>(3,1) << -dx, -dy, -dz);

        LOG(INFO) << "getTemplateDifference - 相机坐标系中的位置偏差: ["
                  << camera_offset.at<double>(0) << ", "
                  << camera_offset.at<double>(1) << ", "
                  << camera_offset.at<double>(2) << "] (米)";

        // 获取当前机器人位姿
        rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();

        // 手爪到相机的变换
        rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);

        // 基坐标系到相机的变换
        rw::math::Transform3D<> T_base_camera = T_base_gripper * T_gripper_camera;

        // 将相机坐标系中的偏差向量转换到基坐标系
        cv::Mat R_base_camera;
        cv::Mat t_dummy; // 创建一个临时变量来接收平移向量，虽然我们不需要它
        transform3DToRt(T_base_camera, R_base_camera, t_dummy);
        cv::Mat base_offset = R_base_camera * camera_offset;

        LOG(INFO) << "getTemplateDifference - 基坐标系中的位置偏差: ["
                  << base_offset.at<double>(0) << ", "
                  << base_offset.at<double>(1) << ", "
                  << base_offset.at<double>(2) << "] (米)";

        // 构建位置偏移
        rw::math::Vector3D<> position_offset(
                base_offset.at<double>(0),
                base_offset.at<double>(1),
                base_offset.at<double>(2)
        );

        // 计算移动距离但不进行缩放限制
        double moveDistance = position_offset.norm2() * 1000.0;
        LOG(INFO) << "getTemplateDifference - 计算的移动距离: " << moveDistance << "mm";

        // 创建只有位置变化的变换
        return rw::math::Transform3D<>(position_offset, rw::math::Rotation3D<>::identity());
    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception in getTemplateDifference: " << e.what();
        return rw::math::Transform3D<>();
    }
}

rw::math::Transform3D<> TrackingManager::getTemplateRobotPose() const
{
    auto it = templates.find(currentTemplate);
    if (it != templates.end() && it->second.isValid) {
        return it->second.robotPose;
    }
    return rw::math::Transform3D<>();
}

rw::math::Transform3D<> TrackingManager::getCurrentBoardPose() const
{
    // 检查是否有有效的检测结果
    if (R_target2cam.empty() || t_target2cam.empty()) {
        LOG(WARNING) << "No valid board detection available";
        return rw::math::Transform3D<>();
    }

    // 将OpenCV的旋转矩阵和平移向量转换为Transform3D
    return rtToTransform3D(R_target2cam, t_target2cam);
}

rw::math::Transform3D<> TrackingManager::getTemplateBoardPose() const
{
    // 检查是否有有效的模板
    if (!initialPoseSet || R_target2cam_initial.empty() || t_target2cam_initial.empty()) {
        LOG(WARNING) << "No valid template board pose available";
        return rw::math::Transform3D<>();
    }

    // 将保存的模板旋转矩阵和平移向量转换为Transform3D
    return rtToTransform3D(R_target2cam_initial, t_target2cam_initial);
}

rw::math::Transform3D<> TrackingManager::getCameraToGripperTransform() const
{
    // 手眼标定得到的是相机相对于机器人手爪的位姿
    // 我们需要的是手爪相对于相机的位姿，所以需要取逆
    if (R_cam2gripper.empty() || t_cam2gripper.empty()) {
        LOG(WARNING) << "Hand-eye calibration data not available";
        return rw::math::Transform3D<>();
    }

    // 将OpenCV的旋转矩阵和平移向量转换为Transform3D
    return rtToTransform3D(R_cam2gripper, t_cam2gripper);
}

// 新增方法：获取相机内参矩阵
cv::Mat TrackingManager::getCameraMatrix() const
{
    return cameraMatrix.clone(); // 返回副本以防止外部修改
}

// 新增方法：获取相机畸变系数
cv::Mat TrackingManager::getDistCoeffs() const
{
    return distCoeffs.clone(); // 返回副本以防止外部修改
}

// 新增方法：获取手眼标定旋转矩阵
cv::Mat TrackingManager::getRotationMatrix() const
{
    return R_cam2gripper.clone(); // 返回副本以防止外部修改
}

// 新增方法：获取手眼标定平移向量
cv::Mat TrackingManager::getTranslationVector() const
{
    return t_cam2gripper.clone(); // 返回副本以防止外部修改
}

// 新增方法：移动机器人到模板记录的位置
bool TrackingManager::moveToTemplatePosition(const std::string& templateName)
{
    LOG(INFO) << "尝试移动机器人到模板位置: " << templateName;

    try {
        // 检查机器人连接
        if (!robot || !robot->isConnect()) {
            LOG(ERROR) << "机器人未连接，无法移动到模板位置";
            return false;
        }

//        // 检查当前相机视野是否有标记板
//        if (R_target2cam.empty() || t_target2cam.empty()) {
//            LOG(ERROR) << "当前视野中没有检测到标记板，无法计算目标位置";
//            return false;
//        }

        // 检查模板是否存在
        TrackingTemplate tmpl;
        if (templates.find(templateName) != templates.end() && templates[templateName].isValid) {
            // 使用内存中的模板
            LOG(INFO) << "使用内存中的模板: " << templateName;
            tmpl = templates[templateName];
        } else {
            // 尝试从文件加载
            std::string filename = "tracking_template_" + templateName + ".yml";
            LOG(INFO) << "尝试从文件加载模板: " << filename;

            cv::FileStorage fs(filename, cv::FileStorage::READ);
            if (!fs.isOpened()) {
                LOG(ERROR) << "无法打开模板文件: " << filename;
                return false;
            }

            fs["R_target2cam"] >> tmpl.R_target2cam;
            fs["t_target2cam"] >> tmpl.t_target2cam;

            cv::Mat R_robot, t_robot;
            fs["R_robot"] >> R_robot;
            fs["t_robot"] >> t_robot;
            fs.release();

            if (tmpl.R_target2cam.empty() || tmpl.t_target2cam.empty() || R_robot.empty() || t_robot.empty()) {
                LOG(ERROR) << "模板文件中数据不完整";
                return false;
            }

            tmpl.robotPose = rtToTransform3D(R_robot, t_robot);
            tmpl.isValid = true;
        }

        // 简化版本：直接使用模板中记录的机器人位姿，不进行坐标变换计算
        if (!(std::abs(tmpl.robotPose.P()[0]) < 1e-10 &&
              std::abs(tmpl.robotPose.P()[1]) < 1e-10 &&
              std::abs(tmpl.robotPose.P()[2]) < 1e-10)) {
            LOG(INFO) << "使用模板中记录的机器人位姿直接移动";
            return moveRobotLinear(tmpl.robotPose, 0.1, 0.1);
        }

        // 如果模板中没有记录机器人位姿，则使用位置偏差法
        // 计算位置偏差
        double dx = t_target2cam.at<double>(0) - tmpl.t_target2cam.at<double>(0);
        double dy = t_target2cam.at<double>(1) - tmpl.t_target2cam.at<double>(1);
        double dz = t_target2cam.at<double>(2) - tmpl.t_target2cam.at<double>(2);

        // 相机坐标系中的位置偏差向量 - 注意这里需要取反，因为我们要移动到目标位置
        cv::Mat camera_offset = (cv::Mat_<double>(3,1) << dx, dy, dz);

        LOG(INFO) << "相机坐标系中的位置偏差: [" << camera_offset.at<double>(0) << ", " << camera_offset.at<double>(1) << ", " << camera_offset.at<double>(2) << "]";

        // 获取当前机器人位姿
        rw::math::Transform3D<> T_base_gripper = robot->getRobotActualTCP();
        LOG(INFO) << "当前机器人位置: ["
                  << T_base_gripper.P()[0] << ", "
                  << T_base_gripper.P()[1] << ", "
                  << T_base_gripper.P()[2] << "] (米)";

        // 手爪到相机的变换
        rw::math::Transform3D<> T_gripper_camera = rtToTransform3D(R_cam2gripper, t_cam2gripper);
        LOG(INFO) << "手眼标定平移向量: ["
                  << t_cam2gripper.at<double>(0) << ", "
                  << t_cam2gripper.at<double>(1) << ", "
                  << t_cam2gripper.at<double>(2) << "] (米)";

        // 基坐标系到相机的变换
        rw::math::Transform3D<> T_base_camera = T_base_gripper * T_gripper_camera;
        LOG(INFO) << "基坐标系到相机的位置: ["
                  << T_base_camera.P()[0] << ", "
                  << T_base_camera.P()[1] << ", "
                  << T_base_camera.P()[2] << "] (米)";

        // 将相机坐标系中的偏差向量转换到基坐标系
        cv::Mat R_base_camera;
        cv::Mat t_dummy; // 创建一个临时变量来接收平移向量，虽然我们不需要它
        transform3DToRt(T_base_camera, R_base_camera, t_dummy);
        cv::Mat base_offset = R_base_camera * camera_offset;

        LOG(INFO) << "旋转矩阵 R_base_camera: \n"
                  << R_base_camera.at<double>(0,0) << " " << R_base_camera.at<double>(0,1) << " " << R_base_camera.at<double>(0,2) << "\n"
                  << R_base_camera.at<double>(1,0) << " " << R_base_camera.at<double>(1,1) << " " << R_base_camera.at<double>(1,2) << "\n"
                  << R_base_camera.at<double>(2,0) << " " << R_base_camera.at<double>(2,1) << " " << R_base_camera.at<double>(2,2);

        LOG(INFO) << "基坐标系中的位置偏差: ["
                  << base_offset.at<double>(0) << ", "
                  << base_offset.at<double>(1) << ", "
                  << base_offset.at<double>(2) << "] (米)";

        // 构建位置偏移
        rw::math::Vector3D<> position_offset(
                base_offset.at<double>(0),
                base_offset.at<double>(1),
                base_offset.at<double>(2)
        );

        // 计算移动距离，但不进行限制
        double moveDistance = position_offset.norm2() * 1000.0;
        LOG(INFO) << "moveToTemplatePosition - position_offset.norm2()=" << position_offset.norm2() << " (米)";
        LOG(INFO) << "moveToTemplatePosition - 计算得到的移动距离: " << moveDistance << "mm";

        // 计算目标位姿 (保持当前姿态)
        rw::math::Vector3D<> target_position = T_base_gripper.P() + position_offset;
        rw::math::Transform3D<> T_base_target(target_position, T_base_gripper.R());

        // 安全检查
        if (std::isnan(position_offset[0]) || std::isnan(position_offset[1]) || std::isnan(position_offset[2])) {
            LOG(ERROR) << "计算的位置偏移包含NaN值";
            return false;
        }

        // 移动机器人到目标位姿
        LOG(INFO) << "移动机器人到计算的目标位置，移动距离: " << moveDistance << "mm";
        return moveRobotLinear(T_base_target, 0.1, 0.1);
    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception in moveToTemplatePosition: " << e.what();
        return false;
    }
}

// 新增方法：重新加载标定参数
bool TrackingManager::reloadCalibration()
{
    // 重新加载相机标定参数
    bool cameraCalibLoaded = loadCameraCalibration("camera_calibration.yml");

    // 重新加载手眼标定参数
    bool handEyeCalibLoaded = loadHandEyeCalibration("hand_eye_calibration.yml");

    LOG(INFO) << "重新加载标定参数：相机标定" << (cameraCalibLoaded ? "成功" : "失败")
              << "，手眼标定" << (handEyeCalibLoaded ? "成功" : "失败");

    return cameraCalibLoaded && handEyeCalibLoaded;
}

// 新增方法：直接获取当前位姿数据进行初始化，用于创建模板
bool TrackingManager::initializeCurrentPose()
{
    LOG(INFO) << "直接初始化当前位姿数据";

    // 使用最新的frame，不单独拍照
    if (frame.empty()) {
        LOG(ERROR) << "当前没有可用图像，无法初始化位姿";
        return false;
    }

    // 检测ArUco标记
    std::vector<int> markerIds;
    std::vector<std::vector<cv::Point2f>> markerCorners;
    bool markersDetected = detectArucoBoard(frame, markerIds, markerCorners);

    if (!markersDetected) {
        LOG(ERROR) << "未检测到ArUco标记，无法初始化位姿";
        return false;
    }

    // 估计标记板姿态
    cv::Mat rvec, tvec;
    if (!estimateBoardPose(markerCorners, markerIds, rvec, tvec)) {
        LOG(ERROR) << "无法估计标记板姿态";
        return false;
    }

    // 转换旋转向量为矩阵
    cv::Mat R_target2cam_new;
    cv::Rodrigues(rvec, R_target2cam_new);

    // 存储当前标记板姿态作为初始姿态
    R_target2cam_initial = R_target2cam_new.clone();
    t_target2cam_initial = tvec.clone();

    // 重要：同时更新全局的当前检测结果变量，确保 saveTemplate 可以使用
    this->R_target2cam = R_target2cam_new.clone();
    this->t_target2cam = tvec.clone();

    // 获取并存储当前机器人位姿
    rw::math::Transform3D<> currentRobotPose = robot->getRobotActualTCP();
    initialRobotPose = currentRobotPose;
    referenceRobotPose = currentRobotPose;
    templateRobotPose = currentRobotPose;

    // 设置初始位姿标志
    initialPoseSet = true;

    LOG(INFO) << "成功初始化当前位姿数据";
    return true;
}

// 新增方法：只获取相机图像，更新全局变量frame
bool TrackingManager::captureFrame()
{
    // 从相机获取新帧并更新全局变量frame
    if (camera) {
        frame = camera->getFrame();
        if (frame.empty()) {
            LOG(ERROR) << "无法获取相机图像";
            return false;
        }

        // 添加时间戳
        frameTimestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count() / 1000.0;

        return true;
    }

    LOG(ERROR) << "相机未初始化";
    return false;
}

// 新增方法：只获取相机预览帧，不进行标定板识别或跟踪处理
cv::Mat TrackingManager::getPreviewFrame()
{
    // 检查是否有有效帧
    if (frame.empty()) {
        return frame;
    }

    // 创建预览帧（可以添加一些基本信息，如时间戳等）
    cv::Mat previewFrame = frame.clone();

    // 尝试检测ArUco标记但不进行跟踪处理
    std::vector<int> markerIds;
    std::vector<std::vector<cv::Point2f>> markerCorners;
    bool markersDetected = detectArucoBoard(frame, markerIds, markerCorners);

    if (markersDetected) {
        // 在预览帧上绘制ArUco标记
        cv::aruco::drawDetectedMarkers(previewFrame, markerCorners, markerIds);

        // 尝试估计姿态并绘制坐标轴
        cv::Mat rvec, tvec;
        if (estimateBoardPose(markerCorners, markerIds, rvec, tvec)) {
            cv::drawFrameAxes(previewFrame, cameraMatrix, distCoeffs, rvec, tvec, 0.05);
        }
    }

    // 在预览帧上添加基本信息
    int fontFace = cv::FONT_HERSHEY_SIMPLEX;
    double fontScale = 0.5;
    int thickness = 1;
    cv::Scalar textColor(0, 255, 0); // 绿色

    // 添加时间戳
    std::string timestampText = "time: " + std::to_string(frameTimestamp);
    cv::putText(previewFrame, timestampText, cv::Point(10, 20), fontFace, fontScale, textColor, thickness);

    // 添加模板信息
    std::string templateText = "template: " + currentTemplate;
    cv::putText(previewFrame, templateText, cv::Point(10, 40), fontFace, fontScale, textColor, thickness);

    // 添加跟踪状态
    std::string trackingText = trackingEnabled ? "跟踪: 开启" : "跟踪: 关闭";
    cv::putText(previewFrame, trackingText, cv::Point(10, 60), fontFace, fontScale, textColor, thickness);

    // 添加机器人运动状态
    if (isRobotMoving) {
        std::string robotText = "robot: running";
        cv::putText(previewFrame, robotText, cv::Point(10, 80), fontFace, fontScale, cv::Scalar(0, 0, 255), thickness);
    } else {
        std::string robotText = "robot: stop";
        cv::putText(previewFrame, robotText, cv::Point(10, 80), fontFace, fontScale, textColor, thickness);
    }

    // 添加ArUco检测信息
    std::string arucoText = markersDetected ?
                            "ArUco: 检测到 " + std::to_string(markerIds.size()) + " 个标记" :
                            "ArUco: 未检测到标记";
    cv::putText(previewFrame, arucoText, cv::Point(10, 100), fontFace, fontScale,
                markersDetected ? textColor : cv::Scalar(0, 0, 255), thickness);

    return previewFrame;
}

// 新增方法：配置ArUco参数
bool TrackingManager::configureArUcoBoard(int markersX, int markersY, float markerLength, float markerSeparation)
{
    LOG(INFO) << "配置ArUco参数: markersX=" << markersX << ", markersY=" << markersY
              << ", markerLength=" << markerLength << ", markerSeparation=" << markerSeparation;

    try {
        // 重新创建ArUco板
        board = cv::Ptr<cv::aruco::GridBoard>(new cv::aruco::GridBoard({markersX, markersY}, markerLength, markerSeparation, *dictionary));

        // 重置初始位姿，强制重新初始化
        initialPoseSet = false;

        // 重置过滤器
        positionFilter.reset();
        rotationFilter.reset();

        return true;
    } catch (const std::exception& e) {
        LOG(ERROR) << "配置ArUco参数出错: " << e.what();
        return false;
    }
}

void TrackingManager::warmupKalmanFilter()
{
    // 预热Kalman滤波器的方法，通过插入重复点使其快速收敛
    cv::Point3f zeroPoint(0, 0, 0);

    // 插入10个零点，帮助滤波器快速收敛到稳定状态
    for (int i = 0; i < 10; i++) {
        positionFilter.update(zeroPoint);
        rotationFilter.update(zeroPoint);
    }

    LOG(INFO) << "已预热Kalman滤波器";
    LOG(INFO) << "注意：Kalman滤波器当前已被临时屏蔽，实际使用的是原始值";
}

double TrackingManager::getCurrentTrackingError() const
{
    // 检查是否有有效的检测结果和初始位姿
    if (R_target2cam.empty() || t_target2cam.empty() ||
        R_target2cam_initial.empty() || t_target2cam_initial.empty()) {
        return 1000.0; // 返回一个很大的值表示无法计算误差
    }

    // 计算相对变换
    cv::Mat R_relative = R_target2cam * R_target2cam_initial.t();
    cv::Mat t_relative = t_target2cam - R_relative * t_target2cam_initial;

    // 计算平移和旋转误差
    double translationError = cv::norm(t_relative);
    double trace = R_relative.at<double>(0, 0) + R_relative.at<double>(1, 1) + R_relative.at<double>(2, 2);
    double rotationError = acos((trace - 1.0) / 2.0);

    // 返回综合误差
    return translationError + rotationError * 10.0; // 给旋转误差较大的权重
}

bool TrackingManager::executeTracking(int iterations, double errorThreshold)
{
    if (!trackingEnabled) {
        LOG(WARNING) << "跟踪未启用，无法执行跟踪";
        return false;
    }

    if (!camera || !robot) {
        LOG(ERROR) << "相机或机器人未初始化，无法执行跟踪";
        return false;
    }

    LOG(INFO) << "开始执行固定 " << iterations << " 次跟踪，错误阈值为 " << errorThreshold;

    // 保存开始时的错误值用于比较
    double initialError = 1000.0;
    double currentError = 1000.0;
    int successCount = 0;

    for (int i = 0; i < iterations; i++) {
        LOG(INFO) << "执行跟踪迭代 " << (i+1) << "/" << iterations;

        // 1. 捕获新帧
        if (!captureFrame()) {
            LOG(ERROR) << "无法获取相机图像";
            continue;
        }

        // 2. 处理当前帧
        TrackingStatus status;
        processFrame(status);

        // 如果是第一帧，记录初始误差
        if (i == 0) {
            initialError = getCurrentTrackingError();
        }

        // 3. 计算当前误差
        currentError = getCurrentTrackingError();
        LOG(INFO) << "当前跟踪误差: " << currentError;

        // 4. 如果误差小于阈值，提前完成
        if (currentError < errorThreshold) {
            LOG(INFO) << "跟踪误差已达到目标阈值，提前完成";
            successCount++;

            // 要求连续3次达到阈值才算真正成功
            if (successCount >= 3) {
                LOG(INFO) << "连续3次跟踪误差达到阈值，跟踪成功完成";
                return true;
            }
        } else {
            successCount = 0;
        }

        // 短暂延时，避免帧率过高
        std::this_thread::sleep_for(std::chrono::milliseconds(30));
    }

    // 比较最终误差与初始误差，检查是否有改善
    bool improved = currentError < initialError * 0.8; // 要求至少改善20%

    if (improved) {
        LOG(INFO) << "跟踪完成，误差从 " << initialError << " 降低到 " << currentError;
    } else {
        LOG(WARNING) << "跟踪完成，但误差改善不明显: " << initialError << " -> " << currentError;
    }

    return improved;
}

bool TrackingManager::executeTrackingUntilConverged(int maxIterations, double errorThreshold)
{
    if (!trackingEnabled) {
        LOG(WARNING) << "跟踪未启用，无法执行跟踪";
        return false;
    }

    LOG(INFO) << "开始执行跟踪直到收敛，最大迭代次数: " << maxIterations << "，错误阈值: " << errorThreshold;

    double prevError = 1000.0;
    int stableCount = 0;
    const int requiredStableFrames = 3; // 需要连续稳定的帧数

    for (int i = 0; i < maxIterations; i++) {
        // 1. 捕获新帧
        if (!captureFrame()) {
            LOG(ERROR) << "无法获取相机图像";
            continue;
        }

        // 2. 处理当前帧
        TrackingStatus status;
        processFrame(status);

        // 3. 计算当前误差
        double currentError = getCurrentTrackingError();
        LOG(INFO) << "迭代 " << (i+1) << "/" << maxIterations << "，当前误差: " << currentError;

        // 4. 检查是否稳定
        bool isStable = false;

        // 如果误差低于阈值或者变化很小，认为稳定
        if (currentError < errorThreshold) {
            isStable = true;
            LOG(INFO) << "误差已达到目标阈值";
        } else if (i > 0 && std::abs(currentError - prevError) < errorThreshold * 0.1) {
            isStable = true;
            LOG(INFO) << "误差变化很小，认为稳定";
        }

        if (isStable) {
            stableCount++;
            if (stableCount >= requiredStableFrames) {
                LOG(INFO) << "跟踪已收敛，连续 " << requiredStableFrames << " 帧稳定";
                return true;
            }
        } else {
            stableCount = 0;
        }

        prevError = currentError;

        // 短暂延时，避免帧率过高
        std::this_thread::sleep_for(std::chrono::milliseconds(30));
    }

    LOG(WARNING) << "达到最大迭代次数，跟踪可能未完全收敛，最终误差: " << prevError;
    return prevError < errorThreshold * 2.0; // 如果误差在阈值的两倍以内，仍然认为基本成功
}

// 获取当前时间的字符串表示
std::string TrackingManager::getCurrentTimeString() const
{
    auto now = std::chrono::system_clock::now();
    auto time_t_now = std::chrono::system_clock::to_time_t(now);

    std::tm tm_now;
    localtime_s(&tm_now, &time_t_now);

    char buffer[80];
    std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", &tm_now);

    return std::string(buffer);
}

// 初始化PID控制器
void TrackingManager::initPIDController()
{
    // 设置PID参数 - 使用更保守的参数
    moveDistancePID.Kp = 0.3;  // 降低比例系数，减少超调
    moveDistancePID.Ki = 0.05; // 降低积分系数，减少积分饱和
    moveDistancePID.Kd = 0.1;  // 降低微分系数，减少噪声影响
    moveDistancePID.integral = 0.0;
    moveDistancePID.prevError = 0.0;
    moveDistancePID.output = 10.0;  // 初始输出为10mm
    moveDistancePID.minOutput = 5.0;  // 最小输出3mm
    moveDistancePID.maxOutput = 50.0; // 最大输出50mm
}

// 更新PID控制器
double TrackingManager::updatePIDController(double currentValue, double targetValue)
{
    // 计算误差
    double error = targetValue - currentValue;

    // 计算积分项（带积分限幅和积分分离）
    if (std::abs(error) < 5.0) {  // 误差小于5mm时才进行积分
        moveDistancePID.integral += error;
        // 更严格的积分限幅
        if (moveDistancePID.integral > 50.0) moveDistancePID.integral = 50.0;
        if (moveDistancePID.integral < -50.0) moveDistancePID.integral = -50.0;
    } else {
        moveDistancePID.integral = 0.0;  // 误差较大时清零积分项
    }

    // 计算微分项（带微分限幅）
    double derivative = error - moveDistancePID.prevError;
    if (derivative > 5.0) derivative = 5.0;  // 限制微分项变化率
    if (derivative < -5.0) derivative = -5.0;

    // 计算PID输出
    double output = moveDistancePID.Kp * error +
                    moveDistancePID.Ki * moveDistancePID.integral +
                    moveDistancePID.Kd * derivative;

    // 输出限幅
    if (output > moveDistancePID.maxOutput) output = moveDistancePID.maxOutput;
    if (output < moveDistancePID.minOutput) output = moveDistancePID.minOutput;

    // 更新状态
    moveDistancePID.prevError = error;
    moveDistancePID.output = output;

    return output;
}

// 修改动态调整最大移动距离阈值的方法
void TrackingManager::updateDynamicMaxMoveDistance(double currentMoveDistance)
{
    // 将当前移动距离添加到历史队列
    recentMoveDistances.push_back(currentMoveDistance);

    // 保持队列长度不超过MAX_MOVE_HISTORY
    if (recentMoveDistances.size() > MAX_MOVE_HISTORY) {
        recentMoveDistances.pop_front();
    }


    // 至少需要3个数据点才能进行趋势分析
    if (recentMoveDistances.size() >= 3) {
        // 计算最近几次移动的平均值作为目标值
        double sum = 0.0;
        for (const auto& dist : recentMoveDistances) {
            sum += dist;
        }
        double targetValue = sum / recentMoveDistances.size();

        // 使用PID控制器计算新的最大移动距离
        double newMaxDistance = updatePIDController(currentMoveDistance, targetValue);

        // 更新动态最大移动距离
        if (std::abs(newMaxDistance - dynamicMaxMoveDistance) > 0.1) {  // 变化超过0.1mm才更新
            dynamicMaxMoveDistance = newMaxDistance;
            LOG(INFO) << "PID控制调整最大移动距离阈值到 " << dynamicMaxMoveDistance << "mm";
        }
    }
}

// 新增方法实现：设置是否启用旋转调整
void TrackingManager::setEnableRotationAdjustment(bool enable) {
    enableRotationAdjustment = enable;
    LOG(INFO) << (enable ? "启用" : "禁用") << "旋转调整";
}

// 新增方法实现：获取是否启用旋转调整
bool TrackingManager::isRotationAdjustmentEnabled() const {
    return enableRotationAdjustment;
}

// 新增方法实现：设置旋转方向反转
void TrackingManager::setInvertRotationDirection(bool invert) {
    invertRotationDirection = invert;
    LOG(INFO) << (invert ? "启用" : "禁用") << "旋转方向反转";
}

// 新增方法实现：获取旋转方向反转状态
bool TrackingManager::isRotationDirectionInverted() const {
    return invertRotationDirection;
}