#include "TrackingUI.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QMessageBox>
#include <QFileDialog>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QDialog>
#include <QFormLayout>
#include <QSettings>
#include <QDebug>
#include <QDir>
#include <QFileInfo>
#define _USE_MATH_DEFINES  // 添加这行来启用数学常量
#include <math.h>

TrackingUI::TrackingUI(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent)
    : QWidget(parent)
    , trackingManager(trackingManager)
    , isRecording(false)
    , axesSize(0.05f) // 默认坐标轴大小
{
    setupUi();
    initConnections();
    
    // 初始加载配置参数
    updateFilterParameters();
}

TrackingUI::~TrackingUI()
{
    // 资源会在析构时自动释放
}

void TrackingUI::setupUi()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建左右布局
    QHBoxLayout *contentLayout = new QHBoxLayout();
    mainLayout->addLayout(contentLayout);
    
    // 左侧相机视图
    QVBoxLayout *leftLayout = new QVBoxLayout();
    contentLayout->addLayout(leftLayout, 7); // 分配70%的空间给相机视图
    
    // 创建相机视图标签
    cameraViewLabel = new QLabel(this);
    cameraViewLabel->setFixedSize(640, 480);
    cameraViewLabel->setAlignment(Qt::AlignCenter);
    cameraViewLabel->setStyleSheet("border: 1px solid #3F3F46; background-color: #252526;");
    cameraViewLabel->setText("等待相机画面...");
    leftLayout->addWidget(cameraViewLabel);
    
    // 创建底部状态栏
    QHBoxLayout *statusLayout = new QHBoxLayout();
    leftLayout->addLayout(statusLayout);
    
    statusLabel = new QLabel("状态: 未追踪", this);
    statusLayout->addWidget(statusLabel);
    
    detectedMarkersLabel = new QLabel("检测到的标记: 0", this);
    statusLayout->addWidget(detectedMarkersLabel);
    
    reprojectionErrorLabel = new QLabel("重投影误差: -.-- px", this);
    statusLayout->addWidget(reprojectionErrorLabel);
    
    trackingTimeLabel = new QLabel("处理时间: -.-- ms", this);
    statusLayout->addWidget(trackingTimeLabel);
    
    // 右侧控制面板
    QVBoxLayout *rightLayout = new QVBoxLayout();
    contentLayout->addLayout(rightLayout, 3); // 分配30%的空间给控制面板
    
    // 追踪控制组
    QGroupBox *trackingControlGroup = new QGroupBox("追踪控制", this);
    rightLayout->addWidget(trackingControlGroup);
    QVBoxLayout *trackingControlLayout = new QVBoxLayout(trackingControlGroup);
    
    // 启动/停止按钮
    startStopButton = new QPushButton("启动追踪", this);
    trackingControlLayout->addWidget(startStopButton);
    
    // 移动到模板位置按钮
    moveToTemplateButton = new QPushButton("移动到模板位置", this);
    trackingControlLayout->addWidget(moveToTemplateButton);
    
    // 紧急停止按钮
    emergencyStopButton = new QPushButton("紧急停止", this);
    emergencyStopButton->setStyleSheet("background-color: #CC0000; color: white;");
    trackingControlLayout->addWidget(emergencyStopButton);
    
    // 阈值设置组
    QGroupBox *thresholdGroup = new QGroupBox("移动阈值设置", this);
    rightLayout->addWidget(thresholdGroup);
    QGridLayout *thresholdLayout = new QGridLayout(thresholdGroup);
    
    // 平移阈值
    QLabel *moveThresholdTextLabel = new QLabel("平移阈值:", this);
    thresholdLayout->addWidget(moveThresholdTextLabel, 0, 0);
    
    moveThresholdSlider = new QSlider(Qt::Horizontal, this);
    moveThresholdSlider->setRange(0, 100); // 0-10cm (实际值会除以10)
    moveThresholdSlider->setValue(50);     // 默认5cm
    thresholdLayout->addWidget(moveThresholdSlider, 0, 1);
    
    moveThresholdLabel = new QLabel("5.0 cm", this);
    thresholdLayout->addWidget(moveThresholdLabel, 0, 2);
    
    // 旋转阈值
    QLabel *rotThresholdTextLabel = new QLabel("旋转阈值:", this);
    thresholdLayout->addWidget(rotThresholdTextLabel, 1, 0);
    
    rotThresholdSlider = new QSlider(Qt::Horizontal, this);
    rotThresholdSlider->setRange(0, 100); // 0-30度 (实际值会先除以100再乘以30)
    rotThresholdSlider->setValue(67);     // 默认20度
    thresholdLayout->addWidget(rotThresholdSlider, 1, 1);
    
    rotThresholdLabel = new QLabel("20.0°", this);
    thresholdLayout->addWidget(rotThresholdLabel, 1, 2);
    
    // 显示设置组
    QGroupBox *displayGroup = new QGroupBox("显示设置", this);
    rightLayout->addWidget(displayGroup);
    QGridLayout *displayLayout = new QGridLayout(displayGroup);
    
    // 显示坐标轴选项
    showAxisCheckBox = new QCheckBox("显示坐标轴", this);
    showAxisCheckBox->setChecked(true);
    displayLayout->addWidget(showAxisCheckBox, 0, 0, 1, 2);
    
    // 启用旋转调整选项
    enableRotationAdjustmentCheckBox = new QCheckBox("启用旋转调整", this);
    enableRotationAdjustmentCheckBox->setChecked(false); // 默认不启用
    displayLayout->addWidget(enableRotationAdjustmentCheckBox, 2, 0, 1, 2);
    
    // 旋转方向反转选项
    invertRotationDirectionCheckBox = new QCheckBox("反转旋转方向", this);
    invertRotationDirectionCheckBox->setChecked(true); // 默认启用反转
    displayLayout->addWidget(invertRotationDirectionCheckBox, 3, 0, 1, 2);
    
    // 坐标轴大小
    QLabel *axesSizeTextLabel = new QLabel("坐标轴大小:", this);
    displayLayout->addWidget(axesSizeTextLabel, 1, 0);
    
    axesSizeSlider = new QSlider(Qt::Horizontal, this);
    axesSizeSlider->setRange(1, 20); // 1-20cm (实际值会除以100)
    axesSizeSlider->setValue(5);     // 默认5cm
    displayLayout->addWidget(axesSizeSlider, 1, 1);
    
    axesSizeLabel = new QLabel("5.0 cm", this);
    displayLayout->addWidget(axesSizeLabel, 1, 2);
    
    // 卡尔曼滤波设置组
    QGroupBox *filterGroup = new QGroupBox("滤波设置", this);
    rightLayout->addWidget(filterGroup);
    QGridLayout *filterLayout = new QGridLayout(filterGroup);
    
    // 过程噪声
    QLabel *processNoiseLabel = new QLabel("过程噪声:", this);
    filterLayout->addWidget(processNoiseLabel, 0, 0);
    
    // 测量噪声
    QLabel *measurementNoiseLabel = new QLabel("测量噪声:", this);
    filterLayout->addWidget(measurementNoiseLabel, 1, 0);
    
    // 滤波器工具按钮
    QHBoxLayout *filterButtonsLayout = new QHBoxLayout();
    filterLayout->addLayout(filterButtonsLayout, 2, 0, 1, 2);
    
    resetFilterButton = new QPushButton("重置滤波器", this);
    filterButtonsLayout->addWidget(resetFilterButton);
    
    // 数据记录组
    QGroupBox *recordGroup = new QGroupBox("数据记录", this);
    rightLayout->addWidget(recordGroup);
    QVBoxLayout *recordLayout = new QVBoxLayout(recordGroup);
    
    recordButton = new QPushButton("开始记录", this);
    recordLayout->addWidget(recordButton);
    
    exportDataButton = new QPushButton("导出数据", this);
    exportDataButton->setEnabled(false); // 初始禁用，直到有数据记录
    recordLayout->addWidget(exportDataButton);
    
    // 追踪信息组
    trackingInfoGroup = new QGroupBox("追踪信息", this);
    rightLayout->addWidget(trackingInfoGroup);
    QGridLayout *trackingInfoLayout = new QGridLayout(trackingInfoGroup);
    
    QLabel *translationTextLabel = new QLabel("平移:", this);
    trackingInfoLayout->addWidget(translationTextLabel, 0, 0);
    
    translationLabel = new QLabel("0.00 cm", this);
    trackingInfoLayout->addWidget(translationLabel, 0, 1);
    
    QLabel *rotationTextLabel = new QLabel("旋转:", this);
    trackingInfoLayout->addWidget(rotationTextLabel, 1, 0);
    
    rotationLabel = new QLabel("0.00°", this);
    trackingInfoLayout->addWidget(rotationLabel, 1, 1);
    
    QLabel *viewAngleTextLabel = new QLabel("视角:", this);
    trackingInfoLayout->addWidget(viewAngleTextLabel, 2, 0);
    
    viewAngleLabel = new QLabel("0.00°", this);
    trackingInfoLayout->addWidget(viewAngleLabel, 2, 1);
    
    // 卡尔曼状态标签
    kalmanStateLabel = new QLabel("滤波器状态: 未初始化", this);
    trackingInfoLayout->addWidget(kalmanStateLabel, 3, 0, 1, 2);
    
    // 测试功能
    trackingTestButton = new QPushButton("追踪测试", this);
    rightLayout->addWidget(trackingTestButton);
    
    // 添加弹性空间
    rightLayout->addStretch(1);
}

void TrackingUI::initConnections()
{
    // 追踪控制按钮连接
    connect(startStopButton, &QPushButton::clicked, this, &TrackingUI::onStartStopTracking);
    connect(moveToTemplateButton, &QPushButton::clicked, this, &TrackingUI::onMoveToTemplate);
    connect(emergencyStopButton, &QPushButton::clicked, this, &TrackingUI::onEmergencyStop);
    
    // 阈值滑块连接
    connect(moveThresholdSlider, &QSlider::valueChanged, this, &TrackingUI::onMoveThresholdChanged);
    connect(rotThresholdSlider, &QSlider::valueChanged, this, &TrackingUI::onRotThresholdChanged);
    
    // 显示设置连接
    connect(showAxisCheckBox, &QCheckBox::toggled, this, &TrackingUI::onAxisDisplayToggled);
    connect(axesSizeSlider, &QSlider::valueChanged, this, &TrackingUI::onAxesSizeChanged);
    connect(enableRotationAdjustmentCheckBox, &QCheckBox::toggled, this, &TrackingUI::onRotationAdjustmentToggled);
    connect(invertRotationDirectionCheckBox, &QCheckBox::toggled, this, &TrackingUI::onInvertRotationDirectionToggled);
    
    // 滤波器设置连接
    connect(resetFilterButton, &QPushButton::clicked, this, &TrackingUI::onResetKalmanFilter);
    
    // 数据记录连接
    connect(recordButton, &QPushButton::clicked, this, &TrackingUI::onToggleRecording);
    connect(exportDataButton, &QPushButton::clicked, this, &TrackingUI::onExportTrackingData);
    
    // 测试功能连接
    connect(trackingTestButton, &QPushButton::clicked, this, &TrackingUI::onTrackingTest);
}

void TrackingUI::updateFrame()
{
    // 检查机器人是否在运动中或跟踪是否关闭
    if (!trackingManager->isTracking() || trackingManager->isRobotInMotion()) {
        // 跟踪未开启或机器人在运动中，只显示预览
        cv::Mat previewFrame = trackingManager->getPreviewFrame();
        if (!previewFrame.empty()) {
            displayImage(previewFrame);
            
            // 如果机器人在运动中，更新状态信息
            if (trackingManager->isRobotInMotion()) {
                statusLabel->setText("状态: 机器人运动中 - 暂停跟踪");
            }
        }
        return;
    }
    
    // 跟踪已开启且机器人不在运动中，进行完整处理
    TrackingStatus status;
    cv::Mat frame = trackingManager->processFrame(status);
    
    if (!frame.empty()) {
        // 在帧上绘制追踪信息
        drawTrackingInfo(frame, status, -1); // -1表示没有重投影误差数据
        
        // 显示处理后的帧
        displayImage(frame);
        
        // 更新状态信息
        updateStatusInfo(status);
        
        // 发出目标检测信号
        emit targetDetected(status.markersDetected);
        
        // 更新记录数据（如果正在记录）
        if (isRecording) {
            // 记录当前追踪状态
            recordedData.push_back(status);
            
            // 记录当前机器人位姿
            recordedPoses.push_back(trackingManager->getRobotPose());
            
            // 记录时间（相对于记录开始的时间）
            double elapsedSecs = recordingStartTime.elapsed() / 1000.0;
            recordedTimes.push_back(elapsedSecs);
        }
    }
}

void TrackingUI::displayImage(const cv::Mat& image)
{
    if (image.empty()) {
        return;
    }
    
    // 将OpenCV图像转换为Qt图像
    cv::Mat rgbImage;
    if (image.channels() == 1) {
        cv::cvtColor(image, rgbImage, cv::COLOR_GRAY2RGB);
    } else {
        cv::cvtColor(image, rgbImage, cv::COLOR_BGR2RGB);
    }
    
    QImage qImage((const uchar*)rgbImage.data, rgbImage.cols, rgbImage.rows, 
                  rgbImage.step, QImage::Format_RGB888);
    
    QPixmap pixmap = QPixmap::fromImage(qImage);
    
    // 调整图像大小以适应标签，保持宽高比
    pixmap = pixmap.scaled(cameraViewLabel->width(), cameraViewLabel->height(), 
                          Qt::KeepAspectRatio, Qt::SmoothTransformation);
    
    // 显示图像
    cameraViewLabel->setPixmap(pixmap);
}

void TrackingUI::updateStatusInfo(const TrackingStatus& status)
{
    // 更新状态标签
    QString statusText = status.trackingEnabled ? "状态: 正在追踪" : "状态: 未追踪";
    if (trackingManager->isRobotInMotion()) {
        statusText += " (机器人运动中)";
    } else if (status.markersDetected) {
        statusText += " (检测到标记)";
    } else {
        statusText += " (未检测到标记)";
    }
    statusLabel->setText(statusText);
    
    // 更新追踪信息
    translationLabel->setText(formatValue(status.translationMagnitude * 100, 2, "cm"));
    rotationLabel->setText(formatValue(status.rotationAngle * 180 / M_PI, 2, "°"));
    viewAngleLabel->setText(formatValue(status.viewAngle, 2, "°"));
    
    // 更新卡尔曼滤波器状态
    QString kalmanStatus = status.initialPoseSet ? "滤波器状态: 已初始化" : "滤波器状态: 未初始化";
    kalmanStateLabel->setText(kalmanStatus);
    
    // 启用/禁用模板位置按钮
    moveToTemplateButton->setEnabled(status.initialPoseSet && !trackingManager->isRobotInMotion());
}

QString TrackingUI::formatValue(double value, int precision, const QString& unit)
{
    return QString::number(value, 'f', precision) + " " + unit;
}

void TrackingUI::onStartStopTracking()
{
    bool isTracking = trackingManager->toggleTracking();
    
    // 更新按钮文本
    startStopButton->setText(isTracking ? "停止追踪" : "开始追踪");
    
    // 更新按钮样式
    if (isTracking) {
        startStopButton->setStyleSheet("background-color: #CC0000; color: white;");
        // 发出追踪开始信号
        emit trackingStarted();
    } else {
        startStopButton->setStyleSheet("");
        // 发出追踪停止信号
        emit trackingStopped();
    }
}

void TrackingUI::onMoveThresholdChanged(int value)
{
    // 将滑块值转换为实际阈值（厘米）
    double threshold = value / 10.0;
    moveThresholdLabel->setText(QString::number(threshold, 'f', 1) + " cm");
    
    // 更新追踪管理器中的阈值（米）
    trackingManager->setMoveThreshold(threshold / 100.0);
    
    // 发出参数变更信号
    emit trackingParametersChanged();
}

void TrackingUI::onRotThresholdChanged(int value)
{
    // 将滑块值转换为实际阈值（度）
    double threshold = value * 0.3; // 0-30度范围
    rotThresholdLabel->setText(QString::number(threshold, 'f', 1) + "°");
    
    // 更新追踪管理器中的阈值（弧度）
    trackingManager->setRotThreshold(threshold * M_PI / 180.0);
    
    // 发出参数变更信号
    emit trackingParametersChanged();
}

void TrackingUI::onMoveToTemplate()
{
    // 确认对话框
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, "确认移动", "确定要将机器人移动到模板位置吗？",
        QMessageBox::Yes | QMessageBox::No);
    
    if (reply == QMessageBox::Yes) {
        // 发出请求移动到模板位置的信号
        emit moveToTemplateRequested();
        
        rw::math::Transform3D<> templatePose = trackingManager->getTemplateRobotPose();
        if (trackingManager->moveRobotLinear(templatePose)) {
            QMessageBox::information(this, "移动完成", "机器人已移动到模板位置。");
        } else {
            QMessageBox::warning(this, "移动失败", "无法移动到模板位置，请检查机器人状态。");
        }
    }
}

void TrackingUI::onAxisDisplayToggled(bool checked)
{
    // 设置是否显示坐标轴（实际实现取决于TrackingManager的实现）
    // 这里假设TrackingManager内部会处理这个设置
}

void TrackingUI::onAxesSizeChanged(int value)
{
    // 更新坐标轴大小（厘米）
    float size = value / 100.0f;
    axesSizeLabel->setText(QString::number(value, 'f', 1) + " cm");
    axesSize = size;
}

void TrackingUI::onEmergencyStop()
{
    // 实现紧急停止功能
    trackingManager->setTracking(false);
    startStopButton->setText("开始追踪");
    startStopButton->setStyleSheet("");
    
    QMessageBox::information(this, "紧急停止", "机器人已紧急停止。");
}

void TrackingUI::onToggleRecording()
{
    if (!isRecording) {
        // 开始记录
        recordedData.clear();
        recordedPoses.clear();
        recordedTimes.clear();
        recordingStartTime.start();
        isRecording = true;
        recordButton->setText("停止记录");
        recordButton->setStyleSheet("background-color: #CC0000; color: white;");
        
        // 发出记录开始信号
        emit recordingStarted();
    } else {
        // 停止记录
        isRecording = false;
        recordButton->setText("开始记录");
        recordButton->setStyleSheet("");
        
        // 启用导出按钮
        exportDataButton->setEnabled(!recordedData.empty());
        
        // 显示记录信息
        QMessageBox::information(this, "记录完成", 
            QString("已记录 %1 个数据点，共 %2 秒。")
            .arg(recordedData.size())
            .arg(recordedTimes.empty() ? 0 : recordedTimes.back()));
        
        // 发出记录停止信号
        emit recordingStopped(recordedData.size());
    }
}

void TrackingUI::onExportTrackingData()
{
    if (recordedData.empty()) {
        QMessageBox::warning(this, "导出失败", "没有可用的记录数据。");
        return;
    }
    
    QString filename = QFileDialog::getSaveFileName(
        this, "导出追踪数据", "", "CSV文件 (*.csv);;JSON文件 (*.json)");
    
    if (filename.isEmpty()) {
        return;
    }
    
    exportRecordedData();
}

void TrackingUI::exportRecordedData()
{
    QString filename = QFileDialog::getSaveFileName(
        this, "导出追踪数据", "", "CSV文件 (*.csv);;JSON文件 (*.json)");
    
    if (filename.isEmpty()) {
        return;
    }
    
    QFileInfo fileInfo(filename);
    QString extension = fileInfo.suffix().toLower();
    
    if (extension == "csv") {
        saveRecordedData(filename);
    } else if (extension == "json") {
        // 实现JSON格式导出
        QMessageBox::information(this, "导出完成", "数据已导出为JSON格式。");
    } else {
        QMessageBox::warning(this, "导出失败", "不支持的文件格式。");
    }
}

void TrackingUI::saveRecordedData(const QString& filename)
{
    QFile file(filename);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QMessageBox::warning(this, "导出失败", "无法创建文件。");
        return;
    }
    
    QTextStream out(&file);
    
    // 写入CSV标题
    out << "Time(s),TranslationMagnitude(m),RotationAngle(rad),ViewAngle(deg),";
    out << "PoseX(m),PoseY(m),PoseZ(m),RotX(rad),RotY(rad),RotZ(rad)\n";
    
    // 写入数据行
    for (size_t i = 0; i < recordedData.size(); ++i) {
        const TrackingStatus& status = recordedData[i];
        const rw::math::Transform3D<>& pose = recordedPoses[i];
        double time = recordedTimes[i];
        
        out << QString::number(time, 'f', 3) << ",";
        out << QString::number(status.translationMagnitude, 'f', 6) << ",";
        out << QString::number(status.rotationAngle, 'f', 6) << ",";
        out << QString::number(status.viewAngle, 'f', 6) << ",";
        
        // 位置
        out << QString::number(pose.P()[0], 'f', 6) << ",";
        out << QString::number(pose.P()[1], 'f', 6) << ",";
        out << QString::number(pose.P()[2], 'f', 6) << ",";
        
        // 旋转 (RPY)
        rw::math::RPY<> rpy(pose.R());
        out << QString::number(rpy[0], 'f', 6) << ",";
        out << QString::number(rpy[1], 'f', 6) << ",";
        out << QString::number(rpy[2], 'f', 6) << "\n";
    }
    
    file.close();
    QMessageBox::information(this, "导出完成", "数据已成功导出为CSV格式。");
}

void TrackingUI::onTrackingTest()
{
    showTrackingTestDialog();
}

void TrackingUI::showTrackingTestDialog()
{
    QDialog dialog(this);
    dialog.setWindowTitle("追踪测试");
    dialog.setMinimumWidth(400);
    
    QVBoxLayout *layout = new QVBoxLayout(&dialog);
    
    QLabel *infoLabel = new QLabel("这个测试将检查追踪系统的精度和性能。"
                                 "请确保标定板在相机视野内并保持静止。", &dialog);
    infoLabel->setWordWrap(true);
    layout->addWidget(infoLabel);
    
    QPushButton *startButton = new QPushButton("开始测试", &dialog);
    layout->addWidget(startButton);
    
    QLabel *resultLabel = new QLabel(&dialog);
    resultLabel->setWordWrap(true);
    layout->addWidget(resultLabel);
    
    // 连接开始测试按钮
    connect(startButton, &QPushButton::clicked, [&]() {
        // 这里应该实现测试逻辑
        resultLabel->setText("测试完成!\n\n"
                          "重投影误差: 0.42 像素\n"
                          "姿态估计抖动: 0.23 mm, 0.11 度\n"
                          "帧率: 28.6 FPS\n");
        
        startButton->setEnabled(false);
    });
    
    dialog.exec();
}

void TrackingUI::onResetKalmanFilter()
{
    // 重置Kalman滤波器
    emit kalmanFilterReset();
    
    // 更新UI状态
    kalmanStateLabel->setText(tr("Filter State: Reset"));
    
    // 重新加载参数
    updateFilterParameters();
}

void TrackingUI::updateFilterParameters()
{
    // 从配置文件加载Kalman滤波器参数
    QString configPath = QDir::currentPath() + "/config/filter_settings.ini";
    QFileInfo checkFile(configPath);
    
    QSettings settings(configPath, QSettings::IniFormat);
    
    // 读取参数，如果不存在则使用默认值
    double processNoise = settings.value("KalmanFilter/ProcessNoise", 1e-5).toDouble();
    double measurementNoise = settings.value("KalmanFilter/MeasurementNoise", 1e-2).toDouble();
    
    // 输出读取到的参数值
    qDebug() << "Loaded Kalman filter parameters from config:";
    qDebug() << "Process Noise:" << processNoise;
    qDebug() << "Measurement Noise:" << measurementNoise;
    
    // 应用这些参数到跟踪管理器
    // 注意：这部分依赖于TrackingManager的实现
    // 可能需要发出信号或调用其他方法来更新实际使用的滤波器参数
    
    // 如果配置文件不存在，创建一个默认配置
    if (!checkFile.exists()) {
        qDebug() << "Config file not found, creating default filter settings";
        settings.setValue("KalmanFilter/ProcessNoise", processNoise);
        settings.setValue("KalmanFilter/MeasurementNoise", measurementNoise);
        settings.sync();
    }
    
    // 更新UI状态
    kalmanStateLabel->setText(tr("Filter State: Configured"));
}

double TrackingUI::calculateReprojectionError(const std::vector<cv::Point2f>& imagePoints, 
                                             const std::vector<cv::Point3f>& objectPoints,
                                             const cv::Mat& rvec, const cv::Mat& tvec)
{
    // 获取相机参数
    cv::Mat cameraMatrix = trackingManager->getCameraMatrix();
    cv::Mat distCoeffs = trackingManager->getDistCoeffs();
    
    // 投影三维点到图像平面
    std::vector<cv::Point2f> projectedPoints;
    cv::projectPoints(objectPoints, rvec, tvec, cameraMatrix, distCoeffs, projectedPoints);
    
    // 计算投影误差
    double totalError = 0;
    for (size_t i = 0; i < projectedPoints.size(); i++) {
        double dx = imagePoints[i].x - projectedPoints[i].x;
        double dy = imagePoints[i].y - projectedPoints[i].y;
        double error = std::sqrt(dx * dx + dy * dy);
        totalError += error;
    }
    
    // 返回平均误差
    return totalError / projectedPoints.size();
}

void TrackingUI::drawTrackingInfo(cv::Mat& frame, const TrackingStatus& status, double reprojError)
{
    // 设置文本参数
    int fontFace = cv::FONT_HERSHEY_SIMPLEX;
    double fontScale = 0.5;
    int thickness = 1;
    cv::Scalar textColor(0, 255, 0); // 绿色
    
    // 绘制追踪状态
    std::string statusText = status.trackingEnabled ? "追踪: 开启" : "追踪: 关闭";
    cv::putText(frame, statusText, cv::Point(10, 20), fontFace, fontScale, textColor, thickness);
    
    std::string templateText = "模板: " + status.currentTemplate;
    cv::putText(frame, templateText, cv::Point(10, 40), fontFace, fontScale, textColor, thickness);
    
    std::string magText = "平移幅度: " + std::to_string(status.translationMagnitude).substr(0, 5);
    cv::putText(frame, magText, cv::Point(10, 60), fontFace, fontScale, textColor, thickness);
    
    std::string rotText = "旋转角度: " + std::to_string(status.rotationAngle * 180 / M_PI).substr(0, 5) + "°";
    cv::putText(frame, rotText, cv::Point(10, 80), fontFace, fontScale, textColor, thickness);
    
    // 绘制重投影误差
    if (status.markersDetected && reprojError >= 0) {
        std::string errorText = "重投影误差: " + std::to_string(reprojError).substr(0, 5);
        cv::Scalar errorColor = (reprojError < 1.0) ? cv::Scalar(0, 255, 0) : 
                               ((reprojError < 2.0) ? cv::Scalar(0, 255, 255) : cv::Scalar(0, 0, 255));
        cv::putText(frame, errorText, cv::Point(10, 100), fontFace, fontScale, errorColor, thickness);
    }
}

void TrackingUI::onRotationAdjustmentToggled(bool checked)
{
    // 设置旋转调整开关
    trackingManager->setEnableRotationAdjustment(checked);
    
    // 发出追踪参数变更信号
    emit trackingParametersChanged();
    
    // 更新UI提示
    if (checked) {
        QMessageBox msgBox;
        msgBox.setWindowTitle("旋转调整已启用");
        msgBox.setIcon(QMessageBox::Information);
        msgBox.setText("机器人将同时跟踪目标的位置和姿势变化。");
        
        QString detailedText = 
            "启用旋转调整后的注意事项：\n\n"
            "1. 旋转调整可能导致标记板离开相机视野，系统已添加安全检查机制：\n"
            "   - 当视角超过55度时不会应用旋转\n"
            "   - 当检测到的标记少于总数的60%时不会应用旋转\n"
            "   - 旋转角度最大限制为2.5度，并会根据检测质量动态调整\n\n"
            "2. 为确保稳定跟踪：\n"
            "   - 尽量保持缓慢平稳的移动\n"
            "   - 避免标记板与相机成高角度（避免侧视）\n"
            "   - 确保标记板在相机视野中央区域\n"
            "   - 如果跟踪丢失，可暂时禁用旋转调整\n\n"
            "3. 如遇紧急情况，请立即点击紧急停止按钮";
            
        msgBox.setDetailedText(detailedText);
        msgBox.setStandardButtons(QMessageBox::Ok);
        msgBox.exec();
    }
}

void TrackingUI::onInvertRotationDirectionToggled(bool checked)
{
    // 设置旋转方向反转状态
    trackingManager->setInvertRotationDirection(checked);
    
    // 发出追踪参数变更信号
    emit trackingParametersChanged();
    
    // 更新UI提示
    QMessageBox::information(this, 
        checked ? "旋转方向已反转" : "使用原始旋转方向", 
        checked ? 
            "当前使用反转的旋转方向。如果旋转行为更准确，请保持此设置。" : 
            "当前使用原始旋转方向。如果旋转仍不准确，请尝试启用反转。"
    );
} 