#ifndef TRACKINGUI_H
#define TRACKINGUI_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include <QCheckBox>
#include <QDoubleSpinBox>
#include <QTime>
#include <memory>
#include <vector>
#include <opencv2/opencv.hpp>
#include <QSettings>
#include <QGroupBox>
#include "TrackingManager.h"
#include "KalmanFilter3D.h"

class TrackingUI : public QWidget
{
    Q_OBJECT
    
public:
    explicit TrackingUI(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent = nullptr);
    ~TrackingUI();
    


    void updateStatusInfo(const TrackingStatus& status);
    
    void updateFilterParameters();
    
signals:
    // 定义TrackingUI的信号
    void templateUpdated();                                // 模板已更新
    void emergencyStop();                                  // 紧急停止
    void trackingStarted();                                // 追踪开始
    void trackingStopped();                                // 追踪停止
    void trackingParametersChanged();                      // 追踪参数变更
    void recordingStarted();                               // 数据记录开始
    void recordingStopped(int dataPointsCount);            // 数据记录停止
    void kalmanFilterReset();                              // 卡尔曼滤波器已重置
    void targetDetected(bool detected);                    // 目标检测状态
    void moveToTemplateRequested();                        // 请求移动到模板位置
    
private slots:
    void displayImage(const cv::Mat& image);
    void updateFrame();
    void onStartStopTracking();
    void onMoveThresholdChanged(int value);
    void onRotThresholdChanged(int value);
    void onMoveToTemplate();
    void onAxisDisplayToggled(bool checked);
    void onAxesSizeChanged(int value);
    void onEmergencyStop();
    void onToggleRecording();
    void onExportTrackingData();
    void onTrackingTest();
    void onResetKalmanFilter();
    void onRotationAdjustmentToggled(bool checked);
    void onInvertRotationDirectionToggled(bool checked);
    
private:
    std::shared_ptr<TrackingManager> trackingManager;
    
    // UI components
    QLabel *cameraViewLabel;
    QPushButton *startStopButton;
    QPushButton *moveToTemplateButton;
    QLabel *statusLabel;
    QLabel *infoLabel;
    QGroupBox *trackingInfoGroup;
    QLabel *translationLabel;
    QLabel *rotationLabel;
    QLabel *viewAngleLabel;
    QSlider *moveThresholdSlider;
    QSlider *rotThresholdSlider;
    QLabel *moveThresholdLabel;
    QLabel *rotThresholdLabel;
    
    QSlider *axesSizeSlider;
    QLabel *axesSizeLabel;
    QCheckBox *showAxisCheckBox;
    QCheckBox *enableRotationAdjustmentCheckBox;
    QCheckBox *invertRotationDirectionCheckBox;
    QPushButton *resetFilterButton;
    QPushButton *emergencyStopButton;
    QPushButton *recordButton;
    QPushButton *exportDataButton;
    QPushButton *trackingTestButton;
    QLabel *reprojectionErrorLabel;
    QLabel *detectedMarkersLabel;
    QLabel *kalmanStateLabel;
    QLabel *trackingTimeLabel;
    
    // Tracking data recording
    bool isRecording;
    std::vector<TrackingStatus> recordedData;
    std::vector<rw::math::Transform3D<>> recordedPoses;
    std::vector<double> recordedTimes;
    QTime recordingStartTime;
    float axesSize;
    
    void setupUi();
    void initConnections();
    QString formatValue(double value, int precision = 3, const QString& unit = "");
    void saveRecordedData(const QString& filename);
    void exportRecordedData();
    double calculateReprojectionError(const std::vector<cv::Point2f>& imagePoints, 
                                     const std::vector<cv::Point3f>& objectPoints,
                                     const cv::Mat& rvec, const cv::Mat& tvec);
    void drawTrackingInfo(cv::Mat& frame, const TrackingStatus& status, double reprojError);
    void showTrackingTestDialog();
};

#endif // TRACKINGUI_H 