#ifndef TEMPLATEMANAGER_H
#define TEMPLATEMANAGER_H

#include <QWidget>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <memory>
#include <vector>
#include <QString>

#include "TrackingManager.h"

class TemplateManager : public QWidget
{
    Q_OBJECT
    
public:
    explicit TemplateManager(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent = nullptr);
    ~TemplateManager();
    
    void updateTemplateList();
    int getCurrentTemplateIndex() const;
    QString getCurrentTemplateName() const;
    
signals:
    void templateSelectionChanged(int index);
    void templatesUpdated();
    
    void templateSaved(const QString& templateName);
    void templateLoaded(const QString& templateName);
    void templateDeleted(const QString& templateName);
    void templateCreated(const QString& templateName);
    void templateImported(const QString& templateName);
    void templateExported(const QString& templateName);
    
private slots:
    void onSaveTemplate();
    void onLoadTemplate();
    void onTemplateSelected(int index);
    void onCreateNewTemplate();
    void onDeleteTemplate();
    void onExportTemplate();
    void onImportTemplate();
    void onMoveToTemplatePosition();
    
private:
    std::shared_ptr<TrackingManager> trackingManager;
    
    // UI components
    QComboBox *templateComboBox;
    QLineEdit *newTemplateNameEdit;
    QPushButton *createTemplateButton;
    QPushButton *saveTemplateButton;
    QPushButton *loadTemplateButton;
    QPushButton *deleteTemplateButton;
    QPushButton *exportTemplateButton;
    QPushButton *importTemplateButton;
    QPushButton *moveToTemplatePositionButton;
    
    void setupUi();
    void initConnections();
};

#endif // TEMPLATEMANAGER_H