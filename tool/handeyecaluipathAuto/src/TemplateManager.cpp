#include "TemplateManager.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QMessageBox>
#include <QFileDialog>
#include <QDialog>
#include <QTableWidget>
#include <QHeaderView>
#include <QPainter>
#include <QDateTime>
#include <cmath>
#include <QLabel>

TemplateManager::TemplateManager(std::shared_ptr<TrackingManager> trackingManager, QWidget *parent)
    : QWidget(parent)
    , trackingManager(trackingManager)
{
    setupUi();
    initConnections();
    
    // 初始化模板列表
    updateTemplateList();
}

TemplateManager::~TemplateManager()
{
    // 资源会在析构时自动释放
}

void TemplateManager::setupUi()
{
    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    
    // 创建模板管理组
    QGroupBox *templateGroup = new QGroupBox("模板管理", this);
    mainLayout->addWidget(templateGroup);
    
    QGridLayout *templateLayout = new QGridLayout(templateGroup);
    
    // 模板下拉框
    QLabel *templateLabel = new QLabel("当前模板:", this);
    templateLayout->addWidget(templateLabel, 0, 0);
    
    templateComboBox = new QComboBox(this);
    templateComboBox->setMinimumWidth(200);
    templateLayout->addWidget(templateComboBox, 0, 1, 1, 3);
    
    // 模板操作按钮
    saveTemplateButton = new QPushButton("保存当前模板", this);
    templateLayout->addWidget(saveTemplateButton, 1, 0);
    
    loadTemplateButton = new QPushButton("加载模板", this);
    templateLayout->addWidget(loadTemplateButton, 1, 1);
    
    deleteTemplateButton = new QPushButton("删除模板", this);
    templateLayout->addWidget(deleteTemplateButton, 1, 2);
    
    // 模板导入导出操作
    exportTemplateButton = new QPushButton("导出模板", this);
    templateLayout->addWidget(exportTemplateButton, 2, 0);
    
    importTemplateButton = new QPushButton("导入模板", this);
    templateLayout->addWidget(importTemplateButton, 2, 1);
    
    // 添加移动到模板位置按钮
    moveToTemplatePositionButton = new QPushButton("移动到模板位置", this);
    templateLayout->addWidget(moveToTemplatePositionButton, 2, 2);
    
    // 模板比较按钮已移除
    // compareTemplatesButton = new QPushButton("比较模板", this);
    // templateLayout->addWidget(compareTemplatesButton, 2, 2);
    
    // 创建新模板组
    QGroupBox *newTemplateGroup = new QGroupBox("创建新模板", this);
    mainLayout->addWidget(newTemplateGroup);
    
    QHBoxLayout *newTemplateLayout = new QHBoxLayout(newTemplateGroup);
    
    QLabel *newTemplateLabel = new QLabel("模板名称:", this);
    newTemplateLayout->addWidget(newTemplateLabel);
    
    newTemplateNameEdit = new QLineEdit(this);
    newTemplateLayout->addWidget(newTemplateNameEdit);
    
    createTemplateButton = new QPushButton("创建", this);
    newTemplateLayout->addWidget(createTemplateButton);
    
    // 模板信息展示区
    QGroupBox *infoGroup = new QGroupBox("模板信息", this);
    mainLayout->addWidget(infoGroup, 1); // 分配更多空间
    
    QVBoxLayout *infoLayout = new QVBoxLayout(infoGroup);
    
    // 添加一个示意区域来显示模板位置和姿态
    QLabel *visualizationLabel = new QLabel(this);
    visualizationLabel->setMinimumSize(400, 300);
    visualizationLabel->setAlignment(Qt::AlignCenter);
    visualizationLabel->setText("选择一个模板查看详细信息");
    visualizationLabel->setStyleSheet("border: 1px solid #3F3F46; background-color: #252526;");
    infoLayout->addWidget(visualizationLabel);
    
    // 添加弹性空间
    mainLayout->addStretch(1);
}

void TemplateManager::initConnections()
{
    // 模板选择连接
    connect(templateComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), 
            this, &TemplateManager::onTemplateSelected);
    
    // 模板操作按钮连接
    connect(saveTemplateButton, &QPushButton::clicked, this, &TemplateManager::onSaveTemplate);
    connect(loadTemplateButton, &QPushButton::clicked, this, &TemplateManager::onLoadTemplate);
    connect(deleteTemplateButton, &QPushButton::clicked, this, &TemplateManager::onDeleteTemplate);
    
    // 模板导入导出连接
    connect(exportTemplateButton, &QPushButton::clicked, this, &TemplateManager::onExportTemplate);
    connect(importTemplateButton, &QPushButton::clicked, this, &TemplateManager::onImportTemplate);
    
    // 添加移动到模板位置按钮连接
    connect(moveToTemplatePositionButton, &QPushButton::clicked, this, &TemplateManager::onMoveToTemplatePosition);
    
    // 模板比较按钮连接已移除
    // connect(compareTemplatesButton, &QPushButton::clicked, this, &TemplateManager::onCompareTemplates);
    
    // 创建新模板连接
    connect(createTemplateButton, &QPushButton::clicked, this, &TemplateManager::onCreateNewTemplate);
}

void TemplateManager::updateTemplateList()
{
    // 断开信号连接，避免触发onTemplateSelected
    templateComboBox->blockSignals(true);
    
    // 保存当前选择的模板
    QString currentSelected = templateComboBox->currentText();
    
    // 清空列表
    templateComboBox->clear();
    
    // 获取所有可用模板
    std::vector<std::string> templates = trackingManager->getAvailableTemplates();
    
    // 添加模板到下拉框
    for (const auto& templateName : templates) {
        templateComboBox->addItem(QString::fromStdString(templateName));
    }
    
    // 恢复之前选择的模板（如果存在）
    int index = templateComboBox->findText(currentSelected);
    if (index >= 0) {
        templateComboBox->setCurrentIndex(index);
    } else if (templateComboBox->count() > 0) {
        // 默认选择第一个模板
        templateComboBox->setCurrentIndex(0);
    }
    
    // 恢复信号连接
    templateComboBox->blockSignals(false);
    
    // 更新按钮状态
    bool hasTemplates = templateComboBox->count() > 0;
    loadTemplateButton->setEnabled(hasTemplates);
    deleteTemplateButton->setEnabled(hasTemplates);
    exportTemplateButton->setEnabled(hasTemplates);
    moveToTemplatePositionButton->setEnabled(hasTemplates);
    
    // 发出更新信号
    emit templatesUpdated();
}

int TemplateManager::getCurrentTemplateIndex() const
{
    return templateComboBox->currentIndex();
}

QString TemplateManager::getCurrentTemplateName() const
{
    return templateComboBox->currentText();
}

void TemplateManager::onSaveTemplate()
{
    // 获取当前模板名称或使用默认值
    QString templateName = templateComboBox->currentText();
    if (templateName.isEmpty()) {
        templateName = "template_" + QString::number(QDateTime::currentSecsSinceEpoch());
    }
    
    // 如果模板已存在，确认是否覆盖
    if (templateComboBox->findText(templateName) >= 0) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认覆盖", "模板 '" + templateName + "' 已存在，是否覆盖？",
            QMessageBox::Yes | QMessageBox::No);
        
        if (reply != QMessageBox::Yes) {
            return;
        }
    }
    
    // 保存模板
    if (trackingManager->saveTemplate(templateName.toStdString())) {
        // 更新模板列表
        updateTemplateList();
        
        // 选择新保存的模板
        int index = templateComboBox->findText(templateName);
        if (index >= 0) {
            templateComboBox->setCurrentIndex(index);
        }
        
        // 发出模板保存信号
        emit templateSaved(templateName);
        
        QMessageBox::information(this, "模板保存成功", "模板 '" + templateName + "' 已成功保存。");
    } else {
        QMessageBox::warning(this, "模板保存失败", "无法保存模板，请确保追踪已正确初始化。");
    }
}

void TemplateManager::onLoadTemplate()
{
    QString templateName = templateComboBox->currentText();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "加载失败", "请先选择一个模板。");
        return;
    }
    
    // 加载模板
    if (trackingManager->loadTemplate(templateName.toStdString())) {
        QMessageBox::information(this, "模板加载成功", "模板 '" + templateName + "' 已成功加载。");
        
        // 更新选定模板
        trackingManager->setCurrentTemplate(templateName.toStdString());
        
        // 发出模板加载信号
        emit templateLoaded(templateName);
        
        // 发出模板更新信号
        emit templateSelectionChanged(templateComboBox->currentIndex());
    } else {
        QMessageBox::warning(this, "模板加载失败", "无法加载模板，请检查文件是否存在。");
    }
}

void TemplateManager::onTemplateSelected(int index)
{
    if (index < 0) {
        return;
    }
    
    QString templateName = templateComboBox->itemText(index);
    
    // 更新当前模板
    trackingManager->setCurrentTemplate(templateName.toStdString());
    
    // 发出模板选择变更信号
    emit templateSelectionChanged(index);
}

void TemplateManager::onCreateNewTemplate()
{
    QString templateName = newTemplateNameEdit->text().trimmed();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "创建失败", "请输入有效的模板名称。");
        return;
    }
    
    // 检查模板是否已存在
    if (templateComboBox->findText(templateName) >= 0) {
        QMessageBox::warning(this, "创建失败", "模板 '" + templateName + "' 已存在。");
        return;
    }
    
    // 设置当前模板名称
    trackingManager->setCurrentTemplate(templateName.toStdString());
    
    // 直接初始化当前位姿数据
    if (!trackingManager->initializeCurrentPose()) {
        QMessageBox::warning(this, "创建失败", "无法初始化位姿，请确保标记板在相机视野内且已被检测到。");
        return;
    }
    
    // 保存模板
    if (trackingManager->saveTemplate(templateName.toStdString())) {
        // 更新模板列表
        updateTemplateList();
        
        // 选择新创建的模板
        int index = templateComboBox->findText(templateName);
        if (index >= 0) {
            templateComboBox->setCurrentIndex(index);
        }
        
        // 发出模板创建信号
        emit templateCreated(templateName);
        
        QMessageBox::information(this, "模板创建成功", "模板 '" + templateName + "' 已成功创建。");
        
        // 清空输入框
        newTemplateNameEdit->clear();
    } else {
        QMessageBox::warning(this, "模板创建失败", "无法创建模板，请确保追踪已正确初始化。");
    }
}

void TemplateManager::onDeleteTemplate()
{
    QString templateName = templateComboBox->currentText();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "删除失败", "请先选择一个模板。");
        return;
    }
    
    // 确认删除
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, "确认删除", "确定要删除模板 '" + templateName + "' 吗？此操作无法撤销。",
        QMessageBox::Yes | QMessageBox::No);
    
    if (reply != QMessageBox::Yes) {
        return;
    }
    
    // 删除模板文件
    QString filename = "tracking_template_" + templateName + ".yml";
    QFile file(filename);
    
    if (file.exists() && file.remove()) {
        // 发出模板删除信号
        emit templateDeleted(templateName);
        
        // 更新模板列表
        updateTemplateList();
        
        QMessageBox::information(this, "模板删除成功", "模板 '" + templateName + "' 已成功删除。");
    } else {
        QMessageBox::warning(this, "模板删除失败", "无法删除模板文件，请检查文件权限。");
    }
}

void TemplateManager::onExportTemplate()
{
    QString templateName = templateComboBox->currentText();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "导出失败", "请先选择一个模板。");
        return;
    }
    
    // 选择导出路径
    QString exportPath = QFileDialog::getSaveFileName(
        this, "导出模板", templateName + ".yml", "模板文件 (*.yml)");
    
    if (exportPath.isEmpty()) {
        return;
    }
    
    // 复制模板文件
    QString sourceFile = "tracking_template_" + templateName + ".yml";
    
    if (QFile::exists(sourceFile)) {
        if (QFile::copy(sourceFile, exportPath)) {
            // 发出模板导出信号
            emit templateExported(templateName);
            
            QMessageBox::information(this, "导出成功", "模板 '" + templateName + "' 已成功导出。");
        } else {
            QMessageBox::warning(this, "导出失败", "无法导出模板文件，请检查文件权限。");
        }
    } else {
        QMessageBox::warning(this, "导出失败", "找不到模板文件。");
    }
}

void TemplateManager::onImportTemplate()
{
    // 选择要导入的文件
    QString importFile = QFileDialog::getOpenFileName(
        this, "导入模板", "", "模板文件 (*.yml)");
    
    if (importFile.isEmpty()) {
        return;
    }
    
    // 获取文件名作为模板名
    QFileInfo fileInfo(importFile);
    QString templateName = fileInfo.baseName();
    
    // 如果文件名以"tracking_template_"开头，则去除前缀
    if (templateName.startsWith("tracking_template_")) {
        templateName = templateName.mid(18);
    }
    
    // 检查模板名是否已存在
    QString destFile = "tracking_template_" + templateName + ".yml";
    
    if (QFile::exists(destFile)) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this, "确认覆盖", "模板 '" + templateName + "' 已存在，是否覆盖？",
            QMessageBox::Yes | QMessageBox::No);
        
        if (reply != QMessageBox::Yes) {
            return;
        }
        
        // 删除现有文件
        QFile::remove(destFile);
    }
    
    // 复制文件
    if (QFile::copy(importFile, destFile)) {
        // 更新模板列表
        updateTemplateList();
        
        // 选择新导入的模板
        int index = templateComboBox->findText(templateName);
        if (index >= 0) {
            templateComboBox->setCurrentIndex(index);
        }
        
        // 发出模板导入信号
        emit templateImported(templateName);
        
        QMessageBox::information(this, "导入成功", "模板 '" + templateName + "' 已成功导入。");
    } else {
        QMessageBox::warning(this, "导入失败", "无法导入模板文件，请检查文件权限。");
    }
}

void TemplateManager::onMoveToTemplatePosition()
{
    QString templateName = templateComboBox->currentText();
    if (templateName.isEmpty()) {
        QMessageBox::warning(this, "移动失败", "请先选择一个模板。");
        return;
    }
    
    // 确认是否移动
    QMessageBox::StandardButton reply = QMessageBox::question(
        this, "确认移动", "确定要将机器人移动到模板 '" + templateName + "' 记录的位置吗？",
        QMessageBox::Yes | QMessageBox::No);
    
    if (reply != QMessageBox::Yes) {
        return;
    }
    
    // 执行移动操作
    if (trackingManager->moveToTemplatePosition(templateName.toStdString())) {
        QMessageBox::information(this, "移动成功", "机器人已成功移动到模板 '" + templateName + "' 记录的位置。");
    } else {
        QMessageBox::warning(this, "移动失败", "无法移动到模板位置，请检查机器人状态或模板数据。");
    }
}

