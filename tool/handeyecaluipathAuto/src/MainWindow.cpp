#include "MainWindow.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QFileDialog>
#include <QStatusBar>
#include <QDebug>
#include <QMetaObject>

MainWindow::MainWindow(QWidget *parent)
        : QMainWindow(parent)
        , timer(new QTimer(this))
        , mainTabWidget(new QTabWidget(this))
{
    // 设置样式表
    setTechStyleSheet();

    // 创建追踪管理器（核心组件）
    trackingManager = std::make_shared<TrackingManager>();

    // 创建并初始化各功能模块
    trackingUI = std::make_unique<TrackingUI>(trackingManager, this);
    templateManager = std::make_unique<TemplateManager>(trackingManager, this);
    pathManager = std::make_unique<PathManager>(trackingManager, this);
//    calibrationUI = std::make_unique<CalibrationUI>(trackingManager, this);

    // 设置UI布局
    setupUi();

    // 初始化组件间的连接
    initConnections();

    // 加载 Annis logo
//    loadAnnisLogo();

    // 启动定时器，更新相机画面
    timer->start(10); // ~30 FPS
}

MainWindow::~MainWindow()
{
    // 停止定时器
    timer->stop();
}

void MainWindow::setupUi()
{
    // 设置窗口标题
    setWindowTitle("机器人视觉追踪系统");

    // 创建主布局
    QWidget *centralWidget = new QWidget(this);
    QVBoxLayout *mainLayout = new QVBoxLayout(centralWidget);
    mainLayout->setContentsMargins(0, 0, 0, 0); // 减少边距
    setCentralWidget(centralWidget);

    // 设置标签页
    mainTabWidget = new QTabWidget(this);
    mainLayout->addWidget(mainTabWidget);

    // 将各功能模块添加到标签页
    mainTabWidget->addTab(trackingUI.get(), "实时追踪");
    mainTabWidget->addTab(templateManager.get(), "模板管理");
    mainTabWidget->addTab(pathManager.get(), "路径管理");
//    mainTabWidget->addTab(calibrationUI.get(), "标定设置");

    // 设置默认标签页
    mainTabWidget->setCurrentIndex(0);

    // 创建底部布局
    QWidget *bottomWidget = new QWidget(this);
    bottomWidget->setFixedHeight(60); // 固定高度
    bottomWidget->setStyleSheet("background-color: #252526;"); // 与应用整体风格一致

    QHBoxLayout *bottomLayout = new QHBoxLayout(bottomWidget);
    bottomLayout->setContentsMargins(10, 0, 10, 0); // 减少左右边距

    // 添加状态信息区域（可选）
    QLabel *statusInfoLabel = new QLabel("就绪", this);
    statusInfoLabel->setStyleSheet("color: #AAAAAA;");
    bottomLayout->addWidget(statusInfoLabel);

    // 添加弹性空间，将logo推向右侧
    bottomLayout->addStretch(1);

    // 添加 Logo 显示区域
    logoLabel = new QLabel(this);
    logoLabel->setFixedSize(150, 60); // 调整尺寸适应logo
    logoLabel->setAlignment(Qt::AlignCenter);
    logoLabel->setStyleSheet("background-color: transparent;");
    bottomLayout->addWidget(logoLabel);

    // 将底部布局添加到主布局
    mainLayout->addWidget(bottomWidget);

    // 设置窗口大小
    resize(1200, 800);
}

void MainWindow::initConnections()
{
    // ======== 基本连接 ========

    // 使用invokeMethod定时调用更新函数
    timer->disconnect();
    QObject::connect(timer, &QTimer::timeout, [this]() {
        // 每次都获取相机图像，更新全局frame变量
        trackingManager->captureFrame();
        
        // 处理当前标签页
        int currentTab = mainTabWidget->currentIndex();
        
        if (currentTab == 0) { // 实时追踪标签页
            // 更新追踪UI
            QMetaObject::invokeMethod(trackingUI.get(), "updateFrame", Qt::AutoConnection);
        } 
        else if (currentTab == 2) { // 路径管理标签页
            // 更新路径管理UI相机视图
            QMetaObject::invokeMethod(pathManager.get(), "updateCameraFrame", Qt::AutoConnection);
        }
    });

    // ======== TrackingUI - TemplateManager 连接 ========

    // 当模板列表更新时，刷新追踪UI
    QObject::connect(templateManager.get(), &TemplateManager::templatesUpdated, [this]() {
        QMetaObject::invokeMethod(trackingUI.get(), "updateFrame", Qt::AutoConnection);
    });

    // 当模板被选中时，刷新追踪UI
    QObject::connect(templateManager.get(), &TemplateManager::templateSelectionChanged,
                     [this](int) {
                         QMetaObject::invokeMethod(trackingUI.get(), "updateFrame", Qt::AutoConnection);
                     });

    // ======== 紧急停止响应 ========

    // 当追踪UI发出紧急停止信号时，停止当前所有操作
    QObject::connect(trackingUI.get(), &TrackingUI::emergencyStop, [this]() {
        trackingManager->setTracking(false);
        timer->stop();

        QTimer::singleShot(500, [this]() {
            timer->start();
        });
    });

    // ======== PathManager 相关连接 ========

    // 当开始执行路径时，临时增加帧率以获得更平滑的视觉反馈
    QObject::connect(pathManager.get(), &PathManager::pathExecutionStarted, [this]() {
        timer->setInterval(100);
    });

    // 执行完路径后恢复正常帧率
    QObject::connect(pathManager.get(), &PathManager::pathExecutionFinished, [this]() {
        timer->setInterval(100);
        QMetaObject::invokeMethod(trackingUI.get(), "updateFrame", Qt::AutoConnection);
    });

    // 路径执行错误时的处理
    QObject::connect(pathManager.get(), &PathManager::pathExecutionError, [this](const QString& errorMessage) {
        qDebug() << "路径执行错误: " << errorMessage;
    });

//    // ======== CalibrationUI 相关连接 ========
//
//    QObject::connect(calibrationUI.get(), &CalibrationUI::calibrationParametersReloaded,
//            [this](bool success) {
//        if (success) {
//            QMetaObject::invokeMethod(trackingUI.get(), "updateFrame", Qt::DirectConnection);
//        }
//    });

    // ======== 标签页切换事件 ========

    // 当用户切换到追踪标签页时，确保追踪UI更新
    QObject::connect(mainTabWidget, &QTabWidget::currentChanged, [this](int index) {
        if (index == 0) { // 追踪标签页
            QMetaObject::invokeMethod(trackingUI.get(), "updateFrame", Qt::AutoConnection);
        } else if (index == 2) { // 路径管理标签页
            QMetaObject::invokeMethod(pathManager.get(), "updateCameraFrame", Qt::AutoConnection);
        }
    });

    // ======== TemplateManager 与 PathManager 连接 ========

    // 当模板加载或更新时，通知PathManager
    QObject::connect(templateManager.get(), &TemplateManager::templateLoaded,
                     [this](const QString& templateName) {
                         if (pathManager->hasPathPoints()) {
                             QMessageBox::StandardButton reply = QMessageBox::question(
                                     this, "修正路径",
                                     QString("已加载模板 '%1'，是否要基于此模板修正当前路径？").arg(templateName),
                                     QMessageBox::Yes | QMessageBox::No);

                             if (reply == QMessageBox::Yes) {
                                 QMetaObject::invokeMethod(pathManager.get(), "correctPath", Qt::AutoConnection);
                             }
                         }
                     });

    // ======== 记录功能连接 ========

    // 数据记录相关操作
    QObject::connect(trackingUI.get(), &TrackingUI::recordingStarted, [this]() {
        statusBar()->showMessage("正在记录数据...", 2000);
    });

    QObject::connect(trackingUI.get(), &TrackingUI::recordingStopped, [this](int dataPointsCount) {
        statusBar()->showMessage(QString("记录已停止，共记录 %1 个数据点").arg(dataPointsCount), 3000);
    });

    // ======== 其他连接 ========

    // 连接各种状态变更事件到状态栏显示
    QObject::connect(trackingUI.get(), &TrackingUI::trackingStarted, [this]() {
        statusBar()->showMessage("追踪已启动", 2000);
    });

    QObject::connect(trackingUI.get(), &TrackingUI::trackingStopped, [this]() {
        statusBar()->showMessage("追踪已停止", 2000);
    });

    QObject::connect(trackingUI.get(), &TrackingUI::targetDetected, [this](bool detected) {
        static bool lastDetected = false;

        if (detected && !lastDetected) {
            statusBar()->showMessage("已检测到标记", 1000);
            lastDetected = true;
        } else if (!detected && lastDetected) {
            statusBar()->showMessage("未检测到标记", 1000);
            lastDetected = false;
        }
    });
}

void MainWindow::setTechStyleSheet()
{
    // 设置应用程序样式
    QString styleSheet =
            "QMainWindow { background-color: #2D2D30; color: #FFFFFF; }"
            "QTabWidget::pane { border: 1px solid #3F3F46; background-color: #252526; }"
            "QTabBar::tab { background-color: #2D2D30; color: #FFFFFF; padding: 5px 15px; }"
            "QTabBar::tab:selected { background-color: #007ACC; }"
            "QTabBar::tab:!selected { margin-top: 2px; }"
            "QGroupBox { border: 1px solid #3F3F46; border-radius: 3px; margin-top: 10px; font-weight: bold; color: #FFFFFF; }"
            "QGroupBox::title { subcontrol-origin: margin; subcontrol-position: top left; padding: 5px; }"
            "QPushButton { background-color: #0E639C; color: white; border: none; padding: 5px 15px; border-radius: 2px; }"
            "QPushButton:hover { background-color: #1177BB; }"
            "QPushButton:pressed { background-color: #0E5A8C; }"
            "QSlider::groove:horizontal { border: 1px solid #3F3F46; height: 8px; background: #252526; margin: 2px 0; }"
            "QSlider::handle:horizontal { background: #007ACC; border: 1px solid #5F5F5F; width: 18px; margin: -2px 0; border-radius: 3px; }"
            "QLabel { color: #FFFFFF; }"
            "QLineEdit { background-color: #333337; color: #FFFFFF; border: 1px solid #3F3F46; padding: 3px; }"
            "QComboBox { background-color: #333337; color: #FFFFFF; border: 1px solid #3F3F46; padding: 3px; }"
            "QTableWidget { background-color: #252526; color: #FFFFFF; gridline-color: #3F3F46; }"
            "QTableWidget::item { padding: 5px; }"
            "QHeaderView::section { background-color: #2D2D30; color: #FFFFFF; padding: 5px; border: 1px solid #3F3F46; }"
            "QCheckBox { color: #FFFFFF; }"
            "QRadioButton { color: #FFFFFF; }"
            "QSpinBox, QDoubleSpinBox { background-color: #333337; color: #FFFFFF; border: 1px solid #3F3F46; }"
            // Add dialog styling to match the dark theme
            "QDialog { background-color: #2D2D30; color: #FFFFFF; }"
            "QDialog QLabel { color: #FFFFFF; }"
            "QDialog QPushButton { background-color: #0E639C; color: white; }"
            "QDialog QLineEdit { background-color: #333337; color: #FFFFFF; }"
            "QMessageBox { background-color: #2D2D30; color: #FFFFFF; }"
            "QMessageBox QLabel { color: #FFFFFF; }"
            "QFileDialog { background-color: #2D2D30; color: #FFFFFF; }"
            "QFileDialog QTreeView { background-color: #252526; color: #FFFFFF; }"
            "QFileDialog QComboBox { background-color: #333337; color: #FFFFFF; }"
            "QFileDialog QLineEdit { background-color: #333337; color: #FFFFFF; }"
            "QDialogButtonBox { background-color: #2D2D30; }";

    setStyleSheet(styleSheet);
}

void MainWindow::loadAnnisLogo()
{
    // 尝试从多个可能的路径加载logo
    QStringList possiblePaths = {
            "resources/annis_logo.png",
            "resources/annis_logo.jpg",
            "../resources/annis_logo.png",
            "../resources/annis_logo.jpg",
            "../../resources/annis_logo.png",
            "../../resources/annis_logo.jpg",
            "./annis_logo.png",
            "./annis_logo.jpg"
    };

    QPixmap logo;
    bool loaded = false;

    // 尝试每个可能的路径
    for (const QString& path : possiblePaths) {
        if (QFile::exists(path) && logo.load(path)) {
            loaded = true;
            break;
        }
    }

    if (!loaded) {
        // 如果没有找到文件，尝试从文件对话框加载
        QString fileName = QFileDialog::getOpenFileName(this,
                                                        tr("加载 Annis Logo"),
                                                        "",
                                                        tr("图像文件 (*.png *.jpg *.jpeg *.bmp)"));

        if (!fileName.isEmpty()) {
            logo.load(fileName);
            loaded = true;
        }
    }

    if (loaded) {
        // 缩放logo以适应label
        logo = logo.scaled(logoLabel->width(), logoLabel->height(),
                           Qt::KeepAspectRatio, Qt::SmoothTransformation);
        logoLabel->setPixmap(logo);
        qDebug() << "Annis logo 已加载";
    } else {
        qDebug() << "无法加载 Annis logo";
        logoLabel->setText("Annis");
    }
}
