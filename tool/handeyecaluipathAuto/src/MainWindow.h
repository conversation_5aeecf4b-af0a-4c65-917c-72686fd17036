#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include <QTabWidget>
#include <QLabel>
#include <memory>

#include "TrackingUI.h"
#include "TemplateManager.h"
#include "PathManager.h"
#include "CalibrationUI.h"
#include "TrackingManager.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private:
    QTimer *timer;
    QTabWidget *mainTabWidget;
    QLabel *logoLabel;
    
    // Core components
    std::shared_ptr<TrackingManager> trackingManager;
    
    // UI components by functionality
    std::unique_ptr<TrackingUI> trackingUI;
    std::unique_ptr<TemplateManager> templateManager;
    std::unique_ptr<PathManager> pathManager;
    std::unique_ptr<CalibrationUI> calibrationUI;
    
    void setupUi();
    void initConnections();
    void setTechStyleSheet();
    void loadAnnisLogo();
};

#endif // MAINWINDOW_H 