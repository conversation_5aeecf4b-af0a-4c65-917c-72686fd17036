#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <string>

using namespace cv;

// Function to generate ChArUco board
Mat generateCharucoBoard(int squaresX, int squaresY, int squareLength, int markerLength, 
                        float pixelsPerMM, float paperWidth, float paperHeight, float dpi) {
    // 创建ArUco字典和ChArUco板
    auto dictionary = aruco::getPredefinedDictionary(aruco::DICT_4X4_50);
    auto board = aruco::CharucoBoard(
        {squaresX, squaresY},
        float(squareLength), 
        float(markerLength), 
        dictionary);
    
    // 创建白色背景图像
    const int imageWidth = round(paperWidth * pixelsPerMM);
    const int imageHeight = round(paperHeight * pixelsPerMM);
    Mat boardImage(imageHeight, imageWidth, CV_8UC1, Scalar::all(255));
    
    // 计算缩放因子
    float scale = pixelsPerMM;
    
    // 计算中心偏移
    float boardWidth = squaresX * squareLength;
    float boardHeight = squaresY * squareLength;
    float offsetX = (paperWidth - boardWidth) / 2 * scale;
    float offsetY = (paperHeight - boardHeight) / 2 * scale;
    
    // 生成标定板
    Mat boardImageTemp;
    board.generateImage(Size(boardWidth * scale, boardHeight * scale), boardImageTemp);
    
    // 将标定板复制到最终图像的中心位置
    Rect roi(offsetX, offsetY, boardImageTemp.cols, boardImageTemp.rows);
    boardImageTemp.copyTo(boardImage(roi));
    
    // 添加尺寸信息文本
    const int margins = 20;
    String info = format("ChArUco Size: %.1f x %.1f mm, Square: %d mm, Marker: %d mm",
                        boardWidth, boardHeight, squareLength, markerLength);
    putText(boardImage, info, Point(margins * scale, (paperHeight - margins/2) * scale),
            FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0), 2);
    
    return boardImage;
}

// Function to generate ArUco board
Mat generateArucoBoard(int markersX, int markersY, int markerLength, int markerSeparation,
                      float pixelsPerMM, float paperWidth, float paperHeight, float dpi) {
    // 创建ArUco字典和ArUco板
    auto dictionary = aruco::getPredefinedDictionary(aruco::DICT_6X6_250);
    auto board = aruco::GridBoard(
        {markersX, markersY},
        float(markerLength),
        float(markerSeparation),
        dictionary);
    
    // 创建白色背景图像
    const int imageWidth = round(paperWidth * pixelsPerMM);
    const int imageHeight = round(paperHeight * pixelsPerMM);
    Mat boardImage(imageHeight, imageWidth, CV_8UC1, Scalar::all(255));
    
    // 计算缩放因子
    float scale = pixelsPerMM;
    
    // 计算中心偏移
    float boardWidth = markersX * markerLength + (markersX - 1) * markerSeparation;
    float boardHeight = markersY * markerLength + (markersY - 1) * markerSeparation;
    float offsetX = (paperWidth - boardWidth) / 2 * scale;
    float offsetY = (paperHeight - boardHeight) / 2 * scale;
    
    // 生成标定板
    Mat boardImageTemp;
    board.generateImage(Size(boardWidth * scale, boardHeight * scale), boardImageTemp);
    
    // 将标定板复制到最终图像的中心位置
    Rect roi(offsetX, offsetY, boardImageTemp.cols, boardImageTemp.rows);
    boardImageTemp.copyTo(boardImage(roi));
    
    // 添加尺寸信息文本
    const int margins = 20;
    String info = format("ArUco Size: %.1f x %.1f mm, Marker: %d mm, Separation: %d mm",
                        boardWidth, boardHeight, markerLength, markerSeparation);
    putText(boardImage, info, Point(margins * scale, (paperHeight - margins/2) * scale),
            FONT_HERSHEY_SIMPLEX, 0.7, Scalar(0), 2);
    
    return boardImage;
}

int main() {
    // A4纸尺寸(单位:毫米)
    const float paperWidth = 210;   // A4宽度
    const float paperHeight = 297;  // A4高度
    
    // DPI设置 (1英寸 = 25.4毫米)
    const float dpi = 300;          // 打印分辨率
    const float pixelsPerMM = dpi / 25.4;
    
    // 生成ChArUco标定板
    const int squaresX = 5;         // X方向方格数量
    const int squaresY = 7;         // Y方向方格数量
    const int squareLength = 30;    // 每个方格的边长(毫米)
    const int markerLength = 23;    // 每个标记的边长(毫米)，略小于方格
    
    Mat charucoBoard = generateCharucoBoard(squaresX, squaresY, squareLength, markerLength,
                                          pixelsPerMM, paperWidth, paperHeight, dpi);
    
    // 生成ArUco定位板
    const int markersX = 5;          // X方向标记数量
    const int markersY = 7;          // Y方向标记数量
    const int arucoMarkerLength = 6; // 每个标记的边长(毫米)
    const int markerSeparation = 2;  // 标记之间的间隔(毫米)
    
    Mat arucoBoard = generateArucoBoard(markersX, markersY, arucoMarkerLength, markerSeparation,
                                      pixelsPerMM, paperWidth, paperHeight, dpi);
    
    // 保存图像
    imwrite("charuco_board.png", charucoBoard);
    imwrite("aruco_board.png", arucoBoard);
    
    std::cout << "ChArUco标定板已保存为 charuco_board.png" << std::endl;
    std::cout << "ArUco定位板已保存为 aruco_board.png" << std::endl;
    std::cout << "请用" << dpi << " DPI打印在A4纸上" << std::endl;
    
    return 0;
}
