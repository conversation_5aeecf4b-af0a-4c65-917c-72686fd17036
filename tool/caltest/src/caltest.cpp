#include <glog.h>
#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <vector>
#include <iostream>
#include <fstream>
#include "CameraCalibrator.h"
#include "CameraCapture.h"

using namespace std;
using namespace cv;

// 在文件开头添加函数声明
string currentDateTime();

int main(int argc, char** argv) {
    if (argc != 2) {
        std::cerr << "Usage: " << argv[0] << " <image_folder_path>" << std::endl;
        return -1;
    }
    
    // 从文件夹加载图像
    std::vector<cv::Mat> images = CameraCapture::loadImagesFromFolder(argv[1]);
    if (images.empty()) {
        std::cerr << "No images found in folder: " << argv[1] << std::endl;
        return -1;
    }
    
    // 创建标定器
    CameraCalibrator calibrator;
    
    // 预览所有图像的检测效果
    std::cout << "Previewing detection results. Press any key to advance, 'q' to quit." << std::endl;
    
    cv::namedWindow("Detection Preview", cv::WINDOW_NORMAL);
    for (size_t i = 0; i < images.size(); ++i) {
        cv::Mat output;
        bool detected = calibrator.previewDetection(images[i], output);
        
        // 显示图像序号和检测状态
        std::string status = detected ? "DETECTED" : "NOT DETECTED";
        cv::putText(output, "Image " + std::to_string(i+1) + "/" + 
                   std::to_string(images.size()) + " - " + status,
                   cv::Point(10, output.rows - 20), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.6, 
                   detected ? cv::Scalar(0, 255, 0) : cv::Scalar(0, 0, 255), 2);
        
        cv::imshow("Detection Preview", output);
        
        char key = cv::waitKey(0);
        if (key == 'q') {
            cv::destroyWindow("Detection Preview");
            return 0;
        }
    }
    cv::destroyWindow("Detection Preview");
    
    // 进行相机标定
    if (calibrator.calibrateFromImages(images)) {
        std::cout << "Camera calibration successful!" << std::endl;
        std::cout << "Reprojection error: " << calibrator.getReprojectionError() << std::endl;
        
        // 保存标定结果
        calibrator.saveCalibration("camera_calibration.yml");
    } else {
        std::cout << "Camera calibration failed!" << std::endl;
    }
    
    return 0;
}

// 辅助函数：获取当前时间字符串
string currentDateTime() {
    time_t now = time(0);
    struct tm tstruct;
    char buf[80];
    tstruct = *localtime(&now);
    strftime(buf, sizeof(buf), "%Y-%m-%d %X", &tstruct);
    return buf;
}
