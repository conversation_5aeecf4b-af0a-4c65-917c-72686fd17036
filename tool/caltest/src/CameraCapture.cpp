#include "CameraCapture.h"
#include <iostream>

CameraCapture::CameraCapture() : initialized(false) {}

bool CameraCapture::init(const std::string& deviceName, int width, int height) {
    if (!cap.open(deviceName, cv::CAP_V4L)) {
        std::cerr << "Failed to open camera: " << deviceName << std::endl;
        return false;
    }
    
    setupCamera(width, height);
    initialized = true;
    return true;
}

void CameraCapture::setupCamera(int width, int height) {
    cap.set(cv::CAP_PROP_FOURCC, cv::VideoWriter::fourcc('M','J','P','G'));
    cap.set(cv::CAP_PROP_FRAME_WIDTH, width);
    cap.set(cv::CAP_PROP_FRAME_HEIGHT, height);
    cap.set(cv::CAP_PROP_FPS, 30);
    cap.set(cv::CAP_PROP_AUTO_WB, 1);
    cap.set(cv::CAP_PROP_AUTO_EXPOSURE, 1);
}

cv::Mat CameraCapture::capture() {
    if (!initialized) {
        return cv::Mat();
    }
    
    cv::Mat frame;
    cap >> frame;
    return frame;
}

std::vector<cv::Mat> CameraCapture::captureCalibrationImages(int numImages) {
    std::vector<cv::Mat> images;
    
    if (!initialized) {
        std::cerr << "Camera not initialized!" << std::endl;
        return images;
    }
    
    std::cout << "Press SPACE to capture image, 'q' to quit" << std::endl;
    std::cout << "Need to capture " << numImages << " images" << std::endl;
    
    while (images.size() < numImages) {
        cv::Mat frame = capture();
        if (frame.empty()) break;
        
        // 显示已捕获的图像数量
        std::string text = "Captured: " + std::to_string(images.size()) + "/" + 
                          std::to_string(numImages);
        cv::putText(frame, text, cv::Point(10, 30), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.8, cv::Scalar(0, 255, 0), 2);
        
        cv::imshow("Camera Capture", frame);
        
        char key = cv::waitKey(1);
        if (key == ' ') {  // 空格键
            images.push_back(frame.clone());
            std::cout << "Image " << images.size() << " captured" << std::endl;
        }
        else if (key == 'q') {
            break;
        }
    }
    
    cv::destroyWindow("Camera Capture");
    return images;
}

void CameraCapture::release() {
    if (initialized) {
        cap.release();
        initialized = false;
    }
}

std::vector<cv::Mat> CameraCapture::loadImagesFromFolder(const std::string& folderPath) {
    std::vector<cv::Mat> images;
    std::vector<cv::String> filenames;
    
    // 获取文件夹中的所有图像文件
    cv::glob(folderPath + "/*.jpg", filenames);
    cv::glob(folderPath + "/*.png", filenames);
    
    if (filenames.empty()) {
        std::cerr << "No image files found in folder: " << folderPath << std::endl;
        return images;
    }
    
    // 读取所有图像
    for (const auto& filename : filenames) {
        cv::Mat img = cv::imread(filename);
        if (!img.empty()) {
            images.push_back(img);
            std::cout << "Loaded: " << filename << std::endl;
        } else {
            std::cerr << "Failed to load: " << filename << std::endl;
        }
    }
    
    return images;
} 