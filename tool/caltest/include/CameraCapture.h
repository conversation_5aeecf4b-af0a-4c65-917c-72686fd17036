#pragma once

#include <opencv2/opencv.hpp>
#include <string>
#include <vector>

class CameraCapture {
public:
    CameraCapture();
    
    // 初始化相机
    bool init(const std::string& deviceName = "/dev/video0",
             int width = 1920, int height = 1080);
    
    // 采集标定图像
    std::vector<cv::Mat> captureCalibrationImages(int numImages = 16);
    
    // 从文件夹加载图像
    static std::vector<cv::Mat> loadImagesFromFolder(const std::string& folderPath);
    
    // 单次捕获图像
    cv::Mat capture();
    
    // 关闭相机
    void release();
    
    // 获取VideoCapture对象的引用
    cv::VideoCapture& getVideoCapture() { return cap; }

private:
    cv::VideoCapture cap;
    bool initialized;
    
    // 相机参数设置
    void setupCamera(int width, int height);
}; 