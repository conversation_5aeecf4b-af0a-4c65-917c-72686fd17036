#pragma once

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include "rw/math.hpp"

class HandEyeCalibrator {
public:
    HandEyeCalibrator();
    
    // Add a new pair of poses for hand-eye calibration
    bool addPose(const rw::math::Transform3D<>& robotPose, 
                 const cv::Mat& boardRvec, const cv::Mat& boardTvec);
    
    // Set minimum required poses for calibration
    void setRequiredPosesCount(int count) { m_requiredPoses = count; }
    
    // Get minimum required poses for calibration
    int getRequiredPosesCount() const { return m_requiredPoses; }
    
    // Get number of collected poses
    size_t getCollectedPosesCount() const { return m_rotationGripperToBase.size(); }
    
    // Check if enough poses have been collected
    bool hasEnoughPoses() const { return getCollectedPosesCount() >= m_requiredPoses; }
    
    // Save robot joint poses for future use
    bool saveRobotPoses(const std::vector<rw::math::Q>& poses, const std::string& filename) const;
    
    // Load robot joint poses
    bool loadRobotPoses(std::vector<rw::math::Q>& poses, const std::string& filename) const;
    
    // Perform hand-eye calibration
    bool calibrate();
    
    // Save calibration results
    bool saveCalibration(const std::string& filename) const;
    
    // Get calibration results
    const cv::Mat& getRotationMatrix() const { return m_rotationCameraToGripper; }
    const cv::Mat& getTranslationVector() const { return m_translationCameraToGripper; }
    
    // Check if calibration has been performed
    bool isCalibrated() const { return m_calibrated; }
    
    // Convert RW Transform3D to OpenCV rotation matrix and translation vector
    static void transform3DToRt(const rw::math::Transform3D<>& transform, 
                                cv::Mat& R, cv::Mat& t);

private:
    // Collected pose data for calibration
    std::vector<cv::Mat> m_rotationGripperToBase;
    std::vector<cv::Mat> m_translationGripperToBase;
    std::vector<cv::Mat> m_rotationTargetToCamera;
    std::vector<cv::Mat> m_translationTargetToCamera;
    
    // Calibration results
    cv::Mat m_rotationCameraToGripper;
    cv::Mat m_translationCameraToGripper;
    
    bool m_calibrated;
    int m_requiredPoses;
}; 