#pragma once

#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <vector>
#include <string>

class CameraCalibrator {
public:
    CameraCalibrator();
    
    // Initialize the calibration board with custom parameters
    void initializeBoard(int squaresX = 5, int squaresY = 7, 
                         float squareLength = 0.006f, float markerLength = 0.0046f,
                         int dictionaryId = cv::aruco::DICT_4X4_50);
    
    // Process frame to detect markers and corners
    bool processFrame(const cv::Mat& frame);
    
    // Add current frame and detected corners to calibration data
    bool addCalibrationFrame(const cv::Mat& frame);
    
    // Perform camera calibration with collected data
    double calibrateCamera(const cv::Size& imageSize);
    
    // Save calibration results to file
    bool saveCalibration(const std::string& filename) const;
    
    // Load calibration from file
    bool loadCalibration(const std::string& filename);
    
    // Draw detected markers and corners on the frame
    void drawResults(cv::Mat& frame) const;
    
    // Estimate pose of the charuco board
    bool estimateBoardPose(cv::Mat& rvec, cv::Mat& tvec) const;
    
    // Check if camera is calibrated
    bool isCalibrated() const { return m_calibrated; }
    
    // Get number of collected calibration frames
    size_t getCollectedFramesCount() const { return m_calibrationImages.size(); }
    
    // Get camera matrix and distortion coefficients
    const cv::Mat& getCameraMatrix() const { return m_cameraMatrix; }
    const cv::Mat& getDistCoeffs() const { return m_distCoeffs; }
    
    // Get minimum frames needed for calibration
    int getMinimumFramesForCalibration() const { return 15; }
    
    // Get minimum detected corners for a valid frame
    int getMinimumCornersRequired() const { return 12; }

private:
    cv::Ptr<cv::aruco::Dictionary> m_dictionary;
    cv::Ptr<cv::aruco::CharucoBoard> m_board;
    bool m_calibrated;
    
    // Camera calibration results
    cv::Mat m_cameraMatrix;
    cv::Mat m_distCoeffs;
    
    // Calibration data
    std::vector<cv::Mat> m_calibrationImages;
    std::vector<std::vector<cv::Point2f>> m_allCorners;
    std::vector<std::vector<int>> m_allIds;
    std::vector<std::vector<cv::Point3f>> m_allObjectPoints;
    
    // Current frame detection results
    std::vector<std::vector<cv::Point2f>> m_markerCorners;
    std::vector<int> m_markerIds;
    std::vector<cv::Point2f> m_charucoCorners;
    std::vector<int> m_charucoIds;
}; 