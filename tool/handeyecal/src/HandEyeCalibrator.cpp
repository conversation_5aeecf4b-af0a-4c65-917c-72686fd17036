#include "HandEyeCalibrator.h"
#include <glog.h>
#include <ctime>

HandEyeCalibrator::HandEyeCalibrator() 
    : m_calibrated(false), m_requiredPoses(16) {
    m_rotationCameraToGripper = cv::Mat::eye(3, 3, CV_64F);
    m_translationCameraToGripper = cv::Mat::zeros(3, 1, CV_64F);
}

bool HandEyeCalibrator::addPose(const rw::math::Transform3D<>& robotPose, 
                               const cv::Mat& boardRvec, const cv::Mat& boardTvec) {
    // Convert robot pose to OpenCV format
    cv::Mat R_robot, t_robot;
    transform3DToRt(robotPose, R_robot, t_robot);
    
    // Convert board rotation vector to rotation matrix
    cv::Mat R_board;
    cv::Rodrigues(boardRvec, R_board);
    
    // Store the poses for calibration
    m_rotationGripperToBase.push_back(R_robot);
    m_translationGripperToBase.push_back(t_robot);
    m_rotationTargetToCamera.push_back(R_board);
    m_translationTargetToCamera.push_back(boardTvec.clone());
    
    LOG(INFO) << "Added pose pair #" << getCollectedPosesCount() 
              << " for hand-eye calibration";
    
    return true;
}

bool HandEyeCalibrator::saveRobotPoses(const std::vector<rw::math::Q>& poses, 
                                      const std::string& filename) const {
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open file for writing: " << filename;
        return false;
    }
    
    fs << "poses_count" << (int)poses.size();
    for (size_t i = 0; i < poses.size(); i++) {
        std::string pose_name = "pose_" + std::to_string(i);
        fs << pose_name << cv::Mat(poses[i].toStdVector());
    }
    fs.release();
    
    LOG(INFO) << "Saved " << poses.size() << " robot poses to " << filename;
    return true;
}

bool HandEyeCalibrator::loadRobotPoses(std::vector<rw::math::Q>& poses, 
                                      const std::string& filename) const {
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open file: " << filename;
        return false;
    }
    
    int poses_count;
    fs["poses_count"] >> poses_count;
    poses.clear();
    
    for (int i = 0; i < poses_count; i++) {
        cv::Mat pose_mat;
        fs["pose_" + std::to_string(i)] >> pose_mat;
        std::vector<double> pose_vec;
        pose_mat.copyTo(pose_vec);
        poses.push_back(rw::math::Q(pose_vec));
    }
    fs.release();
    
    LOG(INFO) << "Loaded " << poses.size() << " robot poses from " << filename;
    return true;
}

bool HandEyeCalibrator::calibrate() {
    if (!hasEnoughPoses()) {
        LOG(ERROR) << "Not enough pose pairs for calibration. Need at least " 
                   << m_requiredPoses << ", have " << getCollectedPosesCount();
        return false;
    }
    
    LOG(INFO) << "Starting hand-eye calibration with " 
              << getCollectedPosesCount() << " pose pairs...";
    
    // Perform calibration using Tsai's method
    cv::calibrateHandEye(
        m_rotationGripperToBase, m_translationGripperToBase,
        m_rotationTargetToCamera, m_translationTargetToCamera,
        m_rotationCameraToGripper, m_translationCameraToGripper,
        cv::CALIB_HAND_EYE_TSAI);
    
    m_calibrated = true;
    
    LOG(INFO) << "Hand-eye calibration complete";
    LOG(INFO) << "Rotation matrix:\n" << m_rotationCameraToGripper;
    LOG(INFO) << "Translation vector:\n" << m_translationCameraToGripper;
    
    return true;
}

bool HandEyeCalibrator::saveCalibration(const std::string& filename) const {
    if (!m_calibrated) {
        LOG(ERROR) << "Cannot save calibration: Hand-eye calibration not performed yet";
        return false;
    }
    
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open file for writing: " << filename;
        return false;
    }
    
    time_t now = time(0);
    fs << "calibration_time" << ctime(&now);
    fs << "rotation_matrix" << m_rotationCameraToGripper;
    fs << "translation_vector" << m_translationCameraToGripper;
    fs.release();
    
    LOG(INFO) << "Hand-eye calibration saved to " << filename;
    return true;
}

void HandEyeCalibrator::transform3DToRt(const rw::math::Transform3D<>& transform, 
                                       cv::Mat& R, cv::Mat& t) {
    // Extract rotation matrix
    R = cv::Mat::eye(3, 3, CV_64F);
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            R.at<double>(i, j) = transform.R()(i, j);
        }
    }
    
    // Extract translation vector
    t = cv::Mat(3, 1, CV_64F);
    t.at<double>(0, 0) = transform.P()[0];
    t.at<double>(1, 0) = transform.P()[1];
    t.at<double>(2, 0) = transform.P()[2];
} 