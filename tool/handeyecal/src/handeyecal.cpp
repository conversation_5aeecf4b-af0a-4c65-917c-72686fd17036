#include <opencv2/opencv.hpp>
#include <opencv2/aruco.hpp>
#include <opencv2/aruco/charuco.hpp>
#include <vector>
#include <iostream>
#include <fstream>
#include <glog.h>
// 替换机器人驱动头文件
#include "AuboArcsDriver.h"
#include "rw/math.hpp"
// 添加海康相机头文件
#include "HikVisionCamera.h"

using namespace std;
using namespace cv;
// 移除不再需要的命名空间
// using namespace aubo_robot_namespace;

// 全局变量
// 替换机器人类型
std::shared_ptr<AuboArcsDriver> g_robot;
std::shared_ptr<HikVisionCamera> g_camera; // 添加海康相机对象
std::vector<cv::Mat> g_rotationGripperToBase;
std::vector<cv::Mat> g_translationGripperToBase;
std::vector<cv::Mat> g_rotationTargetToCamera;
std::vector<cv::Mat> g_translationTargetToCamera;
// 添加用于相机标定的变量
std::vector<std::vector<cv::Point2f>> g_allCorners;
std::vector<std::vector<cv::Point3f>> g_allObjectPoints;
std::vector<std::vector<int>> g_allIds;  // 修改为二维向量，存储每张图像的角点ID
std::vector<cv::Mat> g_calibrationImages;

// 将RW数学库的Transform3D转换为OpenCV的R和t
void transform3DToRt( rw::math::Transform3D<> transform, cv::Mat& R, cv::Mat& t) {
    // 提取旋转矩阵
    R = cv::Mat::eye(3, 3, CV_64F);
    for(int i = 0; i < 3; i++) {
        for(int j = 0; j < 3; j++) {
            R.at<double>(i,j) = transform.R()(i,j);
        }
    }
    
    // 提取平移向量
    t = cv::Mat(3, 1, CV_64F);
    t.at<double>(0,0) = transform.P()[0];
    t.at<double>(1,0) = transform.P()[1];
    t.at<double>(2,0) = transform.P()[2];
}

// 全局变量
const int REQUIRED_POSES = 16;
std::vector<rw::math::Q> g_savedRobotPoses;

// 保存机器人位姿函数
void saveRobotPoses(const std::vector<rw::math::Q>& poses, const std::string& filename) {
    FileStorage fs(filename, FileStorage::WRITE);
    fs << "poses_count" << (int)poses.size();
    for (size_t i = 0; i < poses.size(); i++) {
        std::string pose_name = "pose_" + std::to_string(i);
        fs << pose_name << Mat(poses[i].toStdVector());
    }
    fs.release();
}

// 加载机器人位姿函数
bool loadRobotPoses(std::vector<rw::math::Q>& poses, const std::string& filename) {
    FileStorage fs(filename, FileStorage::READ);
    if (!fs.isOpened()) return false;
    
    int poses_count;
    fs["poses_count"] >> poses_count;
    poses.clear();
    
    for (int i = 0; i < poses_count; i++) {
        Mat pose_mat;
        fs["pose_" + std::to_string(i)] >> pose_mat;
        std::vector<double> pose_vec;
        pose_mat.copyTo(pose_vec);
        poses.push_back(rw::math::Q(pose_vec));
    }
    fs.release();
    return true;
}

// 修改 getRobotPose 函数，使用AuboArcsDriver的API
rw::math::Transform3D<> getRobotPose(std::shared_ptr<AuboArcsDriver>& robot) {
    return robot->getRobotActualTCP();
}

int main(int argc, char** argv) {
    // 初始化glog
//    google::InitGoogleLogging(argv[0]);
//    FLAGS_colorlogtostderr = true;
//    FLAGS_logtostderr = true;

    // 初始化机器人，使用AuboArcsDriver
    g_robot = std::make_shared<AuboArcsDriver>();
    if (!g_robot->connect("192.168.1.189", 8899)) {
        LOG(ERROR) << "无法连接机器人";
        return -1;
    }

    // 初始化海康相机
    g_camera = std::make_shared<HikVisionCamera>();
    // 设置错误回调函数
    g_camera->setErrorCallback([](const std::string& error) {
        LOG(ERROR) << "相机错误: " << error;
    });

    // 连接到相机，使用相机序列号
    if (!g_camera->connectToDevice("*********")) { // 请替换为实际的相机序列号
        LOG(ERROR) << "无法连接相机";
        return -1;
    }
    
    // 设置相机参数
    g_camera->setExposureTime(120000.0f); // 曝光时间，单位微秒
    g_camera->setGain(0.0f);             // 增益
    g_camera->setFrameRate(30.0f);       // 帧率
    
    // 开始采集图像
    if (!g_camera->startGrabbing()) {
        LOG(ERROR) << "无法开始图像采集";
        return -1;
    }

    // 创建ChArUco标定板
    // 修复：使用 Ptr<Dictionary> 而不是 auto

    cv::aruco::Dictionary dictionarytmp = cv::aruco::getPredefinedDictionary(cv::aruco::DICT_4X4_50);
    cv::Ptr<cv::aruco::Dictionary> dictionary=cv::Ptr<cv::aruco::Dictionary>(new cv::aruco::Dictionary(dictionarytmp));


    const int squaresX = 5;
    const int squaresY = 7;
    const float squareLength = 0.0112f;
    const float markerLength = 0.0087f;

    // 修复：直接使用构造函数创建标定板
    cv::Ptr<cv::aruco::CharucoBoard> board = cv::Ptr<cv::aruco::CharucoBoard>(
        new cv::aruco::CharucoBoard({squaresX, squaresY}, squareLength, markerLength, *dictionary));



    // 初始化相机内参和畸变系数为空，稍后会通过标定计算得到
    Mat cameraMatrix, distCoeffs;
    bool cameraCalibrated = false;

    // 尝试加载保存的位姿
    bool autoCalibration = loadRobotPoses(g_savedRobotPoses, "robot_poses.yml");

    int poseCount = 0;
    bool teachingMode = !autoCalibration;

    while (true) {
        // 使用海康相机获取图像
        Mat frame = g_camera->getFrame();
        if (frame.empty()) {
            LOG(ERROR) << "无法读取图像";
            continue; // 尝试再次获取图像，而不是直接退出
        }

        // 检测ChArUco角点
        vector<vector<Point2f>> markerCorners;
        vector<int> markerIds;
        vector<Point2f> charucoCorners;
        vector<int> charucoIds;
//        imshow("Hand-Eye Calibration", frame);
        // 修复：使用正确的参数类型调用 detectMarkers
        cv::aruco::detectMarkers(frame, dictionary, markerCorners, markerIds);

        if (markerIds.size() > 0) {
            aruco::drawDetectedMarkers(frame, markerCorners, markerIds);

            // 如果相机已标定，使用标定参数进行角点插值
            if (cameraCalibrated) {
                aruco::interpolateCornersCharuco(markerCorners, markerIds, frame, board,
                                               charucoCorners, charucoIds, cameraMatrix, distCoeffs);
            } else {
                // 相机未标定，不使用相机参数进行角点插值
                aruco::interpolateCornersCharuco(markerCorners, markerIds, frame, board,
                                               charucoCorners, charucoIds);
            }

            if (charucoIds.size() > 0) {
                aruco::drawDetectedCornersCharuco(frame, charucoCorners, charucoIds);

                // 根据模式更新显示文本
                string modeText;
                if (!cameraCalibrated) {
                    modeText = "CAMERA CALIBRATION MODE - Collected: " + std::to_string(g_calibrationImages.size());
                } else {
                    modeText = teachingMode ? "TEACHING MODE" : "AUTO CALIBRATION MODE";
                }
                putText(frame, modeText, Point(10, 60), FONT_HERSHEY_SIMPLEX, 0.8, Scalar(0, 255, 0), 2);

                // 空格键处理
                if (waitKey(1) == 32) {
                    // 如果相机未标定且检测到足够的角点，收集标定数据
                    if (!cameraCalibrated && charucoIds.size() >= 12) {
                        // 保存当前图像用于标定
                        g_calibrationImages.push_back(frame.clone());

                        // 保存角点信息
                        g_allCorners.push_back(charucoCorners);
                        g_allIds.push_back(charucoIds);  // 现在可以正确添加了

                        // 计算标定板上角点的3D坐标
                        std::vector<cv::Point3f> objectPoints;
                        for (int i = 0; i < charucoIds.size(); i++) {
                            int pointId = charucoIds[i];
                            cv::Point3f objPt = board->getChessboardCorners()[pointId];
                            objectPoints.push_back(objPt);
                        }
                        g_allObjectPoints.push_back(objectPoints);

                        LOG(INFO) << "收集标定图像 #" << g_calibrationImages.size();

                        // 当收集到足够的图像后，进行相机标定
                        if (g_calibrationImages.size() >= 15) {
                            LOG(INFO) << "开始相机标定...";

                            // 初始化相机矩阵
                            cameraMatrix = cv::Mat::eye(3, 3, CV_64F);
                            cameraMatrix.at<double>(0, 0) = 1000.0; // 初始焦距估计
                            cameraMatrix.at<double>(1, 1) = 1000.0;
                            cameraMatrix.at<double>(0, 2) = frame.cols / 2.0; // 主点估计
                            cameraMatrix.at<double>(1, 2) = frame.rows / 2.0;

                            distCoeffs = cv::Mat::zeros(5, 1, CV_64F);

                            std::vector<cv::Mat> rvecs, tvecs;
                            double repError = cv::calibrateCamera(
                                g_allObjectPoints, g_allCorners, frame.size(),
                                cameraMatrix, distCoeffs, rvecs, tvecs,
                                cv::CALIB_FIX_ASPECT_RATIO | cv::CALIB_ZERO_TANGENT_DIST);

                            LOG(INFO) << "相机标定完成，重投影误差: " << repError;

                            // 保存相机标定结果
                            FileStorage fs("camera_calibration.yml", FileStorage::WRITE);
                            time_t now = time(0);
                            fs << "calibration_time" << ctime(&now);
                            fs << "camera_matrix" << cameraMatrix;
                            fs << "distortion_coefficients" << distCoeffs;
                            fs << "reprojection_error" << repError;
                            fs.release();

                            cameraCalibrated = true;
                            LOG(INFO) << "相机标定参数已保存，现在开始手眼标定";
                        }
                    }
                    // 如果相机已标定，进行手眼标定
                    else if (cameraCalibrated && charucoIds.size() >= 12 && poseCount < REQUIRED_POSES) {
                        // 原有的手眼标定代码
                        if (teachingMode) {
                            // 获取当前机器人关节位置
                            rw::math::Q currentJointPos = g_robot->getRobotJointQ();

                            // 保存关节角度
                            g_savedRobotPoses.push_back(currentJointPos);

                            // 移动机器人 - 使用AuboArcsDriver的API
                            if (!g_robot->movej(currentJointPos, 0.5, 10, 0.5)) {
                                LOG(ERROR) << "机器人移动失败";
                                continue;
                            }

                            // 等待移动完成 - 使用Sleep替代
                            Sleep(1000);

                            // 获取机器人末端位姿用于标定
                            rw::math::Transform3D<> robotPose = getRobotPose(g_robot);

                            // 估计标定板位姿
                            cv::Mat rvec, tvec;
                            bool valid = cv::aruco::estimatePoseCharucoBoard(
                                charucoCorners, charucoIds, board, cameraMatrix, distCoeffs,
                                rvec, tvec);

                            if (valid) {
                                // 转换机器人位姿到OpenCV格式
                                cv::Mat R_robot, t_robot;
                                transform3DToRt(robotPose, R_robot, t_robot);

                                // 转换标定板位姿到旋转矩阵
                                cv::Mat R_board;
                                cv::Rodrigues(rvec, R_board);

                                // 保存位姿数据
                                g_rotationGripperToBase.push_back(R_robot);
                                g_translationGripperToBase.push_back(t_robot);
                                g_rotationTargetToCamera.push_back(R_board);
                                g_translationTargetToCamera.push_back(tvec);

                                poseCount++;
                                LOG(INFO) << "记录第 " << poseCount << " 组位姿数据 (示教模式)";

                                if (poseCount >= REQUIRED_POSES) {
                                    // 保存机器人位姿以供将来使用
                                    saveRobotPoses(g_savedRobotPoses, "robot_poses.yml");
                                    LOG(INFO) << "保存机器人位姿完成";
                                }
                            }
                        } else {
                            // 自动标定模式
                            if (poseCount >= g_savedRobotPoses.size()) {
                                LOG(ERROR) << "没有足够的保存位姿";
                                continue;
                            }

                            // 移动到保存的位姿 - 使用AuboArcsDriver的API
                            const rw::math::Q& targetJointPos = g_savedRobotPoses[poseCount];
                            if (!g_robot->movej(targetJointPos, 0.5, 10, 0.5)) {
                                LOG(ERROR) << "机器人移动失败";
                                continue;
                            }

                            // 等待移动完成
                            Sleep(1000);

                            // 获取机器人末端位姿用于标定
                            auto robotPose = getRobotPose(g_robot);

                            // 估计标定板位姿
                            cv::Mat rvec, tvec;
                            // 修复：移除错误的 & 符号，使用正确的指针语法
                            bool valid = cv::aruco::estimatePoseCharucoBoard(
                                charucoCorners, charucoIds, board, cameraMatrix, distCoeffs,
                                rvec, tvec);
                                
                            if (valid) {
                                // 转换机器人位姿到OpenCV格式
                                cv::Mat R_robot, t_robot;
                                transform3DToRt(robotPose, R_robot, t_robot);
                                
                                // 转换标定板位姿到旋转矩阵
                                cv::Mat R_board;
                                cv::Rodrigues(rvec, R_board);
                                
                                // 保存位姿数据
                                g_rotationGripperToBase.push_back(R_robot);
                                g_translationGripperToBase.push_back(t_robot);
                                g_rotationTargetToCamera.push_back(R_board);
                                g_translationTargetToCamera.push_back(tvec);
                                
                                poseCount++;
                                LOG(INFO) << "记录第 " << poseCount << " 组位姿数据 (自动模式)";
                            }
                        }
                    }
                }
            }
        }

        // 更新操作指南文本
        string guideText;
        if (!cameraCalibrated) {
            guideText = "Camera Calibration: Move board and press SPACE to capture | 'q' to quit";
        } else {
            guideText = teachingMode ? 
                "Teaching Mode: Move robot and press SPACE to capture | 'q' to quit" :
                "Auto Mode: Press SPACE to start calibration | 'q' to quit";
        }
        putText(frame, guideText, Point(10, frame.rows - 20), 
                FONT_HERSHEY_SIMPLEX, 0.6, Scalar(255, 255, 255), 2);
        namedWindow("Hand-Eye Calibration",cv::WINDOW_NORMAL);
        resizeWindow("Hand-Eye Calibration",1280,720);
        imshow("Hand-Eye Calibration", frame);

        // 检查是否完成或退出
        char key = waitKey(1);
        if (key == 'q' || (cameraCalibrated && poseCount >= REQUIRED_POSES)) {
            if (cameraCalibrated && poseCount >= REQUIRED_POSES) {
                LOG(INFO) << "开始手眼标定计算...";
                
                // 执行手眼标定
                cv::Mat R_cam2gripper, t_cam2gripper;
                cv::calibrateHandEye(g_rotationGripperToBase, g_translationGripperToBase,
                                   g_rotationTargetToCamera, g_translationTargetToCamera,
                                   R_cam2gripper, t_cam2gripper,
                                   cv::CALIB_HAND_EYE_TSAI);

                // 保存标定结果
                FileStorage fs("hand_eye_calibration.yml", FileStorage::WRITE);
                time_t now = time(0);
                fs << "calibration_time" << ctime(&now);
                fs << "rotation_matrix" << R_cam2gripper;
                fs << "translation_vector" << t_cam2gripper;
                fs.release();

                LOG(INFO) << "手眼标定完成";
                LOG(INFO) << "旋转矩阵:\n" << R_cam2gripper;
                LOG(INFO) << "平移向量:\n" << t_cam2gripper;
            }
            break;
        }
    }

    // 关闭资源
    g_robot->stop();
    g_camera->stopGrabbing(); // 停止图像采集
    g_camera->disconnect();   // 断开相机连接
    // cap.release(); // 删除旧的相机释放代码
    destroyAllWindows();

    return 0;
}