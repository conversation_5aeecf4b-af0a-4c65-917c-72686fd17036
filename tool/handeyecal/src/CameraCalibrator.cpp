#include "CameraCalibrator.h"
#include <fstream>
#include <glog.h>
#include <ctime>

CameraCalibrator::CameraCalibrator() : m_calibrated(false) {
    m_cameraMatrix = cv::Mat::eye(3, 3, CV_64F);
    m_distCoeffs = cv::Mat::zeros(5, 1, CV_64F);
    
    // Default initialization with standard parameters
    initializeBoard();
}

void CameraCalibrator::initializeBoard(int squaresX, int squaresY, 
                                     float squareLength, float markerLength,
                                     int dictionaryId) {
    // Set up ArUco dictionary
    cv::aruco::Dictionary dictionaryTemplate = 
        cv::aruco::getPredefinedDictionary(dictionaryId);
    m_dictionary = cv::Ptr<cv::aruco::Dictionary>(
        new cv::aruco::Dictionary(dictionaryTemplate));
    
    // Create CharUco board
    m_board = cv::Ptr<cv::aruco::CharucoBoard>(
        new cv::aruco::CharucoBoard({squaresX, squaresY}, squareLength, markerLength, *m_dictionary));
}

bool CameraCalibrator::processFrame(const cv::Mat& frame) {
    // Detect markers
    cv::aruco::detectMarkers(frame, m_dictionary, m_markerCorners, m_markerIds);
    
    if (m_markerIds.empty()) {
        m_charucoCorners.clear();
        m_charucoIds.clear();
        return false;
    }
    
    // Interpolate corners
    if (m_calibrated) {
        cv::aruco::interpolateCornersCharuco(
            m_markerCorners, m_markerIds, frame, m_board,
            m_charucoCorners, m_charucoIds, m_cameraMatrix, m_distCoeffs);
    } else {
        cv::aruco::interpolateCornersCharuco(
            m_markerCorners, m_markerIds, frame, m_board,
            m_charucoCorners, m_charucoIds);
    }
    
    return !m_charucoCorners.empty();
}

bool CameraCalibrator::addCalibrationFrame(const cv::Mat& frame) {
    if (m_charucoIds.size() < getMinimumCornersRequired()) {
        LOG(WARNING) << "Not enough corners detected for calibration";
        return false;
    }
    
    // Save the current image
    m_calibrationImages.push_back(frame.clone());
    
    // Save detected corners
    m_allCorners.push_back(m_charucoCorners);
    m_allIds.push_back(m_charucoIds);
    
    // Calculate object points
    std::vector<cv::Point3f> objectPoints;
    for (int id : m_charucoIds) {
        cv::Point3f objPt = m_board->getChessboardCorners()[id];
        objectPoints.push_back(objPt);
    }
    m_allObjectPoints.push_back(objectPoints);
    
    LOG(INFO) << "Added calibration frame #" << m_calibrationImages.size()
              << " with " << m_charucoIds.size() << " corners";
    
    return true;
}

double CameraCalibrator::calibrateCamera(const cv::Size& imageSize) {
    if (m_calibrationImages.size() < getMinimumFramesForCalibration()) {
        LOG(ERROR) << "Not enough calibration frames. Need at least " 
                   << getMinimumFramesForCalibration();
        return -1.0;
    }
    
    LOG(INFO) << "Starting camera calibration with "
              << m_calibrationImages.size() << " frames...";
    
    // Initialize camera matrix with reasonable default values
    m_cameraMatrix = cv::Mat::eye(3, 3, CV_64F);
    m_cameraMatrix.at<double>(0, 0) = 1000.0; // Initial focal length estimate
    m_cameraMatrix.at<double>(1, 1) = 1000.0;
    m_cameraMatrix.at<double>(0, 2) = imageSize.width / 2.0;  // Principal point estimate
    m_cameraMatrix.at<double>(1, 2) = imageSize.height / 2.0;
    
    m_distCoeffs = cv::Mat::zeros(5, 1, CV_64F);
    
    std::vector<cv::Mat> rvecs, tvecs;
    double repError = cv::calibrateCamera(
        m_allObjectPoints, m_allCorners, imageSize,
        m_cameraMatrix, m_distCoeffs, rvecs, tvecs,
        cv::CALIB_FIX_ASPECT_RATIO | cv::CALIB_ZERO_TANGENT_DIST);
    
    LOG(INFO) << "Camera calibration complete, reprojection error: " << repError;
    m_calibrated = true;
    
    return repError;
}

bool CameraCalibrator::saveCalibration(const std::string& filename) const {
    if (!m_calibrated) {
        LOG(ERROR) << "Cannot save calibration: Camera not calibrated yet";
        return false;
    }
    
    cv::FileStorage fs(filename, cv::FileStorage::WRITE);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open file for writing: " << filename;
        return false;
    }
    
    time_t now = time(0);
    fs << "calibration_time" << ctime(&now);
    fs << "camera_matrix" << m_cameraMatrix;
    fs << "distortion_coefficients" << m_distCoeffs;
    fs.release();
    
    LOG(INFO) << "Camera calibration saved to " << filename;
    return true;
}

bool CameraCalibrator::loadCalibration(const std::string& filename) {
    cv::FileStorage fs(filename, cv::FileStorage::READ);
    if (!fs.isOpened()) {
        LOG(ERROR) << "Failed to open calibration file: " << filename;
        return false;
    }
    
    fs["camera_matrix"] >> m_cameraMatrix;
    fs["distortion_coefficients"] >> m_distCoeffs;
    fs.release();
    
    if (m_cameraMatrix.empty() || m_distCoeffs.empty()) {
        LOG(ERROR) << "Invalid calibration data in file: " << filename;
        return false;
    }
    
    m_calibrated = true;
    LOG(INFO) << "Camera calibration loaded from " << filename;
    return true;
}

void CameraCalibrator::drawResults(cv::Mat& frame) const {
    if (!m_markerIds.empty()) {
        cv::aruco::drawDetectedMarkers(frame, m_markerCorners, m_markerIds);
    }
    
    if (!m_charucoCorners.empty()) {
        cv::aruco::drawDetectedCornersCharuco(frame, m_charucoCorners, m_charucoIds);
    }
}

bool CameraCalibrator::estimateBoardPose(cv::Mat& rvec, cv::Mat& tvec) const {
    if (!m_calibrated || m_charucoCorners.empty() || m_charucoIds.empty()) {
        return false;
    }
    
    return cv::aruco::estimatePoseCharucoBoard(
        m_charucoCorners, m_charucoIds, m_board,
        m_cameraMatrix, m_distCoeffs, rvec, tvec);
} 