#include "RestInterfaceDriver.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <algorithm>

namespace AnalysisRobot {
namespace RestInterface {

RestInterfaceDriver::RestInterfaceDriver()
    : m_running(false)
    , m_nextTaskId(1000) {
}

RestInterfaceDriver::~RestInterfaceDriver() {
    stop();
}

bool RestInterfaceDriver::initialize(const RestConfig& config) {
    m_config = config;
    
    // 创建HTTP服务器
    m_server = std::make_unique<httplib::Server>();
    
    // 初始化默认路由
    initializeDefaultRoutes();
    
    log("INFO", "REST interface driver initialized");
    return true;
}

bool RestInterfaceDriver::start() {
    if (m_running) {
        setError("Server is already running");
        return false;
    }
    
    if (!m_server) {
        setError("Server not initialized");
        return false;
    }
    
    m_running = true;
    m_serverThread = std::make_unique<std::thread>(&RestInterfaceDriver::serverLoop, this);
    
    log("INFO", "REST server started on " + m_config.host + ":" + std::to_string(m_config.port));
    return true;
}

void RestInterfaceDriver::stop() {
    if (m_running) {
        m_running = false;
        if (m_server) {
            m_server->stop();
        }
        if (m_serverThread && m_serverThread->joinable()) {
            m_serverThread->join();
        }
        log("INFO", "REST server stopped");
    }
}

bool RestInterfaceDriver::isRunning() const {
    return m_running;
}

void RestInterfaceDriver::registerDeviceController(const std::string& deviceName, 
                                                  std::shared_ptr<IDeviceController> controller) {
    m_deviceControllers[deviceName] = controller;
    log("INFO", "Device controller registered: " + deviceName);
}

void RestInterfaceDriver::registerGetRoute(const std::string& path, RequestHandler handler) {
    if (m_server) {
        m_server->Get(path, handler);
        log("INFO", "GET route registered: " + path);
    }
}

void RestInterfaceDriver::registerPostRoute(const std::string& path, RequestHandler handler) {
    if (m_server) {
        m_server->Post(path, handler);
        log("INFO", "POST route registered: " + path);
    }
}

std::string RestInterfaceDriver::getServerInfo() const {
    std::ostringstream oss;
    oss << "REST Interface Driver\n";
    oss << "Host: " << m_config.host << "\n";
    oss << "Port: " << m_config.port << "\n";
    oss << "Status: " << (m_running ? "Running" : "Stopped") << "\n";
    oss << "Registered Devices: " << m_deviceControllers.size() << "\n";
    return oss.str();
}

std::string RestInterfaceDriver::getLastError() const {
    return m_lastError;
}

void RestInterfaceDriver::serverLoop() {
    log("INFO", "Server loop started");
    
    if (m_server) {
        // 启动HTTP服务器，这是阻塞调用
        bool result = m_server->listen(m_config.host, m_config.port);
        if (!result) {
            setError("Failed to start HTTP server on " + m_config.host + ":" + std::to_string(m_config.port));
        }
    }
    
    log("INFO", "Server loop ended");
}

void RestInterfaceDriver::initializeDefaultRoutes() {
    if (!m_server) return;
    
    // 健康检查端点
    m_server->Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        Json::Value response;
        response["status"] = "healthy";
        response["timestamp"] = getCurrentTimeString();
        createJsonResponse(res, response);
    });
    
    // 服务器信息端点
    m_server->Get("/info", [this](const httplib::Request& req, httplib::Response& res) {
        Json::Value response;
        response["server"] = "Analysis Robot REST Interface";
        response["version"] = "1.0.0";
        response["timestamp"] = getCurrentTimeString();
        createJsonResponse(res, response);
    });
    
    // 注册设备API路由
    setupDeviceRoutes();
}

void RestInterfaceDriver::setupDeviceRoutes() {
    if (!m_server) return;
    
    // 设备状态查询路由
    m_server->Get(R"(/api/(\w+)/status)", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    // 设备操作路由
    m_server->Post(R"(/api/(\w+)/operation)", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 任务查询路由
    m_server->Get(R"(/api/(\w+)/query)", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 具体设备路由
    setupSpecificDeviceRoutes();
}

void RestInterfaceDriver::setupSpecificDeviceRoutes() {
    if (!m_server) return;
    
    // 进样传送装置
    m_server->Get("/api/sampleEntry/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    m_server->Post("/api/sampleEntry/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 出样传送装置
    m_server->Get("/api/sampleExit/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 容器货架装置
    m_server->Get("/api/repo/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/repo/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 机械臂
    m_server->Get("/api/robot/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/robot/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/robot/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 称量装置
    m_server->Get("/api/balance/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/balance/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/balance/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 倒样称量装置
    m_server->Get("/api/sampleBalance/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/sampleBalance/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/sampleBalance/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 加液装置
    m_server->Post("/api/dosing/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/dosing/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 定容装置
    m_server->Post("/api/volume/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/volume/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 过滤装置
    m_server->Post("/api/filter/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/filter/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 搅拌装置
    m_server->Post("/api/stir/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 搅拌加热装置
    m_server->Get("/api/heater/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/heater/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // ICP进样装置
    m_server->Post("/api/icpEntry/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/icpEntry/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 水分检测仪
    m_server->Get("/api/moistureBalance/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/moistureBalance/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/moistureBalance/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 培养皿摇床
    m_server->Post("/api/shaker/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 框架装置
    m_server->Get("/api/frame/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/frame/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
}

void RestInterfaceDriver::handleDeviceStatus(const httplib::Request& req, httplib::Response& res) {
    auto pathComponents = parseUriPath(req.path);
    if (pathComponents.size() < 2) {
        createErrorResponse(res, "Invalid device path", 400);
        return;
    }

    std::string deviceName = pathComponents[1];
    auto it = m_deviceControllers.find(deviceName);
    if (it == m_deviceControllers.end()) {
        createErrorResponse(res, "Device not found: " + deviceName, 404);
        return;
    }

    try {
        DeviceStatus status = it->second->getStatus();

        Json::Value response;
        response["name"] = status.name;
        response["description"] = status.description;
        response["status"] = status.status;
        response["message"] = status.message;
        response["data"] = status.data;
        response["updateTime"] = status.updateTime;

        createJsonResponse(res, response);
    } catch (const std::exception& e) {
        createErrorResponse(res, "Failed to get device status: " + std::string(e.what()), 500);
    }
}

void RestInterfaceDriver::handleDeviceOperation(const httplib::Request& req, httplib::Response& res) {
    auto pathComponents = parseUriPath(req.path);
    if (pathComponents.size() < 2) {
        createErrorResponse(res, "Invalid device path", 400);
        return;
    }

    std::string deviceName = pathComponents[1];
    auto it = m_deviceControllers.find(deviceName);
    if (it == m_deviceControllers.end()) {
        createErrorResponse(res, "Device not found: " + deviceName, 404);
        return;
    }

    try {
        Json::Value requestData;
        Json::Reader reader;
        if (!reader.parse(req.body, requestData)) {
            createErrorResponse(res, "Invalid JSON in request body", 400);
            return;
        }

        TaskInfo task = it->second->executeOperation(requestData);

        // 存储任务信息
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }

        Json::Value response;
        response["taskId"] = task.taskId;
        response["action"] = task.action;
        response["status"] = static_cast<int>(task.status);
        response["message"] = task.message;
        response["data"] = task.data;
        response["updateTime"] = task.updateTime;

        createJsonResponse(res, response);
    } catch (const std::exception& e) {
        createErrorResponse(res, "Failed to execute operation: " + std::string(e.what()), 500);
    }
}

void RestInterfaceDriver::handleTaskQuery(const httplib::Request& req, httplib::Response& res) {
    // 从查询参数中获取taskId
    auto it = req.params.find("taskId");
    if (it == req.params.end()) {
        createErrorResponse(res, "Missing taskId parameter", 400);
        return;
    }

    int taskId;
    try {
        taskId = std::stoi(it->second);
    } catch (const std::exception& e) {
        createErrorResponse(res, "Invalid taskId parameter", 400);
        return;
    }

    std::lock_guard<std::mutex> lock(m_tasksMutex);
    auto taskIt = m_tasks.find(taskId);
    if (taskIt == m_tasks.end()) {
        createErrorResponse(res, "Task not found", 404);
        return;
    }

    const TaskInfo& task = taskIt->second;

    Json::Value response;
    response["taskId"] = task.taskId;
    response["action"] = task.action;
    response["status"] = static_cast<int>(task.status);
    response["message"] = task.message;
    response["data"] = task.data;
    response["updateTime"] = task.updateTime;

    createJsonResponse(res, response);
}

std::vector<std::string> RestInterfaceDriver::parseUriPath(const std::string& uri) {
    std::vector<std::string> components;
    std::string path = uri;

    // 移除查询参数
    size_t queryPos = path.find('?');
    if (queryPos != std::string::npos) {
        path = path.substr(0, queryPos);
    }

    // 移除开头的斜杠
    if (!path.empty() && path[0] == '/') {
        path = path.substr(1);
    }

    // 分割路径
    std::istringstream iss(path);
    std::string component;
    while (std::getline(iss, component, '/')) {
        if (!component.empty()) {
            components.push_back(component);
        }
    }

    return components;
}

void RestInterfaceDriver::createJsonResponse(httplib::Response& res, const Json::Value& data, int statusCode) {
    res.status = statusCode;
    res.set_header("Content-Type", "application/json");

    Json::StreamWriterBuilder builder;
    res.body = Json::writeString(builder, data);
}

void RestInterfaceDriver::createErrorResponse(httplib::Response& res, const std::string& message, int statusCode) {
    Json::Value error;
    error["error"] = message;
    error["timestamp"] = getCurrentTimeString();

    createJsonResponse(res, error, statusCode);
}

int RestInterfaceDriver::generateTaskId() {
    return m_nextTaskId++;
}

std::string RestInterfaceDriver::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

void RestInterfaceDriver::setError(const std::string& error) {
    m_lastError = error;
    log("ERROR", error);
}

void RestInterfaceDriver::log(const std::string& level, const std::string& message) {
    if (m_config.enableLogging) {
        std::cout << "[" << getCurrentTimeString() << "] [" << level << "] " << message << std::endl;
    }
}

} // namespace RestInterface
} // namespace AnalysisRobot
