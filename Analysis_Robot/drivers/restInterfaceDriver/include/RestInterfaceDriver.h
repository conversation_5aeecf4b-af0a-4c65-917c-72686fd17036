#ifndef REST_INTERFACE_DRIVER_H
#define REST_INTERFACE_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <map>
#include <thread>
#include <atomic>
#include <mutex>
#include <json/json.h>
#include "httplib.h"

namespace AnalysisRobot {
namespace RestInterface {

/**
 * @brief 任务状态枚举
 */
enum class TaskStatus {
    SUBMITTED,      // 已提交
    IN_PROGRESS,    // 进行中
    SUCCESS,        // 成功
    FAILED          // 失败
};

/**
 * @brief 任务信息结构
 */
struct TaskInfo {
    int taskId;
    std::string action;
    TaskStatus status;
    std::string message;
    Json::Value data;
    std::string updateTime;
    
    TaskInfo() : taskId(0), status(TaskStatus::SUBMITTED) {}
};

/**
 * @brief 设备状态结构
 */
struct DeviceStatus {
    std::string name;
    std::string description;
    std::string status;
    std::string message;
    Json::Value data;
    std::string updateTime;
    
    DeviceStatus() {}
};

/**
 * @brief REST接口配置
 */
struct RestConfig {
    std::string host;           // 服务器地址，默认"0.0.0.0"
    int port;                   // 端口号，默认8080
    int maxConnections;         // 最大连接数，默认100
    int threadPoolSize;         // 线程池大小，默认4
    bool enableLogging;         // 是否启用日志，默认true
    std::string logLevel;       // 日志级别，默认"INFO"
    
    RestConfig() 
        : host("0.0.0.0")
        , port(8080)
        , maxConnections(100)
        , threadPoolSize(4)
        , enableLogging(true)
        , logLevel("INFO") {}
};

/**
 * @brief 请求处理器函数类型
 */
using RequestHandler = std::function<void(const httplib::Request& req, httplib::Response& res)>;

/**
 * @brief 设备控制器接口
 */
class IDeviceController {
public:
    virtual ~IDeviceController() = default;
    virtual DeviceStatus getStatus() = 0;
    virtual TaskInfo executeOperation(const Json::Value& request) = 0;
    virtual TaskInfo queryTask(int taskId) = 0;
};

/**
 * @brief REST接口驱动类
 * 
 * 提供HTTP REST API服务，支持：
 * - 进样传送装置控制
 * - 扫码开合盖装置控制
 * - 容器货架装置控制
 * - 机械臂控制
 * - 称量装置控制
 * - 加液装置控制
 * - 过滤装置控制
 * - 搅拌装置控制
 * - 加热装置控制
 * - ICP进样装置控制
 * - 水分检测仪控制
 * - 框架装置控制
 */
class RestInterfaceDriver {
public:
    /**
     * @brief 构造函数
     */
    RestInterfaceDriver();
    
    /**
     * @brief 析构函数
     */
    ~RestInterfaceDriver();
    
    /**
     * @brief 初始化REST服务
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const RestConfig& config);
    
    /**
     * @brief 启动HTTP服务器
     * @return 是否成功
     */
    bool start();
    
    /**
     * @brief 停止HTTP服务器
     */
    void stop();
    
    /**
     * @brief 检查服务器是否运行
     * @return 是否运行中
     */
    bool isRunning() const;
    
    /**
     * @brief 注册设备控制器
     * @param deviceName 设备名称
     * @param controller 控制器实例
     */
    void registerDeviceController(const std::string& deviceName, 
                                 std::shared_ptr<IDeviceController> controller);
    
    /**
     * @brief 注册GET路由处理器
     * @param path 路径
     * @param handler 处理器函数
     */
    void registerGetRoute(const std::string& path, RequestHandler handler);
    
    /**
     * @brief 注册POST路由处理器
     * @param path 路径
     * @param handler 处理器函数
     */
    void registerPostRoute(const std::string& path, RequestHandler handler);
    
    /**
     * @brief 获取服务器信息
     * @return 服务器信息字符串
     */
    std::string getServerInfo() const;
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    RestConfig m_config;                                        // 配置参数
    std::atomic<bool> m_running;                               // 运行状态
    std::unique_ptr<httplib::Server> m_server;                 // HTTP服务器
    std::unique_ptr<std::thread> m_serverThread;               // 服务器线程
    std::map<std::string, std::shared_ptr<IDeviceController>> m_deviceControllers; // 设备控制器
    std::map<int, TaskInfo> m_tasks;                           // 任务存储
    std::mutex m_tasksMutex;                                   // 任务锁
    std::string m_lastError;                                   // 最后错误信息
    int m_nextTaskId;                                          // 下一个任务ID
    
    /**
     * @brief 服务器主循环
     */
    void serverLoop();
    
    /**
     * @brief 初始化默认路由
     */
    void initializeDefaultRoutes();
    
    /**
     * @brief 设置设备API路由
     */
    void setupDeviceRoutes();
    
    /**
     * @brief 设置具体设备路由
     */
    void setupSpecificDeviceRoutes();
    
    /**
     * @brief 处理设备状态查询
     * @param req 请求
     * @param res 响应
     */
    void handleDeviceStatus(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理设备操作
     * @param req 请求
     * @param res 响应
     */
    void handleDeviceOperation(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 处理任务查询
     * @param req 请求
     * @param res 响应
     */
    void handleTaskQuery(const httplib::Request& req, httplib::Response& res);
    
    /**
     * @brief 解析URI路径
     * @param uri URI
     * @return 路径组件
     */
    std::vector<std::string> parseUriPath(const std::string& uri);
    
    /**
     * @brief 创建JSON响应
     * @param res 响应对象
     * @param data JSON数据
     * @param statusCode HTTP状态码
     */
    void createJsonResponse(httplib::Response& res, const Json::Value& data, int statusCode = 200);
    
    /**
     * @brief 创建错误响应
     * @param res 响应对象
     * @param message 错误消息
     * @param statusCode HTTP状态码
     */
    void createErrorResponse(httplib::Response& res, const std::string& message, int statusCode = 400);
    
    /**
     * @brief 生成新的任务ID
     * @return 任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString();
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
    
    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     */
    void log(const std::string& level, const std::string& message);
};

} // namespace RestInterface
} // namespace AnalysisRobot

#endif // REST_INTERFACE_DRIVER_H
