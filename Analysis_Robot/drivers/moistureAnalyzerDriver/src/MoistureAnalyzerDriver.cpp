#include "MoistureAnalyzerDriver.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <cstring>
#include <errno.h>

namespace AnalysisRobot {
namespace Moisture {

MoistureAnalyzerDriver::MoistureAnalyzerDriver()
    : m_modbusCtx(nullptr)
    , m_status(MoistureStatus::DISCONNECTED)
    , m_statusCallback(nullptr) {
}

MoistureAnalyzerDriver::~MoistureAnalyzerDriver() {
    disconnect();
}

bool MoistureAnalyzerDriver::initialize(const MoistureConfig& config) {
    m_config = config;
    
    // 创建MODBUS RTU上下文
    m_modbusCtx = modbus_new_rtu(
        m_config.serialPort.c_str(),
        m_config.baudRate,
        m_config.parity,
        m_config.dataBits,
        m_config.stopBits
    );
    
    if (m_modbusCtx == nullptr) {
        setError("Failed to create MODBUS context: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    // 设置从机地址
    if (modbus_set_slave(m_modbusCtx, m_config.slaveId) == -1) {
        setError("Failed to set slave ID: " + std::string(modbus_strerror(errno)));
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
        return false;
    }
    
    // 设置响应超时
    struct timeval timeout;
    timeout.tv_sec = m_config.responseTimeout / 1000;
    timeout.tv_usec = (m_config.responseTimeout % 1000) * 1000;
    modbus_set_response_timeout(m_modbusCtx, timeout.tv_sec, timeout.tv_usec);
    
    setStatus(MoistureStatus::DISCONNECTED, "Initialized successfully");
    return true;
}

bool MoistureAnalyzerDriver::connect() {
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context not initialized");
        return false;
    }
    
    if (modbus_connect(m_modbusCtx) == -1) {
        setError("Connection failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    setStatus(MoistureStatus::CONNECTED, "Connected successfully");
    return true;
}

void MoistureAnalyzerDriver::disconnect() {
    if (m_modbusCtx != nullptr) {
        modbus_close(m_modbusCtx);
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
    }
    setStatus(MoistureStatus::DISCONNECTED, "Disconnected");
}

bool MoistureAnalyzerDriver::isConnected() const {
    return m_status != MoistureStatus::DISCONNECTED && m_status != MoistureStatus::FAULT;
}

WeightReading MoistureAnalyzerDriver::readNetWeight() {
    WeightReading result;
    
    if (!isConnected()) {
        result.errorMsg = "Not connected to moisture analyzer";
        setError(result.errorMsg);
        return result;
    }
    
    // 根据文档：立刻读净重 01 03 00 00 00 02
    uint16_t registers[2];
    int rc = modbus_read_registers(m_modbusCtx, 0x0000, 2, registers);
    
    if (!checkModbusResult(rc, "read net weight")) {
        result.errorMsg = m_lastError;
        return result;
    }
    
    // 拼接4字节值
    result.rawValue = (static_cast<uint32_t>(registers[0]) << 16) | registers[1];
    
    // 检查特殊值
    if (result.rawValue == 0x7FFFFFFF) {
        result.errorMsg = "Overload detected";
        setError(result.errorMsg);
        return result;
    } else if (result.rawValue == 0x80000000) {
        result.errorMsg = "Underload detected";
        setError(result.errorMsg);
        return result;
    }
    
    // 转换为重量值（假设与天平相同的转换方式）
    result.weight = static_cast<double>(result.rawValue) / 10000.0;
    result.success = true;
    
    return result;
}

MoistureReading MoistureAnalyzerDriver::readMoisture() {
    MoistureReading result;
    
    if (!isConnected()) {
        result.errorMsg = "Not connected to moisture analyzer";
        setError(result.errorMsg);
        return result;
    }
    
    // 根据文档：读水分 01 03 00 05 00 02
    uint16_t registers[2];
    int rc = modbus_read_registers(m_modbusCtx, 0x0005, 2, registers);
    
    if (!checkModbusResult(rc, "read moisture")) {
        result.errorMsg = m_lastError;
        return result;
    }
    
    // 解析水分数据（具体格式需要根据设备文档确定）
    // 这里假设第一个寄存器是水分百分比*100，第二个是干重
    result.moisture = static_cast<double>(registers[0]) / 100.0;
    result.dryWeight = static_cast<double>(registers[1]) / 100.0;
    result.success = true;
    
    return result;
}

ProcessStatus MoistureAnalyzerDriver::readProcessStatus() {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return ProcessStatus::NORMAL;
    }
    
    // 根据文档：读过程状态 01 03 00 02 00 01
    uint16_t status;
    int rc = modbus_read_registers(m_modbusCtx, 0x0002, 1, &status);
    
    if (!checkModbusResult(rc, "read process status")) {
        return ProcessStatus::NORMAL;
    }
    
    return static_cast<ProcessStatus>(status);
}

bool MoistureAnalyzerDriver::readWeighingStability() {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return false;
    }
    
    // 根据文档：读称重稳定状态 01 03 00 03 00 01
    uint16_t stability;
    int rc = modbus_read_registers(m_modbusCtx, 0x0003, 1, &stability);
    
    if (!checkModbusResult(rc, "read weighing stability")) {
        return false;
    }
    
    return stability == 1;
}

bool MoistureAnalyzerDriver::tare() {
    return executeWriteCommand(0x0004, 0x0002, "tare");
}

bool MoistureAnalyzerDriver::internalCalibration() {
    setStatus(MoistureStatus::CALIBRATING, "Starting internal calibration");
    return executeWriteCommand(0x0004, 0x0003, "internal calibration");
}

bool MoistureAnalyzerDriver::startHeating() {
    setStatus(MoistureStatus::HEATING, "Starting heating");
    return executeWriteCommand(0x0004, 0x000A, "start heating");
}

bool MoistureAnalyzerDriver::stopHeating() {
    return executeWriteCommand(0x0004, 0x000B, "stop heating");
}

bool MoistureAnalyzerDriver::returnToWeighing() {
    setStatus(MoistureStatus::CONNECTED, "Returning to weighing mode");
    return executeWriteCommand(0x0004, 0x000C, "return to weighing");
}

bool MoistureAnalyzerDriver::openChamber() {
    return executeWriteCommand(0x0004, 0x000D, "open chamber");
}

bool MoistureAnalyzerDriver::closeChamber() {
    return executeWriteCommand(0x0004, 0x000E, "close chamber");
}

bool MoistureAnalyzerDriver::setTargetTemperature(int temperature) {
    return executeWriteCommand(0x0007, temperature, "set target temperature");
}

bool MoistureAnalyzerDriver::configureStationAddress(int address) {
    return executeWriteCommand(0x0004, 0x0200 | address, "configure station address");
}

MoistureStatus MoistureAnalyzerDriver::getStatus() const {
    return m_status;
}

void MoistureAnalyzerDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

std::string MoistureAnalyzerDriver::getLastError() const {
    return m_lastError;
}

std::string MoistureAnalyzerDriver::getDeviceInfo() const {
    std::ostringstream oss;
    oss << "Moisture Analyzer Driver\n";
    oss << "Serial Port: " << m_config.serialPort << "\n";
    oss << "Baud Rate: " << m_config.baudRate << "\n";
    oss << "Slave ID: " << m_config.slaveId << "\n";
    oss << "Target Temperature: " << m_config.targetTemperature << "°C\n";
    oss << "Status: ";
    
    switch (m_status) {
        case MoistureStatus::DISCONNECTED: oss << "Disconnected"; break;
        case MoistureStatus::CONNECTED: oss << "Connected"; break;
        case MoistureStatus::CALIBRATING: oss << "Calibrating"; break;
        case MoistureStatus::HEATING: oss << "Heating"; break;
        case MoistureStatus::MEASURING: oss << "Measuring"; break;
        case MoistureStatus::FAULT: oss << "Error"; break;
    }
    
    return oss.str();
}

void MoistureAnalyzerDriver::setStatus(MoistureStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void MoistureAnalyzerDriver::setError(const std::string& error) {
    m_lastError = error;
    std::cerr << "MoistureAnalyzerDriver Error: " << error << std::endl;
}

bool MoistureAnalyzerDriver::checkModbusResult(int result, const std::string& operation) {
    if (result == -1) {
        setError("MODBUS " + operation + " failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    return true;
}

bool MoistureAnalyzerDriver::executeWriteCommand(int address, int value, const std::string& operation) {
    if (!isConnected()) {
        setError("Not connected to moisture analyzer");
        return false;
    }
    
    int rc = modbus_write_register(m_modbusCtx, address, value);
    
    if (!checkModbusResult(rc, operation)) {
        setStatus(MoistureStatus::FAULT, m_lastError);
        return false;
    }
    
    return true;
}

} // namespace Moisture
} // namespace AnalysisRobot
