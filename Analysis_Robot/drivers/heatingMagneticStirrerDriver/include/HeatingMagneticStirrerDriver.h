#ifndef HEATING_MAGNETIC_STIRRER_DRIVER_H
#define HEATING_MAGNETIC_STIRRER_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <modbus.h>

namespace AnalysisRobot {
namespace HeatingMagneticStirrer {

/**
 * @brief 加热磁力搅拌器状态枚举
 */
enum class StirrerStatus {
    DISCONNECTED = 0,   // 未连接
    CONNECTED = 1,      // 已连接
    HEATING = 2,        // 加热中
    STIRRING = 3,       // 搅拌中
    HEATING_STIRRING = 4, // 加热搅拌中
    FAULT = 5           // 错误状态
};

/**
 * @brief 电机方向枚举
 */
enum class MotorDirection {
    CLOCKWISE = 0,      // 正转
    COUNTERCLOCKWISE = 1 // 反转
};

/**
 * @brief 温度和转速读数结果
 */
struct TemperatureSpeedReading {
    bool success;           // 读取是否成功
    double temperature;     // 温度值（℃）
    double speed;          // 转速值（RPM）
    uint16_t rawTemperature; // 原始温度值
    uint16_t rawSpeed;      // 原始转速值
    std::string errorMsg;   // 错误信息
    
    TemperatureSpeedReading() : success(false), temperature(0.0), speed(0.0), 
                               rawTemperature(0), rawSpeed(0) {}
};

/**
 * @brief 加热磁力搅拌器配置
 */
struct StirrerConfig {
    std::string serialPort;     // 串口号，如"COM1"
    int baudRate;               // 波特率，默认9600
    char parity;                // 校验位，默认'N'
    int dataBits;               // 数据位，默认8
    int stopBits;               // 停止位，默认1
    int slaveId;                // 从机地址，默认1
    int responseTimeout;        // 响应超时时间（毫秒），默认1000
    
    StirrerConfig() 
        : serialPort("COM1")
        , baudRate(9600)
        , parity('N')
        , dataBits(8)
        , stopBits(1)
        , slaveId(1)
        , responseTimeout(1000) {}
};

/**
 * @brief 程控步骤结构
 */
struct ProgramStep {
    uint16_t time;              // 时间（分钟）
    uint16_t temperature;       // 温度（℃）
    uint16_t speed;            // 转速（RPM）
    MotorDirection direction;   // 方向
    
    ProgramStep() : time(0), temperature(0), speed(0), direction(MotorDirection::CLOCKWISE) {}
    ProgramStep(uint16_t t, uint16_t temp, uint16_t spd, MotorDirection dir)
        : time(t), temperature(temp), speed(spd), direction(dir) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(StirrerStatus status, const std::string& message)>;

/**
 * @brief 加热磁力搅拌器驱动类
 * 
 * 支持MODBUS RTU协议的加热磁力搅拌器设备，主要功能：
 * - 读取温度和转速
 * - 温度控制（开启/关闭）
 * - 电机控制（开启/关闭/方向）
 * - 参数设置（温度、转速、时间）
 * - 程控功能
 * - 状态监控
 */
class HeatingMagneticStirrerDriver {
public:
    /**
     * @brief 构造函数
     */
    HeatingMagneticStirrerDriver();
    
    /**
     * @brief 析构函数
     */
    ~HeatingMagneticStirrerDriver();
    
    /**
     * @brief 初始化设备连接
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const StirrerConfig& config);
    
    /**
     * @brief 连接设备
     * @return 是否成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    // ========== 读取功能 ==========
    
    /**
     * @brief 读取温度和转速
     * @return 温度转速读数结果
     */
    TemperatureSpeedReading readTemperatureAndSpeed();
    
    // ========== 控制功能 ==========
    
    /**
     * @brief 温度控制
     * @param enable true=启动，false=关闭
     * @return 是否成功
     */
    bool setTemperatureControl(bool enable);
    
    /**
     * @brief 电机控制
     * @param enable true=启动，false=关闭
     * @return 是否成功
     */
    bool setMotorControl(bool enable);
    
    /**
     * @brief 电机方向控制
     * @param direction 电机方向
     * @return 是否成功
     */
    bool setMotorDirection(MotorDirection direction);
    
    /**
     * @brief 程控控制
     * @param programNumber 程控编号（0-9）
     * @param enable true=启动，false=关闭
     * @return 是否成功
     */
    bool setProgramControl(uint8_t programNumber, bool enable);
    
    // ========== 设置功能 ==========
    
    /**
     * @brief 设置目标温度
     * @param temperature 温度值（室温~350℃）
     * @return 是否成功
     */
    bool setTargetTemperature(uint16_t temperature);
    
    /**
     * @brief 设置电机转速
     * @param speed 转速值（50~2000 RPM）
     * @return 是否成功
     */
    bool setMotorSpeed(uint16_t speed);
    
    /**
     * @brief 设置定时时间
     * @param minutes 时间值（0~65535分钟）
     * @return 是否成功
     */
    bool setTimerMinutes(uint16_t minutes);
    
    /**
     * @brief 设置程控参数
     * @param programNumber 程控编号（0-9）
     * @param steps 程控步骤（最多4步）
     * @return 是否成功
     */
    bool setProgramSteps(uint8_t programNumber, const std::vector<ProgramStep>& steps);
    
    // ========== 状态查询 ==========
    
    /**
     * @brief 获取当前状态
     * @return 当前状态
     */
    StirrerStatus getStatus() const;
    
    /**
     * @brief 设置状态回调函数
     * @param callback 回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 获取最后错误信息
     * @return 错误信息
     */
    std::string getLastError() const;
    
    /**
     * @brief 获取设备信息
     * @return 设备信息字符串
     */
    std::string getDeviceInfo() const;

private:
    modbus_t* m_modbusCtx;              // MODBUS上下文
    StirrerConfig m_config;             // 配置参数
    StirrerStatus m_status;             // 当前状态
    StatusCallback m_statusCallback;    // 状态回调
    std::string m_lastError;            // 最后错误信息
    
    /**
     * @brief 设置状态
     * @param status 新状态
     * @param message 状态消息
     */
    void setStatus(StirrerStatus status, const std::string& message = "");
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
    
    /**
     * @brief 检查MODBUS响应
     * @param result MODBUS操作结果
     * @param operation 操作名称
     * @return 是否成功
     */
    bool checkModbusResult(int result, const std::string& operation);
    
    /**
     * @brief 验证温度范围
     * @param temperature 温度值
     * @return 是否有效
     */
    bool isValidTemperature(uint16_t temperature) const;
    
    /**
     * @brief 验证转速范围
     * @param speed 转速值
     * @return 是否有效
     */
    bool isValidSpeed(uint16_t speed) const;
    
    /**
     * @brief 验证程控编号
     * @param programNumber 程控编号
     * @return 是否有效
     */
    bool isValidProgramNumber(uint8_t programNumber) const;
};

} // namespace HeatingMagneticStirrer
} // namespace AnalysisRobot

#endif // HEATING_MAGNETIC_STIRRER_DRIVER_H
