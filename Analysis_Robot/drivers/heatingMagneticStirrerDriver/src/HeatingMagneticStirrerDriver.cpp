#include "HeatingMagneticStirrerDriver.h"
#include <iostream>
#include <sstream>
#include <errno.h>

namespace AnalysisRobot {
namespace HeatingMagneticStirrer {

HeatingMagneticStirrerDriver::HeatingMagneticStirrerDriver()
    : m_modbusCtx(nullptr)
    , m_status(StirrerStatus::DISCONNECTED)
    , m_statusCallback(nullptr) {
}

HeatingMagneticStirrerDriver::~HeatingMagneticStirrerDriver() {
    disconnect();
}

bool HeatingMagneticStirrerDriver::initialize(const StirrerConfig& config) {
    m_config = config;
    
    // 创建MODBUS RTU上下文
    m_modbusCtx = modbus_new_rtu(
        m_config.serialPort.c_str(),
        m_config.baudRate,
        m_config.parity,
        m_config.dataBits,
        m_config.stopBits
    );
    
    if (m_modbusCtx == nullptr) {
        setError("Failed to create MODBUS context: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    // 设置从机地址
    if (modbus_set_slave(m_modbusCtx, m_config.slaveId) == -1) {
        setError("Failed to set slave ID: " + std::string(modbus_strerror(errno)));
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
        return false;
    }
    
    // 设置响应超时
    struct timeval timeout;
    timeout.tv_sec = m_config.responseTimeout / 1000;
    timeout.tv_usec = (m_config.responseTimeout % 1000) * 1000;
    modbus_set_response_timeout(m_modbusCtx, timeout.tv_sec, timeout.tv_usec);
    setStatus(StirrerStatus::DISCONNECTED, "Initialized successfully");
    return true;
}

bool HeatingMagneticStirrerDriver::connect() {
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context not initialized");
        return false;
    }
    
    if (modbus_connect(m_modbusCtx) == -1) {
        setError("Failed to connect: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    setStatus(StirrerStatus::CONNECTED, "Connected successfully");
    return true;
}

void HeatingMagneticStirrerDriver::disconnect() {
    if (m_modbusCtx != nullptr) {
        modbus_close(m_modbusCtx);
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
    }
    setStatus(StirrerStatus::DISCONNECTED, "Disconnected");
}

bool HeatingMagneticStirrerDriver::isConnected() const {
    return m_status != StirrerStatus::DISCONNECTED && m_modbusCtx != nullptr;
}

TemperatureSpeedReading HeatingMagneticStirrerDriver::readTemperatureAndSpeed() {
    TemperatureSpeedReading result;
    
    if (!isConnected()) {
        result.errorMsg = "Not connected to stirrer";
        setError(result.errorMsg);
        return result;
    }
    
    // 根据文档：读温度|转速(0x00) 返回格式：id+cmd+len+温度+转速+crc
    uint16_t registers[2];
    
    int rc = modbus_read_registers(m_modbusCtx, 0x0000, 2, registers);
    if (!checkModbusResult(rc, "read temperature and speed")) {
        result.errorMsg = m_lastError;
        setStatus(StirrerStatus::FAULT, result.errorMsg);
        return result;
    }
    
    result.rawTemperature = registers[0];
    result.rawSpeed = registers[1];
    result.temperature = static_cast<double>(result.rawTemperature);
    result.speed = static_cast<double>(result.rawSpeed);
    result.success = true;
    
    // 根据当前状态更新设备状态
    if (result.temperature > 25 && result.speed > 0) {
        setStatus(StirrerStatus::HEATING_STIRRING, "Heating and stirring");
    } else if (result.temperature > 25) {
        setStatus(StirrerStatus::HEATING, "Heating");
    } else if (result.speed > 0) {
        setStatus(StirrerStatus::STIRRING, "Stirring");
    } else {
        setStatus(StirrerStatus::CONNECTED, "Idle");
    }
    
    return result;
}

bool HeatingMagneticStirrerDriver::setTemperatureControl(bool enable) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    // 温度控制（0x10）数据：00关闭|01启动
    uint16_t value = enable ? 0x0001 : 0x0000;
    int rc = modbus_write_register(m_modbusCtx, 0x0010, value);
    
    if (!checkModbusResult(rc, "set temperature control")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::string message = enable ? "Temperature control enabled" : "Temperature control disabled";
    setStatus(StirrerStatus::CONNECTED, message);
    return true;
}

bool HeatingMagneticStirrerDriver::setMotorControl(bool enable) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    // 电机控制（0x20）数据：00关闭|01启动
    uint16_t value = enable ? 0x0001 : 0x0000;
    int rc = modbus_write_register(m_modbusCtx, 0x0020, value);
    
    if (!checkModbusResult(rc, "set motor control")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::string message = enable ? "Motor control enabled" : "Motor control disabled";
    setStatus(StirrerStatus::CONNECTED, message);
    return true;
}

bool HeatingMagneticStirrerDriver::setMotorDirection(MotorDirection direction) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    // 电机方向控制（0x21）数据：00正转|01反转
    uint16_t value = (direction == MotorDirection::CLOCKWISE) ? 0x0000 : 0x0001;
    int rc = modbus_write_register(m_modbusCtx, 0x0021, value);
    
    if (!checkModbusResult(rc, "set motor direction")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::string message = (direction == MotorDirection::CLOCKWISE) ? 
                         "Motor direction set to clockwise" : "Motor direction set to counterclockwise";
    setStatus(StirrerStatus::CONNECTED, message);
    return true;
}

bool HeatingMagneticStirrerDriver::setProgramControl(uint8_t programNumber, bool enable) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    if (!isValidProgramNumber(programNumber)) {
        setError("Invalid program number: " + std::to_string(programNumber));
        return false;
    }
    
    // 程控控制（0x40）数据：H8:程控编号(0~9)|L8:00关闭|01启动
    uint16_t value = (static_cast<uint16_t>(programNumber) << 8) | (enable ? 0x01 : 0x00);
    int rc = modbus_write_register(m_modbusCtx, 0x0040, value);
    
    if (!checkModbusResult(rc, "set program control")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::ostringstream oss;
    oss << "Program " << static_cast<int>(programNumber) << (enable ? " enabled" : " disabled");
    setStatus(StirrerStatus::CONNECTED, oss.str());
    return true;
}

bool HeatingMagneticStirrerDriver::setTargetTemperature(uint16_t temperature) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    if (!isValidTemperature(temperature)) {
        setError("Invalid temperature: " + std::to_string(temperature));
        return false;
    }
    
    // 温度设置（0x80） 数据：（室温~350）℃
    int rc = modbus_write_register(m_modbusCtx, 0x0080, temperature);
    
    if (!checkModbusResult(rc, "set target temperature")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::ostringstream oss;
    oss << "Target temperature set to " << temperature << "°C";
    setStatus(StirrerStatus::CONNECTED, oss.str());
    return true;
}

bool HeatingMagneticStirrerDriver::setMotorSpeed(uint16_t speed) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    if (!isValidSpeed(speed)) {
        setError("Invalid speed: " + std::to_string(speed));
        return false;
    }
    
    // 电机设置（0x90）数据：（50~2000）RPM
    int rc = modbus_write_register(m_modbusCtx, 0x0090, speed);
    
    if (!checkModbusResult(rc, "set motor speed")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::ostringstream oss;
    oss << "Motor speed set to " << speed << " RPM";
    setStatus(StirrerStatus::CONNECTED, oss.str());
    return true;
}

bool HeatingMagneticStirrerDriver::setTimerMinutes(uint16_t minutes) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }
    
    // 时间设置（0xA0）数据：（0~65535）min
    int rc = modbus_write_register(m_modbusCtx, 0x00A0, minutes);
    
    if (!checkModbusResult(rc, "set timer minutes")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }
    
    std::ostringstream oss;
    oss << "Timer set to " << minutes << " minutes";
    setStatus(StirrerStatus::CONNECTED, oss.str());
    return true;
}

StirrerStatus HeatingMagneticStirrerDriver::getStatus() const {
    return m_status;
}

void HeatingMagneticStirrerDriver::setStatusCallback(StatusCallback callback) {
    m_statusCallback = callback;
}

std::string HeatingMagneticStirrerDriver::getLastError() const {
    return m_lastError;
}

std::string HeatingMagneticStirrerDriver::getDeviceInfo() const {
    std::ostringstream oss;
    oss << "Heating Magnetic Stirrer Driver\n";
    oss << "Serial Port: " << m_config.serialPort << "\n";
    oss << "Baud Rate: " << m_config.baudRate << "\n";
    oss << "Slave ID: " << m_config.slaveId << "\n";
    oss << "Status: ";
    
    switch (m_status) {
        case StirrerStatus::DISCONNECTED: oss << "Disconnected"; break;
        case StirrerStatus::CONNECTED: oss << "Connected"; break;
        case StirrerStatus::HEATING: oss << "Heating"; break;
        case StirrerStatus::STIRRING: oss << "Stirring"; break;
        case StirrerStatus::HEATING_STIRRING: oss << "Heating & Stirring"; break;
        case StirrerStatus::FAULT: oss << "Fault"; break;
    }
    
    return oss.str();
}

void HeatingMagneticStirrerDriver::setStatus(StirrerStatus status, const std::string& message) {
    m_status = status;
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void HeatingMagneticStirrerDriver::setError(const std::string& error) {
    m_lastError = error;
    std::cerr << "HeatingMagneticStirrerDriver Error: " << error << std::endl;
}

bool HeatingMagneticStirrerDriver::checkModbusResult(int result, const std::string& operation) {
    if (result == -1) {
        setError("MODBUS " + operation + " failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    return true;
}

bool HeatingMagneticStirrerDriver::isValidTemperature(uint16_t temperature) const {
    // 根据文档：室温~350℃，假设室温为15℃
    return temperature >= 15 && temperature <= 350;
}

bool HeatingMagneticStirrerDriver::isValidSpeed(uint16_t speed) const {
    // 根据文档：50~2000 RPM
    return speed >= 50 && speed <= 2000;
}

bool HeatingMagneticStirrerDriver::isValidProgramNumber(uint8_t programNumber) const {
    // 根据文档：程控编号0~9
    return programNumber <= 9;
}

bool HeatingMagneticStirrerDriver::setProgramSteps(uint8_t programNumber, const std::vector<ProgramStep>& steps) {
    if (!isConnected()) {
        setError("Not connected to stirrer");
        return false;
    }

    if (!isValidProgramNumber(programNumber)) {
        setError("Invalid program number: " + std::to_string(programNumber));
        return false;
    }

    if (steps.empty() || steps.size() > 4) {
        setError("Invalid number of program steps: " + std::to_string(steps.size()) + " (must be 1-4)");
        return false;
    }

    // 根据文档：程控设置（0xB0） 数据：程控编号+时间1+温度1+转速1+方向1+...+时间4+温度4+转速4+方向4+CRC
    // 准备数据：程控编号 + 4个步骤的数据（每个步骤4个参数）
    std::vector<uint16_t> data;
    data.reserve(17); // 1个程控编号 + 4*4个步骤参数

    // 第一个寄存器：程控编号
    data.push_back(static_cast<uint16_t>(programNumber));

    // 添加步骤数据，不足4步的用0填充
    for (size_t i = 0; i < 4; ++i) {
        if (i < steps.size()) {
            const ProgramStep& step = steps[i];

            // 验证步骤参数
            if (!isValidTemperature(step.temperature)) {
                setError("Invalid temperature in step " + std::to_string(i + 1) + ": " + std::to_string(step.temperature));
                return false;
            }
            if (!isValidSpeed(step.speed)) {
                setError("Invalid speed in step " + std::to_string(i + 1) + ": " + std::to_string(step.speed));
                return false;
            }

            data.push_back(step.time);
            data.push_back(step.temperature);
            data.push_back(step.speed);
            data.push_back(static_cast<uint16_t>(step.direction));
        } else {
            // 填充空步骤
            data.push_back(0); // 时间
            data.push_back(0); // 温度
            data.push_back(0); // 转速
            data.push_back(0); // 方向
        }
    }

    // 写入多个寄存器
    int rc = modbus_write_registers(m_modbusCtx, 0x00B0, static_cast<int>(data.size()), data.data());

    if (!checkModbusResult(rc, "set program steps")) {
        setStatus(StirrerStatus::FAULT, m_lastError);
        return false;
    }

    std::ostringstream oss;
    oss << "Program " << static_cast<int>(programNumber) << " configured with " << steps.size() << " steps";
    setStatus(StirrerStatus::CONNECTED, oss.str());
    return true;
}

} // namespace HeatingMagneticStirrer
} // namespace AnalysisRobot
