#ifndef ROBOT_DRIVER_H
#define ROBOT_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <map>
#include <atomic>
#include <mutex>
#include <modbus.h>

namespace AnalysisRobot {
namespace Robot {

/**
 * @brief 机器人状态枚举
 */
enum class RobotStatus {
    DISCONNECTED = 0,   // 未连接
    IDLE = 1,          // 空闲
    BUSY = 2,          // 忙碌
    ROBOT_ERROR = 3,         // 错误
    EMERGENCY = 4      // 急停
};

/**
 * @brief 运动模式枚举
 */
enum class MotionMode {
    MANUAL = 0,        // 手动模式
    AUTO = 1,          // 自动模式
    TEACH = 2,         // 示教模式
    MAINTENANCE = 3    // 维护模式
};

/**
 * @brief 位置信息结构
 */
struct Position {
    double x, y, z;        // 笛卡尔坐标 (mm)
    double rx, ry, rz;     // 旋转角度 (度)
    
    Position() : x(0), y(0), z(0), rx(0), ry(0), rz(0) {}
    Position(double x_, double y_, double z_, double rx_, double ry_, double rz_)
        : x(x_), y(y_), z(z_), rx(rx_), ry(ry_), rz(rz_) {}
};

/**
 * @brief 关节角度结构
 */
struct JointAngles {
    double joint1, joint2, joint3, joint4, joint5, joint6;  // 关节角度 (度)
    
    JointAngles() : joint1(0), joint2(0), joint3(0), joint4(0), joint5(0), joint6(0) {}
};

/**
 * @brief 运动参数结构
 */
struct MotionParams {
    int speed;         // 速度百分比 (1-100)
    int acceleration;  // 加速度百分比 (1-100)
    
    MotionParams() : speed(50), acceleration(50) {}
    MotionParams(int s, int a) : speed(s), acceleration(a) {}
};

/**
 * @brief 机器人配置
 */
struct RobotConfig {
    std::string ipAddress;          // IP地址，默认"*************"
    int port;                       // 端口号，默认502
    int slaveId;                    // 从机地址，默认1
    int responseTimeout;            // 响应超时时间（毫秒），默认2000
    int maxJointSpeed;              // 最大关节速度 (度/秒)
    int maxLinearSpeed;             // 最大直线速度 (mm/秒)
    
    RobotConfig() 
        : ipAddress("*************")
        , port(502)
        , slaveId(1)
        , responseTimeout(2000)
        , maxJointSpeed(180)
        , maxLinearSpeed(1000) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(RobotStatus status, const std::string& message)>;

/**
 * @brief 机器人驱动类
 * 
 * 支持MODBUS TCP协议的6轴机械臂，主要功能：
 * - 位置控制和反馈
 * - 关节运动和直线运动
 * - IO控制
 * - 安全监控
 * - 任务管理
 */
class RobotDriver {
public:
    /**
     * @brief 构造函数
     */
    RobotDriver();
    
    /**
     * @brief 析构函数
     */
    ~RobotDriver();
    
    /**
     * @brief 初始化机器人连接
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const RobotConfig& config);
    
    /**
     * @brief 连接机器人
     * @return 是否成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    // ========== 状态查询 ==========
    
    /**
     * @brief 获取机器人状态
     * @return 机器人状态
     */
    RobotStatus getStatus() const;
    
    /**
     * @brief 获取当前TCP位置
     * @return TCP位置
     */
    Position getCurrentPosition();
    
    /**
     * @brief 获取当前关节角度
     * @return 关节角度
     */
    JointAngles getCurrentJointAngles();
    
    /**
     * @brief 获取错误代码
     * @return 错误代码
     */
    int getErrorCode();
    
    // ========== 运动控制 ==========
    
    /**
     * @brief 回原点
     * @return 是否成功
     */
    bool home();
    
    /**
     * @brief 关节运动
     * @param angles 目标关节角度
     * @param params 运动参数
     * @return 是否成功
     */
    bool moveJoint(const JointAngles& angles, const MotionParams& params = MotionParams());
    
    /**
     * @brief 直线运动
     * @param position 目标位置
     * @param params 运动参数
     * @return 是否成功
     */
    bool moveLinear(const Position& position, const MotionParams& params = MotionParams());
    
    /**
     * @brief 停止运动
     * @return 是否成功
     */
    bool stop();
    
    /**
     * @brief 暂停运动
     * @return 是否成功
     */
    bool pause();
    
    /**
     * @brief 恢复运动
     * @return 是否成功
     */
    bool resume();
    
    // ========== IO控制 ==========
    
    /**
     * @brief 设置数字输出
     * @param port 端口号 (0-15)
     * @param value 输出值
     * @return 是否成功
     */
    bool setDigitalOutput(int port, bool value);
    
    /**
     * @brief 读取数字输入
     * @param port 端口号 (0-15)
     * @return 输入值
     */
    bool getDigitalInput(int port);
    
    /**
     * @brief 控制夹爪
     * @param command 夹爪指令 (0=释放, 1=夹取, 2=停止)
     * @param force 夹爪力度 (1-100%)
     * @return 是否成功
     */
    bool controlGripper(int command, int force = 50);
    
    /**
     * @brief 获取夹爪状态
     * @return 夹爪状态 (0=释放, 1=夹取, 2=运动中, 3=错误)
     */
    int getGripperStatus();
    
    // ========== 任务管理 ==========
    
    /**
     * @brief 执行取放任务
     * @param sourcePos 源位置编码
     * @param destPos 目标位置编码
     * @return 任务ID
     */
    int executePickPlace(const std::string& sourcePos, const std::string& destPos);
    
    /**
     * @brief 获取任务状态
     * @param taskId 任务ID
     * @return 任务状态 (0=空闲, 1=取料中, 2=移动中, 3=放料中, 4=完成, 5=失败)
     */
    int getTaskStatus(int taskId);
    
    /**
     * @brief 获取任务进度
     * @return 进度百分比 (0-100)
     */
    double getTaskProgress();
    
    // ========== 配置和回调 ==========
    
    /**
     * @brief 设置状态回调函数
     * @param callback 回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    RobotConfig m_config;                   // 配置参数
    modbus_t* m_modbusCtx;                 // MODBUS上下文
    std::atomic<RobotStatus> m_status;      // 当前状态
    StatusCallback m_statusCallback;        // 状态回调
    std::string m_lastError;               // 最后错误信息
    mutable std::mutex m_mutex;            // 线程安全锁
    
    // 位置编码映射
    static std::map<std::string, uint16_t> s_positionCodes;
    
    /**
     * @brief 读取寄存器
     * @param address 寄存器地址
     * @return 寄存器值
     */
    uint16_t readRegister(int address);
    
    /**
     * @brief 写入寄存器
     * @param address 寄存器地址
     * @param value 写入值
     * @return 是否成功
     */
    bool writeRegister(int address, uint16_t value);
    
    /**
     * @brief 读取32位值
     * @param address 起始地址
     * @return 32位值
     */
    int32_t readInt32(int address);
    
    /**
     * @brief 写入32位值
     * @param address 起始地址
     * @param value 32位值
     * @return 是否成功
     */
    bool writeInt32(int address, int32_t value);
    
    /**
     * @brief 位置编码转换
     * @param position 位置字符串
     * @return 位置编码
     */
    uint16_t encodePosition(const std::string& position);
    
    /**
     * @brief 检查MODBUS结果
     * @param result MODBUS操作结果
     * @param operation 操作描述
     * @return 是否成功
     */
    bool checkModbusResult(int result, const std::string& operation);
    
    /**
     * @brief 设置状态
     * @param status 新状态
     * @param message 状态消息
     */
    void setStatus(RobotStatus status, const std::string& message);
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

    /**
     * @brief 等待运动完成
     * @return 是否成功
     */
    bool waitForMotionComplete();
};

} // namespace Robot
} // namespace AnalysisRobot

#endif // ROBOT_DRIVER_H
