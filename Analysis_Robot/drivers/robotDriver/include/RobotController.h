#ifndef ROBOT_CONTROLLER_H
#define ROBOT_CONTROLLER_H

#include "RobotDriver.h"
#include <json/json.h>
#include <thread>
#include <atomic>
#include <map>
#include <mutex>

namespace AnalysisRobot {
namespace Robot {

/**
 * @brief 任务状态枚举
 */
enum class TaskStatus {
    SUBMITTED,      // 已提交
    IN_PROGRESS,    // 进行中
    SUCCESS,        // 成功
    FAILED          // 失败
};

/**
 * @brief 任务信息结构
 */
struct TaskInfo {
    int taskId;
    std::string action;
    TaskStatus status;
    std::string message;
    Json::Value data;
    std::string updateTime;
    
    TaskInfo() : taskId(0), status(TaskStatus::SUBMITTED) {}
};

/**
 * @brief 设备状态结构
 */
struct DeviceStatus {
    std::string name;
    std::string description;
    std::string status;
    std::string message;
    Json::Value data;
    std::string updateTime;
    
    DeviceStatus() {}
};

/**
 * @brief 设备控制器接口
 */
class IDeviceController {
public:
    virtual ~IDeviceController() = default;
    virtual DeviceStatus getStatus() = 0;
    virtual TaskInfo executeOperation(const Json::Value& request) = 0;
    virtual TaskInfo queryTask(int taskId) = 0;
};

/**
 * @brief 机器人控制器类
 * 
 * 实现IDeviceController接口，提供REST API到机器人驱动器的桥接
 */
class RobotController : public IDeviceController {
private:
    std::shared_ptr<RobotDriver> m_driver;
    RobotConfig m_config;
    std::string m_deviceName;
    std::string m_deviceType;
    
    // 状态管理
    std::atomic<bool> m_initialized;
    std::atomic<bool> m_connected;
    std::string m_lastError;
    
    // 任务管理
    std::map<int, TaskInfo> m_tasks;
    std::mutex m_tasksMutex;
    std::atomic<int> m_nextTaskId;
    
    // 监控线程
    std::thread m_monitorThread;
    std::atomic<bool> m_monitoring;
    
    // 任务监控线程
    std::map<int, std::thread> m_taskThreads;
    std::mutex m_taskThreadsMutex;
    
public:
    /**
     * @brief 构造函数
     * @param name 设备名称
     * @param config 机器人配置
     */
    RobotController(const std::string& name, const RobotConfig& config);
    
    /**
     * @brief 析构函数
     */
    ~RobotController();
    
    /**
     * @brief 初始化控制器
     * @return 是否成功
     */
    bool initialize();
    
    /**
     * @brief 关闭控制器
     */
    void shutdown();
    
    // 实现IDeviceController接口
    DeviceStatus getStatus() override;
    TaskInfo executeOperation(const Json::Value& request) override;
    TaskInfo queryTask(int taskId) override;
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    /**
     * @brief 启动监控线程
     */
    void startMonitoring();
    
    /**
     * @brief 停止监控线程
     */
    void stopMonitoring();
    
    /**
     * @brief 监控循环
     */
    void monitorLoop();
    
    /**
     * @brief 停止所有任务线程
     */
    void stopAllTaskThreads();
    
    /**
     * @brief 清理任务线程
     * @param taskId 任务ID
     */
    void cleanupTaskThread(int taskId);
    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 更新任务状态
     * @param taskId 任务ID
     * @param status 任务状态
     * @param message 状态消息
     * @param data 附加数据
     */
    void updateTaskStatus(int taskId, TaskStatus status, const std::string& message, const Json::Value& data = Json::Value());
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 返回数据
     * @return 任务信息
     */
    TaskInfo createSuccessTask(const std::string& action, const Json::Value& data = Json::Value());
    
    /**
     * @brief 创建错误任务
     * @param error 错误信息
     * @return 任务信息
     */
    TaskInfo createErrorTask(const std::string& error);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString();
    
    // ========== 操作处理方法 ==========
    
    /**
     * @brief 处理取放操作
     * @param data 操作数据
     * @return 任务信息
     */
    TaskInfo handlePickPlaceOperation(const Json::Value& data);
    
    /**
     * @brief 处理移动操作
     * @param data 操作数据
     * @return 任务信息
     */
    TaskInfo handleMoveToOperation(const Json::Value& data);
    
    /**
     * @brief 处理回原点操作
     * @return 任务信息
     */
    TaskInfo handleHomeOperation();
    
    /**
     * @brief 处理停止操作
     * @return 任务信息
     */
    TaskInfo handleStopOperation();
    
    /**
     * @brief 处理IO设置操作
     * @param data 操作数据
     * @return 任务信息
     */
    TaskInfo handleSetIOOperation(const Json::Value& data);
    
    /**
     * @brief 处理夹爪操作
     * @param data 操作数据
     * @return 任务信息
     */
    TaskInfo handleGripperOperation(const Json::Value& data);
    
    /**
     * @brief 监控取放任务
     * @param taskId REST任务ID
     * @param driverTaskId 驱动器任务ID
     */
    void monitorPickPlaceTask(int taskId, int driverTaskId);
    
    /**
     * @brief 监控移动任务
     * @param taskId 任务ID
     */
    void monitorMoveTask(int taskId);
};

} // namespace Robot
} // namespace AnalysisRobot

#endif // ROBOT_CONTROLLER_H
