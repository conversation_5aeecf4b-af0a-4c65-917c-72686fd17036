#include "RobotDriver.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <cstring>
#include <errno.h>
#include <chrono>
#include <thread>

namespace AnalysisRobot {
namespace Robot {

// 位置编码映射表
std::map<std::string, uint16_t> RobotDriver::s_positionCodes = {
    // 工作台位置
    {"A01", 0x0A01}, {"A02", 0x0A02}, {"A03", 0x0A03},
    {"B01", 0x0B01}, {"B02", 0x0B02}, {"B03", 0x0B03},
    {"C01", 0x0C01}, {"C02", 0x0C02}, {"C03", 0x0C03},
    
    // 货架位置
    {"R01", 0x0101}, {"R02", 0x0102}, {"R03", 0x0103},
    {"R04", 0x0104}, {"R05", 0x0105}, {"R06", 0x0106},
    
    // 特殊位置
    {"HOME", 0x0000},           // 原点位置
    {"SAFE", 0x0001},           // 安全位置
    {"MAINTENANCE", 0x0002}     // 维护位置
};

RobotDriver::RobotDriver()
    : m_modbusCtx(nullptr)
    , m_status(RobotStatus::DISCONNECTED)
    , m_statusCallback(nullptr) {
}

RobotDriver::~RobotDriver() {
    disconnect();
}

bool RobotDriver::initialize(const RobotConfig& config) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_config = config;
    
    // 创建MODBUS TCP上下文
    m_modbusCtx = modbus_new_tcp(m_config.ipAddress.c_str(), m_config.port);
    
    if (m_modbusCtx == nullptr) {
        setError("Failed to create MODBUS TCP context: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    // 设置从机地址
    if (modbus_set_slave(m_modbusCtx, m_config.slaveId) == -1) {
        setError("Failed to set slave ID: " + std::string(modbus_strerror(errno)));
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
        return false;
    }
    
    // 设置响应超时
    struct timeval timeout;
    timeout.tv_sec = m_config.responseTimeout / 1000;
    timeout.tv_usec = (m_config.responseTimeout % 1000) * 1000;
    modbus_set_response_timeout(m_modbusCtx, timeout.tv_sec, timeout.tv_usec);
    
    // 设置连接超时
    modbus_set_byte_timeout(m_modbusCtx, timeout.tv_sec, timeout.tv_usec);
    
    setStatus(RobotStatus::DISCONNECTED, "Initialized successfully");
    return true;
}

bool RobotDriver::connect() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context not initialized");
        return false;
    }
    
    if (modbus_connect(m_modbusCtx) == -1) {
        setError("Connection failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    
    setStatus(RobotStatus::IDLE, "Connected successfully");
    return true;
}

void RobotDriver::disconnect() {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    if (m_modbusCtx != nullptr) {
        modbus_close(m_modbusCtx);
        modbus_free(m_modbusCtx);
        m_modbusCtx = nullptr;
    }
    
    setStatus(RobotStatus::DISCONNECTED, "Disconnected");
}

bool RobotDriver::isConnected() const {
    return m_status.load() != RobotStatus::DISCONNECTED;
}

RobotStatus RobotDriver::getStatus() const {
    return m_status.load();
}

Position RobotDriver::getCurrentPosition() {
    Position pos;
    
    if (!isConnected()) {
        setError("Not connected to robot");
        return pos;
    }
    
    // 读取TCP位置 (寄存器0x001C-0x0027)
    int32_t x = readInt32(0x001C);  // TCP_X
    int32_t y = readInt32(0x001E);  // TCP_Y
    int32_t z = readInt32(0x0020);  // TCP_Z
    int32_t rx = readInt32(0x0022); // TCP_RX
    int32_t ry = readInt32(0x0024); // TCP_RY
    int32_t rz = readInt32(0x0026); // TCP_RZ
    
    // 转换单位：0.1mm -> mm, 0.01度 -> 度
    pos.x = x / 10.0;
    pos.y = y / 10.0;
    pos.z = z / 10.0;
    pos.rx = rx / 100.0;
    pos.ry = ry / 100.0;
    pos.rz = rz / 100.0;
    
    return pos;
}

JointAngles RobotDriver::getCurrentJointAngles() {
    JointAngles angles;
    
    if (!isConnected()) {
        setError("Not connected to robot");
        return angles;
    }
    
    // 读取关节角度 (寄存器0x0010-0x001B)
    int32_t j1 = readInt32(0x0010);  // JOINT1_ANGLE
    int32_t j2 = readInt32(0x0012);  // JOINT2_ANGLE
    int32_t j3 = readInt32(0x0014);  // JOINT3_ANGLE
    int32_t j4 = readInt32(0x0016);  // JOINT4_ANGLE
    int32_t j5 = readInt32(0x0018);  // JOINT5_ANGLE
    int32_t j6 = readInt32(0x001A);  // JOINT6_ANGLE
    
    // 转换单位：0.01度 -> 度
    angles.joint1 = j1 / 100.0;
    angles.joint2 = j2 / 100.0;
    angles.joint3 = j3 / 100.0;
    angles.joint4 = j4 / 100.0;
    angles.joint5 = j5 / 100.0;
    angles.joint6 = j6 / 100.0;
    
    return angles;
}

int RobotDriver::getErrorCode() {
    if (!isConnected()) {
        return -1;
    }
    
    return readRegister(0x0001);  // ERROR_CODE
}

bool RobotDriver::home() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    // 发送回原点指令 (寄存器0x0030)
    if (!writeRegister(0x0030, 1)) {  // MOTION_CMD = HOME
        return false;
    }
    
    // 等待指令执行完成
    return waitForMotionComplete();
}

bool RobotDriver::moveJoint(const JointAngles& angles, const MotionParams& params) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    // 设置运动参数
    writeRegister(0x003E, params.speed);        // MOTION_SPEED
    writeRegister(0x003F, params.acceleration); // MOTION_ACCEL
    
    // 设置目标关节角度 (转换为0.01度精度)
    writeInt32(0x0040, static_cast<int32_t>(angles.joint1 * 100));
    writeInt32(0x0042, static_cast<int32_t>(angles.joint2 * 100));
    writeInt32(0x0044, static_cast<int32_t>(angles.joint3 * 100));
    writeInt32(0x0046, static_cast<int32_t>(angles.joint4 * 100));
    writeInt32(0x0048, static_cast<int32_t>(angles.joint5 * 100));
    writeInt32(0x004A, static_cast<int32_t>(angles.joint6 * 100));
    
    // 发送关节运动指令
    if (!writeRegister(0x0030, 2)) {  // MOTION_CMD = MOVEJ
        return false;
    }
    
    return waitForMotionComplete();
}

bool RobotDriver::moveLinear(const Position& position, const MotionParams& params) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    // 设置运动参数
    writeRegister(0x003E, params.speed);        // MOTION_SPEED
    writeRegister(0x003F, params.acceleration); // MOTION_ACCEL
    
    // 设置目标位置 (转换为0.1mm和0.01度精度)
    writeInt32(0x0032, static_cast<int32_t>(position.x * 10));   // TARGET_X
    writeInt32(0x0034, static_cast<int32_t>(position.y * 10));   // TARGET_Y
    writeInt32(0x0036, static_cast<int32_t>(position.z * 10));   // TARGET_Z
    writeInt32(0x0038, static_cast<int32_t>(position.rx * 100)); // TARGET_RX
    writeInt32(0x003A, static_cast<int32_t>(position.ry * 100)); // TARGET_RY
    writeInt32(0x003C, static_cast<int32_t>(position.rz * 100)); // TARGET_RZ
    
    // 发送直线运动指令
    if (!writeRegister(0x0030, 3)) {  // MOTION_CMD = MOVEL
        return false;
    }
    
    return waitForMotionComplete();
}

bool RobotDriver::stop() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    return writeRegister(0x0030, 4);  // MOTION_CMD = STOP
}

bool RobotDriver::pause() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    return writeRegister(0x0030, 5);  // MOTION_CMD = PAUSE
}

bool RobotDriver::resume() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    return writeRegister(0x0030, 6);  // MOTION_CMD = RESUME
}

bool RobotDriver::setDigitalOutput(int port, bool value) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    if (port < 0 || port > 15) {
        setError("Invalid port number: " + std::to_string(port));
        return false;
    }
    
    // 读取当前数字输出状态
    uint16_t currentOutput = readRegister(0x0050);  // DIGITAL_OUT
    
    // 设置或清除指定位
    if (value) {
        currentOutput |= (1 << port);
    } else {
        currentOutput &= ~(1 << port);
    }
    
    return writeRegister(0x0050, currentOutput);
}

bool RobotDriver::getDigitalInput(int port) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    if (port < 0 || port > 15) {
        setError("Invalid port number: " + std::to_string(port));
        return false;
    }
    
    uint16_t digitalInput = readRegister(0x0051);  // DIGITAL_IN
    return (digitalInput & (1 << port)) != 0;
}

bool RobotDriver::controlGripper(int command, int force) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return false;
    }
    
    if (command < 0 || command > 2) {
        setError("Invalid gripper command: " + std::to_string(command));
        return false;
    }
    
    if (force < 1 || force > 100) {
        setError("Invalid gripper force: " + std::to_string(force));
        return false;
    }
    
    // 设置夹爪力度
    writeRegister(0x0054, force);    // GRIPPER_FORCE
    
    // 发送夹爪指令
    return writeRegister(0x0052, command);  // GRIPPER_CMD
}

int RobotDriver::getGripperStatus() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return -1;
    }
    
    return readRegister(0x0053);  // GRIPPER_STATUS
}

int RobotDriver::executePickPlace(const std::string& sourcePos, const std::string& destPos) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return -1;
    }
    
    uint16_t sourceCode = encodePosition(sourcePos);
    uint16_t destCode = encodePosition(destPos);
    
    if (sourceCode == 0xFFFF || destCode == 0xFFFF) {
        setError("Invalid position code");
        return -1;
    }
    
    // 设置源位置和目标位置
    writeRegister(0x0076, sourceCode);  // SOURCE_POSITION
    writeRegister(0x0077, destCode);    // DEST_POSITION
    
    // 启动取放任务
    if (!writeRegister(0x0074, 1)) {    // PICK_PLACE_CMD
        return -1;
    }
    
    // 返回任务ID (从寄存器读取)
    uint16_t taskIdHigh = readRegister(0x0004);  // TASK_ID_HIGH
    uint16_t taskIdLow = readRegister(0x0005);   // TASK_ID_LOW
    
    return (static_cast<int>(taskIdHigh) << 16) | taskIdLow;
}

int RobotDriver::getTaskStatus(int taskId) {
    if (!isConnected()) {
        setError("Not connected to robot");
        return -1;
    }
    
    return readRegister(0x0075);  // PICK_PLACE_STATUS
}

double RobotDriver::getTaskProgress() {
    if (!isConnected()) {
        setError("Not connected to robot");
        return 0.0;
    }
    
    uint32_t progress = static_cast<uint32_t>(readInt32(0x0072));  // TASK_PROGRESS
    return progress / 100.0;  // 转换为百分比
}

void RobotDriver::setStatusCallback(StatusCallback callback) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_statusCallback = callback;
}

std::string RobotDriver::getLastError() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_lastError;
}

// ========== 私有方法实现 ==========

uint16_t RobotDriver::readRegister(int address) {
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context is null");
        return 0;
    }
    
    uint16_t value;
    int rc = modbus_read_registers(m_modbusCtx, address, 1, &value);
    
    if (!checkModbusResult(rc, "read register " + std::to_string(address))) {
        return 0;
    }
    
    return value;
}

bool RobotDriver::writeRegister(int address, uint16_t value) {
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context is null");
        return false;
    }
    
    int rc = modbus_write_register(m_modbusCtx, address, value);
    return checkModbusResult(rc, "write register " + std::to_string(address));
}

int32_t RobotDriver::readInt32(int address) {
    uint16_t registers[2];
    
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context is null");
        return 0;
    }
    
    int rc = modbus_read_registers(m_modbusCtx, address, 2, registers);
    
    if (!checkModbusResult(rc, "read int32 " + std::to_string(address))) {
        return 0;
    }
    
    // 拼接32位值：高16位在前，低16位在后
    return (static_cast<int32_t>(registers[0]) << 16) | registers[1];
}

bool RobotDriver::writeInt32(int address, int32_t value) {
    uint16_t registers[2];
    
    // 分解32位值
    registers[0] = static_cast<uint16_t>((value >> 16) & 0xFFFF);  // 高16位
    registers[1] = static_cast<uint16_t>(value & 0xFFFF);          // 低16位
    
    if (m_modbusCtx == nullptr) {
        setError("MODBUS context is null");
        return false;
    }
    
    int rc = modbus_write_registers(m_modbusCtx, address, 2, registers);
    return checkModbusResult(rc, "write int32 " + std::to_string(address));
}

uint16_t RobotDriver::encodePosition(const std::string& position) {
    auto it = s_positionCodes.find(position);
    return (it != s_positionCodes.end()) ? it->second : 0xFFFF;
}

bool RobotDriver::checkModbusResult(int result, const std::string& operation) {
    if (result == -1) {
        setError("MODBUS " + operation + " failed: " + std::string(modbus_strerror(errno)));
        return false;
    }
    return true;
}

void RobotDriver::setStatus(RobotStatus status, const std::string& message) {
    m_status.store(status);
    
    if (m_statusCallback) {
        m_statusCallback(status, message);
    }
}

void RobotDriver::setError(const std::string& error) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_lastError = error;
}

bool RobotDriver::waitForMotionComplete() {
    const int maxWaitTime = 30000;  // 最大等待30秒
    const int checkInterval = 100;  // 每100ms检查一次

    for (int elapsed = 0; elapsed < maxWaitTime; elapsed += checkInterval) {
        uint16_t cmdStatus = readRegister(0x0031);  // CMD_STATUS

        if (cmdStatus == 2) {  // 完成
            return true;
        } else if (cmdStatus == 3) {  // 错误
            setError("Motion command failed");
            return false;
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(checkInterval));
    }

    setError("Motion command timeout");
    return false;
}

} // namespace Robot
} // namespace AnalysisRobot
