# Analysis Robot Drivers

分析机器人硬件驱动程序集合，包含天平接口驱动、水分测定仪驱动和REST接口驱动。

## 📋 项目概述

本项目为分析机器人系统提供了三个核心驱动程序，已集成到整合编译工程中：

1. **天平接口驱动 (BalanceDriver)** - 支持FA2204N等MODBUS RTU协议的精密天平
2. **水分测定仪驱动 (MoistureAnalyzerDriver)** - 支持MODBUS RTU协议的水分测定仪
3. **REST接口驱动 (RestInterfaceDriver)** - 提供HTTP REST API服务，统一管理各种设备

## 🏗️ 项目结构（整合编译）

本项目已按照整合编译工程的要求重新组织：

## 🚀 主要特性

### 天平驱动 (BalanceDriver)
- ✅ MODBUS RTU通信协议支持
- ✅ 重量读取功能
- ✅ 去皮操作
- ✅ 状态监控和错误处理
- ✅ 可配置串口参数

### 水分测定仪驱动 (MoistureAnalyzerDriver)
- ✅ 完整的MODBUS命令集支持
- ✅ 净重和水分读取
- ✅ 加热控制（开始/停止）
- ✅ 开关仓控制
- ✅ 内校和去皮功能
- ✅ 过程状态监控

### REST接口驱动 (RestInterfaceDriver)
- ✅ 基于httplib的HTTP服务器
- ✅ RESTful API设计
- ✅ JSON格式数据交换
- ✅ 设备状态查询
- ✅ 异步任务处理
- ✅ 统一的错误处理

```
项目根目录/
├── hardwaredriver/                 # 硬件驱动目录
│   ├── balanceDriver/              # 天平驱动
│   │   ├── include/BalanceDriver.h
│   │   ├── src/BalanceDriver.cpp
│   │   └── CMakeLists.txt
│   ├── moistureAnalyzerDriver/     # 水分测定仪驱动
│   │   ├── include/MoistureAnalyzerDriver.h
│   │   ├── src/MoistureAnalyzerDriver.cpp
│   │   └── CMakeLists.txt
│   └── restInterfaceDriver/        # REST接口驱动
│       ├── include/RestInterfaceDriver.h
│       ├── src/RestInterfaceDriver.cpp
│       └── CMakeLists.txt
├── test/                           # 测试目录
│   ├── balanceDriverTest/          # 天平驱动测试
│   │   ├── src/balanceDriverTest.cpp
│   │   └── CMakeLists.txt
│   ├── moistureAnalyzerDriverTest/ # 水分测定仪驱动测试
│   │   ├── src/moistureAnalyzerDriverTest.cpp
│   │   └── CMakeLists.txt
│   └── restInterfaceDriverTest/    # REST接口驱动测试
│       ├── src/restInterfaceDriverTest.cpp
│       └── CMakeLists.txt
├── example/                        # 示例程序目录
│   └── analysisRobotExample/       # 完整示例
│       ├── src/analysisRobotExample.cpp
│       └── CMakeLists.txt
└── Analysis_Robot/                 # 文档和原型目录
    ├── docs/                       # 文档目录
    └── README.md                   # 本文件
```

## 🛠️ 依赖项

- **C++17** 或更高版本
- **libmodbus** - MODBUS通信库
- **jsoncpp** - JSON处理库
- **httplib** - HTTP库（包含在fuxicommon中）
- **CMake 3.10** 或更高版本

## 📦 构建说明

### 使用CMake构建

```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake .. -DBUILD_EXAMPLES=ON -DBUILD_TESTS=ON

# 编译
make -j4

# 安装（可选）
make install
```

### 构建选项

- `BUILD_BALANCE_DRIVER=ON` - 构建天平驱动（默认开启）
- `BUILD_MOISTURE_DRIVER=ON` - 构建水分测定仪驱动（默认开启）
- `BUILD_REST_INTERFACE=ON` - 构建REST接口驱动（默认开启）
- `BUILD_EXAMPLES=OFF` - 构建示例程序（默认关闭）
- `BUILD_TESTS=OFF` - 构建测试程序（默认关闭）

## 🔧 使用说明

### 天平驱动使用示例

```cpp
#include "BalanceDriver.h"

using namespace AnalysisRobot::Balance;

// 创建驱动实例
BalanceDriver balance;

// 配置参数
BalanceConfig config;
config.serialPort = "COM3";
config.baudRate = 9600;
config.slaveId = 1;

// 初始化和连接
balance.initialize(config);
balance.connect();

// 去皮
balance.tare();

// 读取重量
WeightReading reading = balance.readWeight();
if (reading.success) {
    std::cout << "Weight: " << reading.weight << " g" << std::endl;
}
```

### 水分测定仪驱动使用示例

```cpp
#include "MoistureAnalyzerDriver.h"

using namespace AnalysisRobot::Moisture;

// 创建驱动实例
MoistureAnalyzerDriver analyzer;

// 配置参数
MoistureConfig config;
config.serialPort = "COM4";
config.targetTemperature = 105;

// 初始化和连接
analyzer.initialize(config);
analyzer.connect();

// 开始加热
analyzer.startHeating();

// 读取水分
MoistureReading result = analyzer.readMoisture();
if (result.success) {
    std::cout << "Moisture: " << result.moisture << "%" << std::endl;
}
```

### REST接口驱动使用示例

```cpp
#include "RestInterfaceDriver.h"

using namespace AnalysisRobot::RestInterface;

// 创建驱动实例
RestInterfaceDriver restDriver;

// 配置参数
RestConfig config;
config.host = "0.0.0.0";
config.port = 8080;

// 初始化和启动
restDriver.initialize(config);
restDriver.start();

// 注册设备控制器
restDriver.registerDeviceController("balance", balanceController);
```

## 🌐 REST API 接口

### 基础接口

- `GET /health` - 健康检查
- `GET /info` - 服务器信息

### 设备状态查询

- `GET /api/{device}/status` - 获取设备状态

### 设备操作

- `POST /api/{device}/operation` - 执行设备操作

### 任务查询

- `GET /api/{device}/query?taskId={id}` - 查询任务状态

### 支持的设备

- `balance` - 天平设备
- `sampleBalance` - 倒样称量设备
- `moistureBalance` - 水分测定仪
- `robot` - 机械臂
- `repo` - 容器货架
- `dosing` - 加液装置
- `filter` - 过滤装置
- `heater` - 加热装置
- 等等...

## 📝 API 使用示例

### 天平操作

```bash
# 获取天平状态
curl http://localhost:8080/api/balance/status

# 执行去皮操作
curl -X POST http://localhost:8080/api/balance/operation \
  -H "Content-Type: application/json" \
  -d '{"action":"RESET"}'

# 执行称重操作
curl -X POST http://localhost:8080/api/balance/operation \
  -H "Content-Type: application/json" \
  -d '{"action":"WEIGH"}'
```

### 水分测定仪操作

```bash
# 获取设备状态
curl http://localhost:8080/api/moistureBalance/status

# 清零操作
curl -X POST http://localhost:8080/api/moistureBalance/operation \
  -H "Content-Type: application/json" \
  -d '{"action":"ZERO"}'

# 开始加热
curl -X POST http://localhost:8080/api/moistureBalance/operation \
  -H "Content-Type: application/json" \
  -d '{"action":"HEAT"}'

# 获取测定结果
curl -X POST http://localhost:8080/api/moistureBalance/operation \
  -H "Content-Type: application/json" \
  -d '{"action":"GET_RESULT"}'
```

## 🔍 故障排除

### 常见问题

1. **串口连接失败**
   - 检查串口号是否正确
   - 确认串口权限
   - 验证波特率和其他参数

2. **MODBUS通信错误**
   - 检查设备地址设置
   - 确认电缆连接
   - 验证设备电源状态

3. **HTTP服务器启动失败**
   - 检查端口是否被占用
   - 确认防火墙设置
   - 验证网络配置

### 调试建议

- 启用详细日志记录
- 使用串口调试工具验证通信
- 检查设备文档确认协议参数
- 使用curl或Postman测试API

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## 📞 支持

如有问题或需要支持，请联系开发团队。
