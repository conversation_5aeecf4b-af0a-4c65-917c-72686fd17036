#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "RestInterfaceDriver.h"
#include <thread>
#include <chrono>
#include <curl/curl.h>

using namespace AnalysisRobot::RestInterface;
using namespace testing;

// Mock设备控制器
class MockDeviceController : public IDeviceController {
public:
    MOCK_METHOD(DeviceStatus, getStatus, (), (override));
    MOCK_METHOD(TaskInfo, executeOperation, (const Json::Value& request), (override));
    MOCK_METHOD(TaskInfo, queryTask, (int taskId), (override));
};

class RestInterfaceDriverTest : public ::testing::Test {
protected:
    void SetUp() override {
        driver = std::make_unique<RestInterfaceDriver>();
        mockController = std::make_shared<MockDeviceController>();
    }

    void TearDown() override {
        if (driver && driver->isRunning()) {
            driver->stop();
        }
    }

    std::unique_ptr<RestInterfaceDriver> driver;
    std::shared_ptr<MockDeviceController> mockController;
};

// 测试驱动初始化
TEST_F(RestInterfaceDriverTest, InitializeWithValidConfig) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8081;  // 使用不同端口避免冲突
    config.enableLogging = true;

    bool result = driver->initialize(config);
    EXPECT_TRUE(result);
    EXPECT_FALSE(driver->isRunning());
}

// 测试服务器启动和停止
TEST_F(RestInterfaceDriverTest, StartAndStopServer) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8082;
    config.enableLogging = false;  // 减少测试输出

    EXPECT_TRUE(driver->initialize(config));
    
    // 启动服务器
    bool startResult = driver->start();
    EXPECT_TRUE(startResult);
    EXPECT_TRUE(driver->isRunning());

    // 等待服务器启动
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 停止服务器
    driver->stop();
    EXPECT_FALSE(driver->isRunning());
}

// 测试重复启动
TEST_F(RestInterfaceDriverTest, DuplicateStart) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8083;
    config.enableLogging = false;

    EXPECT_TRUE(driver->initialize(config));
    EXPECT_TRUE(driver->start());
    
    // 尝试重复启动
    bool secondStart = driver->start();
    EXPECT_FALSE(secondStart);
    EXPECT_FALSE(driver->getLastError().empty());

    driver->stop();
}

// 测试设备控制器注册
TEST_F(RestInterfaceDriverTest, RegisterDeviceController) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8084;
    config.enableLogging = false;

    EXPECT_TRUE(driver->initialize(config));
    
    // 注册设备控制器
    driver->registerDeviceController("testDevice", mockController);
    
    // 验证服务器信息包含设备数量
    std::string serverInfo = driver->getServerInfo();
    EXPECT_THAT(serverInfo, HasSubstr("Registered Devices: 1"));
}

// 测试路由注册
TEST_F(RestInterfaceDriverTest, RegisterRoutes) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8085;
    config.enableLogging = false;

    EXPECT_TRUE(driver->initialize(config));

    bool routeCalled = false;
    driver->registerGetRoute("/test", [&](const httplib::Request& req, httplib::Response& res) {
        routeCalled = true;
        res.set_content("test response", "text/plain");
    });

    EXPECT_TRUE(driver->start());
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 这里可以添加HTTP客户端测试，但需要额外的依赖
    // 暂时只测试路由注册不会崩溃

    driver->stop();
}

// 测试配置参数验证
TEST_F(RestInterfaceDriverTest, ConfigValidation) {
    RestConfig config;
    
    // 测试默认配置
    EXPECT_EQ(config.host, "0.0.0.0");
    EXPECT_EQ(config.port, 8080);
    EXPECT_EQ(config.maxConnections, 100);
    EXPECT_EQ(config.threadPoolSize, 4);
    EXPECT_TRUE(config.enableLogging);
    EXPECT_EQ(config.logLevel, "INFO");
}

// 测试任务信息结构
TEST_F(RestInterfaceDriverTest, TaskInfoStructure) {
    TaskInfo task;
    
    // 测试默认值
    EXPECT_EQ(task.taskId, 0);
    EXPECT_TRUE(task.action.empty());
    EXPECT_EQ(task.status, TaskStatus::SUBMITTED);
    EXPECT_TRUE(task.message.empty());
    EXPECT_TRUE(task.updateTime.empty());
}

// 测试设备状态结构
TEST_F(RestInterfaceDriverTest, DeviceStatusStructure) {
    DeviceStatus status;
    
    // 测试默认值
    EXPECT_TRUE(status.name.empty());
    EXPECT_TRUE(status.description.empty());
    EXPECT_TRUE(status.status.empty());
    EXPECT_TRUE(status.message.empty());
    EXPECT_TRUE(status.updateTime.empty());
}

// 测试任务状态枚举
TEST_F(RestInterfaceDriverTest, TaskStatusEnum) {
    EXPECT_EQ(static_cast<int>(TaskStatus::SUBMITTED), 0);
    EXPECT_EQ(static_cast<int>(TaskStatus::IN_PROGRESS), 1);
    EXPECT_EQ(static_cast<int>(TaskStatus::SUCCESS), 2);
    EXPECT_EQ(static_cast<int>(TaskStatus::FAILED), 3);
}

// 测试服务器信息
TEST_F(RestInterfaceDriverTest, GetServerInfo) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8086;

    driver->initialize(config);

    std::string serverInfo = driver->getServerInfo();
    EXPECT_FALSE(serverInfo.empty());
    EXPECT_THAT(serverInfo, HasSubstr("REST Interface Driver"));
    EXPECT_THAT(serverInfo, HasSubstr("127.0.0.1"));
    EXPECT_THAT(serverInfo, HasSubstr("8086"));
    EXPECT_THAT(serverInfo, HasSubstr("Stopped"));
}

// 测试错误处理
TEST_F(RestInterfaceDriverTest, ErrorHandling) {
    // 测试未初始化就启动
    bool result = driver->start();
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
}

// 测试URI路径解析
TEST_F(RestInterfaceDriverTest, UriPathParsing) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8087;

    driver->initialize(config);

    // 这是一个白盒测试，测试内部的URI解析逻辑
    // 在实际实现中，可能需要添加测试友元或公共测试接口
    
    // 测试基本路径解析逻辑
    std::string testPath = "/api/balance/status";
    std::vector<std::string> expectedComponents = {"api", "balance", "status"};
    
    // 简单的路径分割测试
    std::vector<std::string> components;
    std::string path = testPath;
    if (!path.empty() && path[0] == '/') {
        path = path.substr(1);
    }
    
    std::istringstream iss(path);
    std::string component;
    while (std::getline(iss, component, '/')) {
        if (!component.empty()) {
            components.push_back(component);
        }
    }
    
    EXPECT_EQ(components, expectedComponents);
}

// 测试JSON响应创建
TEST_F(RestInterfaceDriverTest, JsonResponseCreation) {
    Json::Value testData;
    testData["status"] = "success";
    testData["message"] = "test message";
    testData["value"] = 123;

    // 测试JSON序列化
    Json::StreamWriterBuilder builder;
    std::string jsonString = Json::writeString(builder, testData);
    
    EXPECT_FALSE(jsonString.empty());
    EXPECT_THAT(jsonString, HasSubstr("success"));
    EXPECT_THAT(jsonString, HasSubstr("test message"));
    EXPECT_THAT(jsonString, HasSubstr("123"));
}

// 性能测试：测试多次操作的性能
TEST_F(RestInterfaceDriverTest, PerformanceTest) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8088;
    config.enableLogging = false;

    driver->initialize(config);

    auto start = std::chrono::high_resolution_clock::now();
    
    // 执行多次操作
    for (int i = 0; i < 100; ++i) {
        driver->getServerInfo();
        driver->registerDeviceController("device" + std::to_string(i), mockController);
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // 确保操作在合理时间内完成
    EXPECT_LT(duration.count(), 1000);
}

// 线程安全测试
TEST_F(RestInterfaceDriverTest, ThreadSafetyTest) {
    RestConfig config;
    config.host = "127.0.0.1";
    config.port = 8089;
    config.enableLogging = false;

    driver->initialize(config);

    std::vector<std::thread> threads;
    std::atomic<int> successCount{0};
    std::atomic<int> errorCount{0};

    // 创建多个线程同时访问驱动
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&, i]() {
            for (int j = 0; j < 10; ++j) {
                try {
                    driver->getServerInfo();
                    driver->registerDeviceController("device" + std::to_string(i * 10 + j), mockController);
                    successCount++;
                } catch (...) {
                    errorCount++;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 验证没有崩溃，所有操作都完成了
    EXPECT_EQ(successCount + errorCount, 100);
}

// 测试Mock设备控制器
TEST_F(RestInterfaceDriverTest, MockDeviceController) {
    DeviceStatus mockStatus;
    mockStatus.name = "Test Device";
    mockStatus.status = "SUCCESS";
    mockStatus.message = "Device is working";

    TaskInfo mockTask;
    mockTask.taskId = 1001;
    mockTask.action = "TEST_ACTION";
    mockTask.status = TaskStatus::SUCCESS;
    mockTask.message = "Task completed";

    EXPECT_CALL(*mockController, getStatus())
        .WillOnce(Return(mockStatus));

    EXPECT_CALL(*mockController, executeOperation(_))
        .WillOnce(Return(mockTask));

    EXPECT_CALL(*mockController, queryTask(1001))
        .WillOnce(Return(mockTask));

    // 测试Mock调用
    DeviceStatus status = mockController->getStatus();
    EXPECT_EQ(status.name, "Test Device");
    EXPECT_EQ(status.status, "SUCCESS");

    Json::Value request;
    request["action"] = "TEST_ACTION";
    TaskInfo task = mockController->executeOperation(request);
    EXPECT_EQ(task.taskId, 1001);
    EXPECT_EQ(task.action, "TEST_ACTION");

    TaskInfo queriedTask = mockController->queryTask(1001);
    EXPECT_EQ(queriedTask.taskId, 1001);
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
