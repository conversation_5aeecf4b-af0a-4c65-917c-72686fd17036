# HeatingMagneticStirrerDriver 测试

这个目录包含了对 `HeatingMagneticStirrerDriver` 类的全面测试套件。

## 测试覆盖范围

### 基础功能测试
- 构造函数和析构函数
- 初始化和配置验证
- 连接和断开连接
- 状态管理和回调

### 读取功能测试
- 温度和转速读取
- 错误处理和状态更新

### 控制功能测试
- 温度控制开关
- 电机控制开关
- 电机方向控制
- 程控控制

### 设置功能测试
- 目标温度设置
- 电机转速设置
- 定时器设置
- 程控步骤设置

### 参数验证测试
- 温度范围验证 (15-350°C)
- 转速范围验证 (50-2000 RPM)
- 程控编号验证 (0-9)
- 程控步骤验证 (1-4步)

### 边界条件测试
- 边界值测试
- 无效输入处理
- 错误状态处理

### 性能和稳定性测试
- 性能测试
- 线程安全测试
- 状态转换测试

## 构建和运行

### 前提条件
- CMake 3.10+
- Google Test (gtest)
- Google Mock (gmock)
- libmodbus
- 其他项目依赖

### 构建测试
```bash
# 在项目根目录下
mkdir build
cd build
cmake ..
make heatingMagneticStirrerDriverTest
```

### 运行测试
```bash
# 运行所有测试
./test/heatingMagneticStirrerDriverTest/heatingMagneticStirrerDriverTest

# 运行特定测试
./test/heatingMagneticStirrerDriverTest/heatingMagneticStirrerDriverTest --gtest_filter="*InitializeWithValidConfig*"

# 运行测试并显示详细输出
./test/heatingMagneticStirrerDriverTest/heatingMagneticStirrerDriverTest --gtest_output=xml:test_results.xml
```

## 测试说明

### 模拟环境
由于这些测试不依赖实际的硬件设备，大部分测试会在以下情况下运行：
- 没有实际的串口连接
- 没有实际的MODBUS设备
- 测试主要验证参数验证、错误处理和状态管理逻辑

### 实际硬件测试
如果需要在实际硬件上测试，需要：
1. 连接加热磁力搅拌器设备
2. 配置正确的串口参数
3. 修改测试中的配置参数以匹配实际设备

### 测试结果解释
- **PASS**: 测试通过，功能正常
- **FAIL**: 测试失败，可能存在问题
- 大部分连接相关的测试会因为没有实际设备而失败，这是正常的

## 测试用例详情

### 基础功能测试
- `InitializeWithValidConfig`: 测试有效配置的初始化
- `InitializeWithInvalidConfig`: 测试无效配置的处理
- `StatusCallback`: 测试状态回调机制
- `ConnectionStatus`: 测试连接状态检查

### 参数验证测试
- `TemperatureValidation`: 测试温度参数验证
- `SpeedValidation`: 测试转速参数验证
- `ProgramNumberValidation`: 测试程控编号验证

### 程控功能测试
- `SetProgramStepsValid`: 测试有效程控步骤设置
- `SetProgramStepsInvalidTemperature`: 测试无效温度的程控步骤
- `SetProgramStepsInvalidSpeed`: 测试无效转速的程控步骤

### 性能测试
- `PerformanceTest`: 测试多次操作的性能
- `ThreadSafetyTest`: 测试线程安全性

## 扩展测试

如果需要添加更多测试，可以：
1. 在 `heatingMagneticStirrerDriverTest.cpp` 中添加新的测试用例
2. 使用 Google Test 的 `TEST_F` 宏
3. 遵循现有的测试命名约定

## 故障排除

### 常见问题
1. **编译错误**: 检查依赖库是否正确安装
2. **链接错误**: 确保 CMakeLists.txt 中的依赖配置正确
3. **运行时错误**: 检查是否有权限访问串口设备

### 调试技巧
- 使用 `--gtest_break_on_failure` 在失败时中断
- 使用 `--gtest_repeat=N` 重复运行测试
- 使用 `--gtest_shuffle` 随机化测试顺序
