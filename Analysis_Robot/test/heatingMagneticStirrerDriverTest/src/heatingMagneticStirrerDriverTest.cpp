#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "HeatingMagneticStirrerDriver.h"
#include <thread>
#include <chrono>
#include <atomic>

using namespace AnalysisRobot::HeatingMagneticStirrer;
using namespace testing;

class HeatingMagneticStirrerDriverTest : public ::testing::Test {
protected:
    void SetUp() override {
        driver = std::make_unique<HeatingMagneticStirrerDriver>();
    }

    void TearDown() override {
        if (driver) {
            driver->disconnect();
        }
    }

    std::unique_ptr<HeatingMagneticStirrerDriver> driver;
};

// ========== 基础功能测试 ==========

// 测试驱动初始化
TEST_F(HeatingMagneticStirrerDriverTest, InitializeWithValidConfig) {
    StirrerConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;
    config.responseTimeout = 1000;

    // 注意：这个测试可能会失败，因为实际的串口可能不存在
    // 在实际环境中，需要模拟MODBUS设备或使用虚拟串口
    bool result = driver->initialize(config);
    
    // 检查初始化结果
    EXPECT_TRUE(result || !driver->getLastError().empty());
    EXPECT_EQ(driver->getStatus(), StirrerStatus::DISCONNECTED);
}

// 测试无效配置的初始化
TEST_F(HeatingMagneticStirrerDriverTest, InitializeWithInvalidConfig) {
    StirrerConfig config;
    config.serialPort = "";  // 无效串口
    config.baudRate = 9600;
    config.slaveId = 1;

    bool result = driver->initialize(config);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
}

// 测试状态回调
TEST_F(HeatingMagneticStirrerDriverTest, StatusCallback) {
    bool callbackCalled = false;
    StirrerStatus receivedStatus;
    std::string receivedMessage;

    driver->setStatusCallback([&](StirrerStatus status, const std::string& message) {
        callbackCalled = true;
        receivedStatus = status;
        receivedMessage = message;
    });

    StirrerConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    EXPECT_TRUE(callbackCalled);
    EXPECT_EQ(receivedStatus, StirrerStatus::DISCONNECTED);
    EXPECT_FALSE(receivedMessage.empty());
}

// 测试连接状态检查
TEST_F(HeatingMagneticStirrerDriverTest, ConnectionStatus) {
    EXPECT_FALSE(driver->isConnected());
    EXPECT_EQ(driver->getStatus(), StirrerStatus::DISCONNECTED);
}

// ========== 读取功能测试 ==========

// 测试未连接时的温度和转速读取
TEST_F(HeatingMagneticStirrerDriverTest, ReadTemperatureAndSpeedWithoutConnection) {
    TemperatureSpeedReading reading = driver->readTemperatureAndSpeed();
    EXPECT_FALSE(reading.success);
    EXPECT_FALSE(reading.errorMsg.empty());
    EXPECT_THAT(reading.errorMsg, HasSubstr("Not connected"));
}

// ========== 控制功能测试 ==========

// 测试未连接时的温度控制
TEST_F(HeatingMagneticStirrerDriverTest, TemperatureControlWithoutConnection) {
    bool result = driver->setTemperatureControl(true);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的电机控制
TEST_F(HeatingMagneticStirrerDriverTest, MotorControlWithoutConnection) {
    bool result = driver->setMotorControl(true);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的电机方向控制
TEST_F(HeatingMagneticStirrerDriverTest, MotorDirectionWithoutConnection) {
    bool result = driver->setMotorDirection(MotorDirection::CLOCKWISE);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的程控控制
TEST_F(HeatingMagneticStirrerDriverTest, ProgramControlWithoutConnection) {
    bool result = driver->setProgramControl(1, true);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== 设置功能测试 ==========

// 测试未连接时的温度设置
TEST_F(HeatingMagneticStirrerDriverTest, SetTargetTemperatureWithoutConnection) {
    bool result = driver->setTargetTemperature(100);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的转速设置
TEST_F(HeatingMagneticStirrerDriverTest, SetMotorSpeedWithoutConnection) {
    bool result = driver->setMotorSpeed(500);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试未连接时的定时器设置
TEST_F(HeatingMagneticStirrerDriverTest, SetTimerMinutesWithoutConnection) {
    bool result = driver->setTimerMinutes(30);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== 参数验证测试 ==========

// 测试温度范围验证
TEST_F(HeatingMagneticStirrerDriverTest, TemperatureValidation) {
    StirrerConfig config;
    driver->initialize(config);
    
    // 测试无效温度（太低）
    bool result1 = driver->setTargetTemperature(10);  // 低于15℃
    EXPECT_FALSE(result1);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid temperature"));
    
    // 测试无效温度（太高）
    bool result2 = driver->setTargetTemperature(400);  // 高于350℃
    EXPECT_FALSE(result2);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid temperature"));
}

// 测试转速范围验证
TEST_F(HeatingMagneticStirrerDriverTest, SpeedValidation) {
    StirrerConfig config;
    driver->initialize(config);
    
    // 测试无效转速（太低）
    bool result1 = driver->setMotorSpeed(30);  // 低于50 RPM
    EXPECT_FALSE(result1);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid speed"));
    
    // 测试无效转速（太高）
    bool result2 = driver->setMotorSpeed(2500);  // 高于2000 RPM
    EXPECT_FALSE(result2);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid speed"));
}

// 测试程控编号验证
TEST_F(HeatingMagneticStirrerDriverTest, ProgramNumberValidation) {
    StirrerConfig config;
    driver->initialize(config);
    
    // 测试无效程控编号
    bool result = driver->setProgramControl(15, true);  // 大于9
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid program number"));
}

// ========== 设备信息测试 ==========

// 测试设备信息获取
TEST_F(HeatingMagneticStirrerDriverTest, GetDeviceInfo) {
    StirrerConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    std::string deviceInfo = driver->getDeviceInfo();
    EXPECT_FALSE(deviceInfo.empty());
    EXPECT_THAT(deviceInfo, HasSubstr("Heating Magnetic Stirrer Driver"));
    EXPECT_THAT(deviceInfo, HasSubstr("COM1"));
    EXPECT_THAT(deviceInfo, HasSubstr("9600"));
    EXPECT_THAT(deviceInfo, HasSubstr("Disconnected"));
}

// ========== 配置结构测试 ==========

// 测试配置参数验证
TEST_F(HeatingMagneticStirrerDriverTest, ConfigValidation) {
    StirrerConfig config;
    
    // 测试默认配置
    EXPECT_EQ(config.serialPort, "COM1");
    EXPECT_EQ(config.baudRate, 9600);
    EXPECT_EQ(config.parity, 'N');
    EXPECT_EQ(config.dataBits, 8);
    EXPECT_EQ(config.stopBits, 1);
    EXPECT_EQ(config.slaveId, 1);
    EXPECT_EQ(config.responseTimeout, 1000);
}

// 测试温度转速读数结构
TEST_F(HeatingMagneticStirrerDriverTest, TemperatureSpeedReadingStructure) {
    TemperatureSpeedReading reading;
    
    // 测试默认值
    EXPECT_FALSE(reading.success);
    EXPECT_EQ(reading.temperature, 0.0);
    EXPECT_EQ(reading.speed, 0.0);
    EXPECT_EQ(reading.rawTemperature, 0u);
    EXPECT_EQ(reading.rawSpeed, 0u);
    EXPECT_TRUE(reading.errorMsg.empty());
}

// 测试程控步骤结构
TEST_F(HeatingMagneticStirrerDriverTest, ProgramStepStructure) {
    ProgramStep step;
    
    // 测试默认值
    EXPECT_EQ(step.time, 0u);
    EXPECT_EQ(step.temperature, 0u);
    EXPECT_EQ(step.speed, 0u);
    EXPECT_EQ(step.direction, MotorDirection::CLOCKWISE);
    
    // 测试构造函数
    ProgramStep step2(10, 100, 500, MotorDirection::COUNTERCLOCKWISE);
    EXPECT_EQ(step2.time, 10u);
    EXPECT_EQ(step2.temperature, 100u);
    EXPECT_EQ(step2.speed, 500u);
    EXPECT_EQ(step2.direction, MotorDirection::COUNTERCLOCKWISE);
}

// ========== 程控步骤测试 ==========

// 测试程控步骤设置 - 未连接
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsWithoutConnection) {
    std::vector<ProgramStep> steps;
    steps.emplace_back(10, 100, 500, MotorDirection::CLOCKWISE);

    bool result = driver->setProgramSteps(1, steps);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试程控步骤设置 - 无效程控编号
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsInvalidNumber) {
    StirrerConfig config;
    driver->initialize(config);

    std::vector<ProgramStep> steps;
    steps.emplace_back(10, 100, 500, MotorDirection::CLOCKWISE);

    bool result = driver->setProgramSteps(15, steps);  // 无效编号
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid program number"));
}

// 测试程控步骤设置 - 空步骤
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsEmpty) {
    StirrerConfig config;
    driver->initialize(config);

    std::vector<ProgramStep> steps;  // 空步骤

    bool result = driver->setProgramSteps(1, steps);
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid number of program steps"));
}

// 测试程控步骤设置 - 步骤过多
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsTooMany) {
    StirrerConfig config;
    driver->initialize(config);

    std::vector<ProgramStep> steps;
    for (int i = 0; i < 5; ++i) {  // 超过4步
        steps.emplace_back(10, 100, 500, MotorDirection::CLOCKWISE);
    }

    bool result = driver->setProgramSteps(1, steps);
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid number of program steps"));
}

// 测试程控步骤设置 - 无效温度
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsInvalidTemperature) {
    StirrerConfig config;
    driver->initialize(config);

    std::vector<ProgramStep> steps;
    steps.emplace_back(10, 400, 500, MotorDirection::CLOCKWISE);  // 无效温度

    bool result = driver->setProgramSteps(1, steps);
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid temperature in step"));
}

// 测试程控步骤设置 - 无效转速
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsInvalidSpeed) {
    StirrerConfig config;
    driver->initialize(config);

    std::vector<ProgramStep> steps;
    steps.emplace_back(10, 100, 30, MotorDirection::CLOCKWISE);  // 无效转速

    bool result = driver->setProgramSteps(1, steps);
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid speed in step"));
}

// 测试程控步骤设置 - 有效步骤
TEST_F(HeatingMagneticStirrerDriverTest, SetProgramStepsValid) {
    StirrerConfig config;
    driver->initialize(config);

    std::vector<ProgramStep> steps;
    steps.emplace_back(10, 100, 500, MotorDirection::CLOCKWISE);
    steps.emplace_back(20, 150, 800, MotorDirection::COUNTERCLOCKWISE);

    // 由于没有实际连接，这个测试会失败，但我们可以验证参数验证逻辑
    bool result = driver->setProgramSteps(1, steps);
    // 在没有连接的情况下，应该在连接检查时失败，而不是参数验证时失败
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// ========== 边界值测试 ==========

// 测试温度边界值
TEST_F(HeatingMagneticStirrerDriverTest, TemperatureBoundaryValues) {
    StirrerConfig config;
    driver->initialize(config);

    // 测试最小有效温度
    bool result1 = driver->setTargetTemperature(15);
    EXPECT_FALSE(result1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试最大有效温度
    bool result2 = driver->setTargetTemperature(350);
    EXPECT_FALSE(result2);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试边界外的值
    bool result3 = driver->setTargetTemperature(14);  // 低于最小值
    EXPECT_FALSE(result3);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid temperature"));

    bool result4 = driver->setTargetTemperature(351);  // 高于最大值
    EXPECT_FALSE(result4);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid temperature"));
}

// 测试转速边界值
TEST_F(HeatingMagneticStirrerDriverTest, SpeedBoundaryValues) {
    StirrerConfig config;
    driver->initialize(config);

    // 测试最小有效转速
    bool result1 = driver->setMotorSpeed(50);
    EXPECT_FALSE(result1);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试最大有效转速
    bool result2 = driver->setMotorSpeed(2000);
    EXPECT_FALSE(result2);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));

    // 测试边界外的值
    bool result3 = driver->setMotorSpeed(49);  // 低于最小值
    EXPECT_FALSE(result3);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid speed"));

    bool result4 = driver->setMotorSpeed(2001);  // 高于最大值
    EXPECT_FALSE(result4);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid speed"));
}

// 测试程控编号边界值
TEST_F(HeatingMagneticStirrerDriverTest, ProgramNumberBoundaryValues) {
    StirrerConfig config;
    driver->initialize(config);

    // 测试有效程控编号
    for (uint8_t i = 0; i <= 9; ++i) {
        bool result = driver->setProgramControl(i, true);
        EXPECT_FALSE(result);  // 会因为未连接而失败
        EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
    }

    // 测试无效程控编号
    bool result = driver->setProgramControl(10, true);
    EXPECT_FALSE(result);
    EXPECT_THAT(driver->getLastError(), HasSubstr("Invalid program number"));
}

// ========== 性能测试 ==========

// 性能测试：测试多次操作的性能
TEST_F(HeatingMagneticStirrerDriverTest, PerformanceTest) {
    StirrerConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    auto start = std::chrono::high_resolution_clock::now();

    // 执行多次操作
    for (int i = 0; i < 100; ++i) {
        driver->getStatus();
        driver->getDeviceInfo();
        driver->getLastError();
    }

    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

    // 确保操作在合理时间内完成（这里设置为1秒）
    EXPECT_LT(duration.count(), 1000);
}

// ========== 线程安全测试 ==========

// 线程安全测试
TEST_F(HeatingMagneticStirrerDriverTest, ThreadSafetyTest) {
    StirrerConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    std::vector<std::thread> threads;
    std::atomic<int> successCount{0};
    std::atomic<int> errorCount{0};

    // 创建多个线程同时访问驱动
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&]() {
            for (int j = 0; j < 10; ++j) {
                try {
                    driver->getStatus();
                    driver->getDeviceInfo();
                    driver->getLastError();
                    successCount++;
                } catch (...) {
                    errorCount++;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 验证没有崩溃，所有操作都完成了
    EXPECT_EQ(successCount + errorCount, 100);
}

// ========== 状态转换测试 ==========

// 测试状态转换逻辑
TEST_F(HeatingMagneticStirrerDriverTest, StatusTransitions) {
    StirrerConfig config;
    driver->initialize(config);

    // 初始状态应该是DISCONNECTED
    EXPECT_EQ(driver->getStatus(), StirrerStatus::DISCONNECTED);

    // 测试状态回调中的状态转换
    std::vector<StirrerStatus> statusHistory;
    driver->setStatusCallback([&](StirrerStatus status, const std::string& message) {
        statusHistory.push_back(status);
    });

    // 重新初始化以触发状态回调
    driver->initialize(config);

    // 验证状态历史
    EXPECT_FALSE(statusHistory.empty());
    EXPECT_EQ(statusHistory.back(), StirrerStatus::DISCONNECTED);
}

// ========== 错误处理测试 ==========

// 测试错误信息的持久性
TEST_F(HeatingMagneticStirrerDriverTest, ErrorPersistence) {
    // 触发一个错误
    driver->setTargetTemperature(400);  // 无效温度
    std::string firstError = driver->getLastError();
    EXPECT_FALSE(firstError.empty());

    // 再次获取错误信息，应该保持不变
    std::string secondError = driver->getLastError();
    EXPECT_EQ(firstError, secondError);

    // 触发另一个错误
    driver->setMotorSpeed(30);  // 无效转速
    std::string thirdError = driver->getLastError();
    EXPECT_NE(firstError, thirdError);  // 错误信息应该更新
}

// ========== 枚举值测试 ==========

// 测试StirrerStatus枚举
TEST_F(HeatingMagneticStirrerDriverTest, StirrerStatusEnum) {
    EXPECT_EQ(static_cast<int>(StirrerStatus::DISCONNECTED), 0);
    EXPECT_EQ(static_cast<int>(StirrerStatus::CONNECTED), 1);
    EXPECT_EQ(static_cast<int>(StirrerStatus::HEATING), 2);
    EXPECT_EQ(static_cast<int>(StirrerStatus::STIRRING), 3);
    EXPECT_EQ(static_cast<int>(StirrerStatus::HEATING_STIRRING), 4);
    EXPECT_EQ(static_cast<int>(StirrerStatus::FAULT), 5);
}

// 测试MotorDirection枚举
TEST_F(HeatingMagneticStirrerDriverTest, MotorDirectionEnum) {
    EXPECT_EQ(static_cast<int>(MotorDirection::CLOCKWISE), 0);
    EXPECT_EQ(static_cast<int>(MotorDirection::COUNTERCLOCKWISE), 1);
}

// ========== 复杂场景测试 ==========

// 测试多个程控步骤的复杂场景
TEST_F(HeatingMagneticStirrerDriverTest, ComplexProgramStepsScenario) {
    StirrerConfig config;
    driver->initialize(config);

    // 创建复杂的程控步骤
    std::vector<ProgramStep> steps;
    steps.emplace_back(5, 50, 100, MotorDirection::CLOCKWISE);      // 步骤1
    steps.emplace_back(10, 100, 500, MotorDirection::CLOCKWISE);    // 步骤2
    steps.emplace_back(15, 150, 800, MotorDirection::COUNTERCLOCKWISE); // 步骤3
    steps.emplace_back(20, 200, 1000, MotorDirection::CLOCKWISE);   // 步骤4

    // 尝试设置程控步骤
    bool result = driver->setProgramSteps(5, steps);
    EXPECT_FALSE(result);  // 会因为未连接而失败
    EXPECT_THAT(driver->getLastError(), HasSubstr("Not connected"));
}

// 测试设备信息的完整性
TEST_F(HeatingMagneticStirrerDriverTest, DeviceInfoCompleteness) {
    StirrerConfig config;
    config.serialPort = "COM3";
    config.baudRate = 19200;
    config.slaveId = 5;

    driver->initialize(config);

    std::string deviceInfo = driver->getDeviceInfo();

    // 验证设备信息包含所有必要的信息
    EXPECT_THAT(deviceInfo, HasSubstr("Heating Magnetic Stirrer Driver"));
    EXPECT_THAT(deviceInfo, HasSubstr("COM3"));
    EXPECT_THAT(deviceInfo, HasSubstr("19200"));
    EXPECT_THAT(deviceInfo, HasSubstr("5"));
    EXPECT_THAT(deviceInfo, HasSubstr("Status:"));
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
