#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include "MoistureAnalyzerDriver.h"
#include <thread>
#include <chrono>

using namespace AnalysisRobot::Moisture;
using namespace testing;

class MoistureAnalyzerDriverTest : public ::testing::Test {
protected:
    void SetUp() override {
        driver = std::make_unique<MoistureAnalyzerDriver>();
    }

    void TearDown() override {
        if (driver) {
            driver->disconnect();
        }
    }

    std::unique_ptr<MoistureAnalyzerDriver> driver;
};

// 测试驱动初始化
TEST_F(MoistureAnalyzerDriverTest, InitializeWithValidConfig) {
    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;
    config.targetTemperature = 105;
    config.responseTimeout = 1000;

    bool result = driver->initialize(config);
    
    // 检查初始化结果
    EXPECT_TRUE(result || !driver->getLastError().empty());
    EXPECT_EQ(driver->getStatus(), MoistureStatus::DISCONNECTED);
}

// 测试无效配置的初始化
TEST_F(MoistureAnalyzerDriverTest, InitializeWithInvalidConfig) {
    MoistureConfig config;
    config.serialPort = "";  // 无效串口
    config.baudRate = 9600;
    config.slaveId = 1;

    bool result = driver->initialize(config);
    EXPECT_FALSE(result);
    EXPECT_FALSE(driver->getLastError().empty());
}

// 测试状态回调
TEST_F(MoistureAnalyzerDriverTest, StatusCallback) {
    bool callbackCalled = false;
    MoistureStatus receivedStatus;
    std::string receivedMessage;

    driver->setStatusCallback([&](MoistureStatus status, const std::string& message) {
        callbackCalled = true;
        receivedStatus = status;
        receivedMessage = message;
    });

    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    EXPECT_TRUE(callbackCalled);
    EXPECT_EQ(receivedStatus, MoistureStatus::DISCONNECTED);
    EXPECT_FALSE(receivedMessage.empty());
}

// 测试连接状态检查
TEST_F(MoistureAnalyzerDriverTest, ConnectionStatus) {
    EXPECT_FALSE(driver->isConnected());
    EXPECT_EQ(driver->getStatus(), MoistureStatus::DISCONNECTED);
}

// 测试未连接时的操作
TEST_F(MoistureAnalyzerDriverTest, OperationsWithoutConnection) {
    // 测试未连接时读取净重
    WeightReading reading = driver->readNetWeight();
    EXPECT_FALSE(reading.success);
    EXPECT_FALSE(reading.errorMsg.empty());

    // 测试未连接时去皮
    bool tareResult = driver->tare();
    EXPECT_FALSE(tareResult);
    EXPECT_FALSE(driver->getLastError().empty());

    // 测试未连接时开始加热
    bool heatResult = driver->startHeating();
    EXPECT_FALSE(heatResult);
    EXPECT_FALSE(driver->getLastError().empty());
}

// 测试设备信息获取
TEST_F(MoistureAnalyzerDriverTest, GetDeviceInfo) {
    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;
    config.targetTemperature = 105;

    driver->initialize(config);

    std::string deviceInfo = driver->getDeviceInfo();
    EXPECT_FALSE(deviceInfo.empty());
    EXPECT_THAT(deviceInfo, HasSubstr("Moisture Analyzer Driver"));
    EXPECT_THAT(deviceInfo, HasSubstr("COM1"));
    EXPECT_THAT(deviceInfo, HasSubstr("9600"));
    EXPECT_THAT(deviceInfo, HasSubstr("105"));
}

// 测试配置参数验证
TEST_F(MoistureAnalyzerDriverTest, ConfigValidation) {
    MoistureConfig config;
    
    // 测试默认配置
    EXPECT_EQ(config.serialPort, "COM1");
    EXPECT_EQ(config.baudRate, 9600);
    EXPECT_EQ(config.parity, 'N');
    EXPECT_EQ(config.dataBits, 8);
    EXPECT_EQ(config.stopBits, 1);
    EXPECT_EQ(config.slaveId, 1);
    EXPECT_EQ(config.responseTimeout, 1000);
    EXPECT_EQ(config.targetTemperature, 105);
}

// 测试重量读数结构
TEST_F(MoistureAnalyzerDriverTest, WeightReadingStructure) {
    WeightReading reading;
    
    // 测试默认值
    EXPECT_FALSE(reading.success);
    EXPECT_EQ(reading.weight, 0.0);
    EXPECT_EQ(reading.rawValue, 0u);
    EXPECT_TRUE(reading.errorMsg.empty());
}

// 测试水分读数结构
TEST_F(MoistureAnalyzerDriverTest, MoistureReadingStructure) {
    MoistureReading reading;
    
    // 测试默认值
    EXPECT_FALSE(reading.success);
    EXPECT_EQ(reading.moisture, 0.0);
    EXPECT_EQ(reading.dryWeight, 0.0);
    EXPECT_EQ(reading.initialWeight, 0.0);
    EXPECT_TRUE(reading.errorMsg.empty());
}

// 测试过程状态枚举
TEST_F(MoistureAnalyzerDriverTest, ProcessStatusEnum) {
    EXPECT_EQ(static_cast<int>(ProcessStatus::NORMAL), 1);
    EXPECT_EQ(static_cast<int>(ProcessStatus::CALIBRATING), 2);
    EXPECT_EQ(static_cast<int>(ProcessStatus::CALIBRATION_FAULT), 3);
    EXPECT_EQ(static_cast<int>(ProcessStatus::CALIBRATION_COMPLETE), 4);
    EXPECT_EQ(static_cast<int>(ProcessStatus::CALIBRATION_INVALID), 5);
    EXPECT_EQ(static_cast<int>(ProcessStatus::CLEAR_PAN), 6);
    EXPECT_EQ(static_cast<int>(ProcessStatus::REMOVING_WEIGHT), 7);
}

// 测试状态转换
TEST_F(MoistureAnalyzerDriverTest, StatusTransitions) {
    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);
    EXPECT_EQ(driver->getStatus(), MoistureStatus::DISCONNECTED);

    // 注意：实际连接可能失败，但状态应该正确处理
    driver->connect();
    // 状态应该是CONNECTED或FAULT
    MoistureStatus status = driver->getStatus();
    EXPECT_TRUE(status == MoistureStatus::CONNECTED || status == MoistureStatus::FAULT);
}

// 测试温度设置范围
TEST_F(MoistureAnalyzerDriverTest, TemperatureRange) {
    MoistureConfig config;
    
    // 测试不同温度值
    config.targetTemperature = 50;
    EXPECT_EQ(config.targetTemperature, 50);
    
    config.targetTemperature = 200;
    EXPECT_EQ(config.targetTemperature, 200);
    
    config.targetTemperature = 105;  // 默认值
    EXPECT_EQ(config.targetTemperature, 105);
}

// 测试特殊重量值处理
TEST_F(MoistureAnalyzerDriverTest, SpecialWeightValues) {
    // 测试溢出和欠载检测
    // 这些是根据文档中的特殊值
    uint32_t overloadValue = 0x7FFFFFFF;
    uint32_t underloadValue = 0x80000000;
    
    EXPECT_EQ(overloadValue, 0x7FFFFFFF);
    EXPECT_EQ(underloadValue, 0x80000000);
}

// 性能测试：测试多次操作的性能
TEST_F(MoistureAnalyzerDriverTest, PerformanceTest) {
    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    auto start = std::chrono::high_resolution_clock::now();
    
    // 执行多次操作
    for (int i = 0; i < 100; ++i) {
        driver->getStatus();
        driver->getDeviceInfo();
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
    
    // 确保操作在合理时间内完成
    EXPECT_LT(duration.count(), 1000);
}

// 线程安全测试
TEST_F(MoistureAnalyzerDriverTest, ThreadSafetyTest) {
    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;

    driver->initialize(config);

    std::vector<std::thread> threads;
    std::atomic<int> successCount{0};
    std::atomic<int> errorCount{0};

    // 创建多个线程同时访问驱动
    for (int i = 0; i < 10; ++i) {
        threads.emplace_back([&]() {
            for (int j = 0; j < 10; ++j) {
                try {
                    driver->getStatus();
                    driver->getDeviceInfo();
                    successCount++;
                } catch (...) {
                    errorCount++;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 验证没有崩溃，所有操作都完成了
    EXPECT_EQ(successCount + errorCount, 100);
}

// 测试加热流程
TEST_F(MoistureAnalyzerDriverTest, HeatingWorkflow) {
    MoistureConfig config;
    config.serialPort = "COM1";
    config.baudRate = 9600;
    config.slaveId = 1;
    config.targetTemperature = 105;

    driver->initialize(config);

    // 测试加热操作序列（即使连接失败，也应该有正确的错误处理）
    bool startResult = driver->startHeating();
    bool stopResult = driver->stopHeating();
    bool returnResult = driver->returnToWeighing();

    // 由于没有实际设备，这些操作应该失败但不应该崩溃
    EXPECT_FALSE(startResult);
    EXPECT_FALSE(stopResult);
    EXPECT_FALSE(returnResult);
    
    // 应该有错误信息
    EXPECT_FALSE(driver->getLastError().empty());
}

int main(int argc, char** argv) {
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
