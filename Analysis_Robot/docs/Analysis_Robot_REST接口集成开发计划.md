# Analysis_Robot REST接口连接真实驱动器 - 后续开发计划

**项目阶段：** REST接口与硬件驱动器集成  
**开发周期：** 2025年1月30日 - 2025年2月7日 (9天)  
**核心目标：** 实现REST API与底层MODBUS驱动器的完整集成  

---

## 🎯 核心架构设计

### 当前架构分析
基于代码分析，当前架构包含：

1. **底层驱动器** (已完成85%)
   - `BalanceDriver` - 天平MODBUS驱动
   - `MoistureAnalyzerDriver` - 水分测定仪MODBUS驱动

2. **REST接口层** (已完成70%)
   - `RestInterfaceDriver` - HTTP服务器
   - `IDeviceController` - 设备控制器接口

3. **缺失的中间层** (需要开发)
   - 具体设备控制器实现
   - 驱动器适配器
   - 任务管理器

### 目标架构
```
HTTP Client → REST API → Device Controller → Hardware Driver → MODBUS Device
     ↓              ↓              ↓                ↓              ↓
  JSON请求    →  路由处理   →   业务逻辑    →    MODBUS通信   →   物理设备
```

---

## 📋 详细开发计划

### 第1阶段 (1月30日-31日) - 设备控制器实现

#### 1.1 创建设备控制器基类 (4小时)
**文件：** `Analysis_Robot/drivers/common/include/BaseDeviceController.h`

```cpp
class BaseDeviceController : public IDeviceController {
protected:
    std::string m_deviceName;
    std::string m_deviceType;
    std::map<int, TaskInfo> m_tasks;
    std::mutex m_tasksMutex;
    int m_nextTaskId;
    
public:
    BaseDeviceController(const std::string& name, const std::string& type);
    virtual ~BaseDeviceController() = default;
    
    // 实现IDeviceController接口
    DeviceStatus getStatus() override;
    TaskInfo executeOperation(const Json::Value& request) override;
    TaskInfo queryTask(int taskId) override;
    
    // 子类需要实现的纯虚函数
    virtual DeviceStatus getDeviceStatus() = 0;
    virtual TaskInfo executeDeviceOperation(const std::string& action, const Json::Value& data) = 0;
    
protected:
    int generateTaskId();
    void updateTaskStatus(int taskId, TaskStatus status, const std::string& message);
    Json::Value createSuccessResponse(const std::string& action, const Json::Value& data = Json::Value());
    Json::Value createErrorResponse(const std::string& action, const std::string& error);
};
```

#### 1.2 实现天平设备控制器 (6小时)
**文件：** `Analysis_Robot/drivers/balanceDriver/include/BalanceController.h`

```cpp
class BalanceController : public BaseDeviceController {
private:
    std::shared_ptr<BalanceDriver> m_driver;
    BalanceConfig m_config;
    std::thread m_monitorThread;
    std::atomic<bool> m_monitoring;
    
public:
    BalanceController(const BalanceConfig& config);
    ~BalanceController();
    
    // 实现基类纯虚函数
    DeviceStatus getDeviceStatus() override;
    TaskInfo executeDeviceOperation(const std::string& action, const Json::Value& data) override;
    
    // 天平特有功能
    bool initialize();
    bool connect();
    void disconnect();
    
private:
    void monitorLoop();
    TaskInfo handleWeighOperation();
    TaskInfo handleTareOperation();
    TaskInfo handleResetOperation();
    Json::Value convertWeightReading(const WeightReading& reading);
};
```

**支持的操作：**
- `WEIGH` - 称重操作
- `TARE` - 去皮操作  
- `RESET` - 复位操作
- `CALIBRATE` - 校准操作

#### 1.3 实现水分测定仪控制器 (6小时)
**文件：** `Analysis_Robot/drivers/moistureAnalyzerDriver/include/MoistureController.h`

```cpp
class MoistureController : public BaseDeviceController {
private:
    std::shared_ptr<MoistureAnalyzerDriver> m_driver;
    MoistureConfig m_config;
    std::thread m_processThread;
    std::atomic<bool> m_processing;
    
public:
    MoistureController(const MoistureConfig& config);
    ~MoistureController();
    
    // 实现基类纯虚函数
    DeviceStatus getDeviceStatus() override;
    TaskInfo executeDeviceOperation(const std::string& action, const Json::Value& data) override;
    
private:
    void processLoop();
    TaskInfo handleHeatOperation();
    TaskInfo handleStopOperation();
    TaskInfo handleZeroOperation();
    TaskInfo handleGetResultOperation();
    TaskInfo handleOpenChamberOperation();
    TaskInfo handleCloseChamberOperation();
};
```

**支持的操作：**
- `HEAT` - 开始加热
- `STOP` - 停止加热
- `ZERO` - 清零操作
- `GET_RESULT` - 获取测定结果
- `OPEN_CHAMBER` - 开启样品仓
- `CLOSE_CHAMBER` - 关闭样品仓

### 第2阶段 (2月1日-2日) - 任务管理和异步处理

#### 2.1 实现任务管理器 (4小时)
**文件：** `Analysis_Robot/drivers/common/include/TaskManager.h`

```cpp
class TaskManager {
private:
    std::map<int, std::shared_ptr<AsyncTask>> m_tasks;
    std::mutex m_tasksMutex;
    std::thread m_cleanupThread;
    std::atomic<bool> m_running;
    
public:
    TaskManager();
    ~TaskManager();
    
    int submitTask(std::function<TaskInfo()> taskFunction);
    TaskInfo getTaskStatus(int taskId);
    void cancelTask(int taskId);
    void cleanup();
    
private:
    void cleanupLoop();
};

class AsyncTask {
public:
    enum class State { PENDING, RUNNING, COMPLETED, FAILED, CANCELLED };
    
private:
    int m_taskId;
    State m_state;
    std::function<TaskInfo()> m_function;
    TaskInfo m_result;
    std::thread m_thread;
    std::mutex m_mutex;
    
public:
    AsyncTask(int taskId, std::function<TaskInfo()> func);
    ~AsyncTask();
    
    void start();
    void cancel();
    TaskInfo getResult();
    State getState();
};
```

#### 2.2 集成异步处理到设备控制器 (4小时)
修改设备控制器以支持长时间运行的操作：

```cpp
// 在BalanceController中添加异步支持
TaskInfo BalanceController::executeDeviceOperation(const std::string& action, const Json::Value& data) {
    if (action == "WEIGH") {
        // 同步操作，立即返回
        return handleWeighOperation();
    } else if (action == "CALIBRATE") {
        // 异步操作，提交到任务管理器
        int taskId = m_taskManager->submitTask([this]() {
            return handleCalibrateOperation();
        });
        
        TaskInfo task;
        task.taskId = taskId;
        task.action = action;
        task.status = TaskStatus::SUBMITTED;
        task.message = "校准任务已提交";
        return task;
    }
    // ... 其他操作
}
```

### 第3阶段 (2月3日-4日) - REST接口集成

#### 3.1 修改RestInterfaceDriver集成设备控制器 (6小时)

**增强设备注册功能：**
```cpp
// 在RestInterfaceDriver中添加
class RestInterfaceDriver {
private:
    std::map<std::string, std::shared_ptr<BaseDeviceController>> m_controllers;
    std::shared_ptr<TaskManager> m_taskManager;
    
public:
    // 注册具体设备控制器
    void registerBalanceController(const std::string& name, const BalanceConfig& config);
    void registerMoistureController(const std::string& name, const MoistureConfig& config);
    
    // 批量初始化设备
    bool initializeAllDevices();
    void shutdownAllDevices();
};
```

#### 3.2 实现设备工厂模式 (4小时)
**文件：** `Analysis_Robot/drivers/common/include/DeviceFactory.h`

```cpp
class DeviceFactory {
public:
    enum class DeviceType {
        BALANCE,
        MOISTURE_ANALYZER,
        ROBOT,
        DOSING_PUMP
    };
    
    static std::shared_ptr<BaseDeviceController> createDevice(
        DeviceType type, 
        const std::string& name,
        const Json::Value& config
    );
    
private:
    static std::shared_ptr<BalanceController> createBalanceController(
        const std::string& name, 
        const Json::Value& config
    );
    
    static std::shared_ptr<MoistureController> createMoistureController(
        const std::string& name, 
        const Json::Value& config
    );
};
```

### 第4阶段 (2月5日-6日) - 配置管理和错误处理

#### 4.1 实现配置管理器 (4小时)
**文件：** `Analysis_Robot/config/DeviceConfig.json`

```json
{
  "devices": {
    "balance1": {
      "type": "balance",
      "config": {
        "serialPort": "COM3",
        "baudRate": 9600,
        "slaveId": 1,
        "responseTimeout": 1000
      }
    },
    "moisture1": {
      "type": "moisture_analyzer", 
      "config": {
        "serialPort": "COM4",
        "baudRate": 9600,
        "slaveId": 2,
        "targetTemperature": 105
      }
    }
  },
  "server": {
    "host": "0.0.0.0",
    "port": 8080,
    "maxConnections": 100,
    "threadPoolSize": 4
  }
}
```

#### 4.2 增强错误处理和日志系统 (4小时)
```cpp
class ErrorHandler {
public:
    enum class ErrorLevel { INFO, WARNING, ERROR, CRITICAL };
    
    static void logError(ErrorLevel level, const std::string& component, 
                        const std::string& message, const std::string& details = "");
    static Json::Value createErrorResponse(const std::string& errorCode, 
                                         const std::string& message);
    static void handleModbusError(int errorCode, const std::string& operation);
};
```

### 第5阶段 (2月7日) - 集成测试和示例

#### 5.1 创建完整示例程序 (4小时)
**文件：** `Analysis_Robot/example/fullSystemExample.cpp`

```cpp
int main() {
    // 1. 加载配置
    ConfigManager config("config/DeviceConfig.json");
    
    // 2. 创建REST服务器
    RestInterfaceDriver restServer;
    restServer.initialize(config.getServerConfig());
    
    // 3. 注册设备
    restServer.registerBalanceController("balance1", config.getBalanceConfig("balance1"));
    restServer.registerMoistureController("moisture1", config.getMoistureConfig("moisture1"));
    
    // 4. 初始化所有设备
    if (!restServer.initializeAllDevices()) {
        std::cerr << "Failed to initialize devices" << std::endl;
        return -1;
    }
    
    // 5. 启动服务器
    restServer.start();
    
    // 6. 等待用户输入退出
    std::cout << "Press Enter to stop server..." << std::endl;
    std::cin.get();
    
    // 7. 清理资源
    restServer.stop();
    return 0;
}
```

#### 5.2 编写集成测试 (4小时)
**文件：** `Analysis_Robot/test/integrationTest/fullSystemTest.cpp`

```cpp
class FullSystemTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 启动REST服务器和模拟设备
        setupMockDevices();
        startRestServer();
    }
    
    void TearDown() override {
        stopRestServer();
        cleanupMockDevices();
    }
    
    void testBalanceWeighOperation();
    void testMoistureAnalysisProcess();
    void testErrorHandling();
    void testConcurrentOperations();
};
```

---

## 🔧 关键技术实现

### 1. MODBUS驱动器适配
```cpp
// 将底层MODBUS驱动器包装为REST API
class ModbusDeviceAdapter {
public:
    template<typename DriverType>
    static Json::Value executeOperation(
        std::shared_ptr<DriverType> driver,
        const std::string& operation,
        const Json::Value& parameters
    );
    
    template<typename DriverType>
    static DeviceStatus getDeviceStatus(std::shared_ptr<DriverType> driver);
};
```

### 2. 异步任务处理
```cpp
// 长时间运行的操作异步处理
class AsyncOperationHandler {
public:
    static std::future<TaskInfo> executeAsync(std::function<TaskInfo()> operation);
    static void cancelOperation(int taskId);
    static TaskInfo getOperationStatus(int taskId);
};
```

### 3. 设备状态同步
```cpp
// 设备状态实时同步
class DeviceStatusMonitor {
private:
    std::map<std::string, std::shared_ptr<BaseDeviceController>> m_devices;
    std::thread m_monitorThread;
    
public:
    void startMonitoring();
    void stopMonitoring();
    DeviceStatus getDeviceStatus(const std::string& deviceName);
    
private:
    void monitorLoop();
    void updateDeviceStatus(const std::string& deviceName);
};
```

---

## 📊 API接口示例

### 天平操作示例
```bash
# 称重操作
curl -X POST http://localhost:8080/api/balance/operation \
  -H "Content-Type: application/json" \
  -d '{
    "action": "WEIGH"
  }'

# 响应
{
  "taskId": 1001,
  "action": "WEIGH", 
  "status": "SUCCESS",
  "message": "称重完成",
  "data": {
    "weight": 125.67,
    "unit": "g",
    "stability": true
  },
  "updateTime": "2025-01-30 14:30:00"
}
```

### 水分测定操作示例
```bash
# 开始加热
curl -X POST http://localhost:8080/api/moistureBalance/operation \
  -H "Content-Type: application/json" \
  -d '{
    "action": "HEAT",
    "data": {
      "temperature": 105,
      "duration": 300
    }
  }'

# 响应
{
  "taskId": 1002,
  "action": "HEAT",
  "status": "SUBMITTED", 
  "message": "加热任务已提交",
  "updateTime": "2025-01-30 14:35:00"
}

# 查询任务状态
curl http://localhost:8080/api/moistureBalance/query?taskId=1002

# 响应
{
  "taskId": 1002,
  "action": "HEAT",
  "status": "RUNNING",
  "message": "正在加热中",
  "data": {
    "currentTemperature": 98,
    "targetTemperature": 105,
    "remainingTime": 245
  },
  "updateTime": "2025-01-30 14:36:00"
}
```

---

## 📈 开发里程碑

| 阶段 | 完成日期 | 关键交付物 | 完成度目标 |
|------|----------|------------|------------|
| 阶段1 | 1月31日 | 设备控制器实现 | 90% |
| 阶段2 | 2月2日 | 任务管理和异步处理 | 95% |
| 阶段3 | 2月4日 | REST接口集成 | 98% |
| 阶段4 | 2月6日 | 配置管理和错误处理 | 99% |
| 阶段5 | 2月7日 | 集成测试和示例 | 100% |

---

## ⚠️ 风险评估和解决方案

### 技术风险
1. **MODBUS通信稳定性** 
   - 解决方案：实现重连机制和通信监控
2. **多线程并发安全**
   - 解决方案：使用线程安全的数据结构和锁机制
3. **异步任务管理复杂性**
   - 解决方案：使用成熟的任务队列库或自实现简化版本

### 集成风险
1. **设备驱动器兼容性**
   - 解决方案：创建设备模拟器进行测试
2. **REST API性能**
   - 解决方案：实现连接池和请求缓存

---

**文档更新时间：** 2025年1月29日  
**预计完成时间：** 2025年2月7日
