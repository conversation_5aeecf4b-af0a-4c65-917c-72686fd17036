MODBUS 命令集														
主机发送	序号	读命令	设备地址	命令类型	寄存器地址(高字节)	寄存器地址(低字节)	寄存器个数/(高字节)	寄存器个数/(低字节)	CRC1	CRC0				
	1	立刻读净重	01	03	00	00	00	02						
	2	读过程状态	01	03	00	02	00	01			内校过程中用此命令			
	3	读称重稳定状态	01	03	00	03	00	01						
	4	读水分	01	03	00	05	00	02						
														
		写命令	设备地址	命令类型	寄存器地址(高字节)	寄存器地址(低字节)	写数据(高字节)	写数据(低字节)	CRC1	CRC0				
	2	立即去皮	01	06	00	04	00	02						
	3	内校	01	06	00	04	00	03						
	10	站地址配置	01	06	00	04	02	xx			xx站地址	站地址EEPROM保存地址		
	11	加热开始	01	06	00	04	00	0A						
	12	加热停止	01	06	00	04	00	0B						
	13	返回称重	01	06	00	04	00	0C						
	14	开仓	01	06	00	04	00	0D						
	15	关仓	01	06	00	04	00	0E						
	16	设置目标温度	01	06	00	07	xx	xx						
														
														
从机回传	序号	回传类型	设备地址	命令类型	寄存器地址(高字节)	寄存器地址(低字节)	写数据(高字节)	写数据(低字节)	CRC1	CRC0				
	1	写命令回传	正常执行，原命令返回											
	2	净重回传	设备地址	命令类型	4	x（高字节）	x	x	x（低字节）	CRC1	CRC0			
	3	状态回传	设备地址	命令类型	2	x（高字节）	x（状态号）	CRC1	CRC0					
						序号/状态号	模块状态种类	过程						
						2	内校校准中	内校						
						3	内校故障							
						4	内校校准完毕							
						5	本次内校无效							
						6	请清盘	单点外校/多点校/内置砝码校正						
						7	取下砝码中(清盘中)							
														
	4	异常回传	设备地址	命令类型	异常码	CRC1	CRC0	命令执行异常						
			01	命令类型+0x80	02									
					0x01	非法功能（Illegal Function）	从机不支持该功能码							
					0x02	非法地址（Illegal Data Address）	访问了不存在的寄存器							
					0x03	非法数据值（Illegal Data Value）	发送的数据超出允许范围							
					0x04	从机设备故障（Slave Device Failure）	设备内部错误							
					0x05	确认（Acknowledge）	请求被接受，但需要较长时间执行							
					0x06	从机忙（Slave Device Busy）	设备当前忙，无法处理新请求							
					0x08	存储奇偶错误（Memory Parity Error）	内存校验错误							
		注：1 读重量时，如果收到数据0x7fffffff 代表超载，如果收到数据0x80000000代表欠载												
														
														
		       2 波特率：9600，8位数据位，1位停止位，无校验位												
														
														
		常州市幸运电子有限公司												
