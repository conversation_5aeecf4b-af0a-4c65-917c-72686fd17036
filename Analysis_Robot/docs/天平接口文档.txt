以FA2204N为例
MODBUS指令
波特率9600,无校验，1位停止位，8位数据位，站号1
1.读取重量
数据方向
模块
地址
功能码
寄存器起始地址
寄存器数量
主机→从机 0x01
高8位
高8位
低8位
CRC16校验码
0x03
0x00
低8位
0x00
0x00
0x02
高8位低8位
OxCA
0x06
数据方向
模块
功能返回内容
地址码
长度
最高
字节
-
-
最低
字节
CRC16校验码
低8位高8位
主机<从机 0x01 0x03
0x04
×1
×2
X3
×4
**
**
读取内码值为拼接X1 X2 X3 X4
例:
主机发送：0103 0000 00 02 c4 0b
从机返回：0103 0400 20 89 66 1C 43
解析出内码值：0x00208966(十六进制)→2,132,326(十进制）代表213.2326g
2.去皮
数据方向
模块
地址
功能码
寄存器起始地址
低8位
寄存器数量
高8位
低8位
CRC16校验码
高8位低8位
主机→从机0x01
0x06
高8位
0x00
0x06
0x00
0x01
0xA8 OxOO
数据方向
模块
地址
功能码
寄存器起始地址
寄存器数量
主机<从机0x01
低8位
高8位
CRC16校验码
低8位高8位低8位
0x06
高8位
0x00
0x06
0x00
0x01
0xA8 0x0b
从机返回到主机内容同主机发送到从机