# Analysis_Robot REST接口连接真实驱动器 - 实现示例代码

## 🔧 核心实现示例

### 1. 基础设备控制器 (BaseDeviceController)

```cpp
// Analysis_Robot/drivers/common/include/BaseDeviceController.h
#ifndef BASE_DEVICE_CONTROLLER_H
#define BASE_DEVICE_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <map>
#include <mutex>
#include <atomic>

namespace AnalysisRobot {
namespace Common {

class BaseDeviceController : public RestInterface::IDeviceController {
protected:
    std::string m_deviceName;
    std::string m_deviceType;
    std::map<int, RestInterface::TaskInfo> m_tasks;
    std::mutex m_tasksMutex;
    std::atomic<int> m_nextTaskId;
    std::atomic<bool> m_initialized;
    std::string m_lastError;
    
public:
    BaseDeviceController(const std::string& name, const std::string& type)
        : m_deviceName(name)
        , m_deviceType(type)
        , m_nextTaskId(1000)
        , m_initialized(false) {}
    
    virtual ~BaseDeviceController() = default;
    
    // 实现IDeviceController接口
    RestInterface::DeviceStatus getStatus() override {
        RestInterface::DeviceStatus status;
        status.name = m_deviceName;
        status.description = m_deviceType + " 设备控制器";
        status.updateTime = getCurrentTimeString();
        
        // 调用子类实现获取具体状态
        auto deviceStatus = getDeviceStatus();
        status.status = deviceStatus.status;
        status.message = deviceStatus.message;
        status.data = deviceStatus.data;
        
        return status;
    }
    
    RestInterface::TaskInfo executeOperation(const Json::Value& request) override {
        if (!request.isMember("action")) {
            return createErrorTask("缺少action参数");
        }
        
        std::string action = request["action"].asString();
        Json::Value data = request.get("data", Json::Value());
        
        // 调用子类实现执行具体操作
        return executeDeviceOperation(action, data);
    }
    
    RestInterface::TaskInfo queryTask(int taskId) override {
        std::lock_guard<std::mutex> lock(m_tasksMutex);
        auto it = m_tasks.find(taskId);
        if (it != m_tasks.end()) {
            return it->second;
        }
        
        RestInterface::TaskInfo notFound;
        notFound.taskId = taskId;
        notFound.status = RestInterface::TaskStatus::FAILED;
        notFound.message = "任务未找到";
        return notFound;
    }
    
    // 子类需要实现的纯虚函数
    virtual RestInterface::DeviceStatus getDeviceStatus() = 0;
    virtual RestInterface::TaskInfo executeDeviceOperation(const std::string& action, const Json::Value& data) = 0;
    virtual bool initialize() = 0;
    virtual void shutdown() = 0;
    
protected:
    int generateTaskId() {
        return m_nextTaskId++;
    }
    
    void updateTaskStatus(int taskId, RestInterface::TaskStatus status, const std::string& message, const Json::Value& data = Json::Value()) {
        std::lock_guard<std::mutex> lock(m_tasksMutex);
        auto it = m_tasks.find(taskId);
        if (it != m_tasks.end()) {
            it->second.status = status;
            it->second.message = message;
            it->second.data = data;
            it->second.updateTime = getCurrentTimeString();
        }
    }
    
    RestInterface::TaskInfo createSuccessTask(const std::string& action, const Json::Value& data = Json::Value()) {
        RestInterface::TaskInfo task;
        task.taskId = generateTaskId();
        task.action = action;
        task.status = RestInterface::TaskStatus::SUCCESS;
        task.message = action + " 操作成功";
        task.data = data;
        task.updateTime = getCurrentTimeString();
        
        // 存储任务
        std::lock_guard<std::mutex> lock(m_tasksMutex);
        m_tasks[task.taskId] = task;
        
        return task;
    }
    
    RestInterface::TaskInfo createErrorTask(const std::string& error) {
        RestInterface::TaskInfo task;
        task.taskId = generateTaskId();
        task.status = RestInterface::TaskStatus::FAILED;
        task.message = error;
        task.updateTime = getCurrentTimeString();
        return task;
    }
    
    std::string getCurrentTimeString() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }
};

} // namespace Common
} // namespace AnalysisRobot

#endif // BASE_DEVICE_CONTROLLER_H
```

### 2. 天平设备控制器 (BalanceController)

```cpp
// Analysis_Robot/drivers/balanceDriver/include/BalanceController.h
#ifndef BALANCE_CONTROLLER_H
#define BALANCE_CONTROLLER_H

#include "BaseDeviceController.h"
#include "BalanceDriver.h"
#include <thread>
#include <atomic>

namespace AnalysisRobot {
namespace Balance {

class BalanceController : public Common::BaseDeviceController {
private:
    std::shared_ptr<BalanceDriver> m_driver;
    BalanceConfig m_config;
    std::thread m_monitorThread;
    std::atomic<bool> m_monitoring;
    std::atomic<bool> m_connected;
    
public:
    BalanceController(const std::string& name, const BalanceConfig& config)
        : BaseDeviceController(name, "天平设备")
        , m_config(config)
        , m_monitoring(false)
        , m_connected(false) {
        m_driver = std::make_shared<BalanceDriver>();
    }
    
    ~BalanceController() {
        shutdown();
    }
    
    bool initialize() override {
        if (m_initialized) {
            return true;
        }
        
        // 初始化驱动器
        if (!m_driver->initialize(m_config)) {
            m_lastError = "驱动器初始化失败: " + m_driver->getLastError();
            return false;
        }
        
        // 连接设备
        if (!m_driver->connect()) {
            m_lastError = "设备连接失败: " + m_driver->getLastError();
            return false;
        }
        
        m_connected = true;
        m_initialized = true;
        
        // 启动监控线程
        startMonitoring();
        
        return true;
    }
    
    void shutdown() override {
        stopMonitoring();
        if (m_driver && m_connected) {
            m_driver->disconnect();
            m_connected = false;
        }
        m_initialized = false;
    }
    
    RestInterface::DeviceStatus getDeviceStatus() override {
        RestInterface::DeviceStatus status;
        
        if (!m_initialized) {
            status.status = "DISCONNECTED";
            status.message = "设备未初始化";
            return status;
        }
        
        if (!m_connected) {
            status.status = "DISCONNECTED";
            status.message = "设备未连接";
            return status;
        }
        
        // 获取驱动器状态
        BalanceStatus driverStatus = m_driver->getStatus();
        switch (driverStatus) {
            case BalanceStatus::CONNECTED:
                status.status = "IDLE";
                status.message = "设备空闲可用";
                break;
            case BalanceStatus::READING:
                status.status = "BUSY";
                status.message = "正在读取重量";
                break;
            case BalanceStatus::FAULT:
                status.status = "FAILED";
                status.message = "设备故障: " + m_driver->getLastError();
                break;
            default:
                status.status = "UNKNOWN";
                status.message = "未知状态";
                break;
        }
        
        return status;
    }
    
    RestInterface::TaskInfo executeDeviceOperation(const std::string& action, const Json::Value& data) override {
        if (!m_initialized || !m_connected) {
            return createErrorTask("设备未连接");
        }
        
        if (action == "WEIGH") {
            return handleWeighOperation();
        } else if (action == "TARE") {
            return handleTareOperation();
        } else if (action == "RESET") {
            return handleResetOperation();
        } else {
            return createErrorTask("不支持的操作: " + action);
        }
    }
    
private:
    void startMonitoring() {
        if (!m_monitoring) {
            m_monitoring = true;
            m_monitorThread = std::thread(&BalanceController::monitorLoop, this);
        }
    }
    
    void stopMonitoring() {
        if (m_monitoring) {
            m_monitoring = false;
            if (m_monitorThread.joinable()) {
                m_monitorThread.join();
            }
        }
    }
    
    void monitorLoop() {
        while (m_monitoring) {
            // 定期检查设备状态
            if (m_driver && m_connected) {
                // 可以在这里实现设备状态监控逻辑
                // 例如：检查连接状态、读取设备信息等
            }
            
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }
    
    RestInterface::TaskInfo handleWeighOperation() {
        WeightReading reading = m_driver->readWeight();
        
        if (reading.success) {
            Json::Value data;
            data["weight"] = reading.weight;
            data["rawValue"] = static_cast<int>(reading.rawValue);
            data["unit"] = "g";
            data["timestamp"] = getCurrentTimeString();
            
            return createSuccessTask("WEIGH", data);
        } else {
            return createErrorTask("称重失败: " + reading.errorMsg);
        }
    }
    
    RestInterface::TaskInfo handleTareOperation() {
        if (m_driver->tare()) {
            Json::Value data;
            data["message"] = "去皮操作完成";
            return createSuccessTask("TARE", data);
        } else {
            return createErrorTask("去皮失败: " + m_driver->getLastError());
        }
    }
    
    RestInterface::TaskInfo handleResetOperation() {
        // 重新连接设备
        m_driver->disconnect();
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        if (m_driver->connect()) {
            Json::Value data;
            data["message"] = "设备重置完成";
            return createSuccessTask("RESET", data);
        } else {
            m_connected = false;
            return createErrorTask("设备重置失败: " + m_driver->getLastError());
        }
    }
};

} // namespace Balance
} // namespace AnalysisRobot

#endif // BALANCE_CONTROLLER_H
```

### 3. 设备工厂 (DeviceFactory)

```cpp
// Analysis_Robot/drivers/common/include/DeviceFactory.h
#ifndef DEVICE_FACTORY_H
#define DEVICE_FACTORY_H

#include "BaseDeviceController.h"
#include "BalanceController.h"
#include "MoistureController.h"
#include <json/json.h>

namespace AnalysisRobot {
namespace Common {

class DeviceFactory {
public:
    enum class DeviceType {
        BALANCE,
        MOISTURE_ANALYZER,
        UNKNOWN
    };
    
    static std::shared_ptr<BaseDeviceController> createDevice(
        const std::string& deviceName,
        DeviceType type, 
        const Json::Value& config) {
        
        switch (type) {
            case DeviceType::BALANCE:
                return createBalanceController(deviceName, config);
            case DeviceType::MOISTURE_ANALYZER:
                return createMoistureController(deviceName, config);
            default:
                return nullptr;
        }
    }
    
    static DeviceType parseDeviceType(const std::string& typeStr) {
        if (typeStr == "balance") {
            return DeviceType::BALANCE;
        } else if (typeStr == "moisture_analyzer") {
            return DeviceType::MOISTURE_ANALYZER;
        } else {
            return DeviceType::UNKNOWN;
        }
    }
    
private:
    static std::shared_ptr<BaseDeviceController> createBalanceController(
        const std::string& name, 
        const Json::Value& config) {
        
        Balance::BalanceConfig balanceConfig;
        balanceConfig.serialPort = config.get("serialPort", "COM1").asString();
        balanceConfig.baudRate = config.get("baudRate", 9600).asInt();
        balanceConfig.slaveId = config.get("slaveId", 1).asInt();
        balanceConfig.responseTimeout = config.get("responseTimeout", 1000).asInt();
        
        return std::make_shared<Balance::BalanceController>(name, balanceConfig);
    }
    
    static std::shared_ptr<BaseDeviceController> createMoistureController(
        const std::string& name, 
        const Json::Value& config) {
        
        Moisture::MoistureConfig moistureConfig;
        moistureConfig.serialPort = config.get("serialPort", "COM1").asString();
        moistureConfig.baudRate = config.get("baudRate", 9600).asInt();
        moistureConfig.slaveId = config.get("slaveId", 1).asInt();
        moistureConfig.targetTemperature = config.get("targetTemperature", 105).asInt();
        
        return std::make_shared<Moisture::MoistureController>(name, moistureConfig);
    }
};

} // namespace Common
} // namespace AnalysisRobot

#endif // DEVICE_FACTORY_H
```

### 4. 增强的REST接口驱动器

```cpp
// 在RestInterfaceDriver类中添加设备管理功能
class RestInterfaceDriver {
private:
    std::map<std::string, std::shared_ptr<Common::BaseDeviceController>> m_controllers;
    
public:
    // 从配置文件加载设备
    bool loadDevicesFromConfig(const std::string& configFile) {
        Json::Value root;
        std::ifstream file(configFile);
        if (!file.is_open()) {
            setError("无法打开配置文件: " + configFile);
            return false;
        }
        
        file >> root;
        
        if (!root.isMember("devices")) {
            setError("配置文件中缺少devices节点");
            return false;
        }
        
        Json::Value devices = root["devices"];
        for (const auto& deviceName : devices.getMemberNames()) {
            Json::Value deviceConfig = devices[deviceName];
            
            std::string typeStr = deviceConfig.get("type", "").asString();
            Common::DeviceFactory::DeviceType type = Common::DeviceFactory::parseDeviceType(typeStr);
            
            if (type == Common::DeviceFactory::DeviceType::UNKNOWN) {
                log("WARNING", "未知设备类型: " + typeStr);
                continue;
            }
            
            auto controller = Common::DeviceFactory::createDevice(deviceName, type, deviceConfig["config"]);
            if (controller) {
                registerDeviceController(deviceName, controller);
                log("INFO", "设备注册成功: " + deviceName);
            } else {
                log("ERROR", "设备创建失败: " + deviceName);
            }
        }
        
        return true;
    }
    
    // 初始化所有设备
    bool initializeAllDevices() {
        bool allSuccess = true;
        
        for (auto& pair : m_controllers) {
            auto controller = std::dynamic_pointer_cast<Common::BaseDeviceController>(pair.second);
            if (controller) {
                if (!controller->initialize()) {
                    log("ERROR", "设备初始化失败: " + pair.first);
                    allSuccess = false;
                } else {
                    log("INFO", "设备初始化成功: " + pair.first);
                }
            }
        }
        
        return allSuccess;
    }
    
    // 关闭所有设备
    void shutdownAllDevices() {
        for (auto& pair : m_controllers) {
            auto controller = std::dynamic_pointer_cast<Common::BaseDeviceController>(pair.second);
            if (controller) {
                controller->shutdown();
                log("INFO", "设备已关闭: " + pair.first);
            }
        }
    }
};
```

### 5. 完整使用示例

```cpp
// Analysis_Robot/example/fullSystemExample.cpp
#include "RestInterfaceDriver.h"
#include "DeviceFactory.h"
#include <iostream>
#include <signal.h>

using namespace AnalysisRobot;

RestInterface::RestInterfaceDriver* g_server = nullptr;

void signalHandler(int signal) {
    if (g_server) {
        std::cout << "\n正在关闭服务器..." << std::endl;
        g_server->shutdownAllDevices();
        g_server->stop();
    }
    exit(0);
}

int main() {
    // 注册信号处理器
    signal(SIGINT, signalHandler);
    
    // 创建REST服务器
    RestInterface::RestInterfaceDriver server;
    g_server = &server;
    
    // 配置服务器
    RestInterface::RestConfig config;
    config.host = "0.0.0.0";
    config.port = 8080;
    
    if (!server.initialize(config)) {
        std::cerr << "服务器初始化失败: " << server.getLastError() << std::endl;
        return -1;
    }
    
    // 从配置文件加载设备
    if (!server.loadDevicesFromConfig("config/devices.json")) {
        std::cerr << "设备配置加载失败: " << server.getLastError() << std::endl;
        return -1;
    }
    
    // 初始化所有设备
    if (!server.initializeAllDevices()) {
        std::cerr << "设备初始化失败" << std::endl;
        return -1;
    }
    
    // 启动服务器
    if (!server.start()) {
        std::cerr << "服务器启动失败: " << server.getLastError() << std::endl;
        return -1;
    }
    
    std::cout << "Analysis Robot REST服务器已启动" << std::endl;
    std::cout << "服务器地址: http://" << config.host << ":" << config.port << std::endl;
    std::cout << "按 Ctrl+C 停止服务器" << std::endl;
    
    // 保持程序运行
    while (server.isRunning()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    return 0;
}
```

---

## 📋 配置文件示例

```json
{
  "devices": {
    "balance1": {
      "type": "balance",
      "config": {
        "serialPort": "COM3",
        "baudRate": 9600,
        "slaveId": 1,
        "responseTimeout": 1000
      }
    },
    "moisture1": {
      "type": "moisture_analyzer",
      "config": {
        "serialPort": "COM4", 
        "baudRate": 9600,
        "slaveId": 2,
        "targetTemperature": 105,
        "responseTimeout": 2000
      }
    }
  }
}
```

这个实现方案提供了完整的REST接口到硬件驱动器的连接架构，支持设备管理、异步操作、错误处理和配置管理。
