加热磁力搅拌(功能)寄存器
格式遵从Modbus协议
数据校验遵从CRC校验(C16X25)


ID
范围（1~254）广播地址（255）

CMD命令字
读：0x03
写：0x06
写多：0x10
读格式：ID + CMD + 寄存器地址(U16) + 寄存器个数(U16) + CRC(U16)
写格式：ID + CMD + 寄存器地址(U16) + 数据(U16) + CRC(U16)
写多格式：ID + CMD + 寄存器地址(U16) + 数据(U16)+...+ CRC(U16)


控制模块（0x00~0x7F）
设置模块（0x80~0xFF）
其他模块（0x100~0x200）


控制模块寄存器：
读温度|转速(0x00) 返回格式：id+cmd+len+温度+转速+crc

温度控制（0x10）数据：00关闭|01启动
电机控制（0x20）数据：00关闭|01启动
电机方向控制（0x21）数据：00正转|01反转
程控控制（0x40）数据：H8:程控编号(0~9)|L8:00关闭|01启动(目前未增加)



设置模块寄存器：
温度设置（0x80） 数据：（室温~350）℃
电机设置（0x90）数据：（50~2000）RPM
时间设置（0xA0）数据：（0~65535）min
定时结束，下位机返回指令：ID+06+00 0A+00 00+crc
注：程控编号与方向为U8
程控设置（0xB0） 数据：程控编号+时间1+温度1+转速1+方向1+...+时间4+温度4+转速4+方向4+CRC



其他模块寄存器：
下位机接收数据错误反馈(01:无命令字 02:写入寄存器不存在 09：crc校验错误)
