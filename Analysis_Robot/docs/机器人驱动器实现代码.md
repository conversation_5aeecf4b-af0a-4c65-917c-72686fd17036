# 机器人驱动器实现代码 - 适配REST接口

## 🔧 核心实现文件

### 1. 机器人驱动器头文件 (RobotDriver.h)

```cpp
// Analysis_Robot/drivers/robotDriver/include/RobotDriver.h
#ifndef ROBOT_DRIVER_H
#define ROBOT_DRIVER_H

#include <string>
#include <memory>
#include <functional>
#include <map>
#include <modbus.h>

namespace AnalysisRobot {
namespace Robot {

/**
 * @brief 机器人状态枚举
 */
enum class RobotStatus {
    DISCONNECTED = 0,   // 未连接
    IDLE = 1,          // 空闲
    BUSY = 2,          // 忙碌
    ERROR = 3,         // 错误
    EMERGENCY = 4      // 急停
};

/**
 * @brief 运动模式枚举
 */
enum class MotionMode {
    MANUAL = 0,        // 手动模式
    AUTO = 1,          // 自动模式
    TEACH = 2,         // 示教模式
    MAINTENANCE = 3    // 维护模式
};

/**
 * @brief 位置信息结构
 */
struct Position {
    double x, y, z;        // 笛卡尔坐标 (mm)
    double rx, ry, rz;     // 旋转角度 (度)
    
    Position() : x(0), y(0), z(0), rx(0), ry(0), rz(0) {}
    Position(double x_, double y_, double z_, double rx_, double ry_, double rz_)
        : x(x_), y(y_), z(z_), rx(rx_), ry(ry_), rz(rz_) {}
};

/**
 * @brief 关节角度结构
 */
struct JointAngles {
    double joint1, joint2, joint3, joint4, joint5, joint6;  // 关节角度 (度)
    
    JointAngles() : joint1(0), joint2(0), joint3(0), joint4(0), joint5(0), joint6(0) {}
};

/**
 * @brief 运动参数结构
 */
struct MotionParams {
    int speed;         // 速度百分比 (1-100)
    int acceleration;  // 加速度百分比 (1-100)
    
    MotionParams() : speed(50), acceleration(50) {}
    MotionParams(int s, int a) : speed(s), acceleration(a) {}
};

/**
 * @brief 机器人配置
 */
struct RobotConfig {
    std::string serialPort;     // 串口号
    int baudRate;               // 波特率，默认115200
    char parity;                // 校验位，默认'N'
    int dataBits;               // 数据位，默认8
    int stopBits;               // 停止位，默认1
    int slaveId;                // 从机地址，默认3
    int responseTimeout;        // 响应超时时间（毫秒），默认2000
    int maxJointSpeed;          // 最大关节速度 (度/秒)
    int maxLinearSpeed;         // 最大直线速度 (mm/秒)
    
    RobotConfig() 
        : serialPort("COM1")
        , baudRate(115200)
        , parity('N')
        , dataBits(8)
        , stopBits(1)
        , slaveId(3)
        , responseTimeout(2000)
        , maxJointSpeed(180)
        , maxLinearSpeed(1000) {}
};

/**
 * @brief 状态回调函数类型
 */
using StatusCallback = std::function<void(RobotStatus status, const std::string& message)>;

/**
 * @brief 机器人驱动类
 * 
 * 支持MODBUS RTU协议的6轴机械臂，主要功能：
 * - 位置控制和反馈
 * - 关节运动和直线运动
 * - IO控制
 * - 安全监控
 * - 任务管理
 */
class RobotDriver {
public:
    /**
     * @brief 构造函数
     */
    RobotDriver();
    
    /**
     * @brief 析构函数
     */
    ~RobotDriver();
    
    /**
     * @brief 初始化机器人连接
     * @param config 配置参数
     * @return 是否成功
     */
    bool initialize(const RobotConfig& config);
    
    /**
     * @brief 连接机器人
     * @return 是否成功
     */
    bool connect();
    
    /**
     * @brief 断开连接
     */
    void disconnect();
    
    /**
     * @brief 检查连接状态
     * @return 是否已连接
     */
    bool isConnected() const;
    
    // ========== 状态查询 ==========
    
    /**
     * @brief 获取机器人状态
     * @return 机器人状态
     */
    RobotStatus getStatus() const;
    
    /**
     * @brief 获取当前TCP位置
     * @return TCP位置
     */
    Position getCurrentPosition();
    
    /**
     * @brief 获取当前关节角度
     * @return 关节角度
     */
    JointAngles getCurrentJointAngles();
    
    /**
     * @brief 获取错误代码
     * @return 错误代码
     */
    int getErrorCode();
    
    // ========== 运动控制 ==========
    
    /**
     * @brief 回原点
     * @return 是否成功
     */
    bool home();
    
    /**
     * @brief 关节运动
     * @param angles 目标关节角度
     * @param params 运动参数
     * @return 是否成功
     */
    bool moveJoint(const JointAngles& angles, const MotionParams& params = MotionParams());
    
    /**
     * @brief 直线运动
     * @param position 目标位置
     * @param params 运动参数
     * @return 是否成功
     */
    bool moveLinear(const Position& position, const MotionParams& params = MotionParams());
    
    /**
     * @brief 停止运动
     * @return 是否成功
     */
    bool stop();
    
    /**
     * @brief 暂停运动
     * @return 是否成功
     */
    bool pause();
    
    /**
     * @brief 恢复运动
     * @return 是否成功
     */
    bool resume();
    
    // ========== IO控制 ==========
    
    /**
     * @brief 设置数字输出
     * @param port 端口号 (0-15)
     * @param value 输出值
     * @return 是否成功
     */
    bool setDigitalOutput(int port, bool value);
    
    /**
     * @brief 读取数字输入
     * @param port 端口号 (0-15)
     * @return 输入值
     */
    bool getDigitalInput(int port);
    
    /**
     * @brief 控制夹爪
     * @param command 夹爪指令 (0=释放, 1=夹取, 2=停止)
     * @param force 夹爪力度 (1-100%)
     * @return 是否成功
     */
    bool controlGripper(int command, int force = 50);
    
    /**
     * @brief 获取夹爪状态
     * @return 夹爪状态 (0=释放, 1=夹取, 2=运动中, 3=错误)
     */
    int getGripperStatus();
    
    // ========== 任务管理 ==========
    
    /**
     * @brief 执行取放任务
     * @param sourcePos 源位置编码
     * @param destPos 目标位置编码
     * @return 任务ID
     */
    int executePickPlace(const std::string& sourcePos, const std::string& destPos);
    
    /**
     * @brief 获取任务状态
     * @param taskId 任务ID
     * @return 任务状态 (0=空闲, 1=取料中, 2=移动中, 3=放料中, 4=完成, 5=失败)
     */
    int getTaskStatus(int taskId);
    
    /**
     * @brief 获取任务进度
     * @return 进度百分比 (0-100)
     */
    double getTaskProgress();
    
    // ========== 配置和回调 ==========
    
    /**
     * @brief 设置状态回调函数
     * @param callback 回调函数
     */
    void setStatusCallback(StatusCallback callback);
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const;

private:
    RobotConfig m_config;                   // 配置参数
    modbus_t* m_modbusCtx;                 // MODBUS上下文
    RobotStatus m_status;                   // 当前状态
    StatusCallback m_statusCallback;        // 状态回调
    std::string m_lastError;               // 最后错误信息
    
    // 位置编码映射
    static std::map<std::string, uint16_t> s_positionCodes;
    
    /**
     * @brief 读取寄存器
     * @param address 寄存器地址
     * @return 寄存器值
     */
    uint16_t readRegister(int address);
    
    /**
     * @brief 写入寄存器
     * @param address 寄存器地址
     * @param value 写入值
     * @return 是否成功
     */
    bool writeRegister(int address, uint16_t value);
    
    /**
     * @brief 读取32位值
     * @param address 起始地址
     * @return 32位值
     */
    int32_t readInt32(int address);
    
    /**
     * @brief 写入32位值
     * @param address 起始地址
     * @param value 32位值
     * @return 是否成功
     */
    bool writeInt32(int address, int32_t value);
    
    /**
     * @brief 位置编码转换
     * @param position 位置字符串
     * @return 位置编码
     */
    uint16_t encodePosition(const std::string& position);
    
    /**
     * @brief 检查MODBUS结果
     * @param result MODBUS操作结果
     * @param operation 操作描述
     * @return 是否成功
     */
    bool checkModbusResult(int result, const std::string& operation);
    
    /**
     * @brief 设置状态
     * @param status 新状态
     * @param message 状态消息
     */
    void setStatus(RobotStatus status, const std::string& message);
    
    /**
     * @brief 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);
};

} // namespace Robot
} // namespace AnalysisRobot

#endif // ROBOT_DRIVER_H
```

### 2. 机器人控制器实现 (RobotController.h)

```cpp
// Analysis_Robot/drivers/robotDriver/include/RobotController.h
#ifndef ROBOT_CONTROLLER_H
#define ROBOT_CONTROLLER_H

#include "BaseDeviceController.h"
#include "RobotDriver.h"
#include <thread>
#include <atomic>
#include <map>

namespace AnalysisRobot {
namespace Robot {

class RobotController : public Common::BaseDeviceController {
private:
    std::shared_ptr<RobotDriver> m_driver;
    RobotConfig m_config;
    std::thread m_monitorThread;
    std::atomic<bool> m_monitoring;
    std::atomic<bool> m_connected;
    
    // 任务监控
    std::map<int, std::thread> m_taskThreads;
    std::mutex m_taskThreadsMutex;
    
public:
    RobotController(const std::string& name, const RobotConfig& config)
        : BaseDeviceController(name, "6轴机械臂")
        , m_config(config)
        , m_monitoring(false)
        , m_connected(false) {
        m_driver = std::make_shared<RobotDriver>();
    }
    
    ~RobotController() {
        shutdown();
    }
    
    bool initialize() override {
        if (m_initialized) {
            return true;
        }
        
        // 初始化驱动器
        if (!m_driver->initialize(m_config)) {
            m_lastError = "机器人驱动器初始化失败: " + m_driver->getLastError();
            return false;
        }
        
        // 连接设备
        if (!m_driver->connect()) {
            m_lastError = "机器人连接失败: " + m_driver->getLastError();
            return false;
        }
        
        m_connected = true;
        m_initialized = true;
        
        // 启动监控线程
        startMonitoring();
        
        return true;
    }
    
    void shutdown() override {
        stopMonitoring();
        stopAllTaskThreads();
        
        if (m_driver && m_connected) {
            m_driver->disconnect();
            m_connected = false;
        }
        m_initialized = false;
    }
    
    RestInterface::DeviceStatus getDeviceStatus() override {
        RestInterface::DeviceStatus status;
        
        if (!m_initialized || !m_connected) {
            status.status = "DISCONNECTED";
            status.message = "机器人未连接";
            return status;
        }
        
        // 获取驱动器状态
        RobotStatus robotStatus = m_driver->getStatus();
        switch (robotStatus) {
            case RobotStatus::IDLE:
                status.status = "IDLE";
                status.message = "机器人空闲可用";
                break;
            case RobotStatus::BUSY:
                status.status = "BUSY";
                status.message = "机器人忙碌中";
                break;
            case RobotStatus::ERROR:
                status.status = "FAILED";
                status.message = "机器人故障: " + m_driver->getLastError();
                break;
            case RobotStatus::EMERGENCY:
                status.status = "FAILED";
                status.message = "机器人急停状态";
                break;
            default:
                status.status = "UNKNOWN";
                status.message = "未知状态";
                break;
        }
        
        // 获取位置信息
        Position pos = m_driver->getCurrentPosition();
        Json::Value data;
        data["x"] = pos.x;
        data["y"] = pos.y;
        data["z"] = pos.z;
        data["u"] = pos.rx;
        data["v"] = pos.ry;
        data["w"] = pos.rz;
        
        status.data = data;
        return status;
    }
    
    RestInterface::TaskInfo executeDeviceOperation(const std::string& action, const Json::Value& data) override {
        if (!m_initialized || !m_connected) {
            return createErrorTask("机器人未连接");
        }
        
        if (action == "PICK_PLACE") {
            return handlePickPlaceOperation(data);
        } else if (action == "MOVE_TO") {
            return handleMoveToOperation(data);
        } else if (action == "HOME") {
            return handleHomeOperation();
        } else if (action == "STOP") {
            return handleStopOperation();
        } else if (action == "SET_IO") {
            return handleSetIOOperation(data);
        } else if (action == "GRIPPER") {
            return handleGripperOperation(data);
        } else {
            return createErrorTask("不支持的操作: " + action);
        }
    }
    
private:
    void startMonitoring() {
        if (!m_monitoring) {
            m_monitoring = true;
            m_monitorThread = std::thread(&RobotController::monitorLoop, this);
        }
    }
    
    void stopMonitoring() {
        if (m_monitoring) {
            m_monitoring = false;
            if (m_monitorThread.joinable()) {
                m_monitorThread.join();
            }
        }
    }
    
    void stopAllTaskThreads() {
        std::lock_guard<std::mutex> lock(m_taskThreadsMutex);
        for (auto& pair : m_taskThreads) {
            if (pair.second.joinable()) {
                pair.second.join();
            }
        }
        m_taskThreads.clear();
    }
    
    void monitorLoop() {
        while (m_monitoring) {
            // 定期检查机器人状态
            if (m_driver && m_connected) {
                // 可以在这里实现状态监控逻辑
                RobotStatus status = m_driver->getStatus();
                if (status == RobotStatus::ERROR || status == RobotStatus::EMERGENCY) {
                    // 处理错误状态
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
    
    RestInterface::TaskInfo handlePickPlaceOperation(const Json::Value& data) {
        if (!data.isMember("source") || !data.isMember("destination")) {
            return createErrorTask("缺少source或destination参数");
        }
        
        std::string source = data["source"].asString();
        std::string destination = data["destination"].asString();
        
        // 执行取放任务
        int driverTaskId = m_driver->executePickPlace(source, destination);
        if (driverTaskId < 0) {
            return createErrorTask("取放任务启动失败: " + m_driver->getLastError());
        }
        
        // 创建REST任务
        int taskId = generateTaskId();
        
        // 启动任务监控线程
        {
            std::lock_guard<std::mutex> lock(m_taskThreadsMutex);
            m_taskThreads[taskId] = std::thread([this, taskId, driverTaskId]() {
                monitorPickPlaceTask(taskId, driverTaskId);
            });
        }
        
        RestInterface::TaskInfo task;
        task.taskId = taskId;
        task.action = "PICK_PLACE";
        task.status = RestInterface::TaskStatus::SUBMITTED;
        task.message = "取放任务已提交";
        
        // 存储任务
        std::lock_guard<std::mutex> lock(m_tasksMutex);
        m_tasks[taskId] = task;
        
        return task;
    }
    
    void monitorPickPlaceTask(int taskId, int driverTaskId) {
        while (true) {
            int status = m_driver->getTaskStatus(driverTaskId);
            double progress = m_driver->getTaskProgress();
            
            Json::Value data;
            data["progress"] = progress;
            
            switch (status) {
                case 0: // 空闲
                    updateTaskStatus(taskId, RestInterface::TaskStatus::SUBMITTED, "等待执行", data);
                    break;
                case 1: // 取料中
                    updateTaskStatus(taskId, RestInterface::TaskStatus::IN_PROGRESS, "正在取料", data);
                    break;
                case 2: // 移动中
                    updateTaskStatus(taskId, RestInterface::TaskStatus::IN_PROGRESS, "正在移动", data);
                    break;
                case 3: // 放料中
                    updateTaskStatus(taskId, RestInterface::TaskStatus::IN_PROGRESS, "正在放料", data);
                    break;
                case 4: // 完成
                    updateTaskStatus(taskId, RestInterface::TaskStatus::SUCCESS, "取放任务完成", data);
                    cleanupTaskThread(taskId);
                    return;
                case 5: // 失败
                    int errorCode = m_driver->getErrorCode();
                    updateTaskStatus(taskId, RestInterface::TaskStatus::FAILED, 
                                   "取放任务失败，错误码: " + std::to_string(errorCode), data);
                    cleanupTaskThread(taskId);
                    return;
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }
    
    void cleanupTaskThread(int taskId) {
        std::lock_guard<std::mutex> lock(m_taskThreadsMutex);
        auto it = m_taskThreads.find(taskId);
        if (it != m_taskThreads.end()) {
            if (it->second.joinable()) {
                it->second.detach(); // 分离线程，让它自然结束
            }
            m_taskThreads.erase(it);
        }
    }
    
    RestInterface::TaskInfo handleHomeOperation() {
        if (m_driver->home()) {
            Json::Value data;
            data["message"] = "回原点操作完成";
            return createSuccessTask("HOME", data);
        } else {
            return createErrorTask("回原点失败: " + m_driver->getLastError());
        }
    }
    
    RestInterface::TaskInfo handleStopOperation() {
        if (m_driver->stop()) {
            Json::Value data;
            data["message"] = "停止操作完成";
            return createSuccessTask("STOP", data);
        } else {
            return createErrorTask("停止失败: " + m_driver->getLastError());
        }
    }
    
    RestInterface::TaskInfo handleSetIOOperation(const Json::Value& data) {
        if (!data.isMember("port") || !data.isMember("value")) {
            return createErrorTask("缺少port或value参数");
        }
        
        int port = data["port"].asInt();
        bool value = data["value"].asBool();
        
        if (m_driver->setDigitalOutput(port, value)) {
            Json::Value responseData;
            responseData["port"] = port;
            responseData["value"] = value;
            return createSuccessTask("SET_IO", responseData);
        } else {
            return createErrorTask("IO设置失败: " + m_driver->getLastError());
        }
    }
    
    RestInterface::TaskInfo handleGripperOperation(const Json::Value& data) {
        if (!data.isMember("command")) {
            return createErrorTask("缺少command参数");
        }
        
        int command = data["command"].asInt();
        int force = data.get("force", 50).asInt();
        
        if (m_driver->controlGripper(command, force)) {
            Json::Value responseData;
            responseData["command"] = command;
            responseData["force"] = force;
            responseData["status"] = m_driver->getGripperStatus();
            return createSuccessTask("GRIPPER", responseData);
        } else {
            return createErrorTask("夹爪控制失败: " + m_driver->getLastError());
        }
    }
};

} // namespace Robot
} // namespace AnalysisRobot

#endif // ROBOT_CONTROLLER_H
```

### 3. 配置文件示例

```json
{
  "devices": {
    "robot1": {
      "type": "robot",
      "config": {
        "serialPort": "COM5",
        "baudRate": 115200,
        "slaveId": 3,
        "responseTimeout": 2000,
        "maxJointSpeed": 180,
        "maxLinearSpeed": 1000
      }
    }
  },
  "server": {
    "host": "0.0.0.0",
    "port": 8080
  }
}
```

### 4. API使用示例

```bash
# 获取机器人状态
curl http://localhost:8080/api/robot/status

# 执行取放操作
curl -X POST http://localhost:8080/api/robot/operation \
  -H "Content-Type: application/json" \
  -d '{
    "action": "PICK_PLACE",
    "data": {
      "source": "A01",
      "destination": "B02"
    }
  }'

# 控制夹爪
curl -X POST http://localhost:8080/api/robot/operation \
  -H "Content-Type: application/json" \
  -d '{
    "action": "GRIPPER",
    "data": {
      "command": 1,
      "force": 80
    }
  }'

# 设置IO
curl -X POST http://localhost:8080/api/robot/operation \
  -H "Content-Type: application/json" \
  -d '{
    "action": "SET_IO",
    "data": {
      "port": 5,
      "value": true
    }
  }'
```

这个实现提供了完整的机器人MODBUS协议到REST接口的映射，支持位置控制、IO操作、夹爪控制和异步任务管理。
