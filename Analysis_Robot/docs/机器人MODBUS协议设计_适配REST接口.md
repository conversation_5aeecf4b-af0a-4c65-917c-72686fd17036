# 机器人MODBUS协议设计 - 适配REST接口

**设计目标：** 为Analysis_Robot项目设计一个完整的机器人MODBUS协议，完美适配现有的REST接口架构  
**协议版本：** v1.0  
**设计日期：** 2025年1月29日  

---

## 🎯 设计原则

### 1. REST接口适配性
- 完全兼容现有的 `/api/robot/` REST接口
- 支持异步任务处理机制
- 提供丰富的状态信息和错误反馈

### 2. MODBUS协议规范
- 基于MODBUS RTU协议
- 支持标准的03(读保持寄存器)和06(写单个寄存器)功能码
- 寄存器地址统一规划，避免冲突

### 3. 工业应用适用性
- 支持6轴机械臂标准操作
- 提供精确的位置和状态反馈
- 支持多种运动模式和安全机制

---

## 📋 REST接口映射分析

基于现有的REST接口定义：

### 现有REST接口
```json
// GET /api/robot/status
{
  "name": "工作台1机械臂",
  "description": "描述设备信息", 
  "status": "IDLE",  // IDLE空闲, BUSY忙碌, FAILED故障
  "message": "设备空闲可用",
  "data": {
    "x": 100.5, "y": 200.3, "z": 150.0,
    "u": 0.0, "v": 0.0, "w": 0.0
  },
  "updateTime": "2025-01-29 14:30:00"
}

// POST /api/robot/operation
{
  "action": "PICK_PLACE",
  "data": {
    "source": "B01",
    "destination": "B02"
  }
}
```

---

## 🗺️ MODBUS寄存器映射设计

### 寄存器地址分配表

| 功能分类 | 起始地址 | 结束地址 | 数量 | 访问权限 | 描述 |
|----------|----------|----------|------|----------|------|
| **系统状态** | 0x0000 | 0x000F | 16 | R | 系统状态和错误信息 |
| **位置反馈** | 0x0010 | 0x002F | 32 | R | 当前位置(关节角度+笛卡尔坐标) |
| **运动控制** | 0x0030 | 0x004F | 32 | R/W | 运动指令和参数 |
| **IO控制** | 0x0050 | 0x005F | 16 | R/W | 数字IO和模拟IO |
| **安全监控** | 0x0060 | 0x006F | 16 | R | 安全状态和限位信息 |
| **任务管理** | 0x0070 | 0x007F | 16 | R/W | 任务状态和控制 |
| **配置参数** | 0x0080 | 0x009F | 32 | R/W | 速度、加速度等参数 |

### 详细寄存器定义

#### 1. 系统状态寄存器 (0x0000-0x000F)

| 地址 | 名称 | 类型 | 描述 | 值定义 |
|------|------|------|------|--------|
| 0x0000 | SYSTEM_STATUS | R | 系统总状态 | 0=DISCONNECTED, 1=IDLE, 2=BUSY, 3=ERROR, 4=EMERGENCY |
| 0x0001 | ERROR_CODE | R | 错误代码 | 0=无错误, 1-999=具体错误码 |
| 0x0002 | ROBOT_MODE | R | 机器人模式 | 0=手动, 1=自动, 2=示教, 3=维护 |
| 0x0003 | MOTION_STATUS | R | 运动状态 | 0=停止, 1=运动中, 2=暂停, 3=完成 |
| 0x0004 | TASK_ID_HIGH | R | 当前任务ID高位 | 任务ID的高16位 |
| 0x0005 | TASK_ID_LOW | R | 当前任务ID低位 | 任务ID的低16位 |
| 0x0006 | FIRMWARE_VER | R | 固件版本 | 版本号*100 |
| 0x0007 | COMM_STATUS | R | 通信状态 | 0=正常, 1=超时, 2=错误 |

#### 2. 位置反馈寄存器 (0x0010-0x002F)

| 地址 | 名称 | 类型 | 描述 | 单位/精度 |
|------|------|------|------|-----------|
| 0x0010-0x0011 | JOINT1_ANGLE | R | 关节1角度 | 0.01度, 有符号32位 |
| 0x0012-0x0013 | JOINT2_ANGLE | R | 关节2角度 | 0.01度, 有符号32位 |
| 0x0014-0x0015 | JOINT3_ANGLE | R | 关节3角度 | 0.01度, 有符号32位 |
| 0x0016-0x0017 | JOINT4_ANGLE | R | 关节4角度 | 0.01度, 有符号32位 |
| 0x0018-0x0019 | JOINT5_ANGLE | R | 关节5角度 | 0.01度, 有符号32位 |
| 0x001A-0x001B | JOINT6_ANGLE | R | 关节6角度 | 0.01度, 有符号32位 |
| 0x001C-0x001D | TCP_X | R | TCP X坐标 | 0.1mm, 有符号32位 |
| 0x001E-0x001F | TCP_Y | R | TCP Y坐标 | 0.1mm, 有符号32位 |
| 0x0020-0x0021 | TCP_Z | R | TCP Z坐标 | 0.1mm, 有符号32位 |
| 0x0022-0x0023 | TCP_RX | R | TCP RX旋转 | 0.01度, 有符号32位 |
| 0x0024-0x0025 | TCP_RY | R | TCP RY旋转 | 0.01度, 有符号32位 |
| 0x0026-0x0027 | TCP_RZ | R | TCP RZ旋转 | 0.01度, 有符号32位 |

#### 3. 运动控制寄存器 (0x0030-0x004F)

| 地址 | 名称 | 类型 | 描述 | 值定义 |
|------|------|------|------|--------|
| 0x0030 | MOTION_CMD | W | 运动指令 | 1=HOME, 2=MOVEJ, 3=MOVEL, 4=STOP, 5=PAUSE, 6=RESUME |
| 0x0031 | CMD_STATUS | R | 指令状态 | 0=空闲, 1=执行中, 2=完成, 3=错误 |
| 0x0032-0x0033 | TARGET_X | W | 目标X坐标 | 0.1mm, 有符号32位 |
| 0x0034-0x0035 | TARGET_Y | W | 目标Y坐标 | 0.1mm, 有符号32位 |
| 0x0036-0x0037 | TARGET_Z | W | 目标Z坐标 | 0.1mm, 有符号32位 |
| 0x0038-0x0039 | TARGET_RX | W | 目标RX旋转 | 0.01度, 有符号32位 |
| 0x003A-0x003B | TARGET_RY | W | 目标RY旋转 | 0.01度, 有符号32位 |
| 0x003C-0x003D | TARGET_RZ | W | 目标RZ旋转 | 0.01度, 有符号32位 |
| 0x003E | MOTION_SPEED | W | 运动速度 | 1-100% |
| 0x003F | MOTION_ACCEL | W | 运动加速度 | 1-100% |

#### 4. IO控制寄存器 (0x0050-0x005F)

| 地址 | 名称 | 类型 | 描述 | 位定义 |
|------|------|------|------|--------|
| 0x0050 | DIGITAL_OUT | R/W | 数字输出 | Bit0-15对应DO0-DO15 |
| 0x0051 | DIGITAL_IN | R | 数字输入 | Bit0-15对应DI0-DI15 |
| 0x0052 | GRIPPER_CMD | W | 夹爪控制 | 0=释放, 1=夹取, 2=停止 |
| 0x0053 | GRIPPER_STATUS | R | 夹爪状态 | 0=释放, 1=夹取, 2=运动中, 3=错误 |
| 0x0054 | GRIPPER_FORCE | W | 夹爪力度 | 1-100% |
| 0x0055 | GRIPPER_POSITION | R | 夹爪位置 | 0-100% |

#### 5. 任务管理寄存器 (0x0070-0x007F)

| 地址 | 名称 | 类型 | 描述 | 值定义 |
|------|------|------|------|--------|
| 0x0070 | TASK_CMD | W | 任务指令 | 1=START, 2=PAUSE, 3=STOP, 4=RESET |
| 0x0071 | TASK_STATUS | R | 任务状态 | 0=IDLE, 1=RUNNING, 2=PAUSED, 3=COMPLETED, 4=FAILED |
| 0x0072-0x0073 | TASK_PROGRESS | R | 任务进度 | 0-10000 (0.01%精度) |
| 0x0074 | PICK_PLACE_CMD | W | 取放指令 | 1=执行取放任务 |
| 0x0075 | PICK_PLACE_STATUS | R | 取放状态 | 0=空闲, 1=取料中, 2=移动中, 3=放料中, 4=完成, 5=失败 |
| 0x0076 | SOURCE_POSITION | W | 源位置编码 | 位置编码(如B01=0x0B01) |
| 0x0077 | DEST_POSITION | W | 目标位置编码 | 位置编码(如B02=0x0B02) |

---

## 🔧 REST接口到MODBUS的映射实现

### 1. 状态查询映射 (GET /api/robot/status)

```cpp
// RobotController::getDeviceStatus() 实现
RestInterface::DeviceStatus RobotController::getDeviceStatus() {
    RestInterface::DeviceStatus status;
    
    // 读取系统状态
    uint16_t systemStatus = m_driver->readRegister(0x0000);
    uint16_t errorCode = m_driver->readRegister(0x0001);
    
    // 映射状态
    switch (systemStatus) {
        case 0: status.status = "DISCONNECTED"; break;
        case 1: status.status = "IDLE"; break;
        case 2: status.status = "BUSY"; break;
        case 3: 
        case 4: status.status = "FAILED"; break;
    }
    
    // 读取位置信息
    Json::Value data;
    data["x"] = m_driver->readFloat32(0x001C) / 10.0;  // TCP_X
    data["y"] = m_driver->readFloat32(0x001E) / 10.0;  // TCP_Y  
    data["z"] = m_driver->readFloat32(0x0020) / 10.0;  // TCP_Z
    data["u"] = m_driver->readFloat32(0x0022) / 100.0; // TCP_RX
    data["v"] = m_driver->readFloat32(0x0024) / 100.0; // TCP_RY
    data["w"] = m_driver->readFloat32(0x0026) / 100.0; // TCP_RZ
    
    status.data = data;
    return status;
}
```

### 2. 操作指令映射 (POST /api/robot/operation)

```cpp
// RobotController::executeDeviceOperation() 实现
RestInterface::TaskInfo RobotController::executeDeviceOperation(
    const std::string& action, const Json::Value& data) {
    
    if (action == "PICK_PLACE") {
        return handlePickPlaceOperation(data);
    } else if (action == "MOVE_TO") {
        return handleMoveToOperation(data);
    } else if (action == "HOME") {
        return handleHomeOperation();
    } else if (action == "STOP") {
        return handleStopOperation();
    }
    
    return createErrorTask("不支持的操作: " + action);
}

RestInterface::TaskInfo RobotController::handlePickPlaceOperation(const Json::Value& data) {
    // 解析源位置和目标位置
    std::string source = data.get("source", "").asString();
    std::string destination = data.get("destination", "").asString();
    
    // 转换位置编码
    uint16_t sourceCode = encodePosition(source);
    uint16_t destCode = encodePosition(destination);
    
    // 写入MODBUS寄存器
    m_driver->writeRegister(0x0076, sourceCode);    // SOURCE_POSITION
    m_driver->writeRegister(0x0077, destCode);      // DEST_POSITION
    m_driver->writeRegister(0x0074, 1);             // PICK_PLACE_CMD
    
    // 创建异步任务
    int taskId = generateTaskId();
    
    // 启动监控线程
    std::thread([this, taskId]() {
        monitorPickPlaceTask(taskId);
    }).detach();
    
    RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "PICK_PLACE";
    task.status = RestInterface::TaskStatus::SUBMITTED;
    task.message = "取放任务已提交";
    
    return task;
}
```

### 3. 任务查询映射 (GET /api/robot/query)

```cpp
void RobotController::monitorPickPlaceTask(int taskId) {
    while (true) {
        uint16_t status = m_driver->readRegister(0x0075); // PICK_PLACE_STATUS
        uint32_t progress = m_driver->readUint32(0x0072); // TASK_PROGRESS
        
        Json::Value data;
        data["progress"] = progress / 100.0; // 转换为百分比
        
        switch (status) {
            case 0: // 空闲
                updateTaskStatus(taskId, RestInterface::TaskStatus::SUBMITTED, "等待执行", data);
                break;
            case 1: // 取料中
                updateTaskStatus(taskId, RestInterface::TaskStatus::IN_PROGRESS, "正在取料", data);
                break;
            case 2: // 移动中
                updateTaskStatus(taskId, RestInterface::TaskStatus::IN_PROGRESS, "正在移动", data);
                break;
            case 3: // 放料中
                updateTaskStatus(taskId, RestInterface::TaskStatus::IN_PROGRESS, "正在放料", data);
                break;
            case 4: // 完成
                updateTaskStatus(taskId, RestInterface::TaskStatus::SUCCESS, "取放任务完成", data);
                return;
            case 5: // 失败
                uint16_t errorCode = m_driver->readRegister(0x0001);
                updateTaskStatus(taskId, RestInterface::TaskStatus::FAILED, 
                               "取放任务失败，错误码: " + std::to_string(errorCode), data);
                return;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
}
```

---

## 📊 位置编码系统

### 标准位置编码表
```cpp
// 位置编码映射
std::map<std::string, uint16_t> POSITION_CODES = {
    // 工作台位置
    {"A01", 0x0A01}, {"A02", 0x0A02}, {"A03", 0x0A03},
    {"B01", 0x0B01}, {"B02", 0x0B02}, {"B03", 0x0B03},
    {"C01", 0x0C01}, {"C02", 0x0C02}, {"C03", 0x0C03},
    
    // 货架位置
    {"R01", 0x0R01}, {"R02", 0x0R02}, {"R03", 0x0R03},
    
    // 特殊位置
    {"HOME", 0x0000},     // 原点位置
    {"SAFE", 0x0001},     // 安全位置
    {"MAINTENANCE", 0x0002} // 维护位置
};

uint16_t encodePosition(const std::string& position) {
    auto it = POSITION_CODES.find(position);
    return (it != POSITION_CODES.end()) ? it->second : 0xFFFF;
}
```

---

## 🛡️ 安全机制

### 1. 通信安全
- 定期心跳检测 (每500ms)
- 通信超时保护 (2秒)
- 错误重试机制 (最多3次)

### 2. 运动安全
- 软限位检查
- 碰撞检测
- 急停信号处理
- 安全区域监控

### 3. 数据完整性
- CRC校验
- 寄存器范围检查
- 参数有效性验证

---

## 📋 使用示例

### 配置文件示例
```json
{
  "robot1": {
    "type": "robot",
    "config": {
      "serialPort": "COM5",
      "baudRate": 115200,
      "slaveId": 3,
      "responseTimeout": 2000,
      "maxJointSpeed": 180,
      "maxLinearSpeed": 1000
    }
  }
}
```

### API调用示例
```bash
# 获取机器人状态
curl http://localhost:8080/api/robot/status

# 执行取放操作
curl -X POST http://localhost:8080/api/robot/operation \
  -H "Content-Type: application/json" \
  -d '{
    "action": "PICK_PLACE",
    "data": {
      "source": "A01",
      "destination": "B02"
    }
  }'

# 查询任务状态
curl http://localhost:8080/api/robot/query?taskId=1001
```

这个MODBUS协议设计完全适配您现有的REST接口架构，提供了完整的机器人控制功能，支持异步任务处理和丰富的状态反馈。
