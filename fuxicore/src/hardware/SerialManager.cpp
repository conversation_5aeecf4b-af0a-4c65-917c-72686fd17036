#include "hardware/SerialManager.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <algorithm>
#include <cctype>
#include <cstring>

#ifdef _WIN32
#include <windows.h>
#include <winbase.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#pragma comment(lib, "cfgmgr32.lib")
#pragma comment(lib, "setupapi.lib")

// 定义缺失的常量
#ifndef MS_DTR_ON
#define MS_DTR_ON 0x01
#endif
#ifndef MS_RTS_ON
#define MS_RTS_ON 0x02
#endif
#endif

namespace fuxicore {

    SerialManager::SerialManager(const SerialConfig& config)
        : config_(config), state_(SerialState::Closed), should_stop_(false)
#ifdef _WIN32
        , handle_(INVALID_HANDLE_VALUE)
#endif
    {
#ifdef _WIN32
        std::memset(&read_overlapped_, 0, sizeof(read_overlapped_));
        std::memset(&write_overlapped_, 0, sizeof(write_overlapped_));
        read_overlapped_.hEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
        write_overlapped_.hEvent = CreateEvent(nullptr, TRUE, FALSE, nullptr);
#endif
    }

    SerialManager::~SerialManager() {
        close();
        cleanup_overlapped();
    }

    SerialManager::SerialManager(SerialManager&& other) noexcept
        : config_(std::move(other.config_))
        , state_(other.state_.load())
        , should_stop_(other.should_stop_.load())
#ifdef _WIN32
        , handle_(other.handle_)
        , read_overlapped_(other.read_overlapped_)
        , write_overlapped_(other.write_overlapped_)
#endif
        , worker_thread_(std::move(other.worker_thread_))
        , send_queue_(std::move(other.send_queue_))
        , on_data_received_(std::move(other.on_data_received_))
        , on_error_(std::move(other.on_error_))
        , on_opened_(std::move(other.on_opened_))
        , on_closed_(std::move(other.on_closed_)) {
#ifdef _WIN32
        other.handle_ = INVALID_HANDLE_VALUE;
        std::memset(&other.read_overlapped_, 0, sizeof(other.read_overlapped_));
        std::memset(&other.write_overlapped_, 0, sizeof(other.write_overlapped_));
#endif
    }

    SerialManager& SerialManager::operator=(SerialManager&& other) noexcept {
        if (this != &other) {
            close();
            cleanup_overlapped();

            config_ = std::move(other.config_);
            state_ = other.state_.load();
            should_stop_ = other.should_stop_.load();
#ifdef _WIN32
            handle_ = other.handle_;
            read_overlapped_ = other.read_overlapped_;
            write_overlapped_ = other.write_overlapped_;
            other.handle_ = INVALID_HANDLE_VALUE;
            std::memset(&other.read_overlapped_, 0, sizeof(other.read_overlapped_));
            std::memset(&other.write_overlapped_, 0, sizeof(other.write_overlapped_));
#endif
            worker_thread_ = std::move(other.worker_thread_);
            send_queue_ = std::move(other.send_queue_);
            on_data_received_ = std::move(other.on_data_received_);
            on_error_ = std::move(other.on_error_);
            on_opened_ = std::move(other.on_opened_);
            on_closed_ = std::move(other.on_closed_);
        }
        return *this;
    }

    void SerialManager::set_config(const SerialConfig& config) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (state_ != SerialState::Closed) {
            throw SerialException("Cannot change configuration while port is open");
        }
        config_ = config;
    }

    const SerialConfig& SerialManager::get_config() const noexcept {
        return config_;
    }

    SerialState SerialManager::get_state() const noexcept {
        return state_.load();
    }

    void SerialManager::set_data_received_callback(std::function<void(const std::vector<uint8_t>&)> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_data_received_ = std::move(callback);
    }

    void SerialManager::set_error_callback(std::function<void(const std::string&)> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_error_ = std::move(callback);
    }

    void SerialManager::set_opened_callback(std::function<void()> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_opened_ = std::move(callback);
    }

    void SerialManager::set_closed_callback(std::function<void()> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_closed_ = std::move(callback);
    }

    std::future<bool> SerialManager::open_async() {
        return std::async(std::launch::async, [this]() {
            return open();
        });
    }

    bool SerialManager::open(int timeout_ms) {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (state_ != SerialState::Closed) {
            return state_ == SerialState::Open;
        }

        state_ = SerialState::Opening;
        
#ifdef _WIN32
        // 打开串口
        std::string port_path = "\\\\.\\" + config_.port_name;
        handle_ = CreateFileA(
            port_path.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            FILE_FLAG_OVERLAPPED,
            nullptr
        );

        if (handle_ == INVALID_HANDLE_VALUE) {
            state_ = SerialState::Error;
            int error = get_last_error();
            throw SerialException("Failed to open port " + config_.port_name + ": " + get_error_message(error), error);
        }

        // 配置串口参数
        if (!configure_port() || !setup_comm_timeouts()) {
            CloseHandle(handle_);
            handle_ = INVALID_HANDLE_VALUE;
            state_ = SerialState::Error;
            int error = get_last_error();
            throw SerialException("Failed to configure port " + config_.port_name + ": " + get_error_message(error), error);
        }
#endif

        state_ = SerialState::Open;
        should_stop_ = false;
        
        // 启动工作线程
        worker_thread_ = std::thread(&SerialManager::worker_loop, this);
        
        lock.unlock();
        
        if (on_opened_) {
            on_opened_();
        }
        
        return true;
    }

    void SerialManager::close() {
        std::unique_lock<std::mutex> lock(mutex_);
        
        if (state_ == SerialState::Closed) {
            return;
        }

        should_stop_ = true;
        cv_.notify_all();
        
        lock.unlock();
        
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        
        lock.lock();
        
#ifdef _WIN32
        if (handle_ != INVALID_HANDLE_VALUE) {
            CloseHandle(handle_);
            handle_ = INVALID_HANDLE_VALUE;
        }
#endif
        
        state_ = SerialState::Closed;
        
        // 清空发送队列
        std::queue<std::vector<uint8_t>> empty_queue;
        send_queue_.swap(empty_queue);
        
        lock.unlock();
        
        if (on_closed_) {
            on_closed_();
        }
    }

    bool SerialManager::is_open() const noexcept {
        return state_ == SerialState::Open;
    }

    std::vector<uint8_t> SerialManager::receive_data(size_t max_size) {
        if (!is_open()) {
            throw SerialException("Port is not open");
        }

        if (max_size == 0) {
            max_size = config_.buffer_size;
        }

        std::vector<uint8_t> buffer(std::min<size_t>(max_size, config_.buffer_size));
        
#ifdef _WIN32
        DWORD bytes_read = 0;
        ResetEvent(read_overlapped_.hEvent);
        
        if (!ReadFile(handle_, buffer.data(), static_cast<DWORD>(buffer.size()), &bytes_read, &read_overlapped_)) {
            DWORD error = GetLastError();
            if (error == ERROR_IO_PENDING) {
                DWORD wait_result = WaitForSingleObject(read_overlapped_.hEvent, config_.read_timeout_ms);
                if (wait_result == WAIT_OBJECT_0) {
                    if (!GetOverlappedResult(handle_, &read_overlapped_, &bytes_read, FALSE)) {
                        throw SerialException("Failed to read from port: " + get_error_message(get_last_error()));
                    }
                } else if (wait_result == WAIT_TIMEOUT) {
                    CancelIo(handle_);
                    throw SerialException("Read timeout");
                } else {
                    CancelIo(handle_);
                    throw SerialException("Read operation failed: " + get_error_message(get_last_error()));
                }
            } else {
                throw SerialException("Failed to read from port: " + get_error_message(error));
            }
        }
        
        buffer.resize(bytes_read);
#endif
        
        return buffer;
    }

    std::future<std::vector<uint8_t>> SerialManager::receive_data_async(size_t max_size) {
        return std::async(std::launch::async, [this, max_size]() {
            return receive_data(max_size);
        });
    }

    bool SerialManager::send_raw_data(const uint8_t* data, size_t size) {
        if (!is_open()) {
            return false;
        }

        if (size == 0) {
            return true;
        }

#ifdef _WIN32
        DWORD bytes_written = 0;
        ResetEvent(write_overlapped_.hEvent);
        
        if (!WriteFile(handle_, data, static_cast<DWORD>(size), &bytes_written, &write_overlapped_)) {
            DWORD error = GetLastError();
            if (error == ERROR_IO_PENDING) {
                DWORD wait_result = WaitForSingleObject(write_overlapped_.hEvent, config_.write_timeout_ms);
                if (wait_result == WAIT_OBJECT_0) {
                    if (!GetOverlappedResult(handle_, &write_overlapped_, &bytes_written, FALSE)) {
                        if (on_error_) {
                            on_error_("Failed to write to port: " + get_error_message(get_last_error()));
                        }
                        return false;
                    }
                } else {
                    CancelIo(handle_);
                    if (on_error_) {
                        on_error_("Write timeout or operation failed");
                    }
                    return false;
                }
            } else {
                if (on_error_) {
                    on_error_("Failed to write to port: " + get_error_message(error));
                }
                return false;
            }
        }
        
        return bytes_written == size;
#else
        return false;
#endif
    }

    void SerialManager::queue_send_data(const std::vector<uint8_t>& data) {
        std::lock_guard<std::mutex> lock(send_mutex_);
        send_queue_.push(data);
        cv_.notify_one();
    }

    void SerialManager::process_send_queue() {
        while (!should_stop_) {
            std::unique_lock<std::mutex> lock(send_mutex_);
            cv_.wait(lock, [this] { return !send_queue_.empty() || should_stop_; });
            
            if (should_stop_) {
                break;
            }
            
            if (!send_queue_.empty()) {
                auto data = send_queue_.front();
                send_queue_.pop();
                lock.unlock();
                
                send_raw_data(data.data(), data.size());
            }
        }
    }

    void SerialManager::worker_loop() {
        while (!should_stop_ && is_open()) {
            try {
                handle_communication();
                process_send_queue();
            } catch (const std::exception& e) {
                if (on_error_) {
                    on_error_(e.what());
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
            }
        }
    }

    void SerialManager::handle_communication() {
        if (!is_open()) {
            return;
        }

        try {
            auto data = receive_data(config_.buffer_size);
            if (!data.empty() && on_data_received_) {
                on_data_received_(data);
            }
        } catch (const SerialException& e) {
            // 超时是正常的，不需要报错
            if (std::string(e.what()).find("timeout") == std::string::npos) {
                if (on_error_) {
                    on_error_(e.what());
                }
            }
        }
    }

#ifdef _WIN32
    bool SerialManager::configure_port() {
        DCB dcb = { 0 };
        dcb.DCBlength = sizeof(DCB);
        
        if (!GetCommState(handle_, &dcb)) {
            return false;
        }
        
        dcb.BaudRate = config_.baud_rate;
        dcb.ByteSize = config_.data_bits;
        
        switch (config_.parity) {
        case SerialParity::None:
            dcb.Parity = NOPARITY;
            dcb.fParity = FALSE;
            break;
        case SerialParity::Odd:
            dcb.Parity = ODDPARITY;
            dcb.fParity = TRUE;
            break;
        case SerialParity::Even:
            dcb.Parity = EVENPARITY;
            dcb.fParity = TRUE;
            break;
        case SerialParity::Mark:
            dcb.Parity = MARKPARITY;
            dcb.fParity = TRUE;
            break;
        case SerialParity::Space:
            dcb.Parity = SPACEPARITY;
            dcb.fParity = TRUE;
            break;
        }
        
        switch (config_.stop_bits) {
        case SerialStopBits::One:
            dcb.StopBits = ONESTOPBIT;
            break;
        case SerialStopBits::OnePointFive:
            dcb.StopBits = ONE5STOPBITS;
            break;
        case SerialStopBits::Two:
            dcb.StopBits = TWOSTOPBITS;
            break;
        }
        
        switch (config_.flow_control) {
        case SerialFlowControl::None:
            dcb.fOutxCtsFlow = FALSE;
            dcb.fOutxDsrFlow = FALSE;
            dcb.fOutX = FALSE;
            dcb.fInX = FALSE;
            break;
        case SerialFlowControl::Hardware:
            dcb.fOutxCtsFlow = TRUE;
            dcb.fOutxDsrFlow = TRUE;
            dcb.fOutX = FALSE;
            dcb.fInX = FALSE;
            break;
        case SerialFlowControl::Software:
            dcb.fOutxCtsFlow = FALSE;
            dcb.fOutxDsrFlow = FALSE;
            dcb.fOutX = TRUE;
            dcb.fInX = TRUE;
            break;
        }
        
        dcb.fDtrControl = config_.enable_dtr ? DTR_CONTROL_ENABLE : DTR_CONTROL_DISABLE;
        dcb.fRtsControl = config_.enable_rts ? RTS_CONTROL_ENABLE : RTS_CONTROL_DISABLE;
        
        return SetCommState(handle_, &dcb) != 0;
    }

    bool SerialManager::setup_comm_timeouts() {
        COMMTIMEOUTS timeouts = { 0 };
        timeouts.ReadIntervalTimeout = 50;
        timeouts.ReadTotalTimeoutConstant = config_.read_timeout_ms;
        timeouts.ReadTotalTimeoutMultiplier = 10;
        timeouts.WriteTotalTimeoutConstant = config_.write_timeout_ms;
        timeouts.WriteTotalTimeoutMultiplier = 10;
        
        return SetCommTimeouts(handle_, &timeouts) != 0;
    }

    int SerialManager::get_last_error() const {
        return static_cast<int>(GetLastError());
    }

    std::string SerialManager::get_error_message(int error_code) const {
        LPSTR message_buffer = nullptr;
        size_t size = FormatMessageA(
            FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
            nullptr,
            error_code,
            MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
            reinterpret_cast<LPSTR>(&message_buffer),
            0,
            nullptr
        );
        
        std::string message(message_buffer, size);
        LocalFree(message_buffer);
        
        // 移除末尾的换行符
        while (!message.empty() && (message.back() == '\r' || message.back() == '\n')) {
            message.pop_back();
        }
        
        return message;
    }

    void SerialManager::cleanup_overlapped() {
        if (read_overlapped_.hEvent != nullptr) {
            CloseHandle(read_overlapped_.hEvent);
            read_overlapped_.hEvent = nullptr;
        }
        if (write_overlapped_.hEvent != nullptr) {
            CloseHandle(write_overlapped_.hEvent);
            write_overlapped_.hEvent = nullptr;
        }
    }
#endif

    // 串口控制方法
    bool SerialManager::set_dtr(bool enable) {
#ifdef _WIN32
        if (!is_open()) return false;
        return EscapeCommFunction(handle_, enable ? SETDTR : CLRDTR) != 0;
#else
        return false;
#endif
    }

    bool SerialManager::set_rts(bool enable) {
#ifdef _WIN32
        if (!is_open()) return false;
        return EscapeCommFunction(handle_, enable ? SETRTS : CLRRTS) != 0;
#else
        return false;
#endif
    }

    bool SerialManager::get_dtr() const {
#ifdef _WIN32
        if (!is_open()) return false;
        DWORD status;
        if (GetCommModemStatus(handle_, &status)) {
            return (status & MS_DTR_ON) != 0;
        }
#endif
        return false;
    }

    bool SerialManager::get_rts() const {
#ifdef _WIN32
        if (!is_open()) return false;
        DWORD status;
        if (GetCommModemStatus(handle_, &status)) {
            return (status & MS_RTS_ON) != 0;
        }
#endif
        return false;
    }

    bool SerialManager::get_cts() const {
#ifdef _WIN32
        if (!is_open()) return false;
        DWORD status;
        if (GetCommModemStatus(handle_, &status)) {
            return (status & MS_CTS_ON) != 0;
        }
#endif
        return false;
    }

    bool SerialManager::get_dsr() const {
#ifdef _WIN32
        if (!is_open()) return false;
        DWORD status;
        if (GetCommModemStatus(handle_, &status)) {
            return (status & MS_DSR_ON) != 0;
        }
#endif
        return false;
    }

    bool SerialManager::get_ring() const {
#ifdef _WIN32
        if (!is_open()) return false;
        DWORD status;
        if (GetCommModemStatus(handle_, &status)) {
            return (status & MS_RING_ON) != 0;
        }
#endif
        return false;
    }

    bool SerialManager::get_rlsd() const {
#ifdef _WIN32
        if (!is_open()) return false;
        DWORD status;
        if (GetCommModemStatus(handle_, &status)) {
            return (status & MS_RLSD_ON) != 0;
        }
#endif
        return false;
    }

    // 缓冲区控制
    bool SerialManager::flush_input_buffer() {
#ifdef _WIN32
        if (!is_open()) return false;
        return PurgeComm(handle_, PURGE_RXCLEAR) != 0;
#else
        return false;
#endif
    }

    bool SerialManager::flush_output_buffer() {
#ifdef _WIN32
        if (!is_open()) return false;
        return PurgeComm(handle_, PURGE_TXCLEAR) != 0;
#else
        return false;
#endif
    }

    bool SerialManager::flush_all_buffers() {
#ifdef _WIN32
        if (!is_open()) return false;
        return PurgeComm(handle_, PURGE_RXCLEAR | PURGE_TXCLEAR) != 0;
#else
        return false;
#endif
    }

    size_t SerialManager::get_input_buffer_count() const {
#ifdef _WIN32
        if (!is_open()) return 0;
        COMSTAT comstat;
        DWORD errors;
        if (ClearCommError(handle_, &errors, &comstat)) {
            return comstat.cbInQue;
        }
#endif
        return 0;
    }

    size_t SerialManager::get_output_buffer_count() const {
#ifdef _WIN32
        if (!is_open()) return 0;
        COMSTAT comstat;
        DWORD errors;
        if (ClearCommError(handle_, &errors, &comstat)) {
            return comstat.cbOutQue;
        }
#endif
        return 0;
    }

    // 静态工具方法
    std::vector<SerialPortInfo> SerialManager::get_available_ports() {
        std::vector<SerialPortInfo> ports;
        
#ifdef _WIN32
        HDEVINFO device_info_set = SetupDiGetClassDevs(
            &GUID_DEVCLASS_PORTS,
            nullptr,
            nullptr,
            DIGCF_PRESENT
        );
        
        if (device_info_set == INVALID_HANDLE_VALUE) {
            return ports;
        }
        
        SP_DEVINFO_DATA device_info_data;
        device_info_data.cbSize = sizeof(SP_DEVINFO_DATA);
        
        for (DWORD i = 0; SetupDiEnumDeviceInfo(device_info_set, i, &device_info_data); i++) {
            SerialPortInfo port_info;
            
            // 获取端口名称
            HKEY key = SetupDiOpenDevRegKey(
                device_info_set,
                &device_info_data,
                DICS_FLAG_GLOBAL,
                0,
                DIREG_DEV,
                KEY_READ
            );
            
            if (key != INVALID_HANDLE_VALUE) {
                char port_name[256];
                DWORD port_name_size = sizeof(port_name);
                if (RegQueryValueExA(key, "PortName", nullptr, nullptr, 
                                   reinterpret_cast<LPBYTE>(port_name), &port_name_size) == ERROR_SUCCESS) {
                    port_info.port_name = port_name;
                }
                RegCloseKey(key);
            }
            
            // 获取设备描述
            char description[256];
            if (SetupDiGetDeviceRegistryPropertyA(
                device_info_set,
                &device_info_data,
                SPDRP_DEVICEDESC,
                nullptr,
                reinterpret_cast<PBYTE>(description),
                sizeof(description),
                nullptr
            )) {
                port_info.description = description;
            }
            
            // 获取硬件ID
            char hardware_id[256];
            if (SetupDiGetDeviceRegistryPropertyA(
                device_info_set,
                &device_info_data,
                SPDRP_HARDWAREID,
                nullptr,
                reinterpret_cast<PBYTE>(hardware_id),
                sizeof(hardware_id),
                nullptr
            )) {
                port_info.hardware_id = hardware_id;
            }
            
            // 获取制造商
            char manufacturer[256];
            if (SetupDiGetDeviceRegistryPropertyA(
                device_info_set,
                &device_info_data,
                SPDRP_MFG,
                nullptr,
                reinterpret_cast<PBYTE>(manufacturer),
                sizeof(manufacturer),
                nullptr
            )) {
                port_info.manufacturer = manufacturer;
            }
            
            port_info.is_available = is_port_available(port_info.port_name);
            
            if (!port_info.port_name.empty()) {
                ports.push_back(port_info);
            }
        }
        
        SetupDiDestroyDeviceInfoList(device_info_set);
#endif
        
        return ports;
    }

    bool SerialManager::is_port_available(const std::string& port_name) {
#ifdef _WIN32
        std::string port_path = "\\\\.\\" + port_name;
        HANDLE handle = CreateFileA(
            port_path.c_str(),
            GENERIC_READ | GENERIC_WRITE,
            0,
            nullptr,
            OPEN_EXISTING,
            0,
            nullptr
        );
        
        if (handle != INVALID_HANDLE_VALUE) {
            CloseHandle(handle);
            return true;
        }
#endif
        return false;
    }

    std::vector<std::string> SerialManager::get_port_names() {
        std::vector<std::string> port_names;
        auto ports = get_available_ports();
        
        for (const auto& port : ports) {
            port_names.push_back(port.port_name);
        }
        
        return port_names;
    }

    // SerialUtils 实现
    bool SerialUtils::is_valid_baud_rate(uint32_t baud_rate) {
        static const std::vector<uint32_t> valid_rates = {
            110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 38400, 57600, 115200, 128000, 256000
        };
        return std::find(valid_rates.begin(), valid_rates.end(), baud_rate) != valid_rates.end();
    }

    std::vector<uint32_t> SerialUtils::get_standard_baud_rates() {
        return { 110, 300, 600, 1200, 2400, 4800, 9600, 14400, 19200, 38400, 57600, 115200, 128000, 256000 };
    }

    std::string SerialUtils::bytes_to_hex_string(const std::vector<uint8_t>& data, bool uppercase) {
        std::ostringstream oss;
        oss << std::hex << std::setfill('0');
        if (uppercase) {
            oss << std::uppercase;
        }
        
        for (uint8_t byte : data) {
            oss << std::setw(2) << static_cast<int>(byte);
        }
        
        return oss.str();
    }

    std::vector<uint8_t> SerialUtils::hex_string_to_bytes(const std::string& hex_string) {
        std::vector<uint8_t> bytes;
        
        for (size_t i = 0; i < hex_string.length(); i += 2) {
            if (i + 1 < hex_string.length()) {
                std::string byte_string = hex_string.substr(i, 2);
                if (std::isxdigit(byte_string[0]) && std::isxdigit(byte_string[1])) {
                    uint8_t byte = static_cast<uint8_t>(std::stoul(byte_string, nullptr, 16));
                    bytes.push_back(byte);
                }
            }
        }
        
        return bytes;
    }

    uint8_t SerialUtils::calculate_checksum(const std::vector<uint8_t>& data) {
        uint8_t checksum = 0;
        for (uint8_t byte : data) {
            checksum ^= byte;
        }
        return checksum;
    }

    uint16_t SerialUtils::calculate_crc16(const std::vector<uint8_t>& data, uint16_t polynomial) {
        uint16_t crc = 0xFFFF;
        
        for (uint8_t byte : data) {
            crc ^= byte;
            for (int i = 0; i < 8; i++) {
                if (crc & 1) {
                    crc = (crc >> 1) ^ polynomial;
                } else {
                    crc >>= 1;
                }
            }
        }
        
        return crc;
    }

    uint32_t SerialUtils::calculate_crc32(const std::vector<uint8_t>& data, uint32_t polynomial) {
        uint32_t crc = 0xFFFFFFFF;
        
        for (uint8_t byte : data) {
            crc ^= byte;
            for (int i = 0; i < 8; i++) {
                if (crc & 1) {
                    crc = (crc >> 1) ^ polynomial;
                } else {
                    crc >>= 1;
                }
            }
        }
        
        return ~crc;
    }

    std::vector<std::vector<uint8_t>> SerialUtils::split_by_delimiter(const std::vector<uint8_t>& data, uint8_t delimiter) {
        std::vector<std::vector<uint8_t>> result;
        std::vector<uint8_t> current;
        
        for (uint8_t byte : data) {
            if (byte == delimiter) {
                if (!current.empty()) {
                    result.push_back(current);
                    current.clear();
                }
            } else {
                current.push_back(byte);
            }
        }
        
        if (!current.empty()) {
            result.push_back(current);
        }
        
        return result;
    }

    std::vector<std::vector<uint8_t>> SerialUtils::split_by_length(const std::vector<uint8_t>& data, size_t length) {
        std::vector<std::vector<uint8_t>> result;
        
        for (size_t i = 0; i < data.size(); i += length) {
            size_t end = std::min<size_t>(i + length, data.size());
            result.emplace_back(data.begin() + i, data.begin() + end);
        }
        
        return result;
    }

    std::vector<uint8_t> SerialUtils::escape_data(const std::vector<uint8_t>& data, uint8_t escape_char) {
        std::vector<uint8_t> escaped;
        
        for (uint8_t byte : data) {
            if (byte == escape_char) {
                escaped.push_back(escape_char);
                escaped.push_back(escape_char);
            } else {
                escaped.push_back(byte);
            }
        }
        
        return escaped;
    }

    std::vector<uint8_t> SerialUtils::unescape_data(const std::vector<uint8_t>& data, uint8_t escape_char) {
        std::vector<uint8_t> unescaped;
        
        for (size_t i = 0; i < data.size(); i++) {
            if (data[i] == escape_char && i + 1 < data.size() && data[i + 1] == escape_char) {
                unescaped.push_back(escape_char);
                i++; // 跳过下一个转义字符
            } else {
                unescaped.push_back(data[i]);
            }
        }
        
        return unescaped;
    }

} // namespace fuxicore