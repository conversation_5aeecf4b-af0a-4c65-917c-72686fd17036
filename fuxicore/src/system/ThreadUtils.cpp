#include "system/ThreadUtils.h"
#include <algorithm>
#include <iostream>

// 静态成员变量定义
std::thread::id ThreadUtils::s_mainThreadId;
std::once_flag ThreadUtils::s_mainThreadIdFlag;
std::unique_ptr<ThreadUtils::ThreadPool> ThreadUtils::s_globalThreadPool;
std::once_flag ThreadUtils::s_globalThreadPoolFlag;

// Task 实现
ThreadUtils::Task::Task(std::function<void()> func, Priority priority)
    : m_function(std::move(func)), m_priority(priority), m_creationTime(std::chrono::steady_clock::now())
{
}

void ThreadUtils::Task::execute()
{
    if (m_function) {
        try {
            m_function();
        } catch (const std::exception& e) {
            std::cerr << "Task execution failed: " << e.what() << std::endl;
        } catch (...) {
            std::cerr << "Task execution failed with unknown exception" << std::endl;
        }
    }
}

ThreadUtils::Priority ThreadUtils::Task::getPriority() const
{
    return m_priority;
}

std::chrono::steady_clock::time_point ThreadUtils::Task::getCreationTime() const
{
    return m_creationTime;
}

// AtomicCounter 实现
ThreadUtils::AtomicCounter::AtomicCounter(int initialValue)
    : m_value(initialValue)
{
}

int ThreadUtils::AtomicCounter::increment()
{
    return ++m_value;
}

int ThreadUtils::AtomicCounter::decrement()
{
    return --m_value;
}

int ThreadUtils::AtomicCounter::get() const
{
    return m_value.load();
}

void ThreadUtils::AtomicCounter::set(int value)
{
    m_value.store(value);
}

bool ThreadUtils::AtomicCounter::compareAndSwap(int expected, int newValue)
{
    return m_value.compare_exchange_strong(expected, newValue);
}

// ThreadPool 实现
ThreadUtils::ThreadPool::ThreadPool(size_t numThreads)
    : m_maxThreads(numThreads == 0 ? std::thread::hardware_concurrency() : numThreads)
{
    for (size_t i = 0; i < m_maxThreads; ++i) {
        m_workers.emplace_back(&ThreadPool::workerThread, this);
    }
}

ThreadUtils::ThreadPool::~ThreadPool()
{
    shutdown();
}

void ThreadUtils::ThreadPool::setMaxThreadCount(size_t maxCount)
{
    if (maxCount == 0) {
        maxCount = std::thread::hardware_concurrency();
    }

    std::lock_guard<std::mutex> lock(m_queueMutex);
    if (maxCount != m_maxThreads.load()) {
        m_maxThreads.store(maxCount);
        resizeThreadPool(maxCount);
    }
}

size_t ThreadUtils::ThreadPool::maxThreadCount() const
{
    return m_maxThreads.load();
}

size_t ThreadUtils::ThreadPool::activeThreadCount() const
{
    return m_activeTasks.load();
}

size_t ThreadUtils::ThreadPool::queuedTaskCount() const
{
    std::lock_guard<std::mutex> lock(m_queueMutex);
    return m_taskQueue.size();
}

void ThreadUtils::ThreadPool::submitTask(std::function<void()> task, Priority priority)
{
    if (m_shutdown.load()) {
        return;
    }

    auto taskPtr = std::make_shared<Task>(std::move(task), priority);

    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        m_taskQueue.push({taskPtr});
    }

    m_condition.notify_one();
}

void ThreadUtils::ThreadPool::waitForAllTasks()
{
    std::unique_lock<std::mutex> lock(m_queueMutex);
    m_condition.wait(lock, [this] {
        return m_taskQueue.empty() && m_activeTasks.load() == 0;
    });
}

void ThreadUtils::ThreadPool::shutdown()
{
    if (m_shutdown.exchange(true)) {
        return; // 已经关闭
    }

    m_condition.notify_all();

    for (auto& worker : m_workers) {
        if (worker.joinable()) {
            worker.join();
        }
    }

    m_workers.clear();
}

bool ThreadUtils::ThreadPool::isShutdown() const
{
    return m_shutdown.load();
}

void ThreadUtils::ThreadPool::workerThread()
{
    while (!m_shutdown.load()) {
        TaskWrapper taskWrapper;

        {
            std::unique_lock<std::mutex> lock(m_queueMutex);
            m_condition.wait(lock, [this] {
                return !m_taskQueue.empty() || m_shutdown.load();
            });

            if (m_shutdown.load() && m_taskQueue.empty()) {
                break;
            }

            if (!m_taskQueue.empty()) {
                taskWrapper = m_taskQueue.top();
                m_taskQueue.pop();
            } else {
                continue;
            }
        }

        if (taskWrapper.task) {
            m_activeTasks.fetch_add(1);
            try {
                taskWrapper.task->execute();
            } catch (...) {
                // 异常已在 Task::execute 中处理
            }
            m_activeTasks.fetch_sub(1);
            m_condition.notify_all(); // 通知等待的线程
        }
    }
}

void ThreadUtils::ThreadPool::resizeThreadPool(size_t newSize)
{
    // 简化实现：不支持动态调整线程池大小
    // 在实际应用中，这需要更复杂的逻辑来安全地添加/移除线程
}

// TimerManager 实现
ThreadUtils::TimerManager& ThreadUtils::TimerManager::instance()
{
    static TimerManager instance;
    return instance;
}

ThreadUtils::TimerManager::TimerManager()
    : m_timerThread(&TimerManager::timerWorker, this)
{
}

ThreadUtils::TimerManager::~TimerManager()
{
    shutdown();
}

ThreadUtils::TimerManager::TimerId ThreadUtils::TimerManager::startTimer(
    const std::chrono::milliseconds& interval,
    std::function<void()> callback,
    bool singleShot)
{
    if (m_shutdown.load()) {
        return 0;
    }

    TimerId id = m_nextTimerId.fetch_add(1);
    auto now = std::chrono::steady_clock::now();

    TimerInfo info;
    info.id = id;
    info.nextExecution = now + interval;
    info.interval = interval;
    info.callback = std::move(callback);
    info.singleShot = singleShot;
    info.active = true;

    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_timers[id] = std::move(info);
    }

    m_condition.notify_one();
    return id;
}

void ThreadUtils::TimerManager::stopTimer(TimerId timerId)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    auto it = m_timers.find(timerId);
    if (it != m_timers.end()) {
        it->second.active = false;
        m_timers.erase(it);
    }
}

void ThreadUtils::TimerManager::stopAllTimers()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_timers.clear();
}

void ThreadUtils::TimerManager::shutdown()
{
    if (m_shutdown.exchange(true)) {
        return; // 已经关闭
    }

    m_condition.notify_all();

    if (m_timerThread.joinable()) {
        m_timerThread.join();
    }
}

ThreadUtils::TimerManager::TimerId ThreadUtils::TimerManager::startDelayedTask(
    const std::chrono::milliseconds& delay,
    std::function<void()> task)
{
    return startTimer(delay, std::move(task), true);
}

void ThreadUtils::TimerManager::timerWorker()
{
    while (!m_shutdown.load()) {
        std::unique_lock<std::mutex> lock(m_mutex);

        auto now = std::chrono::steady_clock::now();
        auto nextWakeup = now + std::chrono::hours(1); // 默认1小时后唤醒

        // 查找需要执行的定时器
        std::vector<std::function<void()>> readyCallbacks;
        std::vector<TimerId> toRemove;

        for (auto& pair : m_timers) {
            auto& timer = pair.second;
            if (!timer.active) continue;

            if (timer.nextExecution <= now) {
                // 定时器到期
                readyCallbacks.push_back(timer.callback);

                if (timer.singleShot) {
                    toRemove.push_back(timer.id);
                } else {
                    timer.nextExecution = now + timer.interval;
                    nextWakeup = std::min(nextWakeup, timer.nextExecution);
                }
            } else {
                nextWakeup = std::min(nextWakeup, timer.nextExecution);
            }
        }

        // 移除单次定时器
        for (TimerId id : toRemove) {
            m_timers.erase(id);
        }

        lock.unlock();

        // 执行回调函数
        for (auto& callback : readyCallbacks) {
            try {
                callback();
            } catch (const std::exception& e) {
                std::cerr << "Timer callback failed: " << e.what() << std::endl;
            } catch (...) {
                std::cerr << "Timer callback failed with unknown exception" << std::endl;
            }
        }

        // 等待下次唤醒
        lock.lock();
        if (!m_shutdown.load()) {
            m_condition.wait_until(lock, nextWakeup);
        }
    }
}

// ReadWriteLock 实现
void ThreadUtils::ReadWriteLock::lockForRead()
{
    m_mutex.lock_shared();
}

void ThreadUtils::ReadWriteLock::lockForWrite()
{
    m_mutex.lock();
}

bool ThreadUtils::ReadWriteLock::tryLockForRead()
{
    return m_mutex.try_lock_shared();
}

bool ThreadUtils::ReadWriteLock::tryLockForWrite()
{
    return m_mutex.try_lock();
}

void ThreadUtils::ReadWriteLock::unlockRead()
{
    m_mutex.unlock_shared();
}

void ThreadUtils::ReadWriteLock::unlockWrite()
{
    m_mutex.unlock();
}

// ReadLocker 实现
ThreadUtils::ReadLocker::ReadLocker(ReadWriteLock& lock)
    : m_lock(lock.m_mutex)
{
}

ThreadUtils::ReadLocker::ReadLocker(std::shared_mutex& mutex)
    : m_lock(mutex)
{
}

ThreadUtils::ReadLocker::~ReadLocker() = default;

// WriteLocker 实现
ThreadUtils::WriteLocker::WriteLocker(ReadWriteLock& lock)
    : m_lock(lock.m_mutex)
{
}

ThreadUtils::WriteLocker::WriteLocker(std::shared_mutex& mutex)
    : m_lock(mutex)
{
}

ThreadUtils::WriteLocker::~WriteLocker() = default;

// 静态工具函数实现
std::thread::id ThreadUtils::currentThreadId()
{
    return std::this_thread::get_id();
}

std::thread::id ThreadUtils::mainThreadId()
{
    std::call_once(s_mainThreadIdFlag, []() {
        s_mainThreadId = std::this_thread::get_id();
    });
    return s_mainThreadId;
}

bool ThreadUtils::isMainThread()
{
    return currentThreadId() == mainThreadId();
}

void ThreadUtils::yield()
{
    std::this_thread::yield();
}

unsigned int ThreadUtils::hardwareConcurrency()
{
    return std::thread::hardware_concurrency();
}

void ThreadUtils::runInBackground(std::function<void()> task, Priority priority)
{
    getGlobalThreadPool().submitTask(std::move(task), priority);
}

void ThreadUtils::runParallel(const std::vector<std::function<void()>>& tasks, size_t maxConcurrency)
{
    if (tasks.empty()) return;

    if (maxConcurrency == 0) {
        maxConcurrency = std::thread::hardware_concurrency();
    }

    // 创建临时线程池来执行并行任务
    ThreadPool tempPool(std::min(maxConcurrency, tasks.size()));

    // 提交所有任务
    std::vector<std::future<void>> futures;
    futures.reserve(tasks.size());

    for (const auto& task : tasks) {
        auto future = tempPool.submitTaskWithResult([task]() -> void {
            try {
                task();
            } catch (const std::exception& e) {
                std::cerr << "Parallel task failed: " << e.what() << std::endl;
            } catch (...) {
                std::cerr << "Parallel task failed with unknown exception" << std::endl;
            }
        });
        futures.push_back(std::move(future));
    }

    // 等待所有任务完成
    for (auto& future : futures) {
        try {
            future.get();
        } catch (...) {
            // 异常已在任务中处理
        }
    }
}

ThreadUtils::ThreadPool& ThreadUtils::getGlobalThreadPool()
{
    std::call_once(s_globalThreadPoolFlag, []() {
        s_globalThreadPool = std::make_unique<ThreadPool>();
    });
    return *s_globalThreadPool;
}
