#include "system/SystemMonitor.h"
#include <QJsonDocument>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QProcess>
#include <QStorageInfo>
#include <QSysInfo>
#include <QUuid>
#include <QDebug>
#include <QMutexLocker>

#ifdef Q_OS_WIN
#include <windows.h>
#include <psapi.h>
// Note: PDH functions would be used in production
// #include <pdh.h>
// #pragma comment(lib, "pdh.lib")
#endif

namespace Fuxi {
namespace Core {

class SystemMonitor::SystemMonitorPrivate
{
public:
    SystemMonitorPrivate(SystemMonitor* q) : q_ptr(q) {
        monitoringTimer = new QTimer(q);
        monitoringTimer->setInterval(5000); // 5 seconds default
        QObject::connect(monitoringTimer, &QTimer::timeout, q, &SystemMonitor::collectMetrics);
        
        healthCheckTimer = new QTimer(q);
        healthCheckTimer->setInterval(60000); // 1 minute
        QObject::connect(healthCheckTimer, &QTimer::timeout, q, &SystemMonitor::runScheduledHealthChecks);
        
        alertEvaluationTimer = new QTimer(q);
        alertEvaluationTimer->setInterval(10000); // 10 seconds
        QObject::connect(alertEvaluationTimer, &QTimer::timeout, q, &SystemMonitor::evaluateAlertRules);
        
        cleanupTimer = new QTimer(q);
        cleanupTimer->setInterval(3600000); // 1 hour
        QObject::connect(cleanupTimer, &QTimer::timeout, q, &SystemMonitor::cleanupOldData);
        
        // Default configuration
        metricsRetentionDays = 7;
        alertsRetentionDays = 30;
        isMonitoringActive = false;
        currentStatus = SystemStatus::Unknown;
        
        // Initialize performance counters on Windows
#ifdef Q_OS_WIN
        initializePerformanceCounters();
#endif
    }
    
    ~SystemMonitorPrivate() {
#ifdef Q_OS_WIN
        cleanupPerformanceCounters();
#endif
    }
    
    SystemMonitor* q_ptr;
    QTimer* monitoringTimer;
    QTimer* healthCheckTimer;
    QTimer* alertEvaluationTimer;
    QTimer* cleanupTimer;
    
    // Data storage
    QList<SystemMetrics> metricsHistory;
    QList<HealthCheck> healthChecks;
    QList<AlertRule> alertRules;
    QList<SystemAlert> alerts;
    QHash<QString, double> customMetrics;
    
    // Configuration
    int metricsRetentionDays;
    int alertsRetentionDays;
    bool isMonitoringActive;
    SystemStatus currentStatus;
    QDateTime lastUpdateTime;
    
    // Thread safety
    mutable QMutex dataMutex;
    
#ifdef Q_OS_WIN
    // Windows performance counters (placeholder)
    // PDH_HQUERY cpuQuery;
    // PDH_HCOUNTER cpuCounter;
    bool perfCountersInitialized;

    void initializePerformanceCounters();
    void cleanupPerformanceCounters();
#endif
    
    // Helper methods
    SystemMetrics collectCurrentMetrics() const;
    double getCpuUsageWindows() const;
    double getCpuUsageLinux() const;
    void getMemoryInfoWindows(qint64& used, qint64& total) const;
    void getMemoryInfoLinux(qint64& used, qint64& total) const;
    SystemStatus calculateOverallStatus() const;
    QString generateAlertId() const;
};

SystemMonitor::SystemMonitor(QObject *parent)
    : QObject(parent), d_ptr(std::make_unique<SystemMonitorPrivate>(this))
{
}

SystemMonitor::~SystemMonitor() = default;

bool SystemMonitor::SystemMetrics::isValid() const
{
    return timestamp.isValid() && 
           cpuUsage >= 0 && cpuUsage <= 100 &&
           memoryTotal > 0 && memoryUsage >= 0 && memoryUsage <= 100 &&
           diskTotal > 0 && diskUsage >= 0 && diskUsage <= 100;
}

QJsonObject SystemMonitor::SystemMetrics::toJson() const
{
    QJsonObject obj;
    obj["timestamp"] = timestamp.toString(Qt::ISODate);
    obj["cpuUsage"] = cpuUsage;
    obj["memoryUsed"] = static_cast<qint64>(memoryUsed);
    obj["memoryTotal"] = static_cast<qint64>(memoryTotal);
    obj["memoryUsage"] = memoryUsage;
    obj["diskUsed"] = static_cast<qint64>(diskUsed);
    obj["diskTotal"] = static_cast<qint64>(diskTotal);
    obj["diskUsage"] = diskUsage;
    obj["networkBytesIn"] = static_cast<qint64>(networkBytesIn);
    obj["networkBytesOut"] = static_cast<qint64>(networkBytesOut);
    obj["temperature"] = temperature;
    return obj;
}

bool SystemMonitor::HealthCheck::isValid() const
{
    return !id.isEmpty() && !name.isEmpty() && intervalMs > 0 && timeoutMs > 0;
}

bool SystemMonitor::AlertRule::evaluate(double value) const
{
    if (condition == "greater") {
        return value > threshold;
    } else if (condition == "less") {
        return value < threshold;
    } else if (condition == "equal") {
        return qAbs(value - threshold) < 0.001; // Float comparison
    }
    return false;
}

bool SystemMonitor::AlertRule::isValid() const
{
    return !id.isEmpty() && !name.isEmpty() && 
           (condition == "greater" || condition == "less" || condition == "equal") &&
           duration > 0;
}

QJsonObject SystemMonitor::SystemAlert::toJson() const
{
    QJsonObject obj;
    obj["id"] = id;
    obj["ruleId"] = ruleId;
    obj["message"] = message;
    obj["severity"] = static_cast<int>(severity);
    obj["timestamp"] = timestamp.toString(Qt::ISODate);
    obj["acknowledged"] = acknowledged;
    if (acknowledgedTime.isValid()) {
        obj["acknowledgedTime"] = acknowledgedTime.toString(Qt::ISODate);
    }
    obj["context"] = context;
    return obj;
}

void SystemMonitor::startMonitoring()
{
    QMutexLocker locker(&d_ptr->dataMutex);
    if (!d_ptr->isMonitoringActive) {
        d_ptr->isMonitoringActive = true;
        d_ptr->monitoringTimer->start();
        d_ptr->healthCheckTimer->start();
        d_ptr->alertEvaluationTimer->start();
        d_ptr->cleanupTimer->start();
        
        // Collect initial metrics
        locker.unlock();
        collectMetrics();
    }
}

void SystemMonitor::stopMonitoring()
{
    QMutexLocker locker(&d_ptr->dataMutex);
    if (d_ptr->isMonitoringActive) {
        d_ptr->isMonitoringActive = false;
        d_ptr->monitoringTimer->stop();
        d_ptr->healthCheckTimer->stop();
        d_ptr->alertEvaluationTimer->stop();
        d_ptr->cleanupTimer->stop();
    }
}

bool SystemMonitor::isMonitoring() const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    return d_ptr->isMonitoringActive;
}

void SystemMonitor::setMonitoringInterval(int intervalMs)
{
    QMutexLocker locker(&d_ptr->dataMutex);
    d_ptr->monitoringTimer->setInterval(intervalMs);
}

int SystemMonitor::getMonitoringInterval() const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    return d_ptr->monitoringTimer->interval();
}

SystemMonitor::SystemMetrics SystemMonitor::getCurrentMetrics() const
{
    return d_ptr->collectCurrentMetrics();
}

QList<SystemMonitor::SystemMetrics> SystemMonitor::getMetricsHistory(int count) const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    QList<SystemMetrics> result = d_ptr->metricsHistory;
    
    if (result.size() > count) {
        result = result.mid(result.size() - count);
    }
    
    return result;
}

void SystemMonitor::addCustomMetric(const QString& name, double value)
{
    QMutexLocker locker(&d_ptr->dataMutex);
    d_ptr->customMetrics[name] = value;
    emit customMetricAdded(name, value);
}

double SystemMonitor::getCustomMetric(const QString& name) const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    return d_ptr->customMetrics.value(name, 0.0);
}

QStringList SystemMonitor::getCustomMetricNames() const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    return d_ptr->customMetrics.keys();
}

void SystemMonitor::addHealthCheck(const HealthCheck& check)
{
    if (!check.isValid()) {
        return;
    }
    
    QMutexLocker locker(&d_ptr->dataMutex);
    d_ptr->healthChecks.append(check);
}

void SystemMonitor::addAlertRule(const AlertRule& rule)
{
    if (!rule.isValid()) {
        return;
    }
    
    QMutexLocker locker(&d_ptr->dataMutex);
    d_ptr->alertRules.append(rule);
}

SystemMonitor::SystemStatus SystemMonitor::getSystemStatus() const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    return d_ptr->currentStatus;
}

QString SystemMonitor::getSystemStatusText() const
{
    SystemStatus status = getSystemStatus();
    switch (status) {
        case SystemStatus::Healthy: return "Healthy";
        case SystemStatus::Warning: return "Warning";
        case SystemStatus::Critical: return "Critical";
        case SystemStatus::Failed: return "Failed";
        case SystemStatus::Unknown: return "Unknown";
        default: return "Unknown";
    }
}

QDateTime SystemMonitor::getLastUpdateTime() const
{
    QMutexLocker locker(&d_ptr->dataMutex);
    return d_ptr->lastUpdateTime;
}

QString SystemMonitor::getSystemInfo() const
{
    QString info;
    info += QString("OS: %1 %2\n").arg(QSysInfo::productType()).arg(QSysInfo::productVersion());
    info += QString("Kernel: %1 %2\n").arg(QSysInfo::kernelType()).arg(QSysInfo::kernelVersion());
    info += QString("CPU Architecture: %1\n").arg(QSysInfo::currentCpuArchitecture());
    info += QString("Machine: %1\n").arg(QSysInfo::machineHostName());
    return info;
}

qint64 SystemMonitor::getTotalMemory() const
{
    qint64 used, total;
#ifdef Q_OS_WIN
    d_ptr->getMemoryInfoWindows(used, total);
#else
    d_ptr->getMemoryInfoLinux(used, total);
#endif
    return total;
}

void SystemMonitor::collectMetrics()
{
    SystemMetrics metrics = d_ptr->collectCurrentMetrics();
    
    QMutexLocker locker(&d_ptr->dataMutex);
    d_ptr->metricsHistory.append(metrics);
    d_ptr->lastUpdateTime = metrics.timestamp;
    
    // Keep only recent metrics (based on retention policy)
    QDateTime cutoffTime = QDateTime::currentDateTime().addDays(-d_ptr->metricsRetentionDays);
    d_ptr->metricsHistory.erase(
        std::remove_if(d_ptr->metricsHistory.begin(), d_ptr->metricsHistory.end(),
                      [cutoffTime](const SystemMetrics& m) { return m.timestamp < cutoffTime; }),
        d_ptr->metricsHistory.end());
    
    // Update system status
    SystemStatus oldStatus = d_ptr->currentStatus;
    d_ptr->currentStatus = d_ptr->calculateOverallStatus();
    
    locker.unlock();
    
    emit metricsUpdated(metrics);
    
    if (oldStatus != d_ptr->currentStatus) {
        emit systemStatusChanged(oldStatus, d_ptr->currentStatus);
    }
}

void SystemMonitor::runScheduledHealthChecks()
{
    QMutexLocker locker(&d_ptr->dataMutex);
    QList<HealthCheck> checksToRun = d_ptr->healthChecks;
    locker.unlock();
    
    for (const HealthCheck& check : checksToRun) {
        if (check.enabled && check.checkFunction) {
            try {
                bool result = check.checkFunction();
                if (!result) {
                    emit healthCheckFailed(check.id, "Health check failed");
                }
            } catch (...) {
                emit healthCheckFailed(check.id, "Health check threw exception");
            }
        }
    }
}

void SystemMonitor::evaluateAlertRules()
{
    SystemMetrics currentMetrics = getCurrentMetrics();
    
    QMutexLocker locker(&d_ptr->dataMutex);
    QList<AlertRule> rulesToEvaluate = d_ptr->alertRules;
    locker.unlock();
    
    for (const AlertRule& rule : rulesToEvaluate) {
        if (!rule.enabled) continue;
        
        double value = 0.0;
        switch (rule.metricType) {
            case MetricType::CPU:
                value = currentMetrics.cpuUsage;
                break;
            case MetricType::Memory:
                value = currentMetrics.memoryUsage;
                break;
            case MetricType::Disk:
                value = currentMetrics.diskUsage;
                break;
            case MetricType::Temperature:
                value = currentMetrics.temperature;
                break;
            case MetricType::Custom:
                value = getCustomMetric(rule.metricName);
                break;
            default:
                continue;
        }
        
        if (rule.evaluate(value)) {
            // Create alert
            SystemAlert alert;
            alert.id = d_ptr->generateAlertId();
            alert.ruleId = rule.id;
            alert.message = QString("Alert: %1 - Value: %2, Threshold: %3")
                           .arg(rule.name).arg(value).arg(rule.threshold);
            alert.severity = SystemStatus::Warning; // Default severity
            alert.timestamp = QDateTime::currentDateTime();
            alert.acknowledged = false;
            
            QMutexLocker alertLocker(&d_ptr->dataMutex);
            d_ptr->alerts.append(alert);
            alertLocker.unlock();
            
            emit alertTriggered(alert);
        }
    }
}

void SystemMonitor::cleanupOldData()
{
    QMutexLocker locker(&d_ptr->dataMutex);
    
    // Clean up old metrics
    QDateTime metricsCutoff = QDateTime::currentDateTime().addDays(-d_ptr->metricsRetentionDays);
    d_ptr->metricsHistory.erase(
        std::remove_if(d_ptr->metricsHistory.begin(), d_ptr->metricsHistory.end(),
                      [metricsCutoff](const SystemMetrics& m) { return m.timestamp < metricsCutoff; }),
        d_ptr->metricsHistory.end());
    
    // Clean up old alerts
    QDateTime alertsCutoff = QDateTime::currentDateTime().addDays(-d_ptr->alertsRetentionDays);
    d_ptr->alerts.erase(
        std::remove_if(d_ptr->alerts.begin(), d_ptr->alerts.end(),
                      [alertsCutoff](const SystemAlert& a) { return a.timestamp < alertsCutoff; }),
        d_ptr->alerts.end());
}

// Private helper methods implementation
SystemMonitor::SystemMetrics SystemMonitor::SystemMonitorPrivate::collectCurrentMetrics() const
{
    SystemMetrics metrics;
    metrics.timestamp = QDateTime::currentDateTime();

    // Collect CPU usage
#ifdef Q_OS_WIN
    metrics.cpuUsage = getCpuUsageWindows();
#else
    metrics.cpuUsage = getCpuUsageLinux();
#endif

    // Collect memory information
#ifdef Q_OS_WIN
    getMemoryInfoWindows(metrics.memoryUsed, metrics.memoryTotal);
#else
    getMemoryInfoLinux(metrics.memoryUsed, metrics.memoryTotal);
#endif

    if (metrics.memoryTotal > 0) {
        metrics.memoryUsage = (double)metrics.memoryUsed / metrics.memoryTotal * 100.0;
    }

    // Collect disk information (simplified - using root volume)
    QStorageInfo storage = QStorageInfo::root();
    if (storage.isValid()) {
        metrics.diskTotal = storage.bytesTotal();
        metrics.diskUsed = metrics.diskTotal - storage.bytesAvailable();
        if (metrics.diskTotal > 0) {
            metrics.diskUsage = (double)metrics.diskUsed / metrics.diskTotal * 100.0;
        }
    }

    // Network and temperature would require platform-specific implementations
    metrics.networkBytesIn = 0;
    metrics.networkBytesOut = 0;
    metrics.temperature = 0.0;

    return metrics;
}

#ifdef Q_OS_WIN
void SystemMonitor::SystemMonitorPrivate::initializePerformanceCounters()
{
    perfCountersInitialized = false;

    // In production, initialize PDH performance counters
    // if (PdhOpenQuery(NULL, NULL, &cpuQuery) == ERROR_SUCCESS) {
    //     if (PdhAddEnglishCounterA(cpuQuery, "\\Processor(_Total)\\% Processor Time", NULL, &cpuCounter) == ERROR_SUCCESS) {
    //         PdhCollectQueryData(cpuQuery);
    //         perfCountersInitialized = true;
    //     }
    // }
}

void SystemMonitor::SystemMonitorPrivate::cleanupPerformanceCounters()
{
    if (perfCountersInitialized) {
        // In production, cleanup PDH
        // PdhCloseQuery(cpuQuery);
    }
}

double SystemMonitor::SystemMonitorPrivate::getCpuUsageWindows() const
{
    if (!perfCountersInitialized) {
        // Return simulated CPU usage for demo
        return 25.0 + (rand() % 50); // Random value between 25-75%
    }

    // In production, get actual CPU usage
    // PDH_FMT_COUNTERVALUE counterVal;
    // PdhCollectQueryData(cpuQuery);
    // PdhGetFormattedCounterValue(cpuCounter, PDH_FMT_DOUBLE, NULL, &counterVal);
    // return counterVal.doubleValue;

    return 0.0;
}

void SystemMonitor::SystemMonitorPrivate::getMemoryInfoWindows(qint64& used, qint64& total) const
{
    MEMORYSTATUSEX memInfo;
    memInfo.dwLength = sizeof(MEMORYSTATUSEX);
    GlobalMemoryStatusEx(&memInfo);

    total = memInfo.ullTotalPhys;
    used = total - memInfo.ullAvailPhys;
}

#else

double SystemMonitor::SystemMonitorPrivate::getCpuUsageLinux() const
{
    // Simplified CPU usage calculation for Linux
    // In production, implement proper /proc/stat parsing
    return 0.0;
}

void SystemMonitor::SystemMonitorPrivate::getMemoryInfoLinux(qint64& used, qint64& total) const
{
    // Simplified memory info for Linux
    // In production, parse /proc/meminfo
    used = 0;
    total = 0;
}

#endif

SystemMonitor::SystemStatus SystemMonitor::SystemMonitorPrivate::calculateOverallStatus() const
{
    if (metricsHistory.isEmpty()) {
        return SystemStatus::Unknown;
    }

    const SystemMetrics& latest = metricsHistory.last();

    // Simple status calculation based on thresholds
    if (latest.cpuUsage > 90 || latest.memoryUsage > 90 || latest.diskUsage > 95) {
        return SystemStatus::Critical;
    } else if (latest.cpuUsage > 80 || latest.memoryUsage > 80 || latest.diskUsage > 90) {
        return SystemStatus::Warning;
    } else {
        return SystemStatus::Healthy;
    }
}

QString SystemMonitor::SystemMonitorPrivate::generateAlertId() const
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

} // namespace Core
} // namespace Fuxi
