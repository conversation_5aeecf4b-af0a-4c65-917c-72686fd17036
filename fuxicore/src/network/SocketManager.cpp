#include "network/SocketManager.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <chrono>
#include <cstring>

namespace fuxicore {

    // 静态成员初始化
    bool SocketManager::wsa_initialized_ = false;
    std::mutex SocketManager::wsa_mutex_;
    int SocketManager::wsa_ref_count_ = 0;

    bool SocketServer::wsa_initialized_ = false;
    std::mutex SocketServer::wsa_mutex_;
    int SocketServer::wsa_ref_count_ = 0;

    // SocketManager 实现
    SocketManager::SocketManager(const SocketConfig& config)
        : socket_(INVALID_SOCKET), config_(config), state_(SocketState::Disconnected), should_stop_(false) {
        initialize_wsa();
    }

    SocketManager::~SocketManager() {
        disconnect();
        cleanup_wsa();
    }

    SocketManager::SocketManager(SocketManager&& other) noexcept
        : socket_(other.socket_), config_(std::move(other.config_)), 
          state_(other.state_.load()), should_stop_(other.should_stop_.load()),
          worker_thread_(std::move(other.worker_thread_)),
          on_data_received_(std::move(other.on_data_received_)),
          on_error_(std::move(other.on_error_)),
          on_connected_(std::move(other.on_connected_)),
          on_disconnected_(std::move(other.on_disconnected_)) {
        other.socket_ = INVALID_SOCKET;
        other.state_ = SocketState::Disconnected;
        other.should_stop_ = true;
    }

    SocketManager& SocketManager::operator=(SocketManager&& other) noexcept {
        if (this != &other) {
            disconnect();
            
            socket_ = other.socket_;
            config_ = std::move(other.config_);
            state_ = other.state_.load();
            should_stop_ = other.should_stop_.load();
            worker_thread_ = std::move(other.worker_thread_);
            on_data_received_ = std::move(other.on_data_received_);
            on_error_ = std::move(other.on_error_);
            on_connected_ = std::move(other.on_connected_);
            on_disconnected_ = std::move(other.on_disconnected_);
            
            other.socket_ = INVALID_SOCKET;
            other.state_ = SocketState::Disconnected;
            other.should_stop_ = true;
        }
        return *this;
    }

    void SocketManager::initialize_wsa() {
        std::lock_guard<std::mutex> lock(wsa_mutex_);
        if (!wsa_initialized_) {
            WSADATA wsaData;
            int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
            if (result != 0) {
                throw SocketException("Failed to initialize Winsock", result);
            }
            wsa_initialized_ = true;
        }
        wsa_ref_count_++;
    }

    void SocketManager::cleanup_wsa() {
        std::lock_guard<std::mutex> lock(wsa_mutex_);
        wsa_ref_count_--;
        if (wsa_ref_count_ <= 0 && wsa_initialized_) {
            WSACleanup();
            wsa_initialized_ = false;
            wsa_ref_count_ = 0;
        }
    }

    void SocketManager::set_config(const SocketConfig& config) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (state_ != SocketState::Disconnected) {
            throw SocketException("Cannot change configuration while connected");
        }
        config_ = config;
    }

    const SocketConfig& SocketManager::get_config() const noexcept {
        return config_;
    }

    SocketState SocketManager::get_state() const noexcept {
        return state_.load();
    }

    void SocketManager::set_data_received_callback(std::function<void(const std::vector<uint8_t>&)> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_data_received_ = std::move(callback);
    }

    void SocketManager::set_error_callback(std::function<void(const std::string&)> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_error_ = std::move(callback);
    }

    void SocketManager::set_connected_callback(std::function<void()> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_connected_ = std::move(callback);
    }

    void SocketManager::set_disconnected_callback(std::function<void()> callback) {
        std::lock_guard<std::mutex> lock(mutex_);
        on_disconnected_ = std::move(callback);
    }

    std::future<bool> SocketManager::connect_async() {
        return std::async(std::launch::async, [this]() {
            return connect();
        });
    }

    bool SocketManager::connect(int timeout_ms) {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (state_ != SocketState::Disconnected) {
            return false;
        }

        state_ = SocketState::Connecting;
        
        try {
            // 创建socket
            int socket_type = (config_.type == SocketType::TCP) ? SOCK_STREAM : SOCK_DGRAM;
            int protocol = (config_.type == SocketType::TCP) ? IPPROTO_TCP : IPPROTO_UDP;
            
            socket_ = socket(AF_INET, socket_type, protocol);
            if (socket_ == INVALID_SOCKET) {
                throw SocketException("Failed to create socket", get_last_socket_error());
            }

            // 设置socket选项
            if (config_.reuse_address) {
                int reuse = 1;
                setsockopt(socket_, SOL_SOCKET, SO_REUSEADDR, reinterpret_cast<const char*>(&reuse), sizeof(reuse));
            }

            // 设置超时
            int timeout = (timeout_ms > 0) ? timeout_ms : config_.timeout_ms;
            if (timeout > 0) {
                DWORD timeout_dw = static_cast<DWORD>(timeout);
                setsockopt(socket_, SOL_SOCKET, SO_RCVTIMEO, reinterpret_cast<const char*>(&timeout_dw), sizeof(timeout_dw));
                setsockopt(socket_, SOL_SOCKET, SO_SNDTIMEO, reinterpret_cast<const char*>(&timeout_dw), sizeof(timeout_dw));
            }

            // 连接到服务器
            sockaddr_in server_addr{};
            server_addr.sin_family = AF_INET;
            server_addr.sin_port = htons(config_.port);
            
            if (inet_pton(AF_INET, config_.address.c_str(), &server_addr.sin_addr) <= 0) {
                throw SocketException("Invalid address format");
            }

            if (config_.type == SocketType::TCP) {
                if (::connect(socket_, reinterpret_cast<sockaddr*>(&server_addr), sizeof(server_addr)) == SOCKET_ERROR) {
                    int error = get_last_socket_error();
                    closesocket(socket_);
                    socket_ = INVALID_SOCKET;
                    throw SocketException("Failed to connect", error);
                }
            }

            state_ = SocketState::Connected;
            should_stop_ = false;
            
            // 启动工作线程
            worker_thread_ = std::thread(&SocketManager::worker_loop, this);
            
            if (on_connected_) {
                on_connected_();
            }
            
            return true;
        }
        catch (const SocketException& e) {
            state_ = SocketState::Error;
            if (on_error_) {
                on_error_(e.what());
            }
            return false;
        }
    }

    void SocketManager::disconnect() {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (state_ == SocketState::Disconnected) {
                return;
            }
            
            should_stop_ = true;
            state_ = SocketState::Disconnected;
        }
        
        cv_.notify_all();
        
        if (worker_thread_.joinable()) {
            worker_thread_.join();
        }
        
        if (socket_ != INVALID_SOCKET) {
            closesocket(socket_);
            socket_ = INVALID_SOCKET;
        }
        
        if (on_disconnected_) {
            on_disconnected_();
        }
    }

    bool SocketManager::start_server() {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (state_ != SocketState::Disconnected) {
            return false;
        }

        try {
            // 创建监听socket
            int socket_type = (config_.type == SocketType::TCP) ? SOCK_STREAM : SOCK_DGRAM;
            int protocol = (config_.type == SocketType::TCP) ? IPPROTO_TCP : IPPROTO_UDP;
            
            socket_ = socket(AF_INET, socket_type, protocol);
            if (socket_ == INVALID_SOCKET) {
                throw SocketException("Failed to create socket", get_last_socket_error());
            }

            // 设置socket选项
            if (config_.reuse_address) {
                int reuse = 1;
                setsockopt(socket_, SOL_SOCKET, SO_REUSEADDR, reinterpret_cast<const char*>(&reuse), sizeof(reuse));
            }

            // 绑定地址
            sockaddr_in server_addr{};
            server_addr.sin_family = AF_INET;
            server_addr.sin_port = htons(config_.port);
            
            if (config_.address == "0.0.0.0" || config_.address.empty()) {
                server_addr.sin_addr.s_addr = INADDR_ANY;
            } else {
                if (inet_pton(AF_INET, config_.address.c_str(), &server_addr.sin_addr) <= 0) {
                    throw SocketException("Invalid address format");
                }
            }

            if (bind(socket_, reinterpret_cast<sockaddr*>(&server_addr), sizeof(server_addr)) == SOCKET_ERROR) {
                throw SocketException("Failed to bind socket", get_last_socket_error());
            }

            // TCP需要监听
            if (config_.type == SocketType::TCP) {
                if (listen(socket_, SOMAXCONN) == SOCKET_ERROR) {
                    throw SocketException("Failed to listen", get_last_socket_error());
                }
            }

            state_ = SocketState::Listening;
            should_stop_ = false;
            
            // 启动工作线程
            worker_thread_ = std::thread(&SocketManager::worker_loop, this);
            
            return true;
        }
        catch (const SocketException& e) {
            state_ = SocketState::Error;
            if (socket_ != INVALID_SOCKET) {
                closesocket(socket_);
                socket_ = INVALID_SOCKET;
            }
            if (on_error_) {
                on_error_(e.what());
            }
            return false;
        }
    }

    void SocketManager::stop_server() {
        disconnect();
    }

    bool SocketManager::is_listening() const noexcept {
        return state_.load() == SocketState::Listening;
    }

    bool SocketManager::is_connected() const noexcept {
        return state_.load() == SocketState::Connected;
    }

    std::vector<uint8_t> SocketManager::receive_data(size_t max_size) {
        if (!is_connected() && !is_listening()) {
            return {};
        }

        size_t buffer_size = (max_size > 0) ? std::min<size_t>(max_size, config_.buffer_size) : config_.buffer_size;
        std::vector<uint8_t> buffer(buffer_size);
        
        int received = recv(socket_, reinterpret_cast<char*>(buffer.data()), static_cast<int>(buffer_size), 0);
        if (received > 0) {
            buffer.resize(received);
            return buffer;
        }
        
        return {};
    }

    std::future<std::vector<uint8_t>> SocketManager::receive_data_async(size_t max_size) {
        return std::async(std::launch::async, [this, max_size]() {
            return receive_data(max_size);
        });
    }

    bool SocketManager::send_raw_data(const uint8_t* data, size_t size) {
        if (!is_connected()) {
            return false;
        }

        int sent = send(socket_, reinterpret_cast<const char*>(data), static_cast<int>(size), 0);
        return sent != SOCKET_ERROR && sent == static_cast<int>(size);
    }

    void SocketManager::queue_send_data(const std::vector<uint8_t>& data) {
        std::lock_guard<std::mutex> lock(send_mutex_);
        send_queue_.push(data);
        cv_.notify_one();
    }

    void SocketManager::process_send_queue() {
        while (!should_stop_) {
            std::unique_lock<std::mutex> lock(send_mutex_);
            cv_.wait(lock, [this] { return !send_queue_.empty() || should_stop_; });
            
            while (!send_queue_.empty() && !should_stop_) {
                auto data = std::move(send_queue_.front());
                send_queue_.pop();
                lock.unlock();
                
                send_raw_data(data.data(), data.size());
                
                lock.lock();
            }
        }
    }

    void SocketManager::worker_loop() {
        if (config_.type == SocketType::TCP) {
            handle_tcp_communication();
        } else {
            handle_udp_communication();
        }
    }

    void SocketManager::handle_tcp_communication() {
        std::vector<uint8_t> buffer(config_.buffer_size);
        
        while (!should_stop_) {
            if (state_ == SocketState::Listening) {
                // 服务器模式：接受连接
                sockaddr_in client_addr{};
                int addr_len = sizeof(client_addr);
                SOCKET client_socket = accept(socket_, reinterpret_cast<sockaddr*>(&client_addr), &addr_len);
                
                if (client_socket != INVALID_SOCKET) {
                    // 处理单个客户端连接（简化版本）
                    while (!should_stop_) {
                        int received = recv(client_socket, reinterpret_cast<char*>(buffer.data()), static_cast<int>(buffer.size()), 0);
                        if (received > 0) {
                            buffer.resize(received);
                            if (on_data_received_) {
                                on_data_received_(buffer);
                            }
                            buffer.resize(config_.buffer_size);
                        } else if (received == 0) {
                            break; // 客户端断开连接
                        } else {
                            int error = get_last_socket_error();
                            if (error != WSAEWOULDBLOCK && error != WSAEINTR) {
                                break;
                            }
                        }
                    }
                    closesocket(client_socket);
                }
            } else if (state_ == SocketState::Connected) {
                // 客户端模式：接收数据
                int received = recv(socket_, reinterpret_cast<char*>(buffer.data()), static_cast<int>(buffer.size()), 0);
                if (received > 0) {
                    buffer.resize(received);
                    if (on_data_received_) {
                        on_data_received_(buffer);
                    }
                    buffer.resize(config_.buffer_size);
                } else if (received == 0) {
                    // 连接关闭
                    should_stop_ = true;
                    state_ = SocketState::Disconnected;
                } else {
                    int error = get_last_socket_error();
                    if (error != WSAEWOULDBLOCK && error != WSAEINTR) {
                        should_stop_ = true;
                        state_ = SocketState::Error;
                        if (on_error_) {
                            on_error_(get_error_message(error));
                        }
                    }
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    void SocketManager::handle_udp_communication() {
        std::vector<uint8_t> buffer(config_.buffer_size);
        sockaddr_in from_addr{};
        int from_len = sizeof(from_addr);
        
        while (!should_stop_) {
            int received = recvfrom(socket_, reinterpret_cast<char*>(buffer.data()), 
                                  static_cast<int>(buffer.size()), 0, 
                                  reinterpret_cast<sockaddr*>(&from_addr), &from_len);
            
            if (received > 0) {
                buffer.resize(received);
                if (on_data_received_) {
                    on_data_received_(buffer);
                }
                buffer.resize(config_.buffer_size);
            } else {
                int error = get_last_socket_error();
                if (error != WSAEWOULDBLOCK && error != WSAEINTR) {
                    if (on_error_) {
                        on_error_(get_error_message(error));
                    }
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }

    int SocketManager::get_last_socket_error() const {
        return WSAGetLastError();
    }

    std::string SocketManager::get_error_message(int error_code) const {
        char* message = nullptr;
        FormatMessageA(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
                      nullptr, error_code, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
                      reinterpret_cast<LPSTR>(&message), 0, nullptr);
        
        std::string result = message ? message : "Unknown error";
        if (message) {
            LocalFree(message);
        }
        
        return result;
    }

    std::string SocketManager::get_local_address() const {
        if (socket_ == INVALID_SOCKET) {
            return "";
        }
        
        sockaddr_in addr{};
        int addr_len = sizeof(addr);
        if (getsockname(socket_, reinterpret_cast<sockaddr*>(&addr), &addr_len) == 0) {
            char ip_str[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &addr.sin_addr, ip_str, INET_ADDRSTRLEN);
            return std::string(ip_str);
        }
        return "";
    }

    uint16_t SocketManager::get_local_port() const {
        if (socket_ == INVALID_SOCKET) {
            return 0;
        }
        
        sockaddr_in addr{};
        int addr_len = sizeof(addr);
        if (getsockname(socket_, reinterpret_cast<sockaddr*>(&addr), &addr_len) == 0) {
            return ntohs(addr.sin_port);
        }
        return 0;
    }

    std::string SocketManager::get_remote_address() const {
        if (socket_ == INVALID_SOCKET || !is_connected()) {
            return "";
        }
        
        sockaddr_in addr{};
        int addr_len = sizeof(addr);
        if (getpeername(socket_, reinterpret_cast<sockaddr*>(&addr), &addr_len) == 0) {
            char ip_str[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &addr.sin_addr, ip_str, INET_ADDRSTRLEN);
            return std::string(ip_str);
        }
        return "";
    }

    uint16_t SocketManager::get_remote_port() const {
        if (socket_ == INVALID_SOCKET || !is_connected()) {
            return 0;
        }
        
        sockaddr_in addr{};
        int addr_len = sizeof(addr);
        if (getpeername(socket_, reinterpret_cast<sockaddr*>(&addr), &addr_len) == 0) {
            return ntohs(addr.sin_port);
        }
        return 0;
    }

    std::vector<std::string> SocketManager::get_local_ip_addresses() {
        std::vector<std::string> addresses;
        
        char hostname[256];
        if (gethostname(hostname, sizeof(hostname)) == 0) {
            addrinfo hints{}, *result = nullptr;
            hints.ai_family = AF_INET;
            hints.ai_socktype = SOCK_STREAM;
            
            if (getaddrinfo(hostname, nullptr, &hints, &result) == 0) {
                for (addrinfo* ptr = result; ptr != nullptr; ptr = ptr->ai_next) {
                    sockaddr_in* sockaddr_ipv4 = reinterpret_cast<sockaddr_in*>(ptr->ai_addr);
                    char ip_str[INET_ADDRSTRLEN];
                    inet_ntop(AF_INET, &sockaddr_ipv4->sin_addr, ip_str, INET_ADDRSTRLEN);
                    addresses.emplace_back(ip_str);
                }
                freeaddrinfo(result);
            }
        }
        
        return addresses;
    }

    bool SocketManager::is_port_available(uint16_t port, const std::string& address) {
        SOCKET test_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (test_socket == INVALID_SOCKET) {
            return false;
        }
        
        sockaddr_in addr{};
        addr.sin_family = AF_INET;
        addr.sin_port = htons(port);
        
        if (address == "0.0.0.0" || address.empty()) {
            addr.sin_addr.s_addr = INADDR_ANY;
        } else {
            inet_pton(AF_INET, address.c_str(), &addr.sin_addr);
        }
        
        bool available = (bind(test_socket, reinterpret_cast<sockaddr*>(&addr), sizeof(addr)) == 0);
        closesocket(test_socket);
        
        return available;
    }

    std::string SocketManager::resolve_hostname(const std::string& hostname) {
        addrinfo hints{}, *result = nullptr;
        hints.ai_family = AF_INET;
        hints.ai_socktype = SOCK_STREAM;
        
        if (getaddrinfo(hostname.c_str(), nullptr, &hints, &result) == 0) {
            sockaddr_in* sockaddr_ipv4 = reinterpret_cast<sockaddr_in*>(result->ai_addr);
            char ip_str[INET_ADDRSTRLEN];
            inet_ntop(AF_INET, &sockaddr_ipv4->sin_addr, ip_str, INET_ADDRSTRLEN);
            freeaddrinfo(result);
            return std::string(ip_str);
        }
        
        return "";
    }

    // SocketServer 实现
    SocketServer::SocketServer(const SocketConfig& config)
        : config_(config), listen_socket_(INVALID_SOCKET), is_running_(false) {
        initialize_wsa();
    }

    SocketServer::~SocketServer() {
        stop();
        cleanup_wsa();
    }

    void SocketServer::initialize_wsa() {
        std::lock_guard<std::mutex> lock(wsa_mutex_);
        if (!wsa_initialized_) {
            WSADATA wsaData;
            int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
            if (result != 0) {
                throw SocketException("Failed to initialize Winsock", result);
            }
            wsa_initialized_ = true;
        }
        wsa_ref_count_++;
    }

    void SocketServer::cleanup_wsa() {
        std::lock_guard<std::mutex> lock(wsa_mutex_);
        wsa_ref_count_--;
        if (wsa_ref_count_ <= 0 && wsa_initialized_) {
            WSACleanup();
            wsa_initialized_ = false;
            wsa_ref_count_ = 0;
        }
    }

    void SocketServer::set_config(const SocketConfig& config) {
        if (is_running_) {
            throw SocketException("Cannot change configuration while server is running");
        }
        config_ = config;
    }

    bool SocketServer::start() {
        if (is_running_) {
            return false;
        }

        try {
            // 创建监听socket
            listen_socket_ = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
            if (listen_socket_ == INVALID_SOCKET) {
                throw SocketException("Failed to create socket", WSAGetLastError());
            }

            // 设置socket选项
            if (config_.reuse_address) {
                int reuse = 1;
                setsockopt(listen_socket_, SOL_SOCKET, SO_REUSEADDR, reinterpret_cast<const char*>(&reuse), sizeof(reuse));
            }

            // 绑定地址
            sockaddr_in server_addr{};
            server_addr.sin_family = AF_INET;
            server_addr.sin_port = htons(config_.port);
            
            if (config_.address == "0.0.0.0" || config_.address.empty()) {
                server_addr.sin_addr.s_addr = INADDR_ANY;
            } else {
                if (inet_pton(AF_INET, config_.address.c_str(), &server_addr.sin_addr) <= 0) {
                    throw SocketException("Invalid address format");
                }
            }

            if (bind(listen_socket_, reinterpret_cast<sockaddr*>(&server_addr), sizeof(server_addr)) == SOCKET_ERROR) {
                throw SocketException("Failed to bind socket", WSAGetLastError());
            }

            if (listen(listen_socket_, SOMAXCONN) == SOCKET_ERROR) {
                throw SocketException("Failed to listen", WSAGetLastError());
            }

            is_running_ = true;
            accept_thread_ = std::thread(&SocketServer::accept_loop, this);
            
            return true;
        }
        catch (const SocketException& e) {
            if (listen_socket_ != INVALID_SOCKET) {
                closesocket(listen_socket_);
                listen_socket_ = INVALID_SOCKET;
            }
            if (on_error_) {
                on_error_(e.what());
            }
            return false;
        }
    }

    void SocketServer::stop() {
        if (!is_running_) {
            return;
        }
        
        is_running_ = false;
        
        if (listen_socket_ != INVALID_SOCKET) {
            closesocket(listen_socket_);
            listen_socket_ = INVALID_SOCKET;
        }
        
        if (accept_thread_.joinable()) {
            accept_thread_.join();
        }
        
        disconnect_all_clients();
    }

    bool SocketServer::is_running() const noexcept {
        return is_running_;
    }

    void SocketServer::set_client_data_callback(std::function<void(size_t, const std::vector<uint8_t>&)> callback) {
        on_client_data_ = std::move(callback);
    }

    void SocketServer::set_client_connected_callback(std::function<void(size_t)> callback) {
        on_client_connected_ = std::move(callback);
    }

    void SocketServer::set_client_disconnected_callback(std::function<void(size_t)> callback) {
        on_client_disconnected_ = std::move(callback);
    }

    void SocketServer::set_error_callback(std::function<void(const std::string&)> callback) {
        on_error_ = std::move(callback);
    }

    size_t SocketServer::get_client_count() const {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        return std::count_if(clients_.begin(), clients_.end(), 
                           [](const auto& client) { return client && client->active; });
    }

    std::vector<std::pair<std::string, uint16_t>> SocketServer::get_client_addresses() const {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        std::vector<std::pair<std::string, uint16_t>> addresses;
        
        for (const auto& client : clients_) {
            if (client && client->active) {
                addresses.emplace_back(client->address, client->port);
            }
        }
        
        return addresses;
    }

    bool SocketServer::disconnect_client(size_t client_id) {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        if (client_id >= clients_.size() || !clients_[client_id] || !clients_[client_id]->active) {
            return false;
        }
        
        clients_[client_id]->active = false;
        closesocket(clients_[client_id]->socket);
        
        if (clients_[client_id]->worker_thread.joinable()) {
            clients_[client_id]->worker_thread.join();
        }
        
        if (on_client_disconnected_) {
            on_client_disconnected_(client_id);
        }
        
        return true;
    }

    void SocketServer::disconnect_all_clients() {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        for (size_t i = 0; i < clients_.size(); ++i) {
            if (clients_[i] && clients_[i]->active) {
                clients_[i]->active = false;
                closesocket(clients_[i]->socket);
                
                if (clients_[i]->worker_thread.joinable()) {
                    clients_[i]->worker_thread.join();
                }
            }
        }
        clients_.clear();
    }

    void SocketServer::accept_loop() {
        while (is_running_) {
            sockaddr_in client_addr{};
            int addr_len = sizeof(client_addr);
            SOCKET client_socket = accept(listen_socket_, reinterpret_cast<sockaddr*>(&client_addr), &addr_len);
            
            if (client_socket != INVALID_SOCKET && is_running_) {
                char ip_str[INET_ADDRSTRLEN];
                inet_ntop(AF_INET, &client_addr.sin_addr, ip_str, INET_ADDRSTRLEN);
                uint16_t port = ntohs(client_addr.sin_port);
                
                auto client = std::make_unique<ClientConnection>(client_socket, std::string(ip_str), port);
                
                {
                    std::lock_guard<std::mutex> lock(clients_mutex_);
                    size_t client_id = clients_.size();
                    clients_.push_back(std::move(client));
                    
                    clients_[client_id]->worker_thread = std::thread(&SocketServer::handle_client, this, std::move(clients_[client_id]));
                    
                    if (on_client_connected_) {
                        on_client_connected_(client_id);
                    }
                }
                
                cleanup_disconnected_clients();
            }
        }
    }

    void SocketServer::handle_client(std::unique_ptr<ClientConnection> client) {
        std::vector<uint8_t> buffer(config_.buffer_size);
        size_t client_id = std::distance(clients_.data(), &client);
        
        while (client->active && is_running_) {
            int received = recv(client->socket, reinterpret_cast<char*>(buffer.data()), static_cast<int>(buffer.size()), 0);
            
            if (received > 0) {
                buffer.resize(received);
                if (on_client_data_) {
                    on_client_data_(client_id, buffer);
                }
                buffer.resize(config_.buffer_size);
            } else if (received == 0) {
                // 客户端断开连接
                break;
            } else {
                int error = WSAGetLastError();
                if (error != WSAEWOULDBLOCK && error != WSAEINTR) {
                    break;
                }
            }
        }
        
        client->active = false;
        closesocket(client->socket);
        
        if (on_client_disconnected_) {
            on_client_disconnected_(client_id);
        }
    }

    void SocketServer::cleanup_disconnected_clients() {
        std::lock_guard<std::mutex> lock(clients_mutex_);
        clients_.erase(
            std::remove_if(clients_.begin(), clients_.end(),
                         [](const auto& client) {
                             if (!client || !client->active) {
                                 if (client && client->worker_thread.joinable()) {
                                     client->worker_thread.join();
                                 }
                                 return true;
                             }
                             return false;
                         }),
            clients_.end());
    }

} // namespace fuxicore