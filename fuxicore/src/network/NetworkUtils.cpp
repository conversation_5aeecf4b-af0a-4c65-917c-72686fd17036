#include "network/NetworkUtils.h"
#include <QNetworkInterface>
#include <QHostInfo>
#include <QUrl>
#include <QUrlQuery>
#include <QTcpSocket>
#include <QTimer>
#include <QEventLoop>
#include <QDateTime>

QStringList NetworkUtils::getLocalIPAddresses()
{
    QStringList ipAddresses;
    
    // 获取所有网络接口
    QList<QHostAddress> addresses = QNetworkInterface::allAddresses();
    
    for (const QHostAddress& address : addresses) {
        // 只获取IPv4地址，排除回环地址
        if (address.protocol() == QAbstractSocket::IPv4Protocol && 
            address != QHostAddress::LocalHost) {
            ipAddresses.append(address.toString());
        }
    }
    
    return ipAddresses;
}

bool NetworkUtils::isValidIP(const QString& ip)
{
    QHostAddress address(ip);
    return !address.isNull();
}

bool NetworkUtils::isValidPort(int port)
{
    return port > 0 && port <= 65535;
}

QString NetworkUtils::getHostName()
{
    return QHostInfo::localHostName();
}

bool NetworkUtils::ping(const QString& host, int timeout)
{
    QTcpSocket socket;
    QHostAddress address(host);
    
    if (address.isNull()) {
        // 如果是主机名，先解析
        QHostInfo info = QHostInfo::fromName(host);
        if (info.addresses().isEmpty()) {
            return false;
        }
        address = info.addresses().first();
    }
    
    // 使用80端口进行连接测试
    socket.connectToHost(address, 80);
    
    QEventLoop loop;
    QTimer timer;
    timer.setSingleShot(true);
    
    QObject::connect(&timer, &QTimer::timeout, &loop, &QEventLoop::quit);
    QObject::connect(&socket, &QTcpSocket::connected, &loop, &QEventLoop::quit);
    
    timer.start(timeout);
    loop.exec();
    
    bool connected = socket.state() == QAbstractSocket::ConnectedState;
    if (connected) {
        socket.disconnectFromHost();
    }
    
    return connected;
}

QString NetworkUtils::urlEncode(const QString& input)
{
    return QUrl::toPercentEncoding(input);
}

QString NetworkUtils::urlDecode(const QString& input)
{
    return QUrl::fromPercentEncoding(input.toUtf8());
}
