#include "crypto/CryptoUtils.h"
#include <QCryptographicHash>
#include <QDataStream>
#include <QIODevice>
#include <openssl/aes.h>
#include <openssl/evp.h>
#include <openssl/sha.h>
#include <openssl/md5.h>
#include <vector>
#include <QDebug>

QString CryptoUtils::md5(const QByteArray& data)
{
    return QString::fromLatin1(QCryptographicHash::hash(data, QCryptographicHash::Md5).toHex());
}

QString CryptoUtils::sha1(const QByteArray& data)
{
    return QString::fromLatin1(QCryptographicHash::hash(data, QCryptographicHash::Sha1).toHex());
}

QString CryptoUtils::sha256(const QByteArray& data)
{
    return QString::fromLatin1(QCryptographicHash::hash(data, QCryptographicHash::Sha256).toHex());
}

QString CryptoUtils::base64Encode(const QByteArray& data)
{
    return QString::fromLatin1(data.toBase64());
}

QByteArray CryptoUtils::base64Decode(const QString& data)
{
    return QByteArray::fromBase64(data.toLatin1());
}

QByteArray CryptoUtils::aesEncrypt(const QByteArray& data, const QByteArray& key, const QByteArray& iv)
{
    if (key.size() != 16 && key.size() != 24 && key.size() != 32) {
        qWarning() << "Invalid AES key size:" << key.size();
        return QByteArray();
    }
    
    if (iv.size() != 16) {
        qWarning() << "Invalid AES IV size:" << iv.size();
        return QByteArray();
    }
    
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        qWarning() << "Failed to create EVP_CIPHER_CTX";
        return QByteArray();
    }
    
    const EVP_CIPHER* cipher = nullptr;
    switch (key.size()) {
    case 16:
        cipher = EVP_aes_128_cbc();
        break;
    case 24:
        cipher = EVP_aes_192_cbc();
        break;
    case 32:
        cipher = EVP_aes_256_cbc();
        break;
    }
    
    if (!cipher) {
        qWarning() << "Unsupported AES key size";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    
    if (EVP_EncryptInit_ex(ctx, cipher, nullptr, 
                          reinterpret_cast<const unsigned char*>(key.constData()), 
                          reinterpret_cast<const unsigned char*>(iv.constData())) != 1) {
        qWarning() << "Failed to initialize encryption";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    
    int blockSize = EVP_CIPHER_block_size(cipher);
    QByteArray encryptedData;
    encryptedData.resize(data.size() + blockSize);
    
    int len = 0;
    int totalLen = 0;
    
    if (EVP_EncryptUpdate(ctx, 
                         reinterpret_cast<unsigned char*>(encryptedData.data()), 
                         &len, 
                         reinterpret_cast<const unsigned char*>(data.constData()), 
                         data.size()) != 1) {
        qWarning() << "Failed to encrypt data";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    totalLen += len;
    
    if (EVP_EncryptFinal_ex(ctx, 
                           reinterpret_cast<unsigned char*>(encryptedData.data()) + len, 
                           &len) != 1) {
        qWarning() << "Failed to finalize encryption";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    totalLen += len;
    
    EVP_CIPHER_CTX_free(ctx);
    encryptedData.resize(totalLen);
    
    return encryptedData;
}

QByteArray CryptoUtils::aesDecrypt(const QByteArray& data, const QByteArray& key, const QByteArray& iv)
{
    if (key.size() != 16 && key.size() != 24 && key.size() != 32) {
        qWarning() << "Invalid AES key size:" << key.size();
        return QByteArray();
    }
    
    if (iv.size() != 16) {
        qWarning() << "Invalid AES IV size:" << iv.size();
        return QByteArray();
    }
    
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        qWarning() << "Failed to create EVP_CIPHER_CTX";
        return QByteArray();
    }
    
    const EVP_CIPHER* cipher = nullptr;
    switch (key.size()) {
    case 16:
        cipher = EVP_aes_128_cbc();
        break;
    case 24:
        cipher = EVP_aes_192_cbc();
        break;
    case 32:
        cipher = EVP_aes_256_cbc();
        break;
    }
    
    if (!cipher) {
        qWarning() << "Unsupported AES key size";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    
    if (EVP_DecryptInit_ex(ctx, cipher, nullptr, 
                          reinterpret_cast<const unsigned char*>(key.constData()), 
                          reinterpret_cast<const unsigned char*>(iv.constData())) != 1) {
        qWarning() << "Failed to initialize decryption";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    
    int blockSize = EVP_CIPHER_block_size(cipher);
    QByteArray decryptedData;
    decryptedData.resize(data.size() + blockSize);
    
    int len = 0;
    int totalLen = 0;
    
    if (EVP_DecryptUpdate(ctx, 
                         reinterpret_cast<unsigned char*>(decryptedData.data()), 
                         &len, 
                         reinterpret_cast<const unsigned char*>(data.constData()), 
                         data.size()) != 1) {
        qWarning() << "Failed to decrypt data";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    totalLen += len;
    
    if (EVP_DecryptFinal_ex(ctx, 
                           reinterpret_cast<unsigned char*>(decryptedData.data()) + len, 
                           &len) != 1) {
        qWarning() << "Failed to finalize decryption";
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    totalLen += len;
    
    EVP_CIPHER_CTX_free(ctx);
    decryptedData.resize(totalLen);
    
    return decryptedData;
}