#include "base/StringUtils.h"
#include <QRegularExpression>
#include <QTextCodec>
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QByteArray>
#include <algorithm>
#include <cmath>

// 常量定义
const QString StringUtils::UTF8_ENCODING = "UTF-8";
const QString StringUtils::GBK_ENCODING = "GBK";
const QString StringUtils::ASCII_ENCODING = "ASCII";

const QString StringUtils::EMAIL_PATTERN = R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
const QString StringUtils::PHONE_PATTERN = R"(^1[3-9]\d{9}$)";
const QString StringUtils::URL_PATTERN = R"(https?://[^\s/$.?#].[^\s]*)";
const QString StringUtils::IP_PATTERN = R"(^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)";

QStringList StringUtils::split(const QString& str, const QString& delimiter)
{
    if (str.isEmpty())
        return QStringList();

    return str.split(delimiter, Qt::SkipEmptyParts);
}

QString StringUtils::join(const QStringList& list, const QString& separator)
{
    return list.join(separator);
}

bool StringUtils::isBlank(const QString& str)
{
    return str.trimmed().isEmpty();
}

QString StringUtils::trim(const QString& str)
{
    return str.trimmed();
}

std::vector<int> StringUtils::toIntVector(const QString& str, const QString& delimiter)
{
    QStringList parts = split(str, delimiter);
    std::vector<int> result;
    bool ok;

    for (const QString& part : parts) {
        int value = part.trimmed().toInt(&ok);
        if (ok) {
            result.push_back(value);
        }
    }

    return result;
}

std::vector<double> StringUtils::toDoubleVector(const QString& str, const QString& delimiter)
{
    QStringList parts = split(str, delimiter);
    std::vector<double> result;
    bool ok;

    for (const QString& part : parts) {
        double value = part.trimmed().toDouble(&ok);
        if (ok) {
            result.push_back(value);
        }
    }

    return result;
}

// 大小写转换函数
QString StringUtils::toUpperCase(const QString& str)
{
    return str.toUpper();
}

QString StringUtils::toLowerCase(const QString& str)
{
    return str.toLower();
}

QString StringUtils::toTitleCase(const QString& str)
{
    QString result = str.toLower();
    bool capitalizeNext = true;

    for (int i = 0; i < result.length(); ++i) {
        if (result[i].isLetter()) {
            if (capitalizeNext) {
                result[i] = result[i].toUpper();
                capitalizeNext = false;
            }
        } else {
            capitalizeNext = true;
        }
    }

    return result;
}

QString StringUtils::toCamelCase(const QString& str)
{
    QStringList words = str.split(QRegularExpression(R"([_\s-]+)"), Qt::SkipEmptyParts);
    QString result;

    for (int i = 0; i < words.size(); ++i) {
        QString word = words[i].toLower();
        if (i > 0 && !word.isEmpty()) {
            word[0] = word[0].toUpper();
        }
        result += word;
    }

    return result;
}

QString StringUtils::toSnakeCase(const QString& str)
{
    QString result;

    for (int i = 0; i < str.length(); ++i) {
        QChar c = str[i];
        if (c.isUpper() && i > 0) {
            result += '_';
        }
        result += c.toLower();
    }

    return result;
}

// 字符串填充函数
QString StringUtils::leftPad(const QString& str, int width, QChar fillChar)
{
    if (str.length() >= width) return str;
    return QString(width - str.length(), fillChar) + str;
}

QString StringUtils::rightPad(const QString& str, int width, QChar fillChar)
{
    if (str.length() >= width) return str;
    return str + QString(width - str.length(), fillChar);
}

QString StringUtils::centerPad(const QString& str, int width, QChar fillChar)
{
    if (str.length() >= width) return str;

    int totalPadding = width - str.length();
    int leftPadding = totalPadding / 2;
    int rightPadding = totalPadding - leftPadding;

    return QString(leftPadding, fillChar) + str + QString(rightPadding, fillChar);
}

// 字符串替换函数
QString StringUtils::replace(const QString& str, const QString& oldStr,
                            const QString& newStr, bool caseSensitive)
{
    Qt::CaseSensitivity cs = caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;
    QString result = str;
    return result.replace(oldStr, newStr, cs);
}

QString StringUtils::regexReplace(const QString& str, const QString& pattern,
                                 const QString& replacement)
{
    QRegularExpression regex(pattern);
    QString result = str;
    return result.replace(regex, replacement);
}

// 字符串验证函数
bool StringUtils::isEmail(const QString& str)
{
    QRegularExpression regex(EMAIL_PATTERN);
    return regex.match(str).hasMatch();
}

bool StringUtils::isPhoneNumber(const QString& str)
{
    QRegularExpression regex(PHONE_PATTERN);
    return regex.match(str).hasMatch();
}

bool StringUtils::isUrl(const QString& str)
{
    QRegularExpression regex(URL_PATTERN);
    return regex.match(str).hasMatch();
}

bool StringUtils::isIpAddress(const QString& str)
{
    QRegularExpression regex(IP_PATTERN);
    return regex.match(str).hasMatch();
}

bool StringUtils::isNumeric(const QString& str)
{
    if (str.isEmpty()) return false;

    bool ok;
    str.toDouble(&ok);
    return ok;
}

bool StringUtils::isAlpha(const QString& str)
{
    if (str.isEmpty()) return false;

    for (const QChar& c : str) {
        if (!c.isLetter()) return false;
    }
    return true;
}

bool StringUtils::isAlphaNumeric(const QString& str)
{
    if (str.isEmpty()) return false;

    for (const QChar& c : str) {
        if (!c.isLetterOrNumber()) return false;
    }
    return true;
}

// 字符串搜索函数
QList<int> StringUtils::findAll(const QString& str, const QString& pattern, bool caseSensitive)
{
    QList<int> positions;
    Qt::CaseSensitivity cs = caseSensitive ? Qt::CaseSensitive : Qt::CaseInsensitive;

    int pos = 0;
    while ((pos = str.indexOf(pattern, pos, cs)) != -1) {
        positions.append(pos);
        pos += pattern.length();
    }

    return positions;
}

QStringList StringUtils::regexMatch(const QString& str, const QString& pattern)
{
    QRegularExpression regex(pattern);
    QRegularExpressionMatchIterator iterator = regex.globalMatch(str);
    QStringList matches;

    while (iterator.hasNext()) {
        QRegularExpressionMatch match = iterator.next();
        matches.append(match.captured(0));
    }

    return matches;
}

// 编码转换函数
QByteArray StringUtils::toEncoding(const QString& str, const QString& encoding)
{
    QTextCodec* codec = QTextCodec::codecForName(encoding.toUtf8());
    if (codec) {
        return codec->fromUnicode(str);
    }
    return str.toUtf8();
}

QString StringUtils::fromEncoding(const QByteArray& data, const QString& encoding)
{
    QTextCodec* codec = QTextCodec::codecForName(encoding.toUtf8());
    if (codec) {
        return codec->toUnicode(data);
    }
    return QString::fromUtf8(data);
}

QString StringUtils::convertEncoding(const QString& str, const QString& fromEncoding,
                                    const QString& toEncoding)
{
    // 首先将字符串按照源编码转换为字节数组
    QByteArray data = StringUtils::toEncoding(str, fromEncoding);
    // 然后将字节数组按照目标编码转换为字符串
    return StringUtils::fromEncoding(data, toEncoding);
}

// 字符串截取函数
QString StringUtils::truncate(const QString& str, int maxLength, const QString& suffix)
{
    if (str.length() <= maxLength) return str;

    int truncateLength = maxLength - suffix.length();
    if (truncateLength <= 0) return suffix.left(maxLength);

    return str.left(truncateLength) + suffix;
}

// 字符串反转函数
QString StringUtils::reverse(const QString& str)
{
    QString result = str;
    std::reverse(result.begin(), result.end());
    return result;
}

// 字符串相似度计算（编辑距离）
double StringUtils::similarity(const QString& str1, const QString& str2)
{
    int len1 = str1.length();
    int len2 = str2.length();

    if (len1 == 0) return len2 == 0 ? 1.0 : 0.0;
    if (len2 == 0) return 0.0;

    // 动态规划计算编辑距离
    std::vector<std::vector<int>> dp(len1 + 1, std::vector<int>(len2 + 1));

    for (int i = 0; i <= len1; ++i) dp[i][0] = i;
    for (int j = 0; j <= len2; ++j) dp[0][j] = j;

    for (int i = 1; i <= len1; ++i) {
        for (int j = 1; j <= len2; ++j) {
            int cost = (str1[i-1] == str2[j-1]) ? 0 : 1;
            dp[i][j] = std::min({
                dp[i-1][j] + 1,      // 删除
                dp[i][j-1] + 1,      // 插入
                dp[i-1][j-1] + cost  // 替换
            });
        }
    }

    int maxLen = std::max(len1, len2);
    return 1.0 - static_cast<double>(dp[len1][len2]) / maxLen;
}

// 模板替换函数
QString StringUtils::format(const QString& template_, const std::map<QString, QString>& variables)
{
    QString result = template_;

    for (const auto& pair : variables) {
        QString placeholder = "${" + pair.first + "}";
        result.replace(placeholder, pair.second);
    }

    return result;
}

// 随机字符串生成函数
QString StringUtils::generateRandom(int length, const QString& charset)
{
    QString result;
    result.reserve(length);

    for (int i = 0; i < length; ++i) {
        int index = QRandomGenerator::global()->bounded(charset.length());
        result.append(charset[index]);
    }

    return result;
}

// 字符串压缩和解压函数
QByteArray StringUtils::compress(const QString& str)
{
    return qCompress(str.toUtf8());
}

QString StringUtils::decompress(const QByteArray& data)
{
    return QString::fromUtf8(qUncompress(data));
}

// 哈希函数
QString StringUtils::hash(const QString& str)
{
    return QString::number(qHash(str));
}

QString StringUtils::md5(const QString& str)
{
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(str.toUtf8());
    return hash.result().toHex();
}

QString StringUtils::sha1(const QString& str)
{
    QCryptographicHash hash(QCryptographicHash::Sha1);
    hash.addData(str.toUtf8());
    return hash.result().toHex();
}

QString StringUtils::sha256(const QString& str)
{
    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(str.toUtf8());
    return hash.result().toHex();
}