#include "base/MathUtils.h"
#include <numeric>
#include <cmath>
#include <algorithm>
#include <map>
#include <random>
#include <functional>
#include <complex>
#include <QRandomGenerator>

// 数学常量定义
const qreal MathUtils::PI = 3.14159265358979323846;
const qreal MathUtils::E = 2.71828182845904523536;
const qreal MathUtils::GOLDEN_RATIO = 1.61803398874989484820;
const qreal MathUtils::SQRT_2 = 1.41421356237309504880;
const qreal MathUtils::SQRT_3 = 1.73205080756887729352;

qreal MathUtils::average(const std::vector<qreal>& values)
{
    if (values.empty())
        return 0.0;

    qreal sum = std::accumulate(values.begin(), values.end(), 0.0);
    return sum / values.size();
}

qreal MathUtils::variance(const std::vector<qreal>& values)
{
    if (values.size() < 2)
        return 0.0;

    qreal avg = average(values);
    qreal sum = 0.0;
    for (const qreal& value : values) {
        qreal diff = value - avg;
        sum += diff * diff;
    }
    return sum / (values.size() - 1); // 使用样本方差公式
}

qreal MathUtils::standardDeviation(const std::vector<qreal>& values)
{
    return std::sqrt(variance(values));
}

qreal MathUtils::linearInterpolate(qreal x0, qreal y0, qreal x1, qreal y1, qreal x)
{
    if (x0 == x1)
        return (y0 + y1) / 2; // 避免除零错误

    return y0 + (y1 - y0) * (x - x0) / (x1 - x0);
}

// 角度转换函数
qreal MathUtils::degToRad(qreal degrees)
{
    return degrees * PI / 180.0;
}

qreal MathUtils::radToDeg(qreal radians)
{
    return radians * 180.0 / PI;
}

// 数值比较函数
bool MathUtils::isEqual(qreal a, qreal b, qreal epsilon)
{
    return std::abs(a - b) < epsilon;
}

bool MathUtils::isZero(qreal value, qreal epsilon)
{
    return std::abs(value) < epsilon;
}

// 数值范围和限制函数
qreal MathUtils::clamp(qreal value, qreal min, qreal max)
{
    return std::max(min, std::min(max, value));
}

qreal MathUtils::lerp(qreal a, qreal b, qreal t)
{
    return a + t * (b - a);
}

qreal MathUtils::map(qreal value, qreal fromMin, qreal fromMax, qreal toMin, qreal toMax)
{
    return toMin + (value - fromMin) * (toMax - toMin) / (fromMax - fromMin);
}

qreal MathUtils::normalize(qreal value, qreal min, qreal max)
{
    return (value - min) / (max - min);
}

// 统计函数扩展
qreal MathUtils::median(std::vector<qreal> values)
{
    if (values.empty()) return 0.0;

    std::sort(values.begin(), values.end());
    size_t size = values.size();

    if (size % 2 == 0) {
        return (values[size/2 - 1] + values[size/2]) / 2.0;
    } else {
        return values[size/2];
    }
}

qreal MathUtils::mode(const std::vector<qreal>& values)
{
    if (values.empty()) return 0.0;

    std::map<qreal, int> frequency;
    for (qreal value : values) {
        frequency[value]++;
    }

    auto maxElement = std::max_element(frequency.begin(), frequency.end(),
        [](const std::pair<qreal, int>& a, const std::pair<qreal, int>& b) {
            return a.second < b.second;
        });

    return maxElement->first;
}

qreal MathUtils::range(const std::vector<qreal>& values)
{
    if (values.empty()) return 0.0;

    auto minmax = std::minmax_element(values.begin(), values.end());
    return *minmax.second - *minmax.first;
}

qreal MathUtils::percentile(std::vector<qreal> values, qreal p)
{
    if (values.empty()) return 0.0;
    if (p < 0.0 || p > 1.0) return 0.0;

    std::sort(values.begin(), values.end());

    qreal index = p * (values.size() - 1);
    int lowerIndex = static_cast<int>(std::floor(index));
    int upperIndex = static_cast<int>(std::ceil(index));

    if (lowerIndex == upperIndex) {
        return values[lowerIndex];
    }

    qreal weight = index - lowerIndex;
    return values[lowerIndex] * (1.0 - weight) + values[upperIndex] * weight;
}

qreal MathUtils::correlation(const std::vector<qreal>& x, const std::vector<qreal>& y)
{
    if (x.size() != y.size() || x.empty()) return 0.0;

    qreal meanX = average(x);
    qreal meanY = average(y);

    qreal numerator = 0.0;
    qreal sumXSquared = 0.0;
    qreal sumYSquared = 0.0;

    for (size_t i = 0; i < x.size(); ++i) {
        qreal deltaX = x[i] - meanX;
        qreal deltaY = y[i] - meanY;

        numerator += deltaX * deltaY;
        sumXSquared += deltaX * deltaX;
        sumYSquared += deltaY * deltaY;
    }

    qreal denominator = std::sqrt(sumXSquared * sumYSquared);
    return (denominator == 0.0) ? 0.0 : numerator / denominator;
}

// 数值计算函数
qreal MathUtils::factorial(int n)
{
    if (n < 0) return 0.0;
    if (n <= 1) return 1.0;

    qreal result = 1.0;
    for (int i = 2; i <= n; ++i) {
        result *= i;
    }
    return result;
}

qreal MathUtils::combination(int n, int r)
{
    if (r > n || r < 0) return 0.0;
    if (r == 0 || r == n) return 1.0;

    return factorial(n) / (factorial(r) * factorial(n - r));
}

qreal MathUtils::permutation(int n, int r)
{
    if (r > n || r < 0) return 0.0;

    return factorial(n) / factorial(n - r);
}

qreal MathUtils::gcd(int a, int b)
{
    a = std::abs(a);
    b = std::abs(b);

    while (b != 0) {
        int temp = b;
        b = a % b;
        a = temp;
    }
    return a;
}

qreal MathUtils::lcm(int a, int b)
{
    return std::abs(a * b) / gcd(a, b);
}

bool MathUtils::isPrime(int n)
{
    if (n <= 1) return false;
    if (n <= 3) return true;
    if (n % 2 == 0 || n % 3 == 0) return false;

    for (int i = 5; i * i <= n; i += 6) {
        if (n % i == 0 || n % (i + 2) == 0) {
            return false;
        }
    }
    return true;
}

std::vector<int> MathUtils::primeFactors(int n)
{
    std::vector<int> factors;

    // 处理2的因子
    while (n % 2 == 0) {
        factors.push_back(2);
        n /= 2;
    }

    // 处理奇数因子
    for (int i = 3; i * i <= n; i += 2) {
        while (n % i == 0) {
            factors.push_back(i);
            n /= i;
        }
    }

    // 如果n是大于2的质数
    if (n > 2) {
        factors.push_back(n);
    }

    return factors;
}

// 几何计算函数
qreal MathUtils::distance(const QPointF& p1, const QPointF& p2)
{
    qreal dx = p2.x() - p1.x();
    qreal dy = p2.y() - p1.y();
    return std::sqrt(dx * dx + dy * dy);
}

qreal MathUtils::distance3D(qreal x1, qreal y1, qreal z1, qreal x2, qreal y2, qreal z2)
{
    qreal dx = x2 - x1;
    qreal dy = y2 - y1;
    qreal dz = z2 - z1;
    return std::sqrt(dx * dx + dy * dy + dz * dz);
}

qreal MathUtils::angle(const QPointF& p1, const QPointF& p2)
{
    qreal dx = p2.x() - p1.x();
    qreal dy = p2.y() - p1.y();
    return std::atan2(dy, dx);
}

QPointF MathUtils::rotate(const QPointF& point, qreal angle, const QPointF& center)
{
    qreal cos_a = std::cos(angle);
    qreal sin_a = std::sin(angle);

    qreal dx = point.x() - center.x();
    qreal dy = point.y() - center.y();

    qreal rotatedX = dx * cos_a - dy * sin_a + center.x();
    qreal rotatedY = dx * sin_a + dy * cos_a + center.y();

    return QPointF(rotatedX, rotatedY);
}

qreal MathUtils::polygonArea(const QPolygonF& polygon)
{
    if (polygon.size() < 3) return 0.0;

    qreal area = 0.0;
    int n = polygon.size();

    for (int i = 0; i < n; ++i) {
        int j = (i + 1) % n;
        area += polygon[i].x() * polygon[j].y();
        area -= polygon[j].x() * polygon[i].y();
    }

    return std::abs(area) / 2.0;
}

bool MathUtils::pointInPolygon(const QPointF& point, const QPolygonF& polygon)
{
    if (polygon.size() < 3) return false;

    bool inside = false;
    int n = polygon.size();

    for (int i = 0, j = n - 1; i < n; j = i++) {
        const QPointF& pi = polygon[i];
        const QPointF& pj = polygon[j];

        if (((pi.y() > point.y()) != (pj.y() > point.y())) &&
            (point.x() < (pj.x() - pi.x()) * (point.y() - pi.y()) / (pj.y() - pi.y()) + pi.x())) {
            inside = !inside;
        }
    }

    return inside;
}

// 私有辅助函数
qreal MathUtils::gaussianKernel(qreal x, qreal sigma)
{
    return std::exp(-(x * x) / (2.0 * sigma * sigma)) / (sigma * std::sqrt(2.0 * PI));
}
