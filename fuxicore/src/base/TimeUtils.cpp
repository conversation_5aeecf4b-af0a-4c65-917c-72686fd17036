#include "base/TimeUtils.h"
#include <QThread>
#include <QElapsedTimer>
#include <QDateTime>
#include <QTimer>
#include <QCoreApplication>
#include <thread>
#include <chrono>

// 时间格式常量定义
const QString TimeUtils::ISO_FORMAT = "yyyy-MM-ddThh:mm:ss";
const QString TimeUtils::DATE_FORMAT = "yyyy-MM-dd";
const QString TimeUtils::TIME_FORMAT = "hh:mm:ss";
const QString TimeUtils::DATETIME_FORMAT = "yyyy-MM-dd hh:mm:ss";
const QString TimeUtils::COMPACT_FORMAT = "yyyyMMddhhmmss";

// 时间转换因子（相对于纳秒）
const qint64 TimeUtils::CONVERSION_FACTORS[] = {
    1,                    // Nanoseconds
    1000,                 // Microseconds
    1000000,              // Milliseconds
    1000000000,           // Seconds
    60000000000,          // Minutes
    3600000000000,        // Hours
    86400000000000        // Days
};

qint64 TimeUtils::s_startTime = QDateTime::currentMSecsSinceEpoch();

qint64 TimeUtils::currentTimestamp()
{
    return QDateTime::currentSecsSinceEpoch();
}

qint64 TimeUtils::currentTimestampMs()
{
    return QDateTime::currentMSecsSinceEpoch();
}

qint64 TimeUtils::currentTimestampUs()
{
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::microseconds>(duration).count();
}

qint64 TimeUtils::currentTimestampNs()
{
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = now.time_since_epoch();
    return std::chrono::duration_cast<std::chrono::nanoseconds>(duration).count();
}

std::chrono::high_resolution_clock::time_point TimeUtils::currentHighResTime()
{
    return std::chrono::high_resolution_clock::now();
}

QString TimeUtils::formatTimestamp(qint64 timestamp, const QString& format)
{
    QDateTime dateTime = QDateTime::fromSecsSinceEpoch(timestamp);
    return dateTime.toString(format);
}

QString TimeUtils::formatTimestampMs(qint64 timestampMs, const QString& format)
{
    QDateTime dateTime = QDateTime::fromMSecsSinceEpoch(timestampMs);
    return dateTime.toString(format);
}

qint64 TimeUtils::parseTimestamp(const QString& timeStr, const QString& format)
{
    QDateTime dateTime = QDateTime::fromString(timeStr, format);
    return dateTime.toSecsSinceEpoch();
}

qint64 TimeUtils::timeDiff(qint64 start, qint64 end, TimeUnit unit)
{
    qint64 diffSeconds = end - start;
    return convertTime(diffSeconds, TimeUnit::Seconds, unit);
}

void TimeUtils::sleep(int milliseconds)
{
    QThread::msleep(milliseconds);
}

void TimeUtils::sleepUs(int microseconds)
{
    std::this_thread::sleep_for(std::chrono::microseconds(microseconds));
}

qint64 TimeUtils::getRuntimeMs()
{
    return QDateTime::currentMSecsSinceEpoch() - s_startTime;
}

qint64 TimeUtils::getRuntimeSec()
{
    return getRuntimeMs() / 1000;
}

qint64 TimeUtils::convertTime(qint64 value, TimeUnit from, TimeUnit to)
{
    if (from == to) return value;

    // 先转换为纳秒，再转换为目标单位
    qint64 nanoseconds = value * CONVERSION_FACTORS[static_cast<int>(from)];
    return nanoseconds / CONVERSION_FACTORS[static_cast<int>(to)];
}

qint64 TimeUtils::getTodayStart()
{
    QDateTime now = QDateTime::currentDateTime();
    QDateTime todayStart = QDateTime(now.date(), QTime(0, 0, 0));
    return todayStart.toSecsSinceEpoch();
}

qint64 TimeUtils::getTodayEnd()
{
    QDateTime now = QDateTime::currentDateTime();
    QDateTime todayEnd = QDateTime(now.date(), QTime(23, 59, 59));
    return todayEnd.toSecsSinceEpoch();
}

qint64 TimeUtils::getWeekStart()
{
    QDateTime now = QDateTime::currentDateTime();
    int daysToMonday = now.date().dayOfWeek() - 1; // Qt中周一是1
    QDate monday = now.date().addDays(-daysToMonday);
    QDateTime weekStart = QDateTime(monday, QTime(0, 0, 0));
    return weekStart.toSecsSinceEpoch();
}

qint64 TimeUtils::getMonthStart()
{
    QDateTime now = QDateTime::currentDateTime();
    QDate firstDay = QDate(now.date().year(), now.date().month(), 1);
    QDateTime monthStart = QDateTime(firstDay, QTime(0, 0, 0));
    return monthStart.toSecsSinceEpoch();
}

bool TimeUtils::isSameDay(qint64 timestamp1, qint64 timestamp2)
{
    QDateTime dt1 = QDateTime::fromSecsSinceEpoch(timestamp1);
    QDateTime dt2 = QDateTime::fromSecsSinceEpoch(timestamp2);
    return dt1.date() == dt2.date();
}

bool TimeUtils::isWorkday(qint64 timestamp)
{
    QDateTime dt = QDateTime::fromSecsSinceEpoch(timestamp);
    int dayOfWeek = dt.date().dayOfWeek();
    return dayOfWeek >= 1 && dayOfWeek <= 5; // 周一到周五
}

bool TimeUtils::isWeekend(qint64 timestamp)
{
    return !isWorkday(timestamp);
}

QString TimeUtils::getRelativeTime(qint64 timestamp)
{
    qint64 now = currentTimestamp();
    qint64 diff = now - timestamp;

    if (diff < 0) {
        diff = -diff;
        if (diff < 60) return QString("在%1秒后").arg(diff);
        if (diff < 3600) return QString("在%1分钟后").arg(diff / 60);
        if (diff < 86400) return QString("在%1小时后").arg(diff / 3600);
        return QString("在%1天后").arg(diff / 86400);
    }

    if (diff < 60) return QString("%1秒前").arg(diff);
    if (diff < 3600) return QString("%1分钟前").arg(diff / 60);
    if (diff < 86400) return QString("%1小时前").arg(diff / 3600);
    if (diff < 2592000) return QString("%1天前").arg(diff / 86400); // 30天
    if (diff < 31536000) return QString("%1个月前").arg(diff / 2592000); // 12个月
    return QString("%1年前").arg(diff / 31536000);
}

// Timer类实现
TimeUtils::Timer::Timer()
{
    start();
}

void TimeUtils::Timer::start()
{
    m_startTime = std::chrono::high_resolution_clock::now();
}

void TimeUtils::Timer::restart()
{
    start();
}

qint64 TimeUtils::Timer::elapsed() const
{
    return elapsedMs();
}

qint64 TimeUtils::Timer::elapsedMs() const
{
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_startTime);
    return duration.count();
}

qint64 TimeUtils::Timer::elapsedUs() const
{
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(now - m_startTime);
    return duration.count();
}

qint64 TimeUtils::Timer::elapsedNs() const
{
    auto now = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(now - m_startTime);
    return duration.count();
}

QTimer* TimeUtils::createSingleShotTimer(int milliseconds, const TimerCallback& callback)
{
    QTimer* timer = new QTimer();
    timer->setSingleShot(true);
    QObject::connect(timer, &QTimer::timeout, [timer, callback]() {
        callback();
        timer->deleteLater();
    });
    timer->start(milliseconds);
    return timer;
}

QTimer* TimeUtils::createRepeatingTimer(int milliseconds, const TimerCallback& callback)
{
    QTimer* timer = new QTimer();
    timer->setSingleShot(false);
    QObject::connect(timer, &QTimer::timeout, callback);
    timer->start(milliseconds);
    return timer;
}