#include "base/FileUtils.h"
#include <QFile>
#include <QDir>
#include <QFileInfo>
#include <QStack>
#include <QDebug>

bool FileUtils::exists(const QString& filePath)
{
    return QFile::exists(filePath);
}

bool FileUtils::createDirectory(const QString& dirPath)
{
    QDir dir;
    return dir.mkpath(dirPath);
}

bool FileUtils::removeDirectory(const QString& dirPath)
{
    QDir dir(dirPath);
    return dir.removeRecursively();
}

QStringList FileUtils::listFiles(const QString& dirPath, bool recursive)
{
    QStringList result;
    QDir dir(dirPath);
    
    if (!dir.exists()) {
        return result;
    }
    
    if (recursive) {
        QFileInfoList fileInfoList = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);
        for (const QFileInfo& fileInfo : fileInfoList) {
            if (fileInfo.isDir()) {
                QStringList subDirFiles = listFiles(fileInfo.absoluteFilePath(), true);
                result.append(subDirFiles);
            } else {
                result.append(fileInfo.absoluteFilePath());
            }
        }
    } else {
        QFileInfoList fileInfoList = dir.entryInfoList(QDir::Files);
        for (const QFileInfo& fileInfo : fileInfoList) {
            result.append(fileInfo.absoluteFilePath());
        }
    }
    
    return result;
}

bool FileUtils::readFile(const QString& filePath, QByteArray& content)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open file for reading:" << filePath;
        return false;
    }
    
    content = file.readAll();
    file.close();
    return true;
}

bool FileUtils::writeFile(const QString& filePath, const QByteArray& content)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Failed to open file for writing:" << filePath;
        return false;
    }
    
    qint64 written = file.write(content);
    file.close();
    
    return written == content.size();
}

qint64 FileUtils::fileSize(const QString& filePath)
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        return -1;
    }
    
    return fileInfo.size();
}

bool FileUtils::copyFile(const QString& source, const QString& destination)
{
    QFile srcFile(source);
    return srcFile.copy(destination);
}

bool FileUtils::moveFile(const QString& source, const QString& destination)
{
    QFile srcFile(source);
    return srcFile.rename(destination);
}

bool FileUtils::deleteFile(const QString& filePath)
{
    QFile file(filePath);
    return file.remove();
}