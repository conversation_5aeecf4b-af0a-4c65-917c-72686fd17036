#include "data/JsonUtils.h"
#include <QJsonParseError>
#include <QJsonValue>
#include <QJsonArray>
#include <QVariantList>
#include <QVariantMap>
#include <QDebug>

QString JsonUtils::to<PERSON><PERSON>(const QVariant& variant, bool compact)
{
    QJsonDocument doc = QJsonDocument::fromVariant(variant);
    if (compact) {
        return doc.toJson(QJsonDocument::Compact);
    } else {
        return doc.toJson(QJsonDocument::Indented);
    }
}

QVariant JsonUtils::fromJson(const QString& json, bool* ok)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8(), &error);
    
    if (ok) {
        *ok = (error.error == QJsonParseError::NoError);
    }
    
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "Failed to parse JSON:" << error.errorString();
        return QVariant();
    }
    
    return doc.toVariant();
}

QVariantMap JsonUtils::jsonObjectToVariantMap(const QJsonObject& jsonObject)
{
    return jsonObject.toVariantMap();
}

QJsonObject JsonUtils::variantMapToJsonObject(const QVariantMap& variantMap)
{
    return QJsonObject::fromVariantMap(variantMap);
}

QJsonObject JsonUtils::mergeJsonObjects(const QJsonObject& base, const QJsonObject& overlay)
{
    QJsonObject result = base;
    
    for (auto it = overlay.begin(); it != overlay.end(); ++it) {
        result[it.key()] = it.value();
    }
    
    return result;
}

QVariant JsonUtils::getValue(const QJsonObject& jsonObject, const QString& key, const QVariant& defaultValue)
{
    if (!jsonObject.contains(key)) {
        return defaultValue;
    }
    
    QJsonValue value = jsonObject.value(key);
    return value.toVariant();
}

void JsonUtils::setValue(QJsonObject& jsonObject, const QString& key, const QVariant& value)
{
    jsonObject[key] = QJsonValue::fromVariant(value);
}

bool JsonUtils::isValidJson(const QString& json)
{
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(json.toUtf8(), &error);
    return error.error == QJsonParseError::NoError;
}