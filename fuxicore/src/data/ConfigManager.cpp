#include "data/ConfigManager.h"
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QFile>
#include <QDir>
#include <QStandardPaths>
#include <QCryptographicHash>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>
#include <QSettings>
#include <QDebug>

namespace Fuxi {
namespace Core {

class ConfigManager::ConfigManagerPrivate
{
public:
    ConfigManagerPrivate(ConfigManager* q) : q_ptr(q) {
        fileWatcher = new QFileSystemWatcher(q);
        reloadTimer = new QTimer(q);
        reloadTimer->setSingleShot(true);
        reloadTimer->setInterval(1000); // 1 second delay for reload
        
        QObject::connect(fileWatcher, &QFileSystemWatcher::fileChanged, 
                        q, &ConfigManager::onFileChanged);
        QObject::connect(reloadTimer, &QTimer::timeout, 
                        q, &ConfigManager::onReloadTimer);
    }
    
    ConfigManager* q_ptr;
    QJsonObject configData;
    QJsonObject defaultValues;
    QJsonObject validationSchema;
    ConfigInfo configInfo;
    QFileSystemWatcher* fileWatcher;
    QTimer* reloadTimer;
    bool hotReloadEnabled = false;
    bool configModified = false;
    QStringList backupPaths;
    
    // Helper methods
    QJsonObject* navigateToObject(const QStringList& keyParts, bool createPath = false);
    const QJsonObject* navigateToObjectConst(const QStringList& keyParts) const;
    QString calculateFileChecksum(const QString& filePath) const;
    bool validateJsonValue(const QJsonValue& value, const QJsonValue& schema) const;
};

ConfigManager::ConfigManager(QObject *parent)
    : QObject(parent), d_ptr(new ConfigManagerPrivate(this))
{
}

ConfigManager::~ConfigManager()
{
    delete d_ptr;
}

bool ConfigManager::loadConfig(const QString& filePath, ConfigFormat format)
{
    if (filePath.isEmpty()) {
        return false;
    }
    
    QFile file(filePath);
    if (!file.exists()) {
        emit validationFailed(ValidationResult::FileNotFound, "Configuration file not found: " + filePath);
        return false;
    }
    
    if (!file.open(QIODevice::ReadOnly)) {
        emit validationFailed(ValidationResult::PermissionDenied, "Cannot read configuration file: " + filePath);
        return false;
    }
    
    bool success = false;
    switch (format) {
        case ConfigFormat::JSON:
            success = loadJsonConfig(filePath);
            break;
        case ConfigFormat::XML:
            success = loadXmlConfig(filePath);
            break;
        case ConfigFormat::INI:
            success = loadIniConfig(filePath);
            break;
        case ConfigFormat::YAML:
            success = loadYamlConfig(filePath);
            break;
    }
    
    if (success) {
        d_ptr->configInfo.filePath = filePath;
        d_ptr->configInfo.format = format;
        d_ptr->configInfo.lastLoaded = QDateTime::currentDateTime();
        d_ptr->configInfo.lastModified = QFileInfo(filePath).lastModified();
        d_ptr->configInfo.checksum = d_ptr->calculateFileChecksum(filePath);
        d_ptr->configInfo.isValid = true;
        d_ptr->configModified = false;
        
        // Setup file watching for hot reload
        if (d_ptr->hotReloadEnabled) {
            d_ptr->fileWatcher->removePaths(d_ptr->fileWatcher->files());
            d_ptr->fileWatcher->addPath(filePath);
        }
        
        emit configLoaded(filePath);
    }
    
    return success;
}

bool ConfigManager::saveConfig(const QString& filePath, ConfigFormat format)
{
    QString targetPath = filePath.isEmpty() ? d_ptr->configInfo.filePath : filePath;
    ConfigFormat targetFormat = (filePath.isEmpty()) ? d_ptr->configInfo.format : format;
    
    if (targetPath.isEmpty()) {
        return false;
    }
    
    // Ensure directory exists
    QDir dir = QFileInfo(targetPath).absoluteDir();
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    bool success = false;
    switch (targetFormat) {
        case ConfigFormat::JSON:
            success = saveJsonConfig(targetPath);
            break;
        case ConfigFormat::XML:
            success = saveXmlConfig(targetPath);
            break;
        case ConfigFormat::INI:
            success = saveIniConfig(targetPath);
            break;
        case ConfigFormat::YAML:
            success = saveYamlConfig(targetPath);
            break;
    }
    
    if (success) {
        d_ptr->configInfo.lastModified = QDateTime::currentDateTime();
        d_ptr->configInfo.checksum = d_ptr->calculateFileChecksum(targetPath);
        d_ptr->configModified = false;
        emit configSaved(targetPath);
    }
    
    return success;
}

bool ConfigManager::reloadConfig()
{
    if (d_ptr->configInfo.filePath.isEmpty()) {
        return false;
    }
    
    bool success = loadConfig(d_ptr->configInfo.filePath, d_ptr->configInfo.format);
    if (success) {
        emit configReloaded();
    }
    return success;
}

QVariant ConfigManager::getValue(const QString& key, const QVariant& defaultValue) const
{
    if (key.isEmpty()) {
        return defaultValue;
    }
    
    QStringList keyParts = splitKey(key);
    const QJsonObject* obj = d_ptr->navigateToObjectConst(keyParts.mid(0, keyParts.size() - 1));
    
    if (!obj) {
        // Check default values
        if (d_ptr->defaultValues.contains(key)) {
            return d_ptr->defaultValues[key].toVariant();
        }
        return defaultValue;
    }
    
    QString finalKey = keyParts.last();
    if (!obj->contains(finalKey)) {
        // Check default values
        if (d_ptr->defaultValues.contains(key)) {
            return d_ptr->defaultValues[key].toVariant();
        }
        return defaultValue;
    }
    
    return obj->value(finalKey).toVariant();
}

bool ConfigManager::setValue(const QString& key, const QVariant& value)
{
    if (key.isEmpty()) {
        return false;
    }
    
    QVariant oldValue = getValue(key);
    
    QStringList keyParts = splitKey(key);
    QJsonObject* obj = d_ptr->navigateToObject(keyParts.mid(0, keyParts.size() - 1), true);
    
    if (!obj) {
        return false;
    }
    
    QString finalKey = keyParts.last();
    obj->insert(finalKey, QJsonValue::fromVariant(value));
    
    d_ptr->configModified = true;
    emit configChanged(key, oldValue, value);
    
    return true;
}

bool ConfigManager::removeValue(const QString& key)
{
    if (key.isEmpty()) {
        return false;
    }
    
    QStringList keyParts = splitKey(key);
    QJsonObject* obj = d_ptr->navigateToObject(keyParts.mid(0, keyParts.size() - 1));
    
    if (!obj) {
        return false;
    }
    
    QString finalKey = keyParts.last();
    if (!obj->contains(finalKey)) {
        return false;
    }
    
    QVariant oldValue = obj->value(finalKey).toVariant();
    obj->remove(finalKey);
    
    d_ptr->configModified = true;
    emit configChanged(key, oldValue, QVariant());
    
    return true;
}

bool ConfigManager::hasValue(const QString& key) const
{
    if (key.isEmpty()) {
        return false;
    }
    
    QStringList keyParts = splitKey(key);
    const QJsonObject* obj = d_ptr->navigateToObjectConst(keyParts.mid(0, keyParts.size() - 1));
    
    if (!obj) {
        return d_ptr->defaultValues.contains(key);
    }
    
    QString finalKey = keyParts.last();
    return obj->contains(finalKey) || d_ptr->defaultValues.contains(key);
}

QStringList ConfigManager::getSectionKeys(const QString& section) const
{
    if (section.isEmpty()) {
        return d_ptr->configData.keys();
    }
    
    QStringList keyParts = splitKey(section);
    const QJsonObject* obj = d_ptr->navigateToObjectConst(keyParts);
    
    return obj ? obj->keys() : QStringList();
}

QJsonObject ConfigManager::getSection(const QString& section) const
{
    if (section.isEmpty()) {
        return d_ptr->configData;
    }
    
    QStringList keyParts = splitKey(section);
    const QJsonObject* obj = d_ptr->navigateToObjectConst(keyParts);
    
    return obj ? *obj : QJsonObject();
}

bool ConfigManager::setSection(const QString& section, const QJsonObject& data)
{
    if (section.isEmpty()) {
        d_ptr->configData = data;
        d_ptr->configModified = true;
        return true;
    }
    
    QStringList keyParts = splitKey(section);
    QJsonObject* obj = d_ptr->navigateToObject(keyParts.mid(0, keyParts.size() - 1), true);
    
    if (!obj) {
        return false;
    }
    
    QString finalKey = keyParts.last();
    obj->insert(finalKey, data);
    
    d_ptr->configModified = true;
    return true;
}

bool ConfigManager::removeSection(const QString& section)
{
    if (section.isEmpty()) {
        d_ptr->configData = QJsonObject();
        d_ptr->configModified = true;
        return true;
    }
    
    QStringList keyParts = splitKey(section);
    QJsonObject* obj = d_ptr->navigateToObject(keyParts.mid(0, keyParts.size() - 1));
    
    if (!obj) {
        return false;
    }
    
    QString finalKey = keyParts.last();
    if (!obj->contains(finalKey)) {
        return false;
    }
    
    obj->remove(finalKey);
    d_ptr->configModified = true;
    return true;
}

ConfigManager::ValidationResult ConfigManager::validateConfig() const
{
    if (d_ptr->configData.isEmpty()) {
        return ValidationResult::InvalidFormat;
    }
    
    if (!d_ptr->validationSchema.isEmpty()) {
        if (!d_ptr->validateJsonValue(d_ptr->configData, d_ptr->validationSchema)) {
            return ValidationResult::SchemaViolation;
        }
    }
    
    return ValidationResult::Valid;
}

void ConfigManager::enableHotReload(bool enable)
{
    d_ptr->hotReloadEnabled = enable;
    
    if (enable && !d_ptr->configInfo.filePath.isEmpty()) {
        d_ptr->fileWatcher->addPath(d_ptr->configInfo.filePath);
    } else {
        d_ptr->fileWatcher->removePaths(d_ptr->fileWatcher->files());
    }
}

bool ConfigManager::isHotReloadEnabled() const
{
    return d_ptr->hotReloadEnabled;
}

void ConfigManager::setReloadDelay(int milliseconds)
{
    d_ptr->reloadTimer->setInterval(milliseconds);
}

QString ConfigManager::getConfigVersion() const
{
    return getValue("version", "1.0").toString();
}

bool ConfigManager::setConfigVersion(const QString& version)
{
    return setValue("version", version);
}

ConfigManager::ConfigInfo ConfigManager::getConfigInfo() const
{
    return d_ptr->configInfo;
}

QString ConfigManager::getConfigChecksum() const
{
    return d_ptr->configInfo.checksum;
}

bool ConfigManager::isConfigModified() const
{
    return d_ptr->configModified;
}

void ConfigManager::setDefaultValue(const QString& key, const QVariant& value)
{
    d_ptr->defaultValues[key] = QJsonValue::fromVariant(value);
}

QVariant ConfigManager::getDefaultValue(const QString& key) const
{
    return d_ptr->defaultValues.value(key).toVariant();
}

void ConfigManager::onFileChanged(const QString& path)
{
    Q_UNUSED(path)
    if (d_ptr->hotReloadEnabled) {
        d_ptr->reloadTimer->start();
    }
}

void ConfigManager::onReloadTimer()
{
    reloadConfig();
}

// Private helper methods implementation
QStringList ConfigManager::splitKey(const QString& key) const
{
    return key.split('.', Qt::SkipEmptyParts);
}

QJsonObject* ConfigManager::ConfigManagerPrivate::navigateToObject(const QStringList& keyParts, bool createPath)
{
    if (keyParts.isEmpty()) {
        return &configData;
    }

    // For simplicity and to avoid pointer issues with nested objects,
    // only support direct access to the root configData object
    // This means we only support single-level key navigation
    if (keyParts.size() == 1) {
        return &configData;
    }

    // For nested navigation, we would need to restructure the approach
    // to avoid returning pointers to temporary objects
    // For now, return nullptr to indicate nested navigation is not supported
    return nullptr;
}

const QJsonObject* ConfigManager::ConfigManagerPrivate::navigateToObjectConst(const QStringList& keyParts) const
{
    if (keyParts.isEmpty()) {
        return &configData;
    }

    // For now, only support single-level navigation to avoid the temporary object issue
    // This is a simplified implementation that works with the current usage patterns
    if (keyParts.size() == 1) {
        return &configData;
    }

    // For multi-level navigation, we need a different approach
    // Since we can't safely return pointers to temporary objects,
    // we'll return nullptr for now and handle nested access differently
    // TODO: Implement proper nested object navigation using a different pattern
    return nullptr;
}

bool ConfigManager::loadJsonConfig(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);

    if (error.error != QJsonParseError::NoError) {
        emit validationFailed(ValidationResult::InvalidFormat, error.errorString());
        return false;
    }

    d_ptr->configData = doc.object();
    return true;
}

bool ConfigManager::loadXmlConfig(const QString& filePath)
{
    // Simplified XML loading - in production, implement proper XML to JSON conversion
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    // This is a placeholder implementation
    // In production, you would parse XML and convert to JSON structure
    d_ptr->configData = QJsonObject();
    return true;
}

bool ConfigManager::loadIniConfig(const QString& filePath)
{
    QSettings settings(filePath, QSettings::IniFormat);
    QJsonObject obj;

    const QStringList keys = settings.allKeys();
    for (const QString& key : keys) {
        QVariant value = settings.value(key);
        obj[key] = QJsonValue::fromVariant(value);
    }

    d_ptr->configData = obj;
    return true;
}

bool ConfigManager::loadYamlConfig(const QString& filePath)
{
    // Placeholder for YAML loading
    // In production, use a YAML library like yaml-cpp
    Q_UNUSED(filePath)
    return false;
}

bool ConfigManager::saveJsonConfig(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }

    QJsonDocument doc(d_ptr->configData);
    file.write(doc.toJson());
    return true;
}

bool ConfigManager::saveXmlConfig(const QString& filePath)
{
    // Placeholder for XML saving
    Q_UNUSED(filePath)
    return false;
}

bool ConfigManager::saveIniConfig(const QString& filePath)
{
    QSettings settings(filePath, QSettings::IniFormat);

    // Flatten JSON object to INI format
    std::function<void(const QJsonObject&, const QString&)> writeObject;
    writeObject = [&](const QJsonObject& obj, const QString& prefix) {
        for (auto it = obj.begin(); it != obj.end(); ++it) {
            QString key = prefix.isEmpty() ? it.key() : prefix + "." + it.key();
            QJsonValue value = it.value();

            if (value.isObject()) {
                writeObject(value.toObject(), key);
            } else {
                settings.setValue(key, value.toVariant());
            }
        }
    };

    writeObject(d_ptr->configData, QString());
    return true;
}

bool ConfigManager::saveYamlConfig(const QString& filePath)
{
    // Placeholder for YAML saving
    Q_UNUSED(filePath)
    return false;
}

QString ConfigManager::ConfigManagerPrivate::calculateFileChecksum(const QString& filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }

    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(&file);
    return QString::fromUtf8(hash.result().toHex());
}

bool ConfigManager::ConfigManagerPrivate::validateJsonValue(const QJsonValue& value, const QJsonValue& schema) const
{
    // Simplified JSON schema validation
    // In production, use a proper JSON schema validation library
    Q_UNUSED(value)
    Q_UNUSED(schema)
    return true;
}

ConfigManager::ValidationResult ConfigManager::validateConfig(const QString& filePath, ConfigFormat format)
{
    Q_UNUSED(filePath)
    Q_UNUSED(format)
    // Simplified implementation
    return ValidationResult::Valid;
}

bool ConfigManager::createBackup(const QString& backupPath)
{
    QString targetPath = backupPath.isEmpty() ?
        d_ptr->configInfo.filePath + ".backup" : backupPath;

    if (d_ptr->configInfo.filePath.isEmpty()) {
        return false;
    }

    return QFile::copy(d_ptr->configInfo.filePath, targetPath);
}

bool ConfigManager::restoreFromBackup(const QString& backupPath)
{
    if (!QFile::exists(backupPath)) {
        return false;
    }

    return loadConfig(backupPath, d_ptr->configInfo.format);
}

} // namespace Core
} // namespace Fuxi
