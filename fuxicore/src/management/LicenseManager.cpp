#include "management/LicenseManager.h"
#include <QApplication>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QTextStream>
#include <QJsonDocument>
#include <QJsonObject>
#include <QCryptographicHash>
#include <QSysInfo>
#include <QtNetwork/QNetworkInterface>
#include <QVariantMap>
#include <QStorageInfo>
#include <QTimer>
#include <QUuid>
#include <QDebug>
#include <QJsonArray>
// Production includes for enhanced security
#include <QRandomGenerator>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QUrlQuery>
#include <QProcess>
#include <QCoreApplication>
#include <QFileInfo>
#include <QSettings>

#ifdef Q_OS_WIN
#include <windows.h>
#include <intrin.h>
#include <tlhelp32.h>
#include <wbemidl.h>
#include <comdef.h>

// Define NTSTATUS if not available
#ifndef NTSTATUS
typedef LONG NTSTATUS;
#endif

#ifndef NID_sha256
#define NID_sha256 672
#endif
#endif

#ifdef PRODUCTION_BUILD
#include <openssl/evp.h>
#include <openssl/rand.h>
#include <openssl/rsa.h>
#include <openssl/pem.h>
#include <openssl/aes.h>
#include <openssl/sha.h>
#include <openssl/kdf.h>
#endif

namespace Fuxi {
namespace Core {

class LicenseManager::LicenseManagerPrivate
{
public:
    LicenseManagerPrivate(LicenseManager* q) : q_ptr(q) {
        licenseFilePath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/license.dat";

        // Initialize timers
        expiryCheckTimer = new QTimer(q);
        expiryCheckTimer->setInterval(60000); // Check every minute
        QObject::connect(expiryCheckTimer, &QTimer::timeout, q, &LicenseManager::checkLicenseExpiry);
        expiryCheckTimer->start();

        // Security check timer
        securityCheckTimer = new QTimer(q);
        securityCheckTimer->setInterval(30000); // Check every 30 seconds
        QObject::connect(securityCheckTimer, &QTimer::timeout, q, &LicenseManager::performSecurityCheck);
        securityCheckTimer->start();

        // Network manager for online validation
        networkManager = new QNetworkAccessManager(q);
        QObject::connect(networkManager, &QNetworkAccessManager::finished, q, &LicenseManager::onOnlineValidationFinished);

        // Calculate initial executable hash
        lastExecutableHash = q->calculateExecutableHash();
    }
    
    LicenseManager* q_ptr;
    QString licenseFilePath;
    QString privateKeyPath;
    QString publicKeyPath;
    LicenseInfo currentLicense;
    QTimer* expiryCheckTimer;
    QTimer* securityCheckTimer;
    bool licenseLoaded = false;

    // Production security features
    bool onlineValidationEnabled = false;
    QString licenseServerUrl;
    QNetworkAccessManager* networkManager;
    QString lastExecutableHash;
    QStringList trustedProcesses;
    
    // RSA key management
    RSA* loadPrivateKey() const;
    RSA* loadPublicKey() const;
    void freeRSAKey(RSA* key) const;
};

LicenseManager::LicenseManager(QObject *parent)
    : QObject(parent), d_ptr(new LicenseManagerPrivate(this))
{
    // Ensure license directory exists
    QDir dir(QFileInfo(d_ptr->licenseFilePath).absolutePath());
    if (!dir.exists()) {
        dir.mkpath(".");
    }
    
    // Try to load existing license
    validateLicense();
}

LicenseManager::~LicenseManager()
{
    delete d_ptr;
}

bool LicenseManager::LicenseInfo::isValid() const
{
    return !licenseKey.isEmpty() && 
           !customerName.isEmpty() && 
           issueDate.isValid() && 
           expiryDate.isValid() && 
           issueDate < expiryDate &&
           !signature.isEmpty();
}

bool LicenseManager::LicenseInfo::isExpired() const
{
    return QDateTime::currentDateTime() > expiryDate;
}

int LicenseManager::LicenseInfo::daysRemaining() const
{
    if (isExpired()) return 0;
    return QDateTime::currentDateTime().daysTo(expiryDate);
}

QString LicenseManager::generateLicense(const QString& customerName,
                                      const QString& companyName,
                                      LicenseType type,
                                      const QDateTime& expiryDate,
                                      const QStringList& features)
{
    QJsonObject licenseData;
    licenseData["licenseKey"] = generateLicenseKey();
    licenseData["customerName"] = customerName;
    licenseData["companyName"] = companyName;
    licenseData["type"] = static_cast<int>(type);
    licenseData["issueDate"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    licenseData["expiryDate"] = expiryDate.toString(Qt::ISODate);
    licenseData["hardwareFingerprint"] = getHardwareFingerprint();
    licenseData["features"] = QJsonArray::fromStringList(features);
    
    QJsonDocument doc(licenseData);
    QString licenseString = doc.toJson(QJsonDocument::Compact);
    QString signature = signLicense(licenseString);
    
    licenseData["signature"] = signature;
    QJsonDocument finalDoc(licenseData);

    // Encrypt license data using AES instead of Base64
    QString licenseJson = finalDoc.toJson(QJsonDocument::Compact);
    return encryptLicenseData(licenseJson);
}

LicenseManager::ValidationResult LicenseManager::validateLicense(const QString& licenseKey)
{
    QString keyToValidate = licenseKey.isEmpty() ? loadLicenseFromFile(d_ptr->licenseFilePath) : licenseKey;
    
    if (keyToValidate.isEmpty()) {
        return ValidationResult::NotFound;
    }
    
    // Decrypt license data using AES
    QString decryptedData = decryptLicenseData(keyToValidate);
    if (decryptedData.isEmpty()) {
        return ValidationResult::Corrupted;
    }

    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(decryptedData.toUtf8(), &error);
    
    if (error.error != QJsonParseError::NoError) {
        return ValidationResult::InvalidFormat;
    }
    
    QJsonObject licenseData = doc.object();
    
    // Extract license information
    LicenseInfo info;
    info.licenseKey = licenseData["licenseKey"].toString();
    info.customerName = licenseData["customerName"].toString();
    info.companyName = licenseData["companyName"].toString();
    info.type = static_cast<LicenseType>(licenseData["type"].toInt());
    info.issueDate = QDateTime::fromString(licenseData["issueDate"].toString(), Qt::ISODate);
    info.expiryDate = QDateTime::fromString(licenseData["expiryDate"].toString(), Qt::ISODate);
    info.hardwareFingerprint = licenseData["hardwareFingerprint"].toString();
    info.signature = licenseData["signature"].toString();
    
    QJsonArray featuresArray = licenseData["features"].toArray();
    for (const auto& feature : featuresArray) {
        info.enabledFeatures.append(feature.toString());
    }
    
    // Validate license format
    if (!info.isValid()) {
        return ValidationResult::InvalidFormat;
    }
    
    // Check expiry
    if (info.isExpired()) {
        return ValidationResult::Expired;
    }
    
    // Check hardware fingerprint
    if (info.hardwareFingerprint != getHardwareFingerprint()) {
        return ValidationResult::HardwareMismatch;
    }
    
    // Verify signature
    QJsonObject dataToVerify = licenseData;
    dataToVerify.remove("signature");
    QJsonDocument verifyDoc(dataToVerify);
    QString dataString = verifyDoc.toJson(QJsonDocument::Compact);
    
    if (!verifySignature(dataString, info.signature)) {
        return ValidationResult::InvalidSignature;
    }
    
    // License is valid, store it
    d_ptr->currentLicense = info;
    d_ptr->licenseLoaded = true;
    
    emit licenseValidated(ValidationResult::Valid);
    
    // Check if license is expiring soon (within 30 days)
    int daysRemaining = info.daysRemaining();
    if (daysRemaining <= 30 && daysRemaining > 0) {
        emit licenseExpiring(daysRemaining);
    }
    
    return ValidationResult::Valid;
}

LicenseManager::ValidationResult LicenseManager::validateLicenseFile(const QString& filePath)
{
    QString licenseKey = loadLicenseFromFile(filePath);
    return validateLicense(licenseKey);
}

LicenseManager::LicenseInfo LicenseManager::getCurrentLicenseInfo() const
{
    return d_ptr->currentLicense;
}

bool LicenseManager::isFeatureEnabled(const QString& featureName) const
{
    if (!d_ptr->licenseLoaded) {
        emit const_cast<LicenseManager*>(this)->featureAccessDenied(featureName);
        return false;
    }
    
    return d_ptr->currentLicense.enabledFeatures.contains(featureName) ||
           d_ptr->currentLicense.enabledFeatures.contains("*"); // Wildcard for all features
}

bool LicenseManager::installLicense(const QString& licenseKey)
{
    ValidationResult result = validateLicense(licenseKey);
    if (result == ValidationResult::Valid) {
        return saveLicenseToFile(d_ptr->licenseFilePath, licenseKey);
    }
    return false;
}

bool LicenseManager::installLicenseFromFile(const QString& filePath)
{
    QString licenseKey = loadLicenseFromFile(filePath);
    return installLicense(licenseKey);
}

bool LicenseManager::removeLicense()
{
    QFile file(d_ptr->licenseFilePath);
    if (file.exists()) {
        bool removed = file.remove();
        if (removed) {
            d_ptr->currentLicense = LicenseInfo();
            d_ptr->licenseLoaded = false;
        }
        return removed;
    }
    return true;
}

QString LicenseManager::getHardwareFingerprint() const
{
    return generateHardwareFingerprint();
}

bool LicenseManager::saveLicenseToFile(const QString& filePath, const QString& licenseKey)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    out << encryptLicenseData(licenseKey);
    return true;
}

QString LicenseManager::loadLicenseFromFile(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return QString();
    }
    
    QTextStream in(&file);
    QString encryptedData = in.readAll();
    return decryptLicenseData(encryptedData);
}

void LicenseManager::setLicenseFilePath(const QString& filePath)
{
    d_ptr->licenseFilePath = filePath;
}

QString LicenseManager::getLicenseFilePath() const
{
    return d_ptr->licenseFilePath;
}

void LicenseManager::setPrivateKeyPath(const QString& keyPath)
{
    d_ptr->privateKeyPath = keyPath;
}

void LicenseManager::setPublicKeyPath(const QString& keyPath)
{
    d_ptr->publicKeyPath = keyPath;
}

void LicenseManager::checkLicenseExpiry()
{
    if (!d_ptr->licenseLoaded) return;
    
    if (d_ptr->currentLicense.isExpired()) {
        emit licenseExpired();
        return;
    }
    
    int daysRemaining = d_ptr->currentLicense.daysRemaining();
    if (daysRemaining <= 7) {
        emit licenseExpiring(daysRemaining);
    }
}

QString LicenseManager::generateHardwareFingerprint() const
{
    QStringList components;
    
    // CPU information
    components << QSysInfo::currentCpuArchitecture();
    
    // MAC addresses (simplified for compatibility)
    components << QSysInfo::machineHostName();
    components << QSysInfo::currentCpuArchitecture();

    // System information
    components << QSysInfo::kernelType();
    components << QSysInfo::kernelVersion();
    components << QSysInfo::productType();
    components << QSysInfo::productVersion();
    
    // Sort to ensure consistent ordering
    components.sort();
    QString combined = components.join("|");
    
    return QString::fromUtf8(QCryptographicHash::hash(combined.toUtf8(), QCryptographicHash::Sha256).toHex());
}

QString LicenseManager::generateLicenseKey() const
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces).toUpper();
}

// Production-grade cryptographic implementations
QString LicenseManager::signLicense(const QString& licenseData) const
{
#ifdef PRODUCTION_BUILD
    // Use RSA private key signing with SHA-256
    if (d_ptr->privateKeyPath.isEmpty()) {
        return QString();
    }

    QFile keyFile(d_ptr->privateKeyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        return QString();
    }

    QByteArray keyData = keyFile.readAll();
    BIO* bio = BIO_new_mem_buf(keyData.data(), keyData.length());
    RSA* rsa = PEM_read_bio_RSAPrivateKey(bio, nullptr, nullptr, nullptr);
    BIO_free(bio);

    if (!rsa) {
        return QString();
    }

    QByteArray dataToSign = licenseData.toUtf8();
    QByteArray hash = QCryptographicHash::hash(dataToSign, QCryptographicHash::Sha256);

    unsigned char signature[RSA_size(rsa)];
    unsigned int sigLen;

    int result = RSA_sign(NID_sha256,
                         reinterpret_cast<const unsigned char*>(hash.data()),
                         hash.length(),
                         signature,
                         &sigLen,
                         rsa);

    RSA_free(rsa);

    if (result == 1) {
        return QString::fromUtf8(QByteArray(reinterpret_cast<char*>(signature), sigLen).toBase64());
    }

    return QString();
#else
    // Enhanced fallback for non-production builds with HMAC-like signing
    QString secretKey = "FuxiLicenseSecretKey2024" + getHardwareFingerprint();
    QByteArray combined = licenseData.toUtf8() + secretKey.toUtf8();
    QByteArray hash = QCryptographicHash::hash(combined, QCryptographicHash::Sha256);

    // Add timestamp to make signatures unique
    QString timestamp = QString::number(QDateTime::currentMSecsSinceEpoch());
    QByteArray finalHash = QCryptographicHash::hash(hash + timestamp.toUtf8(), QCryptographicHash::Sha256);

    return QString::fromUtf8(finalHash.toHex()) + ":" + timestamp;
#endif
}

bool LicenseManager::verifySignature(const QString& licenseData, const QString& signature) const
{
#ifdef PRODUCTION_BUILD
    // Use RSA public key verification with SHA-256
    if (d_ptr->publicKeyPath.isEmpty()) {
        return false;
    }

    QFile keyFile(d_ptr->publicKeyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        return false;
    }

    QByteArray keyData = keyFile.readAll();
    BIO* bio = BIO_new_mem_buf(keyData.data(), keyData.length());
    RSA* rsa = PEM_read_bio_RSA_PUBKEY(bio, nullptr, nullptr, nullptr);
    BIO_free(bio);

    if (!rsa) {
        return false;
    }

    QByteArray dataToVerify = licenseData.toUtf8();
    QByteArray hash = QCryptographicHash::hash(dataToVerify, QCryptographicHash::Sha256);
    QByteArray sigBytes = QByteArray::fromBase64(signature.toUtf8());

    int result = RSA_verify(NID_sha256,
                           reinterpret_cast<const unsigned char*>(hash.data()),
                           hash.length(),
                           reinterpret_cast<const unsigned char*>(sigBytes.data()),
                           sigBytes.length(),
                           rsa);

    RSA_free(rsa);
    return result == 1;
#else
    // Enhanced fallback for non-production builds
    if (!signature.contains(":")) {
        // Old simple hash verification
        QByteArray hash = QCryptographicHash::hash(licenseData.toUtf8(), QCryptographicHash::Sha256);
        return signature == QString::fromUtf8(hash.toHex());
    }

    // New HMAC-like verification
    QStringList parts = signature.split(":");
    if (parts.size() != 2) {
        return false;
    }

    QString hashPart = parts[0];
    QString timestamp = parts[1];

    QString secretKey = "FuxiLicenseSecretKey2024" + getHardwareFingerprint();
    QByteArray combined = licenseData.toUtf8() + secretKey.toUtf8();
    QByteArray hash = QCryptographicHash::hash(combined, QCryptographicHash::Sha256);
    QByteArray finalHash = QCryptographicHash::hash(hash + timestamp.toUtf8(), QCryptographicHash::Sha256);

    return hashPart == QString::fromUtf8(finalHash.toHex());
#endif
}

QString LicenseManager::encryptLicenseData(const QString& data) const
{
    // Use QAESEncryption library for AES-256-CBC encryption
    QString hwFingerprint = getHardwareFingerprint();
    QByteArray key = QCryptographicHash::hash(hwFingerprint.toUtf8(), QCryptographicHash::Sha256);
    QByteArray iv = generateSecureRandom(16);  // 128-bit IV for AES

    QByteArray dataBytes = data.toUtf8();

    // Use QAESEncryption for encryption
    QByteArray encrypted = QAESEncryption::Crypt(QAESEncryption::AES_256,
                                                QAESEncryption::CBC,
                                                dataBytes,
                                                key,
                                                iv,
                                                QAESEncryption::PKCS7);

    if (encrypted.isEmpty()) {
        return QString();
    }

    // Create container with IV and encrypted data
    QJsonObject container;
    container["iv"] = QString::fromUtf8(iv.toHex());
    container["data"] = QString::fromUtf8(encrypted.toHex());
    container["version"] = "QAES256";
    container["mode"] = "CBC";
    container["padding"] = "PKCS7";

    QJsonDocument doc(container);
    return QString::fromUtf8(doc.toJson(QJsonDocument::Compact).toBase64());
}

QString LicenseManager::decryptLicenseData(const QString& encryptedData) const
{
    // Decrypt using QAESEncryption library
    QByteArray containerData = QByteArray::fromBase64(encryptedData.toUtf8());
    QJsonDocument doc = QJsonDocument::fromJson(containerData);

    if (!doc.isObject()) {
        return QString();
    }

    QJsonObject container = doc.object();

    // Check version for compatibility
    QString version = container["version"].toString();
    if (version == "QAES256") {
        // New QAESEncryption format
        QByteArray iv = QByteArray::fromHex(container["iv"].toString().toUtf8());
        QByteArray encrypted = QByteArray::fromHex(container["data"].toString().toUtf8());
        QString mode = container["mode"].toString();
        QString padding = container["padding"].toString();

        if (iv.isEmpty() || encrypted.isEmpty()) {
            return QString();
        }

        QString hwFingerprint = getHardwareFingerprint();
        QByteArray key = QCryptographicHash::hash(hwFingerprint.toUtf8(), QCryptographicHash::Sha256);

        // Use QAESEncryption for decryption
        QByteArray decrypted = QAESEncryption::Decrypt(QAESEncryption::AES_256,
                                                      QAESEncryption::CBC,
                                                      encrypted,
                                                      key,
                                                      iv,
                                                      QAESEncryption::PKCS7);

        // Remove padding
        decrypted = QAESEncryption::RemovePadding(decrypted, QAESEncryption::PKCS7);
        return QString::fromUtf8(decrypted);
    }
    else if (version == "AES256") {
        // Legacy format - try to decrypt with old method
        return decryptLegacyAESData(encryptedData);
    }
    else {
        // Try legacy XOR decryption for backward compatibility
        return decryptLegacyXORData(encryptedData);
    }
}

QByteArray LicenseManager::hashString(const QString& input) const
{
    return QCryptographicHash::hash(input.toUtf8(), QCryptographicHash::Sha256);
}

QString LicenseManager::base64Encode(const QByteArray& data) const
{
    return QString::fromUtf8(data.toBase64());
}

QByteArray LicenseManager::base64Decode(const QString& data) const
{
    return QByteArray::fromBase64(data.toUtf8());
}

// RSA key management (placeholder implementations)
RSA* LicenseManager::LicenseManagerPrivate::loadPrivateKey() const
{
    // In production, load actual RSA private key from file
    return nullptr;
}

RSA* LicenseManager::LicenseManagerPrivate::loadPublicKey() const
{
    // In production, load actual RSA public key from file
    return nullptr;
}

void LicenseManager::LicenseManagerPrivate::freeRSAKey(RSA* key) const
{
#ifdef PRODUCTION_BUILD
    if (key) {
        RSA_free(key);
    }
#else
    Q_UNUSED(key)
#endif
}

// Production security method implementations
void LicenseManager::enableOnlineValidation(bool enable)
{
    d_ptr->onlineValidationEnabled = enable;
}

void LicenseManager::setLicenseServerUrl(const QString& url)
{
    d_ptr->licenseServerUrl = url;
}

bool LicenseManager::performOnlineValidation()
{
    if (!d_ptr->onlineValidationEnabled || d_ptr->licenseServerUrl.isEmpty()) {
        return false;
    }

    QNetworkRequest request(QUrl(d_ptr->licenseServerUrl + "/validate"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    QJsonObject payload;
    payload["licenseKey"] = d_ptr->currentLicense.licenseKey;
    payload["hardwareFingerprint"] = getHardwareFingerprint();
    payload["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(payload);
    d_ptr->networkManager->post(request, doc.toJson());

    return true;
}

void LicenseManager::onOnlineValidationFinished()
{
    QNetworkReply* reply = qobject_cast<QNetworkReply*>(sender());
    if (!reply) {
        return;
    }

    bool success = false;
    if (reply->error() == QNetworkReply::NoError) {
        QJsonDocument doc = QJsonDocument::fromJson(reply->readAll());
        if (doc.isObject()) {
            QJsonObject response = doc.object();
            success = response["valid"].toBool();
        }
    }

    emit onlineValidationCompleted(success);
    reply->deleteLater();
}

bool LicenseManager::verifyApplicationIntegrity() const
{
    QString currentHash = calculateExecutableHash();
    if (d_ptr->lastExecutableHash.isEmpty()) {
        d_ptr->lastExecutableHash = currentHash;
        return true;
    }

    bool isValid = (currentHash == d_ptr->lastExecutableHash);
    if (!isValid) {
        emit const_cast<LicenseManager*>(this)->tamperingDetected("Executable hash mismatch");
    }

    return isValid;
}

bool LicenseManager::detectDebugging() const
{
    bool debuggerDetected = isDebuggerPresent();
    if (debuggerDetected) {
        emit const_cast<LicenseManager*>(this)->debuggingDetected();
        emit const_cast<LicenseManager*>(this)->securityThreatDetected("Debugger");
    }
    return debuggerDetected;
}

bool LicenseManager::detectVirtualMachine() const
{
    bool vmDetected = isRunningInVM();
    if (vmDetected) {
        emit const_cast<LicenseManager*>(this)->securityThreatDetected("Virtual Machine");
    }
    return vmDetected;
}

QString LicenseManager::getEnhancedHardwareFingerprint() const
{
    QStringList fingerprints = getSystemFingerprints();
    QString combined = fingerprints.join("|");
    return QString::fromUtf8(QCryptographicHash::hash(combined.toUtf8(), QCryptographicHash::Sha256).toHex());
}

void LicenseManager::performSecurityCheck()
{
    // Check for tampering
    if (!verifyApplicationIntegrity()) {
        return;
    }

    // Check for debugging
    if (detectDebugging()) {
        return;
    }

    // Check for virtual machine
    detectVirtualMachine();

    // Perform online validation if enabled
    if (d_ptr->onlineValidationEnabled) {
        performOnlineValidation();
    }
}

// Utility method implementations
QByteArray LicenseManager::generateSecureRandom(int length) const
{
#ifdef PRODUCTION_BUILD
    QByteArray random(length, 0);
    if (RAND_bytes(reinterpret_cast<unsigned char*>(random.data()), length) != 1) {
        // Fallback to Qt's random generator
        return QRandomGenerator::global()->generate64();
    }
    return random;
#else
    QByteArray random;
    for (int i = 0; i < length; ++i) {
        random.append(static_cast<char>(QRandomGenerator::global()->bounded(256)));
    }
    return random;
#endif
}

QString LicenseManager::deriveKeyFromPassword(const QString& password, const QByteArray& salt) const
{
#ifdef PRODUCTION_BUILD
    const int keyLength = 32; // 256 bits
    const int iterations = 100000; // PBKDF2 iterations

    unsigned char key[keyLength];
    if (PKCS5_PBKDF2_HMAC(password.toUtf8().data(), password.toUtf8().length(),
                          reinterpret_cast<const unsigned char*>(salt.data()), salt.length(),
                          iterations, EVP_sha256(), keyLength, key) != 1) {
        return QString();
    }

    return QString::fromUtf8(QByteArray(reinterpret_cast<char*>(key), keyLength).toHex());
#else
    // Simple fallback
    QByteArray combined = password.toUtf8() + salt;
    return QString::fromUtf8(QCryptographicHash::hash(combined, QCryptographicHash::Sha256).toHex());
#endif
}

QByteArray LicenseManager::aesEncrypt(const QByteArray& data, const QByteArray& key, const QByteArray& iv) const
{
#ifdef PRODUCTION_BUILD
    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) return QByteArray();

    if (EVP_EncryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr,
                          reinterpret_cast<const unsigned char*>(key.data()),
                          reinterpret_cast<const unsigned char*>(iv.data())) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }

    QByteArray encrypted(data.length() + 16, 0); // Extra space for tag
    int len;
    int encryptedLen = 0;

    if (EVP_EncryptUpdate(ctx, reinterpret_cast<unsigned char*>(encrypted.data()), &len,
                         reinterpret_cast<const unsigned char*>(data.data()), data.length()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    encryptedLen = len;

    if (EVP_EncryptFinal_ex(ctx, reinterpret_cast<unsigned char*>(encrypted.data()) + len, &len) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    encryptedLen += len;

    // Get the tag
    unsigned char tag[16];
    if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_GET_TAG, 16, tag) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }

    EVP_CIPHER_CTX_free(ctx);

    encrypted.resize(encryptedLen);
    encrypted.append(reinterpret_cast<char*>(tag), 16);

    return encrypted;
#else
    // Simplified AES-like encryption for non-production builds
    if (key.length() < 32 || iv.length() < 16) {
        return QByteArray(); // Invalid key or IV size
    }

    QByteArray result;
    QByteArray expandedKey = key;

    // Expand key to multiple rounds (simplified key schedule)
    for (int round = 0; round < 10; ++round) {
        QByteArray roundKey = QCryptographicHash::hash(expandedKey + QByteArray::number(round), QCryptographicHash::Sha256);
        expandedKey.append(roundKey);
    }

    // Process data in 16-byte blocks (AES block size)
    for (int blockStart = 0; blockStart < data.length(); blockStart += 16) {
        QByteArray block = data.mid(blockStart, 16);

        // Pad last block if necessary
        while (block.length() < 16) {
            block.append(static_cast<char>(16 - block.length())); // PKCS#7 padding
        }

        // Apply multiple rounds of transformation
        for (int round = 0; round < 10; ++round) {
            QByteArray roundKey = expandedKey.mid(round * 32, 32);
            QByteArray roundIV = iv;

            // Mix with round key and IV
            for (int i = 0; i < 16; ++i) {
                block[i] = block[i] ^ roundKey[i % 32] ^ roundIV[i % 16];
            }

            // Simple substitution (S-box like)
            for (int i = 0; i < 16; ++i) {
                unsigned char byte = static_cast<unsigned char>(block[i]);
                byte = ((byte << 1) | (byte >> 7)) ^ 0x63; // Simplified S-box
                block[i] = static_cast<char>(byte);
            }

            // Simple permutation (shift rows like)
            QByteArray temp = block;
            for (int i = 0; i < 16; ++i) {
                block[i] = temp[(i + round + 1) % 16];
            }
        }

        result.append(block);
    }

    return result;
#endif
}

QByteArray LicenseManager::aesDecrypt(const QByteArray& encryptedData, const QByteArray& key, const QByteArray& iv) const
{
#ifdef PRODUCTION_BUILD
    if (encryptedData.length() < 16) return QByteArray(); // Need at least tag

    QByteArray ciphertext = encryptedData.left(encryptedData.length() - 16);
    QByteArray tag = encryptedData.right(16);

    EVP_CIPHER_CTX* ctx = EVP_CIPHER_CTX_new();
    if (!ctx) return QByteArray();

    if (EVP_DecryptInit_ex(ctx, EVP_aes_256_gcm(), nullptr,
                          reinterpret_cast<const unsigned char*>(key.data()),
                          reinterpret_cast<const unsigned char*>(iv.data())) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }

    QByteArray decrypted(ciphertext.length(), 0);
    int len;
    int decryptedLen = 0;

    if (EVP_DecryptUpdate(ctx, reinterpret_cast<unsigned char*>(decrypted.data()), &len,
                         reinterpret_cast<const unsigned char*>(ciphertext.data()), ciphertext.length()) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }
    decryptedLen = len;

    // Set expected tag
    if (EVP_CIPHER_CTX_ctrl(ctx, EVP_CTRL_GCM_SET_TAG, 16,
                           const_cast<unsigned char*>(reinterpret_cast<const unsigned char*>(tag.data()))) != 1) {
        EVP_CIPHER_CTX_free(ctx);
        return QByteArray();
    }

    int ret = EVP_DecryptFinal_ex(ctx, reinterpret_cast<unsigned char*>(decrypted.data()) + len, &len);
    EVP_CIPHER_CTX_free(ctx);

    if (ret <= 0) {
        return QByteArray(); // Authentication failed
    }

    decryptedLen += len;
    decrypted.resize(decryptedLen);

    return decrypted;
#else
    // Simplified AES-like decryption for non-production builds
    if (key.length() < 32 || iv.length() < 16 || encryptedData.isEmpty()) {
        return QByteArray(); // Invalid parameters
    }

    QByteArray result;
    QByteArray expandedKey = key;

    // Expand key to multiple rounds (same as encryption)
    for (int round = 0; round < 10; ++round) {
        QByteArray roundKey = QCryptographicHash::hash(expandedKey + QByteArray::number(round), QCryptographicHash::Sha256);
        expandedKey.append(roundKey);
    }

    // Process data in 16-byte blocks
    for (int blockStart = 0; blockStart < encryptedData.length(); blockStart += 16) {
        QByteArray block = encryptedData.mid(blockStart, 16);

        if (block.length() != 16) {
            break; // Invalid block size
        }

        // Apply inverse transformations in reverse order
        for (int round = 9; round >= 0; --round) {
            QByteArray roundKey = expandedKey.mid(round * 32, 32);
            QByteArray roundIV = iv;

            // Inverse permutation (reverse shift rows)
            QByteArray temp = block;
            for (int i = 0; i < 16; ++i) {
                block[(i + round + 1) % 16] = temp[i];
            }

            // Inverse substitution (reverse S-box)
            for (int i = 0; i < 16; ++i) {
                unsigned char byte = static_cast<unsigned char>(block[i]);
                byte = byte ^ 0x63; // Reverse XOR
                byte = ((byte >> 1) | (byte << 7)); // Reverse rotation
                block[i] = static_cast<char>(byte);
            }

            // Inverse mix with round key and IV
            for (int i = 0; i < 16; ++i) {
                block[i] = block[i] ^ roundKey[i % 32] ^ roundIV[i % 16];
            }
        }

        result.append(block);
    }

    // Remove PKCS#7 padding from last block
    if (!result.isEmpty()) {
        unsigned char paddingLength = static_cast<unsigned char>(result.at(result.length() - 1));
        if (paddingLength > 0 && paddingLength <= 16) {
            // Verify padding
            bool validPadding = true;
            for (int i = 1; i <= paddingLength; ++i) {
                if (result.at(result.length() - i) != static_cast<char>(paddingLength)) {
                    validPadding = false;
                    break;
                }
            }
            if (validPadding) {
                result = result.left(result.length() - paddingLength);
            }
        }
    }

    return result;
#endif
}

// Anti-tampering method implementations
QString LicenseManager::calculateExecutableHash() const
{
    QString executablePath = QCoreApplication::applicationFilePath();
    QFile file(executablePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }

    QByteArray fileData = file.readAll();
    return QString::fromUtf8(QCryptographicHash::hash(fileData, QCryptographicHash::Sha256).toHex());
}

bool LicenseManager::isDebuggerPresent() const
{
#ifdef Q_OS_WIN
    // Check for debugger using Windows API
    if (IsDebuggerPresent()) {
        return true;
    }

    // Additional check using NtQueryInformationProcess
    HANDLE hProcess = GetCurrentProcess();
    BOOL debuggerPresent = FALSE;

    typedef NTSTATUS (WINAPI *NtQueryInformationProcess_t)(
        HANDLE ProcessHandle,
        DWORD ProcessInformationClass,
        PVOID ProcessInformation,
        ULONG ProcessInformationLength,
        PULONG ReturnLength
    );

    HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
    if (hNtdll) {
        NtQueryInformationProcess_t NtQueryInformationProcess =
            (NtQueryInformationProcess_t)GetProcAddress(hNtdll, "NtQueryInformationProcess");

        if (NtQueryInformationProcess) {
            DWORD debugPort = 0;
            NTSTATUS status = NtQueryInformationProcess(hProcess, 7, &debugPort, sizeof(debugPort), nullptr);
            if (status == 0 && debugPort != 0) {
                return true;
            }
        }
    }

    // Check for common debugger processes
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32 pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32);

        if (Process32First(hSnapshot, &pe32)) {
            do {
                QString processName = QString::fromLocal8Bit(pe32.szExeFile).toLower();
                if (processName.contains("ollydbg") ||
                    processName.contains("x64dbg") ||
                    processName.contains("windbg") ||
                    processName.contains("ida") ||
                    processName.contains("cheatengine")) {
                    CloseHandle(hSnapshot);
                    return true;
                }
            } while (Process32Next(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }

    return false;
#else
    // Linux/Mac implementation would go here
    QProcess process;
    process.start("ps", QStringList() << "-eo" << "comm");
    process.waitForFinished();

    QString output = process.readAllStandardOutput();
    QStringList debuggers;
    debuggers << "gdb" << "lldb" << "strace" << "ltrace";

    for (int idx = 0; idx < debuggers.size(); ++idx) {
        const QString& debugger = debuggers.at(idx);
        if (output.contains(debugger)) {
            return true;
        }
    }

    return false;
#endif
}

bool LicenseManager::isRunningInVM() const
{
#ifdef Q_OS_WIN
    // Check for VM-specific registry keys
    QSettings vmwareReg("HKEY_LOCAL_MACHINE\\SOFTWARE\\VMware, Inc.\\VMware Tools", QSettings::NativeFormat);
    if (vmwareReg.contains("InstallPath")) {
        return true;
    }

    QSettings vboxReg("HKEY_LOCAL_MACHINE\\SOFTWARE\\Oracle\\VirtualBox Guest Additions", QSettings::NativeFormat);
    if (vboxReg.contains("Version")) {
        return true;
    }

    // Check for VM-specific hardware
    QStringList vmIndicators;
    vmIndicators << "VMware" << "VirtualBox" << "QEMU" << "Xen"
                 << "Microsoft Corporation" << "Parallels" << "Red Hat" << "oVirt";

    QString systemInfo = QSysInfo::prettyProductName();
    for (int idx = 0; idx < vmIndicators.size(); ++idx) {
        const QString& indicator = vmIndicators.at(idx);
        if (systemInfo.contains(indicator, Qt::CaseInsensitive)) {
            return true;
        }
    }

    // Simplified VM detection
     systemInfo = QSysInfo::prettyProductName();
    if (systemInfo.contains("VMware", Qt::CaseInsensitive) ||
        systemInfo.contains("VirtualBox", Qt::CaseInsensitive) ||
        systemInfo.contains("QEMU", Qt::CaseInsensitive)) {
        return true;
    }

    return false;
#else
    // Linux/Mac VM detection
    QFile cpuinfo("/proc/cpuinfo");
    if (cpuinfo.open(QIODevice::ReadOnly)) {
        QString content = cpuinfo.readAll();
        if (content.contains("hypervisor") || content.contains("VMware") || content.contains("VirtualBox")) {
            return true;
        }
    }

    QFile dmi("/sys/class/dmi/id/product_name");
    if (dmi.open(QIODevice::ReadOnly)) {
        QString product = dmi.readAll().trimmed();
        if (product.contains("VMware") || product.contains("VirtualBox") || product.contains("QEMU")) {
            return true;
        }
    }

    return false;
#endif
}

QStringList LicenseManager::getSystemFingerprints() const
{
    QStringList fingerprints;

    // CPU information
    fingerprints << QSysInfo::currentCpuArchitecture();
    fingerprints << QSysInfo::buildCpuArchitecture();

    // System information
    fingerprints << QSysInfo::kernelType();
    fingerprints << QSysInfo::kernelVersion();
    fingerprints << QSysInfo::productType();
    fingerprints << QSysInfo::productVersion();

    // System fingerprints (simplified for compatibility)
    fingerprints << QSysInfo::machineHostName();
    fingerprints << QSysInfo::currentCpuArchitecture();
    fingerprints << QSysInfo::buildCpuArchitecture();
    fingerprints << QSysInfo::kernelType();
    fingerprints << QSysInfo::kernelVersion();
    fingerprints << QSysInfo::productType();
    fingerprints << QSysInfo::productVersion();

    // Additional Windows-specific fingerprints
#ifdef Q_OS_WIN
    QSettings reg("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography", QSettings::NativeFormat);
    QString machineGuid = reg.value("MachineGuid").toString();
    if (!machineGuid.isEmpty()) {
        fingerprints << machineGuid;
    }

    QSettings biosReg("HKEY_LOCAL_MACHINE\\HARDWARE\\DESCRIPTION\\System\\BIOS", QSettings::NativeFormat);
    fingerprints << biosReg.value("BIOSVersion").toString();
    fingerprints << biosReg.value("SystemManufacturer").toString();
    fingerprints << biosReg.value("SystemProductName").toString();
#endif

    // Remove empty entries and sort for consistency
    fingerprints.removeAll("");
    fingerprints.sort();

    return fingerprints;
}

// Legacy AES decryption for backward compatibility with old AES256 format
QString LicenseManager::decryptLegacyAESData(const QString& encryptedData) const
{
    QByteArray containerData = QByteArray::fromBase64(encryptedData.toUtf8());
    QJsonDocument doc = QJsonDocument::fromJson(containerData);

    if (!doc.isObject()) {
        return QString();
    }

    QJsonObject container = doc.object();
    QByteArray iv = QByteArray::fromHex(container["iv"].toString().toUtf8());
    QByteArray encrypted = QByteArray::fromHex(container["data"].toString().toUtf8());

    if (iv.isEmpty() || encrypted.isEmpty()) {
        return QString();
    }

    QString hwFingerprint = getHardwareFingerprint();
    QByteArray key = QCryptographicHash::hash(hwFingerprint.toUtf8(), QCryptographicHash::Sha256);

    QByteArray decrypted = aesDecrypt(encrypted, key, iv);
    return QString::fromUtf8(decrypted);
}

// Legacy XOR decryption for backward compatibility
QString LicenseManager::decryptLegacyXORData(const QString& encryptedData) const
{
    QByteArray encrypted = QByteArray::fromBase64(encryptedData.toUtf8());

    if (encrypted.length() < 16) { // Need at least MD5 checksum
        return QString();
    }

    // Extract checksum and data
    QByteArray checksum = encrypted.right(16);
    QByteArray dataBytes = encrypted.left(encrypted.length() - 16);

    QString hwFingerprint = getHardwareFingerprint();
    QByteArray key = QCryptographicHash::hash(hwFingerprint.toUtf8(), QCryptographicHash::Sha256);

    // Simple XOR decryption
    QByteArray decrypted;
    for (int i = 0; i < dataBytes.length(); ++i) {
        decrypted.append(dataBytes[i] ^ key[i % key.length()]);
    }

    // Verify checksum
    QByteArray expectedChecksum = QCryptographicHash::hash(decrypted, QCryptographicHash::Md5);
    if (checksum != expectedChecksum) {
        return QString(); // Checksum mismatch
    }

    return QString::fromUtf8(decrypted);
}

// RSA key management methods (placeholder implementations)
RSA* LicenseManager::loadRSAPrivateKey(const QString& keyPath) const
{
#ifdef PRODUCTION_BUILD
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        return nullptr;
    }

    QByteArray keyData = keyFile.readAll();
    BIO* bio = BIO_new_mem_buf(keyData.data(), keyData.length());
    RSA* rsa = PEM_read_bio_RSAPrivateKey(bio, nullptr, nullptr, nullptr);
    BIO_free(bio);

    return rsa;
#else
    Q_UNUSED(keyPath)
    return nullptr;
#endif
}

RSA* LicenseManager::loadRSAPublicKey(const QString& keyPath) const
{
#ifdef PRODUCTION_BUILD
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        return nullptr;
    }

    QByteArray keyData = keyFile.readAll();
    BIO* bio = BIO_new_mem_buf(keyData.data(), keyData.length());
    RSA* rsa = PEM_read_bio_RSA_PUBKEY(bio, nullptr, nullptr, nullptr);
    BIO_free(bio);

    return rsa;
#else
    Q_UNUSED(keyPath)
    return nullptr;
#endif
}

} // namespace Core
} // namespace Fuxi
