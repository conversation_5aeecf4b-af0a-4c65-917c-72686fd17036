#include "management/LogUtils.h"
#include <QDir>
#include <QFileInfo>
#include <QCoreApplication>
#include <iostream>

// 静态成员变量定义
LogUtils::LogLevel LogUtils::s_logLevel = LogLevel::Info;
LogUtils::LogTarget LogUtils::s_logTarget = LogTarget::Console;
QString LogUtils::s_logFile;
QString LogUtils::s_logFormat = "{timestamp} [{level}] {message}";
QMutex LogUtils::s_mutex;
std::unique_ptr<QFile> LogUtils::s_file;
std::unique_ptr<QTextStream> LogUtils::s_stream;
std::function<void(LogUtils::LogLevel, const QString&, const QString&)> LogUtils::s_customHandler;

bool LogUtils::s_rotationEnabled = false;
qint64 LogUtils::s_maxFileSize = 10 * 1024 * 1024; // 10MB
int LogUtils::s_maxFiles = 5;

void LogUtils::initialize(LogLevel level, LogTarget target, const QString& logFile)
{
    QMutexLocker locker(&s_mutex);
    
    s_logLevel = level;
    s_logTarget = target;
    
    if (!logFile.isEmpty()) {
        setLogFile(logFile);
    }
}

void LogUtils::setLogLevel(LogLevel level)
{
    QMutexLocker locker(&s_mutex);
    s_logLevel = level;
}

void LogUtils::setLogTarget(LogTarget target)
{
    QMutexLocker locker(&s_mutex);
    s_logTarget = target;
}

void LogUtils::setLogFile(const QString& filePath)
{
    QMutexLocker locker(&s_mutex);
    
    s_logFile = filePath;
    
    // 关闭现有文件
    if (s_stream) {
        s_stream->flush();
        s_stream.reset();
    }
    if (s_file) {
        s_file->close();
        s_file.reset();
    }
    
    if (!filePath.isEmpty()) {
        // 确保目录存在
        QFileInfo fileInfo(filePath);
        QDir dir = fileInfo.absoluteDir();
        if (!dir.exists()) {
            dir.mkpath(".");
        }
        
        s_file = std::make_unique<QFile>(filePath);
        if (s_file->open(QIODevice::WriteOnly | QIODevice::Append)) {
            s_stream = std::make_unique<QTextStream>(s_file.get());
            s_stream->setCodec("UTF-8");
        }
    }
}

void LogUtils::setLogFormat(const QString& format)
{
    QMutexLocker locker(&s_mutex);
    s_logFormat = format;
}

void LogUtils::setLogRotation(bool enabled, qint64 maxSize, int maxFiles)
{
    QMutexLocker locker(&s_mutex);
    s_rotationEnabled = enabled;
    s_maxFileSize = maxSize;
    s_maxFiles = maxFiles;
}

void LogUtils::log(LogLevel level, const QString& message, const char* file, int line, const char* function)
{
    if (level < s_logLevel) return;
    
    QMutexLocker locker(&s_mutex);
    
    QString formattedMessage = formatMessage(level, message, file, line, function);
    
    // 自定义处理器优先
    if (s_customHandler) {
        s_customHandler(level, message, formattedMessage);
        return;
    }
    
    // 输出到控制台
    if (s_logTarget == LogTarget::Console || s_logTarget == LogTarget::Both) {
        writeToConsole(formattedMessage);
    }
    
    // 输出到文件
    if ((s_logTarget == LogTarget::File || s_logTarget == LogTarget::Both) && s_stream) {
        checkAndRotateFile();
        writeToFile(formattedMessage);
    }
}

void LogUtils::trace(const QString& message, const char* file, int line, const char* function)
{
    log(LogLevel::Trace, message, file, line, function);
}

void LogUtils::debug(const QString& message, const char* file, int line, const char* function)
{
    log(LogLevel::Debug, message, file, line, function);
}

void LogUtils::info(const QString& message, const char* file, int line, const char* function)
{
    log(LogLevel::Info, message, file, line, function);
}

void LogUtils::warning(const QString& message, const char* file, int line, const char* function)
{
    log(LogLevel::Warning, message, file, line, function);
}

void LogUtils::error(const QString& message, const char* file, int line, const char* function)
{
    log(LogLevel::Error, message, file, line, function);
}

void LogUtils::fatal(const QString& message, const char* file, int line, const char* function)
{
    log(LogLevel::Fatal, message, file, line, function);
}

QString LogUtils::levelToString(LogLevel level)
{
    switch (level) {
        case LogLevel::Trace: return "TRACE";
        case LogLevel::Debug: return "DEBUG";
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Fatal: return "FATAL";
        default: return "UNKNOWN";
    }
}

LogUtils::LogLevel LogUtils::stringToLevel(const QString& levelStr)
{
    QString upper = levelStr.toUpper();
    if (upper == "TRACE") return LogLevel::Trace;
    if (upper == "DEBUG") return LogLevel::Debug;
    if (upper == "INFO") return LogLevel::Info;
    if (upper == "WARN" || upper == "WARNING") return LogLevel::Warning;
    if (upper == "ERROR") return LogLevel::Error;
    if (upper == "FATAL") return LogLevel::Fatal;
    return LogLevel::Info;
}

void LogUtils::setCustomHandler(std::function<void(LogLevel, const QString&, const QString&)> handler)
{
    QMutexLocker locker(&s_mutex);
    s_customHandler = handler;
}

void LogUtils::cleanup()
{
    QMutexLocker locker(&s_mutex);
    
    if (s_stream) {
        s_stream->flush();
        s_stream.reset();
    }
    if (s_file) {
        s_file->close();
        s_file.reset();
    }
    
    s_customHandler = nullptr;
}

void LogUtils::flush()
{
    QMutexLocker locker(&s_mutex);
    
    if (s_stream) {
        s_stream->flush();
    }
    
    std::cout.flush();
    std::cerr.flush();
}

qint64 LogUtils::getLogFileSize()
{
    QMutexLocker locker(&s_mutex);
    
    if (s_file) {
        return s_file->size();
    }
    return 0;
}

void LogUtils::rotateLogFile()
{
    if (s_logFile.isEmpty()) return;
    
    QMutexLocker locker(&s_mutex);
    
    // 关闭当前文件
    if (s_stream) {
        s_stream->flush();
        s_stream.reset();
    }
    if (s_file) {
        s_file->close();
        s_file.reset();
    }
    
    // 轮转文件
    QFileInfo fileInfo(s_logFile);
    QString baseName = fileInfo.completeBaseName();
    QString suffix = fileInfo.suffix();
    QString dir = fileInfo.absolutePath();
    
    // 删除最老的文件
    QString oldestFile = QString("%1/%2.%3.%4").arg(dir, baseName).arg(s_maxFiles - 1).arg(suffix);
    QFile::remove(oldestFile);
    
    // 重命名现有文件
    for (int i = s_maxFiles - 2; i >= 0; --i) {
        QString oldName = (i == 0) ? s_logFile : QString("%1/%2.%3.%4").arg(dir, baseName).arg(i).arg(suffix);
        QString newName = QString("%1/%2.%3.%4").arg(dir, baseName).arg(i + 1).arg(suffix);
        
        if (QFile::exists(oldName)) {
            QFile::remove(newName);
            QFile::rename(oldName, newName);
        }
    }
    
    // 重新打开文件
    setLogFile(s_logFile);
}

// 私有辅助函数实现
void LogUtils::writeToConsole(const QString& formattedMessage)
{
    std::cout << formattedMessage.toStdString() << std::endl;
}

void LogUtils::writeToFile(const QString& formattedMessage)
{
    if (s_stream) {
        *s_stream << formattedMessage << Qt::endl;
        s_stream->flush();
    }
}

QString LogUtils::formatMessage(LogLevel level, const QString& message,
                               const char* file, int line, const char* function)
{
    QString result = s_logFormat;

    // 替换占位符
    result.replace("{timestamp}", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz"));
    result.replace("{level}", levelToString(level));
    result.replace("{message}", message);

    if (file) {
        result.replace("{file}", extractFileName(file));
    } else {
        result.replace("{file}", "");
    }

    if (line > 0) {
        result.replace("{line}", QString::number(line));
    } else {
        result.replace("{line}", "");
    }

    if (function) {
        result.replace("{function}", QString(function));
    } else {
        result.replace("{function}", "");
    }

    return result;
}

void LogUtils::checkAndRotateFile()
{
    if (!s_rotationEnabled || !s_file) return;

    if (s_file->size() >= s_maxFileSize) {
        rotateLogFile();
    }
}

QString LogUtils::extractFileName(const char* filePath)
{
    if (!filePath) return "";

    QString path(filePath);
    int lastSlash = path.lastIndexOf('/');
    int lastBackslash = path.lastIndexOf('\\');
    int lastSeparator = qMax(lastSlash, lastBackslash);

    if (lastSeparator >= 0) {
        return path.mid(lastSeparator + 1);
    }

    return path;
}
