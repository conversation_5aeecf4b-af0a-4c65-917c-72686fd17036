#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <future>
#include <concepts>
#include <span>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#include <setupapi.h>
#include <devguid.h>
#pragma comment(lib, "setupapi.lib")
#endif

namespace fuxicore {

    // C++20 concepts for type safety
    template<typename T>
    concept SerialData = std::is_trivially_copyable_v<T> || std::same_as<T, std::string> || std::same_as<T, std::vector<uint8_t>>;

    enum class SerialParity {
        None,
        Odd,
        Even,
        Mark,
        Space
    };

    enum class SerialStopBits {
        One,
        OnePointFive,
        Two
    };

    enum class SerialFlowControl {
        None,
        Hardware,
        Software
    };

    enum class SerialState {
        Closed,
        Opening,
        Open,
        Error
    };

    struct SerialConfig {
        std::string port_name = "COM1";
        uint32_t baud_rate = 9600;
        uint8_t data_bits = 8;
        SerialParity parity = SerialParity::None;
        SerialStopBits stop_bits = SerialStopBits::One;
        SerialFlowControl flow_control = SerialFlowControl::None;
        uint32_t read_timeout_ms = 1000;
        uint32_t write_timeout_ms = 1000;
        size_t buffer_size = 4096;
        bool enable_dtr = false;
        bool enable_rts = false;
    };

    struct SerialPortInfo {
        std::string port_name;
        std::string description;
        std::string hardware_id;
        std::string manufacturer;
        bool is_available;
    };

    class SerialException : public std::exception {
    private:
        std::string message_;
        int error_code_;

    public:
        SerialException(const std::string& message, int error_code = 0)
            : message_(message), error_code_(error_code) {}

        const char* what() const noexcept override {
            return message_.c_str();
        }

        int error_code() const noexcept {
            return error_code_;
        }
    };

    class SerialManager {
    private:
#ifdef _WIN32
        HANDLE handle_;
        OVERLAPPED read_overlapped_;
        OVERLAPPED write_overlapped_;
#endif
        SerialConfig config_;
        std::atomic<SerialState> state_;
        std::atomic<bool> should_stop_;
        
        mutable std::mutex mutex_;
        std::condition_variable cv_;
        
        std::thread worker_thread_;
        std::queue<std::vector<uint8_t>> send_queue_;
        std::mutex send_mutex_;
        
        std::function<void(const std::vector<uint8_t>&)> on_data_received_;
        std::function<void(const std::string&)> on_error_;
        std::function<void()> on_opened_;
        std::function<void()> on_closed_;

        void worker_loop();
        void handle_communication();
        int get_last_error() const;
        std::string get_error_message(int error_code) const;
        bool configure_port();
        bool setup_comm_timeouts();
        void cleanup_overlapped();

    public:
        explicit SerialManager(const SerialConfig& config = {});
        ~SerialManager();

        // 禁用拷贝构造和赋值
        SerialManager(const SerialManager&) = delete;
        SerialManager& operator=(const SerialManager&) = delete;

        // 支持移动语义
        SerialManager(SerialManager&& other) noexcept;
        SerialManager& operator=(SerialManager&& other) noexcept;

        // 配置方法
        void set_config(const SerialConfig& config);
        const SerialConfig& get_config() const noexcept;
        SerialState get_state() const noexcept;

        // 回调设置
        void set_data_received_callback(std::function<void(const std::vector<uint8_t>&)> callback);
        void set_error_callback(std::function<void(const std::string&)> callback);
        void set_opened_callback(std::function<void()> callback);
        void set_closed_callback(std::function<void()> callback);

        // 串口操作
        std::future<bool> open_async();
        bool open(int timeout_ms = -1);
        void close();
        bool is_open() const noexcept;

        // 数据发送方法 (支持C++20 concepts)
        template<SerialData T>
        bool send_data(const T& data) {
            if constexpr (std::same_as<T, std::string>) {
                return send_raw_data(reinterpret_cast<const uint8_t*>(data.data()), data.size());
            }
            else if constexpr (std::same_as<T, std::vector<uint8_t>>) {
                return send_raw_data(data.data(), data.size());
            }
            else {
                return send_raw_data(reinterpret_cast<const uint8_t*>(&data), sizeof(T));
            }
        }

        template<SerialData T>
        std::future<bool> send_data_async(const T& data) {
            return std::async(std::launch::async, [this, data]() {
                return send_data(data);
            });
        }

        // 数据接收方法
        std::vector<uint8_t> receive_data(size_t max_size = 0);
        std::future<std::vector<uint8_t>> receive_data_async(size_t max_size = 0);

        // 串口控制
        bool set_dtr(bool enable);
        bool set_rts(bool enable);
        bool get_dtr() const;
        bool get_rts() const;
        bool get_cts() const;
        bool get_dsr() const;
        bool get_ring() const;
        bool get_rlsd() const; // Receive Line Signal Detect (Carrier Detect)

        // 缓冲区控制
        bool flush_input_buffer();
        bool flush_output_buffer();
        bool flush_all_buffers();
        size_t get_input_buffer_count() const;
        size_t get_output_buffer_count() const;

        // 静态工具方法
        static std::vector<SerialPortInfo> get_available_ports();
        static bool is_port_available(const std::string& port_name);
        static std::vector<std::string> get_port_names();

    private:
        bool send_raw_data(const uint8_t* data, size_t size);
        void queue_send_data(const std::vector<uint8_t>& data);
        void process_send_queue();
    };

    // 串口工具类
    class SerialUtils {
    public:
        // 波特率验证
        static bool is_valid_baud_rate(uint32_t baud_rate);
        static std::vector<uint32_t> get_standard_baud_rates();
        
        // 数据格式化
        static std::string bytes_to_hex_string(const std::vector<uint8_t>& data, bool uppercase = true);
        static std::vector<uint8_t> hex_string_to_bytes(const std::string& hex_string);
        
        // 校验和计算
        static uint8_t calculate_checksum(const std::vector<uint8_t>& data);
        static uint16_t calculate_crc16(const std::vector<uint8_t>& data, uint16_t polynomial = 0x8005);
        static uint32_t calculate_crc32(const std::vector<uint8_t>& data, uint32_t polynomial = 0x04C11DB7);
        
        // 数据包处理
        static std::vector<std::vector<uint8_t>> split_by_delimiter(const std::vector<uint8_t>& data, uint8_t delimiter);
        static std::vector<std::vector<uint8_t>> split_by_length(const std::vector<uint8_t>& data, size_t length);
        
        // 转义处理
        static std::vector<uint8_t> escape_data(const std::vector<uint8_t>& data, uint8_t escape_char = 0x7D);
        static std::vector<uint8_t> unescape_data(const std::vector<uint8_t>& data, uint8_t escape_char = 0x7D);
    };

} // namespace fuxicore