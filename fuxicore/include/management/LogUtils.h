#ifndef LOGUTILS_H
#define LOGUTILS_H

#include <QString>
#include <QDateTime>
#include <QFile>
#include <QTextStream>
#include <QMutex>
#include <QDebug>
#include <functional>
#include <memory>

/**
 * @brief 日志工具类
 * 提供灵活的日志记录功能，支持多种输出方式和日志级别
 */
class LogUtils
{
public:
    // 日志级别枚举
    enum class LogLevel {
        Trace = 0,
        Debug = 1,
        Info = 2,
        Warning = 3,
        Error = 4,
        Fatal = 5
    };

    // 日志输出目标枚举
    enum class LogTarget {
        Console = 1,
        File = 2,
        Both = 3
    };

    /**
     * @brief 初始化日志系统
     * @param level 最低日志级别
     * @param target 输出目标
     * @param logFile 日志文件路径（当target包含File时）
     */
    static void initialize(LogLevel level = LogLevel::Info, 
                          LogTarget target = LogTarget::Console,
                          const QString& logFile = "");

    /**
     * @brief 设置日志级别
     * @param level 日志级别
     */
    static void setLogLevel(LogLevel level);

    /**
     * @brief 设置日志输出目标
     * @param target 输出目标
     */
    static void setLogTarget(LogTarget target);

    /**
     * @brief 设置日志文件
     * @param filePath 文件路径
     */
    static void setLogFile(const QString& filePath);

    /**
     * @brief 设置日志格式
     * @param format 格式字符串，支持占位符：{timestamp}, {level}, {message}, {file}, {line}, {function}
     */
    static void setLogFormat(const QString& format);

    /**
     * @brief 启用/禁用自动日志文件轮转
     * @param enabled 是否启用
     * @param maxSize 最大文件大小（字节）
     * @param maxFiles 最大文件数量
     */
    static void setLogRotation(bool enabled, qint64 maxSize = 10 * 1024 * 1024, int maxFiles = 5);

    /**
     * @brief 记录日志
     * @param level 日志级别
     * @param message 日志消息
     * @param file 源文件名
     * @param line 行号
     * @param function 函数名
     */
    static void log(LogLevel level, const QString& message, 
                   const char* file = nullptr, int line = 0, const char* function = nullptr);

    /**
     * @brief 便捷的日志记录函数
     */
    static void trace(const QString& message, const char* file = nullptr, int line = 0, const char* function = nullptr);
    static void debug(const QString& message, const char* file = nullptr, int line = 0, const char* function = nullptr);
    static void info(const QString& message, const char* file = nullptr, int line = 0, const char* function = nullptr);
    static void warning(const QString& message, const char* file = nullptr, int line = 0, const char* function = nullptr);
    static void error(const QString& message, const char* file = nullptr, int line = 0, const char* function = nullptr);
    static void fatal(const QString& message, const char* file = nullptr, int line = 0, const char* function = nullptr);

    /**
     * @brief 获取日志级别字符串
     * @param level 日志级别
     * @return 级别字符串
     */
    static QString levelToString(LogLevel level);

    /**
     * @brief 从字符串解析日志级别
     * @param levelStr 级别字符串
     * @return 日志级别
     */
    static LogLevel stringToLevel(const QString& levelStr);

    /**
     * @brief 设置自定义日志处理器
     * @param handler 处理器函数
     */
    static void setCustomHandler(std::function<void(LogLevel, const QString&, const QString&)> handler);

    /**
     * @brief 清理日志系统
     */
    static void cleanup();

    /**
     * @brief 刷新日志缓冲区
     */
    static void flush();

    /**
     * @brief 获取当前日志文件大小
     * @return 文件大小（字节）
     */
    static qint64 getLogFileSize();

    /**
     * @brief 手动轮转日志文件
     */
    static void rotateLogFile();

private:
    static LogLevel s_logLevel;
    static LogTarget s_logTarget;
    static QString s_logFile;
    static QString s_logFormat;
    static QMutex s_mutex;
    static std::unique_ptr<QFile> s_file;
    static std::unique_ptr<QTextStream> s_stream;
    static std::function<void(LogLevel, const QString&, const QString&)> s_customHandler;
    
    // 日志轮转相关
    static bool s_rotationEnabled;
    static qint64 s_maxFileSize;
    static int s_maxFiles;
    
    static void writeToConsole(const QString& formattedMessage);
    static void writeToFile(const QString& formattedMessage);
    static QString formatMessage(LogLevel level, const QString& message, 
                                const char* file, int line, const char* function);
    static void checkAndRotateFile();
    static QString extractFileName(const char* filePath);
};

// 便捷宏定义
#define LOG_TRACE(msg) LogUtils::trace(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_DEBUG(msg) LogUtils::debug(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_INFO(msg) LogUtils::info(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_WARNING(msg) LogUtils::warning(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_ERROR(msg) LogUtils::error(msg, __FILE__, __LINE__, __FUNCTION__)
#define LOG_FATAL(msg) LogUtils::fatal(msg, __FILE__, __LINE__, __FUNCTION__)

#endif // LOGUTILS_H
