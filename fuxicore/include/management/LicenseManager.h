#pragma once

#include <QObject>
#include <QString>
#include <QDateTime>
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QTimer>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include "crypto/qaesencryption.h"

// Forward declarations for OpenSSL types
struct rsa_st;
typedef struct rsa_st RSA;

namespace Fuxi {
namespace Core {

/**
 * @brief Industrial-grade license management system
 * 
 * Provides secure license generation, validation, and management
 * with hardware fingerprint binding and time-based restrictions.
 */
class LicenseManager : public QObject
{
    Q_OBJECT

public:
    enum class LicenseType {
        Trial,      // Trial license with time limit
        Standard,   // Standard license
        Professional, // Professional license with extended features
        Enterprise  // Enterprise license with all features
    };

    enum class ValidationResult {
        Valid,              // License is valid
        Expired,            // License has expired
        InvalidSignature,   // License signature is invalid
        HardwareMismatch,   // Hardware fingerprint doesn't match
        NotFound,           // License file not found
        Corrupted,          // License file is corrupted
        InvalidFormat       // License format is invalid
    };

    struct LicenseInfo {
        QString licenseKey;
        QString customerName;
        QString companyName;
        LicenseType type;
        QDateTime issueDate;
        QDateTime expiryDate;
        QString hardwareFingerprint;
        QStringList enabledFeatures;
        QString signature;
        
        bool isValid() const;
        bool isExpired() const;
        int daysRemaining() const;
    };

    explicit LicenseManager(QObject *parent = nullptr);
    ~LicenseManager();

    // License generation (for license server)
    QString generateLicense(const QString& customerName,
                          const QString& companyName,
                          LicenseType type,
                          const QDateTime& expiryDate,
                          const QStringList& features = QStringList());

    // License validation
    ValidationResult validateLicense(const QString& licenseKey = QString());
    ValidationResult validateLicenseFile(const QString& filePath);
    
    // License information
    LicenseInfo getCurrentLicenseInfo() const;
    bool isFeatureEnabled(const QString& featureName) const;
    
    // License management
    bool installLicense(const QString& licenseKey);
    bool installLicenseFromFile(const QString& filePath);
    bool removeLicense();
    
    // Hardware fingerprint
    QString getHardwareFingerprint() const;
    
    // License file operations
    bool saveLicenseToFile(const QString& filePath, const QString& licenseKey);
    QString loadLicenseFromFile(const QString& filePath);
    
    // Configuration
    void setLicenseFilePath(const QString& filePath);
    QString getLicenseFilePath() const;
    
    void setPrivateKeyPath(const QString& keyPath);
    void setPublicKeyPath(const QString& keyPath);

    // Production security features
    void enableOnlineValidation(bool enable = true);
    void setLicenseServerUrl(const QString& url);
    bool performOnlineValidation();

    // Anti-tampering features
    bool verifyApplicationIntegrity() const;
    bool detectDebugging() const;
    bool detectVirtualMachine() const;

    // Enhanced hardware fingerprinting
    QString getEnhancedHardwareFingerprint() const;

signals:
    void licenseValidated(ValidationResult result);
    void licenseExpiring(int daysRemaining);
    void licenseExpired();
    void featureAccessDenied(const QString& featureName);
    void tamperingDetected(const QString& details);
    void debuggingDetected();
    void onlineValidationCompleted(bool success);
    void securityThreatDetected(const QString& threatType);

private slots:
    void checkLicenseExpiry();
    void performSecurityCheck();
    void onOnlineValidationFinished();

private:
    class LicenseManagerPrivate;
    LicenseManagerPrivate* d_ptr;
    
    // Internal methods
    QString generateHardwareFingerprint() const;
    QString signLicense(const QString& licenseData) const;
    bool verifySignature(const QString& licenseData, const QString& signature) const;
    QString encryptLicenseData(const QString& data) const;
    QString decryptLicenseData(const QString& encryptedData) const;
    
    // Utility methods
    QString generateLicenseKey() const;
    QByteArray hashString(const QString& input) const;
    QString base64Encode(const QByteArray& data) const;
    QByteArray base64Decode(const QString& data) const;

    // Production security methods
    QByteArray generateSecureRandom(int length) const;
    QString deriveKeyFromPassword(const QString& password, const QByteArray& salt) const;
    QByteArray aesEncrypt(const QByteArray& data, const QByteArray& key, const QByteArray& iv) const;
    QByteArray aesDecrypt(const QByteArray& encryptedData, const QByteArray& key, const QByteArray& iv) const;

    // Anti-tampering methods
    QString calculateExecutableHash() const;
    bool isDebuggerPresent() const;
    bool isRunningInVM() const;
    QStringList getSystemFingerprints() const;

    // RSA key management (placeholder for production)
    RSA* loadRSAPrivateKey(const QString& keyPath) const;
    RSA* loadRSAPublicKey(const QString& keyPath) const;

    // Legacy compatibility
    QString decryptLegacyXORData(const QString& encryptedData) const;
    QString decryptLegacyAESData(const QString& encryptedData) const;
};

} // namespace Core
} // namespace Fuxi
