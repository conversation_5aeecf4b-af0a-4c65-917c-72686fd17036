#ifndef CRYPTOUTILS_H
#define CRYPTOUTILS_H

#include <QString>
#include <QByteArray>
#include <openssl/evp.h>

/**
 * @brief 加密工具类
 * 提供常用的加密和哈希功能
 */
class CryptoUtils
{
public:
    /**
     * @brief 计算MD5哈希值
     * @param data 输入数据
     * @return MD5哈希值（十六进制字符串）
     */
    static QString md5(const QByteArray& data);

    /**
     * @brief 计算SHA1哈希值
     * @param data 输入数据
     * @return SHA1哈希值（十六进制字符串）
     */
    static QString sha1(const QByteArray& data);

    /**
     * @brief 计算SHA256哈希值
     * @param data 输入数据
     * @return SHA256哈希值（十六进制字符串）
     */
    static QString sha256(const QByteArray& data);

    /**
     * @brief Base64编码
     * @param data 输入数据
     * @return Base64编码字符串
     */
    static QString base64Encode(const QByteArray& data);

    /**
     * @brief Base64解码
     * @param data Base64编码字符串
     * @return 解码后的数据
     */
    static QByteArray base64Decode(const QString& data);

    /**
     * @brief AES加密
     * @param data 待加密数据
     * @param key 密钥（16、24或32字节）
     * @param iv 初始化向量（16字节）
     * @return 加密后的数据
     */
    static QByteArray aesEncrypt(const QByteArray& data, const QByteArray& key, const QByteArray& iv);

    /**
     * @brief AES解密
     * @param data 待解密数据
     * @param key 密钥（16、24或32字节）
     * @param iv 初始化向量（16字节）
     * @return 解密后的数据
     */
    static QByteArray aesDecrypt(const QByteArray& data, const QByteArray& key, const QByteArray& iv);

private:
    static QByteArray hash(const QByteArray& data, const EVP_MD* algorithm);
};

#endif // CRYPTOUTILS_H