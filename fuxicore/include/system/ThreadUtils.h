#ifndef THREADUTILS_H
#define THREADUTILS_H

#include <thread>
#include <mutex>
#include <condition_variable>
#include <shared_mutex>
#include <atomic>
#include <future>
#include <functional>
#include <memory>
#include <queue>
#include <vector>
#include <chrono>
#include <map>
#include <unordered_map>
#include <exception>
#include <type_traits>
#include <algorithm>

/**
 * @brief 线程工具类
 * 提供基于 std 标准库的线程管理、任务调度、同步等功能
 */
class ThreadUtils
{
public:
    // 前向声明
    class ReadLocker;
    class WriteLocker;
    /**
     * @brief 任务优先级枚举
     */
    enum class Priority {
        Low = 0,
        Normal = 1,
        High = 2,
        Critical = 3
    };

    /**
     * @brief 任务包装器
     */
    class Task
    {
    public:
        Task(std::function<void()> func, Priority priority = Priority::Normal);
        void execute();
        Priority getPriority() const;
        std::chrono::steady_clock::time_point getCreationTime() const;

    private:
        std::function<void()> m_function;
        Priority m_priority;
        std::chrono::steady_clock::time_point m_creationTime;
    };

    /**
     * @brief 线程安全的计数器
     */
    class AtomicCounter
    {
    public:
        AtomicCounter(int initialValue = 0);
        
        int increment();
        int decrement();
        int get() const;
        void set(int value);
        bool compareAndSwap(int expected, int newValue);
        
    private:
        std::atomic<int> m_value;
    };

    /**
     * @brief 线程安全的队列
     */
    template<typename T>
    class ThreadSafeQueue
    {
    public:
        void enqueue(const T& item);
        void enqueue(T&& item);
        bool dequeue(T& item);
        bool dequeue(T& item, const std::chrono::milliseconds& timeout);
        void clear();
        size_t size() const;
        bool isEmpty() const;
        void shutdown();

    private:
        mutable std::mutex m_mutex;
        std::condition_variable m_condition;
        std::queue<T> m_queue;
        std::atomic<bool> m_shutdown{false};
    };

    /**
     * @brief 线程池管理器
     */
    class ThreadPool
    {
    public:
        explicit ThreadPool(size_t numThreads = std::thread::hardware_concurrency());
        ~ThreadPool();

        // 禁止拷贝和移动
        ThreadPool(const ThreadPool&) = delete;
        ThreadPool& operator=(const ThreadPool&) = delete;
        ThreadPool(ThreadPool&&) = delete;
        ThreadPool& operator=(ThreadPool&&) = delete;

        void setMaxThreadCount(size_t maxCount);
        size_t maxThreadCount() const;
        size_t activeThreadCount() const;
        size_t queuedTaskCount() const;

        void submitTask(std::function<void()> task, Priority priority = Priority::Normal);

        template<typename Func, typename... Args>
        auto submitTaskWithResult(Func&& func, Args&&... args)
            -> std::future<std::invoke_result_t<Func, Args...>>;

        void waitForAllTasks();
        void shutdown();
        bool isShutdown() const;

    private:
        void workerThread();
        void resizeThreadPool(size_t newSize);

        struct TaskWrapper {
            std::shared_ptr<Task> task;
            bool operator<(const TaskWrapper& other) const {
                // 优先级高的任务优先执行（优先级数值越大越优先）
                if (task->getPriority() != other.task->getPriority()) {
                    return static_cast<int>(task->getPriority()) < static_cast<int>(other.task->getPriority());
                }
                // 相同优先级按创建时间排序（先创建的先执行）
                return task->getCreationTime() > other.task->getCreationTime();
            }
        };

        std::vector<std::thread> m_workers;
        std::priority_queue<TaskWrapper> m_taskQueue;
        mutable std::mutex m_queueMutex;
        std::condition_variable m_condition;
        std::atomic<bool> m_shutdown{false};
        std::atomic<size_t> m_activeTasks{0};
        std::atomic<size_t> m_maxThreads;
    };

    /**
     * @brief 定时器管理器
     */
    class TimerManager
    {
    public:
        static TimerManager& instance();
        ~TimerManager();

        // 禁止拷贝和移动
        TimerManager(const TimerManager&) = delete;
        TimerManager& operator=(const TimerManager&) = delete;

        using TimerId = uint64_t;

        TimerId startTimer(const std::chrono::milliseconds& interval,
                          std::function<void()> callback,
                          bool singleShot = false);

        void stopTimer(TimerId timerId);
        void stopAllTimers();
        void shutdown();

        TimerId startDelayedTask(const std::chrono::milliseconds& delay,
                                std::function<void()> task);

    private:
        TimerManager();
        void timerWorker();

        struct TimerInfo {
            TimerId id;
            std::chrono::steady_clock::time_point nextExecution;
            std::chrono::milliseconds interval;
            std::function<void()> callback;
            bool singleShot;
            bool active;
        };

        std::thread m_timerThread;
        std::mutex m_mutex;
        std::condition_variable m_condition;
        std::map<TimerId, TimerInfo> m_timers;
        std::atomic<TimerId> m_nextTimerId{1};
        std::atomic<bool> m_shutdown{false};
    };

    /**
     * @brief 读写锁包装器
     */
    class ReadWriteLock
    {
        friend class ReadLocker;
        friend class WriteLocker;

    public:
        ReadWriteLock() = default;

        void lockForRead();
        void lockForWrite();
        bool tryLockForRead();
        bool tryLockForWrite();
        void unlockRead();
        void unlockWrite();

    private:
        std::shared_mutex m_mutex;
    };

    /**
     * @brief RAII 读锁
     */
    class ReadLocker
    {
    public:
        explicit ReadLocker(ReadWriteLock& lock);
        explicit ReadLocker(std::shared_mutex& mutex);
        ~ReadLocker();

        // 禁止拷贝和移动
        ReadLocker(const ReadLocker&) = delete;
        ReadLocker& operator=(const ReadLocker&) = delete;

    private:
        std::shared_lock<std::shared_mutex> m_lock;
    };

    /**
     * @brief RAII 写锁
     */
    class WriteLocker
    {
    public:
        explicit WriteLocker(ReadWriteLock& lock);
        explicit WriteLocker(std::shared_mutex& mutex);
        ~WriteLocker();

        // 禁止拷贝和移动
        WriteLocker(const WriteLocker&) = delete;
        WriteLocker& operator=(const WriteLocker&) = delete;

    private:
        std::unique_lock<std::shared_mutex> m_lock;
    };

    // 静态工具函数

    /**
     * @brief 获取当前线程ID
     * @return 线程ID
     */
    static std::thread::id currentThreadId();

    /**
     * @brief 获取主线程ID
     * @return 主线程ID
     */
    static std::thread::id mainThreadId();

    /**
     * @brief 判断是否在主线程中
     * @return 是否在主线程
     */
    static bool isMainThread();

    /**
     * @brief 线程睡眠
     * @param duration 睡眠时间
     */
    template<typename Rep, typename Period>
    static void sleep(const std::chrono::duration<Rep, Period>& duration);

    /**
     * @brief 线程让出CPU
     */
    static void yield();

    /**
     * @brief 获取硬件并发线程数
     * @return 硬件线程数
     */
    static unsigned int hardwareConcurrency();

    /**
     * @brief 在后台线程中执行任务
     * @param task 任务函数
     * @param priority 优先级
     */
    static void runInBackground(std::function<void()> task, Priority priority = Priority::Normal);

    /**
     * @brief 并行执行多个任务
     * @param tasks 任务列表
     * @param maxConcurrency 最大并发数（0表示使用硬件线程数）
     */
    static void runParallel(const std::vector<std::function<void()>>& tasks, size_t maxConcurrency = 0);

    /**
     * @brief 创建线程安全的单例
     * @return 单例实例
     */
    template<typename T>
    static T& getSingleton();

    /**
     * @brief 获取全局线程池实例
     * @return 线程池引用
     */
    static ThreadPool& getGlobalThreadPool();

private:
    static std::thread::id s_mainThreadId;
    static std::once_flag s_mainThreadIdFlag;
    static std::unique_ptr<ThreadPool> s_globalThreadPool;
    static std::once_flag s_globalThreadPoolFlag;
};

// 模板实现

// ThreadSafeQueue 模板实现
template<typename T>
void ThreadUtils::ThreadSafeQueue<T>::enqueue(const T& item)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_shutdown.load()) return;

    m_queue.push(item);
    m_condition.notify_one();
}

template<typename T>
void ThreadUtils::ThreadSafeQueue<T>::enqueue(T&& item)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_shutdown.load()) return;

    m_queue.push(std::move(item));
    m_condition.notify_one();
}

template<typename T>
bool ThreadUtils::ThreadSafeQueue<T>::dequeue(T& item)
{
    std::lock_guard<std::mutex> lock(m_mutex);
    if (m_queue.empty() || m_shutdown.load()) {
        return false;
    }

    item = std::move(m_queue.front());
    m_queue.pop();
    return true;
}

template<typename T>
bool ThreadUtils::ThreadSafeQueue<T>::dequeue(T& item, const std::chrono::milliseconds& timeout)
{
    std::unique_lock<std::mutex> lock(m_mutex);

    if (!m_condition.wait_for(lock, timeout, [this] {
        return !m_queue.empty() || m_shutdown.load();
    })) {
        return false;
    }

    if (m_queue.empty() || m_shutdown.load()) {
        return false;
    }

    item = std::move(m_queue.front());
    m_queue.pop();
    return true;
}

template<typename T>
void ThreadUtils::ThreadSafeQueue<T>::clear()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    std::queue<T> empty;
    m_queue.swap(empty);
}

template<typename T>
size_t ThreadUtils::ThreadSafeQueue<T>::size() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_queue.size();
}

template<typename T>
bool ThreadUtils::ThreadSafeQueue<T>::isEmpty() const
{
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_queue.empty();
}

template<typename T>
void ThreadUtils::ThreadSafeQueue<T>::shutdown()
{
    std::lock_guard<std::mutex> lock(m_mutex);
    m_shutdown.store(true);
    m_condition.notify_all();
}

// ThreadPool 模板实现
template<typename Func, typename... Args>
auto ThreadUtils::ThreadPool::submitTaskWithResult(Func&& func, Args&&... args)
    -> std::future<std::invoke_result_t<Func, Args...>>
{
    using ReturnType = std::invoke_result_t<Func, Args...>;

    auto task = std::make_shared<std::packaged_task<ReturnType()>>(
        std::bind(std::forward<Func>(func), std::forward<Args>(args)...)
    );

    std::future<ReturnType> result = task->get_future();

    submitTask([task]() { (*task)(); });

    return result;
}



// 静态工具函数模板实现
template<typename Rep, typename Period>
void ThreadUtils::sleep(const std::chrono::duration<Rep, Period>& duration)
{
    std::this_thread::sleep_for(duration);
}

template<typename T>
T& ThreadUtils::getSingleton()
{
    static std::once_flag flag;
    static std::unique_ptr<T> instance;

    std::call_once(flag, []() {
        instance = std::make_unique<T>();
    });

    return *instance;
}

#endif // THREADUTILS_H
