#pragma once

#include <QObject>
#include <QString>
#include <QTimer>
#include <QDateTime>
#include <QJsonObject>
#include <memory>

namespace Fuxi {
namespace Core {

/**
 * @brief Industrial system monitoring and health management
 * 
 * Provides comprehensive system monitoring, health checks,
 * performance metrics, and fault detection for industrial applications.
 */
class SystemMonitor : public QObject
{
    Q_OBJECT

public:
    enum class SystemStatus {
        Healthy,        // System is operating normally
        Warning,        // System has warnings but is operational
        Critical,       // System has critical issues
        Failed,         // System has failed
        Unknown         // System status is unknown
    };

    enum class MetricType {
        CPU,            // CPU usage percentage
        Memory,         // Memory usage
        Disk,           // Disk usage
        Network,        // Network statistics
        Temperature,    // System temperature
        Custom          // Custom metric
    };

    struct SystemMetrics {
        double cpuUsage;            // CPU usage percentage (0-100)
        qint64 memoryUsed;          // Memory used in bytes
        qint64 memoryTotal;         // Total memory in bytes
        double memoryUsage;         // Memory usage percentage (0-100)
        qint64 diskUsed;            // Disk used in bytes
        qint64 diskTotal;           // Total disk space in bytes
        double diskUsage;           // Disk usage percentage (0-100)
        qint64 networkBytesIn;      // Network bytes received
        qint64 networkBytesOut;     // Network bytes sent
        double temperature;         // System temperature in Celsius
        QDateTime timestamp;        // When metrics were collected
        
        QJsonObject toJson() const;
        bool isValid() const;
    };

    struct HealthCheck {
        QString id;
        QString name;
        QString description;
        std::function<bool()> checkFunction;
        int intervalMs;
        int timeoutMs;
        bool critical;
        bool enabled;
        
        bool isValid() const;
    };

    struct AlertRule {
        QString id;
        QString name;
        MetricType metricType;
        QString metricName;
        double threshold;
        QString condition;          // "greater", "less", "equal"
        int duration;              // Duration in seconds before alert
        bool enabled;
        
        bool evaluate(double value) const;
        bool isValid() const;
    };

    struct SystemAlert {
        QString id;
        QString ruleId;
        QString message;
        SystemStatus severity;
        QDateTime timestamp;
        QDateTime acknowledgedTime;
        bool acknowledged;
        QJsonObject context;
        
        QJsonObject toJson() const;
    };

    explicit SystemMonitor(QObject *parent = nullptr);
    ~SystemMonitor();

    // Monitoring control
    void startMonitoring();
    void stopMonitoring();
    bool isMonitoring() const;
    
    void setMonitoringInterval(int intervalMs);
    int getMonitoringInterval() const;
    
    // System metrics
    SystemMetrics getCurrentMetrics() const;
    QList<SystemMetrics> getMetricsHistory(int count = 100) const;
    QList<SystemMetrics> getMetricsByTimeRange(const QDateTime& start, const QDateTime& end) const;
    
    // Custom metrics
    void addCustomMetric(const QString& name, double value);
    double getCustomMetric(const QString& name) const;
    QStringList getCustomMetricNames() const;
    void removeCustomMetric(const QString& name);
    
    // Health checks
    void addHealthCheck(const HealthCheck& check);
    void removeHealthCheck(const QString& checkId);
    void enableHealthCheck(const QString& checkId, bool enable = true);
    QList<HealthCheck> getAllHealthChecks() const;
    
    bool runHealthCheck(const QString& checkId);
    bool runAllHealthChecks();
    
    // Alert management
    void addAlertRule(const AlertRule& rule);
    void removeAlertRule(const QString& ruleId);
    void enableAlertRule(const QString& ruleId, bool enable = true);
    QList<AlertRule> getAllAlertRules() const;
    
    QList<SystemAlert> getActiveAlerts() const;
    QList<SystemAlert> getAllAlerts() const;
    bool acknowledgeAlert(const QString& alertId);
    void clearAcknowledgedAlerts();
    
    // System status
    SystemStatus getSystemStatus() const;
    QString getSystemStatusText() const;
    QDateTime getLastUpdateTime() const;
    
    // Performance analysis
    double getAverageCpuUsage(int minutes = 60) const;
    double getAverageMemoryUsage(int minutes = 60) const;
    double getPeakCpuUsage(int minutes = 60) const;
    double getPeakMemoryUsage(int minutes = 60) const;
    
    // System information
    QString getSystemInfo() const;
    QString getOSVersion() const;
    QString getCpuInfo() const;
    qint64 getTotalMemory() const;
    qint64 getTotalDiskSpace() const;
    
    // Configuration
    void setMetricsRetentionDays(int days);
    int getMetricsRetentionDays() const;
    
    void setAlertsRetentionDays(int days);
    int getAlertsRetentionDays() const;
    
    // Data persistence
    bool saveMetricsToFile(const QString& filePath) const;
    bool loadMetricsFromFile(const QString& filePath);
    
    bool saveAlertsToFile(const QString& filePath) const;
    bool loadAlertsFromFile(const QString& filePath);

signals:
    void metricsUpdated(const SystemMetrics& metrics);
    void systemStatusChanged(SystemStatus oldStatus, SystemStatus newStatus);
    void alertTriggered(const SystemAlert& alert);
    void healthCheckFailed(const QString& checkId, const QString& error);
    void customMetricAdded(const QString& name, double value);

private slots:
    void collectMetrics();
    void runScheduledHealthChecks();
    void evaluateAlertRules();
    void cleanupOldData();

private:
    class SystemMonitorPrivate;
    std::unique_ptr<SystemMonitorPrivate> d_ptr;
    
    // Internal methods
    SystemMetrics collectSystemMetrics() const;
    double getCpuUsage() const;
    void getMemoryInfo(qint64& used, qint64& total) const;
    void getDiskInfo(qint64& used, qint64& total) const;
    void getNetworkInfo(qint64& bytesIn, qint64& bytesOut) const;
    double getSystemTemperature() const;
    
    SystemStatus calculateSystemStatus() const;
    void checkAlertConditions(const SystemMetrics& metrics);
    void triggerAlert(const AlertRule& rule, double value);
    
    QString generateAlertId() const;
    void cleanupMetrics();
    void cleanupAlerts();
};

} // namespace Core
} // namespace Fuxi
