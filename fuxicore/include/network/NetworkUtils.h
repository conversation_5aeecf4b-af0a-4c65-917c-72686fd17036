#ifndef NETWORKUTILS_H
#define NETWORKUTILS_H

#include <QString>
#include <QStringList>
#include <QHostAddress>
#include <vector>

#ifdef QT_NETWORK_LIB
class QNetworkInterface;
#endif

/**
 * @brief 网络工具类
 * 提供网络相关的实用功能
 */
class NetworkUtils
{
public:
    /**
     * @brief 获取本机所有IP地址
     * @return IP地址列表
     */
    static QStringList getLocalIPAddresses();

    /**
     * @brief 检查IP地址是否有效
     * @param ip IP地址字符串
     * @return 是否有效
     */
    static bool isValidIP(const QString& ip);

    /**
     * @brief 检查端口是否有效
     * @param port 端口号
     * @return 是否有效
     */
    static bool isValidPort(int port);

    /**
     * @brief 获取本机主机名
     * @return 主机名
     */
    static QString getHostName();

    /**
     * @brief 检查网络连接状态
     * @param host 测试连接的主机地址
     * @param timeout 超时时间（毫秒）
     * @return 是否连接成功
     */
    static bool ping(const QString& host, int timeout = 5000);

    /**
     * @brief URL编码
     * @param input 输入字符串
     * @return 编码后的字符串
     */
    static QString urlEncode(const QString& input);

    /**
     * @brief URL解码
     * @param input 输入字符串
     * @return 解码后的字符串
     */
    static QString urlDecode(const QString& input);
};

#endif // NETWORKUTILS_H
