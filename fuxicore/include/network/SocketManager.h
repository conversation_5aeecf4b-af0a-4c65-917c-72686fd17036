#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <atomic>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <future>
#include <concepts>
#include <span>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#endif

namespace fuxicore {

    // C++20 concepts for type safety
    template<typename T>
    concept SocketData = std::is_trivially_copyable_v<T> || std::same_as<T, std::string> || std::same_as<T, std::vector<uint8_t>>;

    enum class SocketType {
        TCP,
        UDP
    };

    enum class SocketState {
        Disconnected,
        Connecting,
        Connected,
        Listening,
        Error
    };

    struct SocketConfig {
        std::string address = "127.0.0.1";
        uint16_t port = 8080;
        SocketType type = SocketType::TCP;
        int timeout_ms = 5000;
        bool reuse_address = true;
        size_t buffer_size = 4096;
    };

    class SocketException : public std::exception {
    private:
        std::string message_;
        int error_code_;

    public:
        SocketException(const std::string& message, int error_code = 0)
            : message_(message), error_code_(error_code) {}

        const char* what() const noexcept override {
            return message_.c_str();
        }

        int error_code() const noexcept {
            return error_code_;
        }
    };

    class SocketManager {
    private:
        SOCKET socket_;
        SocketConfig config_;
        std::atomic<SocketState> state_;
        std::atomic<bool> should_stop_;
        
        mutable std::mutex mutex_;
        std::condition_variable cv_;
        
        std::thread worker_thread_;
        std::queue<std::vector<uint8_t>> send_queue_;
        std::mutex send_mutex_;
        
        std::function<void(const std::vector<uint8_t>&)> on_data_received_;
        std::function<void(const std::string&)> on_error_;
        std::function<void()> on_connected_;
        std::function<void()> on_disconnected_;

        static bool wsa_initialized_;
        static std::mutex wsa_mutex_;
        static int wsa_ref_count_;

        void initialize_wsa();
        void cleanup_wsa();
        void worker_loop();
        void handle_tcp_communication();
        void handle_udp_communication();
        int get_last_socket_error() const;
        std::string get_error_message(int error_code) const;

    public:
        explicit SocketManager(const SocketConfig& config = {});
        ~SocketManager();

        // 禁用拷贝构造和赋值
        SocketManager(const SocketManager&) = delete;
        SocketManager& operator=(const SocketManager&) = delete;

        // 支持移动语义
        SocketManager(SocketManager&& other) noexcept;
        SocketManager& operator=(SocketManager&& other) noexcept;

        // 配置方法
        void set_config(const SocketConfig& config);
        const SocketConfig& get_config() const noexcept;
        SocketState get_state() const noexcept;

        // 回调设置
        void set_data_received_callback(std::function<void(const std::vector<uint8_t>&)> callback);
        void set_error_callback(std::function<void(const std::string&)> callback);
        void set_connected_callback(std::function<void()> callback);
        void set_disconnected_callback(std::function<void()> callback);

        // 客户端方法
        std::future<bool> connect_async();
        bool connect(int timeout_ms = -1);
        void disconnect();

        // 服务器方法
        bool start_server();
        void stop_server();
        bool is_listening() const noexcept;

        // 数据发送方法 (支持C++20 concepts)
        template<SocketData T>
        bool send_data(const T& data) {
            if constexpr (std::same_as<T, std::string>) {
                return send_raw_data(reinterpret_cast<const uint8_t*>(data.data()), data.size());
            }
            else if constexpr (std::same_as<T, std::vector<uint8_t>>) {
                return send_raw_data(data.data(), data.size());
            }
            else {
                return send_raw_data(reinterpret_cast<const uint8_t*>(&data), sizeof(T));
            }
        }

        template<SocketData T>
        std::future<bool> send_data_async(const T& data) {
            return std::async(std::launch::async, [this, data]() {
                return send_data(data);
            });
        }

        // 数据接收方法
        std::vector<uint8_t> receive_data(size_t max_size = 0);
        std::future<std::vector<uint8_t>> receive_data_async(size_t max_size = 0);

        // 工具方法
        bool is_connected() const noexcept;
        std::string get_local_address() const;
        uint16_t get_local_port() const;
        std::string get_remote_address() const;
        uint16_t get_remote_port() const;

        // 静态工具方法
        static std::vector<std::string> get_local_ip_addresses();
        static bool is_port_available(uint16_t port, const std::string& address = "127.0.0.1");
        static std::string resolve_hostname(const std::string& hostname);

    private:
        bool send_raw_data(const uint8_t* data, size_t size);
        void queue_send_data(const std::vector<uint8_t>& data);
        void process_send_queue();
    };

    // 服务器类，用于处理多个客户端连接
    class SocketServer {
    private:
        struct ClientConnection {
            SOCKET socket;
            std::string address;
            uint16_t port;
            std::thread worker_thread;
            std::atomic<bool> active;
            
            ClientConnection(SOCKET s, const std::string& addr, uint16_t p)
                : socket(s), address(addr), port(p), active(true) {}
        };

        SocketConfig config_;
        SOCKET listen_socket_;
        std::atomic<bool> is_running_;
        std::thread accept_thread_;
        
        std::vector<std::unique_ptr<ClientConnection>> clients_;
        mutable std::mutex clients_mutex_;
        
        std::function<void(size_t, const std::vector<uint8_t>&)> on_client_data_;
        std::function<void(size_t)> on_client_connected_;
        std::function<void(size_t)> on_client_disconnected_;
        std::function<void(const std::string&)> on_error_;

        static bool wsa_initialized_;
        static std::mutex wsa_mutex_;
        static int wsa_ref_count_;

        void initialize_wsa();
        void cleanup_wsa();
        void accept_loop();
        void handle_client(std::unique_ptr<ClientConnection> client);
        void cleanup_disconnected_clients();

    public:
        explicit SocketServer(const SocketConfig& config = {});
        ~SocketServer();

        // 禁用拷贝
        SocketServer(const SocketServer&) = delete;
        SocketServer& operator=(const SocketServer&) = delete;

        // 配置和控制
        void set_config(const SocketConfig& config);
        bool start();
        void stop();
        bool is_running() const noexcept;

        // 回调设置
        void set_client_data_callback(std::function<void(size_t, const std::vector<uint8_t>&)> callback);
        void set_client_connected_callback(std::function<void(size_t)> callback);
        void set_client_disconnected_callback(std::function<void(size_t)> callback);
        void set_error_callback(std::function<void(const std::string&)> callback);

        // 客户端管理
        size_t get_client_count() const;
        std::vector<std::pair<std::string, uint16_t>> get_client_addresses() const;
        bool disconnect_client(size_t client_id);
        void disconnect_all_clients();

        // 数据发送
        template<SocketData T>
        bool send_to_client(size_t client_id, const T& data) {
            std::lock_guard<std::mutex> lock(clients_mutex_);
            if (client_id >= clients_.size() || !clients_[client_id] || !clients_[client_id]->active) {
                return false;
            }

            const uint8_t* raw_data;
            size_t data_size;

            if constexpr (std::same_as<T, std::string>) {
                raw_data = reinterpret_cast<const uint8_t*>(data.data());
                data_size = data.size();
            }
            else if constexpr (std::same_as<T, std::vector<uint8_t>>) {
                raw_data = data.data();
                data_size = data.size();
            }
            else {
                raw_data = reinterpret_cast<const uint8_t*>(&data);
                data_size = sizeof(T);
            }

            int result = send(clients_[client_id]->socket, reinterpret_cast<const char*>(raw_data), static_cast<int>(data_size), 0);
            return result != SOCKET_ERROR;
        }

        template<SocketData T>
        void broadcast_data(const T& data) {
            std::lock_guard<std::mutex> lock(clients_mutex_);
            for (auto& client : clients_) {
                if (client && client->active) {
                    send_to_client(std::distance(clients_.data(), &client), data);
                }
            }
        }
    };

} // namespace fuxicore