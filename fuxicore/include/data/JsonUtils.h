#ifndef JSONUTILS_H
#define JSONUTILS_H

#include <QString>
#include <QVariant>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

/**
 * @brief JSON工具类
 * 提供JSON数据处理的便捷方法
 */
class JsonUtils
{
public:
    /**
     * @brief 将QVariant转换为JSON字符串
     * @param variant QVariant对象
     * @param compact 是否压缩输出
     * @return JSON字符串
     */
    static QString toJson(const QVariant& variant, bool compact = false);

    /**
     * @brief 将JSON字符串转换为QVariant
     * @param json JSON字符串
     * @param ok 转换是否成功
     * @return QVariant对象
     */
    static QVariant fromJson(const QString& json, bool* ok = nullptr);

    /**
     * @brief 将QJsonObject转换为QVariantMap
     * @param jsonObject QJsonObject对象
     * @return QVariantMap对象
     */
    static QVariantMap jsonObjectToVariantMap(const QJsonObject& jsonObject);

    /**
     * @brief 将QVariantMap转换为QJsonObject
     * @param variantMap QVariantMap对象
     * @return QJsonObject对象
     */
    static QJsonObject variantMapToJsonObject(const QVariantMap& variantMap);

    /**
     * @brief 合并两个JSON对象
     * @param base 基础JSON对象
     * @param overlay 覆盖JSON对象
     * @return 合并后的JSON对象
     */
    static QJsonObject mergeJsonObjects(const QJsonObject& base, const QJsonObject& overlay);

    /**
     * @brief 从JSON对象中获取值
     * @param jsonObject JSON对象
     * @param key 键名
     * @param defaultValue 默认值
     * @return 值
     */
    static QVariant getValue(const QJsonObject& jsonObject, const QString& key, const QVariant& defaultValue = QVariant());

    /**
     * @brief 在JSON对象中设置值
     * @param jsonObject JSON对象
     * @param key 键名
     * @param value 值
     */
    static void setValue(QJsonObject& jsonObject, const QString& key, const QVariant& value);

    /**
     * @brief 验证JSON字符串是否有效
     * @param json JSON字符串
     * @return 是否有效
     */
    static bool isValidJson(const QString& json);
};

#endif // JSONUTILS_H