#pragma once

#include <QObject>
#include <QString>
#include <QVariant>
#include <QJsonObject>
#include <QJsonDocument>
#include <QFileSystemWatcher>
#include <QTimer>
#include <QDateTime>

namespace Fuxi {
namespace Core {

/**
 * @brief Industrial-grade configuration management system
 * 
 * Provides robust configuration file handling with support for multiple formats,
 * hot reloading, version management, and validation.
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    enum class ConfigFormat {
        JSON,
        XML,
        INI,
        YAML
    };

    enum class ValidationResult {
        Valid,
        InvalidFormat,
        MissingRequired,
        InvalidValue,
        SchemaViolation,
        FileNotFound,
        PermissionDenied
    };

    struct ConfigInfo {
        QString filePath;
        ConfigFormat format;
        QString version;
        QDateTime lastModified;
        QDateTime lastLoaded;
        bool isValid;
        QString checksum;
    };

    explicit ConfigManager(QObject *parent = nullptr);
    ~ConfigManager();

    // Configuration loading and saving
    bool loadConfig(const QString& filePath, ConfigFormat format = ConfigFormat::JSON);
    bool saveConfig(const QString& filePath = QString(), ConfigFormat format = ConfigFormat::JSON);
    bool reloadConfig();
    
    // Value access
    QVariant getValue(const QString& key, const QVariant& defaultValue = QVariant()) const;
    bool setValue(const QString& key, const QVariant& value);
    bool removeValue(const QString& key);
    bool hasValue(const QString& key) const;
    
    // Section management
    QStringList getSectionKeys(const QString& section = QString()) const;
    QJsonObject getSection(const QString& section) const;
    bool setSection(const QString& section, const QJsonObject& data);
    bool removeSection(const QString& section);
    
    // Configuration validation
    ValidationResult validateConfig() const;
    ValidationResult validateConfig(const QString& filePath, ConfigFormat format);
    bool setValidationSchema(const QString& schemaPath);
    
    // Hot reloading
    void enableHotReload(bool enable = true);
    bool isHotReloadEnabled() const;
    void setReloadDelay(int milliseconds);
    
    // Version management
    QString getConfigVersion() const;
    bool setConfigVersion(const QString& version);
    bool migrateConfig(const QString& fromVersion, const QString& toVersion);
    
    // Backup and restore
    bool createBackup(const QString& backupPath = QString());
    bool restoreFromBackup(const QString& backupPath);
    QStringList getAvailableBackups() const;
    
    // Configuration merging
    bool mergeConfig(const QString& otherConfigPath);
    bool mergeConfig(const QJsonObject& otherConfig);
    
    // Export and import
    bool exportToFormat(const QString& filePath, ConfigFormat targetFormat);
    bool importFromFormat(const QString& filePath, ConfigFormat sourceFormat);
    
    // Configuration information
    ConfigInfo getConfigInfo() const;
    QString getConfigChecksum() const;
    bool isConfigModified() const;
    
    // Default values management
    void setDefaultValue(const QString& key, const QVariant& value);
    QVariant getDefaultValue(const QString& key) const;
    void loadDefaults(const QString& defaultsFilePath);

signals:
    void configLoaded(const QString& filePath);
    void configSaved(const QString& filePath);
    void configReloaded();
    void configChanged(const QString& key, const QVariant& oldValue, const QVariant& newValue);
    void configFileChanged(const QString& filePath);
    void validationFailed(ValidationResult result, const QString& message);
    void backupCreated(const QString& backupPath);

private slots:
    void onFileChanged(const QString& path);
    void onReloadTimer();

private:
    class ConfigManagerPrivate;
    ConfigManagerPrivate* d_ptr;
    
    // Internal methods
    bool loadJsonConfig(const QString& filePath);
    bool loadXmlConfig(const QString& filePath);
    bool loadIniConfig(const QString& filePath);
    bool loadYamlConfig(const QString& filePath);
    
    bool saveJsonConfig(const QString& filePath);
    bool saveXmlConfig(const QString& filePath);
    bool saveIniConfig(const QString& filePath);
    bool saveYamlConfig(const QString& filePath);
    
    QString calculateChecksum(const QString& filePath) const;
    bool validateJsonSchema(const QJsonObject& config, const QJsonObject& schema) const;
    QString generateBackupPath() const;
    
    // Utility methods
    QStringList splitKey(const QString& key) const;
    QJsonObject* navigateToParent(const QStringList& keyParts, bool createPath = false);
    const QJsonObject* navigateToParentConst(const QStringList& keyParts) const;
};

} // namespace Core
} // namespace Fuxi
