#ifndef FUXIUTILS_H
#define FUXIUTILS_H

/**
 * @file fuxiutils.h
 * @brief FuxiUtils 兼容性头文件
 *
 * 这个文件保持向后兼容性，包含所有模块的头文件。
 * 建议新项目使用 fuxicore.h 作为主头文件。
 *
 * @deprecated 请使用 fuxicore.h 替代
 */

// 基础工具模块
#include "base/MathUtils.h"
#include "base/StringUtils.h"
#include "base/TimeUtils.h"
#include "base/FileUtils.h"

// 网络通信模块
#include "network/NetworkUtils.h"
#include "network/SocketManager.h"

// 系统相关模块
#include "system/SystemMonitor.h"
#include "system/ThreadUtils.h"

// 数据处理模块
#include "data/ConfigManager.h"
#include "data/JsonUtils.h"

// 安全加密模块
#include "crypto/CryptoUtils.h"
#include "crypto/qaesencryption.h"

// 硬件接口模块
#include "hardware/SerialManager.h"

// 管理功能模块
#include "management/LicenseManager.h"
#include "management/LogUtils.h"

// 推荐使用新的主头文件
#include "fuxicore.h"

#endif // FUXIUTILS_H