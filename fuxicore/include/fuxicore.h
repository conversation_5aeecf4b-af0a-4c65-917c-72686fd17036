#ifndef FUXICORE_H
#define FUXICORE_H

/**
 * @file fuxicore.h
 * @brief FuxiCore 核心库主头文件
 * 
 * 这是 FuxiCore 库的主头文件，包含了所有功能模块的头文件。
 * FuxiCore 是一个工业级的 C++/Qt 工具库，提供了丰富的实用功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @date 2025
 */

// ============================================================================
// 基础工具模块 (Base Utilities)
// ============================================================================
#include "base/StringUtils.h"      // 字符串处理工具
#include "base/MathUtils.h"        // 数学计算工具
#include "base/TimeUtils.h"        // 时间处理工具
#include "base/FileUtils.h"        // 文件操作工具

// ============================================================================
// 网络通信模块 (Network Communication)
// ============================================================================
#include "network/NetworkUtils.h"  // 网络工具
#include "network/SocketManager.h" // Socket管理器

// ============================================================================
// 系统相关模块 (System Utilities)
// ============================================================================
#include "system/SystemMonitor.h"  // 系统监控
#include "system/ThreadUtils.h"    // 线程工具

// ============================================================================
// 数据处理模块 (Data Processing)
// ============================================================================
#include "data/JsonUtils.h"        // JSON处理工具
#include "data/ConfigManager.h"    // 配置管理器

// ============================================================================
// 安全加密模块 (Cryptography & Security)
// ============================================================================
#include "crypto/CryptoUtils.h"    // 加密工具
#include "crypto/qaesencryption.h" // AES加密
#include "crypto/aesni/aesni-enc-cbc.h"  // AES-NI CBC模式
#include "crypto/aesni/aesni-enc-ecb.h"  // AES-NI ECB模式
#include "crypto/aesni/aesni-key-exp.h"  // AES-NI 密钥扩展
#include "crypto/aesni/aesni-key-init.h" // AES-NI 密钥初始化

// ============================================================================
// 硬件接口模块 (Hardware Interface)
// ============================================================================
#include "hardware/SerialManager.h" // 串口管理器

// ============================================================================
// 管理功能模块 (Management Features)
// ============================================================================
#include "management/LicenseManager.h" // 许可证管理器
#include "management/LogUtils.h"       // 日志工具

/**
 * @namespace Fuxi
 * @brief FuxiCore 库的根命名空间
 */
namespace Fuxi {
    /**
     * @namespace Fuxi::Core
     * @brief FuxiCore 核心功能命名空间
     */
    namespace Core {
        /**
         * @brief 获取 FuxiCore 库版本信息
         * @return 版本字符串
         */
        inline const char* getVersion() {
            return "1.0.0";
        }
        
        /**
         * @brief 获取 FuxiCore 库构建信息
         * @return 构建信息字符串
         */
        inline const char* getBuildInfo() {
            return "Built on " __DATE__ " " __TIME__;
        }
        
        /**
         * @brief 初始化 FuxiCore 库
         * @return 是否初始化成功
         */
        bool initialize();
        
        /**
         * @brief 清理 FuxiCore 库资源
         */
        void cleanup();
    }
}

// ============================================================================
// 便利宏定义 (Convenience Macros)
// ============================================================================

/**
 * @brief 快速访问字符串工具
 */
#define FUXICORE_STRING StringUtils

/**
 * @brief 快速访问数学工具
 */
#define FUXICORE_MATH MathUtils

/**
 * @brief 快速访问时间工具
 */
#define FUXICORE_TIME TimeUtils

/**
 * @brief 快速访问文件工具
 */
#define FUXICORE_FILE FileUtils

/**
 * @brief 快速访问网络工具
 */
#define FUXICORE_NETWORK NetworkUtils

/**
 * @brief 快速访问JSON工具
 */
#define FUXICORE_JSON JsonUtils

/**
 * @brief 快速访问加密工具
 */
#define FUXICORE_CRYPTO CryptoUtils

/**
 * @brief 快速访问日志工具
 */
#define FUXICORE_LOG LogUtils

/**
 * @brief 快速访问线程工具
 */
#define FUXICORE_THREAD ThreadUtils

// ============================================================================
// 版本兼容性 (Version Compatibility)
// ============================================================================

/**
 * @brief 主版本号
 */
#define FUXICORE_VERSION_MAJOR 1

/**
 * @brief 次版本号
 */
#define FUXICORE_VERSION_MINOR 0

/**
 * @brief 修订版本号
 */
#define FUXICORE_VERSION_PATCH 0

/**
 * @brief 版本号字符串
 */
#define FUXICORE_VERSION_STRING "1.0.0"

/**
 * @brief 检查版本兼容性
 */
#define FUXICORE_VERSION_CHECK(major, minor, patch) \
    ((FUXICORE_VERSION_MAJOR > (major)) || \
     (FUXICORE_VERSION_MAJOR == (major) && FUXICORE_VERSION_MINOR > (minor)) || \
     (FUXICORE_VERSION_MAJOR == (major) && FUXICORE_VERSION_MINOR == (minor) && FUXICORE_VERSION_PATCH >= (patch)))

#endif // FUXICORE_H
