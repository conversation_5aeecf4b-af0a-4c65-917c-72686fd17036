#ifndef TIMEUTILS_H
#define TIMEUTILS_H

#include <QString>
#include <QDateTime>
#include <QTimer>
#include <QElapsedTimer>
#include <chrono>
#include <vector>
#include <functional>

/**
 * @brief 时间工具类
 * 提供时间相关的实用功能，包括时间戳处理、格式化、性能测量等
 */
class TimeUtils
{
public:
    // 常用时间格式常量
    static const QString ISO_FORMAT;           // "yyyy-MM-ddThh:mm:ss"
    static const QString DATE_FORMAT;          // "yyyy-MM-dd"
    static const QString TIME_FORMAT;          // "hh:mm:ss"
    static const QString DATETIME_FORMAT;      // "yyyy-MM-dd hh:mm:ss"
    static const QString COMPACT_FORMAT;       // "yyyyMMddhhmmss"

    // 时间单位枚举
    enum class TimeUnit {
        Nanoseconds,
        Microseconds,
        Milliseconds,
        Seconds,
        Minutes,
        Hours,
        Days
    };

    /**
     * @brief 获取当前时间戳（秒）
     * @return 时间戳
     */
    static qint64 currentTimestamp();

    /**
     * @brief 获取当前时间戳（毫秒）
     * @return 时间戳
     */
    static qint64 currentTimestampMs();

    /**
     * @brief 获取当前时间戳（微秒）
     * @return 时间戳
     */
    static qint64 currentTimestampUs();

    /**
     * @brief 获取当前时间戳（纳秒）
     * @return 时间戳
     */
    static qint64 currentTimestampNs();

    /**
     * @brief 获取高精度时间戳（使用std::chrono）
     * @return 高精度时间戳
     */
    static std::chrono::high_resolution_clock::time_point currentHighResTime();

    /**
     * @brief 格式化时间
     * @param timestamp 时间戳（秒）
     * @param format 格式字符串
     * @return 格式化后的时间字符串
     */
    static QString formatTimestamp(qint64 timestamp, const QString& format = DATETIME_FORMAT);

    /**
     * @brief 格式化毫秒时间戳
     * @param timestampMs 时间戳（毫秒）
     * @param format 格式字符串
     * @return 格式化后的时间字符串
     */
    static QString formatTimestampMs(qint64 timestampMs, const QString& format = DATETIME_FORMAT);

    /**
     * @brief 解析时间字符串
     * @param timeStr 时间字符串
     * @param format 格式字符串
     * @return 时间戳（秒）
     */
    static qint64 parseTimestamp(const QString& timeStr, const QString& format = DATETIME_FORMAT);

    /**
     * @brief 获取时间差
     * @param start 开始时间
     * @param end 结束时间
     * @param unit 时间单位
     * @return 时间差
     */
    static qint64 timeDiff(qint64 start, qint64 end, TimeUnit unit = TimeUnit::Seconds);

    /**
     * @brief 延迟执行（毫秒）
     * @param milliseconds 延迟时间
     */
    static void sleep(int milliseconds);

    /**
     * @brief 精确延迟执行（微秒）
     * @param microseconds 延迟时间
     */
    static void sleepUs(int microseconds);

    /**
     * @brief 获取程序运行时间（毫秒）
     * @return 运行时间
     */
    static qint64 getRuntimeMs();

    /**
     * @brief 获取程序运行时间（秒）
     * @return 运行时间
     */
    static qint64 getRuntimeSec();

    /**
     * @brief 时间转换
     * @param value 时间值
     * @param from 源时间单位
     * @param to 目标时间单位
     * @return 转换后的时间值
     */
    static qint64 convertTime(qint64 value, TimeUnit from, TimeUnit to);

    /**
     * @brief 获取今天的开始时间戳（00:00:00）
     * @return 时间戳
     */
    static qint64 getTodayStart();

    /**
     * @brief 获取今天的结束时间戳（23:59:59）
     * @return 时间戳
     */
    static qint64 getTodayEnd();

    /**
     * @brief 获取本周的开始时间戳（周一00:00:00）
     * @return 时间戳
     */
    static qint64 getWeekStart();

    /**
     * @brief 获取本月的开始时间戳（1号00:00:00）
     * @return 时间戳
     */
    static qint64 getMonthStart();

    /**
     * @brief 判断是否为同一天
     * @param timestamp1 时间戳1
     * @param timestamp2 时间戳2
     * @return 是否为同一天
     */
    static bool isSameDay(qint64 timestamp1, qint64 timestamp2);

    /**
     * @brief 判断是否为工作日
     * @param timestamp 时间戳
     * @return 是否为工作日
     */
    static bool isWorkday(qint64 timestamp);

    /**
     * @brief 判断是否为周末
     * @param timestamp 时间戳
     * @return 是否为周末
     */
    static bool isWeekend(qint64 timestamp);

    /**
     * @brief 获取相对时间描述（如"2分钟前"）
     * @param timestamp 时间戳
     * @return 相对时间描述
     */
    static QString getRelativeTime(qint64 timestamp);

    /**
     * @brief 计算函数执行时间
     * @param func 要测量的函数
     * @return 执行时间（毫秒）
     */
    template<typename Func>
    static qint64 measureExecutionTime(Func&& func) {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        return duration.count();
    }

    /**
     * @brief 计算函数执行时间（指定时间单位）
     * @param func 要测量的函数
     * @param unit 时间单位
     * @return 执行时间
     */
    template<typename Func>
    static qint64 measureExecutionTime(Func&& func, TimeUnit unit) {
        auto start = std::chrono::high_resolution_clock::now();
        func();
        auto end = std::chrono::high_resolution_clock::now();

        switch (unit) {
            case TimeUnit::Nanoseconds:
                return std::chrono::duration_cast<std::chrono::nanoseconds>(end - start).count();
            case TimeUnit::Microseconds:
                return std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
            case TimeUnit::Milliseconds:
                return std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
            case TimeUnit::Seconds:
                return std::chrono::duration_cast<std::chrono::seconds>(end - start).count();
            default:
                return std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count();
        }
    }

    /**
     * @brief 性能计时器类
     */
    class Timer {
    public:
        Timer();
        void start();
        void restart();
        qint64 elapsed() const;
        qint64 elapsedMs() const;
        qint64 elapsedUs() const;
        qint64 elapsedNs() const;

    private:
        std::chrono::high_resolution_clock::time_point m_startTime;
    };

    /**
     * @brief 定时器回调函数类型
     */
    using TimerCallback = std::function<void()>;

    /**
     * @brief 创建单次定时器
     * @param milliseconds 延迟时间（毫秒）
     * @param callback 回调函数
     * @return QTimer指针
     */
    static QTimer* createSingleShotTimer(int milliseconds, const TimerCallback& callback);

    /**
     * @brief 创建重复定时器
     * @param milliseconds 间隔时间（毫秒）
     * @param callback 回调函数
     * @return QTimer指针
     */
    static QTimer* createRepeatingTimer(int milliseconds, const TimerCallback& callback);

private:
    static qint64 s_startTime;
    static const qint64 CONVERSION_FACTORS[];
};

#endif // TIMEUTILS_H