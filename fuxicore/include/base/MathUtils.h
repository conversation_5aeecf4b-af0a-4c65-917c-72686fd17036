#ifndef MATHUTILS_H
#define MATHUTILS_H

#include <vector>
#include <QtGlobal>
#include <QPointF>
#include <QPolygonF>
#include <cmath>
#include <complex>

/**
 * @brief 数学工具类
 * 提供常用的数学计算功能，包括统计、几何、数值计算等
 */
class MathUtils
{
public:
    // 数学常量
    static const qreal PI;
    static const qreal E;
    static const qreal GOLDEN_RATIO;
    static const qreal SQRT_2;
    static const qreal SQRT_3;

    // 角度转换
    static qreal degToRad(qreal degrees);
    static qreal radToDeg(qreal radians);

    // 数值比较（考虑浮点精度）
    static bool isEqual(qreal a, qreal b, qreal epsilon = 1e-9);
    static bool isZero(qreal value, qreal epsilon = 1e-9);

    // 数值范围和限制
    static qreal clamp(qreal value, qreal min, qreal max);
    static qreal lerp(qreal a, qreal b, qreal t);
    static qreal map(qreal value, qreal fromMin, qreal fromMax, qreal toMin, qreal toMax);
    static qreal normalize(qreal value, qreal min, qreal max);
    /**
     * @brief 计算向量的平均值
     * @param values 输入向量
     * @return 平均值
     */
    static qreal average(const std::vector<qreal>& values);

    /**
     * @brief 计算向量的方差
     * @param values 输入向量
     * @return 方差
     */
    static qreal variance(const std::vector<qreal>& values);

    /**
     * @brief 计算向量的标准差
     * @param values 输入向量
     * @return 标准差
     */
    static qreal standardDeviation(const std::vector<qreal>& values);

    /**
     * @brief 线性插值
     * @param x0 起始点x坐标
     * @param y0 起始点y坐标
     * @param x1 结束点x坐标
     * @param y1 结束点y坐标
     * @param x 插值点x坐标
     * @return 插值点y坐标
     */
    static qreal linearInterpolate(qreal x0, qreal y0, qreal x1, qreal y1, qreal x);

    // 统计函数扩展
    static qreal median(std::vector<qreal> values);
    static qreal mode(const std::vector<qreal>& values);
    static qreal range(const std::vector<qreal>& values);
    static qreal percentile(std::vector<qreal> values, qreal p);
    static qreal correlation(const std::vector<qreal>& x, const std::vector<qreal>& y);

    // 数值计算
    static qreal factorial(int n);
    static qreal combination(int n, int r);
    static qreal permutation(int n, int r);
    static qreal gcd(int a, int b);
    static qreal lcm(int a, int b);
    static bool isPrime(int n);
    static std::vector<int> primeFactors(int n);

    // 几何计算
    static qreal distance(const QPointF& p1, const QPointF& p2);
    static qreal distance3D(qreal x1, qreal y1, qreal z1, qreal x2, qreal y2, qreal z2);
    static qreal angle(const QPointF& p1, const QPointF& p2);
    static QPointF rotate(const QPointF& point, qreal angle, const QPointF& center = QPointF(0, 0));
    static qreal polygonArea(const QPolygonF& polygon);
    static bool pointInPolygon(const QPointF& point, const QPolygonF& polygon);

    // 三角函数扩展
    static qreal sinh(qreal x);
    static qreal cosh(qreal x);
    static qreal tanh(qreal x);
    static qreal asinh(qreal x);
    static qreal acosh(qreal x);
    static qreal atanh(qreal x);

    // 数值积分和微分
    static qreal integrate(std::function<qreal(qreal)> func, qreal a, qreal b, int steps = 1000);
    static qreal derivative(std::function<qreal(qreal)> func, qreal x, qreal h = 1e-6);

    // 矩阵运算（简单2x2和3x3）
    struct Matrix2x2 {
        qreal m[2][2];
        Matrix2x2(qreal m00 = 0, qreal m01 = 0, qreal m10 = 0, qreal m11 = 0);
        Matrix2x2 operator+(const Matrix2x2& other) const;
        Matrix2x2 operator-(const Matrix2x2& other) const;
        Matrix2x2 operator*(const Matrix2x2& other) const;
        Matrix2x2 operator*(qreal scalar) const;
        qreal determinant() const;
        Matrix2x2 inverse() const;
        Matrix2x2 transpose() const;
    };

    struct Matrix3x3 {
        qreal m[3][3];
        Matrix3x3();
        Matrix3x3(qreal m00, qreal m01, qreal m02,
                  qreal m10, qreal m11, qreal m12,
                  qreal m20, qreal m21, qreal m22);
        Matrix3x3 operator+(const Matrix3x3& other) const;
        Matrix3x3 operator-(const Matrix3x3& other) const;
        Matrix3x3 operator*(const Matrix3x3& other) const;
        Matrix3x3 operator*(qreal scalar) const;
        qreal determinant() const;
        Matrix3x3 inverse() const;
        Matrix3x3 transpose() const;
    };

    // 向量运算
    struct Vector2D {
        qreal x, y;
        Vector2D(qreal x = 0, qreal y = 0);
        Vector2D operator+(const Vector2D& other) const;
        Vector2D operator-(const Vector2D& other) const;
        Vector2D operator*(qreal scalar) const;
        qreal dot(const Vector2D& other) const;
        qreal cross(const Vector2D& other) const;
        qreal length() const;
        qreal lengthSquared() const;
        Vector2D normalized() const;
        qreal angle() const;
        Vector2D rotate(qreal angle) const;
    };

    struct Vector3D {
        qreal x, y, z;
        Vector3D(qreal x = 0, qreal y = 0, qreal z = 0);
        Vector3D operator+(const Vector3D& other) const;
        Vector3D operator-(const Vector3D& other) const;
        Vector3D operator*(qreal scalar) const;
        qreal dot(const Vector3D& other) const;
        Vector3D cross(const Vector3D& other) const;
        qreal length() const;
        qreal lengthSquared() const;
        Vector3D normalized() const;
    };

    // 随机数生成
    static qreal randomReal(qreal min = 0.0, qreal max = 1.0);
    static int randomInt(int min, int max);
    static qreal randomGaussian(qreal mean = 0.0, qreal stddev = 1.0);
    static std::vector<qreal> randomVector(int size, qreal min = 0.0, qreal max = 1.0);

    // 数值求解
    static qreal bisectionMethod(std::function<qreal(qreal)> func, qreal a, qreal b, qreal tolerance = 1e-6);
    static qreal newtonMethod(std::function<qreal(qreal)> func, std::function<qreal(qreal)> derivative,
                             qreal x0, qreal tolerance = 1e-6, int maxIterations = 100);

    // 插值方法扩展
    static qreal cubicInterpolate(qreal p0, qreal p1, qreal p2, qreal p3, qreal t);
    static qreal splineInterpolate(const std::vector<QPointF>& points, qreal x);

    // 信号处理
    static std::vector<qreal> movingAverage(const std::vector<qreal>& data, int windowSize);
    static std::vector<qreal> gaussianFilter(const std::vector<qreal>& data, qreal sigma);
    static std::vector<std::complex<qreal>> fft(const std::vector<qreal>& data);

private:
    static qreal gaussianKernel(qreal x, qreal sigma);
};

#endif // MATHUTILS_H