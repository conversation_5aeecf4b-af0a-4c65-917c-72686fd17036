#ifndef FILEUTILS_H
#define FILEUTILS_H

#include <QString>
#include <QStringList>
#include <QByteArray>
#include <vector>
#include <memory>

class QFile;

/**
 * @brief 文件工具类
 * 提供常用的文件操作功能
 */
class FileUtils
{
public:
    /**
     * @brief 检查文件是否存在
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    static bool exists(const QString& filePath);

    /**
     * @brief 创建目录
     * @param dirPath 目录路径
     * @return 是否创建成功
     */
    static bool createDirectory(const QString& dirPath);

    /**
     * @brief 递归删除目录
     * @param dirPath 目录路径
     * @return 是否删除成功
     */
    static bool removeDirectory(const QString& dirPath);

    /**
     * @brief 获取目录下的所有文件
     * @param dirPath 目录路径
     * @param recursive 是否递归搜索子目录
     * @return 文件路径列表
     */
    static QStringList listFiles(const QString& dirPath, bool recursive = false);

    /**
     * @brief 读取文件内容
     * @param filePath 文件路径
     * @param content 文件内容输出
     * @return 是否读取成功
     */
    static bool readFile(const QString& filePath, QByteArray& content);

    /**
     * @brief 写入文件内容
     * @param filePath 文件路径
     * @param content 文件内容
     * @return 是否写入成功
     */
    static bool writeFile(const QString& filePath, const QByteArray& content);

    /**
     * @brief 获取文件大小
     * @param filePath 文件路径
     * @return 文件大小（字节），失败返回-1
     */
    static qint64 fileSize(const QString& filePath);

    /**
     * @brief 复制文件
     * @param source 源文件路径
     * @param destination 目标文件路径
     * @return 是否复制成功
     */
    static bool copyFile(const QString& source, const QString& destination);

    /**
     * @brief 移动文件
     * @param source 源文件路径
     * @param destination 目标文件路径
     * @return 是否移动成功
     */
    static bool moveFile(const QString& source, const QString& destination);

    /**
     * @brief 删除文件
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    static bool deleteFile(const QString& filePath);
};

#endif // FILEUTILS_H