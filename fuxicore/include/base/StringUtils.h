#ifndef STRINGUTILS_H
#define STRINGUTILS_H

#include <QString>
#include <QStringList>
#include <QRegularExpression>
#include <QTextCodec>
#include <vector>
#include <map>

/**
 * @brief 字符串工具类
 * 提供常用的字符串处理功能，包括分割、连接、格式化、编码转换等
 */
class StringUtils
{
public:
    // 常用编码常量
    static const QString UTF8_ENCODING;
    static const QString GBK_ENCODING;
    static const QString ASCII_ENCODING;

    // 常用正则表达式模式
    static const QString EMAIL_PATTERN;
    static const QString PHONE_PATTERN;
    static const QString URL_PATTERN;
    static const QString IP_PATTERN;
    
    /**
     * @brief 将字符串按指定分隔符分割
     * @param str 输入字符串
     * @param delimiter 分隔符
     * @return 分割后的字符串列表
     */
    static QStringList split(const QString& str, const QString& delimiter);

    /**
     * @brief 将字符串列表连接成一个字符串
     * @param list 字符串列表
     * @param separator 连接符
     * @return 连接后的字符串
     */
    static QString join(const QStringList& list, const QString& separator);

    /**
     * @brief 判断字符串是否为空或仅包含空白字符
     * @param str 输入字符串
     * @return 是否为空白字符串
     */
    static bool isBlank(const QString& str);

    /**
     * @brief 移除字符串两端的空白字符
     * @param str 输入字符串
     * @return 处理后的字符串
     */
    static QString trim(const QString& str);

    /**
     * @brief 将字符串转换为整数向量
     * @param str 输入字符串
     * @param delimiter 分隔符
     * @return 整数向量
     */
    static std::vector<int> toIntVector(const QString& str, const QString& delimiter);

    /**
     * @brief 将字符串转换为双精度浮点数向量
     * @param str 输入字符串
     * @param delimiter 分隔符
     * @return 双精度浮点数向量
     */
    static std::vector<double> toDoubleVector(const QString& str, const QString& delimiter);

    /**
     * @brief 字符串大小写转换
     */
    static QString toUpperCase(const QString& str);
    static QString toLowerCase(const QString& str);
    static QString toTitleCase(const QString& str);
    static QString toCamelCase(const QString& str);
    static QString toSnakeCase(const QString& str);

    /**
     * @brief 字符串填充
     * @param str 原字符串
     * @param width 目标宽度
     * @param fillChar 填充字符
     * @return 填充后的字符串
     */
    static QString leftPad(const QString& str, int width, QChar fillChar = ' ');
    static QString rightPad(const QString& str, int width, QChar fillChar = ' ');
    static QString centerPad(const QString& str, int width, QChar fillChar = ' ');

    /**
     * @brief 字符串替换
     * @param str 原字符串
     * @param oldStr 要替换的字符串
     * @param newStr 新字符串
     * @param caseSensitive 是否区分大小写
     * @return 替换后的字符串
     */
    static QString replace(const QString& str, const QString& oldStr,
                          const QString& newStr, bool caseSensitive = true);

    /**
     * @brief 正则表达式替换
     * @param str 原字符串
     * @param pattern 正则表达式模式
     * @param replacement 替换字符串
     * @return 替换后的字符串
     */
    static QString regexReplace(const QString& str, const QString& pattern,
                               const QString& replacement);

    /**
     * @brief 字符串验证
     */
    static bool isEmail(const QString& str);
    static bool isPhoneNumber(const QString& str);
    static bool isUrl(const QString& str);
    static bool isIpAddress(const QString& str);
    static bool isNumeric(const QString& str);
    static bool isAlpha(const QString& str);
    static bool isAlphaNumeric(const QString& str);

    /**
     * @brief 字符串搜索
     * @param str 源字符串
     * @param pattern 搜索模式
     * @param caseSensitive 是否区分大小写
     * @return 匹配位置列表
     */
    static QList<int> findAll(const QString& str, const QString& pattern,
                             bool caseSensitive = true);

    /**
     * @brief 正则表达式匹配
     * @param str 源字符串
     * @param pattern 正则表达式模式
     * @return 匹配结果列表
     */
    static QStringList regexMatch(const QString& str, const QString& pattern);

    /**
     * @brief 字符串编码转换
     */
    static QByteArray toEncoding(const QString& str, const QString& encoding);
    static QString fromEncoding(const QByteArray& data, const QString& encoding);
    static QString convertEncoding(const QString& str, const QString& fromEncoding,
                                  const QString& toEncoding);

    /**
     * @brief 字符串截取
     * @param str 原字符串
     * @param maxLength 最大长度
     * @param suffix 后缀（如"..."）
     * @return 截取后的字符串
     */
    static QString truncate(const QString& str, int maxLength, const QString& suffix = "...");

    /**
     * @brief 字符串反转
     * @param str 原字符串
     * @return 反转后的字符串
     */
    static QString reverse(const QString& str);

    /**
     * @brief 计算字符串相似度（编辑距离）
     * @param str1 字符串1
     * @param str2 字符串2
     * @return 相似度（0-1之间）
     */
    static double similarity(const QString& str1, const QString& str2);

    /**
     * @brief 字符串模板替换
     * @param template_ 模板字符串
     * @param variables 变量映射
     * @return 替换后的字符串
     */
    static QString format(const QString& template_, const std::map<QString, QString>& variables);

    /**
     * @brief 生成随机字符串
     * @param length 长度
     * @param charset 字符集
     * @return 随机字符串
     */
    static QString generateRandom(int length, const QString& charset = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789");

    /**
     * @brief 字符串压缩和解压
     */
    static QByteArray compress(const QString& str);
    static QString decompress(const QByteArray& data);

    /**
     * @brief 计算字符串哈希值
     * @param str 输入字符串
     * @return 哈希值
     */
    static QString hash(const QString& str);
    static QString md5(const QString& str);
    static QString sha1(const QString& str);
    static QString sha256(const QString& str);
};

#endif // STRINGUTILS_H
